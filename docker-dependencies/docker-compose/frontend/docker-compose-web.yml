volumes:
  web-dist:
    driver: local
    driver_opts:
      o: bind
      type: none
      device: ./data/dist


services:
  ampcon-t-webpack:
    image: ampcon-t:${REACT_APP_VERSION:-latest}
    build:
      context: ./frontend
      dockerfile: ./Dockerfile
      args:
        MODULE_TYPE: ${MODULE_TYPE:-test}
        IMG_CENTER_PROXY_DOMAIN: ${IMG_CENTER_PROXY_DOMAIN:-test}
    container_name: ampcon-t-webpack
    working_dir: /home/<USER>/ampcon-otn
    volumes:
      - web-dist:/home/<USER>/ampcon-otn/dist:ro