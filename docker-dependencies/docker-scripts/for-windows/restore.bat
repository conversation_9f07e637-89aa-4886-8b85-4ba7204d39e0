@echo off
setlocal EnableDelayedExpansion

SET currentDrive=%CD:~0,1%
SET "PRO_PATH=C:\usr\share\automation\server"
SET "RUNNING_VERSION_PATH=C:\usr\share\automation\server\.env"

for /f "tokens=1,2 delims==" %%i in ('findstr "REACT_APP_PRE_VERSION" "%RUNNING_VERSION_PATH%"') do (
    set "pre_version=%%j"
)

for /f "tokens=1,2 delims==" %%i in ('findstr "REACT_APP_VERSION" "%RUNNING_VERSION_PATH%"') do (
    set "current_version=%%j"
)

CALL :main

EXIT /B %ERRORLEVEL%

:cd_pro_path
if /i "%currentDrive%"=="C" (
  cd "%PRO_PATH%"
) else (
  cd /d "%PRO_PATH%"
)
EXIT /B 0

:check_version
IF "!pre_version!"=="" (
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project pre version is None, not allowed restore     <<<<<<<<<<<<<<<<<<<<<<"
  EXIT /B 1
)
IF NOT EXIST "!PRO_PATH!_!pre_version!_bak" (
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project need rollback file not found !!!             <<<<<<<<<<<<<<<<<<<<<<"
  EXIT /B 1
)
EXIT /B 0

:prepare_env_start_server
CALL :cd_pro_path
call stop.exe
echo ">>>>>>>>>>>>>>>>>>>   AmpCon project backup project data start          <<<<<<<<<<<<<<<<<<<<<<"
cd ..
move "!PRO_PATH!" "!PRO_PATH!_!current_version!_bak"
echo ">>>>>>>>>>>>>>>>>>>   AmpCon project backup project data successfully   <<<<<<<<<<<<<<<<<<<<<<"
echo ">>>>>>>>>>>>>>>>>>>   AmpCon project restore project data start         <<<<<<<<<<<<<<<<<<<<<<"
move "!PRO_PATH!_!pre_version!_bak" "!PRO_PATH!"
rem 更改权限的命令在Windows中是不同的，假设你的数据目录不需要更改
CALL :cd_pro_path
call start.exe
echo ">>>>>>>>>>>>>>>>>>>   AmpCon project restore project data successfully  <<<<<<<<<<<<<<<<<<<<<<"
EXIT /B 0

:rollback_server
echo ">>>>>>>>>>>>>>>>>>>   AmpCon project rollback data start          <<<<<<<<<<<<<<<<<<<<<<"
CALL :cd_pro_path
call stop.exe
cd ..
move "!PRO_PATH!_!current_version!_bak" "!PRO_PATH!"
rem 更改权限的命令在Windows中是不同的，假设你的数据目录不需要更改
echo ">>>>>>>>>>>>>>>>>>>   AmpCon project rollback data successfully   <<<<<<<<<<<<<<<<<<<<<<"
CALL :cd_pro_path
call start.exe
EXIT /B 0

:clean_docker_images
echo ">>>>>>>>>>>>>>>>>>>    Clean old docker images Started   <<<<<<<<<<<<<<<<<<<<<<"
for /f "tokens=*" %%i in ('docker images --format "{{.Repository}}:{{.Tag}}"') do (
    for /f "tokens=2 delims=:" %%j in ("%%i") do (
        set "tag=%%j"
        if not "!tag!"=="!pre_version!" if not "!tag!"=="!current_version!" docker rmi -f %%i
    )
)
echo ">>>>>>>>>>>>>>>>>>>   Clean old docker images successfully               <<<<<<<<<<<<<<<<<<<<<<"
EXIT /B 0

:check_server_health
timeout /t 6
for /f "tokens=*" %%a in ('docker ps -qf "name=nginx-service"') do (
    SET "nginx_container_id=%%a"
)
IF "!nginx_container_id!"=="" (
    echo ">>>>>>>>>>>>>>>>>>> Sorry container nginx-service is not running, current upgrade failed...check error   <<<<<<<<<<<<<<<<<<<<<<"
) ELSE (
    FOR /f "delims=" %%a in ('docker inspect --format "{{.State.Health.Status}}" nginx-service') DO (
        SET "health_status=%%a"
    )
    IF "!health_status!"=="healthy" (
        call :update_version
        echo ">>>>>>>>>>>>>>>>>>> Congratulations container nginx-service is healthy, current upgrade successfully!!!  <<<<<<<<<<<<<<<<<<<<<<"
    ) ELSE (
        CALL :rollback_server
        echo ">>>>>>>>>>>>>>>>>>> Sorry! container nginx-service is unhealthy, current upgrade failed...check error     <<<<<<<<<<<<<<<<<<<<<<"
    )
)
EXIT /B 0

:update_version
echo AmpCon project restore post action start
call powershell -Command "(gc %RUNNING_VERSION_PATH%) -replace '^REACT_APP_VERSION=.*', 'REACT_APP_VERSION=%pre_version%' | Out-File -encoding ASCII %RUNNING_VERSION_PATH%"
call powershell -Command "(gc %RUNNING_VERSION_PATH%) -replace '^REACT_APP_PRE_VERSION=.*', 'REACT_APP_PRE_VERSION=' | Out-File -encoding ASCII %RUNNING_VERSION_PATH%"
echo AmpCon project restore post action successfully
EXIT /B 0

:main
CALL :check_version
CALL :prepare_env_start_server
CALL :check_server_health
CALL :clean_docker_images
EXIT /B 0
