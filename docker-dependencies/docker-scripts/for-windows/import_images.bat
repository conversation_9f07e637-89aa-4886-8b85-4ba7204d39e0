@echo off
setlocal enabledelayedexpansion

set input_path=.\docker_images

for %%i in (%input_path%\*.tar.gz) do (
    set image_file=%%i
    set image_name_tag=%%~ni
    set image_name_tag=!image_name_tag:_=/!

    REM Check if the image exists in Docker
    docker images --format "{{.Repository}}:{{.Tag}}" | findstr /b /c:"!image_name_tag!" >nul
    if errorlevel 1 (
        docker load -i "!image_file!"
    )
)
endlocal
