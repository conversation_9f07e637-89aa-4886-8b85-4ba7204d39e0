# windows 安裝AmpCon項目说明

#### AmpCon Server的服务依赖docker windows desktop 安装，请在安装完之后，打开该软件，开启Docker Engine服务
#### Docker Engine 初始化需要windows机器开启Hype-V WSL2虚拟化，如启动失败请查询相关资料


# AmpCon Server 安装步骤

#### 1. 解压安装包 ampcon-t-xxx-release-xxx-for-windows.tar.gz
#### 2. 进入解压后的目录,双击执行 install_or_upgrade.exe 安装脚本
#### 3. 项目默认安装目录在 C:\usr\share\automation\server, 
#### 4. 安装后,进入安装目录（C:\usr\share\automation\server）执行 start.exe / stop.exe 来执行启动服务/关停服务