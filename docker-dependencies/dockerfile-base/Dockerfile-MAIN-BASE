FROM python:3.11.3-slim-bullseye
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list
RUN apt-get update && \
    apt-get install -y iproute2 && \
    apt-get install -y procps && \
    apt-get install -y net-tools && \
    apt-get install -y sshpass && \
    apt-get install -y default-mysql-client && \
    apt-get install -y ipmitool && \
    rm -rf /var/lib/apt/lists/*


RUN mkdir -p /usr/share/automation/server
COPY ../../backend/server-ampcon/pip_libs /usr/share/automation/pip_libs
COPY ../../data/settings/outer/requirements.txt /usr/share/automation/requirements.txt

WORKDIR /usr/share/automation
RUN pip install --no-index --find-links=pip_libs/ pbr
RUN pip install --no-index --find-links=pip_libs/ -r requirements.txt
RUN pip install --no-index --find-links=pip_libs/ protobuf==3.20.0

RUN rm -rf /usr/share/automation/pip_libs
RUN rm -rf /usr/share/automation/requirements.txt
