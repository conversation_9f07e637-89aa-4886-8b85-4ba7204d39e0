FROM ampcon-main:base

RUN mkdir -p /usr/share/automation/server
RUN mkdir -p /usr/share/automation/origin
COPY ../../backend/server-ampcon /usr/share/automation
RUN rm -rf /usr/share/automation/pip_libs

COPY ../../data/settings/inner/* /usr/share/automation/server/
COPY ../../data/settings/outer/requirements.txt /usr/share/automation/
COPY ../../data/settings/outer/ansible.cfg /etc/ansible/ansible.cfg
COPY ../../data/settings/outer/server.cnf /usr/share/automation/server.cnf
COPY ../../data/settings/encrypt_code.py /usr/share/automation/encrypt_code.py
COPY ../../data/custom_sql /usr/share/automation/server/custom_sql

RUN mkdir -p /var/log/openvpn
RUN mkdir -p /etc/openvpn/server

WORKDIR /usr/share/automation

RUN mv server origin
RUN mkdir -p /usr/share/automation/server/alembic
RUN mv /usr/share/automation/origin/server/alembic /usr/share/automation/server
RUN mv /usr/share/automation/origin/server/ansible_lib /usr/share/automation/server
RUN python encrypt_code.py
RUN mv dest/server/* server
RUN rm -rf origin
RUN rm -rf dest


WORKDIR /usr/share/automation/server

CMD echo "build main app image successfully."