# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

stages:          # List of stages for jobs, and their order of execution
  - build
  - deploy

.default_rules:
  rules:
    - if: $CI_PIPELINE_SOURCE == "trigger"
      when: always
    - when: manual

build-job:       # This job runs in the build stage, which runs first.
  stage: build
  extends: .default_rules
  variables:
    GIT_SUBMODULE_STRATEGY: recursive
  before_script:
    - echo "Current branch is $CI_COMMIT_BRANCH"
    - echo "module type is $MODULE_TYPE"
  script:
    - echo "Compiling the code full..."
    - chmod +x ./release.sh
    - ./release.sh --${MODULE_TYPE}
    - sudo chown -R $USER:$USER ./data/dist/
    - echo "Compile complete."
  after_script:
    - echo "Build job completed."
    - sudo mkdir -p  /opt/ampcon-package/${MODULE_TYPE}
    - sudo cp -rf ./ampcon*.tar.gz /opt/ampcon-package/${MODULE_TYPE}
    - cd /opt/ampcon-package/${MODULE_TYPE}
    - export current_timestamp=$(date +%s)
    - echo "current_timestamp is $current_timestamp"
    - |
      for file in *; do
        # 检查文件是否为普通文件(不是目录)
        if [ -f "$file" ]; then
            # 获取文件的创建时间戳
            create_timestamp=$(stat -c %Y "$file")
            # 计算文件存在的天数
            days_old=$((($current_timestamp - $create_timestamp) / 86400))
            # 如果文件超过 15 天,则删除
            if [ $days_old -gt 15 ]; then
                echo "Deleting file: $file (created $days_old days ago)"
                sudo rm -f "$file"
            fi
        fi
      done
  # cache:
  #   key: ampcon-t-package
  #   paths:
  #     - ./ampcon-t-release-*.tar.gz
  allow_failure: false
  tags:
    - ampcon

deploy-job:      # This job runs in the deploy stage.
  stage: deploy  # It only runs when *both* jobs in the test stage complete successfully.
  environment: production
  # cache:
  #   key: ampcon-t-package
  #   paths:
  #     - ./ampcon-t-release-*.tar.gz
  script:
    - echo "Deploying to $DEPLOY_TARGET_SERVER_IP..."
    - export SHORT_COMMIT_ID=$(git rev-parse --short=10 HEAD)
    - export AMPCON_VERSION=$(python3 -c "import json; print(json.load(open('release.json'))['${MODULE_TYPE}']['version'])")
    - export PACKNAME="$MODULE_TYPE-$AMPCON_VERSION-release-$SHORT_COMMIT_ID.tar.gz"
    - echo "PACKNAME is $PACKNAME"
    - IFS=',' read -ra DEPLOY_IPS <<< "$DEPLOY_TARGET_SERVER_IP"
    - |
      for ip in "${DEPLOY_IPS[@]}"; do
        echo "Deploying to $ip..."
        ssh pica8@$ip "rm -rf /home/<USER>/automation"
        ssh pica8@$ip "mkdir -p /home/<USER>/automation"
        scp /opt/ampcon-package/$MODULE_TYPE/$PACKNAME pica8@$ip:/home/<USER>/automation
        ssh pica8@$ip "bash -s" < ./auto-deploy/auto-deploy.sh $PACKNAME
        scp ./auto-deploy/pica-public-test.key pica8@$ip:/home/<USER>/automation/pica-public.key
        ssh pica8@$ip "sudo chown -R root:root /home/<USER>/automation/pica-public.key && sudo cp /home/<USER>/automation/pica-public.key /usr/share/automation/server/data/settings/inner/ && cd /usr/share/automation/server/ && sudo ./stop.sh && sudo ./start.sh "
        echo "Deploying to $ip successfully"
      done
    - echo "Application successfully deployed."
  rules:
    - if: $ENABLE_DEPLOY == "true"
      when: on_success
    - when: manual
  allow_failure: false
  tags:
    - ampcon
  # needs:
  #   - build-job
