import requests
import argparse
import os, subprocess
import json

token = "glptt-97f4a4f4a3c5332ba9e610155608674fa8adac0d"

def get_current_branch():
    try:
        if os.name == 'nt':  # Windows
            return subprocess.check_output(['git', 'rev-parse', '--abbrev-ref', 'HEAD'], universal_newlines=True).strip()
        else:  # Linux
            return subprocess.check_output(['git', 'rev-parse', '--abbrev-ref', 'HEAD'], universal_newlines=True, stderr=subprocess.DEVNULL).strip()
    except (subprocess.CalledProcessError, OSError):
        return None

def main():
    parser = argparse.ArgumentParser(description="Trigger a pipeline in Gitlab")
    parser.add_argument("-m", "--module-type", dest="module_type", default="ampcon-dc",
                        help="The module type, {ampcon-super|ampcon-t|ampcon-dc|ampcon-campus}, default is 'ampcon-dc'")
    parser.add_argument("-i", "--deploy-target-server-ip", dest="deploy_target_server_ip", default="************",
                        help="The deploy target server IP")
    parser.add_argument("-e", "--enable-deploy", dest="enable_deploy", default="false",
                        help="Whether to enable deploy, true or false, default is 'false'")

    args = parser.parse_args()
    ref = get_current_branch()
    url = "http://***********/api/v4/projects/254/trigger/pipeline"

    print(ref, "module-type: ",args.module_type, "deploy-target-server-ip: ", args.deploy_target_server_ip, "enable-deploy: ",args.enable_deploy) 
    variables = {
        "MODULE_TYPE": args.module_type,
        "DEPLOY_TARGET_SERVER_IP": args.deploy_target_server_ip,
        "ENABLE_DEPLOY": args.enable_deploy
    }

    files = {
        "token": (None, token),
        "ref": (None, ref),
        "variables[MODULE_TYPE]": (None, variables["MODULE_TYPE"]),
        "variables[DEPLOY_TARGET_SERVER_IP]": (None, variables["DEPLOY_TARGET_SERVER_IP"]),
        "variables[ENABLE_DEPLOY]": (None, variables["ENABLE_DEPLOY"])
    }

    response = requests.post(url, files=files, allow_redirects=False)
    response.raise_for_status()
    # print(response.text)
    
    try:
        response_data = json.loads(response.text)
        if "web_url" in response_data:
            print("pipline web_url: ", response_data["web_url"])
        else:
            print("Error: 'web_url' not found in response")
    except json.JSONDecodeError:
        print("Error: Unable to parse response")

if __name__ == "__main__":
    main()