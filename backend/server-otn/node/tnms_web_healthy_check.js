const http = require('http');

// URL to check
const urlToCheck = 'http://localhost:8888/healthy';

// Options for the HTTP request
const options = {
  method: 'GET',
  timeout: 5000, // Timeout in milliseconds
};

const request = http.request(urlTo<PERSON><PERSON>ck, options, (response) => {
  // Check the HTTP status code
  if (response.statusCode === 200) {
    console.log('Healthy'); // Application is healthy
    process.exit(0); // Exit with success status code
  } else {
    console.log('Unhealthy'); // Application is not healthy
    process.exit(1); // Exit with failure status code
  }
});

request.on('error', (error) => {
  console.error('Error:', error.message); // Error occurred
  process.exit(1); // Exit with failure status code
});

// Set a timeout for the request
request.setTimeout(options.timeout, () => {
  console.error('Request timed out'); // Request timed out
  request.abort(); // Abort the request
});

request.end(); // Send the HTTP request
