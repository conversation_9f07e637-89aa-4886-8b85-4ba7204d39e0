# This is the default ansible 'hosts' file.
#
# It should live in /etc/ansible/hosts
#
#   - Comments begin with the '#' character
#   - Blank lines are ignored
#   - Groups of hosts are delimited by [header] elements
#   - You can enter hostnames or ip addresses
#   - A hostname/ip can be a member of multiple groups

# Ex 1: Ungrouped hosts, specify before any group headers.

## green.example.com
## blue.example.com
## *************
## *************0

# Ex 2: A collection of hosts belonging to the 'webservers' group

## [webservers]
## alpha.example.org
## beta.example.org
## *************
## *************

lab43

#[spines]
#***********[1:2]
#spine_a ansible_host=************ 
#************ 
#************ 

#[leafs]
#***********[1:4]



# If you have multiple hosts following a pattern you can specify
# them like this:

## www[001:006].example.com

# Ex 3: A collection of database servers in the 'dbservers' group

## [dbservers]
## 
## db01.intranet.mydomain.net
## db02.intranet.mydomain.net
## **********
## **********

# Here's another example of host ranges, this time there are no
# leading 0s:

## db-[99:101]-node.example.com

[lab]
server16 ansible_host=*********** ansible_port=22 ansible_user=lab ansible_ssh_pass=pronto
server43 ansible_host=*********** ansible_port=22 ansible_user=lab ansible_ssh_pass=pronto
server44 ansible_host=*********** ansible_port=22 ansible_user=lab ansible_ssh_pass=pronto

[linux]
pony  ansible_host=************ ansible_port=22 ansible_user=lab ansible_ssh_pass=pronto
puppy ansible_host=************ ansible_port=22 ansible_user=lab ansible_ssh_pass=pronto

[debian]
************
ec4610 ansible_host=************ ansible_port=22 ansible_user=admin ansible_ssh_pass=pica8
