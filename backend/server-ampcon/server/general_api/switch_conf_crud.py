#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: Pica8AmpCon
@file: switch_conf_crud.py
@function:
@time: 2022/1/4 17:02
"""
from .__init__ import traceback, request, jsonify, general_model, inventory, inven_db, json


def switch_to_dict(model):
    """
        model = Column(String(32), primary_key=True)
        speed_for_license = Column(Enum('1G', '10G'), default='1G')
        feature = Column(String(32))
        up_to_date_version = Column(String(32))
        up_to_date_image_path = Column(String(255))
        up_to_date_image_md5_path = Column(String(255))
        up_to_date_onie_path = Column(String(255))
        patched_tar_file = Column(String(255))
        patched_install_script = Column(String(255))
        script_file_path = Column(String(255))
        # add in manual upgrade image request
        manual_upgrade_scripts = Column(String(511))
    """
    return dict(
        model=model.model,
        speed_for_license=model.speed_for_license,
        feature=model.feature,
        up_to_date_version=model.up_to_date_version,
        up_to_date_image_path=model.up_to_date_image_path,
        up_to_date_image_md5_path=model.up_to_date_image_md5_path,
        up_to_date_onie_path=model.up_to_date_onie_path,
        patched_tar_file=model.patched_tar_file,
        patched_install_script=model.patched_install_script,
        script_file_path=model.script_file_path,
        manual_upgrade_scripts=model.manual_upgrade_scripts,
    )


@general_model.route('/settings/switch_model', methods=['POST', 'GET'])
def switch_model():
    """
    :return:     POST ==> code 200 success, 500 error
                 GET  ==>[
                          {
                            "feature": "EE",
                            "manual_upgrade_scripts": "",
                            "model": "N3024ET-ON",
                            "patched_install_script": "",
                            "patched_tar_file": "",
                            "script_file_path": "",
                            "speed_for_license": "1G",
                            "up_to_date_image_md5_path": "",
                            "up_to_date_image_path": "img/N3024ET-ON/onie-installer-picos-4.0.1-916ebbeb43-x86.bin",
                            "up_to_date_onie_path": "img/N3024ET-ON/onie-installer-picos-4.0.1-916ebbeb43-x86.bin",
                            "up_to_date_version": "4.0.1/916ebbeb43"
                          }
                        ]
    """
    if request.method == 'GET':
        db_switch_info = inven_db.get_collection(inventory.SwitchSystemInfo)
        if not db_switch_info:
            return jsonify([])
        return jsonify(list(map(switch_to_dict, db_switch_info)))
    # # No update API in v1
    # else:
    #     status, msg = 500, "success"
    #     try:
    #         params = json.loads(request.get_data(as_text=True))
    #         # strong check params
    #         model = params.get("model", "")
    #         if not model:
    #             raise ValueError("model is required")
    #         switch_obj = inventory.SwitchSystemInfo(
    #             model=model,
    #             speed_for_license=params.get("speed_for_license", ""),
    #             feature=params.get("feature", ""),
    #             up_to_date_version=params.get("up_to_date_version", ""),
    #             up_to_date_image_path=params.get("up_to_date_image_path", ""),
    #             up_to_date_image_md5_path=params.get("up_to_date_image_md5_path", ""),
    #             up_to_date_onie_path=params.get("up_to_date_onie_path", ""),
    #             patched_tar_file=params.get("patched_tar_file", ""),
    #             patched_install_script=params.get("patched_install_script", ""),
    #             script_file_path=params.get("script_file_path", ""),
    #             manual_upgrade_scripts=params.get("manual_upgrade_scripts", ""),
    #         )
    #         # TODO need Call API
    #         inven_db.insert(switch_obj)
    #     except ValueError as v:
    #         status, msg = 500, "ERROR:[%s]" % v
    #     except Exception as e:
    #         status, msg = 500, "ERROR:[%s, %s]" % (e, traceback.format_exc())
    #     finally:
    #         return jsonify({"status_code": status, "msg": msg})
