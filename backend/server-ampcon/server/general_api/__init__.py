#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: Pica8AmpCon
@file: general_api.py
@function:
@time: 2022/1/4 13:43
"""
import json
import traceback

from server import cfg
from flask import Blueprint, request, jsonify


general_model = Blueprint('general_model', __name__, template_folder='templates')

from server.db.models import inventory, general, monitor, automation
from server.db.models.user import User
from server.db.models.user import user_db

inven_db = inventory.inven_db
automation_db = automation.automation_db

# Pay attention to location and don't loop in references
# from template_crud import get_templates_list, get_templates, add_templates, update_templates, del_templates
from .template_crud import *
from .sys_conf_crud import *
from .switch_conf_crud import *
from .configuration_crud import *
from .switch_lifecycle_crud import *
from .automation_crud import *
