#!/usr/bin sh

alembic -c alembic-mysql.ini upgrade head;
if [ $? -ne 0 ]; then
    echo "mysql alembic upgrade head failed. Exiting."
    exit 1
fi
if grep -q "^PRO_TYPE=ampcon-smb" .env || grep -q "^PRO_TYPE=ampcon-campus" .env; then
    echo "PRO_TYPE is ampcon-smb or ampcon-campus. Running alembic upgrade..."
    alembic -c alembic-postgresql.ini upgrade head
    if [ $? -ne 0 ]; then
        echo "postgresql alembic upgrade head failed. Exiting."
        exit 1
    fi
else
    echo "PRO_TYPE is not ampcon-smb or ampcon-campus. Skipping alembic upgrade."
fi
ip route add ********/20 via $(getent hosts openvpn-service | awk '{ print $1 }');

#cpu_cores=$(nproc)
#gunicorn loader:app -w $cpu_cores -b 0.0.0.0:443 --preload
python3 ../pre_start.py
python3 ampcon_engine.pyc