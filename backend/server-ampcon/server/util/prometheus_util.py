import logging
import requests
import traceback
import copy
import re
from datetime import datetime, timedelta
from server import cfg
from sqlalchemy import func, and_, exists
from pygnmi.client import gNMIclient
from concurrent.futures import ThreadPoolExecutor, as_completed

from server.util.terminal_identification_util import get_organization_name_by_mac
from server.db.models.monitor import monitor_db, ModulesLink
from server.db.models.inventory import SystemConfig, License, Switch, MonitorTarget, Topology, TopologyEdge, TopologyNode, ClientDeviceInfo
from server.db.models.inventory import inven_db, PushConfigTask, PushConfigTaskDetails
from server.util import utils
from server import constants

LOG = logging.getLogger(__name__)

def format_query_time(start_time=None, end_time=None):
    delta_time = "5m"
    timestamp = None
    step = "15s"

    now_timestamp = datetime.now().timestamp()

    if end_time:
        if end_time > now_timestamp:
            end_time = now_timestamp
        timestamp = end_time
    else:
        end_time = now_timestamp

    if start_time:
        if start_time >= end_time:
            start_time = end_time - 300 # 5分钟 = 300秒
        else:
            time_delta = datetime.fromtimestamp(end_time) - datetime.fromtimestamp(start_time)
            step = f"{(time_delta.days +1) * 15}s"
            minutes_diff = int(time_delta.total_seconds() // 60)
            delta_time = f"{minutes_diff}m"
    else:
        start_time = end_time - 300 # 5分钟 = 300秒

    return timestamp, delta_time, start_time, end_time, step


def generate_time_points_dict(start_timestamp, end_timestamp, step):
    time_points_dict = {}
    interval = timedelta(seconds=int(step[:-1]))

    current_time = datetime.fromtimestamp(start_timestamp)
    end = datetime.fromtimestamp(end_timestamp)

    while current_time <= end:
        formatted_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
        time_points_dict[formatted_time] = [formatted_time, None]
        current_time += interval

    return time_points_dict


def query_prometheus(query, time=None):
    try:
        response = requests.get(f'http://{cfg.CONF.prometheus_url}/api/v1/query', params={'query': query, 'time': time})
        data = response.json()
        if data['status'] != 'success':
            LOG.error(query)
            LOG.error(data)
            return []
        else:
            # print(data)
            return data['data']['result']
    except Exception as e:
        LOG.error(f"query_prometheus error: {traceback.format_exc()}")
        return []


def query_range_prometheus(query, start_time, end_time, step="15s"):
    try:
        response = requests.get(f'http://{cfg.CONF.prometheus_url}/api/v1/query_range', params={'query': query, 'start': start_time, 'end': end_time, 'step': step})
        data = response.json()
        if data['status'] != 'success':
            LOG.error(query)
            LOG.error(data)
            return []
        else:
            # print(data)
            return data['data']['result']
    except Exception as e:
        LOG.error(f"query_range_prometheus error: {traceback.format_exc()}")
        return []


def query_metric_filter_by_interface(metricName, targetName, interface_list, time=None):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__="{name}", target="{target_value}", interface_name=~"{interfaces}"}}'
    modified_query = query_template.format(name=metricName, target_value=targetName, interfaces=interfaces)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = {}
    for data in result:
        metric = data["metric"]
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
            metric.pop("interface_name")
        else:
            continue

        if name not in metric_data:
            metric_data[name] = []

        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        metric.pop("target")
        metric_data[name] = metric
        # print(name, metric)
    return metric_data


def query_metric_value_filter_by_interface(metricName, targetName, interface_list, time=None):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__="{name}", target="{target_value}", interface_name=~"{interfaces}"}}'
    modified_query = query_template.format(name=metricName, target_value=targetName, interfaces=interfaces)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = {}
    for data in result:
        metric = data["metric"]
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
            metric.pop("interface_name")
        else:
            continue

        if name not in metric_data:
            metric_data[name] = []

        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        metric.pop("target")
        metric_data[name] = metric
        # print(name, metric)
    return metric_data

def get_port_speed_usage(metricName, targetName, interface_list, keyName, time=None):

    interfaces = "|".join(interface_list)
    step1 = '( {{__name__="{name}", target="{targetName}", interface_name=~"{interfaces}"}}'
    step2 = ' / on(interface_name, target)'
    step3 = '{{__name__="openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state_port_speed", target="{targetName}", interface_name=~"{interfaces}"}}'
    step4 = ' * 100 )'

    query_template = step1 + step2 + step3 + step4
    modified_query = query_template.format(name=metricName, targetName=targetName, interfaces=interfaces)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = {}
    for data in result:
        metric = data["metric"]
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
            metric.pop("interface_name")
        else:
            continue

        if data["value"][1] == "+Inf" or data["value"][1] == "NaN":
            rawValue = 0
            value = 0
        else:
            rawValue = data["value"][1]
            value = float(data["value"][1])
        if name not in metric_data:
            metric_data[name] = []

        # metric.pop("__name__")
        # metric.pop("instance")
        # metric.pop("job")
        metric.pop("target")
        if value == 0:
            metric[keyName] = "0.00%"
        elif 0 < value < 0.01:
            metric[keyName] = "0.01%"
        else:
            metric[keyName] = str(round(value, 2)) + "%"
        metric['raw_' + keyName] = rawValue
        metric_data[name] = metric
        # print(name, metric)
    return metric_data


def query_metric(metricName, targetName, time=None):
    query_template  = '{{__name__="{name}", target="{target_value}"}}'
    modified_query = query_template.format(name=metricName, target_value=targetName)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = []
    for data in result:
        metric = data["metric"]
        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        # metric.pop("target")
        metric_data.append(metric)
        metric["metric_value"] = float(data["value"][1])
        metric["metric_time"] = datetime.fromtimestamp(data["value"][0]).strftime('%Y-%m-%d %H:%M:%S')
        # print(name, metric)
    return metric_data


def query_counters_with_prefix(metricPrefix ,targetName, interface_list, time=None):
    # 构造查询表达式
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__=~"{prefix}.*", target="{target_value}", interface_name=~"{interfaces}"}}'
    query_template = query_template.format(prefix=metricPrefix, target_value=targetName, interfaces=interfaces)

    # print(query_template)
    result = query_prometheus(query_template, time)
    # print(result)
    counters_data = {}
    for data in result:
        name = data["metric"]["__name__"][len(metricPrefix):]
        interface_name = data["metric"]["interface_name"]
        if interface_name not in counters_data:
            counters_data[interface_name] = {}
        counters_data[interface_name][name] = int(float(data["value"][1]))

    return counters_data


def query_ai_with_prefix(metricPrefix ,targetName, interface_list, time=None):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__=~"{prefix}.*", target="{target_value}", interface_name=~"{interfaces}"}}'
    query_template = query_template.format(prefix=metricPrefix, target_value=targetName, interfaces=interfaces)
    result = query_prometheus(query_template, time)

    counters_data = {}
    for data in result:
        name = data["metric"]["__name__"][len(metricPrefix):]
        interface_name = data["metric"]["interface_name"]
        queue_name = data["metric"].get("queue_name", None)

        if interface_name not in counters_data:
            counters_data[interface_name] = {}

        if queue_name is None:
            counters_data[interface_name][name] = int(float(data["value"][1]))
        else:
            if queue_name not in counters_data[interface_name]:
                counters_data[interface_name][queue_name] = {}

            counters_data[interface_name][queue_name][name] = int(float(data["value"][1]))

    return counters_data


def query_counters(metricName, targetName, interface_list, time=None, multiChannel=False):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__="{name}", target="{target_value}", interface_name=~"{interfaces}"}}'
    query_template = query_template.format(name=metricName, target_value=targetName, interfaces=interfaces)

    # print(query_template)
    result = query_prometheus(query_template, time)
    # print(result)
    counters_data = {}

    for data in result:
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
        else:
            continue
        value = float(data["value"][1])
        if not multiChannel:
            counters_data[name] = value
        else:
            channel = data["metric"].get("channel_index", None)
            # 多频道时，累加字符串，格式如 "12.34(C1), 56.78(C2)"
            prev = counters_data.get(name, "")
            entry = f"{value:.2f}(C{channel})"
            if prev:
                counters_data[name] = prev + ", " + entry
            else:
                counters_data[name] = entry

    return counters_data


def get_target_interfaces(target, time=None):
    query_template = '{{__name__="openconfig_interfaces:interfaces_interface", target="{target_value}"}}'
    result = query_prometheus(query_template.format(target_value=target), time=time)

    interface_list = [data["metric"]["interface_name"] for data in result if not data["metric"]["interface_name"].startswith(("eth", "vlan", "rif-"))]
    # print(interface_list)
    return interface_list


def query_counter_delta_topk(metric_name, metric_prefix="openconfig_interfaces:interfaces_interface_state_counters_", topk=5, target=None, start_time=None, end_time=None, filter=None):

    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    if target:
        query_template = 'topK(' + str(topk) + ', max_over_time(' + metric_prefix + metric_name + '{target="' + target + '"}['+ delta_time +']))'
    elif filter:
        filter_list = []
        for target, value in filter.items():
            interfaces = "|".join(value)
            filter_template = f"max_over_time({metric_prefix}{metric_name}{{target=\"{target}\", interface_name=~\"{interfaces}\"}}[{delta_time}])"
            filter_list.append(filter_template)

        filter_query = " or ".join(filter_list)
        query_template = f'topk({str(topk)}, ({filter_query}))'
    else:
        query_template = 'topK(' + str(topk) + ', max_over_time(' + metric_prefix + metric_name + '['+ delta_time +']))'
    print(query_template)
    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        if 'queue_name' in metric['metric']:
            query_template = f"{metric_prefix}{metric_name}{{target=\"{metric['metric']['target']}\", interface_name=\"{metric['metric']['interface_name']}\", queue_name=\"{metric['metric']['queue_name']}\"}}"
        else:
            query_template = f"{metric_prefix}{metric_name}{{target=\"{metric['metric']['target']}\", interface_name=\"{metric['metric']['interface_name']}\"}}"
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }

        if 'queue_name' in metric['metric']:
            info['queue_name'] = metric['metric']['queue_name']

        res.append(info)

    return res

def query_modules_topk(metric_name, topk=5, target=None, start_time=None, end_time=None, mode="topK"):

    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    # 对于模块数据由于不是单调递增的 所以根据sum_over_time获取delta time内时间序列的总和然后排序
    if target:
        query_template = mode + '(' + str(topk) + ', sum_over_time(' + metric_name + '{target="' + target + '"}['+ delta_time +']))'
    else:
        query_template = mode + '(' + str(topk) + ', sum_over_time(' + metric_name + '['+ delta_time +']))'
    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = metric_name  + '{target="' + metric['metric']['target'] + '"' + ', interface_name="' + metric["metric"]["interface_name"] + '"'
        if metric['metric'].get("channel_index", None) is not None:
            query_template += ', channel_index="' + metric["metric"]["channel_index"] + '"}'
        else:
            query_template += '}'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        if metric['metric'].get("channel_index", None) is not None:
            info["channel_index"] = metric["metric"]["channel_index"]
        res.append(info)

    return res


def get_snmp_device_temperature_topk(start_time=None, end_time=None, topk=5):

    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    query = f'topk({topk}, snmp_device_temperature)'

    result = query_range_prometheus(
        query=query,
        start_time=start_time,
        end_time=end_time,
        step=step
    )

    res = []
    if not result:
        return []

    for metric in result:
        units = None
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]
            if "unit" in metric["metric"]:
                units = metric["metric"]["unit"]

        info ={
            'target': metric['metric']['sn'],
            'interface_name': "Temperature",
            "values": list(time_points_value.values()),
            'unit': units
        }
        res.append(info)
    return res


def get_device_temperature_by_target(target, start_time=None, end_time=None):
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    query = f'snmp_device_temperature{{sn="{target}"}}'
    res = []

    result = query_range_prometheus(
        query=query,
        start_time=start_time,
        end_time=end_time,
        step=step
    )

    if not result:
        values = [[dt, None] for dt in sorted(time_points_dict.keys())]
        res.append({
            "target": target,
            "values": values
        })
        return res

    for metric in result:
        merged_points = {dt: None for dt in time_points_dict.keys()}

        for ts, value in metric["values"]:
            date_time = datetime.fromtimestamp(ts).strftime("%Y-%m-%d %H:%M:%S")
            merged_points[date_time] = round(float(value), 2)

        values = [[dt, merged_points[dt]] for dt in sorted(merged_points.keys())]

        res.append({
            "target": metric['metric'].get('sn', ''),
            "unit": metric["metric"].get("unit", None),
            "values": values
        })

    return res


def decode_mac_address(raw_mac):
    if not raw_mac:
        return None
    if isinstance(raw_mac, str):
        match = re.search(r"([0-9A-Fa-f]{2}:){5}[0-9A-Fa-f]{2}", raw_mac)
        if match:
            return match.group(0)
        mac_bytes = raw_mac.encode('latin1')
    elif isinstance(raw_mac, bytes):
        mac_bytes = raw_mac
    else:
        return None
    try:
        return ":".join(f"{b:02X}" for b in mac_bytes)
    except Exception:
        return None
def get_fsos_supply_status(target):
    index_query = f'snmp_supplyindex{{sn="{target}"}}'
    index_result = query_prometheus(index_query)

    supply_info_map = {}
    if index_result:
        for item in index_result:
            metric = item.get("metric", {})
            value = item.get("value")
            if value and isinstance(value, list) and len(value) == 2:
                idx = value[1]
                supply_info_map[idx] = {
                    "supply_name": metric.get("supply_name", f"supply{idx}"),
                    "status": metric.get("supply_status")
                }

    values = []
    if supply_info_map:
        for idx, info in supply_info_map.items():
            values.append({
                "index": idx,
                "supply_name": info["supply_name"],
                "status": info["status"]
            })
    else:
        label_query = f'snmp_device_label{{sn="{target}"}}'
        label_result = query_prometheus(label_query)

        if label_result:
            power_count = 0
            for item in label_result:
                metric = item.get("metric", {})
                power_status = metric.get("power_status")
                if power_status:
                    power_count += 1
                    values.append({
                        "index": str(power_count),
                        "supply_name": "power",
                        "status": power_status
                    })

    return {
        "target": target,
        "values": values
    }




def get_fsos_target_mac_address(target):
    query = f'snmp_device_label{{sn="{target}"}}'
    result = query_prometheus(query)

    if not result:
        LOG.warning(f"No MAC address found for target: {target}")
        return None

    for item in result:
        mac = item["metric"].get("mac_address")
        if mac:
            decoded = decode_mac_address(mac)
            res = {
                "target": target,
                "mac_address": decoded
            }
            return res

    LOG.warning(f"No MAC address found for target: {target}")
    return None


def get_fsos_interface_overview(target):
    res = {}

    base_query = f'snmp_ifindex{{sn="{target}"}}'
    base_result = query_prometheus(base_query)

    if not base_result:
        LOG.warning(f"[InterfaceOverview] No base interface info found for target: {target}")
        return None

    for item in base_result:
        metric = item.get("metric", {})
        value = item.get("value")
        index_value = None

        if isinstance(value, list) and len(value) >= 2:
            try:
                index_value = int(value[1])  # ifIndex
            except (ValueError, TypeError):
                continue

        if index_value is None:
            continue

        res[index_value] = {
            "name": metric.get("interface_name"),
            "address": decode_mac_address(metric.get("interface_physical_address")),
            "port_status": metric.get("interface_status"),
            "mtu": None,
            "port_speed": None,
            "in_packets_discarded": None,
            "in_packets_with_errors": None,
            "out_packets_discarded": None,
            "out_packets_with_errors": None,
            "bits_sent": None,
            "bits_received": None,
        }

    metric_map = {
        "snmp_ifindex_interface_mtu": "mtu",
        "snmp_ifindex_interface_speed": "port_speed",
        "snmp_ifindex_in_packets_discarded": "in_packets_discarded",
        "snmp_ifindex_in_packets_with_errors": "in_packets_with_errors",
        "snmp_ifindex_out_packets_discarded": "out_packets_discarded",
        "snmp_ifindex_out_packets_with_errors": "out_packets_with_errors",
        "snmp_ifindex_bits_sent": "bits_sent",
        "snmp_ifindex_bits_received": "bits_received",
    }

    for metric_name, field_name in metric_map.items():
        query = f'{metric_name}{{sn="{target}"}}'
        result = query_prometheus(query)

        if not result:
            continue

        for item in result:
            metric = item.get("metric", {})
            value = item.get("value")
            index_value = None

            if isinstance(value, list) and len(value) >= 2:
                try:
                    index_value = int(metric.get("index_value") or value[0])
                except (ValueError, TypeError):
                    LOG.warning(f"[Interface Overview] Invalid index or timestamp in metric {metric_name}: {value}")
                    continue

            if index_value not in res:
                continue

            metric_value = value[1] if isinstance(value, list) and len(value) >= 2 else value

            try:
                if "." in str(metric_value):
                    metric_value = float(metric_value)
                else:
                    metric_value = int(metric_value)
            except (ValueError, TypeError) as e:
                LOG.warning(
                    f"[Interface Overview] Failed to convert metric value '{metric_value}' "
                    f"for ifIndex '{index_value}' field '{field_name}': {e}"
                )
                continue

            res[index_value][field_name] = metric_value

    return res


def get_fsos_fan_speed_by_target(target, start_time=None, end_time=None):
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    index_query = f'snmp_fanindex{{sn="{target}"}}'
    index_result = query_prometheus(index_query)

    index_map = {}
    for item in index_result:
        metric = item.get("metric", {})
        index = metric.get("index_value")
        fan_name = metric.get("fan_name")
        if index:
            index_map[index] = fan_name or f"fan{index}"

    speed_query = f'snmp_fanindex_fan_speed{{sn="{target}"}}'

    speed_result = query_range_prometheus(
        query=speed_query,
        start_time=start_time,
        end_time=end_time,
        step=step
    )

    if not speed_result:
        LOG.warning(f"No fan speed data found for target={target}")
        return {}

    fan_list = []
    unit = None

    for metric in speed_result:
        index = metric['metric'].get('index_value',None)
        fan_name = index_map.get(index, f"fan_{index}")
        merged_points = {dt: None for dt in time_points_dict.keys()}

        for ts, value in metric.get("values", []):
            date_time = datetime.fromtimestamp(ts).strftime("%Y-%m-%d %H:%M:%S")
            try:
                merged_points[date_time] = round(float(value), 2)
            except (ValueError, TypeError):
                merged_points[date_time] = None

        values = [[dt, merged_points[dt]] for dt in sorted(merged_points.keys())]

        if unit is None:
            unit = metric["metric"].get("unit", None)

        fan_list.append({
            "fan_index": index,
            "fan_name": fan_name,
            "values": values
        })

    res = {
        "unit": unit or None,
        "fans": fan_list
    }

    return res


def get_fsos_fan_status(target):
    speed_query = f'snmp_fanindex_fan_speed{{sn="{target}"}}'
    speed_result = query_prometheus(speed_query)

    index_query = f'snmp_fanindex{{sn="{target}"}}'
    index_result = query_prometheus(index_query)

    unit = None

    fan_info_map = {}
    for item in index_result:
        metric = item.get("metric", {})
        value = item.get("value")
        idx = None
        if value and isinstance(value, list) and len(value) == 2:
            idx = value[1]
        if idx:
            fan_info_map[idx] = {
                "fan_name": metric.get("fan_name", f"fan{idx}"),
                "status": metric.get("fan_status")
            }

    speed_map = {}
    for item in speed_result:
        metric = item.get("metric", {})
        index = metric.get("index_value")
        value = item.get("value")
        if value and isinstance(value, list) and len(value) == 2:
            _, speed_val = value
            speed_map[index] = int(speed_val)
        if unit is None:
            unit = metric.get("unit")

    all_indexes = set(fan_info_map.keys()) | set(speed_map.keys())
    values = []
    for idx in all_indexes:
        fan_info = fan_info_map.get(idx, {})
        values.append({
            "index": idx,
            "fan_name": fan_info.get("fan_name", f"fan{idx}"),
            "status": fan_info.get("status"),
            "speed": speed_map.get(idx)
        })

    res = {
        "target": target,
        "values": values,
        "unit": unit or None
    }
    return res



def get_fsos_target_interfaces(target, start_time=None, end_time=None, step=None):
    """
    Query FSOS device interfaces that have interface_status='1'.
    :param target: device sn
    :param start_time: query start time
    :param end_time: query end time
    :return: dict[str, str] -> index_value -> interface_name
    """
    query_template = f'avg_over_time(snmp_ifindex{{sn="{target}"}}[{step}]) '
    result = query_range_prometheus(query_template, start_time, end_time, step)

    interface_dict = {}
    for data in result:
        metric = data.get("metric", {})
        if "interface_name" not in metric or "interface_status" not in metric:
            continue

        if str(metric["interface_status"]) != "up":
            continue

        for timestamp, value in data.get("values", []):
            index_value = str(metric.get("index_value") or value)
            interface_name = metric["interface_name"]
            interface_dict[index_value] = interface_name

    return interface_dict


def query_fsos_interface_common(metric_name, target, interface_dict=None, interface_name=None, start_time=None, end_time=None):
    """
    :param metric_name: collect metric name in prometheus
    :param target: device sn
    :param interface_dict: index_value and interface_name filter dict(opional, dict[str])
    :param interface_name: single str or list[str]
    :return: list[dict]  -> each interface -> data point
    """
    timestamp, _, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    selected_index = None
    name_map = {}  # index -> interface_name
    if interface_dict:
        if interface_name:
            if isinstance(interface_name, str):
                interface_name = [interface_name]
            selected_index = [
                str(idx) for idx, name in interface_dict.items()
                if name in interface_name
            ]
            # build index -> interface_name map
            name_map = {str(idx): name for idx, name in interface_dict.items() if name in interface_name}
        else:
            selected_index = list(map(str, interface_dict.keys()))
            name_map = {str(idx): name for idx, name in interface_dict.items()}

    if selected_index:
        index = "|".join(selected_index)
        promql = (
            f'avg_over_time({metric_name}{{sn="{target}", index_value=~"{index}"}}[{step}]) '
        )
    else:
        promql = (
            f'avg_over_time({metric_name}{{sn="{target}"}}[{step}]) '
        )

    result = query_range_prometheus(promql, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric.get("values", []):
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")

            if value == "-65535":
                value = None
            else:
                try:
                    value = float(value)
                except (TypeError, ValueError):
                    value = None

            time_points_value[date_time_str] = [date_time_str, value]

        idx = metric["metric"].get("index_value")
        iface_name = name_map.get(idx, idx)

        info = {
            "target": metric["metric"].get("sn", None),
            "interface_name": iface_name,
            "index_value": idx,
            "values": list(time_points_value.values()),
            "unit": metric["metric"].get("unit", "")
        }
        res.append(info)

    return res


def query_fsos_cpu_usage(target=None, start_time=None, end_time=None):
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    base_time_points = generate_time_points_dict(start_time, end_time, step)

    query_template = f'{{__name__="snmp_cpu_used_percentage", sn="{target}"}}'

    result = query_range_prometheus(query_template, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(base_time_points)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]

        info = {
            "target": metric.get("metric", {}).get("sn", target),
            "interface_name": "CPU",
            "values": list(time_points_value.values()),
            "unit": metric["metric"].get("unit", "")
        }
        res.append(info)

    return res

def query_fsos_memory_usage(target=None, start_time=None, end_time=None):
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    base_time_points = generate_time_points_dict(start_time, end_time, step)

    metric_names = [
        "snmp_memory_used_percentage",
        "snmp_memory_usage_size",
        "snmp_memory_size",
    ]

    units = {}
    results = {}
    for metric_name in metric_names:
        query_template = f'{{__name__="{metric_name}", sn="{target}"}}'
        result = query_range_prometheus(query_template, start_time, end_time, step)

        metric_data = {}
        for metric in result:
            for timestamp, value in metric["values"]:
                date_time_str = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
                metric_data[date_time_str] = float(value)
                if "unit" in metric["metric"]:
                    units[metric_name] = metric["metric"]["unit"]
        results[metric_name] = metric_data

    res_values = []
    for timestamp in base_time_points.keys():
        perc = results.get("snmp_memory_used_percentage", {}).get(timestamp)
        used = results.get("snmp_memory_usage_size", {}).get(timestamp)
        total = results.get("snmp_memory_size", {}).get(timestamp)

        if perc is not None and total is not None and used is None:
            used = total * perc / 100
        if used is not None and total is not None and perc is None and total > 0:
            perc = used / total * 100
        if used is not None and perc is not None and total is None and perc > 0:
            total = used / (perc / 100)

        res_values.append({
            "time": timestamp,
            "used_percentage": round(perc, 2) if perc is not None else None,
            "used_size": round(used, 2) if used is not None else None,
            "total_size": round(total, 2) if total is not None else None,
            "unit": units.get("snmp_memory_used_percentage") or units.get("snmp_memory_usage_size") or units.get("snmp_memory_size")
        })

    res = []
    info = {
        "target": target,
        "interface_name": "Memory",
        "values": res_values
    }
    res.append(info)

    return res

def query_fsos_flash_usage(target=None):
    metric_names = [
        "snmp_flash_free_size",
        "snmp_flash_usage_size",
        "snmp_flash_size",
    ]

    units = {}
    results = {}
    for metric_name in metric_names:
        query_template = f'{{__name__="{metric_name}", sn="{target}"}}'
        result = query_prometheus(query_template)

        metric_data = {}
        for metric in result:
            timestamp, value = metric["value"]
            date_time_str = datetime.fromtimestamp(float(timestamp)).strftime("%Y-%m-%d %H:%M:%S")
            metric_data[date_time_str] = float(value)
            if "unit" in metric["metric"]:
                units[metric_name] = metric["metric"]["unit"]
        results[metric_name] = metric_data

    latest_time = None
    for metric in results.values():
        if metric:
            now_time = max(metric.keys())
            if latest_time is None or now_time > latest_time:
                latest_time = now_time

    def to_bytes(value, unit):
        if value is None:
            return None
        if unit == "MB":
            return value * 1024 * 1024
        elif unit == "GB":
            return value * 1024 * 1024 * 1024
        elif unit == "KB":
            return value * 1024
        else:
            return value

    free = to_bytes(results.get("snmp_flash_free_size", {}).get(latest_time),
                    units.get("snmp_flash_free_size"))
    used = to_bytes(results.get("snmp_flash_usage_size", {}).get(latest_time),
                    units.get("snmp_flash_usage_size"))
    total = to_bytes(results.get("snmp_flash_size", {}).get(latest_time),
                     units.get("snmp_flash_size"))

    if free is not None and total is not None and used is None:
        used = total - free
    if used is not None and total is not None and free is None:
        free = total - used
    if used is not None and free is not None and total is None:
        total = used + free

    def normalize(values):
        units = ["B", "KB", "MB", "GB", "TB"]
        factors = {
            "B": 1,
            "KB": 1024,
            "MB": 1024 ** 2,
            "GB": 1024 ** 3,
            "TB": 1024 ** 4,
        }

        unit_indexes = []
        for v in values:
            if v is None:
                unit_indexes.append(0)
                continue
            if v >= 1024 ** 4:
                unit_indexes.append(units.index("TB"))
            elif v >= 1024 ** 3:
                unit_indexes.append(units.index("GB"))
            elif v >= 1024 ** 2:
                unit_indexes.append(units.index("MB"))
            elif v >= 1024:
                unit_indexes.append(units.index("KB"))
            else:
                unit_indexes.append(units.index("B"))

        target_unit_index = max(unit_indexes)
        target_unit = units[target_unit_index]

        result = []
        for v in values:
            if v is None:
                result.append(None)
            else:
                val = round(v / factors[target_unit], 2)
                result.append(f"{val}{target_unit}")

        return result

    free_val, used_val, total_val = normalize([free, used, total])

    values = {}
    if free_val is not None:
        values["free_size"] = f"{free_val}"
    if used_val is not None:
        values["usage_size"] = f"{used_val}"
    if total_val is not None:
        values["total_size"] = f"{total_val}"

    if not values:
        return []

    res = [{
        "target": target,
        "interface_name": "flash-usages",
        "values": [{
            "time": latest_time,
            **values
        }]
    }]

    return res

def query_snmp_visual_counter_delta_topk(metric_name, topk=5, target=None, start_time=None, end_time=None, filter=None):
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    interface_dict = {}
    if target:
        interface_dict[target] = get_fsos_target_interfaces(target, start_time, end_time, step)

    if target:
        query_template = (
            f'topk({topk}, '
            f'max_over_time({metric_name}{{sn="{target}"}}[{delta_time}]) '
            f'* on(index_value, instance, job, sn) group_left(interface_status) '
            f'snmp_ifindex{{interface_status="up"}})'
        )
    elif filter:
        filter_list = []
        for target_item, value_list in filter.items():
            if target_item not in interface_dict:
                interface_dict[target_item] = get_fsos_target_interfaces(target_item, start_time, end_time, step)

            selected_indexes = []
            for interface_name in value_list:
                indexes = [
                    str(idx) for idx, name in interface_dict[target_item].items()
                    if name == interface_name
                ]
                selected_indexes.extend(indexes)

            if selected_indexes:
                indexes_str = "|".join(selected_indexes)
                filter_template = (
                    f'max_over_time({metric_name}{{sn="{target_item}", index_value=~"{indexes_str}"}}[{delta_time}]) '
                    f'* on(index_value, instance, job, sn) group_left(interface_status) '
                    f'snmp_ifindex{{interface_status="down"}}'
                )
                filter_list.append(filter_template)

        filter_query = " or ".join(filter_list)
        query_template = f'topk({topk}, ({filter_query}))'
    else:
        query_template = (
            f'topk({topk}, '
            f'max_over_time({metric_name}[{delta_time}]) '
            f'* on(index_value, instance, job, sn) group_left(interface_status) '
            f'snmp_ifindex{{interface_status="up"}})'
        )

    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        sn = metric["metric"]["sn"]
        index_value = metric["metric"]["index_value"]
        query_templates.append(
            f'{metric_name}{{sn="{sn}", index_value="{index_value}"}}'
        )
        if sn not in interface_dict:
            interface_dict[sn] = get_fsos_target_interfaces(sn, start_time, end_time, step)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for ts, value in metric.get("values", []):
            date_time = datetime.fromtimestamp(ts)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        sn = metric["metric"].get("sn", None)
        index_value = metric["metric"].get("index_value")
        interface_name = interface_dict[sn].get(index_value) if index_value else None

        if not interface_name:
            continue

        info = {
            "target": sn,
            "interface_name": interface_name,
            "index_value": metric["metric"].get("index_value", ""),
            "values": list(time_points_value.values()),
            "unit": metric["metric"].get("unit", "")
        }
        res.append(info)

    return res

def query_snmp_cpu_usage_topk(topk=5, target=None, start_time=None, end_time=None):
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    # 获取每个核的total avg并计算平均值
    if target:
        query_template = 'topk(' + str(topk) + ', avg by (sn) (avg_over_time(snmp_cpu_used_percentage{sn="' + target + '"}[' + delta_time + '])))'
    else:
        query_template = 'topk(' + str(topk) + ', avg by (sn) (avg_over_time(snmp_cpu_used_percentage['+ delta_time +'])))'

    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = 'avg by (sn) (snmp_cpu_used_percentage{sn="' + metric['metric']['sn'] + '"})'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    units = {}
    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]
            if "unit" in metric["metric"]:
                units = metric["metric"]["unit"]

        info = {
            'target': metric['metric']['sn'],
            "interface_name": "cpu",
            "values": list(time_points_value.values()),
            'unit': units
        }
        res.append(info)

    return res

def query_snmp_memory_usage_topk(topk=5, target=None, start_time=None, end_time=None):
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    if target:
        query_template = f'topk({topk}, avg by (sn) (avg_over_time(snmp_memory_used_percentage{{sn="{target}"}}[{delta_time}])))'
    else:
        query_template = f'topk({topk}, avg by (sn) (avg_over_time(snmp_memory_used_percentage[{delta_time}])))'

    result = query_prometheus(query_template, timestamp)

    if not result:
        if target:
            query_template = f'topk({topk}, avg by (sn) (avg_over_time(snmp_memory_usage_size{{sn="{target}"}}[{delta_time}]) / avg_over_time(snmp_memory_size{{sn="{target}"}}[{delta_time}]])))'
        else:
            query_template = f'topk({topk}, avg by (sn) (avg_over_time(snmp_memory_usage_size[{delta_time}]) / avg_over_time(snmp_memory_size[{delta_time}]])))'
        result = query_prometheus(query_template, timestamp)

    range_query_template = []
    for metric in result:
        if "snmp_memory_used_percentage" in query_template:
            query_template = f'avg by (sn) (snmp_memory_used_percentage{{sn="{metric["metric"]["sn"]}"}})'
        else:
            query_template = f'avg by (sn) (snmp_memory_usage_size{{sn="{metric["metric"]["sn"]}"}} / snmp_memory_size{{sn="{metric["metric"]["sn"]}"}})'

        range_query_template.append(query_template)

    final_query = " or ".join(range_query_template)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    units = {}
    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]
            if "unit" in metric["metric"]:
                units = metric["metric"]["unit"]

        info = {
            'target': metric['metric']['sn'],
            "interface_name": "memory",
            "values": list(time_points_value.values()),
            'unit': units
        }
        res.append(info)

    return res


def query_cpu_usage_topk(topk=5, target=None, start_time=None, end_time=None):

    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    # 获取每个核的total avg并计算平均值
    if target:
        query_template = 'topk(' + str(topk) + ', avg by (target) (avg_over_time(openconfig_system:system_cpus_cpu_state_total_avg{target="' + target + '"}[' + delta_time + '])))'
    else:
        query_template = 'topk(' + str(topk) + ', avg by (target) (avg_over_time(openconfig_system:system_cpus_cpu_state_total_avg['+ delta_time +'])))'

    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = 'avg by (target) (openconfig_system:system_cpus_cpu_state_total_avg{target="' + metric['metric']['target'] + '"})'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]

        info = {
            'target': metric['metric']['target'],
            "interface_name": "cpu",
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res

def query_memory_usage_topk(topk=5, target=None, start_time=None, end_time=None):

    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    # 获取每个核的total avg并计算平均值
    if target:
        query_template = 'topk(' + str(topk) + ', 100 * avg by (target) (avg_over_time(openconfig_system:system_memory_state_used{target="' + target + '"}[' + delta_time +']) / avg_over_time(openconfig_system:system_memory_state_physical{target="' + target + '"}[' + delta_time +'])))'
    else:
        query_template = 'topk(' + str(topk) + ', 100 * avg by (target) (avg_over_time(openconfig_system:system_memory_state_used[' + delta_time +']) / avg_over_time(openconfig_system:system_memory_state_physical[' + delta_time +'])))'

    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = '100 * avg by (target) (openconfig_system:system_memory_state_used{target="' + metric['metric']['target'] + '"} / openconfig_system:system_memory_state_physical{target="' + metric['metric']['target'] + '"})'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]

        info = {
            'target': metric['metric']['target'],
            "interface_name": "memory",
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res

def query_rate_topk(metric_name, topk=5, target=None, filter=[], start_time=None, end_time=None, mode="topK"):

    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    # 对于模块数据由于不是单调递增的 所以根据sum_over_time获取delta time内时间序列的总和然后排序
    if target:
        if filter:
            interface_name = "|".join(filter)
            query_template = f'{mode}({str(topk)}, sum_over_time({metric_name}{{target="{target}", interface_name=~"{interface_name}"}}[{delta_time}]))'
        else:
            query_template = f'{mode}({str(topk)}, sum_over_time({metric_name}{{target="{target}"}}[{delta_time}]))'
    else:
        query_template = f'{mode}({str(topk)}, sum_over_time({metric_name}[{delta_time}]))'
    # print(query_template)
    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = metric_name  + '{target="' + metric['metric']['target'] + '"' + ', interface_name="' + metric["metric"]["interface_name"] + '"}'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res


def query_attenuation_topk(topk=5, target=None, start_time=None, end_time=None):

    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    if target:
        query_template = 'topk(' + str(topk) + ', sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power{target="' + target + '"}['+ delta_time +']) -' +\
                            'sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power{target="' + target + '"}['+ delta_time +']))'
    else:
        query_template = 'topk(' + str(topk) + ', sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power['+ delta_time +']) -' +\
                            'sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power['+ delta_time +']))'

    result = query_prometheus(query_template, timestamp)

    metriclabel = []
    for metric in result:
        info = {
            'name': "attenuation",
            'target': metric['metric']['target'],
            'interface_name': metric["metric"]["interface_name"],
        }
        metriclabel.append(info)

    query_templates = []
    for metric in result:
        query_template = "openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power - openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power" \
                        + '{target="' + metric['metric']['target'] + '"' + ', interface_name="' + metric["metric"]["interface_name"] + '"}'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)


    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res


def query_lldp_neighbor_state(target_list, time=None):
    query_template = '{__name__="openconfig_lldp:lldp_interfaces_interface_neighbors_neighbor_state", target=~"' + '|'.join(target_list) + '"}'
    result = query_prometheus(query_template, time)
    lldp_neighbor_state = {}
    for res in result:
        state = {
            "source": res["metric"]["target"],
            "target_mac": res["metric"]["chassis_id"],
            "source_port": res["metric"]["interface_name"],
            "target_port": res["metric"]["port_id"],
        }
        source = state['source']
        source_port = state['source_port']
        key = f"{source}_{source_port}"
        # 暂定如果有重复的只取一个，有重复的说明当前时刻有变化
        if key not in lldp_neighbor_state:
            lldp_neighbor_state[key] = state

    return lldp_neighbor_state


def query_lldp_state(target, mac="", time=None):
    query_template = '{__name__="openconfig_lldp:lldp_state", target="' + target + '"}'
    result = query_prometheus(query_template, time)
    if result and result[0]["metric"].get("chassis_id"):
        state = {
            "mac_addr" : result[0]["metric"]["chassis_id"],
            "system_name" : result[0]["metric"]["system_name"],
            "system_description" : result[0]["metric"]["system_description"],
            "monitor_status": "online"
        }
    else:
        state = {
            "monitor_status": "offline",
            "mac_addr" : mac
        }
    return state


def query_node_metric(instance, metric, search_fields = {}):
    if not search_fields.get("value"):
        query_template  = '{{__name__="{name}", instance="{instance}"}}'
        modified_query = query_template.format(name=metric, instance=instance)
    else:
        query_template = '{{__name__="{name}", instance="{instance}", {field}=~"(?i).*{value}.*"}}'

        fields = search_fields.get("fields", [])
        value = search_fields.get("value", "")
        search_conditions = []
        for field in fields:
            search_conditions.append(query_template.format(name=metric, instance=instance, field=field, value=value))
        modified_query = " or ".join(search_conditions)

    result = query_prometheus(modified_query)

    metric_data = {}
    for data in result:
        if not data["metric"].get("device"):
            return data["metric"]
        else:
            device = data["metric"]["device"]

            if device not in metric_data:
                metric_data[device] = []

            metric = data["metric"]
            metric.pop("__name__")
            metric.pop("instance")
            metric.pop("job")
            metric.pop("device")
            metric_data[device] = metric
    return metric_data


def query_node_range_metric(metric, start_time=None, end_time=None):
    query_template  = '{{__name__="{name}"}}'

    _, _, start_time, end_time, step = format_query_time(start_time, end_time)
    modified_query = query_template.format(name=metric)
    result = query_range_prometheus(modified_query, start_time, end_time, step)
    metric_data = {}
    for data in result:

        instance = data["metric"]["instance"]

        if instance not in metric_data:
            metric_data[instance] = []

        metric = data["metric"]
        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        metric_data[instance].append(metric)
    return metric_data


def query_node_topk(metric_name, topk=5, filter=None, start_time=None, end_time=None):

    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    if filter:
        filter_list = []
        for key, value in filter.items():
            instance = key + ":9100"
            devices = "|".join(value)
            filter_template = f"max_over_time({metric_name}{{instance=\"{instance}\", device=~\"{devices}\"}}[{delta_time}])"
            filter_list.append(filter_template)

        filter_query = " or ".join(filter_list)
        query_template = f'topk({str(topk)}, ({filter_query}))'
    else:
        query_template = f'topk({str(topk)}, max_over_time({metric_name}[{delta_time}]) *on(device, instance) max by (instance, device) (max_over_time(node_nic_info[{delta_time}])))'

    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = f"{metric_name}{{instance=\"{metric['metric']['instance']}\", device=\"{metric['metric']['device']}\"}}"
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'instance': metric['metric']['instance'].split(":")[0],
            'device': metric['metric']['device'],
            "values": list(time_points_value.values())
        }

        res.append(info)

    return res


def query_dlb_rate_topk(dividend_metric_name, divisor_metric_name, topk=5, target=None, filter=[], start_time=None, end_time=None, mode="topK"):

    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    if target:
        if filter:
            interface_name = "|".join(filter)
            dividend_metric = f'{{__name__="{dividend_metric_name}", target="{target}", interface_name=~"{interface_name}"}}'
            divisor_metric = f'{{__name__="{divisor_metric_name}", target="{target}", interface_name=~"{interface_name}"}}'
        else:
            dividend_metric = f'{{__name__="{dividend_metric_name}", target="{target}"}}'
            divisor_metric = f'{{__name__="{divisor_metric_name}", target="{target}"}}'

        unless_metric_template = f' unless({{__name__="{divisor_metric_name}", target="{target}"}} == 0)' # 排除除数为0的指标进行排序
    else:
        dividend_metric = f'{{__name__="{dividend_metric_name}"}}'
        divisor_metric = f'{{__name__="{divisor_metric_name}"}}'
        unless_metric_template = f' unless({{__name__="{divisor_metric_name}"}} == 0)'
    metric_template = f'({dividend_metric} / on(interface_name, target) group_left {divisor_metric} * 100)'
    query_template = f'{mode}({str(topk)}, max_over_time({metric_template}[{delta_time}:{step}]){unless_metric_template})'

    LOG.info(query_template)
    result = query_prometheus(query_template, timestamp)

    # 如果个数不够再考虑获取除数为0的指标, 排除上一次的查询结果
    if len(result) < topk:
        interface_name_dict = {}
        for metric in result:
            metric_target = metric['metric']['target']
            metric_interface_name = metric['metric']['interface_name']

            if metric_target not in interface_name_dict:
                interface_name_dict[metric_target] = []

            interface_name_dict[metric_target].append(metric_interface_name)

        unless_metric_template = ""
        for metric_target, interface_name_list in interface_name_dict.items():
            interface_name = "|".join(interface_name_list)
            unless_metric_template += f'unless({{__name__="{divisor_metric_name}", target="{metric_target}", interface_name=~"{interface_name}"}}) '

        query_template = f'{mode}({str(topk - len(result))}, max_over_time({metric_template}[{delta_time}:{step}]){unless_metric_template})'
        LOG.info(query_template)
        result2 = query_prometheus(query_template, timestamp)
        result = result+ result2

    query_templates = []
    for metric in result:
        # print(metric['metric']['target'], metric['metric']['interface_name'])
        dividend_metric = f'{{__name__="{dividend_metric_name}", target="{metric["metric"]["target"]}", interface_name="{metric["metric"]["interface_name"]}"}}'
        divisor_metric = f'({{__name__="{divisor_metric_name}", target="{metric["metric"]["target"]}", interface_name="{metric["metric"]["interface_name"]}"}})'
        metric_template = f'({dividend_metric} / on(interface_name, target) {divisor_metric} * 100)'
        query_templates.append(metric_template)

    final_query = " or ".join(query_templates)
    # print(final_query)
    result = query_range_prometheus(final_query, start_time, end_time, step)
    # print(result)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            if value == "+Inf" or value == "NaN":
                value = 0
            time_points_value[date_time_str] = [date_time_str, round(float(value), 8)]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res


def query_fan_data(target, start_time=None, end_time=None):

    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    fan_state = query_metric("openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:fan", target, timestamp)
    fan_position = {fan['fan_index']: fan['position'] for fan in fan_state}
    rear_fan_state = query_metric("openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:rear_fan", target, timestamp)
    rear_fan_position = {fan['rear_fan_index']: fan['position'] for fan in rear_fan_state}

    query_template = f'{{__name__=~"openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:(fan_pwm|rear_fan_pwm)", target="{target}"}}'

    result = query_range_prometheus(query_template, start_time, end_time, step)
    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, str(round(float(value) * 100, 4))]

        info ={
            'target': metric['metric']['target'],
            "values": list(time_points_value.values())
        }

        if 'fan_index' in metric['metric']:
            info['fan_index'] = metric['metric']['fan_index']
            info['interface_name'] = fan_position.get(metric['metric']['fan_index'], None)
        else:
            info['rear_fan_index'] = metric['metric']['rear_fan_index']
            info['interface_name'] = rear_fan_position.get(metric['metric']['rear_fan_index'], None)

        res.append(info)

    return res


def query_interface_nic(targetName, time=None):
    query_template  = 'label_replace(openconfig_network_instance:network_instances_network_instance_fdb_mac_table_entries_entry_interface_interface_ref_state{{target="{target_value}"}}, "address","$1","entry_mac_address", "(.*)")' + \
                      '* on(address) group_left(operstate, instance, device) (node_network_info unless (node_network_info{{device="lo"}}))' + \
                      '* on(device, instance) group_left(name) node_nic_info' + \
                      '* on(interface, target) group_left(oper_status, mtu) ' + \
                      'label_replace(openconfig_interfaces:interfaces_interface_state{{target="{target_value}"}}, "interface","$1","interface_name", "(.*)")'
    modified_query = query_template.format(target_value=targetName)
    print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    res = []
    for data in result:
        metric_value = float(data["value"][1])
        if not metric_value:
            continue
        mac_info = data["metric"]
        mac_info.pop("job")
        mac_info["instance"] = mac_info["instance"].split(":")[0]
        res.append(mac_info)
    return res


def get_lldp_neighbor(target_ip, sn, username, passwd, port="9339"):
    res = {}
    try:
        tmp = []
        with gNMIclient(target=(target_ip, port), username=username, password=passwd, timeout=15) as gc:
            result = gc.get(path=['openconfig-lldp:lldp'])
            for notification in result["notification"]:
                for update in notification["update"]:
                    lldp_interface = update['val']['openconfig-lldp:interfaces']['interface']
                    for interface in lldp_interface:
                        if interface.get("neighbors", {}):
                            for neighbor in interface["neighbors"]["neighbor"]:
                                info = {
                                    "source_mac": update['val']['openconfig-lldp:state']['chassis-id'],
                                    "target_mac": neighbor["state"]['chassis-id'],
                                    "source_port": interface["name"],
                                    "target_port": neighbor["state"]['port-id']
                                }
                                tmp.append(info)
        res[sn] = tmp
    except Exception as e:
        LOG.error(traceback.format_exc())
    return res


def lldp_refresh(sn_list):
    """
    return
        {
            'source_sn_value': {
                'target_sn_value': [{
                        'source_mac': '',
                        'target_mac': '',
                        'source_port': '',
                        'target_port': ''
                    },
                ],
            },
        }
    """
    new_neighbor = {}
    try:
        session = monitor_db.get_session()

        query_node = session.query(MonitorTarget).filter(MonitorTarget.sn.in_(sn_list)).filter(MonitorTarget.device_type == 1).all()
        mac_sn_dict = {monitor_target.switch.mac_addr: monitor_target.sn for monitor_target in query_node }

        lldp_neighbor = query_lldp_neighbor_state(sn_list)

        for neighbor in lldp_neighbor.values():
            source_sn = neighbor.pop("source")
            target_mac = neighbor["target_mac"].lower()
            if target_mac in mac_sn_dict:
                target_sn = mac_sn_dict[target_mac]

                if source_sn not in new_neighbor:
                    new_neighbor[source_sn] = {}
                if target_sn not in new_neighbor[source_sn]:
                    new_neighbor[source_sn][target_sn] = []

                new_neighbor[source_sn][target_sn].append(neighbor)

        return new_neighbor
    except Exception as e:
        LOG.error(traceback.format_exc())
    return new_neighbor


def query_module_link():
    query_template = 'openconfig_lldp:lldp_interfaces_interface_neighbors_neighbor_state * on(interface_name, target) group_left() openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state'
    result = query_prometheus(query_template)
    lldp_neighbor_state = {}
    for res in result:
        state = {
            "source": res["metric"]["target"],
            "target_mac": res["metric"]["chassis_id"],
            "source_port": res["metric"]["interface_name"],
            "target_port": res["metric"]["port_id"],
        }
        source = state['source']
        source_port = state['source_port']
        key = f"{source}_{source_port}"
        # 暂定如果有重复的只取一个，有重复的说明当前时刻有变化
        if key not in lldp_neighbor_state:
            lldp_neighbor_state[key] = state
    return lldp_neighbor_state


def update_module_link():
    print("update_module_link")
    try:
        session_monitor = monitor_db.get_session()

        with session_monitor.begin(subtransactions=True):
            session_monitor.query(ModulesLink).filter(
                ~exists().where(Switch.sn == ModulesLink.source_sn) |
                ~exists().where(Switch.sn == ModulesLink.target_sn)).delete(synchronize_session=False)

        query_node = session_monitor.query(MonitorTarget).filter(MonitorTarget.device_type == 1).all()
        mac_sn_dict = {monitor_target.switch.mac_addr: monitor_target.sn for monitor_target in query_node }

        lldp_neighbor = query_module_link()
        link_list=[]
        for neighbor in lldp_neighbor.values():
            source_sn = neighbor.pop("source")
            target_mac = neighbor["target_mac"].lower()
            if target_mac in mac_sn_dict:
                target_sn = mac_sn_dict[target_mac]
                link_list.append((source_sn, target_sn, neighbor["source_port"], neighbor["target_port"], neighbor["target_mac"]))

        print(link_list)
        session_monitor.query(ModulesLink).update({"link_status": False})
        for link in link_list:
            source_sn, target_sn, source_port, target_port, target_mac = link
            with session_monitor.begin(subtransactions=True):
                modules_link =  session_monitor.query(ModulesLink).filter(ModulesLink.source_sn == source_sn, ModulesLink.target_sn == target_sn,
                                                                ModulesLink.source_port == source_port, ModulesLink.target_port == target_port).first()

                if modules_link:
                    modules_link.link_status = True
                    modules_link.target_mac = target_mac
                else:
                    modules_link = ModulesLink(source_sn=source_sn, target_sn=target_sn, source_port=source_port, target_port=target_port,
                                            target_mac=target_mac, link_status=True, light_attenuation_threshold=5)
                    session_monitor.add(modules_link)
    except Exception as e:
        LOG.error(traceback.format_exc())


def query_nvidia_nic_port():
    query_template  = 'node_infiniband_physical_state_id * on(instance) group_left(nodename) node_uname_info'
    result = query_prometheus(query_template)
    res = {}
    for item in result:
        metric = item['metric']
        instance = metric['instance'].split(":")[0]

        if instance not in res:
            res[instance] = {
                "instance": instance,
                "nodename": metric['nodename'],
                "children": []
            }
        res[instance]["children"].append({
            "port_title": metric['device'] + " port " + metric['port'],
            "port_value": metric['device'],
        })
    return res

def query_broadcom_nic_port():
    query_template  = 'node_ethtool_info{driver="bnxt_en"} * on(instance) group_left(nodename) node_uname_info'
    result = query_prometheus(query_template)
    # print(result)
    res = {}
    for item in result:
        metric = item['metric']
        instance = metric['instance'].split(":")[0]

        if instance not in res:
            res[instance] = {
                "instance": instance,
                "nodename": metric['nodename'],
                "children": []
            }
        res[instance]["children"].append({
            "port_title": metric['device'],
            "port_value": metric['device']
        })
    return res


def query_rock_nic_port(start_time=None, end_time=None, type=""):
    _, _, start_time, end_time, step = format_query_time(start_time, end_time)

    if type == "nvidia":
        query_template  = 'node_ethtool_info{driver="mlx5_core"} * on(instance) group_left(nodename) node_uname_info'
    elif type == "broadcom":
        query_template  = 'node_ethtool_info{driver="bnxt_en"} * on(instance) group_left(nodename) node_uname_info'
    else:
        query_template  = 'node_ethtool_info{driver=~"bnxt_en|mlx5_core"} * on(instance) group_left(nodename) node_uname_info'
    result = query_range_prometheus(query_template, start_time, end_time, step)
    # print(result)
    res = {}
    for item in result:
        metric = item['metric']
        instance = metric['instance'].split(":")[0]

        if instance not in res:
            res[instance] = {
                "instance": instance,
                "nodename": metric['nodename'],
                "children": []
            }
        res[instance]["children"].append({
            "port_title": metric['device'],
            "port_value": metric['device']
        })
    return res


def query_roce_sfp_info():
    available_hosts = list(set(map(lambda x: x.ip, utils.query_host().all())))  # for host group filter
    instance_pattern = '|'.join([f"{host}:9100" for host in available_hosts])
    query_template = '{__name__=~"roce_sfp.*", instance=~"' + instance_pattern + '"}'
    result = query_prometheus(query_template)

    res = {}
    for item in result:
        metric = item['metric']
        value = item['value']
        instance = metric.pop("instance")
        device =  metric.pop("device")
        metric_name = metric.pop("__name__")
        metric.pop("job")

        if instance not in res:
            res[instance] = {}
        if device not in res[instance]:
            res[instance][device] = {}

        if metric_name == "roce_sfp_info":
            res[instance][device].update(metric)
        elif metric_name in ["roce_sfp_tx_power_dbm", "roce_sfp_rx_power_dbm", "roce_sfp_tx_bias_current_ma"]:
            channel = int(metric["channel"])
            if metric_name not in res[instance][device]:
                res[instance][device][metric_name] = {}
            res[instance][device][metric_name][channel] = value[1]
        else:
            res[instance][device][metric_name] = value[1]

    roce_info = []
    for key, (instance, devices_info) in enumerate(res.items()):
        query_template  = '{__name__="node_uname_info", instance="' + instance +'"}'
        result = query_prometheus(query_template)
        hostname = result[0]["metric"]["nodename"]

        nic_info = query_node_metric(instance=instance, metric="node_nic_info")

        info = {
            "name": hostname,
            "id": key + 1,
            "children": []
        }

        for index, (device, values) in enumerate(devices_info.items()):
            for field in ["roce_sfp_tx_power_dbm", "roce_sfp_rx_power_dbm", "roce_sfp_tx_bias_current_ma"]:
                if field in values:
                    field_values = values[field]
                    field_str = ','.join(field_values[i] for i in sorted(field_values.keys()))
                    values[field] = field_str
            values["device"] = device
            values["id"] = int(str(key+1) + str(index+1))
            if device in nic_info:
                values["name"] = nic_info[device]["name"]
            info["children"].append(values)

        roce_info.append(info)

    return roce_info


def query_interface_mac_address():
    """
    [
        {
            "sn": "testsn",
            "mac_address": "00:0c:29:d8:a5:a5",
            "port": "te-1/1/1"
        },
        {
            "sn": "testsn",
            "mac_address": "64:9d:99:d7:7d:de",
            "port": "te-1/1/1"
        }
    ]
    """
    query_template = 'openconfig_network_instance:network_instances_network_instance_fdb_mac_table_entries_entry_interface_interface_ref_state'
    result = query_prometheus(query_template)
    # print(result)
    res = []
    seen = set()
    for item in result:
        mac_address = item["metric"]["entry_mac_address"]
        sn = item["metric"]["target"]
        port = item["metric"]["interface"]

        identifier = (mac_address, sn)
        if identifier not in seen:
            seen.add(identifier)
            info = {
                "mac_address": mac_address.lower(),
                "sn": sn,
                "port": port
            }
            res.append(info)
    return res


def query_interface_mac_address_by_target(target, time=None):
    """
    [
        {
            "sn": "testsn",
            "mac_address": "00:0c:29:d8:a5:a5",
        },
        {
            "sn": "testsn",
            "mac_address": "64:9d:99:d7:7d:de",
        }
    ]
    """
    query_template  = f'{{__name__="openconfig_network_instance:network_instances_network_instance_fdb_mac_table_entries_entry_interface_interface_ref_state", target="{target}"}}'
    result = query_prometheus(query_template, time=time)
    # print(result)
    res = []
    seen = set()
    for item in result:
        mac_address = item["metric"]["entry_mac_address"]
        sn = item["metric"]["target"]
        port = item["metric"]["interface"]

        identifier = (mac_address, sn)
        if identifier not in seen:
            seen.add(identifier)
            info = {
                "mac_address": mac_address.lower(),
                "sn": sn,
                "port": port
            }
            res.append(info)
    return res


def update_client_data(mac_info_list):
    session = inven_db.get_session()
    switch_mac_list = list(map(lambda x: x[0], session.query(Switch.mac_addr).filter(Switch.mac_addr != None).all()))
    with session.begin(subtransactions=True):
        session.query(ClientDeviceInfo).filter(ClientDeviceInfo.mac_address.in_(switch_mac_list)).delete()

    mac_info_set = set((item['sn'], item['mac_address'], item['port']) for item in [i for i in mac_info_list if i['mac_address'] not in switch_mac_list])
    sn_mac_port_dict = {(item['sn']+item['mac_address']): item for item in mac_info_list if item['mac_address'] not in switch_mac_list}
    # key sn+mac+port value ip
    sn_mac_port_ip_address_dict = {(item['sn']+item['mac_address']+item['port']): item.get('ip_address', '') for item in mac_info_list if item['mac_address'] not in switch_mac_list}
    to_be_update_device_info_list = []

    session = inven_db.get_session()
    # update client device data
    try:
        with session.begin(subtransactions=True):
            existing_client_devices = session.query(ClientDeviceInfo).execution_options(populate_existing=True).all()
            for current_client_device in existing_client_devices:
                current_client_device.client_name = current_client_device.mac_address if not current_client_device.client_name else current_client_device.client_name
                # exact match switch_sn mac_address and port
                current_client_device.ip_source = 'static' if not current_client_device.ip_source or current_client_device.ip_source != 'dhcp' else current_client_device.ip_source
                if (current_client_device.switch_sn, current_client_device.mac_address, current_client_device.port) in mac_info_set:
                    if current_client_device.state == 'offline':
                        current_client_device.update_time = func.now()
                    current_client_device.state = 'online'
                    current_client_device.ip_address = sn_mac_port_ip_address_dict.get(
                        current_client_device.switch_sn + current_client_device.mac_address + current_client_device.port
                    ) or current_client_device.ip_address
                    current_client_device.ip_source = (
                        'dhcp' if sn_mac_port_ip_address_dict.get(
                            current_client_device.switch_sn + current_client_device.mac_address + current_client_device.port
                        ) else current_client_device.ip_source or 'static'
                    )
                    current_client_device.manufacturer = get_organization_name_by_mac(current_client_device.mac_address) if not current_client_device.manufacturer else current_client_device.manufacturer
                    mac_info_set.remove((current_client_device.switch_sn, current_client_device.mac_address, current_client_device.port))
                # can match switch_sn and mac_address but port cannot be matched, then try update record which port is empty
                elif not current_client_device.port and (current_client_device.switch_sn + current_client_device.mac_address) in sn_mac_port_dict.keys() and (current_client_device.switch_sn, current_client_device.mac_address) not in to_be_update_device_info_list:
                    if mac_info_set:
                        target_mac_info = sn_mac_port_dict[current_client_device.switch_sn+current_client_device.mac_address]
                        to_be_update_device_info_list.append((current_client_device.switch_sn, current_client_device.mac_address, target_mac_info['port']))
                        mac_info_set.remove((current_client_device.switch_sn, current_client_device.mac_address,target_mac_info['port']))
                # cannot match switch_sn and mac_address, then set state to offline
                # or can match switch_sn and mac_address but port cannot be matched and there is no record which port is empty
                # then set state to offline
                else:
                    if current_client_device.state == 'online':
                        current_client_device.update_time = func.now()
                    current_client_device.state = 'offline'
                    current_client_device.manufacturer = get_organization_name_by_mac(current_client_device.mac_address) if not current_client_device.manufacturer else current_client_device.manufacturer
            for to_be_update_device_info in to_be_update_device_info_list:
                to_be_update_device = session.query(ClientDeviceInfo).filter(and_(ClientDeviceInfo.port.is_(None), ClientDeviceInfo.switch_sn == to_be_update_device_info[0], ClientDeviceInfo.mac_address == to_be_update_device_info[1])).first()
                if to_be_update_device is None:
                    LOG.error("Update client not exist")
                    LOG.error(to_be_update_device_info)
                    continue
                to_be_update_device.state = 'online'
                to_be_update_device.update_time = func.now()
                to_be_update_device.manufacturer = get_organization_name_by_mac(to_be_update_device.mac_address) if not to_be_update_device.manufacturer else to_be_update_device.manufacturer
                to_be_update_device.port = to_be_update_device_info[2]
                to_be_update_device.ip_address = sn_mac_port_ip_address_dict.get(
                    to_be_update_device.switch_sn + to_be_update_device.mac_address + to_be_update_device.port,
                    '') or to_be_update_device.ip_address
                to_be_update_device.ip_source = 'dhcp' if to_be_update_device.ip_address else 'static'
            for to_be_insert_device_info in mac_info_set:
                to_be_insert_device_switch_sn, to_be_insert_device_mac_address, to_be_insert_device_port = to_be_insert_device_info
                if to_be_insert_device_mac_address not in switch_mac_list:
                    to_be_insert_device = ClientDeviceInfo(
                        switch_sn=to_be_insert_device_switch_sn,
                        mac_address=to_be_insert_device_mac_address,
                        ip_address=sn_mac_port_ip_address_dict.get(
                            to_be_insert_device_switch_sn + to_be_insert_device_mac_address + to_be_insert_device_port,
                            ''),
                        client_name=to_be_insert_device_mac_address,
                        manufacturer=get_organization_name_by_mac(to_be_insert_device_mac_address),
                        port=to_be_insert_device_port,
                        state='online',
                        update_time=func.now()
                    )
                    to_be_insert_device.ip_source = 'dhcp' if to_be_insert_device.ip_address else 'static'
                    session.add(to_be_insert_device)
    except Exception as e:
        session.rollback()
        LOG.error("Update client device data fail")
        LOG.error(traceback.format_exc())


def query_dhcp_snooping_info():
    query_template = 'fsconfig_dhcp_snooping:dhcp_snooping_state_entries_entry'
    result = query_prometheus(query_template)
    res = []
    seen = set()
    for item in result:
        vlan_id = item["metric"]["vlan_id"]
        mac_address = item["metric"]["mac_address"]
        port = item["metric"]["port"]
        ip_address = item["metric"]["ip_address"]
        lease = item["metric"]["lease"]
        sn = item["metric"]["target"]

        identifier = (sn, mac_address, port)
        if identifier not in seen:
            seen.add(identifier)
            info = {
                "vlan_id": vlan_id,
                "mac_address": mac_address.lower(),
                "port": port,
                "ip_address": ip_address,
                "lease": lease,
                "sn": sn
            }
            res.append(info)
    return res


def query_dhcp_snooping_info_by_target_and_mac(target, mac, port, time=None):
    query_template = f'{{__name__="fsconfig_dhcp_snooping:dhcp_snooping_state_entries_entry", target="{target}", mac_address="{mac}", port="{port}"}}'
    result = query_prometheus(query_template, time)
    res = []
    seen = set()
    for item in result:
        vlan_id = item["metric"]["vlan_id"]
        mac_address = item["metric"]["mac_address"]
        port = item["metric"]["port"]
        ip_address = item["metric"]["ip_address"]
        lease = item["metric"]["lease"]
        sn = item["metric"]["target"]

        identifier = (sn, mac_address, port)
        if identifier not in seen:
            seen.add(identifier)
            info = {
                "vlan_id": vlan_id,
                "mac_address": mac_address.lower(),
                "port": port,
                "ip_address": ip_address,
                "lease": lease,
                "sn": sn
            }
            res.append(info)
    return res[0] if res else None


def query_mlag_info_by_target(target, time=None):
    query_mlag_config_template  = f'{{__name__="fsconfig_mlag:mlag_config", target="{target}"}}'
    query_mlag_state_template  = f'{{__name__="fsconfig_mlag:mlag_state", target="{target}"}}'
    mlag_config = query_prometheus(query_mlag_config_template, time=time)
    mlag_state = query_prometheus(query_mlag_state_template, time=time)

    if not mlag_config and not mlag_state:
        return []

    return [{
        'domain_id': mlag_config[0]['metric'].get('domain_id', '') if mlag_config else None,
        'domain_mac': mlag_config[0]['metric'].get('domain_mac', '') if mlag_config else '',
        'node_id': mlag_config[0]['metric'].get('node_id', '') if mlag_config else None,
        'peer_link': mlag_config[0]['metric'].get('peer_link', '') if mlag_config else '',
        'peer_ip': mlag_config[0]['metric'].get('peer_ip', '') if mlag_config else '',
        'peer_vlan': mlag_config[0]['metric'].get('peer_vlan', '') if mlag_config else None,
        'neighbor_status': mlag_state[0]['metric'].get('neighbor_status', '') if mlag_state else '',
        'config_matched': mlag_state[0]['metric'].get('config_matched', '') if mlag_state else '',
        'mac_synced': mlag_state[0]['metric'].get('mac_synced', '') if mlag_state else '',
        'num_of_links': mlag_state[0]['metric'].get('num_of_links', 0) if mlag_state else 0,
    }]


def query_poe_info_by_target(target, time=None):
    query_poe_state_template = f'{{__name__="openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_openconfig_if_poe:poe_state", target="{target}"}}'
    poe_state = query_prometheus(query_poe_state_template, time=time)

    if not poe_state:
        return []

    data_dict = {}

    for poe_state_detail in reversed(poe_state):
        data_dict[poe_state_detail['metric'].get('interface_name', '') + target] = {
            'interface_name': poe_state_detail['metric'].get('interface_name', ''),
            'enabled': poe_state_detail['metric'].get('enabled', ''),
            'power_used': poe_state_detail['metric'].get('power_used', None),
            'power_class': poe_state_detail['metric'].get('power_class', '')
        }

    return list(reversed(data_dict.values()))


def get_modules_count(target=None):
    target_str = ""
    if target:
        target_str = f'target=~"{"|".join(target)}"'
    query_template = f"""
        count by (fsconfig_platform_transceiver_extensions_transmission_rate) (
            count by (fsconfig_platform_transceiver_extensions_transmission_rate, interface_group, target) (
                label_replace(
                    openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state{{interface_name=~"[a-z]+-1/1/[0-9]+.*",{target_str}}},
                    "interface_group",
                    "$1-$2",
                    "interface_name",
                    "([a-z]+)-(1/1/[0-9]+)[^,]*"
                )
            )
        )
    """
    result = query_prometheus(query_template)
    res = {}
    for item in result:
        metric = item.get('metric', {})
        rate = metric.get('fsconfig_platform_transceiver_extensions_transmission_rate', 'Unknow')
        if rate and item['value']:
            res[rate] = item['value'][1]
    return res

def get_modules_history_info(target=None, start_time=None, end_time=None):
    _, _, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_value = generate_time_points_dict(start_time, end_time, step)
    target_str = ""
    if target:
        target_str = f'target=~"{"|".join(target)}"'
    query_template = f"""
        (
            clamp_min((
                (sum(
                    label_replace(
                        openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state{{interface_name=~"[a-z]+-1/1/[0-9]+.*",{target_str}}},
                        "interface_group",
                        "$1-$2",
                        "interface_name",
                        "([a-z]+)-(1/1/[0-9]+)[^,]*"
                    )
                ) or vector(0)) - (sum(
                    label_replace(
                        openconfig_platform:components_interface_abnormal_num{{interface_name=~"[a-z]+-1/1/[0-9]+.*",{target_str}}},
                        "interface_group",
                        "$1-$2",
                        "interface_name",
                        "([a-z]+)-(1/1/[0-9]+)[^,]*"
                    )
                ) or vector(0))
            ),0)
            /
            (sum(
                label_replace(
                    openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state{{interface_name=~"[a-z]+-1/1/[0-9]+.*",{target_str}}},
                    "interface_group",
                    "$1-$2",
                    "interface_name",
                    "([a-z]+)-(1/1/[0-9]+)[^,]*"
                )
            ) or vector(0))
        ) * 100
    """
    print(query_template)
    result = query_range_prometheus(query_template, start_time, end_time, step)
    query_abnormal_template = f"""
        sum(
            label_replace(
                openconfig_platform:components_interface_abnormal_num{{interface_name=~"[a-z]+-1/1/[0-9]+.*",{target_str}}},
                "interface_group",
                "$1-$2",
                "interface_name",
                "([a-z]+)-(1/1/[0-9]+)[^,]*"
            )
        )
    """
    print(query_abnormal_template)
    abnormal_result = query_range_prometheus(query_abnormal_template, start_time, end_time, step)
    res = {
        "value": []
    }
    if result and abnormal_result:
        all_result = result[0]
        abnormal_result = abnormal_result[0]
        for index, item in enumerate(all_result["values"]):
            timestamp = item[0]
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            value = item[1] + '%'
            abnormal_count = abnormal_result["values"][index][1]
            time_points_value[date_time_str] = [date_time_str, value, abnormal_count]
        res.update({
            "value": list(time_points_value.values())
        })
    return res

def get_modules_port_status(target, port, metric, start_time=None, end_time=None, channel_index=None):
    _, _, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_value = generate_time_points_dict(start_time, end_time, step)
    thresholds = {
        "low_major": None,
        "low_warning": None,
        "high_major": None,
        "high_warning": None
    }
    query_low = ''
    query_high = ''
    filter_str = f'channel_index="{channel_index}"' if channel_index else ''
    common_prefix = 'openconfig_platform:components_component_openconfig_platform_transceiver:'
    def get_thresholds(query, prefix, thresholds):
        result = query_prometheus(query, end_time)
        for item in result:
            severity = item["metric"].get("threshold_severity", "")
            if ":" in severity:
                key = f"{prefix}_{severity.split(':')[1].lower()}"
                value = item.get("value", [None, None])[1]
                thresholds[key] = value
        return thresholds
    if "temperature" in metric:
        query_low = "transceiver_thresholds_threshold_state_laser_temperature_lower"
        query_high = "transceiver_thresholds_threshold_state_laser_temperature_upper"
    elif "voltage" in metric:
        query_low = "transceiver_thresholds_threshold_state_supply_voltage_lower"
        query_high = "transceiver_thresholds_threshold_state_supply_voltage_upper"
    elif "bias" in metric:
        query_low = "transceiver_thresholds_threshold_state_laser_bias_current_lower"
        query_high = "transceiver_thresholds_threshold_state_laser_bias_current_upper"
    elif "output" in metric:
        query_low = "transceiver_thresholds_threshold_state_output_power_lower"
        query_high = "transceiver_thresholds_threshold_state_output_power_upper"
    else:
        query_low = "transceiver_thresholds_threshold_state_input_power_lower"
        query_high = "transceiver_thresholds_threshold_state_input_power_upper"

    query_low = f"""{common_prefix}{query_low}{{interface_name="{port}", target="{target}"}}"""
    thresholds = get_thresholds(query_low, "low", thresholds)
    query_high = f"""{common_prefix}{query_high}{{interface_name="{port}", target="{target}"}}"""
    thresholds = get_thresholds(query_high, "high", thresholds)
    query_template = f"""{common_prefix}{metric}{{interface_name="{port}", target="{target}", {filter_str}}}"""
    result = query_range_prometheus(query_template, start_time, end_time, step)
    res = {
        "threshold": thresholds,
        "value": [],
        "sn": target,
        "interface_name": port
    }
    if result:
        item = result[0]
        for timestamp, value in item["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]
        res.update({
            "sn": item["metric"].get("target"),
            "interface_name": item["metric"].get("interface_name"),
            "value": list(time_points_value.values())
        })
        if item["metric"].get("channel_index", None) is not None:
            res["channel_index"] = item["metric"]["channel_index"]
    return res

def get_light_attenuation(port_info, start_time=None, end_time=None):
    _, _, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)
    res = []
    for item in port_info:
        source_interface = item['source_port']
        source_sn = item['source_sn']
        target_interface = item['target_port']
        target_sn = item['target_sn']
        target_mac = item['target_mac']
        query_template = f"""
            openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power
                {{interface_name="{source_interface}",target="{source_sn}"}}
                    * on () group_left ()
                        (group by (interface_name, target, port_id, chassis_id) (
                            openconfig_lldp:lldp_interfaces_interface_neighbors_neighbor_state
                                {{chassis_id="{target_mac}",interface_name="{source_interface}",port_id="{target_interface}",target="{source_sn}"}}
                        ))
            -
            on(channel_index) group_left() 
                (openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power
                    {{interface_name="{target_interface}", target="{target_sn}"}}  * on() group_left() 
                        (group by (interface_name, target, port_id, chassis_id) (
                            openconfig_lldp:lldp_interfaces_interface_neighbors_neighbor_state
                                {{chassis_id="{target_mac}",interface_name="{source_interface}",port_id="{target_interface}",target="{source_sn}"}}
        )))
        """
        result = query_range_prometheus(query_template, start_time, end_time, step)
        for item in result:
            time_points_value = copy.deepcopy(time_points_dict)
            for timestamp, value in item["values"]:
                date_time = datetime.fromtimestamp(timestamp)
                date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
                time_points_value[date_time_str] = [date_time_str, f'{float(value):.2f}']
            info = {
                'channel_index': item['metric']['channel_index'],
                'target': item['metric']['target'],
                'interface_name': item['metric']['interface_name'],
                'values': list(time_points_value.values())
            }
            res.append(info)
    return res

def query_snmp_sn_list():
    pql = 'snmp_device_label{}'
    results = query_prometheus(pql)

    sn_list = []
    for item in results:
        metric = item.get("metric", {})
        sn = metric.get("sn")
        if sn:
            sn_list.append(sn)
    return sn_list
