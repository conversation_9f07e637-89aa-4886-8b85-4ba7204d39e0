from pyVim.connect import SmartConnect, Disconnect
from pyVmomi import vim, vmodl
import ssl
import json

status_mapping = {
    "green": "Normal",
    "yellow": "Warning",
    "red": "Error",
    "gray": "Unknown"
}

class VSphereDriver():
    def __init__(self, host, username, password, port=443):
        # 禁用 SSL 证书验证     
        context = ssl._create_unverified_context()
        # 连接到 vCenter
        self.connection = SmartConnect(
            host=host,
            user=username,
            pwd=password,
            port=port,
            sslContext=context
        )
    def __del__(self):
        if hasattr(self, 'connection') and self.connection:
            try:
                Disconnect(self.connection)
            except Exception as _:
                pass

    def get_full_required_info(self):
        datacenters, vms, portgroups, hosts = self.list_datacenters()
        used_network = {}
        
        for item in portgroups:
            host_name = item['host_name']
            if host_name not in used_network:
                used_network[host_name] = []
            used_network[host_name].append(item)
        
        return {
            "projects_info": datacenters,
            "vms_info": vms,
            "networks_info": portgroups,
            "hosts_info": hosts,
            "used_network": used_network
        }

    def list_datacenters(self):
        res_datacenters = []
        res_vms = []
        res_portgroups = []
        res_hosts = []
        content = self.connection.RetrieveContent()
        datacenters = content.rootFolder.childEntity
        for dc in datacenters:
            res_datacenters.append({
                "name": dc.name,
                "id": dc._moId
            })
            vms = self._get_vms_in_datacenters(dc.vmFolder, dc.name)
            res_vms.extend(vms)
            host_list, standard_portgroups = self._get_standard_portgroup_info(dc)
            res_portgroups.extend(standard_portgroups)
            res_hosts.extend(host_list)
        return res_datacenters, res_vms, res_portgroups, res_hosts
    
    def _get_vms_in_datacenters(self, folder, datacenter_name):
        vms = []
        for item in folder.childEntity:
            if isinstance(item, vim.VirtualMachine):
                network_names = []
                if item.config:
                    for device in item.config.hardware.device:
                        if isinstance(device, vim.vm.device.VirtualEthernetCard):
                            if device.backing.network:
                                network_names.append(device.backing.network.name)
                vms.append({
                    'id': item._moId,
                    'name': item.name,
                    'status': status_mapping.get(item.overallStatus, "Unknown"),
                    'image_name': item.config.guestFullName if item.config else "",
                    'project_name': datacenter_name,
                    'networks': network_names,
                    'vm_ip': [item.guest.ipAddress] if item.guest.ipAddress else [],
                    'hypervisor_hostname': item.runtime.host.name, 
                    'hypervisor_ip': item.runtime.host.summary.managementServerIp
                })
            elif isinstance(item, vim.Folder):
                vms.extend(self._get_vms_in_datacenters(item, datacenter_name))
        return vms
    
    def _get_standard_portgroup_info(self, dc):
        standard_portgroups = []
        host_list = []
        for cluster in dc.hostFolder.childEntity:
            try:
                for host in cluster.host:
                    host_info = {
                        "host_name": host.name, 
                        "host_ip": host.name,
                        "project_name": dc.name,
                        "project_id": dc._moId,
                    }
                    host_list.append(host_info)
                    network_system = host.configManager.networkSystem
                    for portgroup in network_system.networkInfo.portgroup:
                        portgroup_info = {
                            "name": portgroup.spec.name,
                            "project_name": dc.name,
                            "project_id": dc._moId,
                            "host_name": host.name,
                            "vlan_id": portgroup.spec.vlanId,
                            "vm_counter": 0
                        }
                        for vm in host.vm:
                            if vm.config:
                                for device in vm.config.hardware.device:
                                    if isinstance(device, vim.vm.device.VirtualEthernetCard):
                                        if device.backing.network and device.backing.network.name == portgroup.spec.name:
                                            portgroup_info["vm_counter"] += 1
                        standard_portgroups.append(portgroup_info)
            except Exception as e:
                pass
        return host_list, standard_portgroups
        
if __name__ == "__main__":
    driver = VSphereDriver(
        host='**********',
        username='<EMAIL>',
        password='Admin@fs123',
        port=443,
    )
    print(driver.get_full_required_info())