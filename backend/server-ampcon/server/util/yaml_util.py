import threading
import yaml
from jinja2 import Template

class YAMLEditor:
    
    lock_class = threading.Lock

    _instance = None
    
    def __new__(cls, *args: tuple, **kwargs: dict[str, any]):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, ) -> None:
        self.lock = self.lock_class()

    def read(self, yaml_path: str) -> dict:
        with self.lock:
            with open(yaml_path, "r", encoding="utf-8") as f:
                file_data = f.read()
                # yaml 未分段使用 safe_load方法，分段使用 safe_load_all 方法
                data = yaml.safe_load(file_data)
                return data

    def write(self, yaml_path: str, data: dict,) -> None:
        with self.lock:
            with open(yaml_path, "w+", encoding="utf-8") as f:
                return yaml.safe_dump(data, f)

    def render(self, yaml_path: str, template_path: str, data: dict) -> None:
        with open(template_path, 'r') as t:
            template = Template(t.read())

        with self.lock:
            with open(yaml_path, "w+", encoding="utf-8") as f:
                rendered_template = template.render(config=data)
                return f.write(rendered_template)