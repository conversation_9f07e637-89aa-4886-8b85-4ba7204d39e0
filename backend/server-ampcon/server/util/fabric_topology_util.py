import ipaddress
import asyncio
from datetime import datetime, timedelta
from server.db.models.dc_blueprint import dc_fabric_db, DCLogicalNetwork, DCLogicalInterfaceConfig, DCLogicalRouterConfig, DCLogicalPortConfig, DCFabricTopology, DCFabricTopologyNode, DCLogicalInterface, DCLogicalPort
from server.db.models.resource_pool import resource_pool_asn_db, resource_pool_ip_db, resource_pool_vlandomain_db, resource_pool_vni_db, ResourcePoolVlanDomain
from server.db.models.dc_virtual_resource import DCVirtualResourceHostLinkPort, SwitchPortgroupInfo, SwitchPortgroupConfig, NodeGroup, DCVirtualResourceHost
from server.db.models.dc_virtual_resource import dc_virtual_resource_db, DCVirtualResourceNetwork, NodeNicPortgroup, DCVirtualResourceHostLinkPort, DCVirtualHostNetworkMapping
from server.db.models import inventory
from server.constants import OverlayStatus, CloudPlatform, DeployStatus
import logging
from sqlalchemy import func
from server.celery_app.config_distribution_task import config_distribution_netconf_by_dc_overlay

LOG = logging.getLogger(__name__)


def update_topology_node_info(topology, fabric_topo_id):
    links = {}
    for edge in topology['edges']:
        source_device = edge['source']
        target_device = edge['target']
        source_sn = edge['source_sn']
        target_sn = edge['target_sn']
        link_info = edge['link_info']
        link_type = edge['type']

        for link in link_info:
            logic_link = link['logic_link']
            if source_device not in links:
                links[source_device] = {}
            links[source_device][logic_link] = {
                "source_sn": source_sn,
                "target_sn": target_sn,
                "source_port": link['source_port'],
                "target_port": link['target_port'],
                "link_type": link_type,
                "source_device": source_device,
                "target_device": target_device
            }
            
            if link_type == "spine_link":
                links[source_device][logic_link]["routed_interface_address"] = link['source_routed_address']
                links[source_device][logic_link]["routed_interface_target_address"] = link['target_routed_address']

            # 更新目标链接信息
            if target_device not in links:
                links[target_device] = {}
            links[target_device][logic_link] = {
                "source_sn": target_sn,
                "target_sn": source_sn,
                "source_port": link['target_port'],
                "target_port": link['source_port'],
                "link_type": link_type,
                "source_device": target_device,
                "target_device": source_device
            }
            
            if link_type == "spine_link":
                links[target_device][logic_link]["routed_interface_address"] = link['target_routed_address']
                links[target_device][logic_link]["routed_interface_target_address"] = link['source_routed_address']

    nodes = dc_fabric_db.get_fabric_topology_node(fabric_topo_id)
    node_dict = {node['logic_device']: node for node in topology['nodes']}
    
    old_sn_dict = {}
    for node in nodes:
        if node.logic_name not in node_dict:
            dc_fabric_db.del_fabric_topology_node_by_id(node.id)
        else:
            if node.switch_sn:
                old_sn_dict[node.logic_name] = node.switch_sn    
    
    for node in topology['nodes']:
        logic_name = node['logic_device']
        link_info ={}
        neighbor_router_id_list = []
        for link_name, link in links[logic_name].items():
            modified_link = link.copy()
            modified_link["logic_name"] = link_name
            link_info.setdefault(modified_link["link_type"], []).append(modified_link)
            if modified_link["link_type"] == "spine_link":
                target_device = modified_link["target_device"]
                if node_dict[target_device]['node_info']["router_id"] and node_dict[target_device]['node_info']["router_id"] not in neighbor_router_id_list:
                    neighbor_router_id_list.append(node_dict[target_device]['node_info']["router_id"])
        
        node['node_info']['links'] = link_info
        node['node_info']["neighbor_router_id_list"] = neighbor_router_id_list
        node_id = dc_fabric_db.update_fabric_topology_node_info(logic_name, node['switch_sn'], fabric_topo_id, node['type'], node['node_info'])
        node["logic_device_id"] = node_id
        # 如果更换sn 需要清空旧的配置
        if logic_name in old_sn_dict and node['switch_sn'] != old_sn_dict[logic_name]:
            dc_fabric_db.update_fabric_topology_node_config(logic_name, fabric_topo_id, {})


# 根据template 生成topo
class TopologyBuilder:
    def __init__(self, template_info, topology_type, underlay_routing_protocol, overlay_control_protocol):
        self.template_info = template_info
        self.topology_type = topology_type
        self.underlay_routing_protocol = underlay_routing_protocol
        self.overlay_control_protocol = overlay_control_protocol
        self.router_id_pool = template_info.get("bgp_router_id", None)
        self.vtep_interface_pool = template_info.get("vtep_interface", None)
        self.areaid = template_info.get("areaid", "")
        self.underlay_ebgp_asn_pool = template_info.get("underlay_ebgp_asn", None)
        self.overlay_ibgp_asn_pool = template_info.get("overlay_ibgp_asn", None)
        self.domain_id_queue = list(range(1, 256))
        self.nodes = []
        self.edges = []

    def build_topology(self):
        if self.topology_type == "5-stage":
            self._build_5_stage_topology()
        elif self.topology_type == "3-stage":
            self._build_3_stage_topology()
        return {
            "nodes": self.nodes,
            "edges": self.edges,
        }

    def _build_5_stage_topology(self):
        pods = self.template_info.get("pod", [])
        spines_list = []
        for pod in pods:
            spine_count = pod.get("spine_count", 1)
            units = pod.get("unit", [])
            pod_name = pod.get("name")
            # print(pod, units, pod_name)
            topo = self._build_unit_and_spine(units, spine_count, link_spine_count=1, prefix=pod_name)
            spines_list.extend(topo.get("spines_name"))

        super_spine_count = self.template_info.get("super_spine_count", 1)
        self._build_spine(super_spine_count, spines_list, link_spine_count=1, name_prefix="super_spine", type="super_spine")

    def _build_3_stage_topology(self):
        spine_count = self.template_info.get("spine_count", 1)
        units = self.template_info.get("unit", [])
        # build时默认使用1个link_spine_count
        self._build_unit_and_spine(units, spine_count, link_spine_count=1)

    def _build_unit_and_spine(self, units, spine_count, link_spine_count, prefix=None):
        leaf_list = []
        for unit in units:
            # 相同unit增加序号区分
            count = unit.get('count', 1)
            for i in range(count):
                base_name = f"{prefix}_{unit.get('name')}" if prefix else unit.get('name')
                name_prefix = f"{base_name}_00{i+1}"
                topo = self._build_topology_by_unit(unit, name_prefix)
                leaf_list.extend(topo.get("leaf_list"))
            
        spine_prefix = f"{prefix}_spine" if prefix else "spine"
        topo = self._build_spine(spine_count, leaf_list, link_spine_count, name_prefix=spine_prefix)

        return {
            "spines_name": topo.get("spines_name"),
        }

    def _build_spine(self, spine_count, leafs, link_spine_count, name_prefix="spine", type="spine"):
        spines_name = []
        for i in range(1, spine_count + 1):
            self.nodes.append(self._build_topology_node(
                logic_device=f"{name_prefix}_{i}",
                unit_name="",
                node_type=type,
            ))
            spines_name.append(f"{name_prefix}_{i}")
        for leaf in leafs:
            self.edges.extend(self._build_edges(spines_name, leaf, link_spine_count, type="spine_link"))

        return {
            "spines_name": spines_name,
        }

    def _build_topology_by_unit(self, unit, name_prefix):
        leaf_list = []
        leaf_info = unit.get("unit_info", {}).get("leaf", [])
        access_info = unit.get("unit_info", {}).get("access", [])

        # 构建leaf
        for leaf in leaf_info:
            if leaf["strategy"] == "MLAG":
                self._build_mlag_leafs(leaf, name_prefix, leaf_list)
            else:
                self.nodes.append(self._build_topology_node(
                    logic_device=f"{name_prefix}_{leaf['name']}_1",
                    unit_name=name_prefix,
                    node_type="leaf",
                    node_info=leaf
                ))
                leaf_list.append(f"{name_prefix}_{leaf['name']}_1")

        # 构建access
        for access in access_info:
            self._build_access_nodes(access, name_prefix)

        return {
            "leaf_list": leaf_list,
        }

    def _build_mlag_leafs(self, leaf, name_prefix, leaf_list):
        domain_id = self.domain_id_queue.pop(0)
        leaf["domain_id"] = domain_id
        for i in range(1, 3):  # mlag 创建两个节点
            mlag_node = self._build_topology_node(
                logic_device=f"{name_prefix}_{leaf['name']}_{i}",
                unit_name=name_prefix,
                node_type="leaf",
                node_info=leaf
            )
            mlag_node["node_info"]["domain_id_node"] = i - 1
            self.nodes.append(mlag_node)
            leaf_list.append(f"{name_prefix}_{leaf['name']}_{i}")

        # mlag 构建peer link
        sources = [f"{name_prefix}_{leaf['name']}_1"]
        target = f"{name_prefix}_{leaf['name']}_2"
        count = leaf.get("mlag_peer_count", 2)
        self.edges.extend(self._build_edges(sources, target, count, type="peer_link"))

    def _build_access_nodes(self, access_info, unit_name):
        access_name = access_info.get("name")
        logic_device = f"{unit_name}_{access_name}"
        self.edges.extend(self._build_access_edges(logic_device, access_info, unit_name))
        self.nodes.append(self._build_topology_node(
            logic_device=logic_device,
            unit_name=unit_name,
            node_type="access",
            node_info=access_info
        ))

    def _build_access_edges(self, logic_device, access_info, unit_name):
        leaf = access_info.get("leaf")
        access_method = access_info.get("access_method", "")
        physic_link_count = access_info.get("physic_link_count", 1)

        # 根据method 决定source
        if access_method == "dual":
            sources = [
                f"{unit_name}_{leaf}_1",
                f"{unit_name}_{leaf}_2"
            ]
        elif access_method == "single":
            peer_leaf = access_info.get("peer_leaf")
            source = f"{unit_name}_{leaf}_1" if peer_leaf == "first" else f"{unit_name}_{leaf}_2"
            sources = [source]
        else:
            sources = [f"{unit_name}_{leaf}_1"]

        return self._build_edges(sources, logic_device, physic_link_count)

    def _build_topology_node(self, logic_device, unit_name, node_type, node_info={}):
        # todo 根据type, node_type, underlay_routing_protocol, overlay_control_protocol 决定参数
        node = {
            "logic_device": logic_device,
            "group": unit_name,
            "type": node_type,
            "switch_sn": "",
            "node_info": {}
        }
        
        if node_type == "access":
            if node_info.get("access_method", ""):
                info = {
                    "mlag_vlan_id": node_info.get("mlag_vlanid", ""),
                }
                node["node_info"].update(info)
            return node
        elif node_type == "leaf":
            if node_info.get("strategy", "") == "MLAG":
                info = {
                    "peer_vlan_id": "3966", #默认3966 
                    # "mlag_vlan_id": node_info.get("mlag_vlanid", ""), 
                    "mlag_l3_interface_ip_address_pool": node_info.get("l3_interface", ""),
                    "mlag_l3_interface_ip_address": "", 
                    "mlag_peer_lag_interface_name": "ae48",  #默认ae48
                    "peer_ipv4_address": "",   # 对端ip
                    "strategy": "MLAG",
                    "domain_id": node_info.get("domain_id", ""), 
                }
                node["node_info"].update(info)
            node["leaf_name"] = node_info.get("name", "")
            node["strategy"] = node_info.get("strategy", "")
        
        if self.underlay_routing_protocol == "BGP":
            info = {
                "vtep_interface_pool": self.vtep_interface_pool,
                "vtep_interface": "",
                "router_id_pool": self.router_id_pool,
                "router_id": "",
                "asn_pool": self.underlay_ebgp_asn_pool,
                "asn": "",
                "underlay_routing_protocol": "BGP"
            }
        else:
            info = {
                "vtep_interface_pool": self.vtep_interface_pool,
                "vtep_interface": "",
                "router_id_pool": self.router_id_pool,
                "router_id": "",
                "area_id": self.areaid,
                "asn_pool": self.overlay_ibgp_asn_pool,
                "asn": "",
                "underlay_routing_protocol": "OSPF"
            }
            
        info["reserved_vlan"] = "3967-4094"
        info["hostname"] = logic_device.replace("_", "-")
        
        node["node_info"].update(info)
         
        # if self.overlay_control_protocol == "Static VXLAN":
        #     pass

        return node

    def _build_edges(self, sources, target, count, type="access_link"):
        edges = []
        for source in sources:
            link_info = []
            for i in range(1, count + 1):
                info = {
                    "logic_link": f"{source}<->{target}[{i}]".replace("_", "-"),
                    "source_port": "",
                    "target_port": "",
                    "link_status": "uncheck"
                }
                if type == "spine_link":
                    info["source_routed_address"] = ""
                    info["target_routed_address"] = ""
                link_info.append(info)
            edge = {
                "link_info": link_info,
                "source": source,
                "source_sn": "",
                "type": type,
                "target": target,
                "target_sn": ""
            }
            
            if type == "spine_link":
                edge["source_role"] = "spine"
                edge["target_role"] = "leaf"
            elif type == "peer_link":
                edge["source_role"] = "leaf"
                edge["target_role"] = "leaf"
            elif type == "access_link":
                edge["source_role"] = "leaf"
                edge["target_role"] = "access"
            edges.append(edge)
        return edges
    
        
# update 仅更新topologyde edges 节点不会改变
def update_topology_by_template(topology, template_info, type): 
    # 前端只修改node中的sn ，首先需要将node中的sn分配到edges
    nodes = topology.get("nodes", [])
    edges = topology.get("edges", [])
    sn_dict = {item["logic_device"]: item["switch_sn"] for item in nodes}
    print(sn_dict)
    
    for edge in edges:
       edge["source_sn"] =  sn_dict[edge["source"]]
       edge["target_sn"] =  sn_dict[edge["target"]]
    
    if type == "5-stage": 
        pods = template_info.get("pod", [])
        super_spine_count = template_info.get("super_spine_count", 1)
        for pod in pods:
            spine_count = pod.get("spine_count", 1)
            units = pod.get("unit", []) 
            pod_name = pod.get("name")
            update_unit_and_spine_edges(units, spine_count, edges, prefix=pod_name)

            link_super_spine_count = pod.get("link_superspine_count", 1)
            target_prefix = f"{pod_name}_spine"
            # print(link_super_spine_count, target_prefix)
            update_spine_edges(super_spine_count, link_super_spine_count, edges, target_prefix=target_prefix, spine_prefix="super_spine")
        topology["edges"] = edges
        
    elif type == "3-stage":
        spine_count = template_info.get("spine_count", 1)
        units = template_info.get("unit", [])
        update_unit_and_spine_edges(units, spine_count, edges)
        topology["edges"] = edges
            

def update_unit_and_spine_edges(units, spine_count, edges, prefix=None):
    for unit in units:
        count = unit.get('count', 1)
        for i in range(count):
            base_name = f"{prefix}_{unit.get('name')}" if prefix else unit.get('name')
            name_prefix = f"{base_name}_00{i+1}"
            update_edges_by_unit(unit, name_prefix, edges)
            spine_name_prefix = f"{prefix}_spine" if prefix else "spine"
            link_spine_count = unit.get("unit_info").get('link_spine_count', 1)
            update_spine_edges(spine_count, link_spine_count, edges, target_prefix=name_prefix, spine_prefix=spine_name_prefix)

    
def update_spine_edges(spine_count, link_spine_count, edges, target_prefix, spine_prefix="spine"): 
    for i in range(1, spine_count+1): 
        source=f"{spine_prefix}_{i}"
        for edge in edges:
            # 找到target前缀为指定值的target 用于区分不同unit
            if edge['source'] == source and edge['target'].startswith(target_prefix):
                target = edge['target']
                current_count = len(edge['link_info'])
                # print(source, target, link_spine_count, current_count)
                if link_spine_count > current_count:
                    # 添加新的连接
                    for i in range(current_count + 1, link_spine_count + 1):
                        edge['link_info'].append({
                            "logic_link": f"{source}<->{target}[{i}]".replace("_", "-"),
                            "source_port": "",
                            "target_port": ""
                        })
                elif link_spine_count < current_count:
                    # 只保留前 link_spine_count 个连接
                    edge['link_info'] = edge['link_info'][:link_spine_count]
            

def update_edges_by_unit(unit, name_prefix, edges):
    leaf_info = unit.get("unit_info").get("leaf", [])
    access_info = unit.get("unit_info").get("access", [])
    for leaf in leaf_info:
        if leaf["strategy"] == "MLAG":
            # 更新mlag peer link count
            source=f"{name_prefix}_{leaf['name']}_1"
            target=f"{name_prefix}_{leaf['name']}_2"
            count = leaf.get("mlag_peer_count", 2)
            update_edges_link_count(source, target, count, edges)
    
    for access in access_info:
        # 更新access link count
        leaf = access.get("leaf")
        access_name = access.get("name")
        access_method = access.get("access_method", "")
        physic_link_count = access.get("physic_link_count", 1)
        logic_device = f"{name_prefix}_{access_name}"
        
        if access_method == "dual":
            sources = [
                f"{name_prefix}_{leaf}_1",
                f"{name_prefix}_{leaf}_2"
            ]
        elif access_method == "single":
            peer_leaf = access.get("peer_leaf")
            source = f"{name_prefix}_{leaf}_1" if peer_leaf == "first" else f"{name_prefix}_{leaf}_2"
            sources = [source]
        else:
            sources = [f"{name_prefix}_{leaf}_1"]

        for source in sources:
            update_edges_link_count(source, logic_device, physic_link_count, edges)


# 用于更新连接数 
def update_edges_link_count(source, target, count, edges):
    for edge in edges:
        if edge['source'] == source and edge['target'] == target:
            current_count = len(edge['link_info'])
            if count > current_count:
                # 添加新的连接
                for i in range(current_count + 1, count + 1):
                    edge['link_info'].append({
                        "logic_link": f"{source}<->{target}[{i}]".replace("_", "-"),
                        "source_port": "",
                        "target_port": "",
                        "link_status": "uncheck"
                    })
            elif count < current_count:
                # 只保留前 physic_link_count 个连接
                edge['link_info'] = edge['link_info'][:count]
            break
        

def allocate_asn(asn_pool_id, asn_num, session=None):
    if not resource_pool_asn_db.query_resource_pool_by_id(asn_pool_id, session).first():
        LOG.error('Resource pool not found')
        return []
    else:
        asn_list = resource_pool_asn_db.generate_resource_from_pool(asn_pool_id, asn_num, session)
        if not asn_list:
            LOG.error('generate resource failed')
            return []
        else:
            return asn_list
        
def allocate_ip(ip_pool_id, ip_num, session=None):
    if not resource_pool_ip_db.query_resource_pool_by_id(ip_pool_id, session).first():
        LOG.error('Resource pool not found')
        return []
    else:
        ip_list = resource_pool_ip_db.generate_resource_from_pool(ip_pool_id, ip_num, session)
        if not ip_list:
            LOG.error('generate resource failed')
            return []
        else:
            return ip_list
        
def allocate_ip_pair(ip_pool_id, link_num, session=None):
    if not resource_pool_ip_db.query_resource_pool_by_id(ip_pool_id, session).first():
        LOG.error('Resource pool not found')
        return []
    res = []
    for _ in range(link_num):
        ips = resource_pool_ip_db.generate_resource_from_pool(ip_pool_id, 2, session)

        # 检查两个ip 网络地址是否相同 
        ip0_with_prefix = ips[0] + "/31"
        ip1_with_prefix = ips[1] + "/31"
        ip0_network_address = str(ipaddress.IPv4Interface(ip0_with_prefix).network.network_address)
        ip1_network_address = str(ipaddress.IPv4Interface(ip1_with_prefix).network.network_address)
        if ip0_network_address != ip1_network_address:
            resource_pool_ip_db.delete_record_by_value(ip_pool_id, [int(ipaddress.IPv4Address(ips[0]))])
            new_ip = resource_pool_ip_db.generate_resource_from_pool_by_value(ip_pool_id, int(ipaddress.IPv4Address(ips[1])) + 1 )[0]
            res.append([ip1_with_prefix, new_ip+ "/31"])
        else:
            res.append([ip0_with_prefix, ip1_with_prefix]) 
    if res:
        return res
    else:
        LOG.error('generate resource failed')
        return []
    
def delete_ip_values(ip_pool_id, ip_list, session=None):
    resource_pool_ip_db.delete_record_by_value(ip_pool_id, ip_list, session)
    
def get_fabric_topo_pool_record(topology):
    spine_link_list = []
    asn_set = set()
    router_id_set = set()
    vtep_interface_set = set()
    peer_link_dict = {}
    for node in topology["nodes"]:
        node_info = node["node_info"]

        if (asn := node_info.get("asn")):
            asn_set.add(asn)

        if (router_id := node_info.get("router_id")):
            router_id_set.add(int(ipaddress.IPv4Address(router_id)))

        if (vtep_interface := node_info.get("vtep_interface")):
            vtep_interface_set.add(int(ipaddress.IPv4Address(vtep_interface)))

        if (peer_link_ip := node_info.get("mlag_l3_interface_ip_address")):
            peer_link_pool = node_info.get("mlag_l3_interface_ip_address_pool")
            peer_link_dict.setdefault(peer_link_pool, []).append(int(ipaddress.IPv4Address(peer_link_ip.split('/')[0])))

    for edge in topology["edges"]:
        if edge["type"] == "spine_link":
            for link_info in edge["link_info"]:
                for key in ["source_routed_address", "target_routed_address"]:
                    if (address := link_info.get(key)):
                        spine_link_list.append(int(ipaddress.IPv4Address(address.split('/')[0])))

    
    return list(asn_set), list(router_id_set), list(vtep_interface_set), peer_link_dict, spine_link_list


def release_pool_record(fabric_config, session):
    try:
        underlay_routing_protocol = fabric_config["underlay_routing_protocol"]
        
        asn_list, router_id_list, vtep_interface_list, peer_link_dict, spine_link_list = get_fabric_topo_pool_record(fabric_config["topology"])
        if asn_list:
            if underlay_routing_protocol == "BGP":
                asn_pool_id = fabric_config.get('underlay_ebgp_asn')
            elif underlay_routing_protocol == "OSPF":
                asn_pool_id = fabric_config.get('overlay_ibgp_asn')
            print(asn_pool_id, asn_list)
            resource_pool_asn_db.delete_record_by_value(asn_pool_id, asn_list, session)

        if router_id_list:
            router_id_pool = fabric_config.get('bgp_router_id')
            print(router_id_pool, router_id_list)
            resource_pool_ip_db.delete_record_by_value(router_id_pool, router_id_list, session)
            
        if vtep_interface_list:
            vtep_interface_pool = fabric_config.get('vtep_interface')
            print(vtep_interface_pool, vtep_interface_list)
            resource_pool_ip_db.delete_record_by_value(vtep_interface_pool, vtep_interface_list, session)
            
        if spine_link_list:
            spine_link_pool = fabric_config.get("routed_interface_address_pool")
            print(spine_link_pool, spine_link_list)
            resource_pool_ip_db.delete_record_by_value(spine_link_pool, spine_link_list, session)
            
        for peer_link_pool, peer_link in peer_link_dict.items():
            print(peer_link_pool, peer_link)
            resource_pool_ip_db.delete_record_by_value(peer_link_pool, peer_link, session)
    except Exception as e:
        LOG.error(str(e))
        LOG.error("release record failed")
        
        
def get_switch_port_info(logic_device_id):

    session = dc_fabric_db.get_session()
    node = dc_fabric_db.get_fabric_topology_node_info(logic_device_id)
    
    used_ports = []
    for _, links in node.node_info["links"].items():
        for link in links:
            # 检查 source_sn 和 target_sn 是否与输入相同
            if link['source_sn'] == node.switch_sn:
                used_ports.append(link['source_port'])
            if link['target_sn'] == node.switch_sn:
                used_ports.append(link['target_port'])
                
    linkport = session.query(DCVirtualResourceHostLinkPort).filter(DCVirtualResourceHostLinkPort.logic_device_id == logic_device_id).all()
    for port_info in linkport:
        used_ports.extend([port.strip() for port in port_info.port_name.split(',')])
        
    pg_info_list = session.query(SwitchPortgroupInfo).filter(SwitchPortgroupInfo.logic_device_id == logic_device_id).all()
    for pg_info in pg_info_list:
        sw_port_info= pg_info.port_info.get("sw_port_info", {})
        for port_info in sw_port_info.values(): 
            used_ports.extend(port_info.get("port_list", []))
        
    
    switch = session.query(inventory.Switch).filter(inventory.Switch.sn == node.switch_sn).first()
    
    model_port_info = session.query(inventory.ModelPhysicPort).filter(inventory.ModelPhysicPort.platform_name == switch.platform_model).all()
       
    all_port = {
        'ge': [],
        'te': [],
        'qe': [],
        'xe': []
    } 
    for model_port in model_port_info:
        if model_port.port_type in all_port.keys():
            all_port[model_port.port_type].append(model_port.port_name)
        
    return all_port, switch.platform_model, [port for port in used_ports if port]
    

def get_switch_lag_id(logic_device_id_list, count):

    session = dc_fabric_db.get_session()
    used_list = []
    for logic_device_id in logic_device_id_list:
        pg_info_list = session.query(SwitchPortgroupInfo).filter(SwitchPortgroupInfo.logic_device_id == logic_device_id).all()
        used_id =[]
        for pg_info in pg_info_list:
            sw_port_info= pg_info.port_info.get("sw_port_info", {})
            for port_info in sw_port_info.values(): 
                used_id.append(port_info.get("lag_id", 0))
        # 云平台已使用的lag id 
        linkport_list = session.query(DCVirtualResourceHostLinkPort).filter(DCVirtualResourceHostLinkPort.logic_device_id == logic_device_id).all()
        for port in linkport_list:
            used_id.append(port.lag_id)
        
        used_list.append(used_id)
        
    print(used_list)
    used_numbers = {num for sublist in used_list for num in sublist}
    available_numbers = [num for num in range(1, 48) if num not in used_numbers]
    result = available_numbers[:count]
    
    return result


def get_baremetal_switch_portgroup_config(node_group, op=""):
    
    def _build_config_dict(device_config, node_group):
        config= {
            "meta": {
                "device_config_id": device_config.id,
                "fabric_id": node_group.id  ## 这里不是fabric_id 因为配置下发task表中使用fabric_id字段所以key为fabric_id 此处把fabric_id当做一个trace_id
            },
            "old_val": device_config.config if device_config.config else {},
            "new_val": {
                "access_port_list": [],
                "trunk_port_list": [],
                "lag_list": []
            }
        }
        
        return config
    
    config_dict={}
    for switch_pg in node_group.switch_portgroup:
        for pg_info in switch_pg.switch_portgroup_info:
            device_config = dc_virtual_resource_db.update_switch_portgroup_config(logic_device_id=pg_info.logic_device_id, switch_sn=pg_info.switch_sn,
                                                                                  related_id=node_group.id, related_type="BareMetal")
            
            if pg_info.switch_sn not in config_dict:
                config_dict[pg_info.switch_sn] = _build_config_dict(device_config, node_group)
                
            if pg_info.status not in ['Deleting', 'Delete Failed'] and op != "delete":

                if switch_pg.link_count == 1 and switch_pg.link_type.lower() == "single leaf":
                    all_ports = []
                    for item in pg_info.port_info['sw_port_info'].values():
                        all_ports.extend(item['port_list'])
                    if switch_pg.connect_mode == "access":
                        config_dict[pg_info.switch_sn]["new_val"]["access_port_list"].extend(all_ports)
                    else:
                        config_dict[pg_info.switch_sn]["new_val"]["trunk_port_list"].extend(all_ports)
                elif switch_pg.link_count > 1 and switch_pg.link_type.lower() == "single leaf":
                    for item in pg_info.port_info['sw_port_info'].values():
                        lag_id = item['lag_id']
                        lag_info = {
                            "lag_name": f"ae{lag_id}",
                            "lag_port_list": item['port_list'],
                            "port_mode": switch_pg.connect_mode,
                        }
                        config_dict[pg_info.switch_sn]["new_val"]["lag_list"].append(lag_info)
                else:
                    logic_device = dc_fabric_db.get_fabric_topology_node_info(pg_info.logic_device_id)
                    domain_id = logic_device.node_info["domain_id"]
                    
                    for item in pg_info.port_info['sw_port_info'].values():
                        lag_id = item['lag_id']
                        lag_info = {
                            "lag_name": f"ae{lag_id}",
                            "lag_port_list": item['port_list'],
                            "port_mode": switch_pg.connect_mode,
                            "link_id": lag_id,
                            "domain_id": domain_id,
                        }
                        
                        config_dict[pg_info.switch_sn]["new_val"]["lag_list"].append(lag_info)      
                                         
        # 单归的情况下需要把ae口配置同步到另一台设备                                 
        if switch_pg.link_type == "MLAG Leaf" and len(switch_pg.switch_portgroup_info) == 1:
            pg_info = switch_pg.switch_portgroup_info[0]
            nodes = dc_fabric_db.get_fabric_topology_node_by_vd(switch_pg.vlan_domain_id)
            another_node = [node for node in nodes if node.id != pg_info.logic_device_id][0]
            
            device_config = dc_virtual_resource_db.update_switch_portgroup_config(logic_device_id=another_node.id, switch_sn=another_node.switch_sn,
                                                                                  related_id=node_group.id, related_type="BareMetal")
            if another_node.switch_sn not in config_dict:
                config_dict[another_node.switch_sn] = _build_config_dict(device_config, node_group)
                
            if pg_info.status not in ['Deleting', 'Delete Failed'] and op != "delete":
                logic_device = dc_fabric_db.get_fabric_topology_node_info(pg_info.logic_device_id)
                domain_id = logic_device.node_info["domain_id"]
                
                for item in pg_info.port_info['sw_port_info'].values():
                    lag_id = item['lag_id']
                    lag_info = {
                        "lag_name": f"ae{lag_id}",
                        "lag_port_list": [],
                        "port_mode": switch_pg.connect_mode,
                        "link_id": lag_id,
                        "domain_id": domain_id,
                    }
                    
                    config_dict[another_node.switch_sn]["new_val"]["lag_list"].append(lag_info)
            
    return config_dict


def get_cloudplatform_link_port_config(host, op=""):
    
    def _build_config_dict(device_config, host):
        config = {
            "meta": {
                "device_config_id": device_config.id,
                "fabric_id": host.id
            },
            "old_val": device_config.config if device_config.config else {},
            "new_val": {
                "access_port_list": [],
                "trunk_port_list": [],
                "lag_list": []
            }
        }
        
        return config
    
    config_dict={}
    host_links = dc_virtual_resource_db.get_virtual_resource_host_link(host.id)
    for link in host_links:
        link_ports = dc_virtual_resource_db.get_virtual_resource_host_link_port(link.id)
        for link_port in link_ports:
            device_config = dc_virtual_resource_db.update_switch_portgroup_config(logic_device_id=link_port.logic_device_id, switch_sn=link_port.switch_sn,
                                                                                  related_id=host.id, related_type="CloudPlatform")
            
            if link_port.switch_sn not in config_dict:
                config_dict[link_port.switch_sn] = _build_config_dict(device_config, host)
            
            port_list = [port.strip() for port in link_port.port_name.split(',')]
            if link_port.status not in ['Deleting', 'Delete Failed'] and op != "delete":
                   
                if link.link_count in ["1", "single"] and link.link_type.lower() == "single leaf":
                    if link.connect_mode == "access":
                        config_dict[link_port.switch_sn]["new_val"]["access_port_list"] = port_list
                    else:
                        config_dict[link_port.switch_sn]["new_val"]["trunk_port_list"] = port_list
                elif link.link_count not in ["1", "single"] and link.link_type.lower() == "single leaf":
                    lag_info = {
                        "lag_name": f"ae{link_port.lag_id}",
                        "lag_port_list": port_list,
                        "port_mode": link.connect_mode,
                    }
                    config_dict[link_port.switch_sn]["new_val"]["lag_list"].append(lag_info)
                else:
                    logic_device = dc_fabric_db.get_fabric_topology_node_info(link_port.logic_device_id)
                    domain_id = logic_device.node_info["domain_id"]
                    lag_info = {
                        "lag_name": f"ae{link_port.lag_id}",
                        "lag_port_list": port_list,
                        "port_mode": link.connect_mode,
                        "link_id": link_port.lag_id,
                        "domain_id": domain_id
                    }
                    config_dict[link_port.switch_sn]["new_val"]["lag_list"].append(lag_info)
                

         # 单归的情况下需要把ae口配置同步到另一台设备                                 
        if link.link_type.lower() == "mlag leaf" and len(link.link_ports) == 1:
            link_port = link_ports[0]
            nodes = dc_fabric_db.get_fabric_topology_node_by_vd(link.vlan_domain_id)
            another_node = [node for node in nodes if node.id != link_port.logic_device_id][0]
            
            device_config = dc_virtual_resource_db.update_switch_portgroup_config(logic_device_id=another_node.id, switch_sn=another_node.switch_sn,
                                                                                  related_id=host.id, related_type="CloudPlatform")
            
            if another_node.switch_sn not in config_dict:
                config_dict[another_node.switch_sn] = _build_config_dict(device_config, host)

                
            if link_port.status not in ['Deleting', 'Delete Failed'] and op != "delete":
                logic_device = dc_fabric_db.get_fabric_topology_node_info(link_port.logic_device_id)
                domain_id = logic_device.node_info["domain_id"]
                lag_info = {
                    "lag_name": f"ae{link_port.lag_id}",
                    "lag_port_list": [],
                    "port_mode": link.connect_mode,
                    "link_id": link_port.lag_id,
                    "domain_id": domain_id
                }
                config_dict[another_node.switch_sn]["new_val"]["lag_list"].append(lag_info)
        
    return config_dict           

# 更新删除状态
def baremetal_switch_portgroup_callback(status, **kwargs):
    node_group_id = kwargs.get("node_group_id") 
    print("node_group_id", node_group_id)   
    session = dc_fabric_db.get_session()
    node_group = dc_virtual_resource_db.get_node_group_by_id(node_group_id)
    del_spg = []
    del_port = []
    if status == "Deployed":
        if node_group.status == "Deleting":
            # 如果status为成功 且 nodegroup需要删除 则直接删除node group即可
            print("delete node group")
            dc_virtual_resource_db.delete_node_group(node_group.id)
            session.query(SwitchPortgroupConfig).filter(SwitchPortgroupConfig.related_id == node_group_id, SwitchPortgroupConfig.related_type == "BareMetal").delete()
            return
        else:
            # 如果node group 不需要删除 需要逐层查看是否有元素需要删除
            for switch_pg in node_group.switch_portgroup:
                if switch_pg.status == "Deleting":
                    del_spg.append(switch_pg.id)
                else:
                    for pg_info in switch_pg.switch_portgroup_info:
                        if pg_info.status == "Deleting":
                            del_port.append(pg_info.id)
                            
            
    elif status == "Deploy Failed":
        ## 获取task那边的状态 用来判断任务是否执行成功
        subquery = session.query(
            inventory.ConfigDistributionTaskForDC,
            func.row_number().over(
                partition_by=[inventory.ConfigDistributionTaskForDC.sn, 
                              inventory.ConfigDistributionTaskForDC.type,
                              inventory.ConfigDistributionTaskForDC.fabric_id],
                order_by=inventory.ConfigDistributionTaskForDC.create_time.desc()
            ).label('row_num')
        ) \
        .filter(inventory.ConfigDistributionTaskForDC.fabric_id == node_group_id,
                inventory.ConfigDistributionTaskForDC.type == "uplink_bm").subquery()
        
        tasks = session.query(subquery).filter(subquery.c.row_num == 1).all()
        
        success_device_sn = [task.sn for task in tasks if task.task_status == 2]
        failed_device_sn = [task.sn for task in tasks if task.task_status == 3]
        print("success device: ", success_device_sn)
        print("failed device: ", failed_device_sn)
        
        ## 更新config状态
        success_device_id = []
        for sn in success_device_sn:
            device_config = dc_virtual_resource_db.update_switch_portgroup_config(switch_sn=sn,related_id=node_group.id, related_type="BareMetal", status="Deployed")
            success_device_id.append(device_config.logic_device_id)
            
        failed_device_id = []
        for sn in failed_device_sn:
            if node_group.status == "Deleting":
                device_config = dc_virtual_resource_db.update_switch_portgroup_config(switch_sn=sn,related_id=node_group.id, related_type="BareMetal", status="Delete Failed")
            else:
                device_config = dc_virtual_resource_db.update_switch_portgroup_config(switch_sn=sn,related_id=node_group.id, related_type="BareMetal", status="Deploy Failed")
            failed_device_id.append(device_config.logic_device_id)
            
        print("success device: ", success_device_id)
        print("failed device: ", failed_device_id)
        
        for switch_pg in node_group.switch_portgroup:
            spg_allow_delete = True  # 用于标记spg 是否允许被删除 
            for pg_info in switch_pg.switch_portgroup_info:
                if pg_info.status == "Deleting":
                    # port删除成功则清除记录 不成功则更新状态
                    if pg_info.logic_device_id in success_device_id:
                        del_port.append(pg_info.id)
                    elif pg_info.logic_device_id in failed_device_id:
                        dc_virtual_resource_db.update_switch_portgroup_info(info_id=pg_info.id, status="Delete Failed")
                        spg_allow_delete = False # 如果port删除失败spg不允许删除
                else:
                    # 如果存在port没有删除则更新spg不允许删除
                    spg_allow_delete = False
                  
            # spg需要删除 且允许删除时 清除记录  不允许删除时更新记录 
            if switch_pg.status == "Deleting":
                if spg_allow_delete:            
                    del_spg.append(switch_pg.id)  
                else:  
                    dc_virtual_resource_db.update_switch_portgroup(portgroup_id=switch_pg.id, status="Delete Failed")
        
    # 删除的记录 
    print("del_spg: ", del_spg, "del_port: ", del_port)
    dc_virtual_resource_db.del_switch_portgroup_list(del_spg)
    dc_virtual_resource_db.del_switch_portgroup_info_list(del_port)
    
    ## 更新剩余所有pg状态
    port_group_list = dc_virtual_resource_db.get_switch_portgroup_by_nodegroup(node_group_id)

    for port_group in port_group_list:
        ## 只更新状态不为 Delete的pg状态
        if port_group.status not in ["Deleting", "Delete Failed"]:
            port_group_status = "Deployed"
            for pg_info in port_group.switch_portgroup_info:
                pg_config = session.query(SwitchPortgroupConfig).filter(SwitchPortgroupConfig.related_id == node_group_id, 
                                                                        SwitchPortgroupConfig.related_type == "BareMetal",
                                                                        SwitchPortgroupConfig.logic_device_id == pg_info.logic_device_id).first()
                if pg_info.status not in ["Deleting", "Delete Failed"]:
                    dc_virtual_resource_db.update_switch_portgroup_info(info_id=pg_info.id, status=pg_config.status)
                    if port_group_status != pg_config.status:
                        port_group_status = pg_config.status
            dc_virtual_resource_db.update_switch_portgroup(portgroup_id=port_group.id, status=port_group_status)
            
    if node_group.status == "Deleting" and status == "Deploy Failed":
        dc_virtual_resource_db.update_node_group(group_id=node_group_id, status="Delete Failed")        
    else:
        dc_virtual_resource_db.update_node_group(group_id=node_group_id, status=status)


def cloudplatform_switch_portgroup_callback(status, **kwargs):
    host_id = kwargs.get("host_id") 
    session = dc_fabric_db.get_session()
    del_link = []
    del_port = []
    host = dc_virtual_resource_db.get_virtual_resource_host_by_id(host_id)
    if status == "Deployed":
        if host.status == "Deleting":
            print("delete host")
            dc_virtual_resource_db.del_virtual_resource_host_by_id(host.id)
            session.query(SwitchPortgroupConfig).filter(SwitchPortgroupConfig.related_id == host_id, SwitchPortgroupConfig.related_type == "CloudPlatform").delete()
            return
        else:
            for link in host.host_link:
                if link.status == "Deleting":
                    del_link.append(link.id)
                else:
                    for port in link.link_ports:
                        if port.status == "Deleting":
                            del_port.append(port.id)

    elif status == "Deploy Failed":                    
        ## 获取task那边的状态 用来判断任务是否执行成功
        subquery = session.query(
            inventory.ConfigDistributionTaskForDC,
            func.row_number().over(
                partition_by=[inventory.ConfigDistributionTaskForDC.sn, 
                              inventory.ConfigDistributionTaskForDC.type,
                              inventory.ConfigDistributionTaskForDC.fabric_id],
                order_by=inventory.ConfigDistributionTaskForDC.create_time.desc()
            ).label('row_num')
        ) \
        .filter(inventory.ConfigDistributionTaskForDC.fabric_id == host_id,
                inventory.ConfigDistributionTaskForDC.type == "uplink_cloud").subquery()
        
        tasks = session.query(subquery).filter(subquery.c.row_num == 1).all()
        
        success_device_sn = [task.sn for task in tasks if task.task_status == 2]
        failed_device_sn = [task.sn for task in tasks if task.task_status == 3]
        print("success device: ", success_device_sn)
        print("failed device: ", failed_device_sn)
        
        ## 更新config状态
        success_device_id = []
        for sn in success_device_sn:
            device_config = dc_virtual_resource_db.update_switch_portgroup_config(switch_sn=sn,related_id=host_id, related_type="CloudPlatform", status="Deployed")
            success_device_id.append(device_config.logic_device_id)
            
        failed_device_id = []
        for sn in failed_device_sn:
            if host.status == "Deleting":
                device_config = dc_virtual_resource_db.update_switch_portgroup_config(switch_sn=sn,related_id=host_id, related_type="CloudPlatform", status="Delete Failed")
            else:
                device_config = dc_virtual_resource_db.update_switch_portgroup_config(switch_sn=sn,related_id=host_id, related_type="CloudPlatform", status="Deploy Failed")
            failed_device_id.append(device_config.logic_device_id)
            
        print("success device: ", success_device_id)
        print("failed device: ", failed_device_id)
        
        for link in host.host_link:
            link_allow_delete = True  # 用于标记spg 是否允许被删除  
            for port in link.link_ports:
                if port.status == "Deleting":
                    # port删除成功则清除记录 不成功则更新状态
                    if port.logic_device_id in success_device_id:
                        del_port.append(port.id)
                    elif port.logic_device_id in failed_device_id:
                        dc_virtual_resource_db.update_virtual_resource_host_link_port(port_id=port.id, status="Delete Failed")
                        link_allow_delete = False # 如果port删除失败spg不允许删除
                else:
                    # 如果存在port没有删除则更新spg不允许删除
                    link_allow_delete = False
                  
            # spg需要删除 且允许删除时 清除记录  不允许删除时更新记录 
            if link.status == "Deleting":
                if link_allow_delete:            
                    del_link.append(link.id)  
                else:  
                    dc_virtual_resource_db.update_virtual_resource_host_link(link_id=link.id, status="Delete Failed")
        
    # 删除的记录 
    print("del_link: ", del_link, "del_port: ", del_port)
    dc_virtual_resource_db.del_virtual_resource_host_link_list(del_link)
    dc_virtual_resource_db.del_virtual_resource_host_link_port_list(del_port)
    
    ## 更新剩余所有pg状态
    link_list = dc_virtual_resource_db.get_virtual_resource_host_link(host_id)

    for link in link_list:
        ## 只更新状态不为 Delete的pg状态
        if link.status not in ["Deleting", "Delete Failed"]:
            link_status = "Deployed"
            for port_info in link.link_ports:
                pg_config = session.query(SwitchPortgroupConfig).filter(SwitchPortgroupConfig.related_id == host_id, 
                                                                        SwitchPortgroupConfig.related_type == "CloudPlatform",
                                                                        SwitchPortgroupConfig.logic_device_id == port_info.logic_device_id).first()
                if port_info.status not in ["Deleting", "Delete Failed"]:
                    dc_virtual_resource_db.update_virtual_resource_host_link_port(port_id=port_info.id, status=pg_config.status)
                    if link_status != pg_config.status:
                        link_status = pg_config.status
            dc_virtual_resource_db.update_virtual_resource_host_link(link_id=link.id, status=link_status)
            
    if host.status == "Deleting" and status == "Deploy Failed":
        dc_virtual_resource_db.update_virtual_resource_host(host_id=host_id, status="Delete Failed")        
    else:
        dc_virtual_resource_db.update_virtual_resource_host(host_id=host_id, status=status)
 
def check_uplink_task_status():
    
    session = dc_fabric_db.get_session()
    
    STATUS_MAPPING = {
        "Deploying": "Deploy Failed",
        "Deleting": "Delete Failed"
    }
    TIMEOUT = timedelta(seconds=60*10)
    
    current_time = datetime.now()
    
    node_groups = session.query(NodeGroup).all()
    for node_group in node_groups:
        for switch_pg in node_group.switch_portgroup: 
            for pg_info in switch_pg.switch_portgroup_info:
                if pg_info.status in STATUS_MAPPING and current_time - pg_info.modified_time > TIMEOUT:
                    dc_virtual_resource_db.update_switch_portgroup_info(
                        info_id=pg_info.id, 
                        status=STATUS_MAPPING[pg_info.status]
                    )
            
            if switch_pg.status in STATUS_MAPPING and current_time - switch_pg.modified_time > TIMEOUT:
                dc_virtual_resource_db.update_switch_portgroup(
                    portgroup_id=switch_pg.id, 
                    status=STATUS_MAPPING[switch_pg.status]
                )
        
        if node_group.status in STATUS_MAPPING and current_time - node_group.modified_time > TIMEOUT:
            dc_virtual_resource_db.update_node_group(
                group_id=node_group.id, 
                status=STATUS_MAPPING[node_group.status]
            )

    hosts = session.query(DCVirtualResourceHost).all() 
    for host in hosts:
        for host_link in host.host_link: 
            for port_info in host_link.link_ports:
                if port_info.status in STATUS_MAPPING and current_time - port_info.modified_time > TIMEOUT:
                    dc_virtual_resource_db.update_virtual_resource_host_link_port(
                        port_id=port_info.id, 
                        status=STATUS_MAPPING[port_info.status]
                    )
                    
            if host_link.status in STATUS_MAPPING and current_time - host_link.modified_time > TIMEOUT:
                dc_virtual_resource_db.update_virtual_resource_host_link(
                    link_id=host_link.id,
                    status=STATUS_MAPPING[host_link.status]
                )
        
        if host.status in STATUS_MAPPING and current_time - host.modified_time > TIMEOUT:
            dc_virtual_resource_db.update_virtual_resource_host(
                host_id=host.id, 
                status=STATUS_MAPPING[host.status]
            )
       
            
def allocate_bd_vlan_by_bm_network(network, ls_id):
    
    db_session = dc_fabric_db.get_session()
    
    nicpg_list = db_session.query(NodeNicPortgroup).filter(NodeNicPortgroup.network_id == network.id).all()
        
    vd_list = []
    for nicpg in nicpg_list:
        if nicpg.switch_portgroup.vlan_domain_id not in vd_list:
            vd_list.append(nicpg.switch_portgroup.vlan_domain_id)
            
    # 云平台处理 根据mapping表找到对应network的link  
    link_mapping_list = db_session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.network_id == network.id).all()
    for mapping in link_mapping_list:
        if mapping.link.vlan_domain_id not in vd_list:
            vd_list.append(mapping.link.vlan_domain_id)
                
    print(vd_list)
    if not vd_list:
        return {}, f"Virtual network:{network.network_name} cannot allocate VLAN, please check network access or node addition"
    generated_res = {}
    generated_falied = False
    ## 可能有多个vd 每个vd都要占用这个vlan 如果有一个vd占用失败 就要全部回退
    for vd_id in vd_list:
        res = resource_pool_vlandomain_db.generate_bdvlan_from_pool_by_value(vd_id, network.vlan_id, ls_id)
        if not res:
            generated_falied = True
        else:
            generated_res[vd_id] = res

    if generated_falied:
        for vd_id, values in generated_res.items():
            resource_pool_vlandomain_db.delete_bdvlan_from_pool_by_value(vd_id, values)
        return {}, f"Cannot allocate VLAN, VLAN ID:{network.vlan_id}"
    return generated_res, ""
     
def get_intersection(ranges1, ranges2):
    """
    计算两个范围列表的交集
    """
    result = []
    i = j = 0
    
    while i < len(ranges1) and j < len(ranges2):
        # 当前比较的两个范围
        start1, end1 = ranges1[i][0], ranges1[i][1]
        start2, end2 = ranges2[j][0], ranges2[j][1]
        
        # 计算交集
        start = max(start1, start2)
        end = min(end1, end2)
        
        if start <= end:
            result.append([start, end])
        
        # 移动指针
        if end1 < end2:
            i += 1
        else:
            j += 1
    
    return result

def find_first_available_optimized(common_ranges, used_numbers):
    """
    按范围顺序查找可用值
    """
    used_set = set(used_numbers)
    
    # 按范围起始值排序
    sorted_ranges = sorted(common_ranges, key=lambda x: x[0])
    
    for start, end in sorted_ranges:
        # 检查范围内的每个数字
        for num in range(start, end + 1):
            if num not in used_set:
                return num
    
    return None 

def check_anycast_subnet_conflict(anycast_ip):
    db_session = dc_fabric_db.get_session()
    
    li_list = db_session.query(DCLogicalInterface).all() 
    try:
        anycast_ip_net = ipaddress.ip_network(anycast_ip, strict=False)
        return any(
            anycast_ip_net.overlaps(ipaddress.ip_network(li.anycast_ipv4, strict=False))
            for li in li_list
        )
    except ValueError as e:
        return True 

def allocate_vrf_vlan_by_bm_network(network, lr_id):
    
    db_session = dc_fabric_db.get_session()
    
    vd_list = []
    nicpg_list = db_session.query(NodeNicPortgroup).filter(NodeNicPortgroup.network_id == network.id).all()
    for nicpg in nicpg_list:
        if nicpg.switch_portgroup.vlan_domain_id not in vd_list:
            vd_list.append(nicpg.switch_portgroup.vlan_domain_id)
         
    # 云平台处理 根据mapping表找到对应network的link  
    link_mapping_list = db_session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.network_id == network.id).all()
    for mapping in link_mapping_list:
        if mapping.link.vlan_domain_id not in vd_list:
            vd_list.append(mapping.link.vlan_domain_id)
    
    merged_range_list = []  
    used_list = []    
    for vd_id in vd_list:
        all_ranges_list, all_used_num_list = resource_pool_vlandomain_db.query_vrf_vlan_range_info(vd_id)
        print(all_ranges_list, all_used_num_list)
        ranges = [[r[0], r[1]] for r in all_ranges_list]
        print(ranges)
        if not merged_range_list:
            merged_range_list = ranges
        else:
            merged_range_list = get_intersection(merged_range_list, ranges)
        
        for used in all_used_num_list:
            used_list.extend(used)
            
    print(merged_range_list, used_list)

    available_nums = find_first_available_optimized(merged_range_list, used_list)
    print(available_nums)
    
    generated_res = {}
    generated_falied = False
    ## 可能有多个vd 每个vd都要占用这个vlan 如果有一个vd占用失败 就要全部回退
    for vd_id in vd_list:
        res = resource_pool_vlandomain_db.generate_vrfvlan_from_pool_by_value(vd_id, available_nums, lr_id)
        if not res:
            generated_falied = True
        else:
            generated_res[vd_id] = res
            
    if generated_falied:
        for vd_id, values in generated_res.items():
            resource_pool_vlandomain_db.delete_vrfvlan_from_pool_by_value(vd_id, values)
        return {}, 0
    return generated_res, available_nums


def allocate_virtual_ip_range(fabric_id, gateway):
    session = dc_fabric_db.get_session()
    
    topo = session.query(DCFabricTopology).filter(DCFabricTopology.fabric_id == fabric_id).first()
    nodes = session.query(DCFabricTopologyNode).filter(DCFabricTopologyNode.fabric_topo_id == topo.id, DCFabricTopologyNode.type == "leaf").all()
                
    gateway_net = ipaddress.IPv4Interface(gateway)
    network = gateway_net.network
        
    last_ip = network.broadcast_address - 1  # 最后一个可用 IP
    
    # 计算起始 IP（确保不越界）
    start_ip = max(last_ip - len(nodes), network.network_address) 
    
    # 生成 IP 列表，并排除网关 IP
    allocated_ips = []
    current_ip = last_ip
    while len(allocated_ips) < len(nodes) and current_ip >= start_ip:
        if current_ip != gateway_net.ip:
            allocated_ips.append(ipaddress.IPv4Address(current_ip))
        current_ip -= 1
    
    if len(allocated_ips) < len(nodes):  
        return False, "The number of IPs required exceeds the available range"
    
    # 转换为带网络前缀的字符串
    prefix_len = gateway_net.network.prefixlen
    ip_list =  [f"{ip}/{prefix_len}" for ip in sorted(allocated_ips)]
    return True, ", ".join(ip_list)         


def mac_to_int(mac):
    """MAC地址转整数"""
    return int(mac.replace(':', ''), 16)

def int_to_mac(num):
    """整数转MAC地址"""
    hex_str = f"{num:012x}"
    return ":".join(hex_str[i:i+2] for i in range(0, 12, 2))

def allocate_macs(start_mac, end_mac, used_macs, count=1):
    """分配一个或多个新MAC地址"""
    # print(start_mac, end_mac, used_macs)
    start = mac_to_int(start_mac)
    end = mac_to_int(end_mac)
    used = {mac_to_int(mac) for mac in used_macs}
    
    allocated = []
    for candidate in range(start, end + 1):
        if candidate not in used:
            allocated.append(int_to_mac(candidate))
            if len(allocated) == count:
                return allocated if count > 1 else allocated[0]
    
    raise ValueError("Not enough available MAC addresse")

def allocate_virtual_ip(ip_range_str, used_ip_list):
    ip_candidates = [ip.strip() for ip in ip_range_str.split(",")]
    
    unused_ips = [ip for ip in ip_candidates if ip not in used_ip_list]
    
    if not unused_ips:
        raise ValueError("No available IPs left in the pool")
    return unused_ips[0]

def get_virtual_network_connect_info(vn, query_cloud_in_use=True):
    session = dc_fabric_db.get_session()
    
    # 获取连接到vn的vl2中的设备
    network = session.query(DCVirtualResourceNetwork).filter(DCVirtualResourceNetwork.virtual_network_id == vn.id).first()
    
    spg_dict = {}

    if not network:
        return spg_dict

    ## 根据节点上联获取
    if network.az.resource_type == CloudPlatform.BAREMETAL:
        nic_pgs = session.query(NodeNicPortgroup).filter(NodeNicPortgroup.network_id == network.id).all()
        for nic_pg in nic_pgs:
            key = "nic_pg_" + str(nic_pg.id)
            spg_dict[key] = {
                "host_name": nic_pg.node_host.host_name,
                "host_ip": nic_pg.node_host.ip_addr,
                "status": nic_pg.status,
                "nic_pg_id": nic_pg.id,
                "vlan_id": network.vlan_id,
                "connect_mode": nic_pg.switch_portgroup.connect_mode,
                "vlan_domain_id": nic_pg.switch_portgroup.vlan_domain_id,
                "logical_device_list": [pg_info.logic_device_id for pg_info in nic_pg.switch_portgroup.switch_portgroup_info],
                "nic_port_group":  nic_pg.portgroup_name,
                "nic_port": [",".join(pg_info.port_info.get("nic_port_list", [])) for pg_info in nic_pg.switch_portgroup.switch_portgroup_info if pg_info.port_info.get("nic_port_list", [])],
                "switch_port_group": nic_pg.switch_portgroup.portgroup_name,
                "attachment_type": nic_pg.switch_portgroup.link_type,
                "connect_leaf_info":[f"{pg_info.logic_device.logic_name} {','.join(pg_info.port_info.get('sw_port_info', {}).get(str(nic_pg.id), {}).get('port_list', []))}" 
                                     for pg_info in nic_pg.switch_portgroup.switch_portgroup_info]
                
                
            }
            if nic_pg.switch_portgroup.link_type == "MLAG Leaf" and len(nic_pg.switch_portgroup.switch_portgroup_info) == 1:
                pg_info = nic_pg.switch_portgroup.switch_portgroup_info[0]
                nodes = dc_fabric_db.get_fabric_topology_node_by_vd(nic_pg.switch_portgroup.vlan_domain_id)
                another_node = [node for node in nodes if node.id != pg_info.logic_device_id][0]
                spg_dict[key]["logical_device_list"].append(another_node.id)
    else:
        # 云平台处理
        link_mapping_list = dc_virtual_resource_db.get_virtual_resource_host_link_network_mapping(network.id, query_in_use=query_cloud_in_use) 
        for mapping in link_mapping_list:
            key = "cloud_" + str(mapping.id)
            spg_dict[key] = {
                "host_name": mapping.link.host.host_name,
                "host_ip": mapping.link.host.management_ip,
                "status": mapping.status,
                "cloud_link_id": mapping.id,
                "vlan_id": network.vlan_id,
                "connect_mode": mapping.link.connect_mode,
                "vlan_domain_id": mapping.link.vlan_domain_id,
                "logical_device_list": [port_info.logic_device_id for port_info in mapping.link.link_ports],
                "nic_port_group": "--",
                "nic_port": [],
                "switch_port_group": mapping.link.port_group_name,
                "attachment_type": mapping.link.link_type,
                "connect_leaf_info": [f"{port_info.logic_device.logic_name} {port_info.port_name}" for port_info in mapping.link.link_ports]
            }
            
            if mapping.link.link_type.lower() == "mlag leaf" and len(mapping.link.link_ports) == 1:
                link_port = mapping.link.link_ports[0]
                nodes = dc_fabric_db.get_fabric_topology_node_by_vd(mapping.link.vlan_domain_id)
                another_node = [node for node in nodes if node.id != link_port.logic_device_id][0]
                spg_dict[key]["logical_device_list"].append(another_node.id)
                
    return spg_dict


def get_logical_network_device_info(id, type, lp=None, li=None):
    session = dc_fabric_db.get_session()
    res = {}
    gw_interface = f"{li.anycast_ipv4}({lp.vlan})" if lp and li else None
    if type == "BareMetal":
        nic_pg = dc_virtual_resource_db.get_node_nic_portgroup_by_id(id)
        for pg_info in nic_pg.switch_portgroup.switch_portgroup_info:
            vlan_domain_pool = session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id == nic_pg.switch_portgroup.vlan_domain_id).first()
            if li:
                li_config = session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.logic_device_id == pg_info.logic_device_id, 
                                                                           DCLogicalInterfaceConfig.logical_interface_id == li.id).first()
            else:
                li_config = None
            
            switch = session.query(inventory.Switch).filter(inventory.Switch.sn == pg_info.switch_sn).first()
            info = {
                "vlan_domain": vlan_domain_pool.name if vlan_domain_pool else "--",
                "switch_sn": pg_info.switch_sn,
                "l2_vni": lp.l2vni if lp else "--",
                "l2_vlan": lp.vlan if lp else "--",
                "rd": "Auto",
                "rt": "Auto",
                "connected_lr": li.logical_router.name if li and li.logical_router else "--",
                "az": nic_pg.node_host.node_group.az_name,
                "mgmt_ip": switch.link_ip_addr,
                "vtep_id": pg_info.logic_device.node_info.get("vtep_interface", "--"),
                "vrf_vlan": li.logical_router.vrf_vlan if li else "--",
                "gw_interface": [gw_interface] if gw_interface else [] ,
                "virtual_ip": [li_config.virtual_ip] if li_config else []
            }
            res[pg_info.switch_sn] = info
        if nic_pg.switch_portgroup.link_type == "MLAG Leaf" and len(nic_pg.switch_portgroup.switch_portgroup_info) == 1:
            pg_info = nic_pg.switch_portgroup.switch_portgroup_info[0]
            nodes = dc_fabric_db.get_fabric_topology_node_by_vd(nic_pg.switch_portgroup.vlan_domain_id)
            another_node = [node for node in nodes if node.id != pg_info.logic_device_id][0]
            switch = session.query(inventory.Switch).filter(inventory.Switch.sn == another_node.switch_sn).first()
            if li:
                li_config = session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.logic_device_id == another_node.id, 
                                                                           DCLogicalInterfaceConfig.logical_interface_id == li.id).first()
            else:
                li_config = None
            
            info = {
                "vlan_domain": vlan_domain_pool.name if vlan_domain_pool else "--",
                "switch_sn": another_node.switch_sn,
                "l2_vni": lp.l2vni if lp else "--",
                "l2_vlan": lp.vlan if lp else "--",
                "rd": "Auto",
                "rt": "Auto",
                "connected_lr": li.logical_router.name if li and li.logical_router else "--",
                "az": nic_pg.node_host.node_group.az_name,
                "mgmt_ip": switch.link_ip_addr,
                "vtep_id": another_node.node_info.get("vtep_interface", "--"),
                "vrf_vlan": li.logical_router.vrf_vlan if li else "--",
                "gw_interface": [gw_interface] if gw_interface else [] ,
                "virtual_ip": [li_config.virtual_ip] if li_config else []

            }
            res[another_node.switch_sn] = info
    else:
        cloud_link = session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.id == id).first()
        host_link = dc_virtual_resource_db.get_virtual_resource_host_link_by_id(cloud_link.hostlink_id) 
        for link_info in host_link.link_ports:
            vlan_domain_pool = session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id == host_link.vlan_domain_id).first()
            switch = session.query(inventory.Switch).filter(inventory.Switch.sn == link_info.switch_sn).first()
            if li:
                li_config = session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.logic_device_id == link_info.logic_device_id, 
                                                                           DCLogicalInterfaceConfig.logical_interface_id == li.id).first()
            else:
                li_config = None
                                        
            info = {
                "vlan_domain": vlan_domain_pool.name if vlan_domain_pool else "--",
                "switch_sn": link_info.switch_sn,
                "l2_vni": lp.l2vni if lp else "--",
                "l2_vlan": lp.vlan if lp else "--",
                "rd": "Auto",
                "rt": "Auto",
                "connected_lr": li.logical_router.name if li and li.logical_router else "--",
                "az": host_link.host.az.az_name,
                "mgmt_ip": switch.link_ip_addr,
                "vtep_id": link_info.logic_device.node_info.get("vtep_interface", "--"),
                "vrf_vlan": li.logical_router.vrf_vlan if li else "--",
                "gw_interface": [gw_interface] if gw_interface else [],
                "virtual_ip": [li_config.virtual_ip] if li_config else []
            }
            res[link_info.switch_sn] = info
        if host_link.link_type.lower() == "mlag leaf" and len(host_link.link_ports) == 1:
            link_port = host_link.link_ports[0]
            nodes = dc_fabric_db.get_fabric_topology_node_by_vd(host_link.vlan_domain_id)
            another_node = [node for node in nodes if node.id != link_port.logic_device_id][0]
            switch = session.query(inventory.Switch).filter(inventory.Switch.sn == another_node.switch_sn).first()
            if li:
                li_config = session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.logic_device_id == another_node.id, 
                                                                           DCLogicalInterfaceConfig.logical_interface_id == li.id).first()
            else:
                li_config = None
            info = {
                "vlan_domain": vlan_domain_pool.name if vlan_domain_pool else "--",
                "switch_sn": another_node.switch_sn,
                "l2_vni": lp.l2vni if lp else "--",
                "l2_vlan": lp.vlan if lp else "--",
                "rd": "Auto",
                "rt": "Auto",
                "connected_lr": li.logical_router.name if li and li.logical_router else "--",
                "az": host_link.host.az.az_name,
                "mgmt_ip": switch.link_ip_addr,
                "vtep_id": another_node.node_info.get("vtep_interface", "--"),
                "vrf_vlan": li.logical_router.vrf_vlan if li else "--",
                "gw_interface": [gw_interface] if gw_interface else [],
                "virtual_ip": [li_config.virtual_ip] if li_config else []
            }
            res[another_node.switch_sn] = info
            
    return res


def check_logical_router_vrf_vlan(lr_id_list):
    for lr_id in lr_id_list:
        lr = dc_fabric_db.get_logical_router_by_id(lr_id)  
        
        if lr.vrf_vlan:
            # 没有lr连接时直接清理
            if not lr.dc_logical_interface:
                resource_pool_vlandomain_db.delete_vrfvlan_from_pool_by_logical_router(lr.vrf_vlan, lr.id)
                lr.vrf_vlan = None
                continue
            # TODO 存在其他interface时只清理不被占用的vd


def is_create_status(status):
    invalid_statuses = {OverlayStatus.DELETE_NORMAL, OverlayStatus.DELETE_ERROR}
    return status not in invalid_statuses

def create_ls_config(logic_device, lp_sw_config, ls, lp, spg_info, is_mlag):
    ls_config = {
        "meta": {
            "logic_device_id": logic_device.id,
            "fabric_id": lp_sw_config.id
        },
        "old_val": {},
        "new_val": {}
    }
    ls_config["new_val"]["logical_switch"] = {
        "l2vni": lp.l2vni,
        "l2vlan": lp.vlan,
        "arp_nd_suppress": ls.arp_nd_suppress,
        "is_mlag": is_mlag,
        "interface": [],  ## {"connect_mode": "access", "interface": []}
        "lag": []
    }
    ls_config["meta"]["port_config_id"] = lp_sw_config.id
    ls_config["old_val"] = lp_sw_config.config if lp_sw_config.config else {}
    
    if spg_info["port_info"]["lag_id"] == 0:
        # ls_config["new_val"]["logical_switch"]["interface"].extend(spg_info["port_info"]["port_list"]) 
        ls_config["new_val"]["logical_switch"]["interface"].append({
                "connect_mode": spg_info["connect_mode"],
                "interface": spg_info["port_info"]["port_list"]
            }
        )
    else:
        lag_id = spg_info["port_info"]["lag_id"]
        # ls_config["new_val"]["logical_switch"]["lag"].append(f"ae{lag_id}") 
        ls_config["new_val"]["logical_switch"]["lag"].append({
                "connect_mode": spg_info["connect_mode"],
                "lag": f"ae{lag_id}"
            })
    return ls_config

def create_lr_config(logic_device, lr_sw_config, lr, is_mlag):
    lr_config = {
        "meta": {
            "logic_device_id": logic_device.id,
            "fabric_id": lr_sw_config.id
        },
        "old_val": {},
        "new_val": {}
    }
    
    
    lr_config["new_val"]["logical_router"] = {
        "vrf_name": lr.vrf_name,
        "l3vni": lr.l3vni,
        "l3vlan": lr.vrf_vlan,
        "is_mlag": is_mlag,
        "asn": logic_device.node_info["asn"]
    }
    lr_config["meta"]["router_config_id"] = lr_sw_config.id
    lr_config["old_val"] = lr_sw_config.config if lr_sw_config.config else {}
    return lr_config


def create_lr_ls_config(logic_device, li_sw_config, lr, li, lp):
    lr_ls_config = {
        "meta": {
            "logic_device_id": logic_device.id,
            "fabric_id": li_sw_config.id
        },
        "old_val": {},
        "new_val": {}
    }
    lr_ls_config["new_val"]["router_to_switch"] = {
        "l2vlan": lp.vlan,
        "vrf_name": lr.vrf_name,
        "anycast_ip": li.anycast_ipv4,
        "virtual_ip": "",
        "anycast_mac": li.anycast_mac
    }
    lr_ls_config["meta"]["interface_config_id"] = li_sw_config.id
    lr_ls_config["old_val"] = li_sw_config.config if li_sw_config.config else {}
    return lr_ls_config
                               
                                
def check_overlay_config():
    try:
        session = dc_fabric_db.get_session()
        
        ln_list = session.query(DCLogicalNetwork).all()
        
        lr_configs = session.query(DCLogicalRouterConfig).all()
        ls_configs = session.query(DCLogicalPortConfig).all()
        lr_ls_configs = session.query(DCLogicalInterfaceConfig).all()
        
        old_lr_config = [config.id for config in lr_configs]  
        old_lr_ls_config = [config.id for config in lr_ls_configs]
        old_ls_config = [config.id for config in ls_configs]   
        new_lr_config = []
        new_lr_ls_config = []
        new_ls_config = [] 
        
        used_mac_list = []
        overlay_config = {}
        connect_info_dict = {}
        for ln in ln_list:
            for lr in ln.dc_logical_router:
                router_config = dc_fabric_db.get_logical_router_config_by_router_id(lr.id)
                for switch_config in router_config:
                    if switch_config.l3_anycast_mac:
                        used_mac_list.append(switch_config.l3_anycast_mac)
            
            for ls in ln.dc_logical_switch:
                # print(ls.name)
                
                ## 只取第一个 一个ls只能连到一个lr
                if len(ls.dc_logical_port) > 0:
                    lp = ls.dc_logical_port[0]
                else:
                    continue
                
                ## 检查ls是否有连接到vl2 且vl2是否有上联设备
                vn = lp.virtual_network
                network = session.query(DCVirtualResourceNetwork).filter(DCVirtualResourceNetwork.virtual_network_id == vn.id).first()
                # print(network)
                
                ## 这里应该不会出现找不到network的情况
                if not network:
                    continue
            
                spg_dict = {}
                ## 根据节点上联获取
                if network.az.resource_type == CloudPlatform.BAREMETAL:
                    nic_pgs = session.query(NodeNicPortgroup).filter(NodeNicPortgroup.network_id == network.id).all()
                    for nic_pg in nic_pgs:
                        # print(nic_pg.switch_portgroup.portgroup_name)
                        if nic_pg.switch_portgroup_id not in spg_dict:
                            spg_dict[nic_pg.switch_portgroup_id] = []
                            
                        for pg_info in nic_pg.switch_portgroup.switch_portgroup_info:
                            # print(pg_info.logic_device_id, pg_info.port_info["sw_port_info"][str(nic_pg.id)])
                            info = {
                                "logic_device_id": pg_info.logic_device_id,
                                "port_info": pg_info.port_info["sw_port_info"][str(nic_pg.id)],
                                "connect_mode": nic_pg.switch_portgroup.connect_mode,
                                "link_type": nic_pg.switch_portgroup.link_type,
                                "status": nic_pg.status
                            }
                            
                            spg_dict[nic_pg.switch_portgroup_id].append(info)
                           
                        # 单归情况下需要将配置同步到另一台设备 
                        if nic_pg.switch_portgroup.link_type == "MLAG Leaf" and len(nic_pg.switch_portgroup.switch_portgroup_info) == 1:
                            pg_info = nic_pg.switch_portgroup.switch_portgroup_info[0]
                            nodes = dc_fabric_db.get_fabric_topology_node_by_vd(nic_pg.switch_portgroup.vlan_domain_id)
                            another_node = [node for node in nodes if node.id != pg_info.logic_device_id][0]
                            info = {
                                "logic_device_id": another_node.id,
                                "port_info": pg_info.port_info["sw_port_info"][str(nic_pg.id)],
                                "connect_mode": nic_pg.switch_portgroup.connect_mode,
                                "link_type": nic_pg.switch_portgroup.link_type,
                                "status": nic_pg.status
                            }
                            
                            spg_dict[nic_pg.switch_portgroup_id].append(info)    
                else:
                    # 云平台处理
                    link_mapping_list = dc_virtual_resource_db.get_virtual_resource_host_link_network_mapping(network.id)   
                    for mapping in link_mapping_list:
                        spg_dict[mapping.link.id] = []
                        # 检查mapping的状态是否需要更新  
                        if lp.status == OverlayStatus.CREATE_NORMAL and mapping.status == "Disconnected" and mapping.in_use == True:
                            mapping.status = "Connect Successful"
                        for link_info in mapping.link.link_ports:
                            info = {
                                "logic_device_id": link_info.logic_device_id,
                                "port_info": {
                                    "lag_id": link_info.lag_id if link_info.lag_id else 0,
                                    "port_list": [port.strip() for port in link_info.port_name.split(',')],
                                },
                                "connect_mode": mapping.link.connect_mode,
                                "link_type": mapping.link.link_type,
                                "status": mapping.status
                            }
                            spg_dict[mapping.link.id].append(info)
                            
                        # 单归情况下需要将配置同步到另一台设备
                        if mapping.link.link_type.lower() == "mlag leaf" and len(mapping.link.link_ports) == 1:
                            link_port = mapping.link.link_ports[0]
                            nodes = dc_fabric_db.get_fabric_topology_node_by_vd(mapping.link.vlan_domain_id)
                            another_node = [node for node in nodes if node.id != link_port.logic_device_id][0]
                            info = {
                                "logic_device_id": another_node.id,
                                "port_info": {
                                    "lag_id": link_info.lag_id if link_info.lag_id else 0,
                                    "port_list": [],
                                },
                                "connect_mode": mapping.link.connect_mode,
                                "link_type": mapping.link.link_type,
                                "status": mapping.status
                            }
                            spg_dict[mapping.link.id].append(info)
                
                # 用于状态更新 提前获取以防止数据不一致， 后续可以考虑与spg_dict获取步骤合并
                connect_info = get_virtual_network_connect_info(vn)
                if vn.id not in connect_info_dict:
                    connect_info_dict[vn.id] = connect_info
                    
                LOG.info(f"pg info: {spg_dict.values()}")
                # print(ls.dc_logical_interface)
                li = None
                lr = None  
                # lr到ls只有一条线 所以取第一个
                if len(ls.dc_logical_interface) > 0:
                    li = ls.dc_logical_interface[0]
                    lr = ls.dc_logical_interface[0].logical_router 
                
                ls_config_data = {} 
                lr_config_data = {} 
                delete_statuses = {OverlayStatus.DELETE_NORMAL, OverlayStatus.DELETE_ERROR}
                
                if all(status not in delete_statuses for status in [ls.status, lp.status, vn.status]):
                    for spg in spg_dict.values():   
                        if li and lr and all(status not in delete_statuses for status in [lr.status, li.status]):
                            ## 需要配置lr时再分配mac
                            # 分配mac 在外层分配 因为同机架的mac相同
                            # 如果多个pg中 其中某一个pg已经分配过mac 则使用同一个mac
                            existing_mac = None
                            for spg_info in spg:
                                lr_sw_config = dc_fabric_db.get_logical_router_config_by_device(
                                    logical_router_id=li.id, 
                                    logic_device_id=spg_info["logic_device_id"]
                                )
                                if lr_sw_config and lr_sw_config.l3_anycast_mac:
                                    existing_mac = lr_sw_config.l3_anycast_mac
                                    break  # 找到已分配的MAC就停止查找
                            
                            if existing_mac:
                                new_mac = existing_mac
                            else:
                                l3_anycast_mac_range = lr.l3_anycast_mac_range
                                start_mac, end_mac = l3_anycast_mac_range.split('-')
                                new_mac = allocate_macs(start_mac, end_mac, used_mac_list) # 分配出来后先不加入used 真正使用时再加入
                                                        
                        for spg_info in spg:
                            if spg_info["status"] in ['Disconnecting', 'Disconnected', 'Disconnect Failed']:
                                continue
                            logic_device = dc_fabric_db.get_fabric_topology_node_info(node_id=spg_info["logic_device_id"])
                            is_mlag = True if logic_device.node_info.get("strategy", "") == "MLAG" else False
                            ## LS配置
                            lp_sw_config = dc_fabric_db.update_logical_port_config(lp_id=lp.id, logic_device_id=logic_device.id)
                            ls_config_key = logic_device.switch_sn
                            if ls_config_key not in ls_config_data: 
                                config = {
                                    "logical_switch": {},
                                    "router_to_switch": {}
                                }

                                ls_config = create_ls_config(logic_device, lp_sw_config, ls, lp, spg_info, is_mlag)

                                new_ls_config.append(lp_sw_config.id)
                                config["logical_switch"] = ls_config
                                ls_config_data[ls_config_key] = config   
                            else:
                                ## 如果已经有相同sn的相同ls配置 则只更新lag 或interface
                                if spg_info["port_info"]["lag_id"] == 0:
                                    ls_config_data[ls_config_key]["logical_switch"]["new_val"]["logical_switch"]["interface"].append({
                                            "connect_mode": spg_info["connect_mode"],
                                            "interface": spg_info["port_info"]["port_list"]
                                        })                                                                                                 
                                else:
                                    lag_id = spg_info["port_info"]["lag_id"]
                                    ls_config_data[ls_config_key]["logical_switch"]["new_val"]["logical_switch"]["lag"].append({
                                            "connect_mode": spg_info["connect_mode"],
                                            "lag": f"ae{lag_id}"
                                        })
                                
                            ## 如果lr li 已经配置
                            if  li and lr and all(status not in delete_statuses for status in [lr.status, li.status]): 
                                
                                # 获取已经分配过的virtual ip 便于后续继续分配
                                interface_config = dc_fabric_db.get_logical_interface_config_by_interface(li.id)
                                used_vip_list = []
                                
                                for switch_config in interface_config:
                                    if switch_config.virtual_ip:
                                        used_vip_list.append(switch_config.virtual_ip)
                                        
                                # 检查lr vrf_vlan是否已分配
                                check_res = resource_pool_vlandomain_db.check_vrf_vlan_value_with_vlan_domain(lr.vrf_vlan, logic_device.vlan_domain_pool_id)
                                vrf_vlan_status = True
                                if check_res["res"] and check_res["lr_id"] == lr.id:
                                    pass
                                elif check_res["res"] and check_res["lr_id"] != lr.id:
                                    vrf_vlan_status = False
                                else:
                                    res = resource_pool_vlandomain_db.generate_vrfvlan_from_pool_by_value(logic_device.vlan_domain_pool_id, lr.vrf_vlan, lr.id)
                                    if not res:
                                        vrf_vlan_status = False
                                          
                                if vrf_vlan_status:
                                    ## LR相关配置
                                    lr_sw_config = dc_fabric_db.update_logical_router_config(lr_id=lr.id, logic_device_id=logic_device.id) 
                                    lr_config = create_lr_config(logic_device, lr_sw_config, lr, is_mlag)

                                    # 如果已经配置过LR 就不需要使用新的mac了
                                    if lr_sw_config.l3_anycast_mac:
                                        lr_config["new_val"]["logical_router"]["l3anycast_mac"]= lr_sw_config.l3_anycast_mac 
                                    else:
                                        lr_config["new_val"]["logical_router"]["l3anycast_mac"]= new_mac
                                        used_mac_list.append(new_mac)
                                        dc_fabric_db.update_logical_router_config(lr_config_id=lr_sw_config.id, l3_anycast_mac=new_mac)
                                        
                                    new_lr_config.append(lr_sw_config.id)
                                    lr_config_data[logic_device.switch_sn] = lr_config
                                    
                                    ## LR连接LS相关配置
                                    li_sw_config = dc_fabric_db.update_logical_interface_config(li_id=li.id, logic_device_id=logic_device.id)   
                                    lr_ls_config = create_lr_ls_config(logic_device, li_sw_config, lr, li, lp)
                                        
                                    lr_ls_config["new_val"]["router_to_switch"]["link_type"] = spg_info["link_type"]
                                    # 分配ip
                                    if li_sw_config.virtual_ip:
                                        lr_ls_config["new_val"]["router_to_switch"]["virtual_ip"] = li_sw_config.virtual_ip
                                    else:
                                        vip = allocate_virtual_ip(li.virtual_ipv4_range, used_vip_list)
                                        lr_ls_config["new_val"]["router_to_switch"]["virtual_ip"] = vip
                                        used_vip_list.append(vip)
                                        dc_fabric_db.update_logical_interface_config(li_config_id=li_sw_config.id, virtual_ip=vip)
                                
                                    new_lr_ls_config.append(li_sw_config.id)
                                    ls_config_data[ls_config_key]["router_to_switch"] = lr_ls_config
                                else:
                                    LOG.info("vrf vlan check failed, skip config")
                                    # 直接更新 li config状态为失败
                                    err_msg = "unable to assign vrf vlan"
                                    lr_sw_config = dc_fabric_db.update_logical_router_config(lr_id=lr.id, logic_device_id=logic_device.id, status=DeployStatus.FAILED, err_msg=err_msg)   
                                    li_sw_config = dc_fabric_db.update_logical_interface_config(li_id=li.id, logic_device_id=logic_device.id, status=DeployStatus.FAILED, err_msg=err_msg)  
                                    # vrf vlan 没有对应的分配也需要加入new_lr_config 防止被删除
                                    new_lr_config.append(lr_sw_config.id)
                                    new_lr_ls_config.append(li_sw_config.id)
                     
                    # print(lr_config_data)
                    # print(ls_config_data)                
    
                    if  li and lr and all(status not in delete_statuses for status in [lr.status, li.status]):
                        for sn, config in lr_config_data.items():
                            # print(sn, config)
                            if sn not in overlay_config:
                                overlay_config[sn] = {
                                    lr.id : {
                                        "logical_router": config,
                                        "logical_switch": []    
                                    },
                                    "no_router": {
                                        "logical_router": {},
                                        "logical_switch": []
                                    }
                                }
                            else:
                                if lr.id not in overlay_config[sn]:
                                    overlay_config[sn][lr.id] = {
                                        "logical_router": config,
                                        "logical_switch": []  
                                    }
                        for sn, config in  ls_config_data.items():
                            # print(sn, config)
                            if sn not in overlay_config:
                                # 如果sn不在overlay_config中 说明lr配置生成失败 需要加入no_router中 不能影响ls配置下发
                                overlay_config[sn] = {
                                    "no_router": {
                                        "logical_router": {},
                                        "logical_switch": [config]
                                    }
                                }
                            elif lr.id not in overlay_config[sn]:
                                # 如果lr id 不在 overlay_config[sn] 说明lr配置生成失败 加入no_router
                                overlay_config[sn]["no_router"]["logical_switch"].append(config)
                            else:
                                overlay_config[sn][lr.id]["logical_switch"].append(config)
                    else:
                        for sn, config in  ls_config_data.items():
                            if sn not in overlay_config:
                                overlay_config[sn] = {
                                    "no_router": {
                                        "logical_router": {},
                                        "logical_switch": []
                                    }
                                }
                            overlay_config[sn]["no_router"]["logical_switch"].append(config)
                            
        
        new_config = {} 
        for sn, config in overlay_config.items():
            new_config[sn] = list(config.values())
        # print("new_config: ", new_config) 
        LOG.info(f"new_config: {new_config}")
        
        async def async_overlay_config_distribution(sn, config_list):
            # 将同步函数放到子线程中运行（避免阻塞事件循环）
            await asyncio.to_thread(overlay_config_distribution, sn, config_list)
        
        async def distribute_configs_async(new_config):
            tasks = []
            for sn, config_list in new_config.items():
                # 创建异步任务
                task = asyncio.create_task(async_overlay_config_distribution(sn, config_list))
                tasks.append(task)
            
            # 等待所有任务完成
            await asyncio.gather(*tasks)
        
        asyncio.run(distribute_configs_async(new_config))
        
        # 清理不需要的配置            
        print(old_lr_config, new_lr_config)
        print(old_lr_ls_config, new_lr_ls_config)
        print(old_ls_config, new_ls_config) 
        
        delete_config={} 
        def add_to_delete_config(config_type, config_id, get_config_func, config_key, config_id_key):
            # print(f"delete {config_type} config: ", config_id)
            config = get_config_func(config_id)
            logic_device = dc_fabric_db.get_fabric_topology_node_info(node_id=config.logic_device_id)
            
            if not logic_device:
                return 
            if logic_device.switch_sn not in delete_config:
                delete_config[logic_device.switch_sn] = {
                    "router_to_switch": [],
                    "logical_router": [],
                    "logical_switch": []
                }
                
            to_delete_config = {
                    "meta": {"logic_device_id": logic_device.id, "fabric_id": config.id},
                    "old_val": {},
                    "new_val": {}
            }
            
            to_delete_config["new_val"][config_key] = {}
            to_delete_config["old_val"] = config.config if config.config else {config_type: {}}
            to_delete_config["meta"][config_id_key] = config.id
            
            delete_config[logic_device.switch_sn][config_type].append(to_delete_config)

        # 定义配置类型映射
        config_mapping = [
            {
                "old": old_ls_config,
                "new": new_ls_config,
                "type": "logical_switch",
                "id_key": "port_config_id",
                "func": dc_fabric_db.get_logical_port_config_by_id
            },
            {
                "old": old_lr_ls_config,
                "new": new_lr_ls_config,
                "type": "router_to_switch",
                "id_key": "interface_config_id",
                "func": dc_fabric_db.get_logical_interface_config_by_id
            },
            {
                "old": old_lr_config,
                "new": new_lr_config,
                "type": "logical_router",
                "id_key": "router_config_id",
                "func": dc_fabric_db.get_logical_router_config_by_id
            }
        ]

        # 统一处理所有配置类型
        for mapping in config_mapping:
            for config_id in mapping["old"]:
                if config_id not in mapping["new"]:
                    add_to_delete_config(mapping["type"], config_id, mapping["func"], mapping["type"], mapping["id_key"])
            
        # TODO 下发删除配置 需要回调判断网元是否需要删除    
        # print(delete_config)
        LOG.info(f"delete_config: {delete_config}")

        async def async_overlay_delete_config_distribution(sn, config_dict):
            # 将同步函数放到子线程中运行（避免阻塞事件循环）
            await asyncio.to_thread(overlay_delete_config_distribution, sn, config_dict)
        
        async def distribute_delete_configs_async(delete_config):
            tasks = []
            for sn, config_dict in delete_config.items():
                # 创建异步任务
                task = asyncio.create_task(async_overlay_delete_config_distribution(sn, config_dict))
                tasks.append(task)
            
            # 等待所有任务完成
            await asyncio.gather(*tasks)
        
        asyncio.run(distribute_delete_configs_async(delete_config))
        
        update_overlay_status(ln_list, connect_info_dict)

    except Exception as e:
        import traceback
        LOG.error(traceback.format_exc())
 
def get_config_distribution_task_errmsg(trace_id, type):
    session = dc_fabric_db.get_session()
    task = session.query(inventory.ConfigDistributionTaskForDC).filter(inventory.ConfigDistributionTaskForDC.fabric_id == trace_id,
                                                                       inventory.ConfigDistributionTaskForDC.type == type)\
                                                               .order_by(inventory.ConfigDistributionTaskForDC.create_time.desc()).first()
    if task.task_traceback_info:
        error_lines = task.task_traceback_info.split('\n')
        filtered_lines = [line for line in error_lines if line]
        error_reason = filtered_lines[-1].strip().split(':')[-1].strip()  
        return error_reason
    else:
        return ""                                          

## 放在celery_worker中做编排 实际配置下发放到config_distribute
def overlay_config_distribution(sn, config_list):
    # print(sn, config_list)
    for config in config_list:
        if config.get("logical_router", {}):
            lr_config = config.get("logical_router", {})
            # 存在logical_router 需要等待logical_router下发完成再下发其他配置
            lr_config_dict = {sn: lr_config}
            # 调用配置下发接口同步等待结果 如果配置失败 LR-LS的配置不需要下发
            # print(f"overlay_router start: {lr_config_dict}")
            lr_res = config_distribution_netconf_by_dc_overlay(lr_config_dict, "overlay_router", asynchronous=False)
            LOG.info(f"overlay_router {sn} res: {lr_res}")
            ## 更新下发状态
            if not (lr_res.get("status", 0) == 200 and lr_res.get("res", "") == "Deployed"):
                router_config_id = lr_config["meta"]["router_config_id"]
                err_msg = get_config_distribution_task_errmsg(router_config_id, "overlay_router")
                dc_fabric_db.update_logical_router_config(lr_config_id=router_config_id, status=DeployStatus.FAILED, err_msg=err_msg)
            
            # lr配置成功 开始下发ls相关配置
            ls_config_list = config.get("logical_switch", [])
            for ls_config_dict in ls_config_list:
                ls_config = ls_config_dict.get("logical_switch", {})
                config_dict = {sn: ls_config}
                # 调用配置下发接口同步等待结果 如果配置失败 不下发连接配置
                # print(f"overlay_switch start: {config_dict}")
                ls_res = config_distribution_netconf_by_dc_overlay(config_dict, "overlay_switch", asynchronous=False)
                LOG.info(f"overlay_switch {sn} res: {ls_res}")
                ## 更新下发状态
                if not (ls_res.get("status", 0) == 200 and ls_res.get("res", "") == "Deployed"):
                    port_config_id = ls_config["meta"]["port_config_id"]
                    err_msg = get_config_distribution_task_errmsg(port_config_id, "overlay_switch")
                    dc_fabric_db.update_logical_port_config(lp_config_id=port_config_id, status=DeployStatus.FAILED, err_msg=err_msg)
                if lr_res.get("status", 0) == 200 and lr_res.get("res", "") == "Deployed" and ls_res.get("status", 0) == 200 and ls_res.get("res", "Deployed"):
                    # lr和ls配置成功开始下发连接相关配置
                    lr_ls_config = ls_config_dict.get("router_to_switch", {})
                    lr_ls_config_dict = {sn: lr_ls_config}
                    # 调用配置下发接口等待结束
                    # print(f"overlay_link start: {lr_config_dict}")
                    res = config_distribution_netconf_by_dc_overlay(lr_ls_config_dict, "overlay_link", asynchronous=False)
                    LOG.info(f"overlay_link {sn} res: {res}")
                    ## 更新下发状态
                    if not (res.get("status", 0) == 200 and res.get("res", "") == "Deployed"):
                        interface_config_id = lr_ls_config["meta"]["interface_config_id"]
                        err_msg = get_config_distribution_task_errmsg(interface_config_id, "overlay_link")
                        dc_fabric_db.update_logical_interface_config(li_config_id=interface_config_id, status=DeployStatus.FAILED, err_msg=err_msg)
                else:
                    lr_ls_config = ls_config_dict.get("router_to_switch", {})
                    interface_config_id = lr_ls_config["meta"]["interface_config_id"]
                    dc_fabric_db.update_logical_interface_config(li_config_id=interface_config_id, status=DeployStatus.FAILED)
                    
            
        else:
            # 不存在logical_router 只需要下发logical_switch配置
            ls_config_list = config.get("logical_switch", [])
            for ls_config_dict in ls_config_list:
                ls_config = ls_config_dict.get("logical_switch", {})
                config_dict = {sn: ls_config}
                # 调用配置下发 同步等待任务结束 不管成功失败再开始下发下一个配置
                # print(f"overlay_switch start: {config_dict}")
                res = config_distribution_netconf_by_dc_overlay(config_dict, "overlay_switch", asynchronous=False)
                LOG.info(f"overlay_switch {sn} res: {res}")
                if not (res.get("status", 0) == 200 and res.get("res", "") == "Deployed"):
                    port_config_id = ls_config["meta"]["port_config_id"]
                    err_msg = get_config_distribution_task_errmsg(port_config_id, "overlay_switch")
                    dc_fabric_db.update_logical_port_config(lp_config_id=port_config_id, status=DeployStatus.FAILED, err_msg=err_msg)
                

def overlay_delete_config_distribution(sn, config_dict):
    # print(sn, config_dict)
    router_to_switch_configs =  config_dict.get("router_to_switch", [])
    switch_configs =  config_dict.get("logical_switch", [])
    router_configs =  config_dict.get("logical_router", [])
    delete_failed_interface_config = []
    
    for config in router_to_switch_configs:
        config_dict = {sn: config}
        # print(f"overlay_link start: {config_dict}")
        res = config_distribution_netconf_by_dc_overlay(config_dict, "overlay_link", asynchronous=False)
        LOG.info(f"delete overlay_link {sn} res: {res}")
        interface_config_id = config["meta"]["interface_config_id"]
        if not (res.get("status", 0) == 200 and res.get("res", "") == "Deployed"):
            err_msg = get_config_distribution_task_errmsg(interface_config_id, "overlay_link")
            dc_fabric_db.update_logical_interface_config(li_config_id=interface_config_id, status=DeployStatus.FAILED, err_msg=err_msg)
            delete_failed_interface_config.append(interface_config_id)
        else:
            dc_fabric_db.delete_logical_interface_config(interface_config_id)
    
    ## interface配置删除失败 对应的lr和ls不能执行删除 标记为删除失败
    session = dc_fabric_db.get_session()
    failed_interface_config =  session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.id.in_(delete_failed_interface_config)).all()
    lr_id_list = []
    lp_id_list = []
    for interface_config in failed_interface_config:
        li = interface_config.logical_interface
        lr_id_list.append(li.logical_router_id)
        
        ls = li.logical_switch
        if len(ls.dc_logical_port) > 0:
            lp = ls.dc_logical_port[0]
            lp_id_list.append(lp.id)
    
    lr_config_list = session.query(DCLogicalRouterConfig).filter(DCLogicalRouterConfig.logical_router_id.in_(lr_id_list)).all()
    exclude_lr_config_id_list = [config.id for config in lr_config_list]
    lp_config_list = session.query(DCLogicalPortConfig).filter(DCLogicalPortConfig.logical_port_id.in_(lp_id_list)).all()
    exclude_lp_config_id_list = [config.id for config in lp_config_list]
        
    for config in switch_configs:
        config_dict = {sn: config}
        port_config_id = config["meta"]["port_config_id"]
        if port_config_id in exclude_lp_config_id_list:
            dc_fabric_db.update_logical_port_config(lp_config_id=port_config_id, status=DeployStatus.FAILED, err_msg="delete logical interface failed")
            continue
        # print(f"overlay_switch start: {config_dict}")
        res = config_distribution_netconf_by_dc_overlay(config_dict, "overlay_switch", asynchronous=False)
        LOG.info(f"delete overlay_switch {sn} res: {res}")
        if not (res.get("status", 0) == 200 and res.get("res", "") == "Deployed"):
            err_msg = get_config_distribution_task_errmsg(port_config_id, "overlay_switch")
            dc_fabric_db.update_logical_port_config(lp_config_id=port_config_id, status=DeployStatus.FAILED, err_msg=err_msg)
        else:
            dc_fabric_db.delete_logical_port_config(port_config_id)
        
    for config in router_configs:
        config_dict = {sn: config}
        router_config_id = config["meta"]["router_config_id"]
        if router_config_id in exclude_lr_config_id_list:
            dc_fabric_db.update_logical_router_config(lr_config_id=router_config_id, status=DeployStatus.FAILED, err_msg="delete logical interface failed")
            continue
        # print(f"overlay_router start: {config_dict}")
        res = config_distribution_netconf_by_dc_overlay(config_dict, "overlay_router", asynchronous=False)
        LOG.info(f"delete overlay_router {sn} res: {res}")
        if not (res.get("status", 0) == 200 and res.get("res", "") == "Deployed"):
            err_msg = get_config_distribution_task_errmsg(router_config_id, "overlay_router")
            dc_fabric_db.update_logical_router_config(lr_config_id=router_config_id, status=DeployStatus.FAILED, err_msg=err_msg)
        else:
            dc_fabric_db.delete_logical_router_config(router_config_id)
        
        
def determine_status(entity, failed_config):
    if failed_config:
        ## 存在失败的配置且状态为创建 更新为创建失败
        if entity.status in {OverlayStatus.CREATE_ERROR, OverlayStatus.CREATE_NORMAL}:
            return OverlayStatus.CREATE_ERROR
        ## 存在失败的配置且状态为删除 更新为删除失败
        elif entity.status in {OverlayStatus.DELETE_NORMAL, OverlayStatus.DELETE_ERROR}:
            return OverlayStatus.DELETE_ERROR
    else:
        ## 不存在失败的配置且状态为创建 更新为创建正常
        if entity.status in {OverlayStatus.CREATE_ERROR, OverlayStatus.CREATE_NORMAL}:
            return OverlayStatus.CREATE_NORMAL
        ## 不存在失败的配置且状态为删除 准备删除
        elif entity.status in {OverlayStatus.DELETE_NORMAL, OverlayStatus.DELETE_ERROR}:
            return "DELETE"  # 特殊标记表示需要删除
    
    return entity.status

def update_overlay_status(ln_list, connect_info_dict):
    session = dc_fabric_db.get_session()
    
    lr_configs = session.query(DCLogicalRouterConfig)
    ls_configs = session.query(DCLogicalPortConfig)
    lr_ls_configs = session.query(DCLogicalInterfaceConfig)
    
    del_li = []
    del_lp = []
    for ln in ln_list:     
        for ls in ln.dc_logical_switch:
            lp=None
            if len(ls.dc_logical_port) > 0:
                lp = ls.dc_logical_port[0]
            if lp:
                failed_switch_config = ls_configs.filter(DCLogicalPortConfig.logical_port_id == lp.id, DCLogicalPortConfig.status == DeployStatus.FAILED).first()
                # 更新lp状态
                new_lp = dc_fabric_db.get_logical_port_by_logical_switch_id(ls_id=ls.id)
                if new_lp and lp.status == new_lp.status:
                    new_status = determine_status(lp, failed_switch_config)
                    if new_status == "DELETE":
                        del_lp.append(lp.id)
                    else:
                        lp.status = new_status
                
                ## 更新vl2状态
                vn = lp.virtual_network 
                connect_info = {}
                if vn.id in connect_info_dict:
                    connect_info = connect_info_dict[vn.id]
                    new_connect_info = get_virtual_network_connect_info(vn)
                    
                for key, connect in connect_info.items():
                    if key in new_connect_info and new_connect_info[key]["status"] != connect["status"]:
                        continue
                    failed_device_config = ls_configs.filter(DCLogicalPortConfig.logical_port_id == lp.id, 
                                                             DCLogicalPortConfig.logic_device_id.in_(connect["logical_device_list"]), 
                                                             DCLogicalPortConfig.status == DeployStatus.FAILED).first()
                    final_status = "Connect Successful"
                    if failed_device_config:
                        if connect["status"] in {'Connecting', 'Connect Successful', 'Connect Failed'}:
                            final_status = 'Connect Failed'
                        elif connect["status"] in {'Disconnecting', 'Disconnected', 'Disconnect Failed'}:
                            final_status = 'Disconnect Failed'
                    else:
                        if connect["status"] in {'Connecting', 'Connect Successful', 'Connect Failed'}:
                            final_status = 'Connect Successful'
                        elif connect["status"] in {'Disconnecting', 'Disconnected', 'Disconnect Failed'}:
                            final_status = 'Disconnected'
                            
                    if connect.get("nic_pg_id", None) is not None:
                        dc_virtual_resource_db.update_node_nic_portgroup(nic_id=connect["nic_pg_id"], status=final_status)
                    else:
                        session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.id == connect["cloud_link_id"]).update({"status": final_status})

                # 更新ls状态
                new_status = determine_status(ls, failed_switch_config)
                if new_status != "DELETE":
                    ls.status = new_status
                
                # 更新vn状态
                # new_status = determine_status(vn, failed_switch_config)
                # if new_status != "DELETE":
                #     vn.status = new_status
                # vn 不需要变红 直接更新为normal即可    
                vn.status = OverlayStatus.CREATE_NORMAL               
                
        for li in ln.dc_logical_interface:
            new_li = dc_fabric_db.get_logical_interface_by_id(li.id)
            if li.status == new_li.status:
                failed_lr_ls_config = lr_ls_configs.filter(DCLogicalInterfaceConfig.logical_interface_id == li.id, DCLogicalInterfaceConfig.status == DeployStatus.FAILED).first()
                ## 更新lr-ls状态
                new_status = determine_status(li, failed_lr_ls_config)
                if new_status == "DELETE":
                    del_li.append(li.id)
                else:
                    li.status = new_status
        
        for lr in ln.dc_logical_router:
            failed_router_config = lr_configs.filter(DCLogicalRouterConfig.logical_router_id == lr.id, DCLogicalRouterConfig.status == DeployStatus.FAILED).first() 
            ## 更新lr状态
            new_status = determine_status(lr, failed_router_config)
            if new_status != "DELETE":
                lr.status = new_status
                
    
    print(del_li, del_lp)  
    del_li_obj = session.query(DCLogicalInterface).filter(DCLogicalInterface.id.in_(del_li)).all()
    lr_id_list = [li.logical_router_id for li in del_li_obj]
    session.query(DCLogicalInterface).filter(DCLogicalInterface.id.in_(del_li)).delete()
    check_logical_router_vrf_vlan(list(set(lr_id_list)))
    ## 回收l2vni和 l2vlan
    for lp_id in del_lp:
        lp = dc_fabric_db.get_logical_port_by_id(lp_id)
        resource_pool_vlandomain_db.delete_bdvlan_from_pool_by_logical_switch(lp.vlan, lp.logical_switch_id)
        resource_pool_vni_db.delete_resource_by_fabric(lp.logical_switch.fabric_id, lp.l2vni)
    
    session.query(DCLogicalPort).filter(DCLogicalPort.id.in_(del_lp)).delete()
    

def generate_lr_cli_config(vrf_name, l3vni, l3vlan):
    config = f"""
set ip vrf {vrf_name}
set vxlans vni {l3vni} vlan {l3vlan}
set vxlans vrf {vrf_name} l3-vni {l3vni}
set vlans vlan-id {l3vlan} l3-interface "vlan{l3vlan}"
set l3-interface vlan-interface vlan{l3vlan} vrf "{l3vlan}" {vrf_name}
set protocols bgp vrf {vrf_name} evpn advertise ipv4-unicast
"""
    return config

def generate_ls_cli_config(l2vlan, l2vni, arp_nd_suppress):
    config = f"""
set vxlans vni {l2vni} vlan {l2vlan} 
set vlans vlan-id {l2vlan}  
"""
    if arp_nd_suppress:
        config += f"set vxlans vni {l2vni} arp-nd-suppress\n"
    return config

def generate_lr_ls_cli_config(l2vlan, anycast_ip, anycast_mac, vrf_name): 
    config = f"""
set vlans vlan-id {l2vlan} l3-interface "vlan{l2vlan}"
set l3-interface vlan-interface vlan{l2vlan} vrf "{vrf_name}" 
set l3-interface vlan-interface vlan{l2vlan} anycast address {anycast_ip.split("/")[0]} prefix-length {anycast_ip.split("/")[1]}
set l3-interface vlan-interface vlan{l2vlan} anycast mac {anycast_mac}
set protocols bgp vrf {vrf_name} ipv4-unicast network {ipaddress.ip_interface(anycast_ip)} 
"""
    return config
    

def get_current_configuration(ln_id):
    session = dc_fabric_db.get_session()
    ln = session.query(DCLogicalNetwork).filter(DCLogicalNetwork.id == ln_id).first()
    
    config = {}
    for lr in ln.dc_logical_router:
        lr_config = session.query(DCLogicalRouterConfig).filter(DCLogicalRouterConfig.logical_router_id == lr.id, DCLogicalRouterConfig.status == DeployStatus.SUCCEED).first()
        if lr_config and lr_config.config.get("logical_router"):
            config[f"logical router: {lr.name}"] = generate_lr_cli_config(lr_config.config["logical_router"]["vrf_name"],
                                                     lr_config.config["logical_router"]["l3vni"],
                                                     lr_config.config["logical_router"]["l3vlan"])
    
    for ls in ln.dc_logical_switch:
        lp = ls.dc_logical_port[0] if ls.dc_logical_port else None
        ls_cli = None
        if lp:
            lp_config = session.query(DCLogicalPortConfig).filter(DCLogicalPortConfig.logical_port_id == lp.id, DCLogicalPortConfig.status == DeployStatus.SUCCEED).first()
            if not lp_config and lp.status == OverlayStatus.DELETE_ERROR:
                lp_config = session.query(DCLogicalPortConfig).filter(DCLogicalPortConfig.logical_port_id == lp.id).first()
            if lp_config and lp_config.config.get("logical_switch"):
                ls_cli = generate_ls_cli_config(lp_config.config["logical_switch"]["l2vlan"],
                                                lp_config.config["logical_switch"]["l2vni"],
                                                lp_config.config["logical_switch"]["arp_nd_suppress"]) 
                
        li = ls.dc_logical_interface[0] if ls.dc_logical_interface else None
        li_cli = None
        if li:
            li_config = session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.logical_interface_id == li.id, DCLogicalInterfaceConfig.status == DeployStatus.SUCCEED).first()
            if not li_config and li.status == OverlayStatus.DELETE_ERROR:
                li_config = session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.logical_interface_id == li.id).first()
            if li_config and li_config.config.get("router_to_switch"):
                li_cli = generate_lr_ls_cli_config(li_config.config["router_to_switch"]["l2vlan"],
                                                   li_config.config["router_to_switch"]["anycast_ip"],
                                                   li_config.config["router_to_switch"]["anycast_mac"],
                                                   li_config.config["router_to_switch"]["vrf_name"])
                
        if ls_cli and li_cli:
            config[f"logical switch: {ls.name}"] = ls_cli + li_cli
        elif ls_cli:
            config[f"logical switch: {ls.name}"] = ls_cli
            
    return config