import re
from server.db.models import inventory
from server.db.models.alarm_trap import SnmpAlarmTrapOriginalData
from server.db.models.monitor import monitor_db
import json

invent_db = inventory.inven_db


def handle_alarm_trap(alarm_trap_data):
    alarmTrapOriginalModel = alarm_trap_data.get("AlarmTrapOriginalModel")
    generalAlarmModel = alarm_trap_data.get("GeneralAlarmModel")

    print(f"alarmTrapOriginalModel:{alarmTrapOriginalModel} , generalAlarmModel:{generalAlarmModel}")

    # 先入库
    db_session = invent_db.get_session()
    with db_session.begin():
        for original in alarmTrapOriginalModel:
            snmpAlarmTrapOriginalData = SnmpAlarmTrapOriginalData(source_ip=original["source_ip"],
                                                                  occurrence_time=original["occurrence_time"],
                                                                  name=original["oid"], value=original["value"],
                                                                  description="")
            db_session.add(snmpAlarmTrapOriginalData)

        for general in generalAlarmModel:
            alarmStr = general["alarm_name"]
            print(f"Alarm text is:{alarmStr}")
            if "normal" in alarmStr:
                monitor_db.handle_event(general["sn"], general["resource_id"], general["alarm_name"])
            else:
                monitor_db.add_event(general["sn"], general["alarm_level"], general["alarm_name"], general["resource_id"])
