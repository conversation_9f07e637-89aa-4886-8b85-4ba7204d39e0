import os
import shutil

import requests

from server.db.models import general, inventory
from server.db.models.general import GeneralTemplate
from server.db.models.general import general_db
from server import constants
from server import cfg
from server.util import utils


def upload_template_implement(name, description, content_lines, is_internal=False):
    content = ''
    param = ''
    content_start = False
    param_start = False

    if '(' in name or ')' in name or ':' in name or '=' in name or '\\' in name:
        return {'status': 500, 'info': 'Template name is invalid'}

    template_exit = general_db.get_model(general.GeneralTemplate, filters={'name': [name]})
    if template_exit:
        return {'status': 400, 'info': 'template %s already exited' % name}

    if type(content_lines[0]) != str:
        content_lines = list(map(lambda x: x.decode(), content_lines))
    for line in content_lines:

        if 'content_start' in line:
            content_start = True
            continue
        if 'content_end$' in line:
            content_start = False
            continue
        if 'param_start' in line:
            param_start = True
            continue
        if 'param_end$' in line:
            param_start = False
            continue

        if content_start:
            content += line
            continue
        if param_start:
            param += line
            continue
        if 'name:' in line:
            if name == '':
                name = line.split(':')[1].strip()
            continue
        if 'description:' in line:
            if description == '':
                description = line.split(':')[1].strip()
            continue

    template = general.GeneralTemplate(name=name, description=description, j2_template=content,
                                       params=param, internal=is_internal)

    general_db.insert(template)
    return {'status': 200, 'info': 'upload template %s success' % name}


def template_remove_implement(name):
    general_db.delete_collection(general.GeneralConfigParams, filters={'template_name': [name]})
    general_db.delete_collection(general.GeneralTemplate, filters={'name': [name]})
    return 'ok'


def download_internal_templates():
    res = []
    proxy = cfg.CONF.license_portal_proxy
    git_file_path = '/tmp/git'
    zip_file_path = '/tmp/main.zip'
    if os.path.exists(git_file_path):
        shutil.rmtree(git_file_path)
    if os.path.exists(zip_file_path):
        os.remove(zip_file_path)

    # max retry 3 times
    max_retries = 3
    response = None
    for _ in range(max_retries):
        try:
            if proxy.get('https'):
                response = requests.get(constants.INTERNAL_TEMPLATE_GIT_DOWNLOAD_URL, proxies={'https': proxy.get('https')}, timeout=20)
            else:
                response = requests.get(constants.INTERNAL_TEMPLATE_GIT_DOWNLOAD_URL, timeout=20)
            if response.status_code == 200:
                with open(zip_file_path, 'wb') as f:
                    f.write(response.content)
                break
        except requests.exceptions.RequestException as e:
            if _ < max_retries - 1:
                continue
            else:
                return []
    if response and response.status_code == 200:
        with open(zip_file_path, 'wb') as f:
            f.write(response.content)
    else:
        return []
    if not os.path.exists('/tmp/main.zip'):
        return []
    utils.unzipFile(zip_file_path='/tmp/main.zip', unzip_path='/tmp/git')
    source_path = '/tmp/git/Jinja_Templates-main/configuration_templates/default_prebuilt_template'
    names = list(os.walk(source_path, topdown=True))[0][2]
    for name in names:
        temp = {'name': name, 'description': name}
        with open(os.path.join(source_path, name), 'r') as f:
            temp['content_lines'] = f.readlines()
        res.append(temp)
    return res


def is_internal_template(name):
    db_session = general_db.get_session()
    template = db_session.query(GeneralTemplate).filter(GeneralTemplate.name == name).first()
    if template.internal:
        return True
    else:
        return False
