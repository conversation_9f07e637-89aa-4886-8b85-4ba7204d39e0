import json
import logging
import time
import traceback
import paramiko
import json
from server.db.models import inventory
from server.db.models.otn import OtnTempData, OtnDeviceBasic
from server.util.db_distributed_lock import DistributedLock
from server.util.socket_client import SocketClient
import socket

invent_db = inventory.inven_db
LOG = logging.getLogger(__name__)

DCP920_CONFIG_MODEL = {"power": {"TX_direction_power": {"get": "PGV_0", "set": "EDFA_PGV1"},
                                 "RX_direction_power": {"get": "PGV_1", "set": "EDFA_PGV2"},
                                 "TX_Work_model": {"get": "M_0", "set": "VOA_M1"},
                                 "TX_Alarm_threshold": {"get": "RX_0", "set": "VOA_RX1"},
                                 "TX_Power": {"get": "P_0", "set": "VOA_P1"},
                                 "TX_Attenuation": {"get": "A_0", "set": "VOA_A1"},
                                 "RX_Work_model": {"get": "M_1", "set": "VOA_M2"},
                                 "RX_Alarm_threshold": {"get": "RX_1", "set": "VOA_RX2"},
                                 "RX_Power": {"get": "P_1", "set": "VOA_P2"},
                                 "RX_Attenuation": {"get": "A_1", "set": "VOA_A2"}},
                       "dispersion_automatic_nmu": {"Adjust_state": {"get": "START", "set": "START"},
                                                    "Cable_length": {"get": "CableL", "set": "CableL"},
                                                    "Fixed_the_dispersion": {"get": "DCM", "set": "DCM"},
                                                    "Dispersion_scope": {"get": "Range", "set": "Range"},
                                                    "Dispersion_slot": {"get": "DCMS", "set": "DCMS"},
                                                    "Optical_module": {"get": "LocationM", "set": "LocationM"},
                                                    "Remote_optical_module": {"get": "remoteModule",
                                                                              "set": "remoteModule"},
                                                    "Cable_type": {"get": "CableType", "set": "CableType"},
                                                    "Remote_IPV4": {"get": "remoteIP", "set": "remoteIP"}},
                       "dispersion_automatic_800g": {"Control_mode": {"get": "TXC#", "set": "M#_TXC"}},
                       "dispersion_manual": {"Dynamic_compensation": {"get": "GDS", "set": "SDS"},
                                             "Frequency_interval": {"get": "GCH", "set": "SCH"}}}

DCP920_SLOT_INDEX_MAPPING = {"line": {"1": 6, "2": 14, "3": 4, "4": 12, "5": 2, "6": 10, "7": 0, "8": 8},
                             "client": {"1": 7, "2": 15, "3": 5, "4": 13, "5": 3, "6": 11, "7": 1, "8": 9}}

DCP920_NMU_MAPPING = {"70": "5", "69": "4", "71": "3", "55": "8"}


def beat_sync_dcp920_device_info_all():
    LOG.info('Start sync all dcp920 device info.')

    # 获取所有dcp设备，series为1
    db_session = invent_db.get_session()
    with db_session.begin():
        device_info_list = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.series == 1).all()
        for device_info in device_info_list:
            result, NMU = get_info_nmu_by_ip(device_info.ip)
            dcpTempData = OtnTempData(id=device_info.id, ip=device_info.ip, nmu=json.dumps(NMU),
                                      data=json.dumps(result))
            db_session.merge(dcpTempData)

    LOG.info('End sync all dcp920 device info.')


def beat_sync_dcp920_device_info_single(id, ip):
    result, dcpDeviceBasic = get_info_nmu(id, ip)
    dcpData = result[0]
    NMU = result[1]
    if dcpData is None:
        return None
    print("cron task result:" + str(dcpData))
    db_session = invent_db.get_session()
    with db_session.begin():
        dcpTempData = OtnTempData(id=dcpDeviceBasic.id, ip=dcpDeviceBasic.ip, nmu=json.dumps(NMU),
                                  data=json.dumps(dcpData))
        db_session.merge(dcpTempData)
    return dcpData


def get_info_nmu(id, ip=None):
    db_session = invent_db.get_session()
    if ip is not None:
        print("device ip:" + ip)
        dcpDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
        if dcpDeviceBasic is None:
            return [None, None], None
        return get_info_nmu_by_ip(ip), dcpDeviceBasic

    print("device id:" + id)
    dcpDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.id == id).first()
    if dcpDeviceBasic is None:
        return [None, None], None

    print(str(dcpDeviceBasic))
    ip = dcpDeviceBasic.ip
    return get_info_nmu_by_ip(ip), dcpDeviceBasic


def get_info_nmu_by_ip(ip):
    # 获取锁并查询设备基本信息
    startTime = time.time()
    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        try:
            print("get lock total time:" + str(time.time() - startTime))
            socket_client = SocketClient(ip)
            recData, NMU = get_all_board_info(socket_client)
            # print(recData)
            print("get data total time:" + str(time.time() - startTime))
            if recData["business_info"] != {}:
                update_reachable_status(ip, 1)
            return recData, NMU
        except socket.timeout:
            print("socket timed out!")
            traceback.print_exc()
        except Exception as e:
            traceback.print_exc()
        finally:
            distributed_lock.release()
            print("end work, release lock!")
    else:
        distributed_lock.release_all()
        print("get lock fail!")
    update_reachable_status(ip, 0)
    return "", ""


def update_reachable_status(ip, status):
    db_session = invent_db.get_session()
    # dcp920系列值为1
    db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).update(
        {OtnDeviceBasic.reachable_status: status, OtnDeviceBasic.series: 1})


# 查询数据指令
def get_all_board_info(socket_client):
    socket_client.result = {"business_info": {}}
    strSend = f"<C00_[PSWD_{socket_client.password}][CS_?][IP_?][DTP_?][PWR_?][CPUSTS_?][CPUUSE_?]\
    [CPULIM_?][MEMSTS_?][MEMUSE_?][MEMLIM_?][NTPCLIENT_?][TIMEZONE_?][NTPSTOP_?][PWM_?][MODE_?][DBMBACK_?]\
    [DBMSAVE_?][DBMROW_?][TIME_?]>"
    str_back = socket_client.send_command(strSend)

    if len(str_back) > 0:
        index = 0
        fromIndex = 0
        while index < len(str_back):
            if str_back[index] == '[':
                fromIndex = index
            if str_back[index] == ']':
                toIndex = index
                strData = str_back[fromIndex + 1: toIndex]
                exOrderNMU(socket_client, strData)
            index += 1

    return socket_client.result, socket_client.NMU


# 指令解析
def exOrderNMU(socket_client, str_order):
    result = socket_client.result
    OrderArray = str_order.split('_')
    orderKey = OrderArray[0]
    orderValue = OrderArray[1]
    if orderKey == 'CS':
        HasInfo = ""
        if len(OrderArray) > 2:
            HasInfo = OrderArray[2]
        index = 3
        while index < len(OrderArray) and index - 3 < len(HasInfo):
            if HasInfo[index - 3] == '1':
                strTemp = OrderArray[index]
                if strTemp != '0000':
                    updateboard(socket_client, index - 2, strTemp)
            index += 1
    # 设备基本信息解析
    elif orderKey == 'IP':
        result['ipv4'] = orderValue
    elif orderKey == 'DTP':
        result['model'] = orderValue
    elif orderKey == 'PWR':
        result['power'] = orderValue
    elif orderKey == 'CPUSTS':
        result['cpu_state'] = orderValue
    elif orderKey == 'CPUUSE':
        result['cpu_used'] = orderValue
    elif orderKey == 'CPULIM':
        result['cpu_overload_threshold'] = orderValue
    elif orderKey == 'MEMSTS':
        result['memory_state'] = orderValue
    elif orderKey == 'MEMUSE':
        result['memory_used'] = orderValue
    elif orderKey == 'MEMLIM':
        result['memory_overload_threshold'] = orderValue
    elif orderKey == 'NTPCLIENT':
        tempArray = orderValue.split('-')
        result['ntp_server_address'] = tempArray[0]
        result['time_synchronization_interval'] = tempArray[1]
    elif orderKey == 'TIMEZONE':
        result['device_timezone'] = orderValue
    elif orderKey == 'NTPSTOP':
        result['ntp_state'] = orderValue
    elif orderKey == 'PWM':
        result['fan_gear'] = orderValue
    elif orderKey == 'MODE':
        result['fan_mode'] = orderValue
    elif orderKey == 'DBMBACK':
        result['backup_interval'] = orderValue
    elif orderKey == 'DBMSAVE':
        result['record_interval'] = orderValue
    elif orderKey == 'DBMROW':
        result['record_qty'] = orderValue
    elif orderKey == 'TIME':
        result['clock'] = orderValue
    else:
        print('ExOrderNMU key ' + orderKey + ' can not analysis!!!')


def showField(OrderArray):
    print(OrderArray[0] + ' is:' + OrderArray[1])


def updateboard(socket_client, slot_index, BoardType):
    boardType = BoardType[0: 2]
    socket_client.NMU[str(slot_index)] = boardType
    if boardType == '03':
        if BoardType[2: 2] == '03':
            get_info_double_edfa(socket_client, slot_index)
    # VOA
    elif boardType == '16':
        get_info4_voa(socket_client, slot_index)
    # TDCM
    elif boardType == '21':
        getInfoTDCM(socket_client, slot_index)
    # 1500 400G
    elif boardType == '53':
        getInfo1500P400G(socket_client, slot_index)
    # 风扇
    elif boardType == '55':
        getInfoFAN(socket_client, slot_index)
    # 1500 800G
    elif boardType == '69':
        getInfo1500P800G(socket_client, slot_index)
    # EDFA_VOA
    elif boardType == '70':
        getInfoEDFA_VOA(socket_client, slot_index)
    # TDCM_OLP
    elif boardType == '71':
        getInfoTDCM_OLP(socket_client, slot_index)
    else:
        print('BoardType can not analysis!!!')


def getInfoTDCM_OLP(socket_client, slot_index):
    strSend = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][STA_?]>"
    str_back = socket_client.send_command(strSend)
    if len(str_back) > 0 and str_back != f"<C{str(slot_index).zfill(2)}_[PSWD_ERROR]>":
        dictionary = initial_analysis(str_back)
        temp_dictionary = {}
        # 光色散，状态[STS_？动态补偿值[SDS_？[GDS_？[GSD_？
        temp_dictionary["ModuleStatus"] = dictionary["STS"][0:1]
        temp_dictionary["DynamicCompensationValue"] = dictionary["GDS"]
        temp_dictionary["ModuleTemp"] = dictionary["TMP"]
        temp_dictionary["FrequencyInterval"] = "100" if dictionary["GCH"] == "196000" else "50"
        socket_client.result["business_info"]["The-dispersion-information"] = temp_dictionary


def exOrderTDCM_OLP(slot_index, str_order):
    pass


def getInfoEDFA_VOA(socket_client, slot_index):
    strSend = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][STA_?]>"
    str_back = socket_client.send_command(strSend)
    if len(str_back) > 0 and str_back != f"<C{str(slot_index).zfill(2)}_[PSWD_ERROR]>":
        dictionary = initial_analysis_ext(str_back)
        # 光功率 ["PIE"] [PIA_？  ["POE"][POA_？
        temp_dictionary_tx = {"InputPower": dictionary["PWI"][0], "LowInputPower": dictionary["PIA"][0],
                              "OutputPower": dictionary["PWO"][0], "LowOutputPower": dictionary["POA"][0],
                              "WorkModel": dictionary["M"][0][0], "isInputAlarm": dictionary["PIN"][0],
                              "isOutputAlarm": dictionary["POU"][0]}
        socket_client.result["business_info"]["TX-Directional-power"] = temp_dictionary_tx
        temp_dictionary_rx = {"InputPower": dictionary["PWI"][1], "LowInputPower": dictionary["PIA"][1],
                              "OutputPower": dictionary["PWO"][1], "LowOutputPower": dictionary["POA"][1],
                              "WorkModel": dictionary["M"][0][1], "isInputAlarm": dictionary["PIN"][1],
                              "isOutputAlarm": dictionary["POU"][1]}
        socket_client.result["business_info"]["RX-Directional-power"] = temp_dictionary_rx

        # 光衰减 功率？ [B_  、衰减[V1_？（[A_、[C_）？？
        temp_dictionary_tx = {"WorkModel": dictionary["M"][1][0], "Power": dictionary["B"][1][0],
                              "Attenuation": dictionary["A"][0], "AlarmThreshold": dictionary["RX"][0],
                              "ModelWaveLen": "1310" if dictionary["W"][0] == "0" else "1550",
                              "Alarm": dictionary["ALARM"][0]}
        socket_client.result["business_info"]["TX-Directional-attenuation"] = temp_dictionary_tx
        temp_dictionary_rx = {"WorkModel": dictionary["M"][1][1], "Power": dictionary["B"][1][1],
                              "Attenuation": dictionary["A"][1], "AlarmThreshold": dictionary["RX"][1],
                              "ModelWaveLen": "1310" if dictionary["W"][1] == "0" else "1550",
                              "Alarm": dictionary["ALARM"][1]}
        socket_client.result["business_info"]["RX-Directional-attenuation"] = temp_dictionary_rx


def exOrderEDFA_VOA(slot_index, str_order):
    pass


def getInfo1500P800G(socket_client, slot_index):
    strSend = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][STA_?]>"
    str_back = socket_client.send_command(strSend)
    result = socket_client.result
    if len(str_back) > 0 and str_back != f"<C{str(slot_index).zfill(2)}_[PSWD_ERROR]>":
        dictionary = initial_analysis_ext(str_back)
        # 端口信息
        result["interface_status"] = ''.join(dictionary["S"])
        result["business_info"]["client"] = []
        result["business_info"]["line"] = []
        for i in range(1, 9):
            # 客户侧
            c_i = DCP920_SLOT_INDEX_MAPPING["client"][str(i)]
            if result["interface_status"][c_i] == '0':
                result["business_info"]["client"].append({"port": "C" + str(i)
                                                             , "RX1": "NA"
                                                             , "RX2": "NA"
                                                             , "RX3": "NA"
                                                             , "RX4": "NA"
                                                             , "TX1": "NA"
                                                             , "TX2": "NA"
                                                             , "TX3": "NA"
                                                             , "TX4": "NA"
                                                             , "MaxWaveLength": "NA"
                                                             , "MinWaveLength": "NA"
                                                             , "Rate": "NA"
                                                             , "ModuleTemp": "NA"
                                                             , "ControlMode": "NA"
                                                             , "InputAlarmThreshold": "NA"
                                                             , "TXC": "NA"
                                                             , "RXPA": "NA"
                                                             , "TXPA": "NA"
                                                             , "TA": "NA"
                                                          })
            else:
                result["business_info"]["client"].append({"port": "C" + str(i)
                                                             , "RX1": str(dictionary["RX1"][c_i])
                                                             , "RX2": str(dictionary["RX2"][c_i])
                                                             , "RX3": str(dictionary["RX3"][c_i])
                                                             , "RX4": str(dictionary["RX4"][c_i])
                                                             , "TX1": str(dictionary["TX1"][c_i])
                                                             , "TX2": str(dictionary["TX2"][c_i])
                                                             , "TX3": str(dictionary["TX3"][c_i])
                                                             , "TX4": str(dictionary["TX4"][c_i])
                                                             , "MaxWaveLength": str(dictionary["MAX"][c_i])
                                                             , "MinWaveLength": str(dictionary["MIN"][c_i])
                                                          # 速率是读取的数据为X，计算X*4/1000得到速率,单位G/s
                                                             ,
                                                          "Rate": str(int(int(dictionary["RATE"][c_i]) * 4 / 1000))
                                                             , "ModuleTemp": str(
                        dictionary["T"][c_i]) if "T" in dictionary else "NA"
                                                             , "ControlMode": str(dictionary["TXC"][c_i])
                                                             , "InputAlarmThreshold": str(dictionary["RXA"][c_i])
                                                             , "TXC": str(dictionary["TXC"][c_i])
                                                             , "RXPA": str(dictionary["RXPA"][c_i])
                                                             , "TXPA": str(dictionary["TXPA"][c_i])
                                                             , "TA": str(dictionary["TA"][c_i])
                                                          })

            # 线路侧，客户侧是将100G信号拆分成了4路25G信号组成，线路侧是将100G拆成了两路50G信号组成
            l_i = DCP920_SLOT_INDEX_MAPPING["line"][str(i)]
            if result["interface_status"][l_i] == '0':
                result["business_info"]["line"].append({"port": "L" + str(i)
                                                           , "RX1": "NA"
                                                           , "RX2": "NA"
                                                        # , "RX3": "NA"
                                                        # , "RX4": "NA"
                                                           , "TX1": "NA"
                                                           , "TX2": "NA"
                                                        # , "TX3": "NA"
                                                        # , "TX4": "NA"
                                                           , "MaxWaveLength": "NA"
                                                           , "MinWaveLength": "NA"
                                                           , "Rate": "NA"
                                                           , "ModuleTemp": "NA"
                                                           , "ControlMode": "NA"
                                                           , "InputAlarmThreshold": "NA"
                                                           , "TXC": "NA"
                                                           , "RXPA": "NA"
                                                           , "TXPA": "NA"
                                                           , "TA": "NA"
                                                        })
            else:
                result["business_info"]["line"].append({"port": "L" + str(i)
                                                           , "RX1": str(dictionary["RX1"][l_i])
                                                           , "RX2": str(dictionary["RX2"][l_i])
                                                        # , "RX3": str(dictionary["RX3"][l_i])
                                                        # , "RX4": str(dictionary["RX4"][l_i])
                                                           , "TX1": str(dictionary["TX1"][l_i])
                                                           , "TX2": str(dictionary["TX2"][l_i])
                                                        # , "TX3": str(dictionary["TX3"][l_i])
                                                        # , "TX4": str(dictionary["TX4"][l_i])
                                                           , "MaxWaveLength": str(dictionary["MAX"][l_i])
                                                           , "MinWaveLength": str(dictionary["MIN"][l_i])
                                                        #  设备线路侧的速率无意义，是固定值103
                                                           , "Rate": "103.00"
                                                           , "ModuleTemp": str(
                        dictionary["T"][l_i]) if "T" in dictionary else "NA"
                                                           , "ControlMode": str(dictionary["TXC"][l_i])
                                                           , "InputAlarmThreshold": str(dictionary["RXA"][l_i])
                                                           , "TXC": str(dictionary["TXC"][l_i])
                                                           , "RXPA": str(dictionary["RXPA"][l_i])
                                                           , "TXPA": str(dictionary["TXPA"][l_i])
                                                           , "TA": str(dictionary["TA"][l_i])
                                                        })


def exOrder1500P800G(slot_index, str_order):
    pass


def getInfoFAN(socket_client, slot_index):
    strSend = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][STA_?]>"
    str_back = socket_client.send_command(strSend)
    strStart = 0
    result = ""
    if len(str_back) > 0 and str_back != f"<C{str(slot_index).zfill(2)}_[PSWD_ERROR]>":
        index = 0
        while index < len(str_back):
            if str_back[index] == '[':
                strStart = index
            if str_back[index] == ']':
                strEnd = index
                strData = str_back[strStart + 1: strEnd]
                OrderArray = strData.split('_')
                key = OrderArray[0]
                if key in ['FAN1', 'FAN2', 'FAN3', 'FAN4', 'FAN5', 'FAN6', 'FAN7']:
                    result += OrderArray[1]

            index += 1
    socket_client.result['fan_status'] = result


def exOrderFAN(str_order):
    OrderArray = str_order.split('_')
    key = OrderArray[0]
    if key == 'FAN1':
        showField(OrderArray)
        pass
    elif key == 'FAN2':
        showField(OrderArray)
        pass
    elif key == 'FAN3':
        showField(OrderArray)
        pass
    elif key == 'FAN4':
        showField(OrderArray)
        pass
    elif key == 'FAN5':
        showField(OrderArray)
        pass
    elif key == 'FAN6':
        showField(OrderArray)
        pass
    elif key == 'FAN7':
        showField(OrderArray)
        pass
    else:
        print('exOrderFAN key ' + key + ' is illegal!!!')


def getInfo1500P400G(socket_client, slot_index):
    strSend = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][STA_?]>"
    str_back = socket_client.send_command(strSend)
    strStart = 0
    if len(str_back) > 0 and str_back != f"<C{str(slot_index).zfill(2)}_[PSWD_ERROR]>":
        index = 0
        while index < len(str_back):
            if str_back[index] == '[':
                strStart = index
            if str_back[index] == ']':
                strEnd = index
                strData = str_back[strStart + 1: strEnd]
                exOrder1500P400G(strData)
            index += 1


def exOrder1500P400G(str_order):
    OrderArray = str_order.split('_')
    key = OrderArray[0]
    if key == 'B':
        pass
    elif key == 'SV':
        pass
    elif key == 'HV':
        pass
    elif key == 'SN':
        pass
    elif key == 'MD':
        pass
    elif key == 'DT':
        pass
    elif key == 'S':
        pass
    elif key == 'M':
        pass
    elif key == 'MAX':
        pass
    elif key == 'MIN':
        pass
    elif key == 'RATE':
        pass
    elif key == 'RX1':
        pass
    elif key == 'RX2':
        pass
    elif key == 'RX3':
        pass
    elif key == 'RX4':
        pass
    elif key == 'TX1':
        pass
    elif key == 'TX2':
        pass
    elif key == 'TX3':
        pass
    elif key == 'TX4':
        pass
    elif key == 'RXA':
        pass
    elif key == 'T':
        pass
    elif key == 'TD':
        pass
    elif key == 'TXC':
        pass
    elif key == 'RXPA':
        pass
    elif key == 'TXPA':
        pass
    elif key == 'TA':
        pass
    else:
        print('exOrder1500P400G key ' + key + ' is illegal!!!')


def getInfoTDCM(socket_client, slot_index):
    strSend = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][STA_?]>"
    str_back = socket_client.send_command(strSend)
    strStart = 0
    if len(str_back) > 0 and str_back != f"<C{str(slot_index).zfill(2)}_[PSWD_ERROR]>":
        index = 0
        while index < len(str_back):
            if str_back[index] == '[':
                strStart = index
            if str_back[index] == ']':
                strEnd = index
                strData = str_back[strStart + 1: strEnd]
                exOrderTDCM(strData)
            index += 1


def exOrderTDCM(str_order):
    OrderArray = str_order.split('_')
    key = OrderArray[0]
    if key == 'B':
        pass
    elif key == 'STS':
        pass
    elif key == 'TMP':
        pass
    elif key == 'GDS':
        pass
    elif key == 'GSD':
        pass
    elif key == 'GCH':
        pass
    elif key == 'SV':
        pass
    elif key == 'HV':
        pass
    elif key == 'SN':
        pass
    elif key == 'MD':
        pass
    elif key == 'DT':
        pass
    else:
        print('exOrderTDCM key ' + key + ' is illegal!!!')


def get_info4_voa(socket_client, slot_index):
    strSend = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][STA_?]>"
    str_back = socket_client.send_command(strSend)
    strStart = 0
    if len(str_back) > 0 and str_back != f"<C{str(slot_index).zfill(2)}_[PSWD_ERROR]>":
        index = 0
        while index < len(str_back):
            if str_back[index] == '[':
                strStart = index
            if str_back[index] == ']':
                strEnd = index
                strData = str_back[strStart + 1: strEnd]
                exOrder4VOA(strData)
            index += 1


def exOrder4VOA(str_order):
    OrderArray = str_order.split('_')
    key = OrderArray[0]
    if key == 'B':
        pass
    elif key == 'M1':
        pass
    elif key == 'W1':
        pass
    elif key == 'CV1':
        pass
    elif key == 'RX1':
        pass
    else:
        print('exOrder4VOA key ' + key + ' is illegal!!!')


def get_info_double_edfa(socket_client, slot_index):
    strSend = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][STA_?][B_?]>"
    str_back = socket_client.send_command(strSend)
    strStart = 0
    if len(str_back) > 0 and str_back != f"<C{str(slot_index).zfill(2)}_[PSWD_ERROR]>":
        index = 0
        while index < len(str_back):
            if str_back[index] == '[':
                strStart = index
            if str_back[index] == ']':
                strEnd = index
                strData = str_back[strStart + 1: strEnd]
                exOrderDEDFA(strData)
            index += 1


def exOrderDEDFA(str_order):
    OrderArray = str_order.split('_')
    key = OrderArray[0]
    if key == 'B':
        pass
    elif key == 'PWI':
        pass
    elif key == 'PWO':
        pass
    elif key == 'PIE':
        pass
    elif key == 'POE':
        pass
    elif key == 'PIA':
        pass
    elif key == 'POA':
        pass
    elif key == 'PGV':
        pass
    elif key == 'MTV':
        pass
    elif key == 'PTV':
        pass
    elif key == 'M':
        pass
    elif key == 'PSW':
        pass
    elif key == 'PPV':
        pass
    elif key == 'PIV':
        pass
    elif key == 'TEC':
        pass
    elif key == 'ILIM':
        pass
    elif key == 'PIN':
        pass
    elif key == 'POU':
        pass
    elif key == 'INLOS':
        pass
    elif key == 'MT':
        pass
    elif key == 'PT':
        pass
    elif key == 'PI':
        pass
    else:
        print('exOrderDEDFA key ' + key + ' is illegal!!!')


def get_test_result(ip):
    remote_file = '/tmp/nmu.json'
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        ssh.connect(ip, username='root', password='123456')
        stdin, stdout, stderr = ssh.exec_command(f'cat {remote_file}')
        file_content = stdout.read().decode('utf-8')
        json_data = json.loads(file_content)
        da = json_data[0]['da']
        return {
            'left_border_tdcm_value': da[68],
            'tdcm_value': da[70],
            'right_border_tdcm_value': da[72],
            'test_status': da[66]
        }, 0

    except Exception as e:
        return {
            'left_border_tdcm_value': "0.0",
            'tdcm_value': '0.0',
            'right_border_tdcm_value': '0.0',
            'test_status': '0'
        }, 1
    finally:
        ssh.close()


def get_config(ip, config_type):
    recData = {}
    nmu = get_nmu(ip)
    startTime = time.time()
    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        try:
            print("get lock total time:" + str(time.time() - startTime))
            socket_client = SocketClient(ip)
            if config_type == 'power':
                recData = get_power_config(socket_client, nmu)
            elif config_type == 'dispersion_automatic':
                recData = get_dispersion_automatic_config(socket_client, nmu)
            elif config_type == 'dispersion_manual':
                recData = get_dispersion_manual_config(socket_client, nmu)
            else:
                return recData, 1
            print(recData)
            print("get config total time:" + str(time.time() - startTime))
        except socket.timeout:
            print("socket timed out!")
            traceback.print_exc()
            return recData, 1
        except Exception as e:
            traceback.print_exc()
            return recData, 1
        finally:
            distributed_lock.release()
            print("end get config, release lock!")
    else:
        distributed_lock.release_all()
    return recData, 0


def modify_config(ip, config_type, key, value):
    recData = {}
    nmu = get_nmu(ip)
    startTime = time.time()
    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        try:
            print("get lock total time:" + str(time.time() - startTime))
            socket_client = SocketClient(ip)
            if config_type == 'power':
                recData = modify_power_config(socket_client, nmu, key, value)
            elif config_type == 'dispersion_automatic':
                recData = modify_dispersion_automatic_config(socket_client, nmu, key, value)
            elif config_type == 'dispersion_manual':
                recData = modify_dispersion_manual_config(socket_client, nmu, key, value)
            else:
                return recData, 1
            print(recData)
            print("set config total time:" + str(time.time() - startTime))
        except socket.timeout:
            print("socket timed out!")
            traceback.print_exc()
            return recData, 1
        except Exception as e:
            traceback.print_exc()
            return recData, 1
        finally:
            distributed_lock.release()
            print("end modify config, release lock!")
    else:
        distributed_lock.release_all()
    return recData, 0


def get_nmu(ip):
    db_session = invent_db.get_session()
    data = db_session.query(OtnTempData).filter(OtnTempData.ip == ip).first()
    return json.loads(data.nmu) if data else {}


def get_slot(nmu, index):
    for key, value in nmu.items():
        print(key, value)
        if value == index:
            return key.zfill(2)
    # dcp设备默认的板卡槽位号
    slot = DCP920_NMU_MAPPING.get(index)
    if slot is not None:
        return slot.zfill(2)
    raise Exception("get slot failed")


def modify_power_config(socket_client, nmu, key, value):
    # 要根据nmu处获取EDFA_VOA-70所对应的槽位号拼接指令得到功率数据
    slot = get_slot(nmu, "70")
    command = make_config_command("power", key, value,
                                  f"<C{slot}_[PSWD_{socket_client.password}][%s_%s]>")
    print("modify_power_config command:%s" % command)
    str_back = socket_client.send_command(command)
    print("config result:%s" % str_back)
    if len(str_back) > 0 and "_ERR" not in str_back:
        return str_back
    return ''


def modify_dispersion_automatic_config(socket_client, nmu, key, value):
    if "Control_mode" in key:
        # 要根据nmu处获取1500 800G-69所对应的槽位号拼接指令得到发光控制数据
        slot = get_slot(nmu, "69")
        command = make_config_command("dispersion_automatic_800g", key, value,
                                      f"<C{slot}_[PSWD_{socket_client.password}][%s_%s]>")
    else:
        command = make_config_command("dispersion_automatic_nmu", key, value,
                                      f"<C00_[PSWD_{socket_client.password}][%s_%s]>")
    print("modify_dispersion_automatic_config command:%s" % command)
    str_back = socket_client.send_command(command)
    print("config result:%s" % str_back)
    if len(str_back) > 0 and "_ERR" not in str_back:
        return str_back
    return ''


def modify_dispersion_manual_config(socket_client, nmu, key, value):
    # 要根据nmu处获取TDCM_OLP-71所对应的槽位号拼接指令得到动态补偿值数据
    slot = get_slot(nmu, "71")
    command = make_config_command("dispersion_manual", key, value,
                                  f"<C{slot}_[PSWD_{socket_client.password}][%s_%s]>")
    print("modify_dispersion_manual_config command:%s" % command)
    str_back = socket_client.send_command(command)
    print("config result:%s" % str_back)
    if len(str_back) > 0 and "_ERR" not in str_back:
        return str_back
    return ''


def make_config_command(config_type, key, value, base_command):
    control_replace = 0
    # 跟槽位号有关的配置解析
    if "@" in key:
        arrays = key.split("@")
        key = arrays[0]
        control_replace = DCP920_SLOT_INDEX_MAPPING[arrays[1]][arrays[2]] + 1
    config_model_info = DCP920_CONFIG_MODEL.get(config_type)
    result = None
    if config_model_info is None or config_model_info.get(key) is None:
        return result
    command_key = config_model_info.get(key).get("set")
    if "#" not in command_key:
        return base_command % (command_key, value)
    else:
        return base_command % (command_key.replace("#", str(control_replace)), value)


def get_power_config(socket_client, nmu):
    # 要根据nmu处获取EDFA_VOA-70所对应的槽位号拼接指令得到功率数据
    slot = get_slot(nmu, "70")
    strSend = f"<C{slot}_[PSWD_{socket_client.password}][STA_?]>"
    str_back = socket_client.send_command(strSend)
    result = {}
    if len(str_back) > 0 and "_ERR" not in str_back:
        dictionary = initial_analysis_ext(str_back)
        result = analyze_config_result(dictionary, "power")
    return result


def get_dispersion_automatic_config(socket_client, nmu):
    strSend = f"<C00_[PSWD_{socket_client.password}][START_?][CableL_?][DCM_?][Range_?]\
    [DCMS_?][LocationM_?][remoteModule_?][CableType_?][remoteIP_?]>"
    str_back = socket_client.send_command(strSend)
    result_nmu = {}
    if len(str_back) > 0 and "_ERR" not in str_back:
        dictionary = initial_analysis_ext(str_back)
        result_nmu = analyze_config_result(dictionary, "dispersion_automatic_nmu")

    # 要根据nmu处获取1500 800G-69所对应的槽位号拼接指令得到发光控制数据
    slot = get_slot(nmu, "69")
    strSend = f"<C{slot}_[PSWD_{socket_client.password}][STA_?]>"
    str_back = socket_client.send_command(strSend)
    result_800g = {}
    if len(str_back) > 0 and "_ERR" not in str_back:
        dictionary = initial_analysis_ext(str_back)
        result_800g = analyze_config_result(dictionary, "dispersion_automatic_800g")

    result = {**result_nmu, **result_800g}

    return result


def get_dispersion_manual_config(socket_client, nmu):
    # 要根据nmu处获取TDCM_OLP-71所对应的槽位号拼接指令得到动态补偿值数据
    slot = get_slot(nmu, "71")
    strSend = f"<C{slot}_[PSWD_{socket_client.password}][STA_?]>"
    str_back = socket_client.send_command(strSend)
    result = {}
    if len(str_back) > 0 and "_ERR" not in str_back:
        dictionary = initial_analysis_ext(str_back)
        result = analyze_config_result(dictionary, "dispersion_manual")
    return result


def analyze_config_result(dictionary, config_type):
    config_model_info = DCP920_CONFIG_MODEL.get(config_type)
    result = {}
    if config_model_info is None:
        return result
    for key, value in config_model_info.items():
        get_command = value["get"]
        if "#" in get_command:
            get_command_param = get_command.split("#")
            # 目前只有端口的发光控制需要特殊处理
            interface_status = dictionary["S"]
            result[key] = get_control_mode(dictionary[get_command_param[0]], interface_status)
        elif "_" in get_command:
            get_command_param = get_command.split("_")
            # 工作模式配置取第二个M数据
            if get_command_param[0] == "M":
                result[key] = dictionary[get_command_param[0]][1][int(get_command_param[1])]
            else:
                result[key] = dictionary[get_command_param[0]][int(get_command_param[1])]
        else:
            result[key] = dictionary[get_command][0]
    return result


def get_control_mode(data, interface_status):
    result = {}
    for key, value in DCP920_SLOT_INDEX_MAPPING.items():
        str = ""
        for Ikey, Ivalue in value.items():
            # 端口状态为0时，发光控制无法设置，状态为未知:3
            if interface_status[Ivalue] == "0":
                str += "3"
            else:
                str += data[Ivalue]
        result[key] = str
    return result


def initial_analysis(str_back):
    result = {}
    index = 0
    strStart = 0
    while index < len(str_back):
        if str_back[index] == '[':
            strStart = index
        if str_back[index] == ']':
            strEnd = index
            strData = str_back[strStart + 1: strEnd]
            OrderArray = strData.split('_')
            if len(OrderArray) == 1:
                index += 1
                continue
            result[OrderArray[0]] = OrderArray[1]
        index += 1
    return result


def initial_analysis_ext(str_back):
    result = {}
    index = 0
    strStart = 0
    while index < len(str_back):
        if str_back[index] == '[':
            strStart = index
        if str_back[index] == ']':
            strEnd = index
            strData = str_back[strStart + 1: strEnd]
            OrderArray = strData.split('_')
            temp = []
            for i in range(1, len(OrderArray)):
                temp.append(OrderArray[i])
            if OrderArray[0] in result:
                array = [result[OrderArray[0]], temp]
                result[OrderArray[0]] = array
            else:
                result[OrderArray[0]] = temp
        index += 1
    return result
