import json
import time
import logging
from typing import List, Dict, Any
from contextlib import contextmanager

from server.db.redis_common import RedisSessionFactory, RedisQueue
from server.db.models.monitor import monitor_db
from server.util.utils import email_handler

LOG = logging.getLogger(__name__)

class AlertDBProcessor:
    def __init__(self):
        # Initialize configuration
        self.running = True
        self.redis_queue = RedisQueue('alert_queue')
        self.batch_size = 10  # Batch processing size
        self.max_retries = 3  # Maximum retry attempts
        self.retry_delay = 1  # Retry delay in seconds
        self.poll_interval = 1  # 轮询间隔

    def stop(self):
        """Stop the processor gracefully"""
        LOG.info("Stopping processor...")
        self.running = False

    @contextmanager
    def _get_db_session(self):
        """数据库会话上下文管理器"""
        session = monitor_db.get_session()
        try:
            yield session
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    def _process_single_alert(self, alert: Dict[str, Any], db_session) -> bool:
        """Process single alert"""
        try:
            monitor_db.add_event(
                alert["labels"]["target"],
                alert["labels"]["severity"],
                alert["annotations"]["description"],
                db_session
            )
            email_handler(alert, db_session)
            return True
        except Exception as e:
            LOG.error(f"Failed to process alert: {e}")
            return False

    def _process_batch(self, alerts: List[Dict[str, Any]], db_session) -> None:
        """Process batch of alerts"""
        for alert in alerts:
            for retry in range(self.max_retries):
                try:
                    if self._process_single_alert(alert, db_session):
                        break
                    if retry < self.max_retries - 1:
                        time.sleep(self.retry_delay * (retry + 1))  # 指数退避
                except Exception as e:
                    LOG.error(f"Error processing alert (attempt {retry + 1}): {e}")
                    db_session.rollback()
                    if retry < self.max_retries - 1:
                        time.sleep(self.retry_delay * (retry + 1))

    def run(self):
        """Main processing loop"""
        LOG.info("Alert processor started")
        
        while self.running:
            try:
                # 使用阻塞方式获取告警
                alerts = self.redis_queue.get_many(
                    self.batch_size, 
                    block=True, 
                    timeout=self.poll_interval
                )
                
                if alerts:
                    with self._get_db_session() as db_session:
                        LOG.info(f"Processing {len(alerts)} alerts")
                        self._process_batch(alerts, db_session)
                
            except Exception as e:
                LOG.error(f"Error in main processing loop: {e}")
                time.sleep(self.poll_interval)

        LOG.info("Alert processor stopped")

def alert_db_processor():
    """Start the alert processor"""
    processor = AlertDBProcessor()
    processor.run()
