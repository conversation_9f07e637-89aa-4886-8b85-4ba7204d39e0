from socket import socket, AF_INET, SOCK_STREAM, error
import time
import logging
import traceback

LOG = logging.getLogger(__name__)


class SocketClient:

    def __init__(self, host, password="G2zSEC7mKnU=", port=4001, buf_size=51200, time_out=3, model=None):
        self.ADDR = (host, port)
        self.password = password
        self.buf_size = buf_size
        self.time_out = time_out
        self.result = {}
        self.NMU = {}
        self.socket_client = None
        self.model = model

    def __del__(self):
        pass

    def send_command(self, command):
        if not command:
            return ''
        for index in range(5):
            try:
                # 创建套接字
                with socket(AF_INET, SOCK_STREAM) as client_socket:
                    client_socket.settimeout(self.time_out)

                    # 连接到目标地址和端口
                    client_socket.connect(self.ADDR)
                    print(f"have connect to {self.ADDR}")

                    # 发送消息
                    commandStr = command.encode('utf-8')
                    print(f"Send data>>>>----{commandStr}")
                    client_socket.sendall(commandStr)  # 发送消息
                    recData = b''
                    # 循环接收数据直到服务器关闭连接或者接收到指定长度的数据
                    while True:
                        data = client_socket.recv(self.buf_size)
                        if not data:
                            # 如果接收到的数据长度为0，说明服务器已经关闭了连接
                            break
                        recData += data
                        if recData.decode('utf-8', errors='ignore').endswith('>'):
                            break

                    print('Receive data<<<<----' + recData.decode('utf-8', errors='ignore'))
                    if not recData:
                        return ''
                    return recData.decode('utf-8', errors='ignore')

            except ConnectionRefusedError as e:
                print(f"Connection failed, target server not started or connection denied:{e}")
                # LOG.exception(traceback.format_exc())
                time.sleep(0.5)
            except error as e:
                print(f"An error occurred while sending the message:{e}")
                # LOG.exception(traceback.format_exc())
                time.sleep(0.5)
            finally:
                print("Connection close")
                if client_socket is not None:
                    client_socket.close()

        return ''

    def create_socket_client(self):
        startTime = time.time()
        for index in range(5):
            try:
                # 创建套接字
                self.socket_client = socket(AF_INET, SOCK_STREAM)
                self.socket_client.settimeout(self.time_out)

                # 连接到目标地址和端口
                self.socket_client.connect(self.ADDR)
                print(f"have connect to {self.ADDR}")
                print("create socket client total time:" + str(time.time() - startTime))
                return
            except ConnectionRefusedError as e:
                print(f"Connection failed, target server not started or connection denied:{e}")
                # LOG.exception(traceback.format_exc())
                time.sleep(0.5)
            except error as e:
                print(f"An error occurred while create connect:{e}")
                # LOG.exception(traceback.format_exc())
                time.sleep(0.5)
            finally:
                print("create socket client end.")

    def send_command_by_long_connection(self, command):
        startTime = time.time()
        try:
            # 发送消息
            commandStr = command.encode('utf-8')
            print(f"Send data>>>>----{commandStr}")
            self.socket_client.sendall(commandStr)  # 发送消息
            recData = b''
            # 循环接收数据直到服务器关闭连接或者接收到指定长度的数据
            while True:
                data = self.socket_client.recv(self.buf_size)
                if not data:
                    # 如果接收到的数据长度为0，说明服务器已经关闭了连接
                    break
                recData += data
                resultTemp = recData.decode('utf-8', errors='ignore').strip().rstrip('\0')
                if resultTemp.endswith('>'):
                    break

            print(f"Receive data<<<<----==={recData.decode('utf-8', errors='ignore')}===")
            print("send command total time:" + str(time.time() - startTime))
            if not recData:
                return ''
            return recData.decode('utf-8', errors='ignore')
        except Exception as e:
            print(f"An error occurred while sending the message:{e}")
            traceback.print_exc()
            # LOG.exception(traceback.format_exc())
        finally:
            print("send command end.")
        print("send command total time:" + str(time.time() - startTime))
        return ''

    def close_socket_client(self):
        if self.socket_client is not None:
            self.socket_client.close()
            print("Connection close")
