# -*- coding: utf-8 -*-

from sqlalchemy import (
    Column,
    Integer,
    String,
    ForeignKey,
    DateTime
)
from sqlalchemy.orm import relationship

from server.db.models.base import Base


class DeviceImage(Base):
    def __init__(self):
        pass

    __tablename__ = 'device_image_info'
    id = Column(Integer, primary_key=True)
    image_name = Column(String(255), nullable=True)
    image_path = Column(String(255), nullable=True)
    image_md5_path = Column(String(255), nullable=True)
    model = Column(String(32), nullable=True)
    version = Column(String(32), nullable=True)
    revision = Column(String(32), nullable=True)


class DeviceuUgradeOperationLog(Base):
    __tablename__ = 'device_upgrade_operation_log'
    id = Column(Integer, primary_key=True)
    sn = Column(String(64), index=True)
    log_info = Column(String(128))


class DeviceLatestUpgradeStatus(Base):
    __tablename__ = 'device_latest_upgrade_status'
    id = Column(Integer, primary_key=True)
    sn = Column(String(64), unique=True)
    upgrade_type = Column(Integer, default=0)
    upgrade_job_name = Column(String(64), nullable=True)
    image_id = Column(Integer, ForeignKey('device_image_info.id', ondelete='SET NULL'), nullable=True)
    upgrade_time = Column(DateTime, nullable=True)
    upgrade_status = Column(Integer, default=0)
    image = relationship('DeviceImage')
