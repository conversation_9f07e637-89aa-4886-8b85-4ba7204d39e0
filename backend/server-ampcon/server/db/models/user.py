# -*- coding: utf-8 -*-
import copy
import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import collate

from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    Enum,
    Boolean,
    ForeignKey,
)
from sqlalchemy.orm import relationship, exc

from server.db.db_common import DBCommon
from server.db.models.base import Base
from server.db.models import inventory


class User(Base):
    __tablename__ = 'user'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(32), index=True, unique=True, nullable=False)
    passwd = Column(String(256), nullable=False)
    ctime = Column(DateTime, default=datetime.datetime.now)
    type = Column(Enum('superadmin', 'admin', 'superuser', 'readonly'), default='readonly')
    email = Column(String(128))
    user_type = Column(Enum('global', 'group'), default='global')
    is_lock = Column(Integer, default=0, nullable=False)

    @property
    def password(self):
        raise AttributeError("user password cannot be read")

    @password.setter
    def password(self, password: str):
        self.passwd = generate_password_hash(password)

    def check_password_hash(self, password):
        return check_password_hash(self.passwd, password)


class TacacsConfig(Base):
    __tablename__ = 'tacacs_config'
    id = Column(Integer, autoincrement=True, primary_key=True)
    enable = Column(Boolean, default=False)
    server_host = Column(String(256))
    server_host_ii = Column(String(256))
    server_secret = Column(String(32))
    session_timeout = Column(Integer, default=5)
    # ASCII: 1, PAP:2, CHAP:3
    auth_protocol = Column(Integer, default=1)
    user_mapping = Column(String(512), default='')


class UserDb(DBCommon):
    def create_default_user_if_no_exit(self):
        session = self.get_session()
        with session.begin(subtransactions=True):
            users = session.query(User).all()
            if not users:
                user = User(name='admin', passwd='pica8', type='superuser')
                session.add(user)
    
    def query_user(self, user):
        session = self.get_session()
        users_res = session.query(User).filter(collate(User.name, "utf8_bin") == user).first()
        return users_res
    
    def update_user_status(self, user, lock_status):
        session = self.get_session()
        session.query(User).filter(User.name == user).update({User.is_lock: lock_status}, synchronize_session=False)
    
    def create_tacacs_config_if_not_exit(self):
        session = self.get_session()
        with session.begin(subtransactions=True):
            tacacs_config = session.query(TacacsConfig).all()
            if not tacacs_config:
                tmp_tacacs_config = TacacsConfig(enable=False, server_host='', server_secret='', session_timeout=5,
                                                 auth_protocol=1)
                session.add(tmp_tacacs_config)
    
    def get_tacacs_config(self):
        session = self.get_session()
        tacacs_config = session.query(TacacsConfig).first()
        return tacacs_config

    def update_user_group_mapping(self, user_id, group_id_list):
        # delete mapping records
        self.get_session().query(inventory.UserGroupMapping).filter(inventory.UserGroupMapping.user_id==user_id).delete()
        # add new mapping records
        user_group_mapping_add_list = []
        for group_id in group_id_list:
            user_group_mapping_add_list.append(inventory.UserGroupMapping(user_id=user_id, group_id=group_id))
        self.get_session().add_all(user_group_mapping_add_list)

    def update_user(self, filters, updates):
        group_id_list = copy.deepcopy(updates['group_id_list'])
        del(updates['group_id_list'])
        self.update_model(User, filters, updates)
        user_id = self.query_user(updates['name']).id
        self.update_user_group_mapping(user_id, group_id_list)

    def delete_user(self, username):
        user = self.query_user(username)
        if user:
            user_db.delete_collection(inventory.UserGroupMapping, filters={'user_id': [user.id]})
        user_db.delete_collection(User, filters={'name': [username]})


user_db = UserDb()
