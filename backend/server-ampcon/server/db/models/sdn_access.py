# -*- coding: utf-8 -*-
from sqlalchemy import (
    Column,
    Integer,
    String,
    ForeignKey,
    Boolean,
    Enum,
    BigInteger,
    func
)

from server.db.db_common import DBCommon
from server.db.models.base import Base
from server.db.models import inventory


class HostAccessControl(Base):
    __tablename__ = 'host_access_control'
    id = Column(Integer, primary_key=True, autoincrement=True)
    smac = Column(String(64), unique=True, nullable=False)
    sip = Column(String(64))
    # usually, each host mac has a host name when import. If the mac learned from switch, it will be "learned"
    source_host_name = Column(String(64))
    # usually, one mac will only existed in one switch. We need consider how to process when L2 loop happend
    located_switch = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'))
    # if False, means the mac is just learn from switch, True, means the host is configured by app
    processed_host = Column(Boolean, default=False)
    online = Column(Boolean, default=False)
    # action, if 0 means drop, -1 Unknown, just forwarding, .1-4094. means will change to configured VLAN
    assigned_vlan_id = Column(Integer)
    port_num = Column(String(64))
    map_queue = Column(Enum('0', '1', '2', '3', '4', '5', '6', '7'), default='0')
    counter_pkt = Column(BigInteger, default=0)
    counter_byte = Column(BigInteger, default=0)
    # the meter is kbps
    bandwidth = Column(BigInteger)


class SdnAccessSwitch(Base):
    __tablename__ = 'switch_configured'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'))


class SdnAccessPolicy(Base):
    __tablename__ = 'sdn_access_switch_policy'
    id = Column(Integer, primary_key=True, autoincrement=True)
    flow_name = Column(String(128), unique=True)
    flow_string = Column(String(128))
    flow_action = Column(Enum('drop', 'forward'), default='forward')
    priority = Column(Integer)
    map_queue = Column(Enum('0', '1', '2', '3', '4', '5', '6', '7'), default='0')
    counter = Column(BigInteger,default=0)
    # the meter is kbps
    meter = Column(BigInteger)
    cookie = Column(String(128))


class SDNAccessDB(DBCommon):

    def get_vlan_statics(self, session=None):
        session = session or self.get_session()
        statics = session.query(HostAccessControl.assigned_vlan_id,
                                func.count(HostAccessControl.smac).label('count'))\
            .group_by(HostAccessControl.assigned_vlan_id).all()
        return statics

    def get_switches(self, session=None):
        session = session or self.get_session()
        switches = session.query(SdnAccessSwitch.sn.label('sn'), inventory.Switch.mgt_ip).\
            outerjoin(inventory.Switch, inventory.Switch.sn == SdnAccessSwitch.sn).\
            filter(SdnAccessSwitch.sn is not None).all()
        return switches

    def add_mac_host_control(self, sn, mac, vlan, ifname):
        host_control = HostAccessControl(located_switch=sn, smac=mac, assigned_vlan_id=vlan, port_num=ifname)
        self.insert_or_update(host_control, primary_key='smac')

    def get_switch_mac_ls(self, sn, session=None):
        session = session or self.get_session()
        reses = session.query(HostAccessControl.id, HostAccessControl.smac, HostAccessControl.located_switch,
                              HostAccessControl.assigned_vlan_id, HostAccessControl.port_num,
                              HostAccessControl.online, HostAccessControl.map_queue).\
            filter(HostAccessControl.located_switch == sn).all()
        mac_vlans = set([(res.smac, res.located_switch, res.assigned_vlan_id, res.port_num, res.online) for res in reses])
        macs = set([res.smac for res in reses])
        mac_ids = dict((res.smac, (res.id, res.located_switch, res.assigned_vlan_id, res.map_queue)) for res in reses)
        return mac_vlans, macs, mac_ids

    def bulk_insert_mac_l(self, mac_ls, session=None):
        session = session or self.get_session()
        session.bulk_insert_mappings(HostAccessControl, mac_ls)

    def bulk_update_mac_l(self, mac_ls, session=None):
        session = session or self.get_session()
        session.bulk_update_mappings(HostAccessControl, mac_ls)

    def get_host_vlans(self, session=None):
        session = session or self.get_session()
        reses = session.query(HostAccessControl.assigned_vlan_id).distinct().all()
        return [res.assigned_vlan_id for res in reses]

    def del_no_use_macs(self, macs, session=None):
        session = session or self.get_session()
        base_query = session.query(HostAccessControl).filter(HostAccessControl.processed_host == False)

        max_reqs = 1000
        length = len(macs)
        if length < 1000:
            base_query.filter(HostAccessControl.smac.in_(macs)).delete(synchronize_session=False)

        # note: set can not support __getitem__, so transform to list
        if type(macs) == set:
            macs = list(macs)

        seg = length / max_reqs + 1
        for i in range(1, seg + 1):
            start = (i - 1) * max_reqs
            end = i * max_reqs
            base_query.filter(HostAccessControl.smac.in_(macs[start: end])).delete(synchronize_session=False)

        # update config host online false
        session.query(HostAccessControl).filter(HostAccessControl.processed_host == True)\
            .filter(HostAccessControl.smac.in_(macs)).update({'located_switch': None,
                                                              'port_num': None,
                                                              'online': False},
                                                             synchronize_session='fetch')

    def get_config_hosts(self, session=None):
        session = session or self.get_session()
        return session.query(HostAccessControl).filter_by(located_switch=None, processed_host=True).all()


sdn_access_db = SDNAccessDB()


if __name__ == '__main__':
    from server import cfg

    cfg.CONF(default_config_files=['../../automation.ini'])
    # _, macs, mac_ids = sdn_access_db.get_switch_mac_ls('D1012094419PI000005')
    # print len(macs)
    # sdn_access_db.bulk_insert_mac_l([{'smac': 'test', 'located_switch': 'D1012094419PI000005', 'assigned_vlan_id': 10, 'port_num': 'ge-1/1/1'}])
    session = sdn_access_db.get_session()
    # hosts = session.query(HostAccessControl).filter(HostAccessControl.smac.in_(set(['00:00:00:00:75:1c', '00:00:00:00:75:1b']))).all()
    # print hosts
    # sdn_access_db.bulk_update_mac_l([{'id': 174311,'smac': '00:00:00:00:75:0d', 'located_switch': 'D1012094419PI000005',
    #                            'assigned_vlan_id': 11, 'port_num': 'ge-1/1/10',
    #                            'online': True}])
    # switches = sdn_access_db.get_switches()
    # print switches[0].mgt_ip
    # config_hosts = sdn_access_db.get_collection(HostAccessControl,
    #                                             filters={'located_switch': ['']})
    # config_hosts = dict((config_host.smac, config_host.id) for config_host in config_hosts)
    # print config_hosts
    print(sdn_access_db.get_config_hosts())
