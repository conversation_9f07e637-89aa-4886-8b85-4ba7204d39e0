from server.db.models.base import Base
from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    DateTime,
    Float
)


class SnmpAlarmTrapMetadata(Base):
    __tablename__ = 'snmp_alarm_trap_metadata'
    trap_id = Column(Integer(), autoincrement=True, primary_key=True)
    name = Column(String(128), nullable=False)
    alarm_type = Column(Integer(), nullable=False)
    alarm_level = Column(Integer(), nullable=False)
    oid = Column(String(128), nullable=False, unique=True)
    match_regular = Column(String(128), nullable=False)
    sub_oid_mapping = Column(Text(4096), nullable=True)
    value_mapping = Column(Text(4096), nullable=True)
    description = Column(String(64), nullable=True)


class SnmpAlarmTrapOriginalData(Base):
    __tablename__ = 'snmp_alarm_trap_original_data'
    alarm_id = Column(Integer(), autoincrement=True, primary_key=True)
    source_ip = Column(String(64), nullable=False)
    occurrence_time = Column(DateTime(), nullable=False)
    name = Column(String(128), nullable=False)
    value = Column(String(4096), nullable=False)
    description = Column(String(128), nullable=True)
