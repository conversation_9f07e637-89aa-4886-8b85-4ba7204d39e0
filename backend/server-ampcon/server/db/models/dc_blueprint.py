# -*- coding: utf-8 -*-
import time

from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    ForeignKey,
    Boolean,
    DateTime,
    Enum,
    JSON,
    Table
)
from sqlalchemy.orm import relationship, exc

from server.db.db_common import DBCommon
from server.db.models.base import Base
from server.constants import OverlayStatus


class DCFabricUnit(Base):
    __tablename__ = "dc_fabric_unit"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), nullable=False)
    description = Column(String(128), nullable=True)
    unit_info = Column(JSON)


class DCFabricTemplate(Base):
    __tablename__ = "dc_fabric_template"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), nullable=False)
    description = Column(String(128), nullable=True)
    type = Column(String(32), nullable=False)
    underlay_routing_protocol = Column(String(32), nullable=False)
    overlay_control_protocol = Column(String(32), nullable=False)
    template_info = Column(JSON)
    
    
class DCFabricTopology(Base):
    __tablename__ = "dc_fabric_topology"
    id = Column(Integer, primary_key=True, autoincrement=True)
    fabric_id = Column(Integer, ForeignKey('fabric.id', ondelete='CASCADE', onupdate='CASCADE'))
    template_name = Column(String(128), nullable=False)
    fabric_config = Column(JSON)
    status = Column(String(32), default="Not Deployed") # Not Deployed | Deploying |Deploy Failed | Deployed
    fabric = relationship("Fabric", foreign_keys=[fabric_id])
    

class DCFabricTopologyNode(Base):
    __tablename__ = "dc_fabric_topology_node"
    id = Column(Integer, primary_key=True, autoincrement=True)
    logic_name = Column(String(128), nullable=False)
    switch_sn = Column(String(128), nullable=True)
    fabric_topo_id = Column(Integer, ForeignKey('dc_fabric_topology.id', ondelete='CASCADE', onupdate='CASCADE'))
    type = Column(String(32), nullable=False)
    status = Column(String(32), default="Undeploy") # Undeploy Deploying Failed Success
    vlan_domain_pool_id = Column(Integer, ForeignKey('resource_pool_vlan_domain.id', onupdate='CASCADE', ondelete='SET NULL'), nullable=True)
    node_info = Column(JSON)
    node_config = Column(JSON)
    # vlan_domain = relationship("ResourcePoolVlanDomain", foreign_keys=[vlan_domain_pool_id])
    

class DCLogicalNetwork(Base):
    __tablename__ = 'dc_logical_network'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128))
    description = Column(String(256))
    vpc_lock = Column(Boolean, default=False)
    
class DCLogicalNetworkConfigDetail(Base):
    __tablename__ = 'dc_logical_network_config_detail'

    id = Column(Integer, primary_key=True, autoincrement=True)
    logical_network_id = Column(Integer, ForeignKey('dc_logical_network.id'))
    description = Column(String(256))
    config_detail = Column(JSON)

    logical_network = relationship("DCLogicalNetwork", backref="config_details", uselist=False)
  
    
class DCLogicalRouter(Base):
    __tablename__ = 'dc_logical_router'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128))
    description = Column(String(256))
    fabric_id = Column(Integer, ForeignKey('fabric.id', ondelete='CASCADE', onupdate='CASCADE'))
    logical_network_id = Column(Integer, ForeignKey('dc_logical_network.id', ondelete='CASCADE', onupdate='CASCADE'))
    vrf_mode = Column(Enum('auto', 'manual', name='vrf_mode_enum'), nullable=False)
    vrf_name = Column(String(128))
    l3_anycast_mac_range = Column(String(256))
    status = Column(Integer)
    l3vni = Column(Integer)
    type = Column(Enum('Auto', 'Manual', name='type_enum'), nullable=False, server_default='Auto')
    position_x = Column(Integer, nullable=True)
    position_y = Column(Integer, nullable=True)
    vrf_vlan = Column(Integer, nullable=True)
    
    fabric = relationship("Fabric", backref="dc_logical_router", uselist=False)
    logical_network = relationship("DCLogicalNetwork", backref="dc_logical_router", uselist=False)
    
    @property
    def fabric_name(self):
        return self.fabric.fabric_name if self.fabric else None
    
    @property
    def logical_network_name(self):
        return self.logical_network.name if self.logical_network else None
    

class DCLogicalSwitch(Base):
    __tablename__ = 'dc_logical_switch'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128))
    description = Column(String(256))
    fabric_id = Column(Integer, ForeignKey('fabric.id', ondelete='CASCADE', onupdate='CASCADE'))
    logical_network_id = Column(Integer, ForeignKey('dc_logical_network.id', ondelete='CASCADE', onupdate='CASCADE'))
    arp_nd_suppress = Column(Boolean, default=True)
    status = Column(Integer)
    type = Column(Enum('Auto', 'Manual', name='type_enum'), nullable=False, server_default='Auto')
    position_x = Column(Integer, nullable=True)
    position_y = Column(Integer, nullable=True)
    
    fabric = relationship("Fabric", backref="dc_logical_switch", uselist=False)
    logical_network = relationship("DCLogicalNetwork", backref="dc_logical_switch", uselist=False)
    
    @property
    def fabric_name(self):
        return self.fabric.fabric_name if self.fabric else None

    @property
    def logical_network_name(self):
        return self.logical_network.name if self.logical_network else None

class DCVirtualNetwork(Base):
    __tablename__ = 'dc_virtual_network'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128))
    fabric_id = Column(Integer, ForeignKey('fabric.id', ondelete='CASCADE', onupdate='CASCADE'))
    az_id = Column(Integer, ForeignKey('virtual_resource_pool_az.id', ondelete='CASCADE', onupdate='CASCADE'))
    logical_network_id = Column(Integer, ForeignKey('dc_logical_network.id', ondelete='CASCADE', onupdate='CASCADE'))
    status = Column(Integer)
    position_x = Column(Integer, nullable=True)
    position_y = Column(Integer, nullable=True)
    
        
    fabric = relationship("Fabric", backref="dc_virtual_network", uselist=False)
    logical_network = relationship("DCLogicalNetwork", backref="dc_virtual_network", uselist=False)
    
    @property
    def fabric_name(self):
        return self.fabric.fabric_name if self.fabric else None
    
    @property
    def logical_network_name(self):
        return self.logical_network.name if self.logical_network else None
    

class DCLogicalInterface(Base):
    __tablename__ = 'dc_logical_interface'

    id = Column(Integer, primary_key=True, autoincrement=True)
    logical_network_id = Column(Integer, ForeignKey('dc_logical_network.id', ondelete='CASCADE', onupdate='CASCADE'))
    logical_router_id = Column(Integer, ForeignKey('dc_logical_router.id', ondelete='CASCADE', onupdate='CASCADE'))
    logical_switch_id = Column(Integer, ForeignKey('dc_logical_switch.id', ondelete='CASCADE', onupdate='CASCADE'))
    anycast_ipv4 = Column(String(64))
    virtual_ipv4_range = Column(String(256))
    anycast_mac = Column(String(64))
    status = Column(Integer)
    
    logical_network = relationship("DCLogicalNetwork", backref="dc_logical_interface", uselist=False)
    logical_router = relationship("DCLogicalRouter", backref="dc_logical_interface", uselist=False)
    logical_switch = relationship("DCLogicalSwitch", backref="dc_logical_interface", uselist=False)
    
    
class DCLogicalPort(Base):
    __tablename__ = 'dc_logical_port'

    id = Column(Integer, primary_key=True, autoincrement=True)
    az_id = Column(Integer, ForeignKey('virtual_resource_pool_az.id', ondelete='CASCADE', onupdate='CASCADE'))
    logical_network_id = Column(Integer, ForeignKey('dc_logical_network.id', ondelete='CASCADE', onupdate='CASCADE'))
    logical_switch_id = Column(Integer, ForeignKey('dc_logical_switch.id', ondelete='CASCADE', onupdate='CASCADE'))
    virtual_network_id = Column(Integer, ForeignKey('dc_virtual_network.id', ondelete='CASCADE', onupdate='CASCADE'))
    l2vni = Column(Integer)
    status = Column(Integer)
    vlan = Column(Integer)
    
    logical_network = relationship("DCLogicalNetwork", backref="dc_logical_port", uselist=False)
    logical_switch = relationship("DCLogicalSwitch", backref="dc_logical_port", uselist=False)
    virtual_network = relationship("DCVirtualNetwork", backref="dc_logical_port", uselist=False)
  
class DCLogicalRouterConfig(Base):
    __tablename__ = 'dc_logical_router_config'

    id = Column(Integer, primary_key=True, autoincrement=True)
    logical_router_id = Column(Integer, ForeignKey('dc_logical_router.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    logic_device_id = Column(Integer, ForeignKey('dc_fabric_topology_node.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    l3_anycast_mac = Column(String(64))
    status = Column(Integer)
    config = Column(JSON)
    err_msg = Column(Text)
    
    logic_device = relationship("DCFabricTopologyNode", backref="dc_logical_router_config", uselist=False)
    logical_router = relationship("DCLogicalRouter", backref="dc_logical_router_config", uselist=False)

class DCLogicalInterfaceConfig(Base):
    __tablename__ = 'dc_logical_interface_config'

    id = Column(Integer, primary_key=True, autoincrement=True)
    logical_interface_id = Column(Integer, ForeignKey('dc_logical_interface.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    logic_device_id = Column(Integer, ForeignKey('dc_fabric_topology_node.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    virtual_ip = Column(String(64))
    status = Column(Integer)
    config = Column(JSON)
    err_msg = Column(Text)
    
    logic_device = relationship("DCFabricTopologyNode", backref="dc_logical_interface_config", uselist=False)
    logical_interface = relationship("DCLogicalInterface", backref="dc_logical_interface_config", uselist=False)
    
    
class DCLogicalPortConfig(Base):
    __tablename__ = 'dc_logical_port_config'

    id = Column(Integer, primary_key=True, autoincrement=True)
    logical_port_id = Column(Integer, ForeignKey('dc_logical_port.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    logic_device_id = Column(Integer, ForeignKey('dc_fabric_topology_node.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    status = Column(Integer)
    config = Column(JSON)
    err_msg = Column(Text)
    
    logic_device = relationship("DCFabricTopologyNode", backref="dc_logical_port_config", uselist=False)
    logical_port = relationship("DCLogicalPort", backref="dc_logical_port_config", uselist=False)
    
    
class DCFabricDB(DBCommon):

    def update_fabric_unit(self, unit_id, unit_name, unit_info, description=None):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_unit = None
            if unit_id:
                existing_unit = session.query(DCFabricUnit).filter(DCFabricUnit.id == unit_id).first()
                unit = session.query(DCFabricUnit).filter(DCFabricUnit.name == unit_name).first()
                if unit and unit.id != unit_id:
                    raise Exception(f"unit_name: {unit_name} is already exists")
            else:
                existing_unit = session.query(DCFabricUnit).filter(DCFabricUnit.name == unit_name).first()
                if existing_unit:
                    raise Exception(f"unit_name: {unit_name} is already exists")
            if existing_unit:
                existing_unit.name = unit_name
                existing_unit.unit_info = unit_info
                existing_unit.description = description
            else:
                unit = DCFabricUnit(name=unit_name, description=description, unit_info=unit_info)
                session.add(unit)
                
    def get_fabric_unit_by_id(self, unit_id):
        session = self.get_session()
        unit = session.query(DCFabricUnit).filter(DCFabricUnit.id == unit_id).first()
        return unit
    
    def del_fabric_unit_by_id(self, unit_id):
        session = self.get_session()
        session.query(DCFabricUnit).filter(DCFabricUnit.id == unit_id).delete()
        
    def check_fabric_unit_name(self, name):
        session = self.get_session()
        existing_unit = session.query(DCFabricUnit).filter(DCFabricUnit.name == name).first()
        if existing_unit:
            return True
        else:
            return False
        
    def update_fabric_template(self, template_id, template_name, type, underlay_routing_protocol, overlay_control_protocol, template_info, description=None):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_template = None
            if template_id: 
                existing_template = session.query(DCFabricTemplate).filter(DCFabricTemplate.id == template_id).first()
                template = session.query(DCFabricTemplate).filter(DCFabricTemplate.name == template_name).first()
                if template and template.id != template_id:
                    raise Exception(f"template_name: {template_name} is already exists")
            else:
                existing_template = session.query(DCFabricTemplate).filter(DCFabricTemplate.name == template_name).first()
                if existing_template:
                    raise Exception(f"template_name: {template_name} is already exists")
            if existing_template:
                existing_template.name = template_name
                existing_template.type = type
                existing_template.underlay_routing_protocol = underlay_routing_protocol
                existing_template.overlay_control_protocol = overlay_control_protocol
                existing_template.template_info = template_info
                existing_template.description = description
            else:
                unit = DCFabricTemplate(name=template_name, description=description, type=type, template_info=template_info,
                                        underlay_routing_protocol=underlay_routing_protocol, overlay_control_protocol=overlay_control_protocol)
                session.add(unit)
                
    def get_fabric_template_by_id(self, template_id):
        session = self.get_session()
        template = session.query(DCFabricTemplate).filter(DCFabricTemplate.id == template_id).first()
        return template
    
    def del_fabric_template_by_id(self, template_id):
        session = self.get_session()
        session.query(DCFabricTemplate).filter(DCFabricTemplate.id == template_id).delete()
        
    def check_fabric_template_name(self, template_name):
        session = self.get_session()
        existing_template = session.query(DCFabricTemplate).filter(DCFabricTemplate.name == template_name).first()
        if existing_template:
            return True
        else:
            return False
        
    def add_fabric_topology(self, fabric_id, template_name, fabric_config):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_topo = session.query(DCFabricTopology).filter(DCFabricTopology.fabric_id == fabric_id).first()
            
            if existing_topo:
                existing_topo.template_name = template_name
                existing_topo.fabric_config = fabric_config
                return existing_topo
            else:
                topo = DCFabricTopology(fabric_id=fabric_id, template_name=template_name, fabric_config=fabric_config)
                session.add(topo)
                return topo
        
    def update_fabric_topology(self, fabric_id, template_name, fabric_config, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(DCFabricTopology).filter(DCFabricTopology.fabric_id == fabric_id).update({DCFabricTopology.template_name: template_name, DCFabricTopology.fabric_config: fabric_config})
    
                
    def get_fabric_topo_by_id(self, topo_id, session=None):
        if not session:
            session = self.get_session()
        topology = session.query(DCFabricTopology).filter(DCFabricTopology.id == topo_id).first()
        return topology
    
    def get_fabric_topo_by_fabric_id(self, fabric_id, session=None):
        if not session:
            session = self.get_session()
        topology = session.query(DCFabricTopology).filter(DCFabricTopology.fabric_id == fabric_id).first()
        return topology
    
    def del_fabric_topo_by_id(self, topo_id):
        session = self.get_session()
        session.query(DCFabricTopology).filter(DCFabricTopology.id == topo_id).delete()
    
    def update_fabric_topology_node_info(self, logic_name, switch_sn, fabric_topo_id, type, node_info):
        session = self.get_session()
        with session.begin(subtransactions=True):
            node = session.query(DCFabricTopologyNode).filter(DCFabricTopologyNode.fabric_topo_id == fabric_topo_id, 
                                                              DCFabricTopologyNode.logic_name == logic_name).first()
            if node:
                node.switch_sn = switch_sn
                node.type = type
                node.node_info = node_info
            else:
                node = DCFabricTopologyNode(logic_name=logic_name, switch_sn=switch_sn, fabric_topo_id=fabric_topo_id, type=type, node_info=node_info)
                session.add(node)
            return node.id
                
    def update_fabric_topology_node_config(self, logic_name, fabric_topo_id, node_config):
        session = self.get_session()
        session.query(DCFabricTopologyNode).filter(DCFabricTopologyNode.fabric_topo_id == fabric_topo_id, 
                                                    DCFabricTopologyNode.logic_name == logic_name).update({"node_config": node_config})
                
    def get_fabric_topology_node_info(self, node_id, session=None):
        if not session:
            session = self.get_session()
        node_info = session.query(DCFabricTopologyNode).filter(DCFabricTopologyNode.id == node_id).first()
        return node_info
    
    def del_fabric_topology_node_by_id(self, node_id):
        session = self.get_session()
        session.query(DCFabricTopologyNode).filter(DCFabricTopologyNode.id == node_id).delete()
    
    def get_fabric_topology_node(self, topo_id, session=None):
        if not session:
            session = self.get_session()
        node = session.query(DCFabricTopologyNode).filter(DCFabricTopologyNode.fabric_topo_id == topo_id).all()
        return node
    
    def get_fabric_topology_node_by_vd(self, vd_id, session=None):
        if not session:
            session = self.get_session()
        nodes = session.query(DCFabricTopologyNode).filter(DCFabricTopologyNode.vlan_domain_pool_id == vd_id).all()
        return nodes
    
    def update_logical_network(self, name, description= None, vpc_lock=None, ln_id=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_network=None
            if ln_id:
                existing_network = session.query(DCLogicalNetwork).filter(
                    DCLogicalNetwork.id == ln_id).first()
                network = session.query(DCLogicalNetwork).filter(
                    DCLogicalNetwork.name == name).first()
                if network and network.id != ln_id:
                    raise Exception(f"logical network name: {name} already exists")
            else:
                existing_network = session.query(DCLogicalNetwork).filter(
                    DCLogicalNetwork.name == name).first()
                if existing_network:
                    raise Exception(f"logical network name: {name} already exists")

            if existing_network:
                existing_network.name = name
                if description is not None:
                    existing_network.description = description
                if vpc_lock is not None:    
                    existing_network.vpc_lock = vpc_lock
                network = existing_network
            else:
                network = DCLogicalNetwork(name=name, description=description, vpc_lock = vpc_lock)
                session.add(network)
        return network
    
    def get_logical_network_by_id(self, ln_id, session=None):
        if not session:
            session = self.get_session()
        node_info = session.query(DCLogicalNetwork).filter(DCLogicalNetwork.id == ln_id).first()
        return node_info
    
    def delete_logical_network_by_id(self, ln_id, session=None):
        if not session:
            session = self.get_session()
        logical_network = session.query(DCLogicalNetwork).filter_by(id=ln_id).first()
        if logical_network:
            with session.begin(subtransactions=True):
                session.delete(logical_network)
        else:
            raise Exception(f"Logical network with id {ln_id} does not exist")

    def update_logical_network_config_detail(self, description= None, config_detail=None, ln_id=None, config_id=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_config=None
            if config_id:
                existing_config = session.query(DCLogicalNetworkConfigDetail).filter(
                    DCLogicalNetworkConfigDetail.id == config_id).first()

            if existing_config:
                existing_config.description = description
                network = existing_config
            else:
                network = DCLogicalNetworkConfigDetail(description=description, config_detail=config_detail, logical_network_id=ln_id)
                session.add(network)
        return network
    
    def delete_logical_network_config_detail_by_id(self, config_id, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_config = session.query(DCLogicalNetworkConfigDetail).filter(
                DCLogicalNetworkConfigDetail.id == config_id).first()
            if existing_config:
                session.delete(existing_config)
            else:
                raise Exception(f"Logical network config detail with id {config_id} does not exist")
    
    def update_logical_router(self, name=None, ln_id=None, fabric_id=None, description=None, vrf_mode=None, vrf_name=None, l3_anycast_mac_range=None, 
                              l3vni=None, vrf_vlan=None, status=None, lr_id=None, position_x=None,position_y=None, type=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_router=None
            if lr_id:
                existing_router = session.query(DCLogicalRouter).filter(
                    DCLogicalRouter.id == lr_id).first()
                if name is not None:
                    router = session.query(DCLogicalRouter).filter(
                        DCLogicalRouter.name == name).first()
                    if router and router.id != lr_id:
                        raise Exception(f"logical router name: {name} already exists")
            else:
                existing_router = session.query(DCLogicalRouter).filter(
                    DCLogicalRouter.name == name).first()
                if existing_router:
                    raise Exception(f"logical router name: {name} already exists")
                
            if vrf_name:
                router = session.query(DCLogicalRouter).filter(
                    DCLogicalRouter.vrf_name == vrf_name).first()
                if router and router.id != lr_id:
                    raise Exception(f"logical router vrf_name: {vrf_name} already exists")

            if existing_router:
                if name is not None:
                    existing_router.name = name
                if description is not None:
                    existing_router.description = description
                if vrf_vlan is not None:
                    existing_router.vrf_vlan = vrf_vlan
                if vrf_name is not None and vrf_mode == "auto":
                    existing_router.vrf_name = vrf_name
                if status is not None:
                    existing_router.status = status
                if position_x is not None:
                    existing_router.position_x = position_x
                if position_y is not None:
                    existing_router.position_y = position_y
                router = existing_router
            else:
                router = DCLogicalRouter(name=name, description=description, fabric_id=fabric_id, logical_network_id=ln_id, type=type,
                                         vrf_mode=vrf_mode, vrf_name=vrf_name, l3_anycast_mac_range=l3_anycast_mac_range, l3vni=l3vni,
                                         status=OverlayStatus.CREATE_NORMAL, position_x=position_x, position_y=position_y)

                session.add(router)
        return router
    
    def get_logical_router_by_id(self, lr_id, session=None):
        if not session:
            session = self.get_session()
        router = session.query(DCLogicalRouter).filter(DCLogicalRouter.id == lr_id).first()
        return router
    
    def delete_logical_router_by_id(self, lr_id, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(DCLogicalRouter).filter(DCLogicalRouter.id == lr_id).delete()
    
    def update_logical_switch(self, name=None, ln_id=None, fabric_id=None, description=None, arp_nd_suppress=None, status=None, ls_id=None, 
                              position_x=None,position_y=None, type=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_switch=None
            if ls_id:
                existing_switch = session.query(DCLogicalSwitch).filter(
                    DCLogicalSwitch.id == ls_id).first()
                if name is not None:
                    switch = session.query(DCLogicalSwitch).filter(
                        DCLogicalSwitch.name == name).first()
                    if switch and switch.id != ls_id:
                        raise Exception(f"logical switch name: {name} already exists")
            else:
                existing_switch = session.query(DCLogicalSwitch).filter(
                    DCLogicalSwitch.name == name).first()
                if existing_switch:
                    raise Exception(f"logical switch name: {name} already exists")

            if existing_switch:
                if name is not None:
                    existing_switch.name = name 
                if description is not None:
                    existing_switch.description = description
                if status is not None:
                    existing_switch.status = status
                if position_x is not None:
                    existing_switch.position_x = position_x
                if position_y is not None:
                    existing_switch.position_y = position_y
                switch = existing_switch
            else:
                switch = DCLogicalSwitch(name=name, description=description, fabric_id=fabric_id, logical_network_id=ln_id, type=type,
                                         arp_nd_suppress=arp_nd_suppress, status=OverlayStatus.CREATE_NORMAL, position_x=position_x, position_y=position_y)

                session.add(switch)
        return switch
    
    def get_logical_switch_by_id(self, ls_id, session=None):
        if not session:
            session = self.get_session()
        switch = session.query(DCLogicalSwitch).filter(DCLogicalSwitch.id == ls_id).first()
        return switch
    
    def delete_logical_switch_by_id(self, ls_id, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(DCLogicalSwitch).filter(DCLogicalSwitch.id == ls_id).delete()
    
    def update_virtual_network(self, name=None, ln_id=None, fabric_id=None, az_id=None, position_x=None, position_y=None, status=None, vn_id=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_vn=None
            if vn_id:
                existing_vn = session.query(DCVirtualNetwork).filter(
                    DCVirtualNetwork.id == vn_id).first()
                        
            if existing_vn:
                if status is not None:
                    existing_vn.status = status
                if position_x is not None:
                    existing_vn.position_x = position_x
                if position_y is not None:
                    existing_vn.position_y = position_y
                if name is not None:
                    existing_vn.name = name
                network = existing_vn
            else:   
                network = DCVirtualNetwork(name=name, az_id=az_id, fabric_id=fabric_id, logical_network_id=ln_id, 
                                           position_x=position_x, position_y=position_y, status=OverlayStatus.CREATE_NORMAL)
                session.add(network)
        return network
    
    def get_virtual_network_by_id(self, vn_id, session=None):
        if not session:
            session = self.get_session()
        network = session.query(DCVirtualNetwork).filter(DCVirtualNetwork.id == vn_id).first()
        return network
    
    def delete_virtual_network_by_id(self, vn_id, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(DCVirtualNetwork).filter(DCVirtualNetwork.id == vn_id).delete()
    
    def update_logical_interface(self, ln_id=None, ls_id=None, lr_id=None, anycast_ipv4=None, virtual_ipv4_range=None, anycast_mac=None,
                                 status=None, li_id=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_interface=None
            if li_id:
                existing_interface = session.query(DCLogicalInterface).filter(
                    DCLogicalInterface.id == li_id).first()
            else:
                interface = session.query(DCLogicalInterface).filter(
                    DCLogicalInterface.anycast_ipv4 == anycast_ipv4).first()
                if interface:
                    raise Exception(f"anycast ipv4: {anycast_ipv4} already exists")
            
            if existing_interface:
                if status is not None:
                    existing_interface.status = status
                if virtual_ipv4_range is not None:
                    existing_interface.virtual_ipv4_range = virtual_ipv4_range
                interface = existing_interface
            else:
                interface = DCLogicalInterface(logical_network_id=ln_id, logical_router_id=lr_id, logical_switch_id=ls_id, anycast_ipv4=anycast_ipv4, 
                                             virtual_ipv4_range=virtual_ipv4_range, anycast_mac=anycast_mac, status=OverlayStatus.CREATE_NORMAL)
                session.add(interface)
        return interface
    
    def get_logical_interface_by_logical_switch_id(self, ls_id, session=None):
        if not session:
            session = self.get_session()
        port = session.query(DCLogicalInterface).filter(DCLogicalInterface.logical_switch_id == ls_id).first()
        return port
    
    def get_logical_interface_by_id(self, li_ld, session=None):
        if not session:
            session = self.get_session()
        port = session.query(DCLogicalInterface).filter(DCLogicalInterface.id == li_ld).first()
        return port
    
    
    def update_logical_port(self, az_id=None, ls_id=None, ln_id=None, vn_id=None, l2vni=None, status=None, vlan=None, lp_id=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_port=None
            if lp_id:
                existing_port = session.query(DCLogicalPort).filter(
                    DCLogicalPort.id == lp_id).first()
            
            if existing_port:
                if status is not None:
                    existing_port.status = status
                port = existing_port
            else:
                port = DCLogicalPort(az_id=az_id, logical_network_id=ln_id, logical_switch_id=ls_id, virtual_network_id=vn_id, 
                                        l2vni=l2vni, status=OverlayStatus.CREATE_NORMAL, vlan=vlan)
                session.add(port)
        return port
    
    def get_logical_port_by_id(self, lp_id, session=None):
        if not session:
            session = self.get_session()
        port = session.query(DCLogicalPort).filter(DCLogicalPort.id == lp_id).first()
        return port
    
    def get_logical_port_by_logical_switch_id(self, ls_id, session=None):
        if not session:
            session = self.get_session()
        port = session.query(DCLogicalPort).filter(DCLogicalPort.logical_switch_id == ls_id).first()
        return port
    
    def update_logical_router_config(self, lr_config_id=None, lr_id=None, logic_device_id=None, l3_anycast_mac=None, config=None, status=None, err_msg=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_config=None
            if lr_config_id:
                existing_config = session.query(DCLogicalRouterConfig).filter(
                    DCLogicalRouterConfig.id == lr_config_id).first()
            else:
                existing_config = session.query(DCLogicalRouterConfig).filter(
                    DCLogicalRouterConfig.logical_router_id == lr_id, DCLogicalRouterConfig.logic_device_id == logic_device_id).first()
            
            if existing_config:
                if status is not None:
                    existing_config.status = status
                if config is not None:
                    existing_config.config = config
                if l3_anycast_mac is not None and existing_config.l3_anycast_mac is None:
                    existing_config.l3_anycast_mac = l3_anycast_mac
                if err_msg is not None:
                    existing_config.err_msg = err_msg
                config = existing_config
            else:
                config = DCLogicalRouterConfig(logical_router_id=lr_id, logic_device_id=logic_device_id, l3_anycast_mac=l3_anycast_mac, 
                                               config=config, status=OverlayStatus.CREATE_NORMAL)
                session.add(config)
        return config
    
    def delete_logical_router_config(self, config_id):
        session = self.get_session()
        session.query(DCLogicalRouterConfig).filter(DCLogicalRouterConfig.id == config_id).delete()
    
    def get_logical_router_config_by_id(self, config_id):
        session = self.get_session()
        configs = session.query(DCLogicalRouterConfig).filter(DCLogicalRouterConfig.id == config_id).first()
        return configs
    
    def get_logical_router_config_by_router_id(self, logical_router_id):
        session = self.get_session()
        configs = session.query(DCLogicalRouterConfig).filter(DCLogicalRouterConfig.logical_router_id == logical_router_id).all()
        return configs
    
    def get_logical_router_config_by_device(self, logical_router_id, logic_device_id):
        session = self.get_session()
        config = session.query(DCLogicalRouterConfig).filter(DCLogicalRouterConfig.logical_router_id == logical_router_id, DCLogicalRouterConfig.logic_device_id == logic_device_id).first()
        return config
    
    
    def update_logical_interface_config(self, li_config_id=None, li_id=None, logic_device_id=None, virtual_ip=None, config=None, status=None, err_msg=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_config=None
            if li_config_id:
                existing_config = session.query(DCLogicalInterfaceConfig).filter(
                    DCLogicalInterfaceConfig.id == li_config_id).first()
            else:
                existing_config = session.query(DCLogicalInterfaceConfig).filter(
                    DCLogicalInterfaceConfig.logical_interface_id == li_id, DCLogicalInterfaceConfig.logic_device_id == logic_device_id).first()
            
            if existing_config:
                if status is not None:
                    existing_config.status = status
                if config is not None:
                    existing_config.config = config
                if virtual_ip is not None and existing_config.virtual_ip is None:
                    existing_config.virtual_ip = virtual_ip
                if err_msg is not None:
                    existing_config.err_msg = err_msg
                config = existing_config
            else:
                config = DCLogicalInterfaceConfig(logical_interface_id=li_id, logic_device_id=logic_device_id, virtual_ip=virtual_ip, 
                                                  config=config, status=OverlayStatus.CREATE_NORMAL)
                session.add(config)
        return config
    
    def delete_logical_interface_config(self, config_id):
        session = self.get_session()
        session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.id == config_id).delete()
    
    def get_logical_interface_config_by_id(self, config_id):
        session = self.get_session()
        configs = session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.id == config_id).first()
        return configs
    
    def get_logical_interface_config_by_interface(self, interface_id):
        session = self.get_session()
        configs = session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.logical_interface_id == interface_id).all()
        return configs
    
    def get_logical_interface_config_by_device(self, interface_id, logic_device_id):
        session = self.get_session()
        config = session.query(DCLogicalInterfaceConfig).filter(
                                DCLogicalInterfaceConfig.logical_interface_id == interface_id, DCLogicalInterfaceConfig.logic_device_id == logic_device_id).first()
        return config
    
    def update_logical_port_config(self, lp_config_id=None, lp_id=None, logic_device_id=None, config=None, status=None, err_msg=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_config=None
            if lp_config_id:
                existing_config = session.query(DCLogicalPortConfig).filter(
                    DCLogicalPortConfig.id == lp_config_id).first()
            else:
                existing_config = session.query(DCLogicalPortConfig).filter(
                    DCLogicalPortConfig.logical_port_id == lp_id, DCLogicalPortConfig.logic_device_id == logic_device_id).first()
            
            if existing_config:
                if status is not None:
                    existing_config.status = status
                if config is not None:
                    existing_config.config = config
                if err_msg is not None:
                    existing_config.err_msg = err_msg
                config = existing_config
            else:
                config = DCLogicalPortConfig(logical_port_id=lp_id, logic_device_id=logic_device_id, config=config, status=OverlayStatus.CREATE_NORMAL)
                session.add(config)
        return config
    
    def delete_logical_port_config(self, config_id):
        session = self.get_session()
        session.query(DCLogicalPortConfig).filter(DCLogicalPortConfig.id == config_id).delete()
    
    def get_logical_port_config_by_id(self, config_id):
        session = self.get_session()
        configs = session.query(DCLogicalPortConfig).filter(DCLogicalPortConfig.id == config_id).first()
        return configs
    
    def get_logical_port_config_by_port(self, port_id):
        session = self.get_session()
        configs = session.query(DCLogicalPortConfig).filter(DCLogicalPortConfig.logical_port_id == port_id).all()
        return configs
        

dc_fabric_db = DCFabricDB()

