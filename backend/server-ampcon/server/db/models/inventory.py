# -*- coding: utf-8 -*-
import datetime
import json
import logging
import os
import shutil
import time
import zlib

import sqlalchemy as sa
from sqlalchemy import (
    Column,
    SmallInteger,
    Integer,
    String,
    Text,
    ForeignKey,
    DateTime,
    Boolean,
    Enum,
    Float,
    BigInteger,
    func,
    case, literal_column
)
from sqlalchemy.dialects.mysql import BIGINT, LONGTEXT
from sqlalchemy.orm import relationship, exc, column_property
from sqlalchemy import ForeignKeyConstraint
from sqlalchemy.orm.attributes import flag_modified

from server import cfg
from server import constants
from server.db.db_common import DBCommon
from server.db.models.base import Base
from server.util.encrypt_util import aes_cipher

DISPLAY_LOG_CYCLE = cfg.CONF.display_log_cycle if getattr(cfg.CONF, 'display_log_cycle', None) else 200
LOG = logging.getLogger(__name__)

with open(f'{constants.AUTOMATION_BASE_DIR}/server/model_platform_mapping.json', 'r') as f:
    model_platform_mapping = json.load(f)


class MachineHistoryInfo(Base):
    __tablename__ = 'machine_history_info'
    id = Column(Integer, autoincrement=True, primary_key=True)
    mem = Column(String(11))
    cpu = Column(String(11))
    disk = Column(String(11))


class PushConfigTask(Base):
    __tablename__ = 'push_config_task'
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_name = Column(String(64), nullable=False)
    task_content = Column(LONGTEXT, nullable=False)
    task_status = Column(Integer, nullable=False)
    # 0-Ready 1-Running 2-Finished
    read_tag = Column(Integer, nullable=False)
    create_user = Column(String(32), nullable=False)
    details = relationship("PushConfigTaskDetails", back_populates="parent", cascade="all, delete-orphan")


class PushConfigTaskDetails(Base):
    __tablename__ = 'push_config_task_details'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(64), ForeignKey('push_config_task.task_name', ondelete='CASCADE'))
    sn = Column(String(32), nullable=False)
    # 0-Not-start 1-running 2-success 3-failed
    push_status = Column(Integer, nullable=False)
    push_time = Column(DateTime, nullable=False)
    push_ret = Column(LONGTEXT, nullable=False)
    parent = relationship("PushConfigTask", back_populates="details")


class SwitchSwitchAutoConfig(Base):
    __tablename__ = 'switch_switch_autoconfig'
    id = Column(Integer, primary_key=True, autoincrement=True)
    switch_id = Column(Integer, ForeignKey('switch.id', ondelete='CASCADE'))
    switchAutoConfig_id = Column(Integer, ForeignKey('switch_autoconfig.id', ondelete='CASCADE'))


class SwitchLog(Base):
    __tablename__ = 'switch_log'

    id = Column(Integer, primary_key=True, autoincrement=True)
    switch_id = Column(String(64), nullable=False)
    level = Column(Enum('debug', 'info', 'warn', 'error'), default='debug', nullable=False)
    content = Column(Text(65535), nullable=False)
    report_action = Column(String(64))

    def __init__(self, switch_id, content, report_action, level='debug'):
        self.switch_id = switch_id
        self.content = content
        self.level = level
        self.report_action = report_action


class Switch(Base):
    __tablename__ = 'switch'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), index=True, unique=True)
    address = Column(String(64))
    status = Column(Enum('Init', 'Registered Not-staged', 'Configured', 'Staged', 'Registered', 'Provisioning Success',
                         'Provisioning Failed', 'Imported',
                         'DECOM', 'DECOM-Init', 'DECOM-Pending', 'DECOM-Manual', 'RMA'),
                    default='Configured')
    version = Column(String(32))
    revision = Column(String(32))
    hwid = Column(String(32))
    tmp_ip = Column(String(32))
    mgt_ip = Column(String(32))
    link_ip_addr = Column(String(32), default='')
    topology = Column(Enum('1', '2', '3'), default='1')
    step = Column(SmallInteger, default=0)
    enable = Column(Boolean, default=False)
    current_user = Column(String(32))
    current_password = Column(String(32))
    host_name = Column(String(64))
    domain = Column(String(64))
    remark = Column(Text(1024))
    mac_addr = Column(String(32))
    gnmi_interfaces = Column(Boolean, default=True)
    gnmi_lldp = Column(Boolean, default=True)
    gnmi_modules = Column(Boolean, default=True)
    gnmi_ai = Column(Boolean, default=True)
    # 0:deploy 1:import 2:rma
    import_type = Column(SmallInteger, default=0)
    # 0: upgrading 1: upgraded 2: upgrade failed 3: no upgrade
    upgrade_status = Column(SmallInteger, default=3)
    # 2:unknown 1:un-reachable 0:reachable  3 unmanageable
    reachable_status = Column(SmallInteger, default=0)
    # define a config after switch deployed, e.g. disable/enable VPN, collect config peridic
    post_deployed_config = Column(Text(1024), default='{}')
    is_picos_v = Column(Boolean, default=False)
    platform_model = Column(String(32), ForeignKey('switch_systeminfo.model', ondelete='CASCADE'))
    platform = column_property(case(
        [(literal_column('platform_model') == key, 'x86' if value == 'x86h' or value == 'x86-fs' else value) for
         key, value in model_platform_mapping.items()], else_='other'))
    config_parameters = Column(Text(65535))
    system_config_id = Column(Integer, ForeignKey('system_config.id', ondelete='CASCADE'),
                              server_default='select id from automation.system_config where config_name={} limit 1'.format(
                                  constants.GLOBAL_CONFIG_TAG))

    license = relationship("License", lazy='select', cascade='all, delete, delete-orphan',
                           passive_deletes=True, backref='switches')

    configs = relationship('SwitchAutoConfig', secondary='switch_switch_autoconfig', backref='switches',
                           lazy='select', order_by='SwitchAutoConfig.id', passive_deletes=True)

    switch_model = relationship('SwitchSystemInfo', lazy='select')
    gis = relationship("SwitchGis", lazy='select', cascade='all, delete, delete-orphan',
                       passive_deletes=True, uselist=False)
    switchStatus = relationship('SwitchStatus', lazy='select', cascade='all, delete, delete-orphan',
                                passive_deletes=True)

    switch_monitor = relationship('MonitorTarget', backref='switch', lazy='select',
                                  cascade='all, delete, delete-orphan',
                                  passive_deletes=True, uselist=False)


class SwitchNeInfo(Base):
    __tablename__ = 'switch_ne_info'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), index=True, unique=True)
    switch_menu_tree_id = Column(Integer, ForeignKey('switch_menu_tree_info.group_id', ondelete='CASCADE'), default=1)


class SwitchMenuTreeInfo(Base):
    __tablename__ = 'switch_menu_tree_info'
    group_id = Column(Integer, primary_key=True, autoincrement=True)
    group_name = Column(String(32))
    parent_group_id = Column(Integer)
    longitude = Column(Float)
    latitude = Column(Float)
    switch_ne_info = relationship('SwitchNeInfo', backref='switch_menu_tree_info', lazy='select')


class SwitchAgentConf(Base):
    __tablename__ = 'switch_agent_conf'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), unique=True)
    conf = Column(sa.BLOB(65535), nullable=False)


class AssociationGroup(Base):
    __tablename__ = 'association_group'
    id = Column(Integer, primary_key=True, autoincrement=True)
    switch_sn = Column('switch_sn', String(64))
    group_name = Column('group_name', String(64),
                        ForeignKey('group.group_name', ondelete='CASCADE', onupdate='CASCADE'))
    audit = Column(Boolean, default=False)
    action = Column(Boolean, default=False)
    upgrading = Column(Boolean, default=False)
    retrieve_config = Column(Boolean, default=False)
    # 1:deploy 2:import
    switch_type = Column(Integer)
    delete = Column(Boolean, default=False)


class Group(Base):
    __tablename__ = 'group'
    id = Column(Integer, primary_key=True, autoincrement=True)
    group_name = Column(String(64), index=True, unique=True, nullable=False)
    description = Column(Text(1024))
    audit = Column(Boolean, default=False)
    action = Column(Boolean, default=False)
    upgrading = Column(Boolean, default=False)
    retrieve_config = Column(Boolean, default=False)
    association_group = relationship("AssociationGroup", lazy='joined', cascade='all, delete, delete-orphan',
                                     passive_deletes=True)
    group_type = Column(Enum('switch', 'host'), default='switch', nullable=False)


class UserGroupMapping(Base):
    __tablename__ = 'user_group_mapping'
    user_id = Column(Integer, primary_key=True)
    group_id = Column(Integer, primary_key=True)


class HostGroupMapping(Base):
    __tablename__ = 'host_group_mapping'
    group_name = Column(String(64), ForeignKey('group.group_name', ondelete='CASCADE'), primary_key=True)
    device_name = Column(String(64), ForeignKey('ansible_device.device_name', ondelete='CASCADE'), primary_key=True)


class UserSwitchLock(Base):
    __tablename__ = 'user_switch_lock'
    id = Column(Integer, primary_key=True, autoincrement=True)
    user = Column(String(32), index=True,
                  nullable=False)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), index=True, unique=True)


class DeployedSecuritySwitch(Base):
    __tablename__ = 'deployed_security_switch'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), index=True, unique=True)
    invalid_times = Column(Integer, default=0)


class SwitchConfigBackup(Base):
    __tablename__ = 'switch_config_backup'
    id = Column(Integer, primary_key=True, autoincrement=True)
    ip = Column(String(64), index=True, unique=True, nullable=False)
    sn = Column(String(64), index=True)
    config = Column(sa.BLOB(1048576))
    # 0:unknown 1:auto 2:manual
    back_up_type = Column(SmallInteger, default=0)


class SwitchConfigSnapshot(Base):
    __tablename__ = 'switch_config_snapshot'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    snapshot_time = Column(DateTime)
    archive_config = Column(sa.BLOB(1048576), nullable=False)
    config_type = Column(String(255), default='L2/L3')
    description = Column(String(255), default='None')
    tag = Column(String(32), default='None')


class SwitchConfigSnapshotWithTag(Base):
    __tablename__ = 'switch_config_snapshot_with_tag'
    id = Column(Integer, primary_key=True)
    sn = Column(String(64), nullable=False)
    snapshot_time = Column(DateTime)
    archive_config = Column(sa.BLOB(1048576), nullable=False)
    config_type = Column(String(255), default='L2/L3')
    description = Column(String(255), default='None')
    tag = Column(String(32), default='None')
    tag_content = Column(String(255))


class SwitchAutoConfig(Base):
    __tablename__ = 'switch_autoconfig'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(64), unique=True, nullable=False)
    config_encrypted = Column('config', LONGTEXT)
    type = Column(Enum('global', 'regional', 'site', 'switch', 'mgt_ip'))
    parent_id = Column(Integer)
    system_model = Column(String(32), ForeignKey('switch_systeminfo.model', ondelete='CASCADE'))
    default = Column(Boolean, default=False)
    switchYamlConfigs = relationship("SwitchYamlConfig", backref='switch_autoConfig', lazy='joined',
                                     passive_deletes=True)

    @property
    def config(self):
        return aes_cipher.decrypt(self.config_encrypted)

    @config.setter
    def config(self, config_content):
        self.config_encrypted = aes_cipher.encrypt(config_content)


class License(Base):
    __tablename__ = 'license'
    id = Column(Integer, primary_key=True, autoincrement=True)
    local_lic = Column(Text(512))
    portal_lic = Column(Text(512))
    license_expired = Column(DateTime)
    lic_feature = Column(String(32))
    lic_speed = Column(String(32))
    lic_name = Column(String(32))
    sn_num = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), unique=True)
    status = Column(Enum('Active', 'Expiring', 'No License', 'Expired', 'Unknown'), default='Unknown')
    upgrate = Column(Boolean, default=False)


class Template(Base):
    __tablename__ = 'template'
    template_name = Column(String(32), primary_key=True)
    type = Column(Enum('global', 'regional', 'site', 'global sec'))
    content = Column(Text(4096))
    model = Column(String(32), ForeignKey('switch_systeminfo.model', ondelete='CASCADE', onupdate='CASCADE'))


class SwitchSystemInfo(Base):
    __tablename__ = 'switch_systeminfo'
    model = Column(String(32), primary_key=True)
    speed_for_license = Column(Enum('1G', '10G'), default='1G')
    feature = Column(String(32))
    platform = Column(String(11))
    up_to_date_version = Column(String(32))
    up_to_date_image_path = Column(String(255))
    up_to_date_image_md5_path = Column(String(255))
    up_to_date_onie_path = Column(String(255))
    patched_tar_file = Column(String(255))
    patched_install_script = Column(String(255))
    script_file_path = Column(String(255))
    # add in manual upgrade image request
    manual_upgrade_scripts = Column(String(511))


class SwitchYamlConfig(Base):
    __tablename__ = 'switch_yamlconfig'
    id = Column(Integer, primary_key=True, autoincrement=True)
    autoconfig_id = Column(Integer, ForeignKey('switch_autoconfig.id', ondelete='CASCADE'))
    pushconfig = Column(Text(65535))


class SwitchGis(Base):
    __tablename__ = 'switch_gis'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), unique=True)
    longitude = Column(Float)
    latitude = Column(Float)
    switch = relationship('Switch', back_populates='gis')


class SwitchStatus(Base):
    __tablename__ = 'switch_status'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    port_name = Column(String(32))
    rc_count = Column(BIGINT(unsigned=True))
    tx_count = Column(BIGINT(unsigned=True))
    rc_drop = Column(BIGINT(unsigned=True))
    tx_drop = Column(BIGINT(unsigned=True))
    lldp_neighbor = Column(Text(1024))
    link_status = Column(Enum('Down', 'Up'), nullable=False, default='Down')
    admin_status = Column(Enum('Enabled', 'Disabled'), nullable=False, default='Enabled')
    flow_control = Column(Enum('Enabled', 'Disabled'), nullable=False, default='Disabled')
    # link_speed = Column(Enum('Auto', '100M', '1Gb', '10Gb', '25Gb', '40Gb', '100Gb'),
    #                     nullable=False, default='Auto')
    link_speed = Column(String(32), nullable=False, default='Auto')
    port_type = Column(String(32), nullable=False, default='RJ45')
    upload_time = Column(DateTime, nullable=False)
    rx_rate = Column(BIGINT(unsigned=True))
    tx_rate = Column(BIGINT(unsigned=True))
    description = Column(String(255), default='')


class SwitchGlobalStatus(Base):
    __tablename__ = 'switch_global_status'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    # upload_time means the time when switch post the data
    upload_time = Column(DateTime, nullable=False)
    # type means the data type. e.g. copp. vlan, event
    type = Column(String(128), nullable=False)
    content = Column(Text(65535))


class SwitchParking(Base):
    __tablename__ = 'switch_parking'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), index=True, unique=True)
    hardware_id = Column(String(64))
    address = Column(String(64))
    model = Column(String(64))
    ip = Column(String(64))
    investigate = Column(Boolean, default=False)
    register_count = Column(SmallInteger, default=0)
    last_register = Column(DateTime, default=func.now())
    history_time = Column(Text(2048))
    remark = Column(Text(1024))


class SystemConfig(Base):
    __tablename__ = 'system_config'
    id = Column(Integer, autoincrement=True, primary_key=True)
    switch_op_user = Column(String(32))
    switch_op_password_encrypted = Column('switch_op_password', String(32))
    license_portal_url = Column(String(255))
    license_portal_user = Column(String(32))
    license_portal_password_encrypted = Column('license_portal_password', String(32))
    license_portal_token = Column(String(255))
    security_config = Column('security_config', String(255))
    parking_security_config = Column('parking_security_config', String(255))
    templates_set = Column(String(255), default='Network Access Device')  # not used
    customized_fields = Column(Text(2056))  # not used
    retrieve_config_num = Column(Integer, default=100)
    db_config_num = Column(Integer, default=3)
    config_name = Column(String(32))
    switch = relationship('Switch', backref='system_config')
    license_account_info = relationship("SystemConfigLicenseAccountInfo", back_populates="system_config")

    @property
    def switch_op_password(self):
        return aes_cipher.decrypt(self.switch_op_password_encrypted)

    @switch_op_password.setter
    def switch_op_password(self, password: str):
        self.switch_op_password_encrypted = aes_cipher.encrypt(password)

    @property
    def license_portal_password(self):
        return aes_cipher.decrypt(self.license_portal_password_encrypted)

    @license_portal_password.setter
    def license_portal_password(self, password: str):
        self.license_portal_password_encrypted = aes_cipher.encrypt(password)

    def get_security_config_content(self):
        s = ''
        if not self.security_config or not os.path.exists(self.security_config):
            return ''
        with open(self.security_config, 'r') as file:
            for line in file.readlines():
                s += line
        return aes_cipher.decrypt(s)

    def get_parking_security_config_content(self):
        s = ''
        if not self.parking_security_config or not os.path.exists(self.parking_security_config):
            return ''
        with open(self.parking_security_config, 'r') as file:
            for line in file.readlines():
                s += line
        return aes_cipher.decrypt(s)

    @staticmethod
    def _get_decrypted_config_path(path):
        temp_path = path.replace('.config', '_{}.config'.format(time.time()))
        s = ''
        with open(path, 'r') as file:
            with open(temp_path, 'w+') as dst_file:
                for line in file.readlines():
                    s += line
                dst_file.write(aes_cipher.decrypt(s))
        return temp_path

    def get_decrypted_security_config_path(self):
        return self._get_decrypted_config_path(self.security_config)

    def get_decrypted_parking_security_config_path(self):
        return self._get_decrypted_config_path(self.parking_security_config)

    @staticmethod
    def remove_temp_security_config_path(temp_path):
        if os.path.exists(temp_path):
            os.remove(temp_path)


class SystemConfigLicenseAccountInfo(Base):
    __tablename__ = 'system_config_license_account_info'
    id = Column(Integer, autoincrement=True, primary_key=True)
    system_config_id = Column(Integer, ForeignKey('system_config.id', ondelete='CASCADE', onupdate='CASCADE'),
                              nullable=False)
    expire_date = Column(String(255))
    standard_license_count = Column(Integer, nullable=False)
    trial_license_count = Column(Integer, nullable=False)
    system_config = relationship("SystemConfig", back_populates="license_account_info")


class DbBackup(Base):
    __tablename__ = 'db_backup'
    id = Column(Integer, autoincrement=True, primary_key=True)
    name = Column(String(64))
    version = Column(String(64))
    backup_file_path = Column(String(128))


class ApplicationConfig(Base):
    __tablename__ = 'application_config'
    id = Column(Integer, primary_key=True, autoincrement=True)
    # The name should be flow_visualbility, sdn_access, vtep_management
    application_name = Column(String(64), unique=True, nullable=False)
    # The configuration should be string, suggest to be json format
    configuration = Column(Text(65535), nullable=False)
    application_status = Column(Text(65535))


class TaskStatus(Base):
    __tablename__ = 'task_status'
    id = Column(Integer, autoincrement=True, primary_key=True)
    name = Column(String(255), index=True, nullable=False, unique=True)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    status = Column(String(32))


class TaskLock(Base):
    __tablename__ = 'task_lock'
    id = Column(Integer, autoincrement=True, primary_key=True)
    name = Column(String(255), index=True, unique=True, nullable=False)
    host = Column(String(32))


class GroupTask(Base):
    __tablename__ = 'group_task'
    id = Column(Integer, autoincrement=True, primary_key=True)
    name = Column(String(255), index=True, unique=True, nullable=False)
    type = Column(String(32), default='upgrade')
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    status = Column(String(32))


class VpnConfig(Base):
    __tablename__ = 'vpn_config'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), unique=True)
    client_key = Column(Text(65535), nullable=False)
    client_crt = Column(Text(65535), nullable=False)
    ca_crt = Column(Text(65535), nullable=False)
    vpn_ip = Column(String(32))
    vpn_online_status = Column(Boolean, default=False)


class ModelPhysicPort(Base):
    __tablename__ = 'model_physic_port'
    id = Column(Integer, autoincrement=True, primary_key=True)
    platform_name = Column(String(32), index=True)
    port_name = Column(String(32), nullable=False)
    port_type = Column(Enum('ge', 'te', 'qe', 'xe'))

    # platform_obj = relationship('SwitchSystemInfo', backref='ports')


class SwitchPort(Base):
    __tablename__ = 'switch_port'
    id = Column(Integer, autoincrement=True, primary_key=True)
    switch_sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    port_name = Column(String(32), nullable=False)
    speed = Column(String(64))
    status = Column(Enum('UP', 'DOWN'), default='DOWN')
    type = Column(String(25))
    management = Column(Enum('enabled', 'disabled'), default='enabled')
    flow_control = Column(Enum('enabled', 'disabled'), default='enabled')
    duplex = Column(Enum('full'), default='full')
    description = Column(String(255), default='')

    switch = relationship('Switch', backref='ports')


class AllowedSourceIPPolicy(Base):
    __tablename__ = 'allowed_source_ip_policy'
    id = Column(Integer, autoincrement=True, primary_key=True)
    ip = Column(String(64), nullable=False)


class SwitchImage(Base):
    def __init__(self):
        pass

    __tablename__ = 'switch_image_info'
    id = Column(Integer, primary_key=True)
    image_name = Column(String(255), nullable=True)
    image_path = Column(String(255), nullable=True)
    image_md5_path = Column(String(255), nullable=True)
    platform = Column(String(11), nullable=True)
    version = Column(String(11), nullable=True)
    revision = Column(String(32), nullable=True)


class HardwareMapping(Base):
    __tablename__ = 'hardware_mapping'
    id = Column(Integer, autoincrement=True, primary_key=True)
    hardware_model = Column(String(32), nullable=True)
    switch_model = Column(String(32), nullable=True)


class LicenseInfo(Base):
    __tablename__ = 'license_info'
    id = Column(Integer, autoincrement=True, primary_key=True)
    license_data = Column(Text(65535), nullable=False)
    license_type = Column(String(32), nullable=False)


class MonitorTarget(Base):
    __tablename__ = 'monitor_target'

    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'))
    port = Column(Integer)
    # 设备类型 1:switch 2:OTN
    device_type = Column(Integer)
    # 转换为二进制 从右往左:  0: interface 1: lldp
    enable = Column(Integer)
    # 用于记录OTN设备必要信息
    name = Column(String(64), nullable=True)
    mac = Column(String(64), nullable=True)
    topology_node = relationship('TopologyNode', backref='monitor_target', cascade='all, delete, delete-orphan',
                                 passive_deletes=True)


class Topology(Base):
    __tablename__ = 'topology'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), nullable=True, unique=True)
    description = Column(String(256), nullable=True)
    nodes = relationship('TopologyNode', backref='topology')
    edges = relationship('TopologyEdge', backref='topology')
    is_show_legend = Column(Boolean, nullable=False, default=True)
    zoom = Column(Float, nullable=False, default=1.0)
    translate_x = Column(Integer, nullable=False, default=0)
    translate_y = Column(Integer, nullable=False, default=0)
    is_in_tree_mode = Column(Boolean, nullable=False, default=False)
    topology_type = Column(Enum('topology', 'fabric', 'site'), nullable=False, default='default')
    is_show_default = Column(Boolean, nullable=False, default=False)


class TopologyNode(Base):
    __tablename__ = 'topology_node'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_label = Column(String(128), nullable=True)
    topology_id = Column(Integer, ForeignKey('topology.id', ondelete='CASCADE'))
    monitor_target_id = Column(Integer, ForeignKey('monitor_target.id', ondelete='CASCADE'))
    layer = Column(Integer, nullable=True)
    position_x = Column(Integer, nullable=True)
    position_y = Column(Integer, nullable=True)


class TopologyEdge(Base):
    __tablename__ = 'topology_edge'

    id = Column(Integer, primary_key=True, autoincrement=True)
    topology_id = Column(Integer, ForeignKey('topology.id', ondelete='CASCADE'))
    source_id = Column(Integer, ForeignKey('topology_node.id', ondelete='CASCADE'))
    target_id = Column(Integer, ForeignKey('topology_node.id', ondelete='CASCADE'))
    source_port = Column(String(32))
    target_port = Column(String(32))
    description = Column(String(256), nullable=True)
    source_node = relationship("TopologyNode", foreign_keys=[source_id], backref="edge_source")
    target_node = relationship("TopologyNode", foreign_keys=[target_id], backref="edge_target")


class Fabric(Base):
    __tablename__ = 'fabric'

    id = Column(Integer, primary_key=True, autoincrement=True)
    fabric_name = Column(String(64), nullable=True, unique=True)
    description = Column(String(256), nullable=True)
    topology_id = Column(Integer, ForeignKey('topology.id', ondelete='CASCADE'))


class AssociationFabric(Base):
    __tablename__ = 'association_fabric'

    id = Column(Integer, primary_key=True, autoincrement=True)
    fabric_id = Column(Integer, ForeignKey('fabric.id', ondelete='CASCADE'))
    switch_id = Column(Integer, ForeignKey('switch.id', ondelete='CASCADE'))
    fabric = relationship("Fabric", foreign_keys=[fabric_id], backref="fabric")
    switch = relationship("Switch", foreign_keys=[switch_id], backref="switch")


class FabricVniMapping(Base):
    __tablename__ = 'fabric_vni_mapping'

    id = Column(Integer, primary_key=True, autoincrement=True)
    fabric_id = Column(Integer, ForeignKey('fabric.id', ondelete='CASCADE'))
    vni_id = Column(Integer, ForeignKey('resource_pool_vni.id', ondelete='CASCADE'))
    fabric = relationship("Fabric", foreign_keys=[fabric_id], backref="fabric_vni_mapping")


class Site(Base):
    __tablename__ = 'site'

    id = Column(Integer, primary_key=True, autoincrement=True)
    site_name = Column(String(64), nullable=True, unique=True)
    description = Column(String(256), nullable=True)
    topology_id = Column(Integer, ForeignKey('topology.id', ondelete='CASCADE'))


class AssociationSite(Base):
    __tablename__ = 'association_site'

    id = Column(Integer, primary_key=True, autoincrement=True)
    site_id = Column(Integer, ForeignKey('site.id', ondelete='CASCADE'))
    switch_id = Column(Integer, ForeignKey('switch.id', ondelete='CASCADE'))


class ClientDeviceInfo(Base):
    __tablename__ = 'client_device_info'
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    client_name = Column(String(256), nullable=True, default=None)
    switch_sn = Column(String(128), ForeignKey('switch.sn'), nullable=False)
    mac_address = Column(String(128), nullable=False)
    ip_address = Column(String(128), nullable=True, default=None)
    ip_source = Column(String(128), nullable=True, default=None)
    manufacturer = Column(Text, nullable=True, default=None)
    state = Column(String(32), nullable=False)
    update_time = Column(DateTime, nullable=True, default=None)
    terminal_type = Column(String(256), nullable=True, default=None)
    port = Column(String(64), nullable=True, default=None)


class SnmpImportDetail(Base):
    __tablename__ = "snmp_import_detail"

    id = Column(Integer, primary_key=True, autoincrement=True)
    mgt_ip = Column(String(32), nullable=False, comment="snmp_device mgt_ip 冗余字段")
    status = Column(String(64), nullable=False, comment="success, failed, pending, running")


class SnmpDevice(Base):
    __tablename__ = 'snmp_device'
    id = Column(Integer, primary_key=True, autoincrement=True)
    mgt_ip = Column(String(32), nullable=False)
    sysname = Column(String(64), nullable=False)
    sn = Column(String(64), nullable=False)
    model = Column(String(32), nullable=True)
    version = Column(String(32), nullable=True)
    revision = Column(String(32), nullable=True)
    reachable_status = Column(SmallInteger, nullable=False, default=0)
    snmp_version = Column(
        Enum('v1', 'v2c', 'v3', name='snmp_version'),
        nullable=False
    )
    community = Column(String(32), nullable=True)
    context_name = Column(String(64), nullable=True)
    security_user = Column(String(64), nullable=True)
    security_level = Column(String(32), nullable=True)
    auth_protocol = Column(String(32), nullable=True)
    auth_key = Column(String(255), nullable=True)
    priv_protocol = Column(String(32), nullable=True)
    priv_key = Column(String(255), nullable=True)


class SnmpMetricIndex(Base):
    __tablename__ = 'snmp_metric_index'
    id = Column(Integer, primary_key=True, autoincrement=True)
    index_name = Column(String(128), nullable=False)
    model = Column(String(128), nullable=False)
    base_oid = Column(String(128), nullable=False)
    description = Column(String(256), nullable=True)
    is_internal = Column(Integer, nullable=False, default=1)  # 1 or 0


class SnmpMetric(Base):
    __tablename__ = 'snmp_metric'
    id = Column(Integer, primary_key=True, autoincrement=True)
    metric_name = Column(String(128), nullable=False)
    model = Column(String(128), nullable=False)
    base_oid = Column(String(128), nullable=False)
    # GET/WALK
    method = Column(String(32), nullable=False)
    index_from_name = Column(String(128), nullable=False)
    data_type = Column(Enum('DIMENSION', 'METRIC'), nullable=False, default='DIMENSION')
    unit = Column(String(64), nullable=True)
    description = Column(String(256), nullable=True)
    is_internal = Column(Integer, nullable=False, default=1) # 1 or 0
    mapping_type = Column(String(256), nullable=True)
    mapping_content = Column(Text, nullable=True)

    __table_args__ = (
        ForeignKeyConstraint(
            ['index_from_name', 'model'],
            ['snmp_metric_index.index_name', 'snmp_metric_index.model'],
            ondelete='CASCADE'
        ),
    )

    snmp_metric_index = relationship(
        "SnmpMetricIndex",
        primaryjoin="and_(SnmpMetric.index_from_name == SnmpMetricIndex.index_name, "
                    "SnmpMetric.model == SnmpMetricIndex.model)"
    )


# for dc
class ConfigDistributionTaskForDC(Base):
    __tablename__ = "config_distribution_task_for_dc"
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_name = Column(String(64), nullable=False)
    type = Column(Enum('underlay', 'overlay_router', 'overlay_switch', 'overlay_link', 'uplink_bm', 'uplink_cloud', 'roce'), nullable=False, default='underlay')
    task_id = Column(String(64), nullable=False)
    sn = Column(String(32), nullable=True)
    task_role = Column(String(32), nullable=True)  # SUPERSPINE/SPINE/LEAF
    task_type = Column(String(255),
                       nullable=True)  # BGP/OSPF/LINK/MLAG/OVERLAY    移除 MLAG_LEAF/MLAG_ACCESS/HOSTNAME   ==> 合并成 MLAG
    task_status = Column(Integer, nullable=True)  # 0----PENDING/  1---RUNNING/  2---SUCCEED/ 3---FAILED
    fabric_id = Column(Integer, nullable=False)
    logic_name = Column(String(128), nullable=True)
    assigned_to = Column(String(32), nullable=True)
    config_data = Column(Text, nullable=True)
    task_traceback_info = Column(Text, nullable=True)
    start_time = Column(DateTime, nullable=True)
    end_time = Column(DateTime, nullable=True)


# for campus
class ConfigDistributionTaskForCampus(Base):
    __tablename__ = "config_distribution_task_for_campus"
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_name = Column(String(64), nullable=False)
    task_id = Column(String(64), nullable=True)
    sn = Column(String(32), nullable=True)
    task_role = Column(String(32), nullable=True)  # CORE/DISTRIBUTION-CORE/ACCESS-BORDER/ACCESS
    task_type = Column(String(255), nullable=True)  # BGP/OSPF/CORE-COMMON/ACCESS-COMMON
    task_status = Column(Integer, nullable=True)  # 0----PENDING/  1---RUNNING/  2---SUCCEED/ 3---FAILED
    site_id = Column(Integer, nullable=False)
    assigned_to = Column(String(32), nullable=True)
    config_data = Column(Text, nullable=True)
    task_traceback_info = Column(Text, nullable=True)
    start_time = Column(DateTime, nullable=True)
    end_time = Column(DateTime, nullable=True)


class InventoryDB(DBCommon):

    def query_index_info(self):
        session = self.get_session()
        # q1 = session.query(PicModel).filter(PicModel.name == picname).first()
        # q2 = session.query(ConfigTem).filter(ConfigTem.pic_id == q1.nid).all()
        all_switch = session.query(Switch).all()
        return all_switch

    def get_switch_info_by_sn_match(self, sn):
        session = self.get_session()
        switchs = session.query(Switch).filter(Switch.sn.like('%' + sn + '%')).all()
        return switchs

    def get_switch_info_by_sn(self, sn):
        session = self.get_session()
        switch = session.query(Switch).filter(Switch.sn == sn).first()
        return switch

    def get_switch_info_by_mgt_ip(self, ip):
        session = self.get_session()
        switch = session.query(Switch).filter(Switch.mgt_ip == ip).first()
        return switch

    def get_switch_info_by_system_config(self, system_config_name):
        session = self.get_session()
        system_config_id = session.query(SystemConfig.id).filter(SystemConfig.config_name == system_config_name).first()
        if not system_config_id:
            return None
        switch = session.query(Switch).filter(Switch.system_config_id == system_config_id).all()
        return switch

    def get_switch_config_by_sn(self, sn):
        session = self.get_session()
        query = session.query(SwitchAutoConfig).join(SwitchSwitchAutoConfig,
                                                     SwitchAutoConfig.id == SwitchSwitchAutoConfig.switchAutoConfig_id)
        auto_configs = query.filter(SwitchSwitchAutoConfig.switch_id == sn).all()
        return auto_configs

    def get_mgt_system_config(self, session=None):
        session = session or self.get_session()
        sys_config = session.query(SystemConfig).first()
        return sys_config

    def get_license_by_sn(self, sn):
        session = self.get_session()
        license = session.query(License).filter(License.sn_num == sn).first()
        return license

    def update_status(self, sn, status):
        session = self.get_session()
        with session.begin(subtransactions=True):

            switch_info = session.query(Switch).filter(Switch.sn == sn).first()
            if switch_info:
                if switch_info.status == status:
                    return

                if switch_info.status not in \
                        [constants.SwitchStatus.REGISTERED, constants.SwitchStatus.PROVISIONING_FAILED] and status in \
                        [constants.SwitchStatus.PROVISIONING_FAILED, constants.SwitchStatus.PROVISIONING_SUCCESS]:
                    return
                else:
                    switch_info.status = status
                    if status == constants.SwitchStatus.PROVISIONING_SUCCESS:
                        switch_info.import_type = constants.ImportType.DEPLOY

    def update_version(self, sn, version, reversion):
        session = self.get_session()
        session.query(Switch).filter(Switch.sn == sn).update(
            {Switch.version: version, Switch.revision: reversion}, synchronize_session=False)
        # session will auto commit , no need to commit
        # session.commit()

    def update_lic(self, sn, key='', expr_date=None, speed='1GE', features='EE', name='', status=None):
        session = self.get_session()
        assert sn
        with session.begin(subtransactions=True):
            license = session.query(License).filter(License.sn_num == sn).first()
            if license:
                license.portal_lic = key
                license.license_expired = expr_date
                license.lic_feature = features
                license.lic_name = name
                license.speed = speed
                if status:
                    license.status = status
            else:
                license = License()
                # license.hwid = hwid
                license.sn_num = sn
                license.portal_lic = key
                license.license_expired = expr_date
                license.lic_feature = features
                license.lic_speed = speed
                license.lic_name = name
                if status:
                    license.status = status
                session.add(license)

    def update_step(self, sn, step):
        session = self.get_session()
        with session.begin(subtransactions=True):
            switch = session.query(Switch).filter(Switch.sn == sn).first()
            if switch and switch.step != step and switch.status in [constants.SwitchStatus.STAGING,
                                                                    constants.SwitchStatus.REGISTERED,
                                                                    constants.SwitchStatus.PROVISIONING_FAILED,
                                                                    constants.SwitchStatus.PROVISIONING_SUCCESS]:
                switch.step = step

    def update_lic_date(self, sn, date):
        session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(License).filter(License.sn_num == sn).update(
                {License.license_expired: date}, synchronize_session=False)

    def add_switch_log(self, sn, content, report_action='', level='debug'):
        session = self.get_session()
        with session.begin(subtransactions=True):
            try:
                switch_log = SwitchLog(sn, content, report_action, level)
                session.add(switch_log)
            except:
                pass

    def get_switch_log(self, sn, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            return session.query(SwitchLog).filter_by(switch_id=sn).order_by('create_time').all()

    def model_list(self):
        return self.get_session().query(SwitchSystemInfo).all()

    def get_lat_long(self, sn):
        session = self.get_session()
        with session.begin(subtransactions=True):
            if sn == "all":
                return session.query(SwitchGis).all()
            else:
                return session.query(SwitchGis).filter_by(sn=sn).all()

    def get_lat_long_with_ansible_status(self, ansible):
        session = self.get_session()
        with session.begin(subtransactions=True):
            if ansible == "reachable":
                return session.query(SwitchGis).join(Switch, SwitchGis.sn == Switch.sn).filter_by(
                    reachable_status=0, status='Provisioning Success').all() + \
                    session.query(SwitchGis).join(Switch, SwitchGis.sn == Switch.sn).filter_by(
                        reachable_status=0, status='Imported').all()
            elif ansible == "all":
                return session.query(SwitchGis).join(Switch, SwitchGis.sn == Switch.sn).filter_by(
                    status='Provisioning Success').all() + session.query(SwitchGis).join(Switch,
                                                                                         SwitchGis.sn == Switch.sn).filter_by(
                    status='Imported').all()
            else:
                return session.query(SwitchGis).join(Switch, SwitchGis.sn == Switch.sn).filter(
                    Switch.reachable_status != 0).filter(Switch.status == 'Provisioning Success').all() + \
                    session.query(SwitchGis).join(Switch, SwitchGis.sn == Switch.sn).filter(
                        Switch.reachable_status != 0).filter(Switch.status == 'Imported').all()

    def get_switch_status(self, sn):
        session = self.get_session()
        with session.begin(subtransactions=True):
            if sn == "all":
                return session.query(SwitchStatus).all()
            else:
                return session.query(SwitchStatus).filter_by(sn=sn).all()

    def update_switch_status(self, sn, status):
        session = self.get_session()
        with session.begin(subtransactions=True):
            for name, portInfo in status.items():
                port_status = session.query(SwitchStatus).filter(SwitchStatus.sn == sn).filter(
                    SwitchStatus.port_name == name).first()
                if port_status:
                    port_status.rc_count = portInfo["inputOct"]
                    port_status.tx_count = portInfo["outputOct"]
                    port_status.rc_drop = portInfo["inputDropOct"]
                    port_status.tx_drop = portInfo["outputDropOct"]
                    port_status.link_status = portInfo["link"]
                    port_status.link_speed = portInfo["speed"]
                    port_status.port_type = portInfo["type"]
                else:
                    port_status = SwitchStatus()
                    port_status.sn = sn
                    port_status.port_name = name
                    port_status.rc_count = portInfo["inputOct"]
                    port_status.tx_count = portInfo["outputOct"]
                    port_status.rc_drop = portInfo["inputDropOct"]
                    port_status.tx_drop = portInfo["outputDropOct"]
                    port_status.link_status = portInfo["link"]
                    port_status.link_speed = portInfo["speed"]
                    port_status.port_type = portInfo["type"]
                    session.add(port_status)

    def update_switch_lldp(self, sn, lldp):
        session = self.get_session()
        with session.begin(subtransactions=True):
            for name, portInfo in lldp.items():
                port_status = session.query(SwitchStatus).filter(SwitchStatus.sn == sn).filter(
                    SwitchStatus.port_name == name).first()
                if port_status:
                    port_status.lldp_neighbor = portInfo["lldpNeighbor"]

    def update_switch_ansible_status(self, sn, status):
        session = self.get_session()
        with session.begin(subtransactions=True):
            switch = session.query(Switch).filter(Switch.sn == sn).first()

            if switch:
                if status == 'reachable':
                    switch.reachable_status = 0
                elif status == 'un-reachable':
                    switch.reachable_status = 1
                elif status == 'unmanageable':
                    switch.reachable_status = 3
                else:
                    switch.reachable_status = 2

    def update_switch_lot(self, sn, ip, model, session=None):
        # model format like AS7326_56X to as7326_56x
        model = {key.lower(): key for (key, _) in model_platform_mapping.items()}.get(model.lower(), model)

        session = session or self.get_session()
        with session.begin(subtransactions=True):
            parking_lot = session.query(SwitchParking).filter(SwitchParking.sn == sn).first()
            if parking_lot:
                parking_lot.ip = ip
                parking_lot.last_register = func.now()
                # Prevent exceeding smallint maximum bounds
                parking_lot.register_count = min(parking_lot.register_count + 1, 32767)
                parking_lot.model = model
                if parking_lot.history_time:
                    history_time_list = parking_lot.history_time.split(';')
                    if len(history_time_list) > 12:
                        history_time_list = [history_time_list[0]] + history_time_list[-11:] + [
                            time.strftime("%Y-%m-%d %H:%M:%S")]
                    else:
                        history_time_list = history_time_list + [time.strftime("%Y-%m-%d %H:%M:%S")]
                    history_time = ';'.join(history_time_list)
                else:
                    history_time = str(parking_lot.create_time) + ';' + time.strftime("%Y-%m-%d %H:%M:%S")
                parking_lot.history_time = history_time
            else:
                switch_park_lot = SwitchParking()
                switch_park_lot.sn = sn
                switch_park_lot.ip = ip
                switch_park_lot.register_count = 1
                switch_park_lot.model = model
                switch_park_lot.last_register = func.now()
                switch_park_lot.history_time = time.strftime("%Y-%m-%d %H:%M:%S")
                session.add(switch_park_lot)

    def update_lot_hardware_id(self, ip, hardware_id):
        session = self.get_session()
        with session.begin(subtransactions=True):
            parking_lot = session.query(SwitchParking).filter(SwitchParking.ip == ip).first()
            if parking_lot:
                parking_lot.hardware_id = hardware_id

    def lock_switch(self, user, sn, session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):

            lock = session.query(UserSwitchLock).filter(UserSwitchLock.user == user, UserSwitchLock.sn == sn).first()
            if lock:
                return True

            lock = session.query(UserSwitchLock).filter(UserSwitchLock.sn == sn).first()
            if lock:
                return False

            lock = UserSwitchLock(user=user, sn=sn)
            session.add(lock)

    def get_switch_lock(self, user, sn, session=None):
        session = session or self.get_session()

        lock = session.query(UserSwitchLock).filter(UserSwitchLock.sn == sn).first()
        if not lock:
            return True

        user = session.query(UserSwitchLock).filter(UserSwitchLock.user == user, UserSwitchLock.sn == sn).first()
        if user:
            return True

        return False

    def add_deployed_security(self, sn, session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            deployed_security = session.query(DeployedSecuritySwitch).filter(DeployedSecuritySwitch.sn == sn).first()
            if not deployed_security:
                deployed_security = DeployedSecuritySwitch(sn=sn)
                session.add(deployed_security)

    def invalid_deployed_security(self, sn, session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            deployed_security = session.query(DeployedSecuritySwitch).filter(DeployedSecuritySwitch.sn == sn).first()
            if deployed_security:
                invalid_times = deployed_security.invalid_times + 1 if deployed_security.invalid_times else 1
                deployed_security.invalid_times = invalid_times

    def clear_deployed_security(self, sn, session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            deployed_security = session.query(DeployedSecuritySwitch).filter(DeployedSecuritySwitch.sn == sn).first()
            if deployed_security:
                deployed_security.invalid_times = 0

    def insert_or_update_back(self, ip, config, back_type, sn, session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            back = session.query(SwitchConfigBackup).filter_by(sn=sn).first()
            compressed_config = zlib.compress(config.encode())
            if back:
                back.config = compressed_config
                back.back_up_type = back_type
                back.ip = ip
                flag_modified(back, "config")
            else:
                switch_back = SwitchConfigBackup(ip=ip, sn=sn, config=compressed_config, back_up_type=back_type)
                session.add(switch_back)

    def get_switch_back(self, ip, session=None):
        session = session or self.get_session()
        switch_back = session.query(SwitchConfigBackup.config).filter_by(ip=ip).first()
        return zlib.decompress(switch_back.config).decode() if switch_back else None

    def get_switch_back_by_sn(self, sn, session=None):
        session = session or self.get_session()
        switch_back = session.query(SwitchConfigBackup.config).filter_by(sn=sn).first()
        return zlib.decompress(switch_back.config).decode() if switch_back else None

    def get_switch_back_sn(self, sn, session=None):
        session = session or self.get_session()
        # return golden config if existed
        switch_back = session.query(SwitchConfigSnapshot.archive_config).filter_by(sn=sn, tag='GOLDEN_CONFIG').first()
        if switch_back:
            return switch_back[0]

        # return latest config if no golden config
        switch_back = session.query(SwitchConfigBackup.config).filter_by(sn=sn).first()
        return zlib.decompress(switch_back.config).decode() if switch_back else None

    def insert_or_update_agent_conf(self, sn, conf_str, session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            agent_conf = session.query(SwitchAgentConf).filter_by(sn=sn).first()
            compressed_conf = zlib.compress(conf_str.encode())
            if agent_conf:
                agent_conf.conf = compressed_conf
            else:
                session.add(SwitchAgentConf(sn=sn, conf=compressed_conf))

    def get_switch_agent_conf(self, sn, session=None):
        session = session or self.get_session()
        agent_conf = session.query(SwitchAgentConf).filter_by(sn=sn).first()
        return zlib.decompress(agent_conf.conf).decode() if agent_conf else None

    def update_lic_new(self, sn_num, model, key='', license_expired='', lic_feature='EE', lic_speed='1GE',
                       status='Unknown'):
        session = self.get_session()
        assert sn_num
        with session.begin(subtransactions=True):
            license = session.query(model).filter(model.sn_num == sn_num).first()
            if key == 'None':
                license.portal_lic = ''
            elif key != 'None' and key:
                license.portal_lic = key
            if license_expired:
                license.license_expired = license_expired

            license.lic_feature = lic_feature
            license.lic_speed = lic_speed
            license.status = status
        session.merge(license)

    def get_switch_groups(self, sn, session=None):
        session = session or self.get_session()
        groups = session.query(AssociationGroup) \
            .outerjoin(Group, AssociationGroup.group_name == Group.group_name) \
            .filter(AssociationGroup.switch_sn == sn).all()
        return groups

    def get_group_switchs(self, group_name, session=None):
        session = session or self.get_session()
        switches = session.query(AssociationGroup) \
            .outerjoin(Switch, AssociationGroup.switch_sn == Switch.sn) \
            .filter(AssociationGroup.group_name == group_name).all()
        return switches

    def get_group_switchs_new(self, group_name, session=None):
        session = session or self.get_session()
        switches = session.query(Switch).outerjoin(AssociationGroup, AssociationGroup.switch_sn == Switch.sn).filter(
            AssociationGroup.group_name == group_name).all()
        return switches

    def get_group_switches_with_license(self, group_name, session=None):
        session = session or self.get_session()
        switches = session.query(Switch, License).outerjoin(AssociationGroup, AssociationGroup.switch_sn == Switch.sn) \
            .outerjoin(License, License.sn_num == Switch.sn).filter(AssociationGroup.group_name == group_name).all()
        return switches

    def get_group_switchs_remove(self, group_name, session=None):
        session = session or self.get_session()
        switches = session.query(Switch).outerjoin(AssociationGroup, AssociationGroup.switch_sn == Switch.sn).filter(
            AssociationGroup.group_name == group_name, AssociationGroup.delete == False).all()
        return switches

    def lock_job(self, job_id, host):
        self.clear_session()
        session = self.get_session()
        try:
            LOG.debug('lock task %s, host %s', job_id, host)
            task_lock = TaskLock(name=job_id, host=host)
            session.add(task_lock)
            lock = self.get_model(TaskLock, filters={'name': [job_id]})
            LOG.debug('lock task %s, host %s success, lock %s', job_id, host, lock)
        except Exception as e:
            LOG.error('error in lock job %s', str(e))
            query_task_lock = session.query(TaskLock).filter(TaskLock.name == job_id).first()
            LOG.warning('lock task %s, host %s failed get exit lock %s', job_id, host, query_task_lock)
            if query_task_lock and query_task_lock.host == host:
                return True
            return False
        self.clear_session()

        return True

    def release_job(self, job_id, host):
        self.clear_session()
        session = self.get_session()
        try:
            db_job = session.query(TaskLock).filter(TaskLock.name == job_id).one()
            LOG.debug('release job %s', db_job)
            if db_job.host == host:
                session.delete(db_job)
                session.flush()
                # session.query(TaskLock).filter(TaskLock.name == job_id).delete(synchronize_session=False)
                return True
            else:
                LOG.debug('delete task %s failed host %s, db_host %s', job_id, host, db_job.host)
        except exc.NoResultFound as e:
            LOG.warning('no result found for task %s', job_id)
            return False
        except exc.MultipleResultsFound as e:
            return False
        except Exception:
            return False
        finally:
            self.clear_session()

        return False

    def update_group_task_status(self, name, status):
        self.update_model(GroupTask, filters={'name': [name]}, updates={GroupTask.status: status})

    def get_model_port_mapping(self, model, session=None):
        # ModelPhysicPort
        session = session or self.get_session()
        port_mapping = {}
        for type in ['ge', 'te', 'qe', 'xe']:
            ports = session.query(ModelPhysicPort).filter(ModelPhysicPort.platform_name == model,
                                                          ModelPhysicPort.port_type == type).all()
            port_num = []
            if ports:
                for port in ports:
                    port_num.append(int(port.port_name.split('/')[2]))
                port_num.sort()
                port_mapping.update({type + '_start': port_num[0], type + '_end': port_num[-1]})
        return port_mapping

    def get_switch_logs(self, sn, session=None):
        db_session = session or self.get_session()
        switch_logs = db_session.query(SwitchLog) \
            .filter(SwitchLog.switch_id == sn).order_by(SwitchLog.create_time.desc(),
                                                        SwitchLog.id.desc()).limit(DISPLAY_LOG_CYCLE).all()
        return reversed(switch_logs)

    def get_next_decom_num(self, sn, session=None):
        session = session or self.get_session()
        decoms = session.query(Switch).filter(Switch.sn.like('%' + sn + '_DECOM' + '%')).all()
        if not decoms:
            return 0
        max_num = 0
        for decom in decoms:
            splits = decom.sn.replace('_RMA', '').split('_DECOM_')
            if len(splits) > 1:
                num = int(splits[1])
                if num > max_num:
                    max_num = num

        return max_num + 1

    def get_next_decom_ip_num(self, ip, session=None):
        session = session or self.get_session()
        decoms = session.query(Switch).filter(Switch.mgt_ip.like('%' + ip + '_DECOM' + '%')).all()
        if not decoms:
            return 0
        max_num = 0
        for decom in decoms:
            splits = decom.sn.replace('_RMA', '').split('_DECOM_')
            if len(splits) > 1:
                num = int(splits[1])
                if num > max_num:
                    max_num = num

        return max_num + 1

    def get_all_switch_versions(self, session=None):
        session = session or self.get_session()
        versions = session.query(Switch.version) \
            .filter(
            Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS, constants.SwitchStatus.IMPORTED])) \
            .distinct().all()
        versions = [version[0] for version in versions]
        return versions

    def add_switch_to_group(self, sn, group_name, session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            ass_group = session.query(AssociationGroup).filter_by(switch_sn=sn, group_name=group_name).first()
            if ass_group:
                return False

            ass_group = AssociationGroup(switch_sn=sn, group_name=group_name)
            session.add(ass_group)

    def del_switch_on_group(self, sn, group_name, session=None):
        session = session or self.get_session()
        session.query(AssociationGroup).filter_by(switch_sn=sn, group_name=group_name).delete(synchronize_session=False)

    def get_switch_local_license(self, sn, session=None):
        session = session or self.get_session()
        local_license = session.query(License).filter(License.sn_num == sn, License.local_lic.isnot(None)).first()
        if local_license:
            return local_license.local_lic

    def unstage_switch(self, sn, session=None):
        session = session or self.get_session()
        with session.begin():
            switch = session.query(Switch).filter_by(sn=sn).first()
            if not switch:
                return 'switch not found!', False

            switch.enable = False
            switch.status = constants.SwitchStatus.CONFIGURED

            # remove vpn keys in db
            session.query(VpnConfig).filter_by(sn=sn).delete(synchronize_session=False)

        return 'success unstage switch %s' % sn, True

    def update_switch_config(self, sn, content, session=None):
        session = session or self.get_session()
        with session.begin():
            switch = session.query(Switch).filter_by(sn=sn).first()
            if not switch:
                return

            # remove global
            for config in switch.configs:
                if config.type == 'global':
                    switch.configs.remove(config)

                # update site config
                if config.type == 'site':
                    config.config = content

    def clear_task_locks(self):
        session = self.get_session()
        session.query(TaskLock).delete(synchronize_session=False)

    def update_association_group(self, switch_sn, group_name_list):
        db_session = self.get_session()
        for group in group_name_list:
            if group and db_session.query(Group).filter(Group.group_name == group).first():
                association_group = db_session.query(AssociationGroup).filter(
                    AssociationGroup.switch_sn == switch_sn,
                    AssociationGroup.group_name == group).first()
                if not association_group:
                    association_group = AssociationGroup(switch_sn=switch_sn, group_name=group)
                    db_session.add(association_group)
                    db_session.flush()

    def get_all_system_config(self):
        db_session = self.get_session()
        return db_session.query(SystemConfig).all()

    def get_system_config_by_config_name(self, config_name):
        db_session = self.get_session()
        return db_session.query(SystemConfig).filter(SystemConfig.config_name == config_name).first()

    def get_system_config_by_config_id(self, config_id):
        db_session = self.get_session()
        return db_session.query(SystemConfig).filter(SystemConfig.id == config_id).first()

    def get_global_system_config(self):
        return self.get_system_config_by_config_name(constants.GLOBAL_CONFIG_TAG)

    def get_system_config_by_sn(self, sn):
        switch = self.get_switch_info_by_sn(sn)
        if switch:
            return self.get_system_config_by_config_id(switch.system_config_id)
        else:
            return self.get_global_system_config()

    def get_system_config_by_mgt_ip(self, ip):
        if not ip:
            return self.get_global_system_config()
        switch = self.get_switch_info_by_mgt_ip(ip)
        if switch:
            return self.get_system_config_by_config_id(switch.system_config_id)
        else:
            return self.get_global_system_config()

    def delete_system_config_by_config_name(self, config_name):
        if config_name == constants.GLOBAL_CONFIG_TAG:
            return False, 'Global config can not be deleted!'
        db_session = self.get_session()
        config = db_session.query(SystemConfig).filter(SystemConfig.config_name == config_name)
        if db_session.query(Switch).filter(Switch.system_config_id == config.first().id).all():
            return False, 'Switch exists under the system config, please remove it and then delete it.'
        db_session.query(Switch).filter(Switch.system_config_id == config.first().id).update(
            {Switch.system_config_id: self.get_global_system_config().id}, synchronize_session=False)
        with db_session.begin(subtransactions=True):
            config.delete()
            system_config_file_path = os.path.realpath(
                os.path.join(constants.AMPCON_BASE_DIR, 'config_gen', 'security', config_name))
            if system_config_file_path.startswith(
                    os.path.join(constants.AMPCON_BASE_DIR, 'config_gen', 'security')) and os.path.exists(
                system_config_file_path):
                shutil.rmtree(system_config_file_path)
            else:
                raise (ValueError('Security config under System config failed to be removed!'))
        return True, 'Delete system config successfully!'

    def update_system_config_switch_operation(self, switch_sn_list, system_config_id):
        db_session = self.get_session()
        db_session.query(Switch).filter(Switch.sn.in_(switch_sn_list)).update(
            {Switch.system_config_id: system_config_id}, synchronize_session=False)
        return True

    def update_system_config_switch_operation_by_id(self, switch_id_list, system_config_id):
        db_session = self.get_session()
        db_session.query(Switch).filter(Switch.id.in_(switch_id_list)).update(
            {Switch.system_config_id: system_config_id}, synchronize_session=False)
        return True

    def update_switch_montior(self, name, device_type, interfaces=None, lldp=None, modules=None, mac=None):
        port = "9339"
        session = self.get_session()
        with session.begin(subtransactions=True):
            # switch类型
            if device_type == 1:
                # 根据swicth model配置ai是否开启
                query_switch = session.query(Switch).filter(Switch.sn == name).first()
                ai = query_switch and query_switch.platform_model in constants.SWITCH_MODEL_ENABLE_ECN_PFC
                if query_switch and not ai:
                    query_switch.gnmi_ai = False
                else:
                    query_switch.gnmi_ai = True

                query_target = session.query(MonitorTarget).filter(MonitorTarget.sn == name).first()
                if query_target:
                    enable_list = [int(digit) for digit in reversed(bin(query_target.enable)[2:].zfill(4))]
                else:
                    enable_list = [1, 1, 1, 0]  # interface, components lldp默认开  ai 默认关

                if interfaces is not None:
                    enable_list[0] = int(interfaces)
                if lldp is not None:
                    enable_list[1] = int(lldp)
                if modules is not None:
                    enable_list[2] = int(modules)
                enable_list[3] = int(ai)

                enable_value = int(''.join(map(str, reversed(enable_list))), 2)
                if query_target:
                    query_target.enable = enable_value
                else:
                    target = MonitorTarget(sn=name, port=port, device_type=device_type, enable=enable_value)
                    session.add(target)
            elif device_type == 2:
                # otn类型
                query_target = session.query(MonitorTarget).filter(MonitorTarget.name == name).first()
                if not query_target:
                    target = MonitorTarget(name=name, device_type=device_type, mac=mac)
                    session.add(target)
                else:
                    query_target.mac = mac
            elif device_type == 3:
                # server类型
                query_target = session.query(MonitorTarget).filter(MonitorTarget.name == name).first()
                if not query_target:
                    target = MonitorTarget(name=name, device_type=device_type)
                    session.add(target)

    def update_mac_addr(self, sn, mac_addr):
        session = self.get_session()
        session.query(Switch).filter(Switch.sn == sn).update({Switch.mac_addr: mac_addr}, synchronize_session=False)

    def delete_switch_montior(self, name, device_type):
        session = self.get_session()
        session.query(MonitorTarget).filter(MonitorTarget.name == name,
                                            MonitorTarget.device_type == device_type).delete(synchronize_session=False)

    def get_fabric_id_by_name(self, name):
        session = self.get_session()
        fabric = session.query(Fabric).filter(Fabric.fabric_name == name).first()
        return fabric.id if fabric else None


inven_db = InventoryDB()


def clear_timeout_job_lock():
    session = inven_db.get_session()
    locks = session.query(TaskLock).all()
    now = datetime.datetime.now()
    for lock in locks:
        lock_update_date = lock.modified_time
        dl = now - lock_update_date
        if dl.total_seconds() > 3600:
            LOG.info('release lock %s', lock)
            session.delete(lock)


if __name__ == '__main__':
    from server import cfg

    version = 0.1

    cfg.CONF(default_config_files=['../../automation.ini'])
    # session = db.get_session()
    # name = '%' + 'tset' + '%'
    # print name
    # session.query(TaskStatus).filter(TaskStatus.name.like('aaa')).delete(synchronize_session=False)
    # task_status = TaskStatus(name='taaa', start_date=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), status='missed')
    # db.insert_or_update(task_status, 'name')
    # task_status = db.get_model(TaskStatus, filters={'name': ['taaa']})
    # print task_status.make_dict()['start_date']
    session = inven_db.get_session()
    # models = (Switch, License, SwitchSystemInfo,)
    # alls = session.query(*models).outerjoin(License, Switch.sn == License.sn_num)\
    #     .outerjoin(SwitchSystemInfo, Switch.platform_model == SwitchSystemInfo.model)\
    #     .filter(License.status == None)\
    #     .all()
    # for all in alls:
    #     print all.Switch

    # subquery = session.query(AssociationGroup.switch_sn).filter(AssociationGroup.group_name == 'test').subquery()
    # switches = session.query(Switch).filter(Switch.sn.in_(subquery)).all()
    # for switch in switches:
    #     print switch.sn
    # print switches
    # data = []
    # for result in alls:
    #     first_slice = result[0].make_dict()
    #     for obj in result[1:]:
    #         if not obj:
    #             continue
    #         prefix = obj.__class__.__name__.lower() + '_'
    #         first_slice.update(dict(((prefix + col.name, getattr(obj, col.name)) for col in obj.__table__.columns
    #                                  if getattr(obj, col.name))))
    #     data.append(first_slice)
    # print data
    # db_session = inven_db.get_session()
    # deploy_versions = db_session.query(Switch.version).filter(
    #     Switch.status == 'Provisioning Success').distinct().all()
    # print deploy_versions
    # switches = inven_db.get_group_switches_with_license('test')
    # switches_dict = []
    # for switch in switches:
    #     switch_dict = switch.Switch.make_dict()
    #     license_dict = {}
    #     if switch.License:
    #         license_dict = switch.License.make_dict()
    #     switch_dict.update(license_dict)
    #     switch_dict['ori'] = True
    #     switches_dict.append(switch_dict)
    # print switches_dict
    # agent_conf = inven_db.get_switch_agent_conf('test231')
    # print agent_conf
    # content = str(content)
    # snopshot = SwitchConfigSnapshot(sn='EC1713003158', snapshot_time=datetime.datetime.utcnow(), archive_config=content)
    # session.add(snopshot)
    # session.flush()

    # switch = inven_db.get_switch_info_by_sn('6XTVNK2')
    # print switch
    print(inven_db.update_model(Switch, {'sn': ['CN0JDFTFDND0083M0100']}, {'address': 'tj'}))
