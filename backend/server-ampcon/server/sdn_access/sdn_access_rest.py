import logging

import json
from webob import Response
import os
import mimetypes

from ryu.base import app_manager
from ryu.controller import ofp_event
from ryu.controller import dpset
from ryu.controller.handler import MAIN_DISPATCHER
from ryu.controller.handler import set_ev_cls
from ryu.ofproto import ofproto_v1_3
from ryu.lib import ofctl_v1_3
from ryu.app.wsgi import ControllerBase, WSGIApplication
from host_manager import HostAccessController

# REST API
############# Configure access controller##########
#
#

import re, socket

def is_mac_valid(x):
    if re.match("[0-9a-f]{2}([-:])[0-9a-f]{2}(\\1[0-9a-f]{2}){4}$", x.lower()):
        return True
    else:
        return False

def is_dpid_valid(x):
    return True

def is_policy_valid(x):
    return True

class AccessController(ControllerBase):
    def __init__(self, req, link, data, **config):
        super(AccessController, self).__init__(req, link, data, **config)
        self.host_access_controller = data['HostAccessController']

    def add_config_switch(self, req, **_kwargs):
        try:
            new_config_dpid = eval(req.body)
            if not is_dpid_valid(new_config_dpid):
                return Response(status=400)
            self.host_access_controller.add_new_configured_switch(new_config_dpid)

        except SyntaxError:
            return Response(status=400)

        return Response(status=200,content_type='application/json',
                    body=json.dumps({'status':'success'}))

    def del_config_switch(self, req, **_kwargs):
        try:
            config_dpid = eval(req.body)
            if not is_dpid_valid(config_dpid):
                return Response(status=400)
            self.host_access_controller.del_configured_switch(config_dpid)

        except SyntaxError:
            return Response(status=400)

        return Response(status=200, content_type='application/json',
                        body=json.dumps({'status': 'success'}))

    def add_bypass_policy(self, req, **_kwargs):
        try:
            new_bypass_policy = eval(req.body)
            if not is_policy_valid(new_bypass_policy):
                return Response(status=400)
            self.host_access_controller.add_bypass_policy(new_bypass_policy)

        except SyntaxError:
            return Response(status=400)

        return Response(status=200, content_type='application/json',
                        body=json.dumps({'status': 'success'}))

    def del_bypass_policy(self, req, **_kwargs):
        try:
            bypass_policy = eval(req.body)
            if not is_policy_valid(bypass_policy):
                return Response(status=400)
            self.host_access_controller.del_bypass_policy(bypass_policy)

        except SyntaxError:
            return Response(status=400)

        return Response(status=200, content_type='application/json',
                        body=json.dumps({'status': 'success'}))

    def add_host(self, req, **_kwargs):
        try:
            new_host = eval(req.body)
            if not is_mac_valid(new_host['smac']):
                return Response(status=400)
            self.host_access_controller.add_new_host(new_host)

        except SyntaxError:
            return Response(status=400)

        return Response(status=200, content_type='application/json',
                        body=json.dumps({'status': 'success'}))

    def update_host(self, req, **_kwargs):
        try:
            new_host = eval(req.body)
            if not is_mac_valid(new_host['smac']):
                return Response(status=400)
            self.host_access_controller.update_single_host_flow(new_host)

        except SyntaxError:
            return Response(status=400)

        return Response(status=200, content_type='application/json',
                        body=json.dumps({'status': 'success'}))

    def del_host(self, req, **_kwargs):
        try:
            new_host = eval(req.body)
            if not is_mac_valid(new_host['smac']):
                return Response(status=400)
            self.host_access_controller.del_host(new_host)

        except SyntaxError:
            return Response(status=400)

        return Response(status=200, content_type='application/json',
                        body=json.dumps({'status': 'success'}))

    def show_all_dpid(self, req, **_kwargs):
        return Response(status=200, content_type='application/json',
                        body=json.dumps(self.host_access_controller.datapaths.keys()))

    def show_configured_sw(self, req, **_kwargs):
        list_sw_configured = []
        for sw in self.host_access_controller.switch_configured.values():
            list_sw_configured.append(sw.make_dict())
        return Response(status=200, content_type='application/json',
                        body=json.dumps(list_sw_configured))

    def show_configured_host(self, req, **_kwargs):
        list_host_configured = []
        for host in self.host_access_controller.host_configured.values():
            list_host_configured.append(host.make_dict())
        return Response(status=200, content_type='application/json',
                        body=json.dumps(list_host_configured))

    def show_host_flow(self, req, **_kwargs):
        host_flow = {}
        return Response(status=200, content_type='application/json',
                        body=json.dumps(self.host_access_controller.get_flows()))

    def sync_switch_list(self, req, **_kwargs):
        #need not implementation, each switch connecting, will cause list update
        pass

    def sync_policy_list(self, req, **_kwargs):
        self.host_access_controller.sync_bypass_policy()

    def sync_all_mac_list(self, req, **_kwargs):
        self.host_access_controller.sync_all_switch_mac()

    def sync_all_switch_status(self, req, **_kwargs):
        self.host_access_controller.sync_all_switch_status()

class AccessControlRestApi(app_manager.RyuApp):
    OFP_VERSIONS = [ofproto_v1_3.OFP_VERSION]
    _CONTEXTS = {
        'wsgi': WSGIApplication,
        'access_control': HostAccessController,
    }

    def __init__(self, *args, **kwargs):
        super(AccessControlRestApi, self).__init__(*args, **kwargs)
        HostAccessController = kwargs['access_control']
        wsgi = kwargs['wsgi']
        self.waiters = {}
        self.data = {}
        self.data['waiters'] = self.waiters
        self.data['HostAccessController'] = HostAccessController

        wsgi.registory['AccessController'] = self.data
        mapper = wsgi.mapper

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/add_config_switch',
                       controller=AccessController, action='add_config_switch',
                       conditions=dict(method=['POST']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/del_config_switch',
                       controller=AccessController, action='del_config_switch',
                       conditions=dict(method=['POST']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/add_bypass_policy',
                       controller=AccessController, action='add_bypass_policy',
                       conditions=dict(method=['POST']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/del_bypass_policy',
                       controller=AccessController, action='del_bypass_policy',
                       conditions=dict(method=['POST']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/add_host',
                       controller=AccessController, action='add_host',
                       conditions=dict(method=['POST']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/update_host',
                       controller=AccessController, action='update_host',
                       conditions=dict(method=['POST']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/del_host',
                       controller=AccessController, action='del_host',
                       conditions=dict(method=['POST']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/show_all_dpid',
                       controller=AccessController, action='show_all_dpid',
                       conditions=dict(method=['GET']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/show_configured_sw',
                       controller=AccessController, action='show_configured_sw',
                       conditions=dict(method=['GET']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/show_configured_host',
                       controller=AccessController, action='show_configured_host',
                       conditions=dict(method=['GET']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/show_host_flow',
                       controller=AccessController, action='show_host_flow',
                       conditions=dict(method=['GET']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/sync_switch_list',
                       controller=AccessController, action='sync_switch_list',
                       conditions=dict(method=['GET']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/sync_policy_list',
                       controller=AccessController, action='sync_policy_list',
                       conditions=dict(method=['GET']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/sync_all_mac_list',
                       controller=AccessController, action='sync_all_mac_list',
                       conditions=dict(method=['GET']))

        mapper.connect('HostAccessController', '/v1.0/HostAccessController/sync_all_switch_status',
                       controller=AccessController, action='sync_all_switch_status',
                       conditions=dict(method=['GET']))