#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: cc_test
@file: __init__.py
@function:
@time: 2022/7/25 18:14
"""
import os
import sys

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(BASE_DIR)

import pymysql
from celery import Celery
from celery.app.control import Control

pymysql.install_as_MySQLdb()

my_celery_app = Celery(__file__)
my_celery_app.config_from_object("celery_app.celeryconfig")
my_celery_app.autodiscover_tasks(['celery_app'])
celery_control = Control(app=my_celery_app)

