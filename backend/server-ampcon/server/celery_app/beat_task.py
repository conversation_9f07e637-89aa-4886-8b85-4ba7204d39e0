#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: ampcon
@file: beat_task.py
@function:
@time: 2022/8/24 13:39
"""
from server.collect.rma_collect import collect_backup_config_all
from server.util import utils, dcp920_util, fmt_util, check_license_util, prometheus_util, \
    virtual_resource_collect_util, ap_util, fabric_topology_util, monitor_client_util
from server.util.env_util import ampcon_pro_type
import cfg
from celery_app import my_celery_app
from celery_app.automation_task import AmpConBaseTask
from server.db.redis_common import AutoRenewalRedisLock, LockAcquisitionError
import time

import logging

LOG = logging.getLogger(__name__)


@my_celery_app.task(name="beat_update_db_license_count", base=AmpConBaseTask)
def beat_update_db_license_count(**kwargs):
    utils.update_db_license_count()


@my_celery_app.task(name="beat_collect_backup_config_all", base=AmpConBaseTask)
def beat_collect_backup_config_all(**kwargs):
    if cfg.CONF.vpn_keepalive and cfg.CONF.rma_interval > 0:
        collect_backup_config_all()


@my_celery_app.task(name="beat_update_vpn_client_status")
def beat_update_vpn_client_status(**kwargs):
    if cfg.CONF.vpn_enable:
        utils.update_vpn_client_status()
    mac_info_list = prometheus_util.query_interface_mac_address()
    dhcp_snooping_list = prometheus_util.query_dhcp_snooping_info()
    # merge by mac address and port
    for mac_info in mac_info_list:
        for dhcp_snooping in dhcp_snooping_list:
            if mac_info['mac_address'] == dhcp_snooping['mac_address'] and mac_info['port'] == dhcp_snooping['port'] and mac_info['sn'] == dhcp_snooping['sn']:
                mac_info.update(dhcp_snooping)
                break
    prometheus_util.update_client_data(mac_info_list)

    if ampcon_pro_type == "ampcon-campus":
        sn_list = prometheus_util.query_snmp_sn_list()
        utils.update_snmp_device_status(sn_list)


@my_celery_app.task(name="beat_update_cpu_mem_record")
def beat_update_cpu_mem_record(**kwargs):
    utils.update_cpu_mem_record()


@my_celery_app.task(name="beat_update_alarm_logs_read_tag")
def beat_update_alarm_logs_read_tag(**kwargs):
    utils.update_alarm_logs_read_tag()


@my_celery_app.task(name="beat_check_license_expire_time")
def beat_check_license_expire_time(**kwargs):
    check_license_util.beat_check_license_expire_time()


@my_celery_app.task(name="beat_sync_virtual_resource_info")
def beat_virtual_resource_info(**kwargs):
    virtual_resource_collect_util.sycn_cloud_resources()


@my_celery_app.task(name="beat_sync_dcp920_device_info")
def beat_sync_dcp920_device_info(**kwargs):
    dcp920_util.beat_sync_dcp920_device_info_all()


@my_celery_app.task(name="beat_update_host_info")
def beat_update_host_info(**kwargs):
    utils.update_host_info()


@my_celery_app.task(name="beat_sync_fmt_device_info")
def beat_sync_fmt_device_info(**kwargs):
    fmt_util.beat_sync_fmt_device_info_all()


@my_celery_app.task(name="beat_sync_dcs_device_info")
def beat_sync_dcs_device_info(**kwargs):
    fmt_util.beat_sync_dcs_device_info_all()


@my_celery_app.task(name="beat_count_alarms_info")
def beat_count_alarms_info(**kwargs):
    utils.beat_count_alarms_info()


@my_celery_app.task(name="beat_upgrade_firmware_task")
def beat_upgrade_firmware_task(**kwargs):
    scheduled = True
    if kwargs.get('immediately'):
        scheduled = False
    ap_util.smb_upgrade_api(kwargs.get('sn'), kwargs.get('image_name'), scheduled=scheduled)


@my_celery_app.task(name="beat_upgrading_result_task")
def beat_upgrading_result_task(**kwargs):
    ap_util.smb_upgrading_result()


@my_celery_app.task(name="beat_update_module_link")
def beat_update_module_link(**kwargs):
    prometheus_util.update_module_link()
    

@my_celery_app.task(name="beat_check_overlay_config")
def beat_check_overlay_config(**kwargs):
    """
        任务周期 10S 
        通过自动续期锁 检查是否有任务在执行, 无任务则执行 否则退出
    """
    auto_lock = AutoRenewalRedisLock("check_overlay_config", ttl_ms=10 * 1000, renewal_interval_s=2)  ## 未续期则自动过期 当做任务结束
    try:
        # 尝试获取锁（失败则抛出异常）
        auto_lock.acquire_or_raise()
        print("check_overlay_config lock acquired. Doing task...")
        # time.sleep(25)  # 超过初始TTL，但会自动续期
        fabric_topology_util.check_overlay_config()
        print("check_overlay_config task complete.")
    except LockAcquisitionError as e:
        print(f"check_overlay_config task is running, skip")
    finally:
        # 确保锁被释放
        auto_lock.release()

@my_celery_app.task(name="beat_check_uplink_task")
def beat_check_uplink_task(**kwargs):
    fabric_topology_util.check_uplink_task_status()


@my_celery_app.task(name='beat_delete_timeout_wireless_client_data_task')
def beat_delete_timeout_wireless_client_data_task(**kwargs):
    LOG.info("开始执行清理无线终端数据...")
    return monitor_client_util.delete_wireless_client_timeout()
