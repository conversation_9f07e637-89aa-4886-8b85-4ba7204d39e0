#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: cc_test
@file: my_tasks.py
@function:
@time: 2022/7/26 15:48
"""

from abc import ABC
import datetime as dt
from datetime import timezone
import json
import logging
from celery import Task
from celery import schedules
from celery_app.celeryconfig import beat_dburi
from celery_app.utils.session import session_cleanup
from celery_app.utils.models import PeriodicTask, IntervalSchedule, CrontabSchedule, PeriodicTaskChanged
from server.db.models.automation import AutomationTask
from celery_app.utils.schedulers import session_manager
from celery_app import celery_control
from server.db.db_common import DBCommon
from celery.signals import task_failure

LOG = logging.getLogger(__name__)


@task_failure.connect
def task_failure_notify(task_id, *args, **kwargs):
    AmpConBaseTask.update_task_with_failed(task_id)


class AmpConBaseTask(Task, ABC):
    def __init__(self, *args, **kwargs):
        super(AmpConBaseTask, self).__init__(*args, **kwargs)
    
    def before_start(self, task_id, args, kwargs):
        """Handler called before the task starts.

        .. versionadded:: 5.2

        Arguments:
            task_id (str): Unique id of the task to execute.
            args (Tuple): Original arguments for the task to execute.
            kwargs (Dict): Original keyword arguments for the task to execute.

        Returns:
            None: The return value of this handler is ignored.
            
            task_id = sa.Column(sa.String(255), primary_key=True, unique=True)
            task_name = sa.Column(sa.String(255))
            schedule_type = sa.Column(sa.String(255))
            sn = sa.Column(sa.String(255))
            group = sa.Column(sa.String(64))
            task_status = sa.Column(sa.String(32))
            task_process = sa.Column(sa.Integer)
            retry_times = sa.Column(sa.Integer)
            args = sa.Column(sa.String(255), default='[]')
            kwargs = sa.Column(sa.String(255), default='{}')
            create_time = sa.Column(sa.DateTime(timezone=True))
            update_time = sa.Column(sa.DateTime(timezone=True))
        """
        
        create_time = update_time = dt.datetime.now(timezone.utc).replace(tzinfo=None)
        tmp_name = kwargs.get("celery_task_name", "")
        task_name = tmp_name if tmp_name else self.name
        sn = kwargs.get("celery_sn", "")
        group = kwargs.get("celery_group", "")
        schedule_type = kwargs.get("celery_type", "DIRECT")
        
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            current_task = session.query(AutomationTask).filter(AutomationTask.task_name == task_name,  AutomationTask.task_status == 'running').first()
            if current_task:
                """
                    @2022-09-20
                    1.the self.AsyncResult(current_task.task_id).state not update when server outages or service restarts.
                    2.the states must be always started.
                    3.the messages return scheduled queues when service restarted and task_reject_on_worker_lost.
                    4.so we not need to purge all message in queues and send task again, it's great risk.
                    5.This prevents message loss or duplication when server outages or service restarts.
                    
                    # if self.AsyncResult(current_task.task_id).state == "STARTED":
                    #     raise ValueError("The task: {} is already running!".format(task_name))
                    # else:
                    #     session.delete(current_task)
                """
                
                if current_task.task_id == task_id:
                    current_task.retry_times += 1
                    return
                else:
                    raise ValueError("The task: {} is already running!".format(task_name))

            current_task_existed = session.query(AutomationTask).filter(AutomationTask.task_id == task_id).first()
            if current_task_existed:
                raise ValueError("The task: {} is already exists!".format(task_name))

            if session.query(AutomationTask).filter(AutomationTask.task_name.startswith("config-distribute-"),  AutomationTask.task_status == 'running', AutomationTask.sn == sn).first():
                LOG.warning("The config distribute task contains switch: {} is already running by other task!".format(task_name))
                raise self.retry(exc=ValueError("The config distribute task contains switch: {} is already running by other task!".format(task_name)), countdown=60, max_retries=10)

            model = AutomationTask(task_id=task_id, task_name=task_name if task_name else self.name,
                                   schedule_type=schedule_type, sn=sn,
                                   group=group, task_status="running", task_process=0, retry_times=0,
                                   args=str(args),
                                   kwargs=str(kwargs), create_time=create_time, modified_time=update_time)
            session.add(model)
    
    def update_state(self, task_id=None, state=None, meta=None, **kwargs):
        """Update task state.


        Arguments:
            task_id (str): Id of the task to update.
                Defaults to the id of the current task.
            state (str): New state.
            meta (Dict): State meta-data.
        """
        super().update_state()
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            session.query(AutomationTask).filter(AutomationTask.task_id == task_id).update(
                {AutomationTask.task_status: state,
                 AutomationTask.modified_time: dt.datetime.now(timezone.utc).replace(tzinfo=None)})

    def on_success(self, retval, task_id, args, kwargs):
        """Success handler.

        Run by the worker if the task executes successfully.

        Arguments:
            retval (Any): The return value of the task.
            task_id (str): Unique id of the executed task.
            args (Tuple): Original arguments for the executed task.
            kwargs (Dict): Original keyword arguments for the executed task.

        Returns:
            None: The return value of this handler is ignored.
        """
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            session.query(AutomationTask).filter(AutomationTask.task_id == task_id).update(
                {AutomationTask.task_status: "success", AutomationTask.modified_time: dt.datetime.now(timezone.utc).replace(tzinfo=None)})
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Retry handler.
    
        This is run by the worker when the task is to be retried.
    
        Arguments:
            exc (Exception): The exception sent to :meth:`retry`.
            task_id (str): Unique id of the retried task.
            args (Tuple): Original arguments for the retried task.
            kwargs (Dict): Original keyword arguments for the retried task.
            einfo (~billiard.einfo.ExceptionInfo): Exception information.

        Returns:
            None: The return value of this handler is ignored.
        """
        ...

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Error handler.

        This is run by the worker when the task fails.

        Arguments:
            exc (Exception): The exception raised by the task.
            task_id (str): Unique id of the failed task.
            args (Tuple): Original arguments for the task that failed.
            kwargs (Dict): Original keyword arguments for the task that failed.
            einfo (~billiard.einfo.ExceptionInfo): Exception information.

        Returns:
            None: The return value of this handler is ignored.
        """
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            session.query(AutomationTask).filter(AutomationTask.task_id == task_id).update(
                {AutomationTask.task_status: "failure",
                 AutomationTask.modified_time: dt.datetime.now(timezone.utc).replace(tzinfo=None)})

    @staticmethod
    def get_running_jobs():
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            running_jobs = session.query(AutomationTask).filter(AutomationTask.task_status == "running")
            if running_jobs.first():
                return running_jobs

    @staticmethod
    def get_running_job_by_sn(sn):
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            running_jobs = session.query(AutomationTask).filter(AutomationTask.task_status == "running",
                                                                AutomationTask.sn == sn)
            if running_jobs.first():
                return running_jobs

    @staticmethod
    def get_running_job_by_task_name(task_name):
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            running_jobs = session.query(AutomationTask).filter(AutomationTask.task_status == "running",
                                                                AutomationTask.task_name == task_name)
            if running_jobs.first():
                return running_jobs

    @staticmethod
    def update_task_with_failed(task_id):
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            session.query(AutomationTask).filter(AutomationTask.task_id == task_id).update(
                {AutomationTask.task_status: "failure",
                 AutomationTask.modified_time: dt.datetime.now(timezone.utc).replace(tzinfo=None)})

    @staticmethod
    def kill_process_by_sn(sn):
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            automation_task_obj = session.query(AutomationTask).filter(AutomationTask.sn == sn,
                                                                       AutomationTask.task_status == "running")
            tmp_obj = automation_task_obj.first()
            if tmp_obj:
                celery_control.revoke(tmp_obj.task_id, terminate=True)
                automation_task_obj.update(
                    {AutomationTask.task_status: "terminate",
                     AutomationTask.modified_time: dt.datetime.now(timezone.utc).replace(tzinfo=None)})

    @staticmethod
    def kill_process_by_task_name(task_name):
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            automation_task_obj = session.query(AutomationTask).filter(AutomationTask.task_name == task_name,
                                                                       AutomationTask.task_status == "running")
            tmp_obj = automation_task_obj.first()
            if tmp_obj:
                celery_control.revoke(tmp_obj.task_id, terminate=True)
                automation_task_obj.update(
                    {AutomationTask.task_status: "terminate",
                     AutomationTask.modified_time: dt.datetime.now(timezone.utc).replace(tzinfo=None)})


class AmpConBeatTask:
    def __init__(self):
        self.beat_dburi = beat_dburi

    def __get_session(self):
        return session_manager.session_factory(self.beat_dburi)

    def get_all_jobs(self):
        session = self.__get_session()
        with session_cleanup(session):
            models = session.query(PeriodicTask).all()
            tmp = list()
            for model in models:
                session.expunge(model)
                tmp.append(model)
            return tmp

    def get_job_by_name(self, job_name):
        session = self.__get_session()
        with session_cleanup(session):
            my_job = session.query(PeriodicTask).filter_by(name=job_name).first()
            if my_job:
                session.expunge(my_job)
                return my_job
            else:
                return f"No job named {job_name}"

    def get_active_job_by_name(self, job_name):
        session = self.__get_session()
        with session_cleanup(session):
            my_job = session.query(PeriodicTask).filter_by(name=job_name, enabled=True).first()
            if my_job:
                session.expunge(my_job)
                return my_job
            else:
                return f"No job named {job_name}"

    def add_job(self, job_name, task, job_type='crontab', job_schedule="* * * * *", args=[], kwargs={}, once=False,
                start_time=None,
                expires=None, job_desc=None, queue=None, exchange=None, routing_key=None,
                priority=None, enabled=True, force_updated=False):
        """
        @param job_name: job_name
        @param task: task_name or task_path
        @param job_type: interval / crontab
        @param job_schedule:
                    interval -> "2 seconds"
                    cron -> "* * * * *" means (minute, hour, day_of_week, day_of_month, month_of_year)
        @param args: []
        @param kwargs: {}
        @param once: once time
        @param start_time: job start time
        @param expires: job expire time
        @param job_desc: description
        @param queue: default normal queue
        @param exchange: default exchange
        @param routing_key: default
        @param priority:  0/1/2/3..
        @param enabled: it does work flag or not work
        @param force_updated: forced update means update column record.
        @return: None: The return value of this handler is ignored.
        """
        if force_updated and isinstance(self.get_active_job_by_name(job_name), PeriodicTask):
            raise ValueError(f"The same name {job_name} job had existed")
        date_now = dt.datetime.now(timezone.utc).replace(tzinfo=None)
        session = self.__get_session()
        with session_cleanup(session):
            session.query(PeriodicTask).filter_by(name=job_name).delete()
            new_job = PeriodicTask(name=job_name, task=task, args=json.dumps(args), kwargs=json.dumps(kwargs),
                                   start_time=start_time, expires=expires, date_changed=date_now,
                                   one_off=once, description=job_desc, queue=queue, exchange=exchange,
                                   routing_key=routing_key, enabled=enabled, priority=priority)

            if job_type.lower() == "interval":
                every, period = job_schedule.strip().split(" ")
                interval_scheduler = IntervalSchedule.from_schedule(session,
                                                                    schedules.schedule(
                                                                        dt.timedelta(**{period: int(every)})))
                new_job.interval_id = interval_scheduler.id

            elif job_type.lower() == "crontab":
                minute, hour, day_of_week, day_of_month, month_of_year = job_schedule.strip().split(" ")
                crontab_scheduler = CrontabSchedule.from_schedule(session,
                                                                  schedules.crontab(minute=minute, hour=hour,
                                                                                    day_of_week=day_of_week,
                                                                                    day_of_month=day_of_month,
                                                                                    month_of_year=month_of_year))
                new_job.crontab_id = crontab_scheduler.id
            else:
                return "task type invalid"
            session.add(new_job)
            PeriodicTaskChanged.update_changed(mapper=None, connection=session, target=None)
            session.commit()

    def __op_job(self, job_name, tag):
        """
        @param job_name: job name
        @param tag: True -> work  False -> not work
        @return:
        """
        current_job = self.get_active_job_by_name(job_name)
        if not isinstance(current_job, PeriodicTask):
            raise ValueError(current_job)
        session = self.__get_session()
        with session_cleanup(session):
            session.query(PeriodicTask).filter_by(name=job_name).update({"enabled": tag}, synchronize_session=False)
            PeriodicTaskChanged.update_changed(mapper=None, connection=session, target=None)
            session.commit()

    def resume_job(self, job_name):
        """
        @param job_name: job_name
        @return:
        """
        self.__op_job(job_name, True)

    def pause_job(self, job_name):
        """
        @param job_name: job_name
        @return:
        """
        self.__op_job(job_name, False)

    def remove_job(self, job_name):
        session = self.__get_session()
        with session_cleanup(session):
            job = session.query(PeriodicTask).filter_by(name=job_name)
            if job.first():
                job.delete(synchronize_session=False)
                PeriodicTaskChanged.update_changed(mapper=None, connection=session, target=None)
                session.commit()

    def remove_like_job(self, job_name):
        session = self.__get_session()
        with session_cleanup(session):
            job = session.query(PeriodicTask).filter(PeriodicTask.name.like('%' + job_name + '%'))
            if job.first():
                job.delete(synchronize_session=False)
                PeriodicTaskChanged.update_changed(mapper=None, connection=session, target=None)
                session.commit()

    def run_jobs(self, job_name_list):
        session = self.__get_session()
        with session_cleanup(session):
            for job_name in job_name_list:
                    job = session.query(PeriodicTask).filter_by(name=job_name)
                    if job.first():
                        job.update({PeriodicTask.start_time: dt.datetime.now(timezone.utc).replace(tzinfo=None) + dt.timedelta(
                            seconds=3), PeriodicTask.enabled: True}, synchronize_session=False)
            PeriodicTaskChanged.update_changed(mapper=None, connection=session, target=None)
            session.commit()


beat_task = AmpConBeatTask()
