import time
import logging

from server.db.models import vtep
from server.vtep_management.vtep_sync import VtepSync

db = vtep.vtep_db

LOG = logging.getLogger(__name__)


class VtepSyncManager():

    def __init__(self):
        self.vtep_syncs = {}

        # green let pool init
        import eventlet
        eventlet.monkey_patch()
        self.pool = eventlet.GreenPool(1000)

    def run(self):
        while True:
            self.sync_server_switch()
            self.sync_switch_vtep()
            time.sleep(60)

    def add_controller(self, sn, ip):
        vtep_sync = VtepSync(sn, ip)
        self.vtep_syncs[sn] = vtep_sync

    def del_controller(self, sn):
        vtep_sync = self.vtep_syncs.pop(sn, None)
        if vtep_sync:
            vtep_sync.close()

    def sync_server_switch(self):
        switches = db.get_all_vtep_switches()
        db_switches = set()
        for sn, ip in switches:
            db_switches.add(sn)
            if sn not in self.vtep_syncs:
                self.add_controller(sn, ip)
            elif self.vtep_syncs[sn].ip != ip:
                # if the IP address of switch changed, re-create a new instance 
                self.del_controller(sn)
                self.add_controller(sn, ip)

        remove_switches = set(self.vtep_syncs.keys()) - db_switches
        for sn in remove_switches:
            self.del_controller(sn)

    def sync_switch_vtep(self):
        LOG.info('start sync VTEP controller')
        for sn, manager in self.vtep_syncs.items():
            try:
                self.pool.spawn_n(manager.sync)
            except Exception as e:
                LOG.exception(e)
        self.pool.waitall()
        LOG.info('finish sync VTEP controller')
