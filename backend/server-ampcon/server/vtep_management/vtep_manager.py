import requests
import exceptions
import json, yaml
from dictdiffer import diff
from ovsdb_manager import Ovsdb_manager
import gevent
import logging
from queue import Empty
from server.db.models.inventory import Switch
from server.db.models.vtep import MacVtepMapping, VtepControlSwitch, VlanVxlanList, PortVlanBinding
from server.db.models.inventory import inven_db
from server.db.models.vtep import vtep_db
import bgp_evpn
from ovs import poller

Log = logging.getLogger(__name__)
try_time = 5


def get_diff_list(old, new):
    # returen the diff [[add_list], [del_list]]
    add_list = []
    del_list = []
    for item in new:
        if item not in old:
            add_list.append(item)
    for item in old:
        if item not in new:
            del_list.append(item)
    return [add_list, del_list]


def get_bgp_peer_status_url():
    url = "http://127.0.0.1:8801/v1/peer/************/statistic"
    return url


def get_bgp_peer_update_url():
    url = "http://127.0.0.1:8801/v1/peer/************/send/update"
    return url


class Vtep_manager(object):

    def __init__(self, *args, **kwargs):
        Log.info("::Start VTEP management controller")
        # format is {"EC1123234": "************"}
        self.bgp_enable = False
        self.bgp_send_entry = {}
        self.switch_configed = {}
        self.vlans_configed = []
        self.ovsdb_manager = {}
        self.session = inven_db.get_session()
        for sw_sn, sw_ip in self.switch_configed.items():
            ins = Ovsdb_manager(sn=sw_sn, ip=sw_ip)
            if ins.get_connection_status():
                self.ovsdb_manager.update({sw_sn: ins})
        self.mac_location_list = {}
        # format is {"PCA3226K02C":
        # {"ge-1/1/1": {"100":"10100", "200":"10200"}}, "EC7454125": {"ge-1/1/1":{"100":"10100"}}}
        # There is some assumeion in order to make desgin simple.  VLAN 100 <--> VNI 10000+100  <---> multiple ports
        self.vlan_vni_mapping = {}
        try:
            self.sync_all_from_db_to_network()
        except Exception as e:
            Log.exception(e)
        from gevent.pool import Group
        group = Group()
        group.spawn(self.sync_network_config_thread)
        group.spawn(self.sync_mac_thread)
        group.spawn(self.sync_vteps)
        # group.add(self.ovsdb_poller_thread)
        # group.add(self.process_event_thread)
        # group.add(self.bgp_evpn_thread)
        # if True:
        #     group.spawn(bgp_evpn.run)
        # group.spawn(self.start_ovsdb_poller_thread)
        # group.spawn(self.process_event_idl_thread)
        group.join()

    def sync_network_config_thread(self):
        while True:
            gevent.sleep(15)
            Log.info(":::Start sync network VLAN from DB")
            try:
                self.sync_thread = self.sync_all_from_db_to_network()
            except Exception as e:
                Log.exception(e)

    def sync_vteps(self):
        while True:
            gevent.sleep(60)
            switches = vtep_db.get_all_vteps()
            if not switches:
                continue

            Log.info(":::Start sync remote vtep ip from DB")
            for switch in self.ovsdb_manager.values():
                local_ip = switch.get_vtep_tunnel_ip()
                if not local_ip:
                    continue

                for vtep_switch in switches:
                    remote_ip = vtep_switch.local_vtep_ip
                    if local_ip == remote_ip:
                        continue

                    # db_configed_vlans = self.get_all_configed_vlans_from_db()
                    db_bind_vlans = vtep_db.get_all_binding_vlans(switch.sn)
                    tunnel_keys = []
                    have_tunnel_keys = []
                    for vlan in db_bind_vlans:
                        tunnel_key = 10000 + int(vlan)
                        local_logic_switch = switch.get_logic_switch(tunnel_key)
                        if not local_logic_switch:
                            if tunnel_key not in tunnel_keys:
                                tunnel_keys.append(tunnel_key)
                        else:
                            if local_logic_switch.uuid not in have_tunnel_keys:
                                have_tunnel_keys.append(local_logic_switch.uuid)

                    try:
                        switch.set_mcast_macs_remote(remote_ip, tunnel_keys, have_tunnel_keys)
                    except Exception as e:
                        Log.exception(e)

    def sync_mac_thread(self):
        while True:
            gevent.sleep(15)
            # The first step is collect all local MAC from each switch,{"22:22:22:22:22:22.100": ************}
            tmp_previous_list = self.mac_location_list
            Log.info("::::*** Currently, There are %d Host in network. Ready to refresh....", len(self.mac_location_list))
            self.mac_location_list = {}
            for switch in self.ovsdb_manager.values():
                # if there is multiple entry in different switch, the last one is saved, this is reasonable
                # in here. we just update the connected switch
                if switch.get_connection_status():
                    Log.info(":::switch %s is connected, start to get MAC", switch.sn)
                    self.mac_location_list.update(switch.get_all_local_mac())
                else:
                    Log.info(":::switch %s is NOT connected, get previous MAC", switch.sn)
                    if switch.disconnected_times <= try_time:
                        for key in tmp_previous_list:
                            if tmp_previous_list[key] == switch.get_vtep_tunnel_ip():
                                self.mac_location_list.update({key:tmp_previous_list[key]})
            controller_mac_list = self.mac_location_list
            # get the MAC from BGP_EVPN
            evpn_mac_list = self.session.query(MacVtepMapping).filter(MacVtepMapping.source != 'controller')
            for mac in evpn_mac_list:
                self.mac_location_list.update({mac.mac_vni: mac.vtep_ip})
            Log.info("::::Refresh done, get total %d host. inside, %d EVPN host, %d local host", len(self.mac_location_list), evpn_mac_list.count(),
                     len(controller_mac_list))
            # Now should start to sync the MAC to all switches
            #for switch in self.ovsdb_manager.values():
            #    remote_mac_list = switch.get_all_remote_mac()
            #    for mac_entry, vtep in self.mac_location_list.items():
            #        if vtep != switch.get_vtep_tunnel_ip():
            #            if mac_entry not in remote_mac_list:
            #                # create in remote table
            #                switch.add_remote_mac(mac=mac_entry.split('.')[0],
            #                                      vlan=int(mac_entry.split('.')[1])-10000, vtep_ip=vtep)
            #    # Need all check the remote MAC should be in list, otherwise, remove it
            #    for mac_entry in remote_mac_list:
            #        if mac_entry not in self.mac_location_list:
            #            switch.del_remote_mac(mac=mac_entry.split('.')[0],vlan=int(mac_entry.split('.')[1])-10000)
            # wirte back to database
            new_mac_list = []
            aged_mac_list = []
            changed_mac_list = []
            mac_in_db = self.session.query(MacVtepMapping).filter(MacVtepMapping.source == 'controller')
            mac_in_db_dict = dict((res.mac_vni, [res.vtep_ip,res.id]) for res in mac_in_db)
            for mac_entry, vtep in self.mac_location_list.items():
                if mac_entry not in mac_in_db_dict.keys():
                    new_mac_list.append({'mac_vni':mac_entry, 'vtep_ip':vtep, 'source':'controller'})
            for mac_entry, vtep_id  in mac_in_db_dict.items():
                if mac_entry not in self.mac_location_list.keys():
                    aged_mac_list.append(mac_entry)
                else:
                    if vtep_id[0] != self.mac_location_list[mac_entry]:
                        changed_mac_list.append({'id': vtep_id[1], 'mac_vni':mac_entry, 'vtep_ip':self.mac_location_list[mac_entry]})
            self.save_mac_vtep_to_db(new_mac_list)
            self.remove_mac_vtep_to_db(aged_mac_list)
            self.update_mac_vtep_to_db(changed_mac_list)

            # To do more?
            # start to send this to BGP
            if self.bgp_enable:
                # need compare the list of current controller list and BGP_SENDING_LIST, will just send delta
                for delta in list(diff(self.bgp_send_entry, controller_mac_list)):
                    # [('add', '', [('22:22:22:22:22:44.10100', '************')]),
                    #  ('remove', '', [('22:22:22:22:22:33.10100', '************')])]
                    #Log.info(":::Need send BGP-evpn to update peer for %s", list(diff(self.bgp_send_entry, controller_mac_list)))
                    if delta[0] == 'add':
                        # call REST of BGP agent
                        for entry in delta[2]:
                            pass
                            #self.send_bgp_update("add", entry)
                    elif delta[0] == 'remove':
                        # call REST to delte in BGP
                        for entry in delta[2]:
                            pass
                            #self.send_bgp_update("remove", entry)
                self.bgp_send_entry = controller_mac_list

    def send_bgp_update(self, type, entry):
        url = get_bgp_peer_update_url()
        if type == "add":
            next_hop_ip = entry[1]
            rd = str(entry[1]) + ":2"
            vtep_ip =  entry[1]
            mac = entry[0].split('.')[0].replace(':','-')
            vni = int(entry[0].split('.')[1])
            headers = {'content-type': 'application/json'}
            r = requests.post(url=url, headers=headers, json={'attr': {'1': 0, '2': [], '5': 100, '14': {'afi_safi': [25, 70], 'nexthop': next_hop_ip,
                                                            'nlri': [{'type': 2, 'value': {'eth_tag_id': 108, 'ip': vtep_ip, 'label': [vni],
                                                            'rd': rd, 'mac': mac, 'esi': 0}}]}}})
        else:
            vtep_ip = entry[1]
            mac = entry[0].split('.')[0].replace(':','-')
            vni = int(entry[0].split('.')[1])
            data = {
                "attr": {
                    "15": {
                        "afi_safi": [25, 70],
                        "withdraw": [
                            {
                                "type": 2,
                                "value": {
                                    "eth_tag_id": 108,
                                    "ip": vtep_ip,
                                    "label": [vni],
                                    "rd": vtep_ip + ':2',
                                    "mac": mac,
                                    "esi": 0}}]
                    }
                }
            }
            headers = {'content-type': 'application/json'}
            r = requests.post(url=url, headers=headers, json=data)

    def save_mac_vtep_to_db(self, mac_vtep=None):
        mac_objs = []
        # delete all
        #num_rows_deleted = self.session.query(MacVtepMapping).filter(MacVtepMapping.source == 'controller').delete()
        # if num_rows_deleted != 0:
        #    session.commit()
        # for key, value in mac_vtep.items():
        #     mac_objs.append(MacVtepMapping(mac_vni=key, vtep_ip=value, source='controller'))
        # self.session.bulk_save_objects(mac_objs)
        self.session.bulk_insert_mappings(MacVtepMapping,mac_vtep)

    def remove_mac_vtep_to_db(self, mac_vtep=None):
        for mac in mac_vtep:
            self.session.query(MacVtepMapping).filter(MacVtepMapping.source == 'controller').filter(MacVtepMapping.mac_vni == mac).delete()

    def update_mac_vtep_to_db(self, mac_vtep=None):
        self.session.bulk_update_mappings(MacVtepMapping,mac_vtep)

    def process_event_idl_thread(self):
        while True:
            gevent.sleep(1)
            for ovsdb_inst in self.ovsdb_manager.values():
                try:
                    event = ovsdb_inst.idl.event_queue.get_nowait()
                    self.process_event(event, ovsdb_inst.sn, ovsdb_inst.ip)
                except Empty:
                    Log.debug("::Have processed all event in switch: %s", ovsdb_inst.sn)

    def process_event(self, event=None, sn=None, ip=None):
        Log.info("::We are processing event: %s %s %s for switch %s %s", event[0], event[1]._table.name, event[2], sn, ip)
        if event[0] == "create" and event[1]._table.name == "Ucast_Macs_Local":
            # need get the True vtep tunnel ip
            vtep_tunnel_ip = self.ovsdb_manager[sn].get_vtep_tunnel_ip()
            vlan = event[1].logical_switch.tunnel_key[0]-10000
            Log.info(":::New MAC %s in VLAN %s learn in %s, need populated to other VTEPs ", event[1].MAC, vlan,ip )
            for each_vtep in self.ovsdb_manager.values():
                if each_vtep.ip != ip:
                    each_vtep.add_remote_mac(mac=event[1].MAC,vlan=vlan,vtep_ip=vtep_tunnel_ip)
        if event[0] == "delete" and event[1]._table.name == "Ucast_Macs_Local":
            vtep_tunnel_ip = self.ovsdb_manager[sn].get_vtep_tunnel_ip()
            vlan = event[1].logical_switch.tunnel_key[0]-10000
            Log.info(":::MAC %s in VLAN %s aged/removed in %s, need populated to other VTEPs ", event[1].MAC, vlan,ip )
            for each_vtep in self.ovsdb_manager.values():
                if each_vtep.ip != ip:
                    each_vtep.del_remote_mac(mac=event[1].MAC,vlan=vlan)

    def start_ovsdb_poller_thread(self):
        while True:
            Log.info("::Start the OVSDB polling")
            gevent.sleep(15)
            for ovsdb_inst in self.ovsdb_manager.values():
                ovsdb_inst.idl.run()
                for i in range(1, try_time):
                    if ovsdb_inst.seq_no == ovsdb_inst.idl.change_seqno:
                        poller_inst = poller.Poller()
                        ovsdb_inst.idl.wait(poller_inst)
                        poller_inst.block()
                        continue
                    ovsdb_inst.seq_no = ovsdb_inst.idl.change_seqno
                    ovsdb_inst.local_mac_dict = ovsdb_inst.get_all_local_mac()
                    ovsdb_inst.ovsdb_logical_sw = ovsdb_inst.get_logic_sw_list()
                    break
                #ovsdb_inst.idl.run()
                # if ovsdb_inst.seq_no == ovsdb_inst.idl.change_seqno:
                #     ovsdb_inst.local_mac_dict = ovsdb_inst.idl.tables["Ucast_Macs_Local"].rows
                #     ovsdb_inst.ovsdb_logical_sw = ovsdb_inst.idl.tables["Logical_Switch"].rows
                # else:
                #     ovsdb_inst.seq_no = ovsdb_inst.idl.change_seqno
                #     new_local_mac_dict = ovsdb_inst.idl.tables["Ucast_Macs_Local"].rows
                #     ovsdb_inst.local_mac_dict=ovsdb_inst.idl.tables["Ucast_Macs_Local"].rows
                #     ovsdb_inst.ovsdb_logical_sw = ovsdb_inst.idl.tables["Logical_Switch"].rows

    def get_global_mac_list(self):
        return self.mac_location_list

    def get_all_configed_switch(self):
        return self.switch_configed

    def get_all_configed_switch_from_db(self):
        switch_list = self.session.query(VtepControlSwitch.sn, Switch.mgt_ip)\
            .outerjoin(Switch, VtepControlSwitch.sn == Switch.sn).filter(VtepControlSwitch.enable == True).all()
        return dict(switch_list)

    def get_all_configed_vlans_from_db(self):
        vlan_list = []
        vlans = self.session.query(VlanVxlanList).filter()
        for vlan in vlans:
            vlan_list.append(vlan.vlan)
        return vlan_list
        # return [100,200]

    def get_vlan_vni_binding_from_db(self):
        binding_dict = {}
        bind_from_db = self.session.query(PortVlanBinding).all()
        for sw in bind_from_db:
            binding_dict.update({sw.sn: yaml.full_load(sw.port_binding)})
        # return {"PCA3226K02C": {"ge-1/1/1": {"100": "10100", "200": "10200"}}}
        # return {"PCA3226K02C": {"te-1/1/1": {100: 10100},"te-1/1/2": {100: 10100}},
        #  "581254X1836043": {"te-1/1/1": {100: 10100, 200: 10200},"te-1/1/2": {}}}
        return binding_dict

    def add_configed_switch(self, sn, ip):
        self.switch_configed.update({sn: ip})
        self.ovsdb_manager.update({sn: Ovsdb_manager(sn, ip)})
        return self.switch_configed

    def del_configed_switch(self, sn):
        self.switch_configed.pop(sn)
        self.ovsdb_manager.pop(sn)
        return self.switch_configed

    def add_new_physical_port_to_db(self,sn,ports):
        db_port_entry = self.session.query(PortVlanBinding).filter(PortVlanBinding.sn == sn).first()
        binding_dict =  yaml.full_load(db_port_entry.port_binding)
        for port in ports:
            if port not in binding_dict.keys():
                if binding_dict.keys() != []:
                    binding_dict.update({str(port): binding_dict[binding_dict.keys()[0]]})
                else:
                    binding_dict.update({str(port): {}})
        #write back to db
        # binding = PortVlanBinding()
        # binding.sn = sn
        # binding.port_binding =str(binding_dict)
        # vtep_db.insert_or_update(binding, primary_key='sn')
        db_port_entry.port_binding = str(binding_dict)

    def del_physical_port_to_db(self, sn, ports):
        db_port_entry = self.session.query(PortVlanBinding).filter(PortVlanBinding.sn == sn).first()
        binding_dict = yaml.full_load(db_port_entry.port_binding)
        for port in ports:
            if port in binding_dict.keys():
                binding_dict.pop(str(port))
        #write back to db
        # binding = PortVlanBinding()
        # binding.sn = sn
        # binding.port_binding =str(binding_dict)
        # vtep_db.insert_or_update(binding, primary_key='sn')
        db_port_entry.port_binding = str(binding_dict)

    def add_port_binding(self, sw_sn=None, port=None, vlan=None):
        # if sw_sn not in self.vlan_vni_mapping.keys():
        #     return
        # vlan_list = self.get_vlans(sw_sn)
        # if vlan not in vlan_list:
        #     self.create_vlan_logic_switch(sw_sn)
        vni = vlan + 10000
        switch_vni_mapping = self.vlan_vni_mapping['sw_sn']
        if port not in switch_vni_mapping.keys():
            self.vlan_vni_mapping[sw_sn].update({port: {vlan: vni}})
        else:
            self.vlan_vni_mapping[sw_sn][port].update({vlan: vni})
        # call ovsdb function to add port mapping  :::  vtep-ctl bind-ls XorPlus te-1/1/1 100 ls100
        self.ovsdb_manager[sw_sn].add_port_binding(port=port, vlan=vlan)
        return self.vlan_vni_mapping

    def del_port_binding(self, sw_sn=None, port=None, vlan=None):
        pass

    def create_vlan_logic_switch(self, sw_sn=None, vlan=None):
        self.ovsdb_manager[sw_sn].add_logical_switch(vlan)
        pass

    def del_vlan_logic_switch(self, sw_sn=None, vlan=None):
        pass

    # def get_vlans(self, sw_sn=None):
    #     mapping = self.vlan_vni_mapping[sw_sn]
    #     return self.get_vlan_from_mapping_dict(mapping)

    def get_vlan_from_mapping_dict(self, mapping):
        # {'ge-1/1/1': {'200': '10200'}} as input
        vlan_list = []
        for vlan_map in mapping.values():
            for vlan in vlan_map.keys():
                if vlan not in vlan_list:
                    vlan_list.append(vlan)
        return vlan_list

    def sync_all_from_db_to_network(self):
        # will sync switch configured, vlan configure, and VLAN -port binding configure

        # check the status of OVSDB connection
        for sn, ovsdb_inst in self.ovsdb_manager.items():
            switch = self.session.query(VtepControlSwitch).filter(VtepControlSwitch.sn == sn).first()
            # update the local VTEP IP
            if switch:
                switch.local_vtep_ip = ovsdb_inst.get_vtep_tunnel_ip()

        # compare the configured switch
        db_configed_switch = self.get_all_configed_switch_from_db()
        configured_switch = list(diff(self.switch_configed, db_configed_switch))
        if configured_switch:
            Log.warn("::::There is diffence between Switch and DB  %s", configured_switch)
            for delta_item in configured_switch:
                if delta_item[0] == 'add':
                    for sw in delta_item[2]:
                        ins = Ovsdb_manager(sn=sw[0], ip=sw[1])
                        if ins.get_connection_status():
                            self.switch_configed.update({sw[0]: sw[1]})
                            self.ovsdb_manager.update({sw[0]: ins})
                if delta_item[0] == 'remove':
                    for sw in delta_item[2]:
                        self.switch_configed.pop(sw[0])
                        self.ovsdb_manager.pop(sw[0])

        # Then, need sync the VLAN in each switch
        db_configed_vlans = self.get_all_configed_vlans_from_db()
        for switch in self.ovsdb_manager.values():
            # need get the VLAN list
            vlan_in_switch = switch.get_vlans_list()
            add_vlans, del_vlans = get_diff_list(vlan_in_switch, db_configed_vlans)
            for vlan in add_vlans:
                switch.add_logical_switch(vlan)

            for vlan in del_vlans:
                switch.del_logical_switch(vlan)

        # compare the VLAN Maaping [('add', 'EC7454125.ge-1/1/1', [('100', '10100')]), ('remove', 'EC7454125.ge-1/1/1', [('200', '10200')]),
        #     ('add', 'PCA3226K02C.ge-1/1/1', [('300', '10300')]), ('add', '', [('EC2222222', {'ge-1/1/1': {'200': '10200'}})])]
        db_vlan_vni_binding = self.get_vlan_vni_binding_from_db()
        for switch in self.ovsdb_manager.values():
            # need get the VLAN binding and mapping
            port_bind = switch.get_port_binding()
            db_bindings = db_vlan_vni_binding[switch.sn]
            differ_vlans = list(diff(port_bind, db_bindings))
            if differ_vlans:
                Log.warn("::::There is diffence between Switch and DB  %s", differ_vlans)
                for delta_item in differ_vlans:
                    if delta_item[0] == 'add':
                        if delta_item[1] == '':
                            Log.warn("::::There is mismatch port config: %s please change the config in switch %s", delta_item[2], switch.sn)
                            # In this case, db should del switch physical port
                            del_physical_port = []
                            for port in delta_item[2]:
                                del_physical_port.append(port[0])
                            self.del_physical_port_to_db(switch.sn, del_physical_port)
                        else:
                            Log.info("::::Need add VLANs %s for switch %s port %s", delta_item[2], switch.sn, delta_item[1])
                            for vlan in delta_item[2]:
                                # in here, vlan is [(300, 10300)], inside for, is (300,10300)
                                switch.add_port_binding(port=delta_item[1], vlan=vlan[0])
                    elif delta_item[0] == 'remove':
                        if delta_item[1] == '':
                            Log.warn("::::There is mismatch port config: %s please change the config in switch %s", delta_item[2], switch.sn)
                            # In this case, db should add new switch physical port
                            new_physical_port = []
                            for port in delta_item[2]:
                                new_physical_port.append(port[0])
                            self.add_new_physical_port_to_db(switch.sn, new_physical_port)
                        else:
                            Log.info("::::Need del VLANs %s for switch %s port %s", delta_item[2], switch.sn, delta_item[1])
                            for vlan in delta_item[2]:
                                # in here, vlan is [(300, 10300)], inside for, is (300,10300)
                                switch.del_port_binding(port=delta_item[1],vlan=vlan[0])