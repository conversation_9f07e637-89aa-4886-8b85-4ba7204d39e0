import logging
import os
import time
import yaml

from server import constants
from server.ansible_lib import ansible_common
from server.ansible_lib.ansible_common import CollectCallback
from server.collect.single_collect_common import get_switch_sn
from server.db.models.inventory import SwitchConfigBackup, Switch, SwitchConfigSnapshot, SystemConfig
from server.db.models.inventory import inven_db
from server.db.models.monitor import monitor_db
from server.db.models.user import User
from server.util import ssh_util as client, http_client, utils, ssh_helper
from server.db.models import inventory
from datetime import datetime

LOG = logging.getLogger(__name__)

FILE_DIR = 'config_gen/rma/'


def update_backup_config(sn, result):
    session = inven_db.get_session()
    file_path = FILE_DIR + sn + '.config'
    with session.begin():
        backup = inven_db.get_model(SwitchConfigBackup, filters={'sn': [sn]}, session=session)
        if backup:
            backup.config = file_path
        else:
            backup = SwitchConfigBackup(sn=sn, config=file_path)
            session.add(backup)


def collect_backup_config_all():
    # back_type = conform_task_type()

    host_map = dict()
    host_list = []

    # if back_type == 'deploy' or back_type == 'all':
    switches = inven_db.get_collection(Switch, filters={'status': ['Provisioning Success', 'Imported']})
    for switch in switches:
        if 'retrieve_config' in yaml.full_load(switch.post_deployed_config).keys():
            retrieve_config = yaml.full_load(switch.post_deployed_config)['retrieve_config']
        else:
            #in default, will retrieve config
            retrieve_config = 'true'

        if retrieve_config == 'true' or retrieve_config == 'True':
            host_map.update({switch.mgt_ip: switch.sn})
            host_list.append(switch.mgt_ip)
    LOG.info("collect backup config started")
    collect_backup_config_paramiko(host_list, host_map)
    inven_db.clear_session()
    LOG.info("collect backup config finished")


def collect_backup_config_single(host, sn, current_time=None):
    return collect_backup_config_paramiko([host], {host: sn}, current_time)[host]


def collect_backup_config_paramiko(host_list, host_map, current_time=None):

    result = {}
    for host in host_list:

        sn = host_map[host]
        user, pw = utils.get_switch_default_user(sn=sn)
        if not sn:
            sn, status = get_switch_sn(host, user, pw)
            if status == constants.RMA_UN_REACHABLE:
                inven_db.update_model(Switch, filters={'mgt_ip': [host]},
                                      updates={Switch.status: constants.UN_REACHABLE})
            if status != constants.RMA_ACTIVE:
                result[host] = status
                continue

        retry_times = 0
        done = False
        while not done and retry_times < 3:
            file_content, code = client.interactive_shell_linux('cat /pica/config/pica_startup.boot',
                                                                host, username=user, password=pw,
                                                                error_fn=ssh_helper.ignore_stdout_error)
            if file_content:
                done = True
                break
            retry_times += 1
        if not done:
            result[host] = constants.RMA_FAILED
            continue

        session = inven_db.get_session()
        if code == constants.RMA_UN_REACHABLE:
            update_switch_status(host, constants.UN_REACHABLE, session=session)
            result[host] = constants.RMA_UN_REACHABLE
            inven_db.add_switch_log(sn, 'cannot retrieve switch config by switch unreachable')
            continue

        if code != constants.RMA_ACTIVE:
            update_switch_status(host, constants.REACHABLE, session=session)
            result[host] = constants.RMA_FAILED
            inven_db.add_switch_log(sn, 'cannot retrieve switch config by error, %s' % file_content)
            continue

        try:
            back_switch_file(host, sn, file_content, session=session, current_time=current_time)
        except Exception as e:
            LOG.exception(e)
            inven_db.add_switch_log(sn, 'back switch retrieve config error, %s' % str(e))

        update_switch_status(host, constants.REACHABLE, session=session)
        result[host] = constants.RMA_ACTIVE

    return result

def upload_rollback_config_paramiko(ip, user, pw, config_content, fallback_timeout=240, commit_wait_time=10):
    # Load override .boot config
    # If the configuration makes the switch unreachable, it will automatically fallback in 1min
    # Args:
    #   ip: IP address of the switch
    #   config_content: .boot format config content
    #   fallback_timeout: timeout value for "commit confirmed"

    create_cmds = """rm -rf /home/<USER>/upload_rollback.config && echo '{0}' >> /home/<USER>/upload_rollback.config""".format(config_content)
    res, status = client.interactive_shell_linux(create_cmds, ip, username=user, password=pw, error_fn=ssh_helper.ignore_stdout_error)
    if status != constants.RMA_ACTIVE:
        return False, 'Failed to upload configuration to switch.'

    load_config_cmds = '/pica/bin/pica_sh -c "configure;load override /home/<USER>/upload_rollback.config;commit"'
    res, status = client.interactive_shell_linux(load_config_cmds, ip, username=user,password=pw, timeout=fallback_timeout)
    if status != constants.RMA_ACTIVE:
        return False, 'Failed to rollback the snapshot.'

    # time.sleep(commit_wait_time)

    # commit_cmds = 'commit'
    # res, status = client.interactive_shell_configure(commit_cmds, ip, username=user,password=pw)
    # if status != constants.RMA_ACTIVE:
    #     return False, 'Failed to confirm the commit. The configuration will automatically rollback in 1 min.'
    
    return True, 'Success to rollback the snapshot.'

def update_switch_status(host, status, session=None):
    inven_db.update_model(Switch, filters={'mgt_ip': [host]}, updates={Switch.reachable_status: status},
                          session=session)


def back_switch_file(host, sn, content, session=None, current_time=None):
    content = content.replace('\r\n', '\n')
    system_config = inven_db.get_collection(SystemConfig, filters={})

    retrieve_config_num = system_config[0].retrieve_config_num-1 if len(system_config) > 0 else 99
    
    check_backup = inven_db.get_model(SwitchConfigBackup, filters={'ip': [host]})
    if check_backup and check_backup.sn != sn:
        # should delete confict back entry
        inven_db.delete_model(SwitchConfigBackup, 'ip', host)
    inven_db.insert_or_update_back(host, content, constants.RMA_BACK_AUTO, sn, session=session)
    # add new function that save snapshot config
    if not current_time:
        current_time = datetime.utcnow()
    db_session = inven_db.get_session()
    with db_session.begin():
        uptodate_snapshot_list = db_session.query(inventory.SwitchConfigSnapshot).filter(
            inventory.SwitchConfigSnapshot.sn == sn).order_by(inventory.SwitchConfigSnapshot.snapshot_time.desc()).all()
        if uptodate_snapshot_list:
            if len(uptodate_snapshot_list) > retrieve_config_num:
                delete_collect = uptodate_snapshot_list[retrieve_config_num:]
                for record in delete_collect:
                    if record.tag != 'GOLDEN_CONFIG':
                        db_session.delete(record)
            db_session.add(SwitchConfigSnapshot(sn=sn, snapshot_time=current_time, archive_config=str(content).encode()))
        else:
            db_session.add(SwitchConfigSnapshot(sn=sn, snapshot_time=current_time, archive_config=str(content).encode()))

        # automatically check the backup config & golden config
        golden_snapshot = db_session.query(inventory.SwitchConfigSnapshot) \
            .filter(inventory.SwitchConfigSnapshot.sn == sn, inventory.SwitchConfigSnapshot.tag == 'GOLDEN_CONFIG').first()
        if golden_snapshot and convert_configstr_to_setcommand(golden_snapshot.archive_config.decode()) != convert_configstr_to_setcommand(str(content)):
            # add alarm message if current is config is different
            monitor_db.add_event(sn, 'warn', 'Retrieved config is different from the "GOLDEN_CONFIG".')


def transfer_rma_config(filename, path, session=None):
    random_user = inven_db.get_model(User, session=session)
    http_client.start_transfer_file(random_user.name, [{'filename': filename, 'path': path, 'dest': path}])


def collect_backup_config_single_group(host, sn, report_time, model='', action=''):
    return collect_backup_config_paramiko_group([host], {host: sn}, report_time, model, action=action)[host]


def collect_backup_config_paramiko_group(host_list, host_map, report_time, model='', action=''):
    result = {}
    for host in host_list:

        sn = host_map[host]
        user, pw = utils.get_switch_default_user(sn=sn)

        if not sn:
            sn, status = get_switch_sn(host, user, pw)
            if status == constants.RMA_UN_REACHABLE:
                inven_db.update_model(Switch, filters={'mgt_ip': [host]},
                                      updates={Switch.status: constants.UN_REACHABLE})
            if status != constants.RMA_ACTIVE:
                result[host] = status
                log_info = 'Could not establish SSH connection to %s' % host
                add_group_log(sn, report_time, log_info, level='error', action=action)
                LOG.warning('retrieve switch %s failed, %s', host, log_info)
                continue

        retry_times = 0
        done = False
        while not done and retry_times < 3:
            file_content, code = client.interactive_shell_linux('cat /pica/config/pica_startup.boot',
                                                                host, username=user, password=pw)
            if file_content:
                done = True
                break
            retry_times += 1
        if not done:
            result[host] = constants.RMA_FAILED
            log_info = 'Could not get config on %s, please retry' % host
            add_group_log(sn, report_time, log_info, level='error', action=action)
            LOG.warning('retrieve switch %s failed, %s', host, log_info)
            continue

        session = inven_db.get_session()
        if code == constants.RMA_UN_REACHABLE:
            # update_switch_status(host, constants.UN_REACHABLE, session=session)
            result[host] = constants.RMA_UN_REACHABLE

            log_info = 'The device with the IP %s is unreachable or does not run PicOS' % host
            add_group_log(sn, report_time, log_info, level='error', action=action)
            LOG.warning('retrieve switch %s failed, %s', host, log_info)
            continue

        if code != constants.RMA_ACTIVE:
            # update_switch_status(host, constants.RMA_FAILED, session=session)
            result[host] = constants.RMA_FAILED

            log_info = 'Could not establish SSH connection to %s' % host
            add_group_log(sn, report_time, log_info, level='error', action=action)
            LOG.warning('retrieve switch %s failed, %s', host, log_info)
            continue

        back_switch_file(host, sn, file_content, session=session)

        # update_switch_status(host, constants.REACHABLE, session=session)
        result[host] = constants.RMA_ACTIVE

        log_info = 'The switch %s retrieve config success' % sn
        add_group_log(sn, report_time, log_info, level='info', action=action)
        LOG.debug('retrieve switch %s success', host)

    return result


def add_group_log(sn, report_time, log, action='', level='info'):
    inven_db.add_switch_log(sn, report_time + log, action, level=level)

def convert_configstr_to_setcommand(config_str):
    resultOutput = []
    config_list = config_str.split('\n')
    treePath = []
    
    treeLevel = 0

    for i in range(len(config_list)):
        rowString = config_list[i].strip()
        if rowString.find('/*') >= 0 or not rowString:
            continue
        elif rowString.find('{') >= 0:
            node_str = rowString.split('{')[0].strip()
            treePath.append(node_str)
            treeLevel += 1
            continue
        elif rowString.find('}') >= 0:
            if config_list[i-1].strip().find('{') >= 0:
                tmpString = 'set {0}'.format(' '.join(treePath))
                resultOutput.append(tmpString)
            treePath.pop()
            treeLevel -= 1
            continue
        elif rowString.find(': ') >= 0:
            node_list = rowString.split(':')
            tmpString = 'set {0} {1} {2}'.format(' '.join(treePath), node_list[0].strip(), node_list[1].strip())
            resultOutput.append(tmpString)
            continue
        else:
            tmpString = 'set {0} {1}'.format(' '.join(treePath), rowString)
            resultOutput.append(tmpString)
    
    return '\n'.join(resultOutput)

