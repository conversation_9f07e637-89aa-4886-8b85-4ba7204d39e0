import logging
import os
import platform
import time
from datetime import datetime as dt

from flask_login import current_user
from flask import Blueprint, render_template, send_file, request, jsonify
from flask import Response

from server import constants as C
from server.south_api import ssh_api
from server.util import utils
from server.util.permission import super_user_permission, super_admin_permission, admin_permission

if platform.system() != 'Windows':
    from server.collect.collect_switch_status import *

from server.util.utils import get_latlong, get_picos_v, get_parking_security_config, \
    is_need_to_push_security_config_again
from server.db.models.user import User
from server.db.models.inventory import Switch, SwitchGis, VpnConfig, ApplicationConfig, SwitchStatus, SwitchAutoConfig, \
    DeployedSecuritySwitch, SwitchParking
from server.db.models.inventory import inven_db as db
from server.db.models.monitor import monitor_db
from server.ansible_deploy_switch import AnsibleDeploySwitch as DeployDriver, celery_start_push_parking_security_config

import base64
import io

from server.vpn import vpn_utils

management_mold = Blueprint('management_mold', __name__, template_folder='templates')
Log = logging.getLogger(__name__)


@management_mold.route('/switch_management')
@admin_permission.require(http_exception=403)
def switch_management():
    active = ('lifecycle', 'switch_management')
    switch_location_list = db.get_lat_long_with_ansible_status("all")
    switch_location_list_OK = db.get_lat_long_with_ansible_status("reachable")
    switch_location_and_detail_list_OK = []
    for switch in switch_location_list_OK:
        switch_info = db.get_switch_info_by_sn(switch.sn)
        # if the lat, lng is 0, change it as central latlong
        if switch.latitude == 0 or switch.longitude == 0:
            switch.longitude = \
                db.get_collection(ApplicationConfig, filters={'application_name': ['map_central_latlng']})[
                    0].configuration.split(',')[0]
            switch.latitude = \
                db.get_collection(ApplicationConfig, filters={'application_name': ['map_central_latlng']})[
                    0].configuration.split(',')[1]
        switch_location_and_detail_list_OK.append([switch, switch_info])
    switch_location_list_NG = db.get_lat_long_with_ansible_status("un-reachable")
    switch_location_and_detail_list_NG = []
    for switch in switch_location_list_NG:
        switch_info = db.get_switch_info_by_sn(switch.sn)
        # if the lat, lng is 0, change it as central latlong
        if switch.latitude == 0 or switch.longitude == 0:
            switch.longitude = \
                db.get_collection(ApplicationConfig, filters={'application_name': ['map_central_latlng']})[
                    0].configuration.split(',')[0]
            switch.latitude = \
                db.get_collection(ApplicationConfig, filters={'application_name': ['map_central_latlng']})[
                    0].configuration.split(',')[1]
        switch_location_and_detail_list_NG.append([switch, switch_info])
    map_config = {}
    map_config['nominatim_server_url'] = \
        db.get_collection(ApplicationConfig, filters={'application_name': ['nominatim_server_url']})[0].configuration
    map_config['tileLayer'] = db.get_collection(ApplicationConfig, filters={'application_name': ['tileLayer']})[
        0].configuration
    map_config['maxZoom'] = db.get_collection(ApplicationConfig, filters={'application_name': ['maxZoom']})[
        0].configuration
    map_config['map_central_latlng'] = \
        db.get_collection(ApplicationConfig, filters={'application_name': ['map_central_latlng']})[0].configuration
    statistic_swtich = {}
    statistic_swtich.update(
        {'all_switch': len(utils.query_switch().filter(Switch.status.in_(['Provisioning Success', 'Imported'])).all())})
    statistic_swtich.update({'active_switch': len(
        utils.query_switch().filter(Switch.status.in_(['Provisioning Success', 'Imported'])).filter(
            Switch.reachable_status.in_([0])).all())})
    statistic_swtich.update({'inactive_switch': len(
        utils.query_switch().filter(Switch.status.in_(['Provisioning Success', 'Imported'])).filter(
            Switch.reachable_status.in_([1, 2, 3])).all())})
    return render_template('switch_management.html', active=active, switch_list_ok=switch_location_and_detail_list_OK,
                           switch_list_ng=switch_location_and_detail_list_NG, statistic_swtich=statistic_swtich,
                           map_config=map_config)


@management_mold.route('/switch_status_refresh/<string:sn>')
def switch_status_refresh(sn):
    try:
        refresh_switch_status(sn)
    except Exception as e:
        pass

    switch_info_list = db.get_switch_status(sn)
    switch_info_list = [switch_info.make_dict() for switch_info in switch_info_list]

    switch_info_list_sorted = None

    if switch_info_list:
        # devide the ports in LAG and physical port
        switch_ge_phy_list = []
        switch_te_phy_list = []
        switch_qe_phy_list = []
        switch_xe_phy_list = []
        switch_others_phy_list = []
        switch_lag_list = []
        for port_member in switch_info_list:
            if 'ae' in port_member['port_name']:
                switch_lag_list.append(port_member)
            elif 'ge-1/1/' in port_member['port_name']:
                switch_ge_phy_list.append(port_member)
            elif 'te-1/1/' in port_member['port_name']:
                switch_te_phy_list.append(port_member)
            elif 'qe-1/1/' in port_member['port_name']:
                switch_qe_phy_list.append(port_member)
            elif 'xe-1/1/' in port_member['port_name']:
                switch_xe_phy_list.append(port_member)
            else:
                switch_others_phy_list.append(port_member)
        switch_ge_phy_list_order = sorted(switch_ge_phy_list, key=lambda port: int(port['port_name'].split('/')[2]))
        switch_te_phy_list_order = sorted(switch_te_phy_list, key=lambda port: int(port['port_name'].split('/')[2]))
        switch_qe_phy_list_order = sorted(switch_qe_phy_list, key=lambda port: int(port['port_name'].split('/')[2]))
        switch_xe_phy_list_order = sorted(switch_xe_phy_list, key=lambda port: int(port['port_name'].split('/')[2]))
        switch_others_phy_list_order = sorted(switch_others_phy_list,
                                              key=lambda port: int(port['port_name'].split('/')[2]))
        switch_lag_list_order = sorted(switch_lag_list, key=lambda port: int(port['port_name'].split('ae')[1]))
        switch_info_list_sorted = switch_ge_phy_list_order + switch_te_phy_list_order + switch_qe_phy_list_order \
                                  + switch_xe_phy_list_order + switch_others_phy_list_order + switch_lag_list_order

    response = {}

    response['port_list'] = switch_info_list_sorted

    session = db.get_session()
    switch_vlan_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="vlan_config").first()
    vlan_status = json.loads(switch_vlan_status.content.replace('\'', '\"')) \
        if switch_vlan_status and switch_vlan_status.content else {}
    response['vlan_status'] = vlan_status

    cpu_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="cpu_usage").first()
    cpu_status = '0' if not cpu_status else cpu_status.content
    response['cpu_status'] = cpu_status

    memory_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="memory_usage").first()
    memory_status = '0;0' if not memory_status else memory_status.content
    response['memory_status'] = memory_status

    temperature_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="temperature_stat").first()
    temperature_status = '-;-' if not temperature_status else temperature_status.content
    response['temperature_status'] = temperature_status

    rpsu_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="rpsu_stat").first()
    rpsu_status = 'N/A' if not rpsu_status else rpsu_status.content
    response['rpsu_status'] = rpsu_status

    stp_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="stp_stat").first()
    stp_status = 'N/A' if not stp_status else stp_status.content
    response['stp_status'] = stp_status

    logs = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="logs").first()
    logs = 'N/A' if not logs else logs.content
    response['logs'] = logs

    port_poe_enable = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="port_poe_enable").first()
    port_poe_enable = '[]' if not port_poe_enable else port_poe_enable.content
    response['port_poe_enable'] = port_poe_enable

    poe_info = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="poe_info").first()
    poe_info = [] if not poe_info else json.loads(poe_info.content)
    response['poe_info'] = poe_info

    return jsonify(response)


@management_mold.route('/display_switch_status/<string:sn>')
def display_switch_status(sn):
    active = ('auto_deployment', 'deploy_switch_list')

    global_config = db.get_system_config_by_sn(sn)
    login_user = global_config.switch_op_user
    pw = global_config.switch_op_password

    switch_info = db.get_switch_info_by_sn(sn)
    host_name = switch_info.host_name if switch_info else ''

    switch_global_info = db.get_switch_info_by_sn(sn)

    try:
        switch_info_list = db.get_switch_status(sn)
        if switch_info_list:
            # now = datetime.datetime.now()
            # last_update_time = switch_info_list[0].modified_time
            # if (now - last_update_time).seconds > 15:
            #     refresh = True
            refresh = False
        else:
            refresh = True

        if refresh:
            refresh_switch_status(sn)
            # if not refresh_switch_status(sn):
            #     return render_template("single_switch.html", active=active, status=[],
            #                            vlan_status=[],
            #                            cpu_status=None, memory_status=None,
            #                            temperature_status=None,
            #                            rpsu_status=None, stp_status=None, logs=None,
            #                            port_poe_enable=None,  poe_info=None,
            #                            login_user=login_user, pw=pw, global_info=switch_global_info,
            #                            sn=sn, error='switch not reachable')
            switch_info_list = db.get_switch_status(sn)

        switch_info_list = [switch_info.make_dict() for switch_info in switch_info_list]
        # need to sort ports in GUI
        switch_info_list_sorted = []
        if switch_info_list:
            # devide the ports in LAG and physical port
            switch_ge_phy_list = []
            switch_te_phy_list = []
            switch_qe_phy_list = []
            switch_xe_phy_list = []
            switch_others_phy_list = []
            switch_lag_list = []
            for port_member in switch_info_list:
                if 'ae' in port_member['port_name']:
                    switch_lag_list.append(port_member)
                elif 'ge-1/1/' in port_member['port_name']:
                    switch_ge_phy_list.append(port_member)
                elif 'te-1/1/' in port_member['port_name']:
                    switch_te_phy_list.append(port_member)
                elif 'qe-1/1/' in port_member['port_name']:
                    switch_qe_phy_list.append(port_member)
                elif 'xe-1/1/' in port_member['port_name']:
                    switch_xe_phy_list.append(port_member)
                else:
                    switch_others_phy_list.append(port_member)
            switch_ge_phy_list_order = sorted(switch_ge_phy_list, key=lambda port: int(port['port_name'].split('/')[2]))
            switch_te_phy_list_order = sorted(switch_te_phy_list, key=lambda port: int(port['port_name'].split('/')[2]))
            switch_qe_phy_list_order = sorted(switch_qe_phy_list, key=lambda port: int(port['port_name'].split('/')[2]))
            switch_xe_phy_list_order = sorted(switch_xe_phy_list, key=lambda port: int(port['port_name'].split('/')[2]))
            switch_others_phy_list_order = sorted(switch_others_phy_list,
                                                  key=lambda port: int(port['port_name'].split('/')[2]))
            switch_lag_list_order = sorted(switch_lag_list, key=lambda port: int(port['port_name'].split('ae')[1]))
            switch_info_list_sorted = switch_ge_phy_list_order + switch_te_phy_list_order + switch_qe_phy_list_order \
                                      + switch_xe_phy_list_order + switch_others_phy_list_order + switch_lag_list_order

        session = db.get_session()
        vlan_status = {}
        switch_vlan_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="vlan_config").first()
        if switch_vlan_status and switch_vlan_status.content:
            vlan_status = json.loads(switch_vlan_status.content.replace('\'', '\"'))
        cpu_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="cpu_usage").first()
        if not cpu_status:
            cpu_status = {'content': '0'}
        memory_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="memory_usage").first()
        if not memory_status:
            memory_status = {'content': '0;0'}
        temperature_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="temperature_stat").first()
        if not temperature_status:
            temperature_status = {'content': '-;-'}
        rpsu_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="rpsu_stat").first()
        if not rpsu_status:
            rpsu_status = {'content': 'N/A'}
        stp_status = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="stp_stat").first()
        if not stp_status:
            stp_status = {'content': 'N/A'}
        logs = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="logs").first()
        if not logs:
            logs = {'content': 'N/A'}
        port_poe_enable = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="port_poe_enable").first()
        if not port_poe_enable:
            port_poe_enable = {'content': '[]'}
        poe_info = session.query(SwitchGlobalStatus).filter_by(sn=sn, type="poe_info").first()
        if not poe_info:
            poe_info = []
        else:
            poe_info = json.loads(poe_info.content)

        return render_template("single_switch.html", active=active, status=switch_info_list_sorted,
                               vlan_status=vlan_status,
                               cpu_status=cpu_status, memory_status=memory_status,
                               temperature_status=temperature_status,
                               rpsu_status=rpsu_status, stp_status=stp_status, logs=logs,
                               port_poe_enable=port_poe_enable, poe_info=poe_info,
                               login_user=login_user, pw=pw, global_info=switch_global_info, sn=sn, host_name=host_name)
    except Exception as e:
        Log.exception(e)
        return render_template("single_switch.html", active=active, status=[],
                               vlan_status=[],
                               cpu_status=None, memory_status=None,
                               temperature_status=None,
                               rpsu_status=None, stp_status=None, logs=None,
                               port_poe_enable=None, poe_info=None,
                               login_user=login_user, pw=pw, global_info=switch_global_info, sn=sn, host_name=host_name,
                               error=str(e))


# it's not be used.
def refresh_switch_status(sn):
    switch = db.get_switch_info_by_sn(sn)
    if not switch:
        return False
    global_config = db.get_system_config_by_sn(sn)
    user = global_config.switch_op_user
    pw = global_config.switch_op_password
    host = switch.mgt_ip

    other_system_status_list = ssh_api.get_switch_system_static(host, user, pw)
    current_time = datetime.datetime.now()
    session = db.get_session()
    if not other_system_status_list:
        return False
    with session.begin(subtransactions=True):
        cpu_usage = session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.sn == sn).filter(
            SwitchGlobalStatus.type == 'cpu_usage').first()
        if cpu_usage:
            cpu_usage.content = other_system_status_list['cpu_usage']
            cpu_usage.upload_time = current_time
        else:
            cpu_usage = SwitchGlobalStatus()
            cpu_usage.sn = sn
            cpu_usage.type = 'cpu_usage'
            cpu_usage.content = other_system_status_list['cpu_usage']
            cpu_usage.upload_time = current_time
            session.add(cpu_usage)

        memory_usage = session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.sn == sn).filter(
            SwitchGlobalStatus.type == 'memory_usage').first()
        if memory_usage:
            memory_usage.content = str(other_system_status_list['memory_total']) + ';' + str(
                other_system_status_list['memory_use'])
            memory_usage.upload_time = current_time
        else:
            memory_usage = SwitchGlobalStatus()
            memory_usage.sn = sn
            memory_usage.type = 'memory_usage'
            memory_usage.content = str(other_system_status_list['memory_total']) + ';' + str(
                other_system_status_list['memory_use'])
            memory_usage.upload_time = current_time
            session.add(memory_usage)

        temperature_stat = session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.sn == sn).filter(
            SwitchGlobalStatus.type == 'temperature_stat').first()
        if temperature_stat:
            temperature_stat.content = str(other_system_status_list['board_temperature']) + ';' + str(
                other_system_status_list['asic_temperature'])
            temperature_stat.upload_time = current_time
        else:
            temperature_stat = SwitchGlobalStatus()
            temperature_stat.sn = sn
            temperature_stat.type = 'temperature_stat'
            temperature_stat.content = str(other_system_status_list['board_temperature']) + ';' + str(
                other_system_status_list['asic_temperature'])
            temperature_stat.upload_time = current_time
            session.add(temperature_stat)

        rpsu_stat = session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.sn == sn).filter(
            SwitchGlobalStatus.type == 'rpsu_stat').first()
        if rpsu_stat:
            rpsu_stat.content = other_system_status_list['rpsu']
            rpsu_stat.upload_time = current_time
        else:
            rpsu_stat = SwitchGlobalStatus()
            rpsu_stat.sn = sn
            rpsu_stat.type = 'rpsu_stat'
            rpsu_stat.content = other_system_status_list['rpsu']
            rpsu_stat.upload_time = current_time
            session.add(rpsu_stat)

        stp_stat = session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.sn == sn).filter(
            SwitchGlobalStatus.type == 'stp_stat').first()
        if stp_stat:
            stp_stat.content = other_system_status_list['stp']
            stp_stat.upload_time = current_time
        else:
            stp_stat = SwitchGlobalStatus()
            stp_stat.sn = sn
            stp_stat.type = 'stp_stat'
            stp_stat.content = other_system_status_list['stp']
            stp_stat.upload_time = current_time
            session.add(stp_stat)

        logs = session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.sn == sn).filter(
            SwitchGlobalStatus.type == 'logs').first()
        if logs:
            logs.content = other_system_status_list['logs']
            logs.upload_time = current_time
        else:
            logs = SwitchGlobalStatus()
            logs.sn = sn
            logs.type = 'logs'
            logs.content = other_system_status_list['logs']
            logs.upload_time = current_time
            session.add(logs)

        port_poe_enable = session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.sn == sn).filter(
            SwitchGlobalStatus.type == 'port_poe_enable').first()
        if port_poe_enable:
            port_poe_enable.content = other_system_status_list['port_poe_enable']
            port_poe_enable.upload_time = current_time
        else:
            port_poe_enable = SwitchGlobalStatus()
            port_poe_enable.sn = sn
            port_poe_enable.type = 'port_poe_enable'
            port_poe_enable.content = other_system_status_list['port_poe_enable']
            port_poe_enable.upload_time = current_time
            session.add(port_poe_enable)

        poe_info = session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.sn == sn).filter(
            SwitchGlobalStatus.type == 'poe_info').first()
        if poe_info:
            poe_info.content = other_system_status_list['poe_info']
            poe_info.upload_time = current_time
        else:
            poe_info = SwitchGlobalStatus()
            poe_info.sn = sn
            poe_info.type = 'poe_info'
            poe_info.content = other_system_status_list['poe_info']
            poe_info.upload_time = current_time
            session.add(poe_info)

    status = ssh_api.get_port_status(host, user, pw)
    if not status:
        return False
    with session.begin(subtransactions=True):
        for portInfo in status:
            port_status = session.query(SwitchStatus).filter(SwitchStatus.sn == sn).filter(
                SwitchStatus.port_name == portInfo['port_name']).first()
            if port_status:
                port_status.rc_rate = portInfo['band_use_rate']
                port_status.tx_rate = portInfo['band_use_rate']
                port_status.rc_count = portInfo['inputOct']
                port_status.tx_count = portInfo['outputOct']
                port_status.rc_drop = portInfo['inputDropOct']
                port_status.tx_drop = portInfo['outputDropOct']
                port_status.link_status = portInfo['link']
                port_status.link_speed = portInfo['speed']
                port_status.port_type = 'RJ45'
                port_status.admin_status = portInfo['admin_status']
                port_status.flow_control = portInfo['flow_control']
                port_status.description = portInfo['description']
                port_status.upload_time = current_time
            else:
                port_status = SwitchStatus()
                port_status.sn = sn
                port_status.port_name = portInfo['port_name']
                port_status.rc_rate = portInfo['band_use_rate']
                port_status.tx_rate = portInfo['band_use_rate']
                port_status.rc_count = portInfo['inputOct']
                port_status.tx_count = portInfo['outputOct']
                port_status.rc_drop = portInfo['inputDropOct']
                port_status.tx_drop = portInfo['outputDropOct']
                port_status.link_status = portInfo['link']
                port_status.link_speed = portInfo['speed']
                port_status.port_type = 'RJ45'
                port_status.admin_status = portInfo['admin_status']
                port_status.flow_control = portInfo['flow_control']
                port_status.description = portInfo['description']
                port_status.upload_time = current_time
                session.add(port_status)

    vlan_status_str = ssh_api.get_vlan_status(host, user, pw)
    if not vlan_status_str:
        return False
    with session.begin(subtransactions=True):
        vlan_status = session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.sn == sn).filter(
            SwitchGlobalStatus.type == 'vlan_config').first()
        if vlan_status:
            vlan_status.content = str(vlan_status_str)
            vlan_status.upload_time = current_time
        else:
            vlan_status = SwitchGlobalStatus()
            vlan_status.sn = sn
            vlan_status.type = 'vlan_config'
            vlan_status.content = str(vlan_status_str)
            vlan_status.upload_time = current_time
            session.add(vlan_status)
    return True


@management_mold.route('/push_config_to_switch/<string:sn>', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='push_config_to_switch', contents='push config to switch')
def push_config_to_switch(sn):
    session = inven_db.get_session()
    switches = db.get_collection(Switch, filters={'sn': [sn]})
    if not switches:
        return "Not found switch"
    form = request.form
    pending_config = form.get('config', None)
    ignore_error = form.get('ignore_error', 'no')
    # need to format the config with;
    config_list = pending_config.split('\n')
    # finial_pending_config = 'configure;'
    finial_pending_config = 'configure;'
    for config_line in config_list:
        if config_line != '':
            finial_pending_config += config_line
            finial_pending_config += ';'
    finial_pending_config += 'commit'
    global_config = db.get_system_config_by_sn(sn)
    login_user = global_config.switch_op_user
    pw = global_config.switch_op_password
    login_user, pw = (login_user, pw) if sn != C.PICOS_V_SN else (C.PICOS_V_USERNAME, C.PICOS_V_PASSWORD)
    # host_map = dict()
    # map(lambda switch: host_map.update({switch.mgt_ip: switch.sn}), switches)
    # cmd = '/pica/bin/pica_sh -c '+ '"'+ finial_pending_config + '"'
    # tasks = [ansible_common.command_task('Push user L2L3 config',
    #                                      cmd, tags=[])
    #          ]
    # callback = ansible_common.SwitchOperCallback(host_map)
    # callback.register_handler('Push user L2L3 config', push_config_handle)
    # ansible_common.run_tasks(login_user, pw, tasks, host_map.keys(), callback)
    # msg = {'info': 'Push pending configuration success', 'status': '200'}
    # db.add_switch_log(sn, 'add configuration'+ pending_config, 'warn')
    # Log.warning('Push config into switch %s' % finial_pending_config)
    # return jsonify(msg)
    try:
        if ignore_error == 'no':
            res, status = ssh_api.apply_configs(switches[0].mgt_ip, pending_config, sn=sn, user=login_user, password=pw)
        else:
            res, status = ssh_api.apply_configs_ignore_error(switches[0].mgt_ip, pending_config, sn=sn, user=login_user,
                                                             password=pw)
        if status != C.RMA_ACTIVE:
            reason = res[-200:] if len(res) > 200 else res
            monitor_db.add_event(switches[0].mgt_ip, 'error',
                                 'apply config to %s failed, stdout:[%s], Push config: %s' % (
                                     sn, reason, pending_config[0:150]), session=session)
            msg = {'status': '500', 'info': 'Apply config to %s failed, stdout:[%s]' % (sn, reason)}
            return jsonify(msg)
        else:
            monitor_db.add_event(switches[0].mgt_ip, 'info',
                                 'apply config success, Push config: %s' % pending_config[0:150])
            msg = {'status': '200', 'info': 'The %s switch pushing config success ' % sn}
            return jsonify(msg)
    except Exception as e:
        monitor_db.add_event(switches[0].mgt_ip, 'error',
                             'apply config to %s failed, stdout:[%s], Push config: %s' % (
                                 sn, str(e), pending_config[0:150]))
        msg = {'status': '500', 'info': 'Apply config to %s failed, stdout:[%s]' % (sn, str(e))}
        return jsonify(msg)


def display_switch_config(sn, result):
    # In later, we need asdd some logic in here
    pass


@management_mold.route('/vpn_reg/<string:info>', methods=['GET'])
def vpn_reg(info):
    info_list = info.split(';')
    if not info_list[0]:
        return 'bad params'
    sn = info_list[0]
    ip = info_list[1]
    model = info_list[2]
    service_tag = info_list[3] if len(info_list) >= 4 else None

    info_dict = dict(
        sn=info_list[0],
        ip=info_list[1],
        model=info_list[2],
        hwid=service_tag,
        flag=int(info_list[4]) if len(info_list) >= 5 else 0,
        # 0: inband 1: manage 2:vpn
        uplink_type=int(info_list[5]) if len(info_list) >= 6 else 0,
        # for support dell switch service code or tag
        service_code=info_list[6] if len(info_list) >= 7 else ''
    )

    is_need_to_push_security_config = is_need_to_push_security_config_again(sn)
    if service_tag:
        switch = inven_db.get_switch_info_by_sn(service_tag)
        # dell service tag switch
        if switch:
            check_switch = inven_db.get_switch_info_by_sn(sn)
            if check_switch:
                inven_db.add_switch_log(info['service_code'], 'The switch sn %s have already exist' % sn,
                                        level='warn')
                return Response('The switch sn %s have already exist' % str(sn), status=500)

            # update switch sn and service code
            inven_db.update_model(Switch, {'sn': [service_tag]},
                                  {'sn': sn, 'remark': service_tag})
            inven_db.update_model(SwitchAutoConfig, {'name': [service_tag + '_site_config']},
                                  {'name': sn + '_site_config'})
    deployed_security_switch = inven_db.get_model(DeployedSecuritySwitch, filters={'sn': [sn]})
    # should update switch lot whatever
    db.update_switch_lot(sn, ip, model)
    switch = inven_db.get_switch_info_by_sn(sn)
    if not switch or not switch.enable and get_parking_security_config(sn):
        if not deployed_security_switch or is_need_to_push_security_config:
            LOG.info("switch %s haven't been configured", sn)
            monitor_db.add_event(sn, 'warn',
                                 'switch %s registered, but have not been configured' % sn)
            # update hardware_id when parking lot
            hardware_id = ssh_api.get_hardware_id(info_dict["ip"])
            db.update_lot_hardware_id(info_dict["ip"], hardware_id)

            # push parking security file and execute it
            celery_start_push_parking_security_config.delay(info_dict, celery_sn=info_dict['sn'],
                                                            celery_task_name=f"push_parking_security_config_{info_dict['sn']}")

    # get the license ca, key, cert from database

    vpn_client = db.get_collection(VpnConfig, filters={'sn': [sn]})
    if not vpn_client:
        LOG.info("switch %s haven't VPN key files", sn)
        monitor_db.add_event(sn, 'warn',
                             'switch %s try to download VPN keys, but have not been created' % sn)
        return Response('No VPN config found in server databased, please login server and create one', status=500)
    else:
        return 'Found the VPN config in server database'


@management_mold.route('/vpn/<string:sn>/<string:file>', methods=['GET'])
def send_vpn_files(sn, file):
    # delete the directoy and create one again
    if not os.path.exists('vpn/keys/' + sn):
        os.makedirs('vpn/keys/' + sn)
    if os.path.exists('vpn/keys/' + sn + '/' + file):
        os.remove('vpn/keys/' + sn + '/' + file)

    # get the license ca, key, cert from database
    vpn_client = db.get_model(VpnConfig, filters={'sn': [sn]})
    if not vpn_client:
        return Response('No VPN keys', status=500)

    # the file name with ca.crt will be save in db as ca_crt
    attr_name = file.replace('.', '_')
    if hasattr(vpn_client, attr_name):
        content = getattr(vpn_client, attr_name)
    else:
        return Response("invalid file name", status=500)

    f = open('vpn/keys/' + sn + '/' + file, 'w')
    f.write(content)
    f.close()

    # if no key file, it should be error
    return send_file('vpn/keys/' + sn + '/' + file)


@management_mold.route('/<string:sn>/vpn/enable', methods=['GET'])
@admin_permission.require(http_exception=403)
def enable_switch_vpn(sn):
    vpn_utils.create_vpn_client(sn)
    return 'ok'


@management_mold.route('/vpn/<string:file>', methods=['GET'])
def send_vpn_static_files(file):
    # if not key file, it should be error
    return send_file('vpn/' + file)


@management_mold.route('/save_map_config', methods=['POST'])
@admin_permission.require(http_exception=403)
def save_map_config():
    info = request.form
    geocoding_api = info['geocoding_api']
    nominatim_server_url = info['nominatim_server_url']
    map_central_latlng = info['map_central_latlng']
    maxZoom = info['maxZoom']
    tileLayer = info['tileLayer']

    if geocoding_api in ['osm']:
        geo_api_config = ApplicationConfig(application_name='geocoding_api', configuration=geocoding_api)
        db.insert_or_update(geo_api_config, primary_key='application_name')
    if nominatim_server_url != '':
        nominatim_server_url = ApplicationConfig(application_name='nominatim_server_url',
                                                 configuration=nominatim_server_url)
        db.insert_or_update(nominatim_server_url, primary_key='application_name')

    # check the map central latlng
    latitude, longitude = map_central_latlng.split(',')
    if not C.LATITUDE_REGEX.match(latitude):
        msg = {'status': '500', 'info': 'Invalid latitude!'}
        return jsonify(msg)
    if not C.LONGITUDE_REGEX.match(longitude):
        msg = {'status': '500', 'info': 'Invalid longitude!'}
        return jsonify(msg)

    if latitude != '0' and longitude != '0':
        map_central_latlng = ApplicationConfig(application_name='map_central_latlng', configuration=map_central_latlng)
        db.insert_or_update(map_central_latlng, primary_key='application_name')
    if isinstance(int(maxZoom), int):
        if int(maxZoom) > 0 and int(maxZoom) < 14:
            maxZoom = ApplicationConfig(application_name='maxZoom', configuration=maxZoom)
            db.insert_or_update(maxZoom, primary_key='application_name')
        else:
            msg = {'status': '500', 'info': 'Incorrect Zoom value, should 1 to 13'}
            return jsonify(msg)
    else:
        msg = {'status': '500', 'info': 'Incorrect Zoom value input'}
        return jsonify(msg)
    if tileLayer != '':
        tileLayer = ApplicationConfig(application_name='tileLayer', configuration=tileLayer)
        db.insert_or_update(tileLayer, primary_key='application_name')
    msg = {'status': '200', 'info': 'Update Map config success'}
    return jsonify(msg)


@management_mold.route('/upload_switch_status', methods=['POST'])
def upload_switch_status():
    info = request.values
    # sn = info['sn']
    current_time = datetime.datetime.now()
    for key in info:
        if key not in ['sn', 'model', 'ip']:
            db.insert(SwitchGlobalStatus(sn=info['sn'], upload_time=current_time, type=key, content=info[key]))
        if key == 'interface_status':
            status = json.loads(info[key].replace('\'', '\"'))
            session = db.get_session()
            with session.begin(subtransactions=True):
                for name, portInfo in status.items():
                    port_status = session.query(SwitchStatus).filter(SwitchStatus.sn == info['sn']).filter(
                        SwitchStatus.port_name == name).first()
                    if port_status:
                        port_status.rc_rate = portInfo[2]
                        port_status.tx_rate = portInfo[3]
                        port_status.rc_count = portInfo[4]
                        port_status.tx_count = portInfo[5]
                        port_status.rc_drop = portInfo[6]
                        port_status.tx_drop = portInfo[7]
                        port_status.link_status = portInfo[0]
                        port_status.link_speed = portInfo[1]
                        port_status.port_type = portInfo[8]
                        port_status.upload_time = current_time
                    else:
                        port_status = SwitchStatus()
                        port_status.sn = info['sn']
                        port_status.port_name = name
                        port_status.rc_rate = portInfo[2]
                        port_status.tx_rate = portInfo[3]
                        port_status.rc_count = portInfo[4]
                        port_status.tx_count = portInfo[5]
                        port_status.rc_drop = portInfo[6]
                        port_status.tx_drop = portInfo[7]
                        port_status.link_status = portInfo[0]
                        port_status.link_speed = portInfo[1]
                        port_status.port_type = portInfo[8]
                        port_status.upload_time = current_time
                    session.add(port_status)

    msg = {'status': '200', 'info': 'Update switch status success'}
    return jsonify(msg)


@management_mold.route('/login_switch', methods=['POST'])
def login_switch():
    info = request.form
    ip = info['ip']
    param = info.get('param')
    picos_v = get_picos_v().first()
    if picos_v and ip == picos_v.mgt_ip:
        login_user = C.PICOS_V_USERNAME
        pw = C.PICOS_V_PASSWORD
    else:
        login_user = info.get("ssh_user", "")
        pw = info.get("ssh_pwd", "")

    if param:
        encoded = base64.b64encode((ip + ';' + login_user + ';' + pw + ';' + param).encode()).decode()
    else:
        encoded = base64.b64encode((ip + ';' + login_user + ';' + pw).encode()).decode()
    from server import cfg
    server_ip = cfg.CONF.global_ip
    return 'http://' + server_ip + ':80' + '?' + encoded


@management_mold.route('/edit_mgt_ip', methods=['POST'])
def edit_mgt_ip():
    info = request.form
    mgt_ip = info.get("mgt_ip", "")
    sn = info.get("sn", "")
    try:
        if not mgt_ip:
            return jsonify({'code': 400, 'message': 'Mgt_ip required'})
        inven_db.update_model(Switch, filters={'sn': [sn]}, updates={Switch.mgt_ip: mgt_ip})
    except Exception as e:
        return jsonify({'code': 500, 'message': e})
    else:
        return jsonify({'code': 200, 'message': 'success'})


@management_mold.route('/get_running_config', methods=['POST'])
def get_running_config():
    form = request.form
    sn = form.get('sn', None)
    format = form.get('format', 'tree')
    session = inven_db.get_session()
    switches = db.get_collection(Switch, filters={'sn': [sn]})
    if not switches:
        return "Can't found switch"
    host = switches[0].mgt_ip
    running_config = ssh_api.get_switch_running_config(sn=sn, host=host, format=format)['running_config']
    if format == "set":
        sort_running_config = "\n".join(sorted(running_config.split("\n")))
    else:
        sort_running_config = running_config
    if running_config != 'Null' and running_config != 'Can not get config':
        return jsonify({'running_config': sort_running_config})
    else:
        return jsonify({'running_config': 'Can not get the config from switch'})
