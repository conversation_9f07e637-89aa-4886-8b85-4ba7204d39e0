import logging
import time
import traceback
from datetime import datetime, timedelta, date

import flask_login
import requests
from flask import Blueprint, request, jsonify
from server import constants
from server.constants import PICOS_V_SN
from server.db.models import general
from server.db.models import inventory, automation
from server.db.models.dc_blueprint import dc_fabric_db, DCFabricTopology
from server.db.models.wireless import WirelessRrmTaskLog, WirelessRrmTaskResult
from server.service.upgrade_license import batch_upgrade_license
from server.util import ssh_util as conn_client
from server.util import utils
from server.util.fabric_topology_util import release_pool_record
from server.util.permission import super_user_permission, admin_permission, readonly_permission
from server.util.tip_client_util import ServerType, post, delete
from server.util.utils import is_name_valid

from celery_app.automation_task import AmpConBaseTask
from celery_app.config_distribution_task import config_distribution_cli

inven_db = inventory.inven_db
new_lifecycle_model = Blueprint('new_lifecycle_model', __name__, template_folder='templates')
LOG = logging.getLogger(__name__)


@new_lifecycle_model.route('/license_audit')
@admin_permission.require(http_exception=403)
def license_audit():
    groups = utils.get_user_group().filter(inventory.Group.audit == True).all()
    report_time_list = get_last_reports(7, 'audit')

    content = {
        "groups": [group.group_name for group in groups],
        "reportTimeList": report_time_list,
    }
    return jsonify({'data': content, 'status': 200})


@new_lifecycle_model.route('/license_action')
@admin_permission.require(http_exception=403)
def license_action():
    groups = utils.get_user_group().filter(inventory.Group.action == True).all()
    report_time_list = get_last_reports(7, 'action')
    content = {
        "groups": [group.group_name for group in groups],
        "reportTimeList": report_time_list,
    }
    return jsonify({'data': content, 'status': 200})


@new_lifecycle_model.route('/lifecycle_license/<string:sn>/<string:action>')
@admin_permission.require(http_exception=403)
def lifecycle_license(sn, action):
    date_time = (date.today() + timedelta(days=30)).strftime('%Y-%m-%d')
    switch_op_user, switch_op_password = utils.get_switch_default_user(sn=sn)
    switch_task_running = AmpConBaseTask.get_running_job_by_task_name(f"{sn}_{action}")
    if switch_task_running:
        LOG.info('license is in upgrade')
        return jsonify({'info': 'license is in upgrade', 'status': 200})
    batch_upgrade_license.delay(sn, '', '', '', switch_op_user,
                                switch_op_password, action, date_time, celery_sn=sn, celery_task_name=f"{sn}_{action}")
    return jsonify({'info': 'upgrade license', 'status': 200})


def get_last_reports(days, report_action=''):
    current_time = datetime.utcnow()
    filter_time = current_time - timedelta(days=days)
    # Then we filter all log before the days
    db_session = inven_db.get_session()
    logs_register = db_session.query(inventory.SwitchLog).filter(inventory.SwitchLog.create_time > filter_time).filter(
        inventory.SwitchLog.report_action == report_action).order_by(inventory.SwitchLog.create_time.desc()).all()
    # Now we need get the list of report time
    logs_time_list = []
    if logs_register:
        for log in logs_register:
            if log.content[0:31] not in logs_time_list:
                logs_time_list.append(log.content[0:31])
    return logs_time_list


@new_lifecycle_model.route('/get_report_by_time/<string:report_time>')
@admin_permission.require(http_exception=403)
def get_report_by_time(report_time):
    logs_string = ''
    db_session = inven_db.get_session()
    logs_register = db_session.query(
        inventory.SwitchLog).filter(inventory.SwitchLog.content.contains(report_time)).order_by(
        inventory.SwitchLog.create_time.desc()).all()
    if logs_register:
        for log in logs_register:
            log_str = log.switch_id + ': ' + log.content + '\n'
            logs_string += log_str
    return logs_string


@new_lifecycle_model.route('/group_management/data_with_picos_v', methods=['POST'])
def group_management_data_with_picos_v():
    # with picos_v
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/group_management/data', methods=['POST'])
def group_management_data():
    # without picos_v
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED]), inventory.Switch.sn != PICOS_V_SN]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/fabric_management/data', methods=['POST'])
def fabric_management_data():
    # without picos_v
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED, constants.SwitchStatus.STAGED,
                                                 constants.SwitchStatus.STAGING, constants.SwitchStatus.CONFIGURED,
                                                 constants.SwitchStatus.PROVISIONING_FAILED,
                                                 constants.SwitchStatus.REGISTERED, constants.SwitchStatus.DECOM,
                                                 constants.SwitchStatus.DECOM_MANUAL,
                                                 constants.SwitchStatus.DECOM_INIT,
                                                 constants.SwitchStatus.DECOM_PENDING, constants.SwitchStatus.RMA])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/site_management/data', methods=['POST'])
def site_management_data():
    # without picos_v
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED, constants.SwitchStatus.STAGED,
                                                 constants.SwitchStatus.STAGING, constants.SwitchStatus.CONFIGURED,
                                                 constants.SwitchStatus.PROVISIONING_FAILED,
                                                 constants.SwitchStatus.REGISTERED, constants.SwitchStatus.DECOM,
                                                 constants.SwitchStatus.DECOM_MANUAL,
                                                 constants.SwitchStatus.DECOM_INIT,
                                                 constants.SwitchStatus.DECOM_PENDING, constants.SwitchStatus.RMA])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/license_table_data/<string:group_name>', methods=['POST'])
@admin_permission.require(http_exception=403)
def license_table_data(group_name):
    session = inven_db.get_session()
    group_type = session.query(inventory.Group.group_type).filter(
        inventory.Group.group_name == group_name).first().group_type
    if group_type == 'switch':
        extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                     constants.SwitchStatus.IMPORTED])]
        page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter, group=group_name)
        return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})
    else:
        page_num, page_size, total_count, query_results = utils.query_helper(automation.AnsibleDevice,
                                                                             pre_query=session.query(
                                                                                 automation.AnsibleDevice).join(
                                                                                 inventory.HostGroupMapping,
                                                                                 automation.AnsibleDevice.device_name == inventory.HostGroupMapping.device_name).filter(
                                                                                 inventory.HostGroupMapping.group_name == group_name))
        data = [result.make_dict() for result in query_results]
        return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/load_switch_group_table/<string:group_name>', methods=['POST'])
@admin_permission.require(http_exception=403)
def load_switch_group_table(group_name):
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter, group_name,
                                                                             use_selected=True)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/upgrade_table/data', methods=['POST'])
@admin_permission.require(http_exception=403)
def upgrade_table_data():
    selected_platform = request.get_json().get('selectedPlatform', None)
    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter,
                                                                             selected_platform=selected_platform)
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/create_group', methods=["POST"])
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='create_group', contents='create group {group_name}')
def create_group():
    params = request.get_json()

    if flask_login.current_user.type != 'superuser':
        return jsonify({'info': "Only superuser can create group!", 'status': 400})

    group_type = params.get('groupType', 'switch')
    if group_type == 'switch':
        action_list = params.get('actionList')
    group_name = params.get('groupName')
    description = params.get('description')

    if not is_name_valid(group_name):
        return jsonify({'info': "Group name is invalid", 'status': 400})

    db_session = inven_db.get_session()
    if db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name).first():
        return jsonify({'info': "Group already exist", 'status': 400})

    with db_session.begin():
        group = inventory.Group()
        group.group_name = group_name
        group.description = description
        group.group_type = group_type
        if group_type == 'switch':
            for attr in action_list:
                setattr(group, attr, True)
        db_session.add(group)
    return jsonify({'info': "Group create successfully", 'status': 200})


@new_lifecycle_model.route('/groups')
@super_user_permission.require(http_exception=403)
def get_groups():
    db_session = inven_db.get_session()
    groups = db_session.query(inventory.Group.group_name, inventory.Group.group_type).group_by(
        inventory.Group.group_name, inventory.Group.group_type).all()
    switch_groups = [g.group_name for g in groups if g.group_type == 'switch']
    host_groups = [g.group_name for g in groups if g.group_type == 'host']
    return jsonify({'data': {'switch': switch_groups, 'host': host_groups}, 'status': 200})


@new_lifecycle_model.route('/fetch_host_info', methods=['POST'])
@super_user_permission.require(http_exception=403)
def fetch_host_info():
    group_name = request.get_json().get('groupName', None)
    data = []
    if group_name:
        page_num, page_size, total_count, results = utils.query_helper(automation.AnsibleDevice,
                                                                       pre_query=utils.query_host(group_name=group_name,
                                                                                                  use_selected=True))
        data = [
            {
                'id': result[0].id,
                'ip': result[0].ip,
                'device_name': result[0].device_name,
                'device_user': result[0].device_user,
                'selected': result[1],
                'create_time': result[0].create_time.strftime('%Y-%m-%d %H:%M:%S') if result[0].create_time else ''
            } for result in results
        ]
    else:
        page_num, page_size, total_count, results = utils.query_helper(automation.AnsibleDevice)
        data = [result.make_dict() for result in results]

    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/delete_group/<string:group_name>')
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='delete_group', contents='delete group {group_name}')
def delete_group(group_name):
    # check whether it is used in background tasks
    jobs = AmpConBaseTask.get_running_jobs()
    if jobs:
        for job in jobs:
            if 'group_upgrade::{0}::'.format(group_name) in job.task_name:
                msg = {'info': 'Group {0} is used for group upgrade tasks'.format(group_name), 'status': 400}
                return jsonify(msg)
            if 'group_push_image::{0}::'.format(group_name) in job.task_name:
                msg = {'info': 'Group {0} is used for group push image tasks'.format(group_name), 'status': 400}
                return jsonify(msg)
    utils.delete_group(group_name)
    msg = {'info': 'Success to delete group {0}'.format(group_name), 'status': 200}
    return jsonify(msg)


@new_lifecycle_model.route('/load_group/<string:group_name>')
def load_group(group_name):
    db_session = inven_db.get_session()
    group = db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name).first()
    if group.group_type == 'switch':
        audit = group.audit
        action = group.action
        upgrading = group.upgrading
        retrieve_config = group.retrieve_config
        return jsonify({'data': {'group_name': group.group_name, 'group_des': group.description,
                                 'audit': audit, 'action': action, 'upgrading': upgrading,
                                 'retrieve_config': retrieve_config}, 'status': 200})
    else:
        # for host group, no action list
        return jsonify({'data': {'group_name': group.group_name, 'group_des': group.description}, 'status': 200})


@new_lifecycle_model.route('/fetch_group_devices/<string:group_name>')
def fetch_group_devices(group_name):
    db_session = inven_db.get_session()
    group = db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name).first()
    if not group:
        return jsonify({'info': 'Group not found', 'status': 400})

    if group.group_type == 'switch':
        devices = db_session.query(inventory.Switch).join(inventory.AssociationGroup,
                                                          inventory.Switch.sn == inventory.AssociationGroup.switch_sn) \
            .filter(inventory.AssociationGroup.group_name == group_name).all()
    else:
        devices = db_session.query(automation.AnsibleDevice).join(inventory.HostGroupMapping,
                                                                  automation.AnsibleDevice.device_name == inventory.HostGroupMapping.device_name) \
            .filter(inventory.HostGroupMapping.group_name == group_name).all()
    if devices:
        data = [device.make_dict() for device in devices]
    else:
        data = []
    return jsonify({'data': data, 'status': 200})


@new_lifecycle_model.route('/load_group_switch', methods=["POST"])
@readonly_permission.require(http_exception=403)
def load_group_switch():
    params = request.get_json()

    group_name = params.get('groupName')

    db_session = inven_db.get_session()
    site = db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name).first()
    if not site:
        return jsonify({'info': "Group not found", 'status': 400})

    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED, constants.SwitchStatus.STAGED,
                                                 constants.SwitchStatus.STAGING, constants.SwitchStatus.CONFIGURED,
                                                 constants.SwitchStatus.PROVISIONING_FAILED,
                                                 constants.SwitchStatus.REGISTERED, constants.SwitchStatus.DECOM,
                                                 constants.SwitchStatus.DECOM_MANUAL,
                                                 constants.SwitchStatus.DECOM_INIT,
                                                 constants.SwitchStatus.DECOM_PENDING, constants.SwitchStatus.RMA])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter, selected_group=[group_name])
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/edit_group_table_data', methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_group_table_data():
    try:
        data = request.get_json()
        groupName = data.get("groupName", None)
        page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch, pre_query=utils.query_switch(
            group_list=[groupName] if groupName else None))
        lifecycle_query_obj = utils.query_lifecycle_switch_data(query_obj)
        if not groupName:
            response = {
                "data": [{
                    "id": switch_id,
                    "host_name": switch_host_name,
                    "sn": switch_sn,
                    "create_time": switch_create_time.strftime('%Y-%m-%d %H:%M:%S') if switch_create_time else "",
                    "version": switch_version,
                    "revision": switch_revision,
                    "status": switch_status,
                    "mgt_ip": switch_mgt_ip,
                    "platform_model": switch_platform_model,
                    "address": switch_address,
                    "selected": False,
                    "reachable_status": switch_reachable_status,
                    "license_status": license_info.status if license_info else "",
                    "license_expired": license_info.license_expired.strftime(
                        '%Y-%m-%d %H:%M:%S') if license_info else "",
                } for switch_sn, switch_id, switch_host_name,
                switch_create_time, switch_version, switch_revision,
                switch_status, switch_mgt_ip, switch_platform_model, switch_address,
                switch_selected, switch_reachable_status, license_info,
                switch_system_info, switch_config_backup in lifecycle_query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        else:
            response = {
                "data": [{
                    "id": switch_id,
                    "host_name": switch_host_name,
                    "sn": switch_sn,
                    "create_time": switch_create_time.strftime('%Y-%m-%d %H:%M:%S') if switch_create_time else "",
                    "version": switch_version,
                    "revision": switch_revision,
                    "status": switch_status,
                    "mgt_ip": switch_mgt_ip,
                    "platform_model": switch_platform_model,
                    "address": switch_address,
                    "selected": switch_selected,
                    "reachable_status": switch_reachable_status,
                    "license_status": license_info.status if license_info else "",
                    "license_expired": license_info.license_expired.strftime(
                        '%Y-%m-%d %H:%M:%S') if license_info else "",
                } for switch_sn, switch_id, switch_host_name,
                switch_create_time, switch_version, switch_revision,
                switch_status, switch_mgt_ip, switch_platform_model, switch_address,
                switch_selected, switch_reachable_status, license_info,
                switch_system_info, switch_config_backup in lifecycle_query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        return jsonify(response)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get add device modal table data fail', 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/save_group', methods=["POST"])
@super_user_permission.require(http_exception=403)
def save_group():
    params = request.get_json()

    action_dict = params.get('actionList')
    group_name = params.get('groupName')
    del_switches = params.get('delSwitches')
    new_switches = params.get('addSwitches', [])

    group_action_list = ['audit', 'action', 'upgrading', 'retrieve_config']

    db_session = inven_db.get_session()
    db_session.query(inventory.AssociationGroup).filter(inventory.AssociationGroup.group_name == group_name,
                                                        inventory.AssociationGroup.switch_sn.in_(del_switches)) \
        .delete(synchronize_session=False)

    group = db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name).first()
    if not group:
        return jsonify({'info': 'group %s not found' % str(group_name), 'status': 400})

    with db_session.begin():
        group.group_name = group_name
        for attr in group_action_list:
            setattr(group, attr, action_dict.get(attr, False))

        for switch in new_switches:
            association_group = inventory.AssociationGroup()
            association_group.switch_sn = switch
            association_group.group_name = group.group_name
            group.association_group.append(association_group)
        db_session.merge(group)
    return jsonify({'info': "Group edit successfully", 'status': 200})


@new_lifecycle_model.route('/edit_host_group', methods=["POST"])
@super_user_permission.require(http_exception=403)
def edit_host_group():
    params = request.get_json()
    group_name = params.get('groupName')
    del_hosts = params.get('delHosts', [])
    new_hosts = params.get('addHosts', [])
    db_session = inven_db.get_session()
    db_session.query(inventory.HostGroupMapping).filter(inventory.HostGroupMapping.group_name == group_name,
                                                        inventory.HostGroupMapping.device_name.in_(del_hosts)) \
        .delete(synchronize_session=False)
    group = db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name).first()
    if not group:
        return jsonify({'info': 'Group %s not found' % str(group_name), 'status': 400})
    with db_session.begin():
        for host in new_hosts:
            host_group_mapping = inventory.HostGroupMapping()
            host_group_mapping.group_name = group_name
            host_group_mapping.device_name = host
            db_session.add(host_group_mapping)
        db_session.merge(group)
    return jsonify({'info': "Host group edit successfully", 'status': 200})


@new_lifecycle_model.route('/delete_host_from_group', methods=["POST"])
@super_user_permission.require(http_exception=403)
def delete_host_from_group():
    params = request.get_json()
    group_name = params.get('groupName')
    device_name = params.get('deviceName')
    db_session = inven_db.get_session()
    host_group_mapping = db_session.query(inventory.HostGroupMapping).filter(
        inventory.HostGroupMapping.group_name == group_name,
        inventory.HostGroupMapping.device_name == device_name
    ).first()
    if not host_group_mapping:
        return jsonify({'info': f'Host {device_name} not found in group {group_name}', 'status': 400})
    try:
        with db_session.begin():
            db_session.delete(host_group_mapping)
        return jsonify({'info': "Host removed from group successfully", 'status': 200})
    except Exception as e:
        return jsonify({'info': f"Failed to remove host from group: {str(e)}", 'status': 500})


@new_lifecycle_model.route('/fabric')
@readonly_permission.require(http_exception=403)
def get_fabric():
    db_session = inven_db.get_session()
    fabric_names = list(map(lambda x: x[0], db_session.query(inventory.Fabric.fabric_name).all()))
    return jsonify({'data': fabric_names, 'status': 200})


@new_lifecycle_model.route('/list_fabric', methods=["POST"])
@readonly_permission.require(http_exception=403)
def list_fabric():
    page_num, page_size, total_count, query_obj = utils.list_fabric_info()
    response = {
        "data": [{
            "id": fabric.id,
            "fabric_name": fabric.fabric_name,
            "description": fabric.description if fabric.description else "--",
            "switch_count": switch_count,
            "az_count": az_count,
            "vd_count": vd_count,
            "logical_count": logical_count,
            "fabric_topo_id": fabric_topo.id if fabric_topo else 0,
            "template_name": fabric_topo.template_name if fabric_topo else "--",
            "underlay_routing_protocol": fabric_topo.fabric_config.get("underlay_routing_protocol",
                                                                       "") if fabric_topo and fabric_topo.fabric_config.get(
                "underlay_routing_protocol", "") else "--",
            "status": fabric_topo.status if fabric_topo else "Not Deployed",
            "modified_time": fabric_topo.modified_time.strftime(
                '%Y-%m-%d %H:%M:%S') if fabric_topo and fabric_topo.modified_time and fabric_topo.modified_time > fabric.modified_time else fabric.modified_time.strftime(
                '%Y-%m-%d %H:%M:%S'),
        } for fabric, switch_count, fabric_topo, az_count, vd_count, logical_count in query_obj],
        "page": page_num,
        "pageSize": page_size,
        "total": total_count,
        "status": 200
    }
    return jsonify(response)


@new_lifecycle_model.route('/create_fabric', methods=["POST"])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='create_fabric', contents='create fabric')
def create_fabric():
    params = request.get_json()

    fabric_name = params.get('fabricName', '')
    description = params.get('description', '')

    if (not is_name_valid(fabric_name)) or ' ' in fabric_name or len(fabric_name) > 64:
        return jsonify({'info': "Fabric name is invalid", 'status': 400})

    if description and len(description) > 256:
        return jsonify({'info': "Fabric description is too long", 'status': 400})

    db_session = inven_db.get_session()
    if db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first():
        return jsonify({'info': "Fabric already exist", 'status': 400})

    try:
        with db_session.begin():
            fabric_topology = inventory.Topology()
            fabric_topology.name = fabric_name
            fabric_topology.description = description
            fabric_topology.topology_type = 'fabric'
            db_session.add(fabric_topology)

            fabric_topology_id = db_session.query(inventory.Topology).filter(inventory.Topology.name == fabric_name,
                                                                             inventory.Topology.topology_type == 'fabric').first().id

            fabric = inventory.Fabric()
            fabric.fabric_name = fabric_name
            fabric.description = description
            fabric.topology_id = fabric_topology_id
            db_session.add(fabric)
        return jsonify({'info': "Fabric create successfully", 'status': 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        db_session.rollback()
        msg = {'info': 'Fabric create failed', 'status': 500}
        return jsonify(msg)


# 清空配置
def delete_fabric_topo_deployment(fabric_topo_id):
    try:
        topology = dc_fabric_db.get_fabric_topo_by_id(fabric_topo_id)
        if topology.status == "Deploying":
            return jsonify({'info': "Config distribution task is running, please wait a few minutes.", 'status': 400})

        nodes = dc_fabric_db.get_fabric_topology_node(fabric_topo_id)
        config_dict = {}

        for node in nodes:
            if not node.switch_sn:
                continue
            config_dict[node.switch_sn] = {
                "meta": {
                    "role": node.type,
                    "fabric_topo_id": fabric_topo_id,
                    "logic_name": node.logic_name,
                },
                "old_val": node.node_config if node.node_config else {},
                "new_val": {}
            }

        LOG.info(config_dict)

        return config_distribution_cli(config_dict, asynchronous=False)

    except Exception as e:
        LOG.error(traceback.format_exc())
        return {'status': 400, 'info': 'Deploy topo failed.'}


@new_lifecycle_model.route('/delete_fabric', methods=["POST"])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='delete_fabric', contents='delete fabric')
def delete_fabric():
    params = request.get_json()

    fabric_name = params.get('fabricName')

    db_session = inven_db.get_session()
    fabric = db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first()
    if not fabric:
        return jsonify({'info': "Fabric not found", 'status': 400})

    topo = db_session.query(DCFabricTopology).filter(DCFabricTopology.fabric_id == fabric.id).first()
    if topo:
        delete_result = delete_fabric_topo_deployment(topo.id)
        LOG.info(f"Delete fabric topo result: {delete_result}")
    else:
        LOG.info(f"No topology found for fabric {fabric_name}, skipping topo deletion")

    try:
        with db_session.begin():
            default_fabric = db_session.query(inventory.Fabric).filter(
                inventory.Fabric.fabric_name == 'default').first()
            if not default_fabric:
                raise Exception("Default fabric not found. Please create one named 'default'.")
            # release fabric topo pool records
            topo = db_session.query(DCFabricTopology).filter(DCFabricTopology.fabric_id == fabric.id).first()
            if topo and topo.fabric_config:
                release_pool_record(topo.fabric_config, db_session)

            # remove association fabric data
            db_session.query(inventory.AssociationFabric) \
                .filter(inventory.AssociationFabric.fabric_id == fabric.id) \
                .update({"fabric_id": default_fabric.id})
            db_session.query(inventory.Topology).filter(inventory.Topology.id == fabric.topology_id).delete()
            db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).delete()

            # update alarm rules
            for rule in db_session.query(general.EmailRuleSettings).filter(
                    general.EmailRuleSettings.fabric_name_list.like(f'%{fabric_name}%')).all():
                if fabric_name in rule.fabric_name_list.split(','):
                    rule.fabric_name_list = ','.join(
                        [fabric_name_i for fabric_name_i in rule.fabric_name_list.split(',') if
                         fabric_name_i != fabric_name])

        msg = {'info': 'Success to delete fabric {0}'.format(fabric_name), 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        db_session.rollback()
        msg = {'info': 'Failed to delete fabric {0}'.format(fabric_name), 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/load_fabric/<string:fabric_name>')
@readonly_permission.require(http_exception=403)
def load_fabric(fabric_name):
    db_session = inven_db.get_session()
    fabric = db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first()
    if not fabric:
        return jsonify({'info': "Fabric not found", 'status': 400})
    return jsonify(
        {'data': {'fabric_name': fabric.fabric_name, 'fabric_description': fabric.description}, 'status': 200})


@new_lifecycle_model.route('/load_fabric_switch', methods=["POST"])
@readonly_permission.require(http_exception=403)
def load_fabric_switch():
    params = request.get_json()

    fabric_name = params.get('fabricName')

    db_session = inven_db.get_session()
    fabric = db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first()
    if not fabric:
        return jsonify({'info': "Fabric not found", 'status': 400})

    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED, constants.SwitchStatus.STAGED,
                                                 constants.SwitchStatus.STAGING, constants.SwitchStatus.CONFIGURED,
                                                 constants.SwitchStatus.PROVISIONING_FAILED,
                                                 constants.SwitchStatus.REGISTERED, constants.SwitchStatus.DECOM,
                                                 constants.SwitchStatus.DECOM_MANUAL,
                                                 constants.SwitchStatus.DECOM_INIT,
                                                 constants.SwitchStatus.DECOM_PENDING, constants.SwitchStatus.RMA])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter, fabric=[fabric_name])
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/list_fabric_switch', methods=["POST"])
@readonly_permission.require(http_exception=403)
def list_fabric_switch():
    params = request.get_json()

    fabric_name = params.get('fabricName')

    db_session = inven_db.get_session()
    fabric = db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first()
    if not fabric:
        return jsonify({'info': "Fabric not found", 'status': 400})

    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED, constants.SwitchStatus.STAGED,
                                                 constants.SwitchStatus.STAGING, constants.SwitchStatus.CONFIGURED,
                                                 constants.SwitchStatus.PROVISIONING_FAILED,
                                                 constants.SwitchStatus.REGISTERED, constants.SwitchStatus.DECOM,
                                                 constants.SwitchStatus.DECOM_MANUAL,
                                                 constants.SwitchStatus.DECOM_INIT,
                                                 constants.SwitchStatus.DECOM_PENDING, constants.SwitchStatus.RMA])]

    session = inven_db.get_session()

    fabric_name_id_mapper = dict(map(lambda x: (x.fabric_name, x.id), session.query(inventory.Fabric).filter(
        inventory.Fabric.fabric_name.in_([fabric_name])).all()))
    query = session.query(inventory.Switch).join(inventory.AssociationFabric,
                                                 inventory.Switch.id == inventory.AssociationFabric.switch_id).filter(
        inventory.AssociationFabric.fabric_id.in_(fabric_name_id_mapper.values()))
    query.filter(*extra_filter)
    data = [item.make_dict() for item in query]
    return jsonify({'status': 200, 'data': data})


@new_lifecycle_model.route('/save_fabric', methods=["POST"])
@admin_permission.require(http_exception=403)
def save_fabric():
    params = request.get_json()

    fabric_name = params.get('fabricName')
    del_switches = params.get('delSwitches', [])
    new_switches = params.get('addSwitches', [])
    save_result, save_result_msg = utils.save_switch_to_fabric(fabric_name, new_switches, del_switches)
    return jsonify({'info': save_result_msg, 'status': 200 if save_result else 400})


@new_lifecycle_model.route('/edit_fabric_table_data', methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_fabric_table_data():
    try:
        data = request.get_json()
        fabric_name = data.get("fabricName", None)
        page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch, pre_query=utils.query_switch(
            fabric_list=[fabric_name] if fabric_name else None))
        if not fabric_name:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": False,
                    "reachable_status": switch.reachable_status
                } for switch in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        else:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": selected,
                    "reachable_status": switch.reachable_status
                } for switch, selected in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        return jsonify(response)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get add device modal table data fail', 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/site')
@readonly_permission.require(http_exception=403)
def get_site():
    db_session = inven_db.get_session()
    site_names = list(map(lambda x: x[0], db_session.query(inventory.Site.site_name).all()))
    return jsonify({'data': site_names, 'status': 200})


@new_lifecycle_model.route('/create_site', methods=["POST"])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='create_site', contents='create site')
def create_site():
    params = request.get_json()

    site_name = params.get('siteName', '')
    description = params.get('description', '')

    if not is_name_valid(site_name) or ' ' in site_name or len(site_name) > 32:
        return jsonify({'info': "Site name is invalid", 'status': 400})

    if description and len(description) > 128:
        return jsonify({'info': "Site description is too long", 'status': 400})

    db_session = inven_db.get_session()
    if db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).first():
        return jsonify({'info': "Site already exist", 'status': 400})

    try:
        with db_session.begin():
            site_topology = inventory.Topology()
            site_topology.name = site_name
            site_topology.description = description
            site_topology.topology_type = 'site'
            db_session.add(site_topology)
            db_session.flush()

            site = inventory.Site()
            site.site_name = site_name
            site.description = description
            site.topology_id = site_topology.id
            db_session.add(site)
            site_info = db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).first()
            site_id = site_info.id
            print(f"Site created {add_wireless_site(site_id, site_name, description)}")
        return jsonify({'info': "Site create successfully", 'status': 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        db_session.rollback()
        msg = {'info': 'Site create failed', 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/delete_site', methods=["POST"])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='delete_site', contents='delete site')
def delete_site():
    params = request.get_json()

    site_name = params.get('siteName')

    db_session = inven_db.get_session()
    site = db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).first()
    if not site:
        return jsonify({'info': "Site not found", 'status': 400})

    try:
        with db_session.begin():
            # remove association site data
            as_sites = db_session.query(inventory.AssociationSite).filter(
                inventory.AssociationSite.site_id == site.id).all()
            db_session.query(inventory.AssociationSite).filter(inventory.AssociationSite.site_id == site.id).delete()
            db_session.query(inventory.Topology).filter(inventory.Topology.id == site.topology_id).delete()
            site_id = site.id
            db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).delete()
            default_site_id = db_session.query(inventory.Site).filter(inventory.Site.site_name == 'default').first().id
            for as_site in as_sites:
                new_site_switch = inventory.AssociationSite(site_id=default_site_id, switch_id=as_site.switch_id)
                db_session.add(new_site_switch)

        print(f"Site delete {delete_wireless_site(site_id, site_name)}")

        # update alarm rules
        for rule in db_session.query(general.EmailRuleSettings).filter(
                general.EmailRuleSettings.site_name_list.like(f'%{site_name}%')).all():
            if site_name in rule.site_name_list.split(','):
                rule.site_name_list = ','.join(
                    [site_name_i for site_name_i in rule.site_name_list.split(',') if site_name_i != site_name])

        msg = {'info': 'Success to delete site {0}'.format(site_name), 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        db_session.rollback()
        msg = {'info': 'Failed to delete site {0}'.format(site_name), 'status': 500}
        return jsonify(msg)


def add_wireless_site(site_id, site_name, site_description):
    # 预置RRM配置
    json_data = {"name": site_name, "description": site_description,
                 "deviceRules": {"firmwareUpgrade": "inherit", "rcOnly": "inherit", "rrm": "no",
                     "algorithms": [{"name": "OptimizeChannel", "parameters": "mode=unmanaged_aware"}]}}
    print(f"Adding site: {json_data}")
    try:
        resp = post(ServerType.OWPROV, f'/api/v1/venue/{site_id}', json_data)
        if not resp:
            LOG.error(f"Failed to add site: {site_name}")
            return None
        return resp
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP 错误发生: {http_err}")
    except requests.exceptions.RequestException as req_err:
        print(f"请求错误发生: {req_err}")
    except ValueError:
        print("无法解析响应为 JSON 格式")
    return None


def delete_wireless_site(site_id, site_name):
    print(f"Deleting site: {site_name}")
    try:
        resp = delete(ServerType.OWPROV, f'/api/v1/venue/{site_name}')
        if not resp:
            LOG.error(f"Failed to delete site: {site_name}")
            return None
        # 清理站点下RRM任务相关数据
        delete_rrm_task_record(site_id)
        return resp
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP 错误发生: {http_err}")
    except requests.exceptions.RequestException as req_err:
        print(f"请求错误发生: {req_err}")
    except ValueError:
        print("无法解析响应为 JSON 格式")
    return None


def delete_rrm_task_record(site_id):
    db_session = inven_db.get_session()
    with db_session.begin():
        db_session.query(WirelessRrmTaskResult).filter(WirelessRrmTaskResult.task_id.in_(
            db_session.query(WirelessRrmTaskLog.task_id).filter(WirelessRrmTaskLog.site_id == site_id))).delete()
        db_session.query(WirelessRrmTaskLog).filter(WirelessRrmTaskLog.site_id == site_id).delete()


@new_lifecycle_model.route('/load_site/<string:site_name>')
@readonly_permission.require(http_exception=403)
def load_site(site_name):
    db_session = inven_db.get_session()
    site = db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).first()
    if not site:
        return jsonify({'info': "Site not found", 'status': 400})
    return jsonify({'data': {'site_name': site.site_name, 'site_description': site.description}, 'status': 200})


@new_lifecycle_model.route('/load_site_switch', methods=["POST"])
@readonly_permission.require(http_exception=403)
def load_site_switch():
    params = request.get_json()

    site_name = params.get('siteName')

    db_session = inven_db.get_session()
    site = db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).first()
    if not site:
        return jsonify({'info': "Site not found", 'status': 400})

    extra_filter = [inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                 constants.SwitchStatus.IMPORTED, constants.SwitchStatus.STAGED,
                                                 constants.SwitchStatus.STAGING, constants.SwitchStatus.CONFIGURED,
                                                 constants.SwitchStatus.PROVISIONING_FAILED,
                                                 constants.SwitchStatus.REGISTERED, constants.SwitchStatus.DECOM,
                                                 constants.SwitchStatus.DECOM_MANUAL,
                                                 constants.SwitchStatus.DECOM_INIT,
                                                 constants.SwitchStatus.DECOM_PENDING, constants.SwitchStatus.RMA])]
    page_num, page_size, total_count, data = utils.new_page_helper_lifecycle(extra_filter, site=[site_name])
    return jsonify({'status': 200, 'data': data, 'total': total_count, 'page': page_num, "pageSize": page_size})


@new_lifecycle_model.route('/save_site', methods=["POST"])
@admin_permission.require(http_exception=403)
def save_site():
    params = request.get_json()

    site_name = params.get('siteName')
    del_switches = params.get('delSwitches', [])
    new_switches = params.get('addSwitches', [])

    save_result, save_result_msg = utils.save_switch_to_site(site_name, new_switches, del_switches)
    return jsonify({'info': save_result_msg, 'status': 200 if save_result else 400})


@new_lifecycle_model.route('/edit_site_table_data', methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_site_table_data():
    try:
        data = request.get_json()
        site_name = data.get("siteName", None)
        page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch, pre_query=utils.query_switch(
            site_list=[site_name] if site_name else None))
        lifecycle_query_obj = utils.query_lifecycle_switch_data(query_obj)
        if not site_name:
            response = {
                "data": [{
                    "id": switch_id,
                    "host_name": switch_host_name,
                    "sn": switch_sn,
                    "create_time": switch_create_time.strftime('%Y-%m-%d %H:%M:%S') if switch_create_time else "",
                    "version": switch_version,
                    "revision": switch_revision,
                    "status": switch_status,
                    "mgt_ip": switch_mgt_ip,
                    "platform_model": switch_platform_model,
                    "address": switch_address,
                    "selected": False,
                    "reachable_status": switch_reachable_status,
                    "license_status": license_info.status if license_info else "",
                    "license_expired": license_info.license_expired.strftime(
                        '%Y-%m-%d %H:%M:%S') if license_info else "",
                } for switch_sn, switch_id, switch_host_name,
                switch_create_time, switch_version, switch_revision,
                switch_status, switch_mgt_ip, switch_platform_model, switch_address,
                switch_selected, switch_reachable_status, license_info,
                switch_system_info, switch_config_backup in lifecycle_query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        else:
            response = {
                "data": [{
                    "id": switch_id,
                    "host_name": switch_host_name,
                    "sn": switch_sn,
                    "create_time": switch_create_time.strftime('%Y-%m-%d %H:%M:%S') if switch_create_time else "",
                    "version": switch_version,
                    "revision": switch_revision,
                    "status": switch_status,
                    "mgt_ip": switch_mgt_ip,
                    "platform_model": switch_platform_model,
                    "address": switch_address,
                    "selected": switch_selected,
                    "reachable_status": switch_reachable_status,
                    "license_status": license_info.status if license_info else "",
                    "license_expired": license_info.license_expired.strftime(
                        '%Y-%m-%d %H:%M:%S') if license_info else "",
                } for switch_sn, switch_id, switch_host_name,
                switch_create_time, switch_version, switch_revision,
                switch_status, switch_mgt_ip, switch_platform_model, switch_address,
                switch_selected, switch_reachable_status, license_info,
                switch_system_info, switch_config_backup in lifecycle_query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        return jsonify(response)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get add device modal table data fail', 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/fetch_site_table_data', methods=["POST"])
@readonly_permission.require(http_exception=403)
def fetch_site_table_data():
    try:
        # 数据获取
        data = request.get_json()
        site_name = data.get("siteName", None)
        selected_switch_id_list = data.get("selectedSwitchIdList", [])
        session = inven_db.get_session()
        type = data.get("layerType", None)

        core_switch_id_list = []
        access_switch_id_list = []

        if type == "core":
            core_switch_id_list = list(
                map(
                    lambda x: x[0],
                    session.query(inventory.AssociationSite.switch_id)
                    .join(inventory.Site, inventory.AssociationSite.site_id == inventory.Site.id)
                    .join(inventory.Switch,
                          inventory.AssociationSite.switch_id == inventory.Switch.id)
                    .filter(
                        inventory.Site.site_name.in_([site_name]),
                        inventory.Switch.platform_model.in_(constants.CORE_SWITCH_MODEL)
                    )
                    .all()
                )
            )

            page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch,
                                                                             pre_query=utils.query_switch(
                                                                                 sn_list=selected_switch_id_list)
                                                                             .filter(inventory.Switch.id.in_(
                                                                                 core_switch_id_list))
                                                                             .filter(
                                                                                 inventory.Switch.reachable_status == 0))

        elif type == "access":
            access_switch_id_list = list(
                map(
                    lambda x: x[0],
                    session.query(inventory.AssociationSite.switch_id)
                    .join(inventory.Site, inventory.AssociationSite.site_id == inventory.Site.id)
                    .join(inventory.Switch,
                          inventory.AssociationSite.switch_id == inventory.Switch.id)
                    .filter(
                        inventory.Site.site_name.in_([site_name]),
                        inventory.Switch.platform_model.in_(constants.ACCESS_SWITCH_MODEL)
                    )
                    .all()
                )
            )

            page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch,
                                                                             pre_query=utils.query_switch(
                                                                                 sn_list=selected_switch_id_list).filter(
                                                                                 inventory.Switch.id.in_(
                                                                                     access_switch_id_list)).filter(
                                                                                 inventory.Switch.reachable_status == 0))

        # 返回数据
        if not selected_switch_id_list:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "mac_addr": switch.mac_addr,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": False,
                    "reachable_status": switch.reachable_status
                } for switch in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        elif core_switch_id_list:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "mac_addr": switch.mac_addr,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": selected,
                    "reachable_status": switch.reachable_status
                } for switch, selected in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        elif access_switch_id_list:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "mac_addr": switch.mac_addr,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": selected,
                    "reachable_status": switch.reachable_status
                } for switch, selected in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }

        return jsonify(response)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get site device modal table data fail', 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/fetch_site_table_data_in_ipclos', methods=["POST"])
@readonly_permission.require(http_exception=403)
def fetch_site_table_data_in_ipclos():
    try:
        # 数据获取
        data = request.get_json()
        site_name = data.get("siteName", None)
        selected_switch_id_list = data.get("selectedSwitchIdList", [])
        session = inven_db.get_session()
        type = data.get("layerType", None)

        core_switch_id_list = []
        access_switch_id_list = []
        distribution_switch_id_list = []
        border_switch_id_list = []

        if type == "border":
            border_switch_id_list = list(
                map(
                    lambda x: x[0],
                    session.query(inventory.AssociationSite.switch_id)
                    .join(inventory.Site, inventory.AssociationSite.site_id == inventory.Site.id)
                    .join(inventory.Switch,
                          inventory.AssociationSite.switch_id == inventory.Switch.id)
                    .filter(
                        inventory.Site.site_name.in_([site_name]),
                        inventory.Switch.platform_model.in_(constants.IPCLOS_BORDER_SWITCH_MODEL)
                    )
                    .all()
                )
            )

            page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch,
                                                                             pre_query=utils.query_switch(
                                                                                 sn_list=selected_switch_id_list).filter(
                                                                                 inventory.Switch.id.in_(
                                                                                     border_switch_id_list)).filter(
                                                                                 inventory.Switch.reachable_status == 0))

        if type == "core":
            core_switch_id_list = list(
                map(
                    lambda x: x[0],
                    session.query(inventory.AssociationSite.switch_id)
                    .join(inventory.Site, inventory.AssociationSite.site_id == inventory.Site.id)
                    .join(inventory.Switch,
                          inventory.AssociationSite.switch_id == inventory.Switch.id)
                    .filter(
                        inventory.Site.site_name.in_([site_name]),
                        inventory.Switch.platform_model.in_(constants.IPCLOS_CORE_SWITCH_MODEL)
                    )
                    .all()
                )
            )

            page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch,
                                                                             pre_query=utils.query_switch(
                                                                                 sn_list=selected_switch_id_list).filter(
                                                                                 inventory.Switch.id.in_(
                                                                                     core_switch_id_list)).filter(
                                                                                 inventory.Switch.reachable_status == 0))

        elif type == "access":
            access_switch_id_list = list(
                map(
                    lambda x: x[0],
                    session.query(inventory.AssociationSite.switch_id)
                    .join(inventory.Site, inventory.AssociationSite.site_id == inventory.Site.id)
                    .join(inventory.Switch,
                          inventory.AssociationSite.switch_id == inventory.Switch.id)
                    .filter(
                        inventory.Site.site_name.in_([site_name]),
                        inventory.Switch.platform_model.in_(constants.IPCLOS_ACCESS_SWITCH_MODEL)
                    )
                    .all()
                )
            )

            page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch,
                                                                             pre_query=utils.query_switch(
                                                                                 sn_list=selected_switch_id_list).filter(
                                                                                 inventory.Switch.id.in_(
                                                                                     access_switch_id_list)).filter(
                                                                                 inventory.Switch.reachable_status == 0))

        elif type == "distribution":
            distribution_switch_id_list = list(
                map(
                    lambda x: x[0],
                    session.query(inventory.AssociationSite.switch_id)
                    .join(inventory.Site, inventory.AssociationSite.site_id == inventory.Site.id)
                    .join(inventory.Switch,
                          inventory.AssociationSite.switch_id == inventory.Switch.id)
                    .filter(
                        inventory.Site.site_name.in_([site_name]),
                        inventory.Switch.platform_model.in_(constants.IPCLOS_DISTRIBUTION_SWITCH_MODEL)
                    )
                    .all()
                )
            )

            page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch,
                                                                             pre_query=utils.query_switch(
                                                                                 sn_list=selected_switch_id_list).filter(
                                                                                 inventory.Switch.id.in_(
                                                                                     distribution_switch_id_list)).filter(
                                                                                 inventory.Switch.reachable_status == 0))

        # 返回数据
        if not selected_switch_id_list:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "mac_addr": switch.mac_addr,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": False,
                    "reachable_status": switch.reachable_status
                } for switch in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        elif core_switch_id_list:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "mac_addr": switch.mac_addr,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": selected,
                    "reachable_status": switch.reachable_status
                } for switch, selected in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        elif access_switch_id_list:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "mac_addr": switch.mac_addr,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": selected,
                    "reachable_status": switch.reachable_status
                } for switch, selected in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        elif distribution_switch_id_list:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "mac_addr": switch.mac_addr,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": selected,
                    "reachable_status": switch.reachable_status
                } for switch, selected in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        elif border_switch_id_list:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "mac_addr": switch.mac_addr,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": selected,
                    "reachable_status": switch.reachable_status
                } for switch, selected in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        return jsonify(response)

    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get site device modal table data fail', 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/lifecycle_renewal/<string:action>/<string:group>')
def lifecycle_renewal(action, group):
    date_time = (date.today() + timedelta(days=30)).strftime('%Y-%m-%d')
    report_time = time.strftime("%Y-%m-%d %H:%M", time.localtime()) + "<" + action + " report> "
    switch_task_running = AmpConBaseTask.get_running_job_by_task_name('switch_%s_%s' % (action, group))
    if switch_task_running:
        LOG.info('license is in upgrade')
        return jsonify({'info': 'license is in upgrade', 'status': 200})
    else:
        # useless switch_op_user, switch_op_password
        switch_op_user, switch_op_password = utils.get_switch_default_user()
        batch_upgrade_license.delay('', group, '', '', switch_op_user,
                                    switch_op_password, action, date_time,
                                    report_time=report_time, celery_task_name=f"switch_{action}_{group}",
                                    celery_group=group)
    return jsonify({'info': 'upgrade license', 'status': 200})


@new_lifecycle_model.route('/get_local_license', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_local_license():
    db_session = inven_db.get_session()
    local_licenses = db_session.query(inventory.License).filter(inventory.License.local_lic.isnot(None))
    page_num, page_size, total_count, query_license = utils.query_helper(inventory.License, local_licenses)
    return jsonify({"data": [license.make_dict() for license in query_license], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_lifecycle_model.route('/local_license_save', methods=["POST"])
@admin_permission.require(http_exception=403)
def local_license_save():
    info = request.get_json()
    sn = str(info['sn'])
    lic = info['license']

    # check the sn/lic is given
    if sn == '' or lic == '':
        msg = {'info': 'Please provide SN and License string', 'status': 500}
        return jsonify(msg)

    # check the sn is exist or not
    lic_entry = inven_db.get_collection(inventory.Switch, filters={'sn': [sn]})
    if lic_entry:
        new_lic = inventory.License()
        new_lic.sn_num = sn
        new_lic.local_lic = lic
        inven_db.insert_or_update(new_lic, primary_key='sn_num')
        msg = {'info': 'The license update success', 'status': 200}
        return jsonify(msg)
    else:
        msg = {'info': 'Switch not configured', 'status': 500}
        return jsonify(msg)


@new_lifecycle_model.route('/local_license_delete', methods=["POST"])
@admin_permission.require(http_exception=403)
def local_license_delete():
    info = request.get_json()
    sn = str(info['sn'])

    inven_db.delete_collection(inventory.License, filters={'sn_num': [sn]})
    msg = {'info': 'The license delete success', 'status': 200}
    return jsonify(msg)


@new_lifecycle_model.route('/update_link_ip_addr', methods=["POST"])
@admin_permission.require(http_exception=403)
def update_link_ip_addr():
    msg = {'status': 200, 'info': 'Success to update link IP address'}
    try:
        utils.update_vpn_link_ip_addr()
    except Exception as e:
        msg = {'status': 500, 'info': 'Fail to update link IP address: {0}'.format(e)}
    return jsonify(msg)


@new_lifecycle_model.route('/update_hostname', methods=["POST"])
@admin_permission.require(http_exception=403)
def update_hostname():
    params = request.get_json()
    try:
        for sn, ip in params.items():
            user, pw = utils.get_switch_default_user(sn=sn)
            new_host_name, code = conn_client.interactive_shell_linux('hostname', ip, username=user, password=pw)
            if code != constants.RMA_ACTIVE:
                continue
            inven_db.update_model(inventory.Switch, filters={'sn': [sn], 'ip': [ip]}, updates={
                inventory.Switch.host_name: new_host_name
            })
        msg = {'status': 200, 'info': 'Success to update hostname'}
    except Exception as e:
        LOG.error('update switch host name failed, %s', e)
        msg = {'status': 500, 'info': 'Failed to update hostname'}
    return jsonify(msg)


@new_lifecycle_model.route('/get_all_fabric_switch', methods=["GET"])
@readonly_permission.require(http_exception=403)
def get_all_fabric_switch():
    db_session = inven_db.get_session()

    valid_status = [
        constants.SwitchStatus.PROVISIONING_SUCCESS,
        constants.SwitchStatus.IMPORTED,
        constants.SwitchStatus.STAGED,
        constants.SwitchStatus.STAGING,
        constants.SwitchStatus.CONFIGURED,
        constants.SwitchStatus.PROVISIONING_FAILED,
        constants.SwitchStatus.REGISTERED,
        constants.SwitchStatus.DECOM,
        constants.SwitchStatus.DECOM_MANUAL,
        constants.SwitchStatus.DECOM_INIT,
        constants.SwitchStatus.DECOM_PENDING,
        constants.SwitchStatus.RMA,
    ]

    with db_session.begin():
        fabrics = db_session.query(inventory.Fabric).all()
        fabric_name_id_mapper = {f.fabric_name: f.id for f in fabrics}
        query = db_session.query(inventory.Switch, inventory.AssociationFabric) \
            .join(inventory.AssociationFabric, inventory.Switch.id == inventory.AssociationFabric.switch_id) \
            .filter(inventory.Switch.status.in_(valid_status),
                    inventory.AssociationFabric.fabric_id.in_(fabric_name_id_mapper.values()))
        from collections import defaultdict
        fabric_switch_map = defaultdict(list)
        for switch, assoc in query.all():
            fabric_name = next((name for name, fid in fabric_name_id_mapper.items() if fid == assoc.fabric_id), None)
            if fabric_name and switch.sn:
                fabric_switch_map[fabric_name].append({
                    "title": switch.host_name or switch.sn,
                    "value": switch.sn
                })
        result = [{"fabric": name, "children": switches} for name, switches in fabric_switch_map.items()]
        return jsonify({"status": 200, "data": result})
