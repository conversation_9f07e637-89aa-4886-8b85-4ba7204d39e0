import json
import ast
import logging
import traceback
import threading
import ipaddress
import copy
from flask import Blueprint, jsonify, Response, request
from datetime import timedelta, datetime, date
from sqlalchemy import func

from server.util import utils
from server.util.permission import admin_permission
from server.db.models.dc_virtual_resource import dc_virtual_resource_db, DCVirtualResourcePoolAZ, DCVirtualResourcesVM, DCVirtualResourceNetwork, DCVirtualResourceVPC, DCVirtualResourceHost, \
                                                    NodeTemplate, NodeGroup, NodeHost, NodeNicPortgroup, SwitchPortgroup, SwitchPortgroupInfo, VlanDomainGroup, DCVirtualResourceHostLink, SwitchPortgroupConfig
from server.db.models.dc_blueprint import dc_fabric_db, DCFabricTopology
from server.db.models.resource_pool import ResourcePoolVlanDomain, resource_pool_vlandomain_db
from server.db.models import inventory
from server.constants import CloudPlatform
from server.util.fabric_topology_util import get_switch_port_info, get_switch_lag_id, get_baremetal_switch_portgroup_config, get_cloudplatform_link_port_config
from sqlalchemy import or_, asc, desc, and_
from server.celery_app.config_distribution_task import config_distribution_netconf_by_dc_overlay

inven_db = inventory.inven_db


dc_virtual_resource_mold = Blueprint(
    "dc_virtual_resource", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)

# AZ start
@dc_virtual_resource_mold.route("/virtual_resource_pool_az/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def virtual_resource_pool_az_list():
    try:
        data = request.get_json()
        sort_fields = data.get("sortFields", [])
        fabric_name_order = ""
        for field_order in sort_fields:
            if field_order.get("field") == "fabric_name":
                fabric_name_order = field_order.get("order")
                sort_fields.remove(field_order)
                break
        page_num, page_size, total_count, query_obj = utils.query_helper(
            DCVirtualResourcePoolAZ, data=data)
        
        res = []       
        for az in query_obj:
            info = {
                "id": az.id,
                "az_name": az.az_name,
                "fabric_id": az.fabric_id,
                "fabric_name": az.fabric_name,
                "resource_type": az.resource_type,
                "global_vlan": str(az.global_vlan) if az.global_vlan else [],
                "global_vni_vlan": str(az.global_vni_vlan) if az.global_vni_vlan else [],
                "connect_status": az.connect_status,
                "connect_active_time": az.connect_active_time,
                "auth_info": ast.literal_eval(az.auth_info) if az.auth_info else {},
            } 
            
            resource = None
            if az.resource_type == CloudPlatform.BAREMETAL:
                resource = dc_virtual_resource_db.get_node_group_by_az_id(az.id)
            else:
                session = inven_db.get_session()
                hosts = session.query(DCVirtualResourceHost).filter(DCVirtualResourceHost.az_id == az.id).all()
                for host in hosts:
                     if host.host_link:
                         resource = host.host_link
                         break
            if resource:
                info["usage_state"] = True
            else:
                info["usage_state"] = False
            res.append(info)
        
        response = {
            "data": res,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        if fabric_name_order != "":
            response.get("data").sort(key=lambda x: x.get("fabric_name", ""), reverse=(True if fabric_name_order == "desc" else False))
        return jsonify(response)
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Failed to list AZ'})


@dc_virtual_resource_mold.route("/virtual_resource_pool_az/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def virtual_resource_pool_az_save():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        id = data.get('id', None)
        az_name = data.get('az_name')
        # Get Fabric ID by Fabric Name
        fabric_id = inven_db.get_fabric_id_by_name(data.get('fabric_name'))
        resource_type = data.get('resource_type')
        global_vlan = str(data.get('global_vlan', []))
        global_vni_vlan = str(data.get('global_vni_vlan',[]))
        auth_info = str(data.get('auth_info', {}))

        dc_virtual_resource_db.update_virtual_resource_pool_az(
            id, az_name, fabric_id, resource_type, global_vlan, global_vni_vlan, auth_info)

        return jsonify({'status': 200, 'info': 'Save Pod successed.'})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Save Pod failed. {str(e)}'})


@dc_virtual_resource_mold.route("/virtual_resource_pool_az/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def virtual_resource_pool_az_delete():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        az_id = data.get('az_id')

        # TODO: Check whether AZ resource is in use !!!

        dc_virtual_resource_db.del_virtual_resource_pool_az_by_id(az_id)
        return jsonify({'status': 200, 'info': 'Delete AZ successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Delete AZ failed.'})
# AZ End

# VPC start


@dc_virtual_resource_mold.route("/virtual_resource_vpc/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def virtual_resource_vpc_list():
    try:
        az_id = request.get_json().get('az_id', None)
        if not az_id:
            return jsonify({'status': 400, 'info': 'Invalid request data, az_id is required.'})
        db_session = inven_db.get_session()
        pre_query_obj = db_session.query(DCVirtualResourceVPC).filter(DCVirtualResourceVPC.az_id == az_id)

        page_num, page_size, total_count, query_obj = utils.query_helper(
            DCVirtualResourceVPC, pre_query=pre_query_obj)
        response = {
            "data": [{
                "id": vpc.id,
                "vpc_id": vpc.vpc_id,
                "vpc_name": vpc.vpc_name,
                "tenant": "admin",
                "fabric_id": vpc.fabric_id,
                "fabric_name": vpc.fabric_name,
                "az_id": vpc.az_id,
                "az_name": vpc.az_name,
                "resource_create_time": vpc.resource_create_time,
            } for vpc in query_obj],
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        return jsonify(response)
    except Exception as e:
        return jsonify({'status': 400, 'info': 'Failed to list VPCs'})

# @dc_virtual_resource_mold.route("/virtual_resource_vpc/save", methods=["POST"])
# @admin_permission.require(http_exception=403)
# def virtual_resource_vpc_save():
#     pass

# @dc_virtual_resource_mold.route("/virtual_resource_vpc/delete", methods=["POST"])
# @admin_permission.require(http_exception=403)
# def virtual_resource_vpc_delete():
#     pass
# VPC end

# VM start


@dc_virtual_resource_mold.route("/virtual_resource_vm/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def virtual_resource_vm_list():
    try:
        data = request.get_json()
        az_id = data.get('az_id', None)
        if not az_id:
            return jsonify({'status': 400, 'info': 'Invalid request data, az_id is required.'})
        db_session = inven_db.get_session()
        pre_query_obj = db_session.query(
            DCVirtualResourcesVM,
            DCVirtualResourceVPC.vpc_name
        ).join(
            DCVirtualResourceVPC, DCVirtualResourcesVM.vpc_id == DCVirtualResourceVPC.id
        ).filter(
            DCVirtualResourcesVM.az_id == az_id
        )
        sort_fields = data.get("sortFields", [])
        if sort_fields:
            for field in sort_fields:
                if field["field"] == "vpc_name":
                    if field["order"] == "asc":
                        pre_query_obj = pre_query_obj.order_by(asc(DCVirtualResourceVPC.vpc_name))
                    elif field["order"] == "desc":
                        pre_query_obj = pre_query_obj.order_by(desc(DCVirtualResourceVPC.vpc_name))
            new_sort_fields = [field for field in sort_fields if field["field"] != "vpc_name"]
            data["sortFields"] = new_sort_fields
        page_num, page_size, total_count, query_obj = utils.query_helper(
            DCVirtualResourcesVM, pre_query=pre_query_obj, data=data)
        response = {
            "data": [{
                "id": vm.id,
                "vm_name": vm.vm_name,
                "vm_ip_address": ast.literal_eval(vm.vm_ip_address) if vm.vm_ip_address else [],
                "host_ip_address": vm.host_ip_address,
                "network_name": ast.literal_eval(vm.network_name) if vm.network_name else [],
                "fabric_id": vm.fabric_id,
                "fabric_name": vm.fabric_name,
                "az_id": vm.az_id,
                "az_name": vm.az_name,
                "vpc_id": vm.vpc_id,
                "vpc_name": vm.vpc_name,
                "power_status": vm.power_status,
                "info": vm.info,
            } for vm, vpc_name in query_obj],
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        return jsonify(response)
    except Exception as e:
        return jsonify({'status': 400, 'info': 'Failed to list VMs'})

# @dc_virtual_resource_mold.route("/virtual_resource_vm/save", methods=["POST"])
# @admin_permission.require(http_exception=403)
# def virtual_resource_vm_save():
#     pass

# @dc_virtual_resource_mold.route("/virtual_resource_vm/delete", methods=["POST"])
# @admin_permission.require(http_exception=403)
# def virtual_resource_vm_delete():
#     pass

# VM end

# Network start


@dc_virtual_resource_mold.route("/virtual_resource_network/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def virtual_resource_network_list():
    try:
        data = request.get_json()
        az_id = data.get('az_id', None)
        if not az_id:
            return jsonify({'status': 400, 'info': 'Invalid request data, az_id is required.'})
        db_session = inven_db.get_session()
        pre_query_obj = db_session.query(
            DCVirtualResourceNetwork,
            DCVirtualResourceVPC.vpc_name
        ).join(
            DCVirtualResourceVPC, DCVirtualResourceNetwork.vpc_id == DCVirtualResourceVPC.id
        ).filter(
            DCVirtualResourceNetwork.az_id == az_id
        )
        sort_fields = data.get("sortFields", [])
        if sort_fields:
            for field in sort_fields:
                if field["field"] == "vpc_name":
                    if field["order"] == "asc":
                        pre_query_obj = pre_query_obj.order_by(asc(DCVirtualResourceVPC.vpc_name))
                    elif field["order"] == "desc":
                        pre_query_obj = pre_query_obj.order_by(desc(DCVirtualResourceVPC.vpc_name))
            new_sort_fields = [field for field in sort_fields if field["field"] != "vpc_name"]
            data["sortFields"] = new_sort_fields
        page_num, page_size, total_count, query_obj = utils.query_helper(
            DCVirtualResourceNetwork, pre_query=pre_query_obj, data=data)
        response = {
            "data": [{
                "id": network.id,
                "network_name": network.network_name,
                "fabric_id": network.fabric_id,
                "fabric_name": network.fabric_name,
                "az_id": network.az_id,
                "az_name": network.az_name,
                "vpc_id": network.vpc_id,
                "vpc_name": network.vpc_name,
                "vm_count": network.vm_count if network.vm_count else "--",
                "host_count": network.host_count if network.host_count else "--",
                "vlan_id": network.vlan_id if network.vlan_id else "--",
                "resource_create_time": network.resource_create_time,
            } for network, vpc_name in query_obj],
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        return jsonify(response)
    except Exception as e:
        return jsonify({'status': 400, 'info': 'Failed to list networks'})

# @dc_virtual_resource_mold.route("/virtual_resource_network/save", methods=["POST"])
# @admin_permission.require(http_exception=403)
# def virtual_resource_network_save():
#     pass

# @dc_virtual_resource_mold.route("/virtual_resource_network/delete", methods=["POST"])
# @admin_permission.require(http_exception=403)
# def virtual_resource_network_delete():
#     pass

# Network end

# Host start
@dc_virtual_resource_mold.route("/virtual_resource_host/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def virtual_resource_host_list():
    try:  
        data = request.get_json()
        sort_fields = data.get("sortFields", [])
        if sort_fields:
            sort_field = sort_fields[0].get("field")
            order = sort_fields[0].get("order", "asc")
        else:
            sort_field = ""
      
        session = inven_db.get_session()
        az = session.query(DCVirtualResourcePoolAZ).filter(DCVirtualResourcePoolAZ.resource_type.in_([CloudPlatform.OPENSTACK, CloudPlatform.VSPHERE])).all()
        az_id_list = [item.id for item in az]
        
        db_fields = {
            "fabric_name": inventory.Fabric.fabric_name,
            "az_name":  DCVirtualResourcePoolAZ.az_name
        }
        pre_query = session.query(
            DCVirtualResourceHost,
            inventory.Fabric.fabric_name,
            DCVirtualResourcePoolAZ
        ).join(
            DCVirtualResourcePoolAZ, DCVirtualResourceHost.az_id == DCVirtualResourcePoolAZ.id
        ).join(
            inventory.Fabric, DCVirtualResourcePoolAZ.fabric_id == inventory.Fabric.id
        ).filter(DCVirtualResourceHost.az_id.in_(az_id_list))
        if sort_field and sort_field in db_fields:

            if order == "asc":
                pre_query = pre_query.order_by(db_fields[sort_field].asc())
            else:
                pre_query = pre_query.order_by(db_fields[sort_field].desc())
            page_num, page_size, total_count, query_info = utils.query_helper(DCVirtualResourceHost, skip_sort=True, pre_query=pre_query)
        else:
            page_num, page_size, total_count, query_info = utils.query_helper(DCVirtualResourceHost, pre_query=pre_query)
        
        res = []
        for host, fabric_name, az in query_info:
            # print(host)
            host_info = host.make_dict()
            host_info["fabric_id"] = host.az.fabric_id
            host_info["fabric_name"] = host.az.fabric_name
            host_info["az_name"] = host.az.az_name
            host_info["az_type"] = host.az.resource_type
            host_info["az_global_vlan"] = json.loads(host.az.global_vlan.replace("'", '"'))
            host_info["az_global_vni_vlan"] = json.loads(host.az.global_vni_vlan.replace("'", '"'))
            host_info["az_connect_status"] = host.az.connect_status
            host_info["usage_state"] = False
            host_info['host_link'] = []
                
            for link in host.host_link:
                link_info = link.make_dict()
                link_info['port_info'] = []
                for link_mapping in link.link_mapping:
                    for network in link_mapping.network:
                        if network.virtual_network_id:
                            host_info["usage_state"] = True
                            break
                    if host_info["usage_state"]:
                        break
                if link.link_count and link.link_count == "single":
                    link_info['link_count'] = "1"
                elif link.link_count and link.link_count == "dual":
                    link_info['link_count'] = "2"

                if link.link_type == "MLAG Leaf":
                    if len(link.link_ports) == 1:
                        link_info['access_mlag_mode'] = "Single-Homed"
                        link_info['peer_leaf'] = link.link_ports[0].logic_device.node_info.get("hostname", "")
                    else:
                        link_info['access_mlag_mode'] = "Dual-Homed"

                for port in link.link_ports:
                    port_info = {
                        'port_id': port.id,
                        'logic_device_id': port.logic_device_id,
                        'logic_device': port.logic_device.logic_name,
                        'switch_sn': port.switch_sn,
                        'port_name': [port.strip() for port in port.port_name.split(',')],
                        'disable_port_list': [port.strip() for port in port.disable_port_list.split(',') if port.strip()]
                    }
                    link_info['port_info'].append(port_info)

                host_info['host_link'].append(link_info)
                    
            res.append(host_info)
        
        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List Host failed.'})


@dc_virtual_resource_mold.route("/virtual_resource_host/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def virtual_resource_host_save():
    session = dc_virtual_resource_db.get_session()
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        host_id = data.get('id', None)
        host_name = data.get('host_name')
        description = data.get('description', "")
        management_ip = data.get('management_ip', "")
        username = data.get('username', "")
        password = data.get('password', "")
        az_id = data.get('az_id')
        host_link = data.get('host_link', [])
        
        az = dc_virtual_resource_db.get_virtual_resource_pool_az_by_id(az_id)
        if not az:
            return jsonify({'status': 500, 'info': f'Save node failed. az: {az_id} not found'})
        
        if host_id:
            host = dc_virtual_resource_db.get_virtual_resource_host_by_id(host_id)
            if host.status == "Deploying":
                return jsonify({'status': 500, 'info': f'Edit node failed. Deploy task is running, please wait!'})

        session.begin()
        host = dc_virtual_resource_db.update_virtual_resource_host(
            host_id=host_id, host_name=host_name, description=description, management_ip=management_ip, 
            username=username, password=password, az_id=az_id, session=session
        )
        
        usage_state = False                    
        for link in host.host_link:
            for link_mapping in link.link_mapping:
                for network in link_mapping.network:
                    if network.virtual_network_id:
                        usage_state = True
                        break
                if usage_state:
                    break
                 
        ## 如果已经被使用 只能修改description 直接返回   
        if usage_state:
            session.commit() 
            return jsonify({'status': 200, 'info': 'The node configurations are saved successfully.'})
        
        existing_link = []
        if host_id:
            existing_link = dc_virtual_resource_db.get_virtual_resource_host_link(host_id)
        
        edit_link = []
        del_port = []
        del_link = []
        for link in host_link:
            link_id = link.get('id', None)
            if link_id:
                edit_link.append(link_id)
            vlan_domain_id = link.get('vlan_domain_id', None)
            port_group_name = link.get('port_group_name', "")
            connect_mode = link.get('connect_mode', "")
            link_type = link.get('link_type', "")
            link_count = link.get('link_count', "")
            port_info = link.get('port_info', [])
            
            if not vlan_domain_id:
                raise Exception(f"vlan domain not found")

            link_info = dc_virtual_resource_db.update_virtual_resource_host_link(
                link_id=link_id, host_id=host.id, vlan_domain_id=vlan_domain_id, port_group_name=port_group_name, 
                connect_mode=connect_mode, link_type=link_type, link_count=link_count, session=session
            )
            
            existing_port = []
            if link_id:
                existing_port = dc_virtual_resource_db.get_virtual_resource_host_link_port(link_id)
            
            lag_id_list = []
            if link_type == "MLAG Leaf"  or int(link_count) > 1:
                ## 获取lag id 
                ## 对于mlag 两台设备的聚合口都要同步 所以找到两台设备获取lag id
                nodes = dc_fabric_db.get_fabric_topology_node_by_vd(vlan_domain_id)
                logic_device_id_list = [node.id for node in nodes]
                lag_id_list = get_switch_lag_id(logic_device_id_list, 1)
                print(lag_id_list) 
                
            lag_id=lag_id_list[0] if lag_id_list else 0   
            
            edit_port = []
            for port in port_info:
                port_id = port.get('port_id', None)
                self_used_ports = []
                if port_id:
                    edit_port.append(port_id)
                    existing_port_info = dc_virtual_resource_db.get_virtual_resource_host_link_port_by_id(port_id)
                    self_used_ports = [port.strip() for port in existing_port_info.port_name.split(',')]
                logic_device_id = port.get('logic_device_id', None)
                switch_sn = port.get('switch_sn', "")
                port_name = ','.join(port.get('port_name',[]))
                disable_port = ','.join(port.get('disable_port_list',[]))
                
                if not switch_sn or not logic_device_id:
                    raise Exception(f"logic device or switch_sn not found")
                
                _, _, used_ports = get_switch_port_info(logic_device_id)
                filtered_list = [item for item in used_ports if item not in self_used_ports]
                invalid_port = list(set(port.get('port_name',[])) & set(filtered_list))
                if invalid_port:
                    raise Exception(f"{invalid_port} already in use")
                
                dc_virtual_resource_db.update_virtual_resource_host_link_port(port_id=port_id, link_id=link_info.id, logic_device_id=logic_device_id, switch_sn=switch_sn, port_name=port_name, 
                                                                              session=session, disable_port=disable_port, lag_id=lag_id)
                
            # 处理删除的port 
            for port in existing_port:
                if port.id not in edit_port:
                    del_port.append(port.id)
               
        # 处理删除的link 
        for link in existing_link:
            if link.id not in edit_link:
                del_link.append(link.id)
                query_port = dc_virtual_resource_db.get_virtual_resource_host_link_port(link.id)
                for port in query_port:
                    del_port.append(port.id)
        
        session.flush() 
        print(del_link, del_port)
        config_dict = get_cloudplatform_link_port_config(host)
        
        host = dc_virtual_resource_db.update_virtual_resource_host(host_id=host.id, status="Deploying", session=session)
        host_links = dc_virtual_resource_db.get_virtual_resource_host_link(host.id)
        for host_link in host_links:
            if host_link.id not in del_link:
                dc_virtual_resource_db.update_virtual_resource_host_link(link_id=host_link.id, status="Deploying", session=session)
            else:
                dc_virtual_resource_db.update_virtual_resource_host_link(link_id=host_link.id, status="Deleting", session=session)
            link_ports = dc_virtual_resource_db.get_virtual_resource_host_link_port(host_link.id)
            for port_info in link_ports:
                if port_info.id not in del_port:
                    dc_virtual_resource_db.update_virtual_resource_host_link_port(port_id=port_info.id, status="Deploying", session=session)
                else:
                    dc_virtual_resource_db.update_virtual_resource_host_link_port(port_id=port_info.id, status="Deleting", session=session)

        session.commit()
        ## TODO 调用配置下发
        print(config_dict, del_link, del_port)
        callback_kwargs = {
            "host_id": host.id
        }
        config_distribution_netconf_by_dc_overlay(config_dict, "uplink_cloud", 
                                                  callback="server.util.fabric_topology_util.cloudplatform_switch_portgroup_callback", callback_kwargs=callback_kwargs)
        return jsonify({'status': 200, 'info': 'The node configurations are saved successfully.'})

    except Exception as e:
        LOG.error(traceback.format_exc())
        session.rollback()
        return jsonify({'status': 500, 'info': f'Save node failed. {str(e)}'})
   
 
@dc_virtual_resource_mold.route("/virtual_resource_host/list_link_info", methods=["POST"])
@admin_permission.require(http_exception=403)
def list_virtual_host_link_info():
    try: 
        
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})   
        host_id = data.get('id', None)
        
        session = dc_virtual_resource_db.get_session()
        host = dc_virtual_resource_db.get_virtual_resource_host_by_id(host_id)
               
        res = []
        for link in host.host_link:   
            vlan_domain_pool = session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id == link.vlan_domain_id).first() 
            for port_info in link.link_ports:
                info = {
                    "pg_info_id": port_info.id,
                    "node_name": host.host_name,
                    "port_count": link.link_count,
                    "switch_port_group": link.port_group_name,
                    "vlan_domain": vlan_domain_pool.name,
                    "link_type": link.link_type,
                    "switch_sn": port_info.switch_sn,
                    "switch_port": port_info.port_name,
                    "port_mode": link.connect_mode,
                    "status": port_info.status
                    
                }
                res.append(info)

        return jsonify({"data": res, "status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List Node Group failed.'})
    

# @dc_virtual_resource_mold.route("/virtual_resource_host/deploy_test", methods=["POST"])
# @admin_permission.require(http_exception=403)
# def virtual_resource_host_deploy_test():
#     session = dc_virtual_resource_db.get_session()
#     try:
#         data = request.get_json()
#         if not data:
#             return jsonify({'info': "Invalid request data", 'status': 400})

#         host_id = data.get('id', None)
#         host = dc_virtual_resource_db.get_virtual_resource_host_by_id(host_id)
                                
#         config_dict = get_cloudplatform_link_port_config(host)
        
#         # dc_virtual_resource_db.update_node_group(group_id=node_group.id, status="Deploying")
#         # for switch_pg in node_group.switch_portgroup: 
#         #     dc_virtual_resource_db.update_switch_portgroup(portgroup_id=switch_pg.id, status="Deploying")
#         #     for pg_info in switch_pg.switch_portgroup_info:
#         #         dc_virtual_resource_db.update_switch_portgroup_info(info_id=pg_info.id, status="Deploying") 
        
#         ## 调用配置下发
#         print(config_dict)
#         callback_kwargs = {
#             "host_id": host.id
#         }
#         config_distribution_netconf_by_dc_overlay(config_dict, "uplink_cloud", callback="server.util.fabric_topology_util.cloudplatform_switch_portgroup_callback", callback_kwargs=callback_kwargs)
#         return jsonify({'status': 200, 'info': 'Save Node Group successed.'})
#     except Exception as e:
#         LOG.error(traceback.format_exc())
#         session.rollback()
#         return jsonify({'status': 500, 'info': f'Save Node Group failed. {str(e)}'})


@dc_virtual_resource_mold.route("/virtual_resource_host/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def virtual_resource_host_delete():
    session = dc_virtual_resource_db.get_session()
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        host_id = data.get('host_id')
        host = dc_virtual_resource_db.get_virtual_resource_host_by_id(host_id)
        config_dict = get_cloudplatform_link_port_config(host, op="delete")
        
        session.begin()
        host = dc_virtual_resource_db.update_virtual_resource_host(host_id=host.id, status="Deleting", session=session)
        for host_link in host.host_link:
            dc_virtual_resource_db.update_virtual_resource_host_link(link_id=host_link.id, status="Deleting", session=session)
            for port_info in host_link.link_ports:
                dc_virtual_resource_db.update_virtual_resource_host_link_port(port_id=port_info.id, status="Deleting", session=session)
        session.commit() 
        
        ## TODO 调用配置下发
        print(config_dict)
        callback_kwargs = {
            "host_id": host.id
        }
        config_distribution_netconf_by_dc_overlay(config_dict, "uplink_cloud", callback="server.util.fabric_topology_util.cloudplatform_switch_portgroup_callback", callback_kwargs=callback_kwargs)
        return jsonify({'status': 200, 'info': 'The node is being deleted.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        session.rollback()
        return jsonify({'status': 400, 'info': 'Delete node failed.'})
    

@dc_virtual_resource_mold.route("/get_az_by_fabric", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_az_by_fabric():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_id = data.get('fabric_id', None)
        type = data.get("type", None)
        if type == "BareMetal":
            az = dc_virtual_resource_db.get_virtual_resource_pool_az_by_fabric(fabric_id, type_list=[CloudPlatform.BAREMETAL])
        elif type == "Cloud":
            az = dc_virtual_resource_db.get_virtual_resource_pool_az_by_fabric(fabric_id, type_list=[CloudPlatform.OPENSTACK, CloudPlatform.VSPHERE])
        else:
            az = dc_virtual_resource_db.get_virtual_resource_pool_az_by_fabric(fabric_id)
        
        res = []
        for item in az:
            az_info = item.make_dict() 
            az_info.pop("auth_info")
            res.append(az_info)
        
        return jsonify({'status': 200, 'info': 'Get az successed.', 'data': res})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'Get az failed.'})

# Host end

## node group

@dc_virtual_resource_mold.route("/resource_interconnection/list_fabric", methods=["POST"])
@admin_permission.require(http_exception=403)
def list_fabric():
    try:
        
        session = dc_virtual_resource_db.get_session()
        query = session.query(inventory.Fabric, DCFabricTopology, 
                              func.count(ResourcePoolVlanDomain.id).label("vlan_domain_count"),
                              func.max(ResourcePoolVlanDomain.modified_time).label("latest_modified_time"))\
                        .outerjoin(DCFabricTopology, inventory.Fabric.id == DCFabricTopology.fabric_id)\
                        .outerjoin(ResourcePoolVlanDomain, inventory.Fabric.id == ResourcePoolVlanDomain.fabric_id)\
                        .filter(DCFabricTopology.status == "Deployed")\
                        .group_by(inventory.Fabric.id, DCFabricTopology.id)
        
        res = []
        for fabric, topo, has_vlan_domain, modified_time  in query:
            res.append({
                "id": fabric.id,
                "fabric_topo_id": topo.id,
                "fabric_name": fabric.fabric_name,
                "has_vlan_domain": True if has_vlan_domain else False,
                "modified_time": modified_time.strftime('%Y-%m-%d %H:%M:%S') if has_vlan_domain else "--"
            })
            
        response = {
            "data": res,
            "status": 200
        }
        return jsonify(response)

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List fabric failed.'})
    
@dc_virtual_resource_mold.route("/vlan_domain_group/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def list_vlan_domain_group():
    try:
        
        session = dc_virtual_resource_db.get_session()
        query = session.query(inventory.Fabric, func.count(ResourcePoolVlanDomain.id).label("vlan_domain_count"))\
                        .outerjoin(ResourcePoolVlanDomain, inventory.Fabric.id == ResourcePoolVlanDomain.fabric_id)\
                        .group_by(inventory.Fabric.id)
        
        res = []
        ## 兼容Q1版本中没有vd group的情况 自动创建
        for fabric, vlan_domain_count in query:
            if vlan_domain_count:
                group = dc_virtual_resource_db.get_vlan_domain_group_by_fabric_id(fabric.id)
                if not group:
                    dc_virtual_resource_db.update_vlan_domain_group(group_name=f"VD_{fabric.fabric_name}", fabric_id=fabric.id)
        data = request.get_json()
        sort_fields = data.get("sortFields", [])
        pre_query = session.query(VlanDomainGroup).join(inventory.Fabric,
                                                        VlanDomainGroup.fabric_id == inventory.Fabric.id)
        new_sort_fields = []
        for field in sort_fields:
            name = field.get("field")
            order = field.get("order")
            if name == "fabric_name":
                col = inventory.Fabric.fabric_name
            else:
                new_sort_fields.append(field)
                continue
            if order == "asc":
                pre_query = pre_query.order_by(asc(col))
            elif order == "desc":
                pre_query = pre_query.order_by(desc(col))
        if data:
            data["sortFields"] = new_sort_fields
        page_num, page_size, total_count, query_info = utils.query_helper(VlanDomainGroup,pre_query=pre_query, data=data)
            
        for info in query_info:
            data = info.make_dict()
            data["description"] = info.description
            data["fabric_name"] = info.fabric.fabric_name
            topo = session.query(DCFabricTopology).filter(DCFabricTopology.fabric_id == info.fabric.id).first()
            data["fabric_topo_id"] = topo.id
            res.append(data)

        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List vlan domain group failed.'})
    

@dc_virtual_resource_mold.route("/vlan_domain_group/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def delete_vlan_domain_group():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        
        group_id = data.get('vdGroupId', None)
        
        group = dc_virtual_resource_db.get_vlan_domain_group_by_id(group_id)
        topo = dc_fabric_db.get_fabric_topo_by_fabric_id(group.fabric_id)
        
        vd_pool = resource_pool_vlandomain_db.list_vlan_domain_pool(topo.fabric_id)
        
        for vd_info in vd_pool.values():
            if vd_info["bridge_domain"].get("is_in_use", False):
                return jsonify({'info': 'Vlan domain is in use and cannot delete.', "status": 400})
            if vd_info["bridge_domain"].get("is_in_use", False):
                return jsonify({'info': 'Vlan domain is in use and cannot delete.', "status": 400})
           
        session = dc_virtual_resource_db.get_session()
        used_link = session.query(DCVirtualResourceHostLink).filter(DCVirtualResourceHostLink.vlan_domain_id.in_(vd_pool.keys())).all()
        if len(used_link) > 0:
            return jsonify({'info': 'Vlan domain is in use and cannot delete.', "status": 400})
        
        used_pg = session.query(SwitchPortgroup).filter(SwitchPortgroup.vlan_domain_id.in_(vd_pool.keys())).all()
        if len(used_pg) > 0:
            return jsonify({'info': 'Vlan domain is in use and cannot delete.', "status": 400})
        
        with session.begin():
            session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id.in_(vd_pool.keys())).delete()
            session.query(VlanDomainGroup).filter(VlanDomainGroup.id == group_id).delete()

        return jsonify({'info': 'The VLAN Domain group is deleted successfully.', "status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        session.rollback()
        return jsonify({'status': 500, 'info': 'The VLAN Domain group is deleted failed.'})


@dc_virtual_resource_mold.route("/node_template/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def node_template_save():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        
        template_id = data.get('id', None)
        template_name = data.get('template_name')
        total_ports = data.get('total_ports')
        template_info = data.get('template_info', {})
        
        dc_virtual_resource_db.update_node_template(template_name, total_ports, template_info, template_id)
            
        return jsonify({"info": "Save Node template successed.","status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': f'Save Node template failed. {str(e)}'})

@dc_virtual_resource_mold.route("/node_template/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def node_template_list():
    try:
        
        query_template = dc_virtual_resource_db.get_all_node_template()
        res = []
        for template, is_referenced  in query_template:
            res.append({
                "id": template.id,
                "template_name": template.template_name,
                "total_ports": template.total_ports,
                "template_info": template.template_info,
                "is_used": is_referenced
            })
            
        response = {
            "data": res,
            "status": 200
        }
        return jsonify(response)

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List Node template failed.'})

@dc_virtual_resource_mold.route("/node_template/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def node_template_delete():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        template_id = data.get('id', None)
        if template_id:
            dc_virtual_resource_db.del_template_by_id(template_id)
        return jsonify({"info": "Save Node template successed.","status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'Delete Node template failed.'})


@dc_virtual_resource_mold.route("/node_group/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def node_group_save():
    session = dc_virtual_resource_db.get_session()
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        node_group_id = data.get('id', None)
        nodegroup_name = data.get('nodegroup_name')
        description = data.get('description', '')
        template_id = data.get('node_template_id')
        az_id = data.get('az_id')
        fabric_id = data.get('fabric_id')
        node_list = data.get('node_host', [])
        switch_pg_list = data.get('switch_portgroup', [])
        
        az = dc_virtual_resource_db.get_virtual_resource_pool_az_by_id(az_id)
        if not az:
            return jsonify({'status': 500, 'info': f'Save Node Group failed. az: {az_id} not found'})
        
        if node_group_id:
            node_group = dc_virtual_resource_db.get_node_group_by_id(node_group_id)
            if node_group.status == "Deploying":
                return jsonify({'status': 500, 'info': f'Edit Node Group failed. Deploy task is running, please wait!'})
        
        template = dc_virtual_resource_db.get_node_template_by_id(template_id)
        nic_pg_name_list = template.template_info.keys()

        session.begin()
        # 创建node_group
        node_group = dc_virtual_resource_db.update_node_group(
            az_id=az_id, fabric_id=fabric_id, node_template_id=template_id, nodegroup_name=nodegroup_name, 
            description=description, group_id=node_group_id, session=session
        )
        
        usage_state = False
        for host in node_group.node_host:
            for nic_pg in host.node_nic_portgroup:
                if nic_pg.network_id:
                    usage_state = True
                    break
                 
        ## 如果已经被vl2使用 只能修改description 直接返回   
        if usage_state:
            session.commit() 
            return jsonify({'status': 200, 'info': 'Save Node Group successed.'})

        
        nic_pg_dict = {}
        for node in node_list:
            node_name = node.get('host_name', "")
            node_id= node.get('id', None)
            ipaddress = node.get('ip_addr', "")
            username = node.get('username', "")
            password = node.get('password', "")
            
            # 创建节点
            node_host = dc_virtual_resource_db.update_node_host(node_name, node_group.id,ipaddress, username, password, node_id, session)
            
            for pg_name in nic_pg_name_list:
                # 创建nic pg 如果已经存在同名且node_host.id一样的则不会添加拿到id即可 
                nic_pg = dc_virtual_resource_db.update_node_nic_portgroup(portgroup_name=pg_name, node_host_id=node_host.id, session=session)
                nic_pg_dict.setdefault(pg_name, []).append(nic_pg.id)
             
        existing_switch_pg = []
        if node_group_id:
            existing_switch_pg = dc_virtual_resource_db.get_switch_portgroup_by_nodegroup(node_group_id)
        
        edit_switch_pg = []
        del_port = []
        for switch_pg in switch_pg_list:
            switch_pg_id = switch_pg.get('id', None)
            vlan_domain_id = switch_pg.get('vlan_domain_id', None)
            port_group_name = switch_pg.get('portgroup_name', "")
            nic_pg_name = switch_pg.get('nic_portgroup_name', "")
            connect_mode = switch_pg.get('connect_mode', "")
            link_type = switch_pg.get('link_type', "")
            link_count = switch_pg.get('link_count', 0)
            portgroup_info = switch_pg.get('portgroup_info', [])
            
            if not vlan_domain_id:
                raise Exception(f"vlan domain not found")

            # 更新switch pg
            switch_portgroup = dc_virtual_resource_db.update_switch_portgroup(
                portgroup_name=port_group_name, node_group_id=node_group.id, vlan_domain_id=vlan_domain_id, connect_mode=connect_mode, 
                link_type=link_type, link_count=link_count, portgroup_id=switch_pg_id, session=session
            )
            
            edit_switch_pg.append(switch_portgroup.id)
            
            # 更新nic pg 与 switch pg的关系
            if nic_pg_name not in nic_pg_dict:
                raise Exception(f"nic portgroup name not found")
            for nic_pg_id in nic_pg_dict[nic_pg_name]:
                dc_virtual_resource_db.update_node_nic_portgroup(switch_portgroup_id=switch_portgroup.id, nic_id=nic_pg_id, session=session)
                    
            existing_port = []
            if switch_pg_id:
                existing_port = dc_virtual_resource_db.get_switch_portgroup_info_by_pg(switch_pg_id)
            
            lag_id_list = []
            if link_type == "MLAG Leaf"  or link_count > 1:
                ## 获取lag id 
                ## 对于mlag 两台设备的聚合口都要同步 所以找到两台设备获取lag id
                nodes = dc_fabric_db.get_fabric_topology_node_by_vd(vlan_domain_id)
                logic_device_id_list = [node.id for node in nodes]
                lag_id_list = get_switch_lag_id(logic_device_id_list, len(nic_pg_dict[nic_pg_name]))
                print(lag_id_list)
            
            edit_port = []
            for port in portgroup_info:
                port_id = port.get('id', None)                 
                logic_device_id = port.get('logic_device_id', None)
                switch_sn = port.get('switch_sn', "")
                port_List = port.get('port_list', []) 
                port_iter = iter(port_List)
                disable_port_list = port.get('disable_port_list', []) 
                nic_port_list = port.get('nic_port_list', []) 
                sw_port_info = {}
                
                if not switch_sn or not logic_device_id:
                    raise Exception(f"logic device or switch_sn not found")
                
                self_used_ports = []
                if port_id:
                    port_info = dc_virtual_resource_db.get_switch_portgroup_info_by_id(port_id)
                    sw_port_info = copy.deepcopy(port_info.port_info["sw_port_info"])  ## 需要深拷贝 否则可能数据更新会失效
                    self_used_ports = [port for item in sw_port_info.values() for port in item['port_list']]
                
                _, _, used_ports = get_switch_port_info(logic_device_id)
                filtered_list = [item for item in used_ports if item not in self_used_ports]
                invalid_port = list(set(port_List) & set(filtered_list))
                if invalid_port:
                    raise Exception(f"{invalid_port} already in use")
                    
                for index, nic_pg_id in enumerate(nic_pg_dict[nic_pg_name]):
                    port_info_key =  str(nic_pg_id)
                    nic_pg_port_list = [next(port_iter) for _ in range(link_count)]
                    if port_info_key in sw_port_info:
                        sw_port_info[port_info_key]["port_list"] = nic_pg_port_list
                        if index < len(lag_id_list) and sw_port_info[port_info_key]["lag_id"] == 0:
                            sw_port_info[port_info_key]["lag_id"] = lag_id_list[index]
                    else:
                        
                        sw_port_info[port_info_key] = {
                            "port_list": nic_pg_port_list,
                            "lag_id": lag_id_list[index] if lag_id_list else 0
                        }
                
                port_info = {
                    "disable_port_list": disable_port_list,
                    "nic_port_list": nic_port_list,
                    "sw_port_info": sw_port_info
                }
                print(port_info) 
                

                
                switch_pg_info = dc_virtual_resource_db.update_switch_portgroup_info(info_id=port_id, portgroup_id=switch_portgroup.id, logic_device_id=logic_device_id, 
                                                                    switch_sn=switch_sn, port_info=port_info, session=session)
                
                edit_port.append(switch_pg_info.id)
                
            # 处理删除的port 
            # 需要删除的port先更新状态 交给配置下发部分 异步删除配置 配置删除后再删除数据
            for port in existing_port:
                if port.id not in edit_port:
                    del_port.append(port.id)
               
        # 处理删除的link 
        del_spg = []
        # 需要删除的pg先更新状态 交给配置下发部分 异步删除配置 配置删除后再删除数据
        for spg in existing_switch_pg:
            if spg.id not in edit_switch_pg:
                del_spg.append(spg.id)
                query_spg = dc_virtual_resource_db.get_switch_portgroup_by_id(spg.id)
                for spg_info in query_spg.switch_portgroup_info:
                    del_port.append(spg_info.id)

        print(del_spg, del_port)
        
        config_dict = get_baremetal_switch_portgroup_config(node_group)
        
        # 更新状态
        dc_virtual_resource_db.update_node_group(group_id=node_group.id, status="Deploying", session=session)
        for switch_pg in node_group.switch_portgroup: 
            if switch_pg.id not in del_spg:
                dc_virtual_resource_db.update_switch_portgroup(portgroup_id=switch_pg.id, status="Deploying", session=session)
            else:
                dc_virtual_resource_db.update_switch_portgroup(portgroup_id=switch_pg.id, status="Deleting", session=session)
            for pg_info in switch_pg.switch_portgroup_info:
                if pg_info.id not in del_port:
                    dc_virtual_resource_db.update_switch_portgroup_info(info_id=pg_info.id, status="Deploying", session=session) 
                else:
                    # 标记删除的port 等待异步删除
                    dc_virtual_resource_db.update_switch_portgroup_info(info_id=pg_info.id, status="Deleting", session=session) 
        
        session.commit() 
                                
        ## TODO 调用配置下发
        print(config_dict, del_spg, del_port)
        callback_kwargs = {
            "node_group_id": node_group.id
        }
        config_distribution_netconf_by_dc_overlay(config_dict, "uplink_bm", 
                                                  callback="server.util.fabric_topology_util.baremetal_switch_portgroup_callback", callback_kwargs=callback_kwargs)
        return jsonify({'status': 200, 'info': 'Save Node Group successed.'})

    except Exception as e:
        LOG.error(traceback.format_exc())
        session.rollback()
        return jsonify({'status': 500, 'info': f'Save Node Group failed. {str(e)}'})


# @dc_virtual_resource_mold.route("/node_group/deploy_test", methods=["POST"])
# @admin_permission.require(http_exception=403)
# def node_group_deploy_test():
#     session = dc_virtual_resource_db.get_session()
#     try:
#         data = request.get_json()
#         if not data:
#             return jsonify({'info': "Invalid request data", 'status': 400})

#         node_group_id = data.get('id', None)
#         node_group = dc_virtual_resource_db.get_node_group_by_id(node_group_id)
                                
#         config_dict = get_baremetal_switch_portgroup_config(node_group)
        
#         # dc_virtual_resource_db.update_node_group(group_id=node_group.id, status="Deploying")
#         # for switch_pg in node_group.switch_portgroup: 
#         #     dc_virtual_resource_db.update_switch_portgroup(portgroup_id=switch_pg.id, status="Deploying")
#         #     for pg_info in switch_pg.switch_portgroup_info:
#         #         dc_virtual_resource_db.update_switch_portgroup_info(info_id=pg_info.id, status="Deploying") 
        
#         ## 调用配置下发
#         print(config_dict)
#         callback_kwargs = {
#             "node_group_id": node_group.id
#         }
#         config_distribution_netconf_by_dc_overlay(config_dict, "uplink_bm", callback="server.util.fabric_topology_util.baremetal_switch_portgroup_callback", callback_kwargs=callback_kwargs)
#         return jsonify({'status': 200, 'info': 'Save Node Group successed.'})

#     except Exception as e:
#         LOG.error(traceback.format_exc())
#         session.rollback()
#         return jsonify({'status': 500, 'info': f'Save Node Group failed. {str(e)}'})


@dc_virtual_resource_mold.route("/node_group/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def node_group_list():
    try:
        data = request.get_json()
        sort_fields = data.get("sortFields", [])
        if sort_fields:
            sort_field = sort_fields[0].get("field")
            order = sort_fields[0].get("order", "asc")
        else:
            sort_field = ""
        
        db_fields = {
            "fabric_name": inventory.Fabric.fabric_name,
            "az_name":  DCVirtualResourcePoolAZ.az_name
        }
        db_session = inven_db.get_session()
        pre_query = db_session.query(
            NodeGroup,
            inventory.Fabric.fabric_name,
            DCVirtualResourcePoolAZ.az_name
        ).join(
            inventory.Fabric, NodeGroup.fabric_id == inventory.Fabric.id
        ).join(
            DCVirtualResourcePoolAZ, NodeGroup.az_id == DCVirtualResourcePoolAZ.id
        )
        if sort_field and sort_field in db_fields:

            if order == "asc":
                pre_query = pre_query.order_by(db_fields[sort_field].asc())
            else:
                pre_query = pre_query.order_by(db_fields[sort_field].desc())
            page_num, page_size, total_count, query_info = utils.query_helper(NodeGroup, skip_sort=True, pre_query=pre_query)
        else:
            page_num, page_size, total_count, query_info = utils.query_helper(NodeGroup, pre_query=pre_query)
               
        res = []
        for info, fabric_name, az_name in query_info:
            data = info.make_dict()
            data["az_name"] = az_name
            data["fabric_name"] = fabric_name
            data["node_count"] = len(info.node_host)
            data["node_host"] = []
            data["usage_state"] = False
            for host in info.node_host:
                host_info = host.make_dict()
                pg_list = {} 
                for nic_pg in host.node_nic_portgroup:
                    pg_list[nic_pg.portgroup_name] = nic_pg.id
                    if nic_pg.network_id:
                        data["usage_state"] = True
                host_info["pg_list"] = pg_list
                data["node_host"].append(host_info)
            
            data["switch_portgroup"] = []
            for switch_pg in info.switch_portgroup:
                switch_pg_info = switch_pg.make_dict()
                if switch_pg.nic_portgroup:
                    switch_pg_info['nic_portgroup_name'] = switch_pg.nic_portgroup[0].portgroup_name
                
                if switch_pg.link_type == "MLAG Leaf":
                    if len(switch_pg.switch_portgroup_info) == 1:
                        switch_pg_info['access_mlag_mode'] = "Single-Homed"
                        switch_pg_info['peer_leaf'] = switch_pg.switch_portgroup_info[0].logic_device.node_info.get("hostname", "")
                    else:
                        switch_pg_info['access_mlag_mode'] = "Dual-Homed"
                  
                switch_pg_info["portgroup_info"] = []     
                for pg_info in switch_pg.switch_portgroup_info:
                    pg_info = pg_info.make_dict()
                    pg_info["port_info"]["port_list"] = []
                    for port_info in pg_info["port_info"].get("sw_port_info", {}).values():
                        port_list= port_info.get("port_list", [])
                        pg_info["port_info"]["port_list"].extend(port_list)
                    switch_pg_info["portgroup_info"].append(pg_info)
                data["switch_portgroup"].append(switch_pg_info)
                
            res.append(data)

        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List Node Group failed.'})
    

@dc_virtual_resource_mold.route("/node_group/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def node_group_delete():
    session = dc_virtual_resource_db.get_session()
    try: 
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})   
        node_group_id = data.get('id', None)
        
        # node group删除，需要先异步执行配置清理，然后进行数据库删除
        node_group = dc_virtual_resource_db.get_node_group_by_id(node_group_id)
        config_dict = get_baremetal_switch_portgroup_config(node_group, op="delete")
        session.begin()
        dc_virtual_resource_db.update_node_group(group_id=node_group_id, status="Deleting", session=session)
        for switch_pg in node_group.switch_portgroup:
            dc_virtual_resource_db.update_switch_portgroup(portgroup_id=switch_pg.id, status="Deleting", session=session)
            for pg_info in switch_pg.switch_portgroup_info:
                dc_virtual_resource_db.update_switch_portgroup_info(info_id=pg_info.id, status="Deleting", session=session)
                
        session.commit() 
                
        ## 调用配置下发
        print(config_dict)
        callback_kwargs = {
            "node_group_id": node_group.id
        }
        config_distribution_netconf_by_dc_overlay(config_dict, "uplink_bm", callback="server.util.fabric_topology_util.baremetal_switch_portgroup_callback", callback_kwargs=callback_kwargs)
        
        return jsonify({'info': 'Delete Node Group task running.', "status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        session.rollback()
        return jsonify({'status': 500, 'info': 'Delete Node Group failed.'})
   

@dc_virtual_resource_mold.route("/node_addition/list_log", methods=["POST"])
@admin_permission.require(http_exception=403)
def node_addition_list_log(): 
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        node_id = data.get('id', None)
        node_type = data.get('type', None) 
        
        session = inven_db.get_session()
        
        if node_type == "BareMetal":
            pg_configs = session.query(SwitchPortgroupConfig).filter(SwitchPortgroupConfig.related_id == node_id, 
                                                                     SwitchPortgroupConfig.related_type == "BareMetal")
            config_type = "uplink_bm"
        elif node_type == "Cloud":
            pg_configs = session.query(SwitchPortgroupConfig).filter(SwitchPortgroupConfig.related_id == node_id, 
                                                                     SwitchPortgroupConfig.related_type == "CloudPlatform")
            config_type = "uplink_cloud"
        else:
            return jsonify({'info': "unknow node type", 'status': 400})
               
        page_num, page_size, total_count, query_info = utils.query_helper(SwitchPortgroupConfig, pre_query=pg_configs)
        
        ## TODO 获取日志
        
        subquery = session.query(
            inventory.ConfigDistributionTaskForDC,
            func.row_number().over(
                partition_by=[inventory.ConfigDistributionTaskForDC.sn, 
                              inventory.ConfigDistributionTaskForDC.type,
                              inventory.ConfigDistributionTaskForDC.fabric_id],
                order_by=inventory.ConfigDistributionTaskForDC.create_time.desc()
            ).label('row_num')
        ) \
        .filter(inventory.ConfigDistributionTaskForDC.fabric_id == node_id,
                inventory.ConfigDistributionTaskForDC.type == config_type).subquery()
        
        tasks = session.query(subquery).filter(subquery.c.row_num == 1).all()
        
        logs_dict = {}
        for task in tasks:
            logs_dict[task.sn] = task.task_traceback_info
        
        res = []
        for info in query_info:
            switch = session.query(inventory.Switch).filter(inventory.Switch.sn == info.switch_sn).first()
            data = {
                "sysname": info.logic_device.node_info.get("hostname", info.logic_device.logic_name),
                "mgmt_ip": switch.link_ip_addr if switch else "" ,
                "type": info.logic_device.type,
                "status": info.status,
                "log": logs_dict.get(info.switch_sn, "")
            }
            
            res.append(data)

        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List Node Group failed.'})
 
@dc_virtual_resource_mold.route("/node_group/list_link_info", methods=["POST"])
@admin_permission.require(http_exception=403)
def list_link_info():
    try: 
        
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})   
        node_group_id = data.get('id', None)
        
        session = dc_virtual_resource_db.get_session()
        node_group = dc_virtual_resource_db.get_node_group_by_id(node_group_id)
               
        res = []
        for switch_pg in node_group.switch_portgroup:   
            vlan_domain_pool = session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id == switch_pg.vlan_domain_id).first() 
            for pg_info in switch_pg.switch_portgroup_info:
                switch_port_info = pg_info.port_info.get("sw_port_info", {})
                
                for pg_id, port_info in switch_port_info.items():
                    pg = dc_virtual_resource_db.get_node_nic_portgroup_by_id(pg_id)
                    info = {
                        "pg_info_id": pg_info.id,
                        "node_group": node_group.nodegroup_name,
                        "node_name": pg.node_host.host_name,
                        "nic_port_group": pg.portgroup_name,
                        "port_count": switch_pg.link_count,
                        "port_name": ",".join(pg_info.port_info.get("nic_port_list", [])),
                        "switch_port_group": switch_pg.portgroup_name,
                        "vlan_domain": vlan_domain_pool.name,
                        "link_type": switch_pg.link_type,
                        "switch_sn": pg_info.switch_sn,
                        "switch_port": ",".join(port_info["port_list"]),
                        "port_mode": switch_pg.connect_mode,
                        "status": pg_info.status
                        
                    }
                    res.append(info)

        return jsonify({"data": res, "status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List Node Group failed.'})
    

@dc_virtual_resource_mold.route("/node_group/list_device_port_info", methods=["POST"])
@admin_permission.require(http_exception=403)
def list_device_port_info():
    try: 
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        device_id_list = data.get('logicDeviceIdList', [])     
        
        res =[]
        for device_id in device_id_list:
            all_port, platform_mode, used_ports = get_switch_port_info(device_id)
            info={
                "all_ports": all_port,
                "platform_mode": platform_mode,
                "used_ports": used_ports,
            } 
            res.append(info)

        return jsonify({"data": res, 'info': 'List port info successed.', "status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List port info failed.'})


## 裸金属接入
@dc_virtual_resource_mold.route("/network_access/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def network_access_save():
    session = dc_virtual_resource_db.get_session()
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        vl2_id = data.get('id', None)
        vl2_name = data.get('vl2Name')
        az_id = data.get('azId')
        fabric_id = data.get('fabricId')
        vlan_id = int(data.get("vlanId", "")) if data.get("vlanId", "") else 0
        
        az = dc_virtual_resource_db.get_virtual_resource_pool_az_by_id(az_id)
        if not az:
            return jsonify({'status': 500, 'info': f'Save VL2 failed. az: {az_id} not found'})
        
        if not vl2_id:
            all_vlans = []
            for vlan_range in json.loads(az.global_vlan.replace("'", '"')):
                all_vlans.extend(range(vlan_range['startVLAN'], vlan_range['endVLAN'] + 1))
            
            all_vl2 = dc_virtual_resource_db.get_virtual_resource_networks_by_az(az_id)
            used_vlan_id = [vl2.vlan_id for vl2 in all_vl2]
            available_vlans = [vlan for vlan in all_vlans if vlan not in used_vlan_id]
            
            if vlan_id in used_vlan_id:
                return jsonify({'status': 500, 'info': f'Save VL2 failed. vlan_id: {vlan_id} already used'})
            
            if vlan_id == 0:
                if not available_vlans:
                    return jsonify({'status': 500, 'info': f'Save VL2 failed. no available global vlan'})
                vlan_id = available_vlans[0]
            
            if vlan_id not in available_vlans:
                return jsonify({'status': 500, 'info': f'Save VL2 failed. vlan_id: {vlan_id} not in global vlan'})
        
        vpc_name = "VPC-" + vl2_name
        vpc_id = None
        vn_id =None
        if vl2_id:
            vl2 = dc_virtual_resource_db.get_virtual_resource_network_by_id(vl2_id)
            vpc_id = vl2.vpc_id
            vn_id = vl2.virtual_network_id
            
        session.begin()
        vpc = dc_virtual_resource_db.update_virtual_resource_vpc(id=vpc_id, az_id=az_id, fabric_id=fabric_id, vpc_name=vpc_name, vpc_id=vpc_name)
        if vn_id:
            dc_fabric_db.update_virtual_network(name=vl2_name, vn_id=vn_id)
        dc_virtual_resource_db.update_baremetal_vl2(network_id=vl2_id, name=vl2_name, fabric_id=fabric_id, az_id=az_id, vpc_id=vpc.id, vlan_id=vlan_id)
        session.commit()
        return jsonify({'status': 200, 'info': 'Save VL2 successed.'})

    except Exception as e:
        LOG.error(traceback.format_exc())
        session.rollback()
        return jsonify({'status': 500, 'info': f'Save VL2 failed. {str(e)}'})


@dc_virtual_resource_mold.route("/network_access/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def network_access_list():
    try: 
            
        session = dc_virtual_resource_db.get_session()

        all_bm_az = session.query(DCVirtualResourcePoolAZ).filter(
            DCVirtualResourcePoolAZ.resource_type == CloudPlatform.BAREMETAL
        ).all()
        all_bm_az_id = [az.id for az in all_bm_az]

        bm_network = session.query(DCVirtualResourceNetwork) \
            .join(DCVirtualResourceNetwork.az) \
            .join(DCVirtualResourceNetwork.fabric) \
            .join(DCVirtualResourceNetwork.vpc) \
            .filter(DCVirtualResourceNetwork.az_id.in_(all_bm_az_id))

        # 解析排序字段
        data = request.get_json()
        sort_fields = data.get("sortFields", [])

        new_sort_fields = []
        for field in sort_fields:
            name = field.get("field")
            order = field.get("order")
            if name == "az_name":
                col = DCVirtualResourcePoolAZ.az_name
            elif name == "fabric_name":
                col = inventory.Fabric.fabric_name
            elif name == "vpc_name":
                col = DCVirtualResourceVPC.vpc_name
            else:
                new_sort_fields.append(field)  # 留给 query_helper() 处理

                continue

            if order == "asc":
                bm_network = bm_network.order_by(asc(col))
            elif order == "desc":
                bm_network = bm_network.order_by(desc(col))

        # 清理掉已手动处理的字段
        data["sortFields"] = new_sort_fields

        page_num, page_size, total_count, query_info = utils.query_helper(
            DCVirtualResourceNetwork,
            pre_query=bm_network,
            data=data
        )

        res = []
        for info in query_info:
            data = info.make_dict()
            data["az_name"] = info.az.az_name if info.az else ""
            data["fabric_name"] = info.fabric.fabric_name if info.fabric else ""
            data["vpc_name"] = info.vpc.vpc_name if info.vpc else ""
            data["user"] = "admin"
            res.append(data)

        return jsonify({
            "data": res,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        })

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List VL2 failed.'})


@dc_virtual_resource_mold.route("/network_access/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def network_access_delete():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        vl2_id = data.get('id', None)
        if vl2_id:
            # 检查是否在组网中 如果在组网中不能删除
            vl2 = dc_virtual_resource_db.get_virtual_resource_network_by_id(vl2_id)
            if vl2 and vl2.virtual_network_id:
               return jsonify({'status': 500, 'info': 'Delete VL2 failed: vl2 already used'}) 
            dc_virtual_resource_db.del_virtual_resource_network_by_id(vl2_id)
            dc_virtual_resource_db.del_virtual_resource_vpc_by_id(vl2.vpc_id)
        return jsonify({"info": "Delete VL2 successed.","status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'Delete VL2 failed.'})
    

@dc_virtual_resource_mold.route("/network_access/node_group_list", methods=["POST"])
@admin_permission.require(http_exception=403)
def network_access_node_group_list():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        azId = data.get('azId')
        
        node_group_list = dc_virtual_resource_db.get_node_group_by_az_id(azId)
        
        res = []
        for node_group in node_group_list:
            if node_group.status == "Deployed":
                res.append({
                    "id": node_group.id,
                    "nodegroup_name": node_group.nodegroup_name
                })

        response = {
            "data": res,
            "status": 200
        }
        return jsonify(response)

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List node group failed.'})
     
  
@dc_virtual_resource_mold.route("/network_access/available_device_list", methods=["POST"])
@admin_permission.require(http_exception=403)
def network_access_available_device_list():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        node_group_id = data.get('nodeGroupId')
        
        session = dc_virtual_resource_db.get_session()
        
        all_switch_pg = session.query(SwitchPortgroup).filter(SwitchPortgroup.node_group_id == node_group_id).all()
        switch_pg_id_list = [switch_pg.id for switch_pg in all_switch_pg]
        
        all_available_nic_pg = session.query(NodeNicPortgroup).filter(NodeNicPortgroup.switch_portgroup_id.in_(switch_pg_id_list), NodeNicPortgroup.network_id == None).all()
        all_host_id_list = [nic_pg.node_host_id for nic_pg in all_available_nic_pg]
        
        all_host = session.query(NodeHost).filter(NodeHost.id.in_(all_host_id_list)).all()
        
        res = []

        for host in all_host:
            info = {
                "host_name" : host.host_name,
                "pg_info": []
            }
            for nic_pg in host.node_nic_portgroup:
                if nic_pg.switch_portgroup and not nic_pg.network_id:
                    pg_info = {
                        "pg_id": nic_pg.id,
                        "pg_name": nic_pg.portgroup_name,
                        "switch_pg_name": nic_pg.switch_portgroup.portgroup_name,
                    }
                    info["pg_info"].append(pg_info)
            res.append(info)

        response = {
            "data": res,
            "status": 200
        }
        return jsonify(response)

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List available devide failed.'})
  
    
@dc_virtual_resource_mold.route("/network_access/associate", methods=["POST"])
@admin_permission.require(http_exception=403)
def network_access_associate():
    session = dc_virtual_resource_db.get_session()
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        vl2_id = data.get('vl2Id')
        nic_pg_id_list = data.get("nicPortgroupIdList", [])

        vl2 = dc_virtual_resource_db.get_virtual_resource_network_by_id(vl2_id)
        if not vl2:
            return jsonify({'status': 500, 'info': f'Associate VL2 failed. vl2: {vl2_id} not found'})
        # 需要对相同设备不同nic group 判重
        portgroups_to_update = session.query(NodeNicPortgroup).filter(NodeNicPortgroup.id.in_(nic_pg_id_list))
        node_host_dict = {}
        vd_id_list = []

        for pg in portgroups_to_update:
            if pg.node_host:
                node_host_dict[pg.node_host_id] = pg.node_host.host_name
            if pg.switch_portgroup: 
                vd_id_list.append(pg.switch_portgroup.vlan_domain_id)
                
        for vd_id in vd_id_list:
            res, msg = resource_pool_vlandomain_db.check_bd_vlan_value_in_range(vl2.vlan_id, vd_id)
            if not res:
                return jsonify({'status': 500, 'info': f'Associate VL2 failed. {msg}'})
        
        for node_host_id, node_host_name in node_host_dict.items():
            # 查找该 node_host_id 下是否已有相同的 network_id
            conflict_exists = session.query(NodeNicPortgroup).filter(
                and_(
                    NodeNicPortgroup.node_host_id == node_host_id,
                    NodeNicPortgroup.network_id == vl2_id,
                    NodeNicPortgroup.id.notin_(nic_pg_id_list)  # 排除当前要更新的记录
                )
            ).exists()
            
            if session.query(conflict_exists).scalar():
                return jsonify({'status': 500, 'info': f'Associate VL2 failed. node_host:{node_host_name} already exists same network:{vl2.network_name}'})
        
        for nic_pg_id in nic_pg_id_list:
            if vl2.virtual_network:
                dc_virtual_resource_db.update_node_nic_portgroup(nic_id=nic_pg_id, network_id=vl2_id, status="Connect Successful")
            else:
                dc_virtual_resource_db.update_node_nic_portgroup(nic_id=nic_pg_id, network_id=vl2_id)
        return jsonify({"info": "Associate VL2 successed.","status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'Associate VL2 failed.'})
    

@dc_virtual_resource_mold.route("/network_access/device_list", methods=["POST"])
@admin_permission.require(http_exception=403)
def network_access_device_list():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        vl2_id = data.get('vl2Id')
        
        session = dc_virtual_resource_db.get_session()
        
        all_nic_pg = session.query(NodeNicPortgroup).filter(NodeNicPortgroup.network_id == vl2_id)
        node_group_id_list = [nic_pg.node_host.node_group.id for nic_pg in all_nic_pg]
        
        all_node_group = session.query(NodeGroup).filter(NodeGroup.id.in_(node_group_id_list)).all()

        res = []
        for node_group in all_node_group:
            info = {
                "nodegroup_name" : node_group.nodegroup_name,
                "pg_info": []
            }
            for host in node_group.node_host:
                for nic_pg in host.node_nic_portgroup:
                    if nic_pg.network_id == vl2_id:
                        pg_info = {
                            "pg_id": nic_pg.id, 
                            "pg_name": nic_pg.portgroup_name,
                            "host_name": host.host_name,
                            "switch_pg_name": nic_pg.switch_portgroup.portgroup_name,
                            "connect_mode": nic_pg.switch_portgroup.connect_mode,
                            "link_type": nic_pg.switch_portgroup.link_type,
                            "status": nic_pg.status
                        }
                        pg_info["connect_detail"] = []
                        for port_group_info in nic_pg.switch_portgroup.switch_portgroup_info:
                            nic_port_list = port_group_info.port_info.get("nic_port_list", [])
                            switch_port_list = port_group_info.port_info.get("sw_port_info", {}).get(str(nic_pg.id), {}).get("port_list", [])
                            for nic_port, switch_port in zip(nic_port_list, switch_port_list):
                                pg_info["connect_detail"].append({
                                    "nic_port": nic_port,
                                    "switch_info":  port_group_info.logic_device.logic_name + " " + switch_port
                                })
                        
                        info["pg_info"].append(pg_info)
            res.append(info)
            
        response = {
            "data": res,
            "status": 200
        }
        return jsonify(response)

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'List available devide failed.'})
  
  
@dc_virtual_resource_mold.route("/network_access/dissociate", methods=["POST"])
@admin_permission.require(http_exception=403)
def network_access_dissociate():
    try:
        session = dc_virtual_resource_db.get_session()
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        vl2_id = data.get('vl2Id')
        nodegroup_id = data.get('nodegroupId', None)
        nic_pg_id = data.get("nicPortgroupId", None)
        
        if nodegroup_id:
            nodegroup = dc_virtual_resource_db.get_node_group_by_id(nodegroup_id)
            if not nodegroup:
                return jsonify({'status': 500, 'info': f'Dissociate VL2 failed. nodegroup: {nodegroup_id} not found'})
            for host in nodegroup.node_host:
                for nic_pg in host.node_nic_portgroup:
                    if nic_pg.network_id == vl2_id:
                        session.query(NodeNicPortgroup).filter(NodeNicPortgroup.id == nic_pg.id).update({"network_id": None})
                        
        elif nic_pg_id:
            nic_pg = dc_virtual_resource_db.get_node_nic_portgroup_by_id(nic_pg_id)
            if not nic_pg:
                return jsonify({'status': 500, 'info': f'Dissociate VL2 failed. nic portgroup: {nic_pg_id} not found'})
            if nic_pg.network_id == vl2_id:
                session.query(NodeNicPortgroup).filter(NodeNicPortgroup.id == nic_pg.id).update({"network_id": None})

        return jsonify({"info": "Dissociate VL2 successed.","status": 200})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'Dissociate VL2 failed.'})  

##
