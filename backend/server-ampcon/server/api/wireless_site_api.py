import json
import logging
from datetime import datetime

from flask import Blueprint, request, jsonify
from server.db.models.wireless import WirelessSiteLabel, WirelessChannel
from server.db.models.wireless_openwifi import Configurations
from server.db.pg_engine import get_pg_session
from server.util.permission import super_user_permission
from server.util.tip_client_util import ServerType, get
from sqlalchemy import func
from sqlalchemy.exc import IntegrityError

LOG = logging.getLogger(__name__)
wireless_site_mold = Blueprint("wireless_site_mold", __name__, template_folder='templates')


@wireless_site_mold.route('/channel', methods=['GET'])
def get_site_channel():
    site_id = request.args.get('siteId', type=str)
    sn = request.args.get('sn', type=str)

    if not site_id or not sn:
        return jsonify({"status": 400, "info": "siteId is required"})

    try:
        with get_pg_session() as session:
            # 1. 从configurations表中获取:venue字段等于站点ID，name字段等于radio的数据
            config = session.query(Configurations).filter(
                Configurations.venue == site_id,
                func.lower(Configurations.name) == "radio"
            ).first()

            if not config:
                return jsonify({"status": 404, "info": f"No radio configuration found for site {site_id}"})

            # 2. 解析configuration字段中的JSON内容（两层嵌套JSON）
            try:
                config_data = json.loads(config.configuration)
            except (json.JSONDecodeError, TypeError) as e:
                return jsonify({"status": 500, "info": "Invalid configuration JSON format"})

            # 3. 处理为列表的情况，取第一个元素
            if isinstance(config_data, list) and len(config_data) > 0:
                config_data = config_data[0]

            # 4. 进一步解析内层的 radios 配置
            country_code = None
            if isinstance(config_data, dict) and "configuration" in config_data:
                try:
                    radios_json = json.loads(config_data["configuration"])
                except Exception as e:
                    radios_json = {}

                if isinstance(radios_json, dict) and "radios" in radios_json:
                    radios = radios_json["radios"]
                    if isinstance(radios, list) and len(radios) > 0:
                        first_radio = radios[0]
                        if isinstance(first_radio, dict) and "country" in first_radio:
                            country_code = first_radio["country"]

            if not country_code:
                return jsonify({"status": 404, "info": "No country code found in radio configuration"})

            # 5. 根据国家码从wireless_channel表获取信道信息
            channel_info = session.query(
                WirelessChannel.country_code,
                WirelessChannel._2g_channel,
                WirelessChannel._5g_channel,
                WirelessChannel._6g_channel
            ).filter(
                WirelessChannel.country_code == country_code.upper()
            ).first()

            if not channel_info:
                return jsonify({"status": 404, "info": f"No channel information found for country code {country_code}"})

            result = {
                "country_code": channel_info.country_code,
                "2G": json.dumps(channel_info._2g_channel or {}),
                "5G": json.dumps(channel_info._5g_channel or {}),
                "6G": json.dumps(channel_info._6g_channel or {})
            }

            # 5. 根据设备SN，从gw处获取设备的能力集信息
            res = get(ServerType.OWGW, f"/api/v1/device/{sn}/capabilities")
            if not res:
                return jsonify({"status": 500, "info": "Failed to get device capabilities"})
            if 'ErrorCode' in res:
                return jsonify({"status": 500, "info": "Failed to get device capabilities"})
            for key, value in res['capabilities']['wifi'].items():
                if '2G' in value['band']:
                    result['2G_wifi'] = key
                    continue
                if '5G' in value['band']:
                    result['5G_wifi'] = key
                    continue
                if '6G' in value['band']:
                    result['6G_wifi'] = key

            # 6. 构造返回结果

            return jsonify({"status": 200, "info": result})

    except Exception as e:
        LOG.error(f"Failed to get site channel information: {str(e)}")
        return jsonify({"status": 500, "info": str(e)})


# 创建group
@wireless_site_mold.route('/label', methods=['POST'])
@super_user_permission.require(http_exception=403)
def add_label():
    try:
        info = request.get_json()
        site_id = info.get('site_id')
        name = info.get('name')

        if site_id is None or not name:
            msg = {"status": 400, "info": "site_id and name are required."}
            return jsonify(msg)

        # 开启事务
        with get_pg_session() as session:
            with session.begin():
                new_label = WirelessSiteLabel()
                new_label.site_id = site_id
                new_label.name = name
                new_label.create_time = datetime.now()
                new_label.modified_time = datetime.now()
                session.add(new_label)
            msg = {"status": 200, "info": "Create label success."}
    except IntegrityError:
        msg = {"status": 400, "info": "A label with the same site_id and name already exists."}
    except Exception as e:
        msg = {"status": 500, "info": str(e)}
    finally:
        return jsonify(msg)


@wireless_site_mold.route('/labels', methods=['POST'])
@super_user_permission.require(http_exception=403)
def batch_add_label():
    msg = {"status": 200, "info": "Create labels success."}
    try:
        info = request.get_json()
        site_id = info.get('site_id')
        name = info.get('name')

        if site_id is None or not name:
            msg = {"status": 400, "info": "site_id and name are required."}
            return jsonify(msg)

        name_set = set()
        for i in name:
            for j in i.split("$"):
                name_set.add(j)

        if len(name_set) == 0:
            return jsonify(msg)
        # 从WirelessSiteLabel根据site_id和name_set查询存在的label
        with get_pg_session() as session:
            exist_labels = session.query(WirelessSiteLabel).filter(
                WirelessSiteLabel.site_id == site_id,
                WirelessSiteLabel.name.in_(name_set)
            ).all()
            exist_names = {label.name for label in exist_labels}
            # 从name_set中移除已存在的标签
            name_set.difference_update(exist_names)

            with session.begin():
                for i in name_set:
                    label_to_merge = WirelessSiteLabel()
                    label_to_merge.site_id = site_id
                    label_to_merge.name = i
                    label_to_merge.create_time = datetime.now()
                    label_to_merge.modified_time = datetime.now()
                    session.merge(label_to_merge)
    except IntegrityError:
        msg = {"status": 400, "info": "A label with the same site_id and name already exists."}
    except Exception as e:
        msg = {"status": 500, "info": str(e)}
    finally:
        return jsonify(msg)


# get label list
@wireless_site_mold.route('/label', methods=['GET'])
@super_user_permission.require(http_exception=403)
def get_labels():
    site_id = request.args.get('siteId', type=int)
    key = request.args.get('key', '', type=str)

    if site_id is None:
        return jsonify({"status": 400, "info": "Parameter 'siteId' is required"}), 400

    try:
        with get_pg_session() as session:
            query = session.query(WirelessSiteLabel).filter_by(site_id=site_id)

            if key:
                query = query.filter(WirelessSiteLabel.name.ilike(f"%{key}%"))

            label = query.all()
            result = [{
                "id": g.id,
                "name": g.name,
                "site_id": g.site_id
            } for g in label]

            return jsonify({"status": 200, "info": result})
    except Exception as e:
        LOG.error(f"Failed to get site labels: {str(e)}")
        return jsonify({"status": 500, "info": str(e)})


# delete label
@wireless_site_mold.route('/label', methods=['DELETE'])
@super_user_permission.require(http_exception=403)
def delete_label():
    info = request.get_json()
    label_id = info.get('id')

    if label_id is None:
        return jsonify({"status": 400, "info": "Parameter 'label_id' is required"}), 400

    try:
        with get_pg_session() as session:
            with session.begin():
                label = session.query(WirelessSiteLabel).filter_by(id=label_id).first()
                if not label:
                    return jsonify({"status": 403, "info": "Label does not exist, deletion not allowed."})
                site_id = label.site_id
                label_name = label.name
                # Inventory删除label
                session.execute(
                    """
                    UPDATE inventory
                    SET labelsname = NULLIF(
                        array_to_string(
                            array_remove(
                                string_to_array(labelsname, ','),
                                :label_name
                            ),
                            ','
                        ),
                        ''
                    )
                    WHERE venue = :site_id AND labelsname LIKE '%' || :label_name || '%'
                    """,
                    {"label_name": label_name, "site_id": str(site_id)}
                )
                # wireless_configure_ssid删除jsonb数组中的label_name
                session.execute(
                    """
                    UPDATE wireless_configure_ssid
                    SET labels_name = COALESCE((
                        SELECT jsonb_agg(value)
                        FROM jsonb_array_elements_text(labels_name) AS value
                        WHERE value != :label_name
                    ), '[]'::jsonb)
                    WHERE site_id = :site_id AND labels_name @> to_jsonb(ARRAY[:label_name])
                    """,
                    {"label_name": label_name, "site_id": site_id}
                )

                session.delete(label)
            return jsonify({"status": 200, "info": "Delete label success."})
    except Exception as e:
        LOG.error(f"Delete label failed: {e}")
        return jsonify({"status": 500, "info": str(e)})
