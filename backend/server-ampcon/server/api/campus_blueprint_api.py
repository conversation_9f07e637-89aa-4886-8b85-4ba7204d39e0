import copy
import ipaddress
import json
import logging
import re
import traceback
import threading
from io import BytesIO
from flask import Blueprint, jsonify, Response, request, send_file
from datetime import timedelta, datetime, date
from sqlalchemy import func, or_, desc, and_, exists, cast, Integer
from sqlalchemy.orm import aliased

from server.util.permission import admin_permission
from server.db.models.campus_blueprint import campus_blueprint_db, CampusTopologyConfig, CampusSitePods, CampusSiteNodes
from server.db.models.inventory import ConfigDistributionTaskForCampus
from server.db.models import inventory
from server.db.models.inventory import inven_db, Switch, Site, AssociationSite
from server.util.prometheus_util import query_lldp_state
from server.util import utils
from server.celery_app.config_distribution_task import config_distribution_netconf_by_mlag, config_distribution_netconf_by_ip_clos, switch_templates_config_deploy_start
from server.util.switch_templates_campus_util import check_import_template

campus_blueprint_mold = Blueprint("campus_blueprint", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)


@campus_blueprint_mold.route("/site_topo/preview", methods=["POST"])
@admin_permission.require(http_exception=403)
def site_topo_preview():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 400, 'info': 'Invalid request data'})

        site_config_id = data.get('site_config_id')
        if not site_config_id:
            return jsonify({'status': 400, 'info': 'site_config_id not found'})

        # Get configuration record
        config = campus_blueprint_db.get_campus_site_config(site_config_id)
        if not config:
            return jsonify({'status': 404, 'info': 'site_config not found'})

        # Get node records and site
        nodes = campus_blueprint_db.get_campus_site_nodes(site_config_id)
        site = campus_blueprint_db.get_site_by_id(config.site_id)
        
        # Build base result structure
        result = build_base_result(site, config)
        
        # Process nodes based on topology type
        if config.type == 'mlag':
            process_mlag_nodes(result, nodes)
        else:  # ip-clos
            process_ip_clos_nodes(result, nodes, site_config_id)

        return jsonify(result)
        
    except Exception as e:
        LOG.error(f"Error in site_topo_preview: {str(e)}")
        LOG.error(traceback.format_exc())
        return Response(status=500)

def build_base_result(site, config):
    """Build base result structure"""
    result = {
        'site_id': site.site_name,
        'site_config_id': config.id,
        'topology_name': config.topology_name,
        'topology': config.configuration.get('topology', {}),
        'networks': config.configuration.get('networks', {}),
        'type': config.type,
        'nodes': {},
        'topology_view': {
            'nodes': [],
            'edges': []
        }
    }

    # Initialize node structure based on topology type
    if config.type == 'mlag':
        result['nodes'] = {
            'core': [],
            'access': []
        }
    else:  # ip-clos
        result['nodes'] = {
            'border': [],
            'core': [],
            'pods': []
        }
    
    return result

def process_mlag_nodes(result, nodes):
    """Process nodes for MLAG topology type"""
    # Get topology view data
    topology_view_nodes, topology_view_edges = campus_blueprint_db.get_topology_view_data(nodes)

    for node in nodes:
        node_info = node.node_info['node_info']
        switch_obj = campus_blueprint_db.get_switch_by_sn(node.switch_sn)
        if not switch_obj:
            continue

        node_data = campus_blueprint_db.build_node_data(node, node_info, switch_obj)

        # Add to corresponding type array
        if node.type in result['nodes']:
            result['nodes'][node.type].append(node_data)

    result['topology_view']['nodes'] = topology_view_nodes
    result['topology_view']['edges'] = topology_view_edges

def process_ip_clos_nodes(result, nodes, site_config_id):
    """Process nodes for IP-CLOS topology type"""
    # Get topology view data
    topology_view_nodes, topology_view_edges = campus_blueprint_db.get_topology_view_data(nodes)

    # Get pods mapping
    pods = campus_blueprint_db.get_pods_by_config(site_config_id)
    pods_map = {pod.id: {
        'pod_id': pod.id,
        'pod_name': pod.pod_name,
        'pod_index': pod.pod_index,
        'distribution': [],
        'access': []
    } for pod in pods}

    for node in nodes:
        node_info = node.node_info['node_info']
        switch_obj = campus_blueprint_db.get_switch_by_sn(node.switch_sn)
        if not switch_obj:
            continue

        node_data = campus_blueprint_db.build_node_data(node, node_info, switch_obj)

        # Assign node to appropriate location based on type
        if node.type in ['border', 'core']:
            result['nodes'][node.type].append(node_data)
        elif node.pod_id and node.pod_id in pods_map:
            pod = pods_map[node.pod_id]
            if node.type == 'distribution':
                pod['distribution'].append(node_data)
            elif node.type == 'access':
                pod['access'].append(node_data)

    # Add pods information to result
    result['nodes']['pods'] = list(pods_map.values())
    result['topology_view']['nodes'] = topology_view_nodes
    result['topology_view']['edges'] = topology_view_edges


@campus_blueprint_mold.route("/site_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def site_topo_save():
    try:
        data = request.get_json().get('data', {})
        if not data:
            return Response("Invalid request data", status=400)

        site_name = data.get('site_id')
        session = inven_db.get_session()
        site = session.query(inventory.Site).filter(inventory.Site.site_name == site_name).first()
        if not site:
            return jsonify({'status': 404, 'info': 'Site not found'})

        site_id = site.id
        topology_name = data.get('topology_name')
        topology_config_type = data.get('type')
        nodes_dict = data.get('nodes', {})  # Now in dictionary format

        config_data = {
            'topology_name': topology_name,
            'topology': data.get('topology'),
            'networks': data.get('networks')
        }

        config_id = data.get('config_id', "")
        if config_id:
            config = campus_blueprint_db.get_campus_site_config(config_id)
            if config:
                campus_blueprint_db.update_campus_site_config(
                    config_id,
                    site_id,
                    site_name,
                    topology_config_type,
                    topology_name,
                    config_data
                )
            else:
                return jsonify({'status': 404, 'info': 'Config not found'})
        else:
            if session.query(CampusTopologyConfig) \
                      .filter(CampusTopologyConfig.topology_name == topology_name) \
                      .first():
                return jsonify({'status': 409,
                                'info': f"Topology name '{topology_name}' already exists"})
            config = campus_blueprint_db.add_campus_site_config(
                site_id,
                site_name,
                topology_config_type,
                topology_name,
                config_data
            )

        current_switch_sns = set()

        if topology_config_type == 'mlag':
            # First collect and validate all port info
            port_info_map = {}  # {switch_sn: port_info}
            for core in nodes_dict.get('core', []):
                downstream_port_info = utils.calculate_downstream_port_info(core["model"])
                if not downstream_port_info:
                    return jsonify({'status': 400, 'info': f'Invalid model for core switch {core["switch_sn"]}'})
                port_info_map[core['switch_sn']] = downstream_port_info

            for access in nodes_dict.get('access', []):
                downstream_port_info = utils.calculate_downstream_port_info(
                    access["model"],
                    exclude_ports=list(access['links']['to_core'].keys())
                )
                if not downstream_port_info:
                    return jsonify({'status': 400, 'info': f'Invalid model for access switch {access["switch_sn"]}'})
                port_info_map[access['switch_sn']] = downstream_port_info

            # Calculate parameters
            inband_subnet_offset = 0
            subnet_ip_offset = 0
            asn_offset = 0
            node_params = {}  # {switch_sn: param}

            # Process core nodes
            for index, core in enumerate(nodes_dict.get('core', [])):
                # if core['links']['to_wan']:
                #     subnet_assigned_ip_count = 2
                # else:
                vrid_index = 0
                subnet_assigned_ip_count = 1

                if index % 2 == 0:
                    domain_id = 0
                    route_id_local = core['router_id']
                    route_id_peer = nodes_dict.get('core', [])[index + 1]['router_id']
                    if config_data['topology']['protocol'].upper() == 'BGP':
                        asn_local = str(int(config_data['topology']['as_base']) + asn_offset)
                        asn_peer = str(int(config_data['topology']['as_base']) + asn_offset + 1)
                    else:
                        asn_local = ""
                        asn_peer = ""

                    subnet_ip_local_list = utils.get_assigned_ip(
                        config_data['topology']['subnet'].split('/')[0],
                        subnet_ip_offset * subnet_assigned_ip_count + 1,
                        1,
                        is_include_start_ip=True
                    )
                    subnet_ip_peer_list = utils.get_assigned_ip(
                        config_data['topology']['subnet'].split('/')[0],
                        subnet_ip_offset * subnet_assigned_ip_count + subnet_assigned_ip_count + 1,
                        1,
                        is_include_start_ip=True
                    )
                else:
                    domain_id = 1
                    route_id_local = core['router_id']
                    route_id_peer = nodes_dict.get('core', [])[index - 1]['router_id']

                    if config_data['topology']['protocol'].upper() == 'BGP':
                        asn_local = str(int(config_data['topology']['as_base']) + asn_offset)
                        asn_peer = str(int(config_data['topology']['as_base']) + asn_offset - 1)
                    else:
                        asn_local = ""
                        asn_peer = ""

                    subnet_ip_local_list = utils.get_assigned_ip(
                        config_data['topology']['subnet'].split('/')[0],
                        subnet_ip_offset * subnet_assigned_ip_count + 1,
                        1,
                        is_include_start_ip=True
                    )
                    subnet_ip_peer_list = utils.get_assigned_ip(
                        config_data['topology']['subnet'].split('/')[0],
                        subnet_ip_offset * subnet_assigned_ip_count,
                        1,
                        is_include_start_ip=True
                    )

                downstream_port_upper_limit = port_info_map[core['switch_sn']]['downstream_port_upper_limit']
                core_param = build_core_param(
                    core, config_data, config.id, domain_id,
                    subnet_ip_peer_list, subnet_ip_local_list,
                    downstream_port_upper_limit, route_id_local,
                    route_id_peer, asn_local, asn_peer,
                    vrid_index, inband_subnet_offset
                )

                node_params[core['switch_sn']] = core_param
                current_switch_sns.add(core['switch_sn'])
                inband_subnet_offset += 1
                subnet_ip_offset += 1
                asn_offset += 1
                # vrid_index += len(core_param['new_val']['vlans'])

            # Process access nodes
            for access in nodes_dict.get('access', []):
                downstream_ports = port_info_map[access['switch_sn']]['downstream_ports']
                access_param = build_access_param(
                    access, config_data, config.id,
                    downstream_ports, inband_subnet_offset
                )

                node_params[access['switch_sn']] = access_param
                current_switch_sns.add(access['switch_sn'])
                inband_subnet_offset += 1

            config_param = {}

            # Save configurations and distribute
            for switch_sn, param in node_params.items():
                # Save to database
                node_info = next(n for n in nodes_dict.get(param['meta']['role'], [])
                                 if n['switch_sn'] == switch_sn)
                param['meta']['sn'] = switch_sn
                prev_node_config = campus_blueprint_db.add_campus_site_node_and_return_prev_node_config(
                    config.id,
                    switch_sn,
                    param['meta']['role'],
                    utils.get_campus_fabric_param_with_node_info(node_info, param)
                )
                param['old_val'] = prev_node_config

                # Log configuration parameters before distribution
                LOG.info(f"Distributing config for switch {switch_sn}, role: {param['meta']['role']}")

                # Format parameters for config distribution
                config_param[switch_sn] = param
                LOG.info(f"Configuration parameters: {json.dumps(param, indent=2)}")

            # Distribute configuration
            config_distribution_netconf_by_mlag(config_param)

            # Clean up unused nodes
            existing_sns = campus_blueprint_db.get_node_switch_sns_by_config(config.id)
            sns_to_delete = existing_sns - current_switch_sns
            if sns_to_delete:
                campus_blueprint_db.delete_nodes_by_sns(config.id, sns_to_delete)
        elif topology_config_type == 'ip-clos':
            # 初始化参数
            asn_offset = 0
            subnet_ip_offset = 0
            node_params = {}

            try:
                # 处理 border 节点
                for index, border in enumerate(nodes_dict.get('border', [])):
                    border_param = process_ip_clos_node(
                        border, config_data, config.id,
                        subnet_ip_offset, index, 'border'
                    )
                    node_params[border['switch_sn']] = border_param
                    current_switch_sns.add(border['switch_sn'])
                    subnet_ip_offset += 1

                # 处理 core 节点
                for index, core in enumerate(nodes_dict.get('core', [])):
                    core_param = process_ip_clos_node(
                        core, config_data, config.id,
                        subnet_ip_offset, index, 'core',
                        asn_offset=asn_offset
                    )
                    node_params[core['switch_sn']] = core_param
                    current_switch_sns.add(core['switch_sn'])
                    asn_offset += 1
                    subnet_ip_offset += 1

                nodes_dict_pod_id_list = list(map(lambda x: x.get('pod_id'), nodes_dict.get('pods', [])))
                campus_blueprint_db.remove_no_exist_site_pod(config.id, nodes_dict_pod_id_list)

                # 处理 pods
                for pod in nodes_dict.get('pods', []):
                    pod_obj = campus_blueprint_db.add_or_update_campus_site_pod(
                        config.id, pod['pod_name'], pod['pod_index'], pod.get('pod_id')
                    )
                    if not pod_obj:
                        raise ValueError(f'Pod not found with id {pod.get("pod_id")}')

                    # 处理 distribution 节点
                    for index, dist in enumerate(pod.get('distribution', [])):
                        dist_param = process_ip_clos_node(
                            dist, config_data, config.id,
                            subnet_ip_offset, index, 'distribution',
                            asn_offset=asn_offset, pod_id=pod_obj.id
                        )
                        node_params[dist['switch_sn']] = dist_param
                        current_switch_sns.add(dist['switch_sn'])
                        asn_offset += 1

                        campus_blueprint_db.add_campus_site_node_and_return_prev_node_config(
                            config.id,
                            dist['switch_sn'],
                            "distribution",
                            utils.get_campus_fabric_param_with_node_info(dist, dist_param),
                            pod_id=pod_obj.id
                        )

                    # 处理 access 节点
                    for index, access in enumerate(pod.get('access', [])):
                        access_param = process_ip_clos_node(
                            access, config_data, config.id,
                            subnet_ip_offset, index, 'access',
                            asn_offset=asn_offset, pod_id=pod_obj.id
                        )
                        node_params[access['switch_sn']] = access_param
                        current_switch_sns.add(access['switch_sn'])
                        asn_offset += 1

                        campus_blueprint_db.add_campus_site_node_and_return_prev_node_config(
                            config.id,
                            access['switch_sn'],
                            "access",
                            utils.get_campus_fabric_param_with_node_info(access, access_param),
                            pod_id=pod_obj.id
                        )

                # 配置下发和保存
                _save_and_distribute_config(node_params, config.id, current_switch_sns, nodes_dict)

            except Exception as e:
                LOG.error(f"Error in ip-clos processing: {str(e)}")
                LOG.error(traceback.format_exc())
                return jsonify({'status': 500, 'info': str(e)})

        return jsonify({'status': 200, 'info': 'Save topology succeed.'})

    except Exception as e:
        LOG.error(f"Error in site_topo_save: {str(e)}")
        LOG.error(traceback.format_exc())
        return Response(status=500)


def build_core_param(core, config_data, config_id, domain_id, subnet_ip_peer_list,
                     subnet_ip_local_list, downstream_port_upper_limit, route_id_local,
                     route_id_peer, asn_local, asn_peer, vrid_index, inband_subnet_offset):
    """Build parameters for core node in campus fabric.

    Args:
        core (dict): Core node configuration data
        config_data (dict): Overall configuration data
        config_id (int): Configuration ID
        domain_id (int): MLAG domain ID
        subnet_ip_peer_list (list): List of peer subnet IPs
        subnet_ip_local_list (list): List of local subnet IPs
        downstream_port_upper_limit (int): Maximum downstream port number
        route_id_local (str): Local router ID
        route_id_peer (str): Peer router ID
        asn_local (str): Local ASN
        asn_peer (str): Peer ASN
        vrid_index (int): VRRP ID index
        inband_subnet_offset (int): Inband subnet offset

    Returns:
        dict: Core node parameters
    """
    # Extract networks VLAN configuration
    networks_vlans = config_data['networks'].get('vlans', {})

    # Build VLAN configurations
    vlans_dict = _build_vlan_config(core['other_ip_config'], networks_vlans)

    # Build base parameter structure
    core_param = {
        'meta': _build_meta_config(config_id, config_data),
        'old_val': {},
        'new_val': {
            'inband': _build_inband_config(config_data, inband_subnet_offset, vrid_index),
            'vlans': vlans_dict,
            'mlag': _build_mlag_config(config_data, domain_id, subnet_ip_peer_list,
                                       subnet_ip_local_list, downstream_port_upper_limit,
                                       core['links']),
            'dhcp_relay': config_data['networks']['dhcp_relay']
        }
    }

    # Add WAN configuration if WAN links exist
    if core['links']['to_wan']:
        core_param['new_val']['wan'] = _build_wan_config(core['links'], config_data, domain_id)

    # Add routing protocol configuration
    _add_routing_config(core_param['new_val'], config_data, core['router_id'],
                        route_id_peer, asn_local, asn_peer)

    # Update VRID for VLANs
    _update_vlan_vrids(vlans_dict, vrid_index)

    return core_param


def _build_vlan_config(ip_configs, networks_vlans):
    """Build VLAN configuration dictionary"""
    return {
        vlan['vlan_name']: {
            'vlan_id': vlan['vlan_id'],
            'ip_address': vlan['ip_address'],
            'vlan_name': vlan['vlan_name'],
            'vrrp_ip': vlan['vrrp_ip'],
            'subnet': networks_vlans.get(vlan['vlan_name'], {}).get('subnet', '')
        } for vlan in ip_configs
    }


def _build_meta_config(config_id, config_data):
    """Build meta configuration"""
    return {
        'role': "core",
        'site_topo_id': config_id,
        'protocol': config_data['topology']['protocol']
    }


def _build_inband_config(config_data, inband_subnet_offset, vrid_index):
    """Build inband configuration"""
    inband_subnet = config_data["topology"]["inband_subnet"]
    subnet_mask = inband_subnet.split('/')[1]

    return {
        'vlan_id': '3965',
        'ip_address': (utils.get_assigned_ip_reverse(inband_subnet,
                                                     inband_subnet_offset * 1 + 1, 1)[0] + '/' + subnet_mask),
        'vrrp_ip': utils.get_assigned_ip_reverse(inband_subnet,
                                                 inband_subnet_offset * 1, 1)[0],
        'vrid': len(config_data['networks']['vlans']) + 1
    }


def _build_mlag_config(config_data, domain_id, subnet_ip_peer_list,
                       subnet_ip_local_list, downstream_port_upper_limit, links):
    """Build MLAG configuration"""
    return {
        'domain_id': config_data['networks']['domain_id'],
        'domain_id_node': str(domain_id),
        'peer_ipv4_address': subnet_ip_peer_list[0],
        'mlag_peer_lag_interface_name': f'ae{downstream_port_upper_limit + 1}',
        'mlag_peer_interface_name': list(links['to_core'].keys()),
        'mlag_peer_vlan_id': "3966",
        'mlag_peer_l3_interface_ip': subnet_ip_local_list[0],
        'mlag_access_links': [{
            'access_interface_name': [to_access_port],
            'access_lag_interface_name': f'ae{index + 2}',
            'link_id': str(index + 1)
        } for index, to_access_port in enumerate(links['to_access'].keys())]
    }


def _build_wan_config(links, config_data, domain_id):
    """Build WAN configuration"""
    subnet = config_data['topology']['subnet'].split('/')[0]
    return {
        'reserved_vlan': '3967-4094',
        "wan_interface_name": list(links['to_wan'].keys()),
        "wan_interface_ip": f"{utils.get_assigned_ip(subnet, 4 * (domain_id + 1) + 1, 1, True)[0]}/30",
        "default_route_next_hop": utils.get_assigned_ip(subnet, 4 * (domain_id + 1) + 2, 1, True)[0]
    }


def _add_routing_config(new_val, config_data, router_id, route_id_peer, asn_local, asn_peer):
    """Add routing protocol configuration"""
    if config_data['topology']['protocol'] == 'OSPF':
        new_val['ospf'] = {
            'router_id': router_id,
            'area_id': '0.0.0.0',
            'original_subnet': config_data['topology']['subnet']
        }
    elif config_data['topology']['protocol'] == 'BGP':
        new_val['bgp'] = {
            'asn': asn_local,
            'router_id': router_id,
            'neighbour_ip_address': route_id_peer,
            'neighbour_asn': asn_peer,
            'original_subnet': config_data['topology']['subnet']
        }


def _update_vlan_vrids(vlans_dict, vrid_index):
    """Update VRID for VLANs"""
    for vlan in vlans_dict.values():
        vlan['vrid'] = str(vrid_index + 1)
        vrid_index += 1


def build_access_param(access, config_data, config_id, downstream_ports, inband_subnet_offset):
    """Build parameters for access node"""
    inband_subnet = config_data['topology']['inband_subnet']
    core_vrrp_ip = utils.calculate_core_vrrp_ip(inband_subnet)

    # Get vlans configuration from networks
    networks_vlans = config_data['networks'].get('vlans', {})

    # Convert vlans from array to object format
    vlans_dict = {}
    for vlan in access['other_ip_config']:
        vlan_name = vlan['vlan_name']
        vlans_dict[vlan_name] = {
            'vlan_id': vlan['vlan_id'],
            'subnet': networks_vlans.get(vlan_name, {}).get('subnet', '')  # Get subnet from networks
        }

    result = {
        'meta': {
            'role': "access",
            'site_topo_id': config_id,
            'protocol': config_data['topology']['protocol'],
        },
        'old_val': {},
        'new_val': {
            'inband': {
                'vlan_id': '3965',
                'ip_address': f'{utils.get_assigned_ip(config_data["topology"]["inband_subnet"].split("/")[0], inband_subnet_offset * 1, 1)[0]}/{config_data["topology"]["inband_subnet"].split("/")[1]}',
                'vrrp_ip': core_vrrp_ip
            },
            'vlans': vlans_dict,
            'mlag': {
                "uplink_lag_interface_name": "ae1",
                "uplink_interface_name": list(access['links']['to_core'].keys()),
                'mlag_peer_vlan_id': "3966"
            },
            'dhcp_snooping': list(map(lambda x: x['vlan_id'], config_data['networks']['dhcp_snooping'])),
            'wan_connect': {}
        }
    }

    # Only add nac field if nac_servers is not empty
    if config_data['networks'].get('nac_servers'):
        result['new_val']['nac'] = {
            'access_interface_name': downstream_ports,
            'nac_servers': config_data['networks']['nac_servers']
        }

    return result


@campus_blueprint_mold.route("/site_topo/get", methods=["POST"])
@admin_permission.require(http_exception=403)
def site_topo_get():
    try:
        data = request.get_json()
        if not data:
            return Response("Invalid request data", status=400)

        topology_config_id = data.get('topology_config_id')
        if not topology_config_id:
            return Response("Missing topology_config_id", status=400)

        # Get configuration record
        config = campus_blueprint_db.get_campus_site_config(topology_config_id)
        if not config:
            return Response("Config not found", status=404)

        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        # Get node records
        nodes = campus_blueprint_db.get_campus_site_nodes(topology_config_id)

        result = {
            'topology_name': config.topology_name,
            'nodes': [],
            'edges': []
        }

        # Build node data
        for node in nodes:
            node_info = node.node_info['node_info']
            node_data = {
                'switch_sn': node.switch_sn,
                'type': node.type,
                'router_id': node_info.get('router_id'),
                'links': node_info.get('links', {}),
                'mac_addr': node_info.get('mac_addr', ''),
                'other_ip_config': node_info.get('other_ip_config', [])
            }
            result['nodes'].append(node_data)
            lldp_state = query_lldp_state(node.switch_sn, node_info.get('mac_addr', ''), date)
            node_data.update(lldp_state)

            # Build edge data
            links = node_info.get('links', {})
            for link_type, link_ports in links.items():
                for port_id, target_sn in link_ports.items():
                    edge = {
                        'source_sn': node.switch_sn,
                        'source_label': node_info.get('mac_addr', ''),
                        'target_sn': target_sn,
                        'target_label': '',
                        'port_info': [{
                            'source_port': port_id,
                            'target_port': port_id
                        }]
                    }
                    result['edges'].append(edge)

        return jsonify(result)

    except Exception as e:
        LOG.error(f"Error in site_topo_get: {str(e)}")
        LOG.error(traceback.format_exc())
        return Response(status=500)
    pass


@campus_blueprint_mold.route("/site_topo/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def site_topo_list():
    try:
        session = inven_db.get_session()

        # 构建基础查询
        pre_query = session.query(CampusTopologyConfig).filter(CampusTopologyConfig.type != "template")

        # 获取分页和搜索结果
        page_num, page_size, total_count, query_result = utils.query_helper(
            CampusTopologyConfig,
            pre_query=pre_query,
            data=request.get_json()
        )

        # 构建返回结果
        res = []
        for config in query_result:
            res.append({
                "id": config.id,
                "topology_name": config.topology_name,
                "type": config.type,
                "site_name": config.site_name,
                "create_time": config.create_time.strftime('%Y-%m-%d %H:%M:%S') if config.create_time else ""
            })

        msg = {
            'status': 200,
            "data": res,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count
        }
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology get fail', 'status': 500}
        return jsonify(msg)


@campus_blueprint_mold.route("/site_topo_info/all", methods=["POST"])
@admin_permission.require(http_exception=403)
def site_topo_info_all():
    try:
        session = inven_db.get_session()
        campus_topology_config_data = session.query(CampusTopologyConfig).filter(CampusTopologyConfig.type != "template").all()

        res = []

        # Build node data
        for config in campus_topology_config_data:
            res.append({
                "id": config.id,
                "topology_name": config.topology_name
            })

        msg = {
            'status': 200,
            "data": res
        }
        return jsonify(msg)

    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology info get fail', 'status': 500}
        return jsonify(msg)


@campus_blueprint_mold.route("/site_topo/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def site_topo_delete():
    try:
        topology_config_id = request.get_json().get('topology_config_id')
        if not topology_config_id:
            return Response("Missing topology_config_id", status=400)

        config = campus_blueprint_db.get_campus_site_config(topology_config_id)
        if not config:
            return Response("Config not found", status=404)

        campus_blueprint_db.delete_campus_site_config(topology_config_id)

        msg = {'status': 200, "data": {}, "info": "Delete topology successed."}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology get fail', 'status': 500}
        return jsonify(msg)


@campus_blueprint_mold.route("/campus_fabric/deployment_status", methods=["POST"])
@admin_permission.require(http_exception=403)
def campus_fabric_deployment_status():
    campus_task_status = ["PENDING", "RUNNING", "SUCCEED", "FAILED"]
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        site_config_id = data.get('siteConfigId')

        session = inven_db.get_session()

        recent_task_record = session.query(ConfigDistributionTaskForCampus).filter(
            ConfigDistributionTaskForCampus.site_id == site_config_id).order_by(
            ConfigDistributionTaskForCampus.create_time.desc()).limit(1).all()

        if not recent_task_record:
            return jsonify({'status': 200, 'data': [], 'page': 1, 'pageSize': 10, 'total': 0})

        pre_query = session.query(ConfigDistributionTaskForCampus, Switch).outerjoin(Switch,
                                                                                     ConfigDistributionTaskForCampus.sn == Switch.sn).filter(
            ConfigDistributionTaskForCampus.site_id == site_config_id).filter(
            ConfigDistributionTaskForCampus.task_name == recent_task_record[0].task_name)

        for filter_field in data.get('filterFields', []):
            if filter_field['field'] == "host_name" and filter_field['filters']:
                pre_query = pre_query.filter(
                    Switch.host_name.in_(list(map(lambda x: x['value'], filter_field['filters']))))
            elif filter_field['field'] == "sn" and filter_field['filters']:
                pre_query = pre_query.filter(Switch.sn.in_(list(map(lambda x: x['value'], filter_field['filters']))))
            elif filter_field['field'] == "task_role" and filter_field['filters']:
                pre_query = pre_query.filter(ConfigDistributionTaskForCampus.task_role.in_(
                    list(map(lambda x: x['value'], filter_field['filters']))))
            elif filter_field['field'] == "task_status" and filter_field['filters']:
                filter_values = [f['value'].strip().upper() for f in filter_field['filters']]
                matched_values = []
                for value in filter_values:
                    for idx, status in enumerate(campus_task_status):
                        if value in status.upper():
                            matched_values.append(idx)
                matched_values = list(set(matched_values))
                if matched_values:
                    pre_query = pre_query.filter(ConfigDistributionTaskForCampus.task_status.in_(matched_values))
                else:
                    pre_query = pre_query.filter(False)

        if data.get('searchFields', {}):
            search_field_value = data['searchFields'].get('value')
            if search_field_value:
                search_upper = search_field_value.strip().upper()
                matched_status_indices = [
                    idx
                    for idx, status in enumerate(campus_task_status)
                    if search_upper in status.upper()
                ]
                search_conditions = [
                    Switch.host_name.like(f"%{search_field_value}%"),
                    Switch.mgt_ip.like(f"%{search_field_value}%"),
                    Switch.sn.like(f"%{search_field_value}%"),
                    ConfigDistributionTaskForCampus.task_role.like(f"%{search_field_value}%")
                ]
                if matched_status_indices:
                    search_conditions.append(
                        ConfigDistributionTaskForCampus.task_status.in_(matched_status_indices)
                    )
                pre_query = pre_query.filter(or_(*search_conditions))

        for sort_field in data.get('sortFields', []):
            if sort_field['field'] == "create_time":
                pre_query = pre_query.order_by(ConfigDistributionTaskForCampus.create_time.asc() if sort_field[
                                                                                                        'order'] == "asc" else ConfigDistributionTaskForCampus.create_time.desc())
            elif sort_field['field'] == "host_name":
                pre_query = pre_query.order_by(
                    Switch.host_name.asc() if sort_field['order'] == "asc" else Switch.host_name.desc())
            elif sort_field['field'] == "sn":
                pre_query = pre_query.order_by(Switch.sn.asc() if sort_field['order'] == "asc" else Switch.sn.desc())
            elif sort_field['field'] == "task_role":
                pre_query = pre_query.order_by(ConfigDistributionTaskForCampus.task_role.asc() if sort_field[
                                                                                                      'order'] == "asc" else ConfigDistributionTaskForCampus.task_role.desc())
            elif sort_field['field'] == "task_status":
                pre_query = pre_query.order_by(ConfigDistributionTaskForCampus.task_status.asc() if sort_field[
                                                                                                        'order'] == "asc" else ConfigDistributionTaskForCampus.task_status.desc())

        page_num, page_size, total_count, query_result = utils.query_helper(
            ConfigDistributionTaskForCampus,
            pre_query=pre_query,
            data=request.get_json(),
            disable_filter_and_sorter=True
        )

        res = []
        for item in query_result:
            deploy_time = item[0].end_time.strftime('%Y-%m-%d %H:%M:%S')
            if item[0].task_status == 3:  # 任务失败
                task_log = f"{deploy_time}: Configuration and commit failed."
            else:  # 任务成功或其他状态
                task_log = f"{deploy_time}: {item[0].task_traceback_info if item[0].task_traceback_info else ''}"

            res.append({
                "sn": item[0].sn,
                "task_status": campus_task_status[item[0].task_status],
                "task_name": item[0].task_name,
                "task_role": item[0].task_role,
                "id": item[0].id,
                "task_log": task_log,
                "host_name": item[1].host_name,
                "mgt_ip": item[1].mgt_ip,
                "link_ip_addr": item[1].link_ip_addr,
                "reachable_status": item[1].reachable_status
            })

        return jsonify({
            'status': 200,
            "data": res,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count
        })
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Get deploy task status failed.'})

def _calculate_subnet_ips(config_data, subnet_ip_offset, subnet_assigned_ip_count, index):
    """计算子网 IP 地址"""
    subnet = config_data['topology']['subnet'].split('/')[0]
    domain_id = index % 2

    if domain_id == 0:
        subnet_ip_local_list = utils.get_assigned_ip(
            subnet,
            subnet_ip_offset * subnet_assigned_ip_count + 1,
            1,
            is_include_start_ip=True
        )
        subnet_ip_peer_list = utils.get_assigned_ip(
            subnet,
            subnet_ip_offset * subnet_assigned_ip_count + subnet_assigned_ip_count + 1,
            1,
            is_include_start_ip=True
        )
    else:
        subnet_ip_local_list = utils.get_assigned_ip(
            subnet,
            subnet_ip_offset * subnet_assigned_ip_count + 1,
            1,
            is_include_start_ip=True
        )
        subnet_ip_peer_list = utils.get_assigned_ip(
            subnet,
            subnet_ip_offset * subnet_assigned_ip_count,
            1,
            is_include_start_ip=True
        )

    return domain_id, subnet_ip_local_list, subnet_ip_peer_list

def _build_mlag_base_config(config_data, domain_id, subnet_ip_peer_list, subnet_ip_local_list,
                           downstream_port_upper_limit, links, link_type='to_core'):
    """构建基础 MLAG 配置"""
    return {
        'domain_id': config_data['networks']['domain_id'],
        'domain_id_node': str(domain_id),
        'peer_ipv4_address': subnet_ip_peer_list[0],
        'mlag_peer_lag_interface_name': f'ae{downstream_port_upper_limit + 1}',
        'mlag_peer_interface_name': list(links.get(link_type, {}).keys()),
        'mlag_peer_vlan_id': "3966",
        'mlag_peer_l3_interface_ip': subnet_ip_local_list[0]
    }

def _build_node_base_param(role, config_id, protocol='BGP'):
    """构建节点基础参数"""
    return {
        'meta': {
            'role': role,
            'site_topo_id': config_id,
            'protocol': protocol
        },
        'old_val': {},
        'new_val': {}
    }

def _process_vlan_config(node_param, node, config_data):
    """处理 VLAN 和 VRF 配置"""
    for vlan_config in node.get('other_ip_config', []):
        vlan_name = vlan_config['vlan_name']
        vlan_id = vlan_config['vlan_id']
        network_config = config_data['networks']['vlans'].get(vlan_name, {})

        # 添加 VLAN 配置
        if 'vlans' not in node_param['new_val']:
            node_param['new_val']['vlans'] = {}

        node_param['new_val']['vlans'][vlan_name] = {
            'vlan_id': vlan_id,
            'subnet': network_config.get('subnet', ''),
            'vlan_ip_address': vlan_config.get('ip_address', ''),
            'vlan_anycast_ip_address': vlan_config.get('anycast_ip_address', '')
        }

        # 处理 VRF 配置
        node_param['new_val']['vrfs'] = config_data['networks'].get('vrfs', {})

        # 处理 DHCP relay 配置
        if network_config.get('dhcp_server'):
            if 'dhcp_relay' not in node_param['new_val']:
                node_param['new_val']['dhcp_relay'] = []

            node_param['new_val']['dhcp_relay'].append({
                'dhcp_network': vlan_name,
                'dhcp_server': network_config['dhcp_server'],
                'vlan_id': vlan_id,
                'network_vlan_name': vlan_name
            })

def _add_wan_config(node_param, node, index, config_data):
    """添加 WAN 配置"""
    if node['links'].get('to_wan'):
        subnet = config_data['topology']['subnet']
        node_param['new_val']['wan'] = {
            'wan_interface_name': list(node['links']['to_wan'].keys()),
            'default_route_next_hop': utils.get_assigned_ip(subnet.split('/')[0], 4 * (index + 1) + 2, 1, True)[0],
            'ipv4_address': utils.get_assigned_ip(subnet.split('/')[0], 4 * (index + 1) + 1, 1, True)[0]
        }

def _calculate_downstream_ports(node, exclude_port_type='to_dis'):
    """计算下行端口"""
    downstream_port_info = utils.calculate_downstream_port_info(
        node['model'],
        exclude_ports=list(node['links'].get(exclude_port_type, {}).keys())
    )
    if not downstream_port_info:
        raise ValueError(f'Invalid model for switch {node["switch_sn"]}')
    return downstream_port_info

def process_ip_clos_node(node, config_data, config_id, subnet_ip_offset, index, role,
                        asn_offset=None, pod_id=None):
    """处理 IP-CLOS 节点"""
    try:
        # 计算下行端口
        downstream_port_info = _calculate_downstream_ports(node)
        downstream_port_upper_limit = downstream_port_info['downstream_port_upper_limit']
        downstream_ports = downstream_port_info.get('downstream_ports')

        # 计算子网 IP
        domain_id, subnet_ip_local_list, subnet_ip_peer_list = _calculate_subnet_ips(
            config_data, subnet_ip_offset, 1, index
        )

        # 构建基础参数
        node_param = _build_node_base_param(role, config_id)

        # 添加 MLAG 配置
        node_param['new_val']['mlag'] = _build_mlag_base_config(
            config_data, domain_id, subnet_ip_peer_list, subnet_ip_local_list,
            downstream_port_upper_limit, node['links']
        )

        # 根据角色添加特定配置
        if role in ['core', 'access']:
            node_param['new_val'].update({
                'link_route': {
                    'reserved_vlan': '3967-4094',
                    'link_interfaces': list(node['links'].get('to_dis', {}).keys()),
                    'to_border_interfaces': list(node['links'].get('to_border', {}).keys())
                },
                'underlay': {
                    'router_id': node['router_id'],
                    'asn': str(int(config_data['topology']['as_base']) + (asn_offset or 0)),
                    'original_subnet': config_data['topology']['subnet']
                },
                'vlans': config_data['networks'].get('vlans', {}),
                'nac': {
                    'access_interface_name': downstream_ports,
                    'nac_servers': config_data['networks'].get('nac_servers', [])
                },
                'vnis': config_data['networks'].get('vnis', {})
            })

        if role == 'distribution':
            # 为 distribution 节点添加 BGP 配置
            node_param['new_val'].update({
                'link_route': {
                    'reserved_vlan': '3967-4094',
                    'link_interfaces': list(node['links'].get('to_access', {}).keys()),
                    'to_border_interfaces': list(node['links'].get('to_border', {}).keys())
                },
                'bgp': {
                    'router_id': node['router_id'],
                    'asn': str(int(config_data['topology']['as_base']) + (asn_offset or 0))
                }
            })

        if role == 'access':
            node_param['new_val'].update({
                'vrfs': config_data['networks'].get('vrfs', {}),
                'dhcp_relay': config_data['networks'].get('dhcp_relay', [])
            })

        # 处理 VLAN 配置
        _process_vlan_config(node_param, node, config_data)

        # 添加 WAN 配置
        if role == 'core':
            _add_wan_config(node_param, node, index, config_data)

        return node_param

    except Exception as e:
        LOG.error(f"Error processing {role} node: {str(e)}")
        raise

def flatten_pods(nodes_dict):
    """将多个pod的节点信息压缩成单个列表

    Args:
        pods: 原始pod列表数据

    Returns:
        dict: 压缩后的数据结构,包含所有distribution和access节点
    """
    result = {
        'distribution': [],
        'access': [],
        'core': [],
        'border': []
    }

    result['core'] = nodes_dict.get("core", [])
    result['border'] = nodes_dict.get("border", [])

    pods = nodes_dict.get("pods", [])
    for pod in pods:
        # 合并 distribution 节点
        if pod.get('distribution'):
            result['distribution'].extend(pod['distribution'])

        # 合并 access 节点
        if pod.get('access'):
            result['access'].extend(pod['access'])

    return result

# 使用示例:
# flattened_nodes = flatten_pods(pods)

def _save_and_distribute_config(node_params, config_id, current_switch_sns, nodes_dict):

    config_param = {}
    # Save configurations and distribute
    for switch_sn, param in node_params.items():
        # Save to database
        role = param['meta']['role']
        node_info = next((n for n in flatten_pods(nodes_dict).get(role, [])
                         if n['switch_sn'] == switch_sn), None)
        if node_info is None:
            continue

        param['meta']['sn'] = switch_sn
        prev_node_config = campus_blueprint_db.add_campus_site_node_and_return_prev_node_config(
            config_id,
            switch_sn,
            role,
            utils.get_campus_fabric_param_with_node_info(node_info, param)
        )
        param['old_val'] = prev_node_config

        # Log configuration parameters before distribution
        LOG.info(f"Distributing config for switch {switch_sn}, role: {role}")

        # Format parameters for config distribution
        config_param[switch_sn] = param
        LOG.info(f"Configuration parameters: {json.dumps(param, indent=2)}")

    # Distribute configuration
    config_distribution_netconf_by_ip_clos(config_param)

    # Clean up unused nodes
    existing_sns = campus_blueprint_db.get_node_switch_sns_by_config(config_id)
    sns_to_delete = existing_sns - current_switch_sns
    if sns_to_delete:
        campus_blueprint_db.delete_nodes_by_sns(config_id, sns_to_delete)

@campus_blueprint_mold.route("/get_switch_status", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_switch_status():
    try:
        data = request.get_json()
        session = inven_db.get_session()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        sn_list = data.get('snList')
        if not sn_list:
            return jsonify({'info': "Missing snList", 'status': 400})
        switches = session.query(Switch).filter(Switch.sn.in_(sn_list)).all()
        switch_status = {switch.sn: switch.reachable_status for switch in switches}
        return jsonify({'status': 200, 'data': switch_status})

    except Exception as e:
        LOG.error(f"Error in get_switch_status: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({'status': 500, 'info': str(e)})


@campus_blueprint_mold.route("/switch_templates/config_delploy", methods=["POST"])
@admin_permission.require(http_exception=403)
def switch_templates_config_deploy():
    try:
        data = request.get_json()
        if not data:
            return Response("Invalid request data", status=400)

        session = inven_db.get_session()

        with session.begin():
            template_name = data.get("name")
            template_config = {
                "switch": data.get("switch", {}),
                "management": data.get("management", {})
            }
            # 判断前端是否有传来 id,如果没有则为新增 template 操作,如果有则是编辑 template 操作
            if not data.get("id"):
                # Check if the template name already exists
                check_template = (session.query(CampusTopologyConfig).filter(
                    CampusTopologyConfig.topology_name == data.get("name"),
                    CampusTopologyConfig.type == "template"
                ).first())
                if check_template:
                    return jsonify({
                        'status': 400,
                        'info': 'The template name already exists!'
                    })

                new_template = CampusTopologyConfig(
                    site_id=1,
                    site_name="default",
                    topology_name=template_name,
                    type="template",
                    configuration=template_config
                )
                session.add(new_template)
                session.flush()  # 确保插入操作被处理，但不提交事务
                template_id = new_template.id
            else:
                template_id = data.get("id")
                template = session.query(CampusTopologyConfig).filter(CampusTopologyConfig.id==template_id).first()
                if template:
                    template.topology_name = template_name
                    template.configuration = template_config

            sn_list = data.get("scope").get("switchList")
            if not sn_list:
                return jsonify({
                    'status': 200,
                    'info': 'Save switch template succeed, but no template was issued to any switch.'
                })
            switch_config_list = []
            for sn in sn_list:
                # 根据 sn, template_id 和 type=template 来查询 campus_site_nodes 表
                node = session.query(CampusSiteNodes).filter(
                    CampusSiteNodes.topology_config_id == template_id,
                    CampusSiteNodes.switch_sn == sn,
                    CampusSiteNodes.type == "template"
                ).first()
                if node:
                    node_info = node.node_info
                    new_node_info = {
                        "new_template": template_config,
                        "old_template": node_info.get("new_template", {}),
                    }
                else:
                    task = session.query(ConfigDistributionTaskForCampus).filter(
                        ConfigDistributionTaskForCampus.task_id == template_id,
                        ConfigDistributionTaskForCampus.task_type == "template",
                        ConfigDistributionTaskForCampus.sn == sn,
                        ConfigDistributionTaskForCampus.task_status == 2,
                    ).order_by(ConfigDistributionTaskForCampus.create_time.desc()).first()
                    if task:
                        new_node_info = json.loads(task.config_data)
                        new_node_info["old_template"] = new_node_info.get("new_template", {})
                        new_node_info["new_template"] = {}
                    else:
                        new_node_info = {
                            "new_template": {},
                            "old_template": {}
                        }
                    new_node = CampusSiteNodes(
                        topology_config_id=template_id,
                        switch_sn=sn,
                        type="template",
                        node_info=new_node_info
                    )
                    session.add(new_node)

                switch_info = {
                    "sn": sn,
                    "new_template": template_config,
                    "old_template": new_node_info.get("old_template", {})
                }
                switch_config_list.append(switch_info)

            # 删除此次不下发的sn的campus_site_nodes记录
            session.query(CampusSiteNodes).filter(
                CampusSiteNodes.topology_config_id == template_id,
                CampusSiteNodes.type == "template",
                ~CampusSiteNodes.switch_sn.in_(sn_list)
            ).delete(synchronize_session=False)

        template_info = {
            "id": template_id,
            "name": template_name,
            "config": template_config
        }

        switch_templates_config_deploy_start(switch_config_list, template_info)
        return jsonify({'status': 200, 'info': 'Save switch template succeed.'})
    except Exception as e:
        LOG.error(f"Error in switch_templates_config_deploy: {str(e)}")
        LOG.error(traceback.format_exc())
        return Response(status=500)


@campus_blueprint_mold.route("/switch_templates/get_list", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_switch_template_list():
    session = inven_db.get_session()
    data = request.get_json()

    # 子查询：按 topology_config_id 统计「合法」模板交换机数量
    # 合法条件：type='template' 且 switch 表中存在对应 sn
    subq = (
        session.query(
            CampusSiteNodes.topology_config_id,
            func.count(CampusSiteNodes.switch_sn).label("switch_num")
        )
        .filter(
            CampusSiteNodes.type == "template",
            exists().where(Switch.sn == CampusSiteNodes.switch_sn)
        )
        .group_by(CampusSiteNodes.topology_config_id)
        .subquery("ts")
    )

    # 主查询：LEFT JOIN 子查询，按 create_time 倒序
    pre_query = (
        session.query(
            CampusTopologyConfig.id,
            CampusTopologyConfig.topology_name,
            subq.c.switch_num,
            CampusTopologyConfig.create_time
        )
        .outerjoin(subq, subq.c.topology_config_id == CampusTopologyConfig.id)
        .filter(CampusTopologyConfig.type == "template")
    )

    if data["sortFields"]:
        sort_fields_list = data["sortFields"]
        for item in sort_fields_list:
            if item["field"] == "name":
                if item["order"] == "desc":
                    pre_query = pre_query.order_by(desc(CampusTopologyConfig.topology_name))
                elif item["order"] == "asc":
                    pre_query = pre_query.order_by(CampusTopologyConfig.topology_name)
            if item["field"] == "create_time":
                if item["order"] == "desc":
                    pre_query = pre_query.order_by(desc(CampusTopologyConfig.create_time))
                elif item["order"] == "asc":
                    pre_query = pre_query.order_by(CampusTopologyConfig.create_time)
            if item["field"] == "switch_num":
                if item["order"] == "desc":
                    pre_query = pre_query.order_by(subq.c.switch_num.desc())
                elif item["order"] == "asc":
                    pre_query = pre_query.order_by(subq.c.switch_num.asc())
        data["sortFields"] = []

    page_num, page_size, total_count, query_template = utils.query_helper(CampusTopologyConfig, pre_query=pre_query, data=data)
    res = []
    for template in query_template:
        info = {
            "id": template.id,
            "name": template.topology_name,
            "switch_num": template.switch_num if template.switch_num else 0,
            "create_time": template.create_time.strftime('%Y-%m-%d %H:%M:%S'),
        }
        res.append(info)

    return jsonify({
        "data": res,
        "page": page_num,
        "pageSize": page_size,
        "total": total_count,
        "status": 200
    })


@campus_blueprint_mold.route("/switch_templates/get_added_switch_list", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_added_switch_list():
    data = request.get_json()
    sn_list = data.get("switchList")
    session = inven_db.get_session()
    added_switch = session.query(Switch).filter(Switch.sn.in_(sn_list))
    page_num, page_size, total_count, query_switch = utils.query_helper(Switch, pre_query=added_switch)
    res = []
    for switch in query_switch:
        info = {
            "id": switch.id,
            "host_name": switch.host_name,
            "sn": switch.sn,
            "mgt_ip": switch.mgt_ip,
            "platform_model": switch.platform_model,
            "reachable_status": switch.reachable_status
        }
        as_site = session.query(AssociationSite).filter(AssociationSite.switch_id == switch.id).first()
        site = session.query(Site).filter(Site.id == as_site.site_id).first()
        if site:
            info["site"] = site.site_name
        else:
            info["site"] = ""
        res.append(info)
    return jsonify({
        "data": res,
        "page": page_num,
        "pageSize": page_size,
        "total": total_count,
        "status": 200
    })


@campus_blueprint_mold.route("/switch_templates/get_switch_list", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_switch_template_switch_list():
    session = inven_db.get_session()
    data = request.get_json()
    filter_fields_list = data.get("filterFields", "")
    filter_site_value = ""
    filter_template_name_value = ""
    if filter_fields_list:
        site_no = 0
        template_name_no = 0
        for i, filter_field in enumerate(filter_fields_list):
            if filter_field.get("field") == "site":
                site_filters = filter_field.get("filters")
                if site_filters:
                    filter_site_value = site_filters[0].get("value", "")
                site_no = i
            if filter_field.get("field") == "template_name":
                template_name_filters = filter_field.get("filters")
                if template_name_filters:
                    filter_template_name_value = template_name_filters[0].get("value", "")
                template_name_no = i
        if site_no > template_name_no:
            del filter_fields_list[site_no]
            del filter_fields_list[template_name_no]
        elif site_no < template_name_no:
            del filter_fields_list[template_name_no]
            del filter_fields_list[site_no]

    sort_fields_list = data.get("sortFields", "")
    sort_order_site = ""
    sort_order_template_name = ""
    if sort_fields_list:
        for i, sort_field in enumerate(sort_fields_list):
            if sort_field.get("field") == "site":
                sort_order_site = sort_field.get("order")
                del sort_fields_list[i]
                break
        for i, sort_field in enumerate(sort_fields_list):
            if sort_field.get("field") == "template_name":
                sort_order_template_name = sort_field.get("order")
                del sort_fields_list[i]
                break

    search_fields_dict = data.get("searchFields", "")
    search_value = ""
    if search_fields_dict:
        search_value = search_fields_dict.get("value", "")
    data.pop("searchFields", None)

    # 1. 子查询 sm：每个 sn 最新、task_type='template'、task_status=2 的记录
    sm = (
        session.query(
            ConfigDistributionTaskForCampus.sn,
            func.max(ConfigDistributionTaskForCampus.create_time).label("max_time")
        )
        .filter(
            ConfigDistributionTaskForCampus.task_type == "template",
            ConfigDistributionTaskForCampus.task_status == 2
        )
        .group_by(ConfigDistributionTaskForCampus.sn)
        .subquery("sm")
    )

    # 2. 子查询 ts：将 sm 与表本身连接，得到对应的 task_id（即 topology_config_id）
    ts = (
        session.query(
            ConfigDistributionTaskForCampus.task_id,
            ConfigDistributionTaskForCampus.sn
        )
        .join(
            sm,
            and_(
                ConfigDistributionTaskForCampus.sn == sm.c.sn,
                ConfigDistributionTaskForCampus.create_time == sm.c.max_time
            )
        )
        .subquery("ts")
    )

    # 3. 子查询 ns：将 ts 与 campus_topology_config 关联，得到 topology_name
    ns = (
        session.query(
            CampusTopologyConfig.topology_name,
            ts.c.sn
        )
        .join(ts, CampusTopologyConfig.id == ts.c.task_id)
        .subquery("ns")
    )

    # 4. 主查询：三表内联 + 左联 ns
    pre_query = (
        session.query(
            Switch.id,
            Switch.host_name,
            Switch.sn,
            Switch.mgt_ip,
            Switch.platform_model,
            Switch.reachable_status,
            Site.site_name,
            ns.c.topology_name
        )
        .join(AssociationSite, Switch.id == AssociationSite.switch_id)
        .join(Site, AssociationSite.site_id == Site.id)
        .outerjoin(ns, Switch.sn == ns.c.sn)
    )

    conditions = []
    if filter_site_value:
        conditions.append(Site.site_name.contains(filter_site_value))
    if filter_template_name_value:
        conditions.append(ns.c.topology_name.contains(filter_template_name_value))
    if search_value:
        conditions.append(
            or_(
                Switch.host_name.contains(search_value),
                Switch.sn.contains(search_value),
                Switch.mgt_ip.contains(search_value),
                Switch.platform_model.contains(search_value),
                Site.site_name.contains(search_value),
                ns.c.topology_name.contains(search_value)
            )
        )
    pre_query = pre_query.filter(*conditions)

    if sort_order_site == "desc":
        pre_query = pre_query.order_by(desc(Site.site_name))
    elif sort_order_site == "asc":
        pre_query = pre_query.order_by(Site.site_name)

    if sort_order_template_name == "desc":
        pre_query = pre_query.order_by(desc(ns.c.topology_name))
    elif sort_order_template_name == "asc":
        pre_query = pre_query.order_by(ns.c.topology_name)

    page_num, page_size, total_count, query_switch = utils.query_helper(Switch, pre_query=pre_query, data=data)
    res = []
    for id, host_name, sn, mgt_ip, platform_model, reachable_status, site_name, topology_name in query_switch:
        info = {
            "id": id,
            "host_name": host_name,
            "sn": sn,
            "mgt_ip": mgt_ip,
            "platform_model": platform_model,
            "reachable_status": reachable_status,
            "site": site_name,
            "template_name": topology_name
        }
        res.append(info)

    selected_sn = data.get("switchList", [])
    if selected_sn:
        for item in res:
            if item.get("sn") in selected_sn:
                item["selected"] = True
            else:
                item["selected"] = False

    return jsonify({
        "data": res,
        "page": page_num,
        "pageSize": page_size,
        "total": total_count,
        "status": 200
    })


@campus_blueprint_mold.route("/switch_templates/get_view", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_view():
    id = int(request.args.get('id'))
    with inven_db.get_session() as session:
        with session.begin():
            task_log = session.query(ConfigDistributionTaskForCampus).filter(
                ConfigDistributionTaskForCampus.task_id == str(id),
                ConfigDistributionTaskForCampus.task_type == "template"
            ).filter(
                or_(
                    ConfigDistributionTaskForCampus.task_status == 1,
                    ConfigDistributionTaskForCampus.task_status == 0,
                )
            ).first()
            if task_log:
                return jsonify({
                    "status": 400,
                    "msg": "Template configuration task is currently running and cannot be modified!"
                })

            template = session.query(CampusTopologyConfig).filter(CampusTopologyConfig.id == id).first()
            template_name = template.topology_name
            config_data = template.configuration
            template_id = template.id
            nodes = session.query(CampusSiteNodes).filter(CampusSiteNodes.topology_config_id == template_id).all()
            sn_list = []
            sn_delete_list = []
            for node in nodes:
                switch = session.query(Switch).filter(Switch.sn == node.switch_sn).first()
                if switch:
                    sn_list.append(node.switch_sn)
                else:
                    sn_delete_list.append(node.switch_sn)

            for sn in sn_delete_list:
                session.query(CampusSiteNodes).filter(CampusSiteNodes.switch_sn == sn).delete()

            res_data = {
                "name": template_name,
                "scope": {
                    "switches": len(sn_list),
                    "switchList": sn_list
                },
                "switch": config_data.get("switch", {}),
                "management": config_data.get("management", {}),
            }
    return jsonify({
        "status": 200,
        "data": res_data
    })


@campus_blueprint_mold.route("/switch_templates/copy", methods=["POST"])
@admin_permission.require(http_exception=403)
def switch_template_copy():
    data = request.get_json()
    template_id = data.get("id")
    new_template_name = data.get("name")
    create_time = data.get("timestamp", "")
    session = inven_db.get_session()
    with session.begin():
        original_template = session.query(CampusTopologyConfig).filter(CampusTopologyConfig.id == template_id).first()
        if original_template:
            new_template = CampusTopologyConfig()
            # 将原始对象的属性复制到新对象（除了主键和自动生成的字段）
            for key in original_template.__table__.columns.keys():
                if key != 'id':
                    setattr(new_template, key, getattr(original_template, key))
            new_template.topology_name = new_template_name
            if create_time:
                new_template.create_time = datetime.strptime(create_time, "%Y-%m-%d_%H:%M:%S")
                new_template.modified_time = datetime.strptime(create_time, "%Y-%m-%d_%H:%M:%S")
            else:
                current_time = datetime.now().replace(microsecond=0)
                new_template.create_time = current_time
                new_template.modified_time = current_time
            session.add(new_template)
            session.commit()
            return jsonify({
                "status": 200,
                "msg": "Copy successful."
            })
        else:
            return jsonify({
                "status": 500,
                "msg": "Copy failed."
            })


@campus_blueprint_mold.route("/switch_templates/get_deploy_status_list", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_deploy_status_list():
    data = request.get_json()
    template_id = data.get("id")
    session = inven_db.get_session()

    # 子查询 stc：取出 task_type='template' 且 task_id=template_id 的所有任务
    stc = (
        session.query(
            ConfigDistributionTaskForCampus.id,
            ConfigDistributionTaskForCampus.sn,
            ConfigDistributionTaskForCampus.task_status,
            ConfigDistributionTaskForCampus.create_time
        )
        .filter(
            ConfigDistributionTaskForCampus.task_type == "template",
            ConfigDistributionTaskForCampus.task_id == template_id
        )
        .subquery("stc")
    )

    # 主查询：三表 INNER JOIN + 与子查询 stc INNER JOIN
    pre_query = (
        session.query(
            Switch.platform_model,
            Switch.sn,
            Switch.reachable_status,
            Switch.mgt_ip,
            Site.site_name,
            stc.c.task_status,
            stc.c.create_time,
            stc.c.id.label("log_id")
        )
        .join(AssociationSite, Switch.id == AssociationSite.switch_id)
        .join(Site, AssociationSite.site_id == Site.id)
        .join(stc, Switch.sn == stc.c.sn)
    )

    # 处理排序
    sortFields = data.get("sortFields")
    for sort_f in sortFields:
        if sort_f.get("field") == "model":
            if sort_f["order"] == "desc":
                pre_query = pre_query.order_by(desc(Switch.platform_model))
            elif sort_f["order"] == "asc":
                pre_query = pre_query.order_by(Switch.platform_model)
        if sort_f.get("field") == "sn":
            if sort_f["order"] == "desc":
                pre_query = pre_query.order_by(desc(Switch.sn))
            elif sort_f["order"] == "asc":
                pre_query = pre_query.order_by(Switch.sn)
        if sort_f.get("field") == "mgt_ip":
            if sort_f["order"] == "desc":
                pre_query = pre_query.order_by(desc(Switch.mgt_ip))
            elif sort_f["order"] == "asc":
                pre_query = pre_query.order_by(Switch.mgt_ip)
        if sort_f.get("field") == "site":
            if sort_f["order"] == "desc":
                pre_query = pre_query.order_by(desc(Site.site_name))
            elif sort_f["order"] == "asc":
                pre_query = pre_query.order_by(Site.site_name)
        if sort_f.get("field") == "status":
            if sort_f["order"] == "desc":
                pre_query = pre_query.order_by(desc(stc.c.task_status))
            elif sort_f["order"] == "asc":
                pre_query = pre_query.order_by(stc.c.task_status)
        if sort_f.get("field") == "create_time":
            if sort_f["order"] == "desc":
                pre_query = pre_query.order_by(desc(stc.c.create_time))
            elif sort_f["order"] == "asc":
                pre_query = pre_query.order_by(stc.c.create_time)
    data["sortFields"] = []

    # 处理单个属性的搜索
    conditions = []
    filterFields = data.get("filterFields")
    for filter_f in filterFields:
        if filter_f.get("filters"):
            search_value = filter_f.get("filters")[0].get("value")
            if filter_f.get("field") == "model":
                conditions.append(Switch.platform_model.contains(search_value))
            if filter_f.get("field") == "sn":
                conditions.append(Switch.sn.contains(search_value))
            if filter_f.get("field") == "mgt_ip":
                conditions.append(Switch.mgt_ip.contains(search_value))
            if filter_f.get("field") == "site":
                conditions.append(Site.site_name.contains(search_value))
    data["filterFields"] = []
    pre_query = pre_query.filter(*conditions)

    # 处理全局搜索
    searchFields = data.get("searchFields")
    global_search_value = searchFields.get("value")
    if global_search_value:
        pre_query = pre_query.filter(
            or_(
                Switch.platform_model.contains(global_search_value),
                Switch.sn.contains(global_search_value),
                Switch.mgt_ip.contains(global_search_value),
                Site.site_name.contains(global_search_value)
            )
        )
    data["searchFields"] = []

    page_num, page_size, total_count, query_nodes = utils.query_helper(Switch, pre_query=pre_query, data=data)
    res = []
    for node in query_nodes:
        info = {
            "model": node.platform_model,
            "sn": node.sn,
            "reachable_status": node.reachable_status,
            "mgt_ip": node.mgt_ip,
            "site": node.site_name,
            "status": node.task_status,
            "create_time": node.create_time.strftime('%Y-%m-%d %H:%M:%S'),
            "log_id": node.log_id
        }
        res.append(info)
    return jsonify({
        "data": res,
        "page": page_num,
        "pageSize": page_size,
        "total": total_count,
        "status": 200
    })


@campus_blueprint_mold.route("/switch_templates/get_deploy_status_log", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_deploy_status_log():
    id = int(request.args.get('log_id'))
    session = inven_db.get_session()
    task = session.query(ConfigDistributionTaskForCampus).filter(ConfigDistributionTaskForCampus.id == id).first()
    log_info = task.task_traceback_info
    return jsonify({
        "status": 200,
        "log_info": log_info
    })


@campus_blueprint_mold.route("/switch_templates/download_template", methods=["GET"])
@admin_permission.require(http_exception=403)
def download_template():
    id = int(request.args.get('id'))
    session = inven_db.get_session()
    template = session.query(CampusTopologyConfig).filter(CampusTopologyConfig.id == id).first()
    template_name = template.topology_name
    config_data = template.configuration

    # 删除default的port
    port_config = (config_data or {}).get("switch", {}).get("portConfiguration", [])
    for i in range(len(port_config) - 1, -1, -1):
        if str(port_config[i].get("name", "")).lower() == "default":
            del port_config[i]

    # 写入内存中，而非磁盘（前端可直接下载）
    json_bytes = BytesIO()
    json_bytes.write(json.dumps(config_data, ensure_ascii=False, indent=4).encode('utf-8'))
    json_bytes.seek(0)

    return send_file(
        json_bytes,
        as_attachment=True,
        download_name=f'{template_name}.json',
        mimetype='application/json'
    )


@campus_blueprint_mold.route("/switch_templates/import_template", methods=["POST"])
@admin_permission.require(http_exception=403)
def import_template():
    file = request.files.get('json_file')
    template_name = request.form.get('template_name')
    if not file:
        return jsonify({
            "status": 400,
            "msg": "No file uploaded"
        })

    try:
        config_data = json.load(file)
        status, error_str = check_import_template(config_data)
        if not status:
            return jsonify({
                "status": 400,
                "msg": f"{error_str}"
            })
        session = inven_db.get_session()
        with session.begin():
            new_template = CampusTopologyConfig(
                site_id=1,
                site_name="default",
                topology_name=template_name,
                type="template",
                configuration=config_data
            )
            session.add(new_template)

        return jsonify({
            "status": 200,
            "msg": "JSON uploaded and saved to DB",
            "id": new_template.id
        })
    except Exception as e:
        return jsonify({
            "status": 500,
            "msg": f"Failed to process file: {e}"
        })

@campus_blueprint_mold.route("/switch_templates/delete_template", methods=["DELETE"])
@admin_permission.require(http_exception=403)
def delete_template():
    delete_id = int(request.args.get('id'))
    session = inven_db.get_session()

    task_log = session.query(ConfigDistributionTaskForCampus).filter(
        ConfigDistributionTaskForCampus.task_id == str(delete_id),
        ConfigDistributionTaskForCampus.task_type == "template"
    ).filter(
        or_(
            ConfigDistributionTaskForCampus.task_status == 1,
            ConfigDistributionTaskForCampus.task_status == 0,
        )
    ).first()
    if task_log:
        return jsonify({
            "status": 400,
            "msg": "Template configuration task is currently running and cannot be deleted!"
        })

    with session.begin():
        session.query(CampusSiteNodes).filter(
            CampusSiteNodes.id == delete_id,
            CampusSiteNodes.type == "template"
        ).delete()
        session.query(ConfigDistributionTaskForCampus).filter(
            ConfigDistributionTaskForCampus.task_id == str(delete_id),
            ConfigDistributionTaskForCampus.task_type == "template"
        ).delete()
        session.query(CampusTopologyConfig).filter(
            CampusTopologyConfig.id == delete_id
        ).delete()
    return jsonify({
        "status": 200,
        "msg": "Delete successfully."
    })

@campus_blueprint_mold.route("/switch_templates/check_duplicate_name", methods=["POST"])
@admin_permission.require(http_exception=403)
def check_duplicate_name():
    data = request.get_json()
    name = data.get("name")
    id = data.get("id", "")
    with inven_db.get_session() as session:
        if id:
            view_template = session.query(CampusTopologyConfig).filter(
                CampusTopologyConfig.id == id,
                CampusTopologyConfig.topology_name == name,
                CampusTopologyConfig.type == "template"
            ).first()
            if view_template:
                return jsonify({
                    'status': 200,
                    'info': 'Success.'
                })
        add_or_update_template = session.query(CampusTopologyConfig).filter(
            CampusTopologyConfig.topology_name == name,
            CampusTopologyConfig.type == "template"
        ).first()
        if add_or_update_template:
            return jsonify({
                'status': 400,
                'info': 'The template name already exists!'
            })
        else:
            return jsonify({
                'status': 200,
                'info': 'Success.'
            })


@campus_blueprint_mold.route("/overlay/get_dropdown_box_data", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_dropdown_box_data():
    data = request.get_json()
    search_value = data.get("search_value", "")
    with inven_db.get_session() as session:
        conditions = []
        if search_value:
            conditions.append(CampusTopologyConfig.topology_name.contains(search_value))
        config_list = session.query(CampusTopologyConfig).filter(
            CampusTopologyConfig.type == "ip-clos",
            *conditions
        ).order_by(desc(CampusTopologyConfig.create_time)).all()
        res = []
        for config in config_list:
            info = {
                "id": config.id,
                "name": config.topology_name,
            }
            res.append(info)
        return jsonify({
            "status": 200,
            "data": res,
            "msg": "success"
        })







