import datetime
import traceback
from datetime import timezone
import logging
import os
import platform
import sys
import re
import zlib
import uuid
import ast

import flask_login
from flask import Blueprint, render_template, request, jsonify, Response, json, send_from_directory, current_app
from sqlalchemy import or_, and_, case, desc

from server import constants
from server.db.models.inventory import Switch, SystemConfig, SwitchConfigBackup, SwitchParking, License, \
    SwitchSystemInfo, TaskStatus, GroupTask, SwitchLog, AssociationGroup, SwitchAutoConfig, SwitchConfigSnapshot, \
    TaskLock, Group, SwitchImage
from server.db.models.user import User, user_db
from server.db.models.inventory import inven_db
from server.db.models.sdn_access import sdn_access_db, SdnAccessSwitch
from server.db.models.vtep import vtep_db, VtepControlSwitch
from server.db.models.automation import Switch<PERSON>om<PERSON><PERSON><PERSON>, automation_db, SwitchCommonJobLog
from server.service import upgrade_switch as upgrade_service
from server.ansible_lib.pica_lic import pica8_license
from server.util import http_client
from server.util import osutil
from server.util import ssh_util as conn_client, utils
from server.util.file_helper import get_range, get_type, partial_response
from server.util.permission import super_user_permission, super_admin_permission, admin_permission, readonly_permission
from server.util.ssh_util import is_picos_v_need_to_skip_add_license
from server import cfg
from server.celery_app.automation_task import AmpConBaseTask, beat_task, AutomationTask
from celery_app import my_celery_app
from celery_app.celeryconfig import beat_dburi
from celery_app.utils.models import PeriodicTask
from celery_app.utils.schedulers import session_manager
from server.south_api.ssh_api import get_hardware_id, delay_enable_gnmi
from server.license_check.license_check import licensechecker

if platform.system() != 'Windows':
    from server.collect.rma_collect import collect_backup_config_single, upload_rollback_config_paramiko
    from server.collect.single_collect_common import collect_import_switches
    from server.collect.single_collect_common import collect_import_linux_server

new_rma_mold = Blueprint('new_rma_mold', __name__, template_folder='templates')
LOG = logging.getLogger(__name__)
config_gen = 'config_gen'
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'


# @rma_mold.route('/upload/ip', methods=['POST'])
# @admin_permission.require(http_exception=403)
# @utils.operation_log(method='upload_excel_ips', contents='upload excel file to import switches')
# def ips_upload():
#     db_session = inven_db.get_session()
#     switch_regs = db_session.query(Switch).all()
#     parking_lots = db_session.query(SwitchParking).all()
    
#     filename = request.files['excelFile'].filename
    
#     if not filename:
#         msg = {'status': '400', 'info': 'No file uploaded'}
#         return jsonify(msg)
    
#     extension = filename.split(".")[1]
#     content = request.files['excelFile'].read()
#     if sys.version_info[0] > 2:
#         content = content.decode('utf-8')
    
#     sheet = p.get_sheet(file_type=extension, file_content=content)
#     system_config = db_session.query(SystemConfig).first()
#     if not system_config:
#         msg = {'status': '400', 'info': 'Please configure System Management first'}
#         return jsonify(msg)
#     user = system_config.switch_op_user
#     pw = system_config.switch_op_password
    
#     switch_reg_ips = [switch.mgt_ip for switch in switch_regs]
#     parking_lot_ips = [parking_lot.ip for parking_lot in parking_lots]
#     added = []
#     existed_deployed = []
#     existed_parking = []
#     for re in sheet:
#         ip = str(re[0])
#         if 'ip' not in ip and ip:
            
#             if ip in switch_reg_ips:
#                 existed_deployed.append(ip)
#                 continue
            
#             if ip in parking_lot_ips:
#                 existed_parking.append(ip)
#                 continue
            
#             # outer_switch = OuterSwitch()
#             # outer_switch.status = constants.RMA_UNKNOWN
#             # outer_switch.ip = ip
#             # db.insert(outer_switch, db_session)
#             added.append(ip)
    
#     collect_import_switches.delay(added, user, pw, celery_task_name=f"collect_switch_info_{str(uuid.uuid4())}")
#     return jsonify({'added': added, 'existed_switches': existed_deployed,
#                     'existed_parking': existed_parking})


# @rma_mold.route('/manual/<string:ip>')
# @admin_permission.require(http_exception=403)
# @utils.operation_log(method='manual_upload_ip', contents='manual upload switch ip:{ip}')
# def ip_manual(ip):
#     db_session = inven_db.get_session()
#     switch = db_session.query(Switch).filter(Switch.mgt_ip == ip).first()
#     parking_lot = db_session.query(SwitchParking).filter(SwitchParking.ip == ip).first()
    
#     if switch:
#         msg = {'status': '400', 'info': 'Ip %s already exists in switch' % ip}
#         return jsonify(msg)
    
#     if parking_lot:
#         msg = {'status': '400', 'info': 'Ip %s already exists in parking lot' % ip}
#         return jsonify(msg)
    
#     # collect switch info
#     system_config = db_session.query(SystemConfig).first()
#     if not system_config:
#         msg = {'status': '500', 'info': 'Please configure System Management first'}
#         return jsonify(msg)
#     user = system_config.switch_op_user
#     pw = system_config.switch_op_password
    
#     res = collect_import_switches([ip], user, pw)
#     if res[ip] == 'ok':
#         msg = {'status': '200', 'info': 'Add ip %s is success' % ip}
#     else:
#         msg = {'status': '500', 'info': 'Add ip %s failed %s' % (ip, res[ip])}
#     return jsonify(msg)


@my_celery_app.task(name="enable_switch_vpn", base=AmpConBaseTask)
def enable_switch_vpn(ip, system_config_name, **kwargs):
    user, pw = utils.get_switch_default_user(system_config_name=system_config_name)
    res, code = conn_client.interactive_shell_linux('sudo mkdir /home/<USER>',
                                                    ip, username=user, password=pw)
    
    # sync auto-deploy.conf
    remote_config_path = '/opt/auto-deploy/auto-deploy.conf'
    res, code = conn_client.interactive_push_file(ip, user, pw, 'agent/auto-deploy.conf',
                                                  remote_config_path)
    if code != constants.RMA_ACTIVE:
        return {'status': 500, 'info': 'Sync auto-deploy.conf fail!'}
    
    remote_script_path = '/home/<USER>/enable_switch_vpn.sh'
    res, code = conn_client.interactive_push_file(ip, user, pw, 'agent/enable_switch_vpn.sh',
                                                  remote_script_path)
    if code != constants.RMA_ACTIVE:
        return {'status': 500, 'info': 'Switch download enable vpn script fail!'}
    res, code = conn_client.interactive_shell_linux('sudo chmod +x %s' % remote_script_path,
                                                    ip, username=user, password=pw)
    if code != constants.RMA_ACTIVE:
        return {'status': 500, 'info': 'Execute enable vpn script fail!'}
    res, code = conn_client.interactive_shell_linux('bash %s %s' % (remote_script_path, cfg.CONF.global_ip),
                                                    ip, username=user, password=pw, timeout=180)
    if code != constants.RMA_ACTIVE:
        return {'status': 500, 'info': 'Execute enable vpn script fail!'}


@my_celery_app.task(name="enable_switch_without_vpn", base=AmpConBaseTask)
def enable_switch_without_vpn(ip, system_config_name, **kwargs):
    user, pw = utils.get_switch_default_user(system_config_name=system_config_name)
    conn_client.interactive_shell_linux('sudo mkdir /home/<USER>', ip, username=user, password=pw)

    sn, code = conn_client.interactive_shell_linux('cat /sys/class/swmon/hwinfo/serial_number',
                                                   ip, username=user, password=pw)
    if code != constants.RMA_ACTIVE:
        return {'status': 500, 'info': 'Get Switch sn fail!'}
    import_switch_without_vpn(sn, ip)


@new_rma_mold.route('/manual/vpn/import/<string:ip>', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='import_vpn_switch', contents='import vpn switch ip:{ip}')
def import_vpn_switch(ip):
    param = request.get_json()
    used_system_config_name = param.get('systemConfigName')
    switch_group_list = param.get('group', [])

    if not used_system_config_name:
        return jsonify({'status': 400, 'info': 'System config name cannot be empty!'})

    used_system_config = inven_db.get_system_config_by_config_name(used_system_config_name)

    if not used_system_config:
        return jsonify({'status': 400, 'info': 'Selected system config cannot be found!'})

    user, pw = utils.get_switch_default_user(system_config_name=used_system_config_name)

    if not user:
        return jsonify({'status': 400, 'info': 'Selected system config cannot be found!'})
    
    sn_str, code = conn_client.interactive_shell_cli('show system serial-number | no-more',
                                                     ip, username=user, password=pw)
    
    if code != constants.RMA_ACTIVE:
        return jsonify({'status': 500, 'info': 'Cannot connect to switch'})

    is_picos_v = is_picos_v_need_to_skip_add_license(ip, user, pw)
    hw_id = get_hardware_id(ip, user, pw)
    if not is_picos_v:
        ret = licensechecker.check_hwid([hw_id])
        if ret["status"] != 200:
            LOG.error('The switch hardware_id not in license')
            return jsonify({'status': 400, 'info': 'The switch hardware_id not in license'})

    if "fabric" in param.keys():
        fabric = param['fabric']
    else:
        fabric = "default"

    if "site" in param.keys():
        site = param['site']
    else:
        site = "default"
    
    sn = re.findall("MotherBoard Serial Number : (.*)", sn_str)[0].strip()

    db_session = inven_db.get_session()
    switch = db_session.query(Switch).filter(Switch.sn == sn).first()
    parking_lot = db_session.query(SwitchParking).filter(SwitchParking.sn == sn).first()
    
    if switch:
        msg = {'status': 400, 'info': 'Sn %s already exists in switch' % sn}
        return jsonify(msg)
    
    if parking_lot:
        msg = {'status': 400, 'info': 'Sn %s already exists in parking lot' % sn}
        return jsonify(msg)
    
    new_switch = Switch(sn=sn, status=constants.SwitchStatus.IMPORTED, import_type=constants.ImportType.IMPORT,
                        reachable_status=constants.REACHABLE, system_config_id=used_system_config.id, is_picos_v=is_picos_v)
    db_session.add(new_switch)
    db_session.flush()

    # update association_group mapping
    if switch_group_list:
        inven_db.update_association_group(new_switch.sn, switch_group_list)
    
    if cfg.CONF.vpn_enable:
        from server.vpn.vpn_utils import create_vpn_client
        try:
            create_vpn_client(sn)
        except Exception as e:
            inven_db.delete_model(Switch, 'sn', sn)
            return jsonify({'status': 500, 'info': 'Import failed %s!' % str(e)})
        enable_switch_vpn.delay(ip, used_system_config_name, celery_sn=sn,
                                celery_task_name=f"enable_switch_vpn_{sn}")
    else:
        enable_switch_without_vpn.delay(ip, used_system_config_name, celery_sn=sn,
                                        celery_task_name=f"enable_switch_without_vpn_{sn}")

    inven_db.update_switch_montior(sn, 1)

    save_site_result, save_site_result_msg = utils.save_switch_to_site(site, [sn], [])
    if not save_site_result:
        return jsonify({'status': 500, 'info': save_site_result_msg})

    save_fabric_result, save_fabric_result_msg = utils.save_switch_to_fabric(fabric, [sn], [])
    if not save_fabric_result:
        return jsonify({'status': 500, 'info': save_fabric_result_msg})

    return jsonify({'status': 200, 'info': 'Import success!'})


@new_rma_mold.route('/manual/vpn/<string:sn>', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='manual_upload_vpn_sn', contents='manual upload switch sn:{sn}')
def adopt_switch(sn):
    param = request.get_json()
    switch_group_list = param.get('group', [])
    db_session = inven_db.get_session()
    switch = db_session.query(Switch).filter(Switch.sn == sn).first()
    parking_lot = db_session.query(SwitchParking).filter(SwitchParking.sn == sn).first()

    if switch:
        msg = {'status': 400, 'info': 'Sn %s already exists in switch' % sn}
        return jsonify(msg)
    
    if parking_lot:
        msg = {'status': 400, 'info': 'Sn %s already exists in parking lot' % sn}
        return jsonify(msg)
    
    new_switch = Switch(sn=sn, status=constants.SwitchStatus.IMPORTED, import_type=constants.ImportType.IMPORT,
                        reachable_status=constants.UN_REACHABLE)
    db_session.add(new_switch)

    # update association_group mapping
    
    if switch_group_list:
        inven_db.update_association_group(new_switch.sn, switch_group_list)

    db_session.flush()
    from server.vpn.vpn_utils import create_vpn_client
    try:
        create_vpn_client(sn)
    except Exception as e:
        inven_db.delete_model(Switch, 'sn', sn)
        return jsonify({'status': 500, 'info': 'Import failed %s!' % str(e)})
    
    return jsonify({'status': 200, 'info': 'Import success!'})


def set_switch_agent_conf(host, sn):
    user, pw = utils.get_switch_default_user(sn=sn)
    with conn_client.open_interactive_connection(host, username=user, password=pw) as client:
        client.execute('sudo sed -i "s/^enable.*$/enable = True/g" %s' % constants.AGENT_CONF)
        client.execute('sudo sed -i "s/^vpn_enable.*$/vpn_enable = True/g" %s' % constants.AGENT_CONF)
        client.execute('sudo mkdir /home/<USER>')
        client.execute('sudo chmod 777 /home/<USER>')
        client.execute('sudo rm -f %s' % constants.FLAG_FILE)
        client.execute('sudo echo deployed > %s' % constants.FLAG_FILE)


@new_rma_mold.route('/vpn/<string:sn>/<string:vpn_ip>')
def import_switch_vpn_ok(sn, vpn_ip):
    # collect switch info
    db_session = inven_db.get_session()
    switch = db_session.query(Switch).filter(Switch.sn == sn).first()
    if not switch:
        return Response('No switch Found', status=500)

    system_config = inven_db.get_system_config_by_sn(sn)
    if not system_config:
        return Response('Please configure System Management first', status=500)
    
    user = system_config.switch_op_user
    pw = system_config.switch_op_password
    
    is_picos_v = is_picos_v_need_to_skip_add_license(vpn_ip, user, pw)
    hw_id = get_hardware_id(vpn_ip, user, pw)
    if not is_picos_v:
        ret = licensechecker.check_hwid([hw_id])
        if ret["status"] != 200:
            LOG.error('The switch hardware_id not in license')
            return jsonify({'status': 400, 'info': 'The switch hardware_id not in license'})

    res = collect_import_switches([vpn_ip], user, pw)
    if res[vpn_ip] != 'ok':
        return Response('Add switch failed', status=500)
    
    collect_backup_config_single(vpn_ip, sn)
    # set switch agent conf
    set_switch_agent_conf(vpn_ip, sn)
    
    switch = db_session.query(Switch).filter(Switch.sn == sn).first()
    if switch not in constants.BUSY_BOX_MODEL_LIST:

        delay_enable_gnmi(vpn_ip, username=user, password=pw, delay=120)
        inven_db.update_switch_montior(sn, 1)
    
    return 'Add switch success'


def import_switch_without_vpn(sn, mgt_ip):
    # collect switch info
    db_session = inven_db.get_session()
    switch = db_session.query(Switch).filter(Switch.sn == sn).first()
    if not switch:
        return Response('No switch Found', status=500)

    system_config = inven_db.get_system_config_by_sn(sn)
    if not system_config:
        return Response('Please configure System Management first', status=500)
    
    user = system_config.switch_op_user
    pw = system_config.switch_op_password
    
    res = collect_import_switches([mgt_ip], user, pw)
    if res[mgt_ip] != 'ok':
        return Response('Add switch failed', status=500)
    
    collect_backup_config_single(mgt_ip, sn)
    # set switch agent conf
    user, pw = utils.get_switch_default_user(sn=sn)
    with conn_client.open_interactive_connection(mgt_ip, username=user, password=pw) as client:
        client.execute('sudo mkdir /home/<USER>')
        client.execute('sudo chmod 777 /home/<USER>')
        client.execute('sudo rm -f %s' % constants.FLAG_FILE)
        client.execute('sudo echo deployed > %s' % constants.FLAG_FILE)
    return 'Add switch success'


# @rma_mold.route('/')
# @rma_mold.route('')
# @admin_permission.require(http_exception=403)
# def rma():
#     active = ('lifecycle', 'rma_decom')
#     models = utils.get_support_models()
#     all_group = utils.get_user_group().all()
#     is_group_user = utils.is_group_user()
#     return render_template('lifecycle/display_rma.html', models=models, active=active, vpn_enable=cfg.CONF.vpn_enable, all_group=all_group, is_group_user=is_group_user)


# @rma_mold.route('/display_inventory')
# @admin_permission.require(http_exception=403)
# def display_decom():
#     active = ('lifecycle', 'show_inventory')
#     db_session = inven_db.get_session()
#     db_session.begin()
#     switch_inventory = utils.query_switch().filter(
#         Switch.status.in_(['DECOM', 'DECOM-Init', 'DECOM-Manual'])).order_by(
#         Switch.modified_time.desc()).all()
#     return render_template("lifecycle/display_inventory.html", switch_tables=switch_inventory, active=active)


# @rma_mold.route('/deploy_switch/list')
# @utils.user_req
# def list_deploy_switch(user):
#     status_filter = Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS])
#     res = switch_data(request.args, extra_filter=[status_filter, Switch.import_type == constants.ImportType.DEPLOY])
#     # switch_regs = db.get_collection(Switch, filters={'status': [constants.PROVISIONING_SUCCESS]})
#     switch_regs = res.get('data')
    
#     display_config = """<a style="font-size: small" href="javascript:void(0);" data-toggle="modal"
#                                     data-target="#model_config" onclick="show_config(this, 'deploy')">
#                                     <span class="fa fa-cog"></span>
#                                Display Config &nbsp;</a>
#                                """
#     upgrade = """<a style="font-size: small" href="javascript:void(0);" data-toggle="modal"
#                               data-target="#upgrade" onclick="save_row_data(this, 'deploy')">
#                               <span class="fa fa-level-up"></span>
#                            Upgrade &nbsp;</a>"""
    
#     if user.type == 'readonly':
#         operations = display_config
#     else:
#         operations = """<a style="font-size: small" href="javascript:void(0);" onclick="dcom(this)">
#                             <span class="fa fa-stop-circle"></span>
#                        DECOM &nbsp;</a>
#                        <a style="font-size: small" href="javascript:void(0);" data-toggle="modal"
#                             data-target="#rma_configs" onclick="save_row_data(this, 'deploy')">
#                             <span class="fa fa-map-signs"></span>
#                        RMA &nbsp;</a>
#                        <a style="font-size: small" href="javascript:void(0);" data-toggle="modal"
#                             data-target="#model_config" onclick="show_config(this, 'deploy')">
#                             <span class="fa fa-cog"></span>
#                        Display Config &nbsp;</a>
#                        <a style="font-size: small" href="javascript:void(0);"
#                           onclick="upload_config(this, 'deploy')">
#                           <span class="fa fa-upload"></span>
#                        Upload Config&nbsp;</a>
#                        <a style="font-size: small" href="javascript:void(0);" data-toggle="modal"
#                                 data-target="#retrieve_config"
#                           onclick="backup(this, 'deploy')">
#                           <span class="fa fa-download"></span>
#                        Backup Config &nbsp;</a>
#                        """
    
#     switch_re = []
#     for switch_reg in switch_regs:
#         deploy_oper = operations
#         switch_dict = switch_reg.make_dict()
#         switch_config_backup = inven_db.get_model(SwitchConfigBackup, filters={'ip': [switch_dict['mgt_ip']]})
#         if switch_config_backup:
#             if switch_config_backup.back_up_type == constants.SWITCH_BACK_MANUAL:
#                 switch_dict['flag'] = 'U'
#             elif switch_config_backup.back_up_type == constants.SWITCH_BACK_AUTO:
#                 switch_dict['flag'] = 'R'
            
#             switch_dict['backup_time'] = switch_config_backup.modified_time
#         else:
#             switch_dict['flag'] = 'unknown'
#             switch_dict['backup_time'] = '---'
        
#         if 'rma_status' not in switch_dict:
#             switch_dict['status'] = 'unknown'
#         elif switch_dict['rma_status'] == constants.RMA_UNKNOWN:
#             switch_dict['status'] = 'unknown'
#         elif switch_dict['rma_status'] == constants.RMA_UN_REACHABLE:
#             switch_dict['status'] = 'unreachable'
#         elif switch_dict['rma_status'] == constants.RMA_FAILED:
#             switch_dict['status'] = 'failed'
#         elif switch_dict['rma_status'] == constants.RMA_ACTIVE:
#             switch_dict['status'] = 'active'
#         elif switch_dict['rma_status'] == constants.RMA_UPGRADING:
#             switch_dict['status'] = 'upgrading'
#         else:
#             switch_dict['status'] = 'unknown'
        
#         if switch_reg.upgrade_status == constants.outswitch_status.UPGRADED:
#             switch_dict['upgrade_status'] = 'upgraded'
#         elif switch_reg.upgrade_status == constants.outswitch_status.UPGRADING:
#             switch_dict['upgrade_status'] = 'upgrading'
#             deploy_oper = display_config
#         elif switch_reg.upgrade_status == constants.outswitch_status.NO_UPGRADE:
#             switch_dict['upgrade_status'] = 'not-upgraded'
#         elif switch_reg.upgrade_status == constants.outswitch_status.UPGRADE_FAILED:
#             switch_dict['upgrade_status'] = 'upgrade failed'
        
#         switch_dict['version'] = switch_dict.get('version', '') + '/' + switch_dict.get('revision', '')
#         if switch_dict.get('revision', '') != switch_reg.switch_model.up_to_date_version.split('/')[1] \
#                 and switch_reg.upgrade_status != constants.outswitch_status.UPGRADING:
#             deploy_oper += upgrade
#         switch_dict['operations'] = deploy_oper
#         switch_re.append(switch_dict)
    
#     return json.dumps(
#         {'data': switch_re, 'recordsTotal': res.get('recordsTotal'), 'recordsFiltered': res.get('recordsFiltered')},
#         default=str)


# @rma_mold.route('/import_switch/list')
# @utils.user_req
# def list_import_switch(user):
#     # switch_imps = db.get_collection(OuterSwitch)
#     res = switch_data(request.args, extra_filter=[Switch.sn != constants.PICOS_V_SN,
#                                                   Switch.import_type == constants.ImportType.IMPORT])
#     switch_imps = res.get('data')
    
#     display_config = """<a style="font-size: small" href="javascript:void(0);" data-toggle="modal"
#                                         data-target="#model_config" onclick="show_config(this, 'import')">
#                                         <span class=" fa fa-cog"></span>
#                                    Display Config &nbsp;</a>
#                                    """
#     if user.type == 'readonly':
#         operations = display_config
#         rma = decom = upgrade = remove = retrieve = ''
#     else:
#         operations = """  <a style="font-size: small" href="javascript:void(0);" data-toggle="modal"
#                                 data-target="#model_config" onclick="show_config(this, 'import')" title="Show the Config currently running on switch">
#                                 <span class=" fa fa-cog"></span>
#                            Display Config &nbsp;</a>
#                            <a style="font-size: small" href="javascript:void(0);"
#                               onclick="upload_config(this, 'import')" title="Upload a new config file to push to switch">
#                               <span class="fa  fa-upload"></span>
#                            Upload Config&nbsp;</a>
#                            """
#         rma = """<a style="font-size: small" href="javascript:void(0);" data-toggle="modal"
#                                     data-target="#rma_configs_imported" onclick="show_rma_model(this, 'import')">
#                                     <span class="fa fa-map-signs"></span>
#                                RMA &nbsp;</a>"""
        
#         decom = """<a style="font-size: small" href="javascript:void(0);" onclick="dcom(this)">
#                                 <span class="fa fa-stop-circle"></span>
#                            DECOM &nbsp;</a>"""
        
#         upgrade = """<a style="font-size: small" href="javascript:void(0);" data-toggle="modal"
#                                   data-target="#upgrade" onclick="save_row_data(this, 'import')" title="Upgrade PicOS image on switch to current AmpCon version">
#                                   <span class="fa fa-level-up"></span>
#                                Upgrade &nbsp;</a> """
        
#         log = """<a style="font-size: small" href="javascript:void(0);" data-toggle="modal"
#                     data-target = "#log_detail" onclick = "show_log_import(this, 'import', false)">
#                     <span class="fa fa-archive"></span>
#                  Log &nbsp;</a>"""
#         remove = """<a style="font-size: small" href="javascript:void(0);"
#                                   onclick="del_switch(this, 'import')" title="Delete switch from database">
#                                   <span class="fa fa-trash"></span>
#                                Remove &nbsp;</a>"""
#         retrieve = """<a style="font-size: small" href="javascript:void(0);" data-toggle="modal"
#                                     data-target="#retrieve_config" onclick="backup(this, 'import')" title="Backup a snapshot of the config running on the switch">
#                                   <span class="fa fa-download"></span>
#                                Backup Config &nbsp;</a>"""
    
#     switch_re = []
#     session = inven_db.get_session()
#     for switch_reg in switch_imps:
#         if not switch_reg.platform_model:
#             switch_re.append({
#                 'flag': 'unknown',
#                 'backup_time': '---',
#                 'status': 'unreachable',
#                 'sn': switch_reg.sn,
#                 'version': '',
#                 'upgrade_status': '---',
#                 'operations': remove,
#                 'mgt_ip': ''
#             })
#             continue
#         sw_platform = inven_db.get_model(SwitchSystemInfo, filters={'model': [switch_reg.platform_model]})
#         switch_dict = switch_reg.make_dict()
        
#         # switch is in TaskStatus
#         task = session.query(TaskStatus).filter(TaskStatus.name.like('%::' + switch_dict['sn'] + '%')).first()
        
#         switch_config_backup = inven_db.get_model(SwitchConfigBackup, filters={'sn': [switch_dict['sn']]},
#                                                   session=session)
#         if switch_config_backup:
#             if switch_config_backup.back_up_type == constants.SWITCH_BACK_MANUAL:
#                 switch_dict['flag'] = 'U'
#             elif switch_config_backup.back_up_type == constants.SWITCH_BACK_AUTO:
#                 switch_dict['flag'] = 'R'
#             switch_dict['backup_time'] = switch_config_backup.modified_time
#         else:
#             switch_dict['flag'] = 'unknown'
#             switch_dict['backup_time'] = '---'
        
#         if switch_dict['reachable_status'] == constants.UNKNOWN:
#             switch_dict['status'] = 'unknown'
#         elif switch_dict['reachable_status'] == constants.UN_REACHABLE:
#             switch_dict['status'] = 'unreachable'
#         elif switch_dict['reachable_status'] == constants.REACHABLE:
#             switch_dict['status'] = 'active'
#         else:
#             switch_dict['status'] = 'unknown'
        
#         switch_dict['sn'] = switch_reg.sn or ''
#         switch_dict['version'] = switch_dict.get('version', '') + '/' + switch_dict.get('revision', '')
#         import_oper = operations
#         if switch_reg.upgrade_status == constants.outswitch_status.UPGRADED:
#             import_oper = operations + log
#             switch_dict['upgrade_status'] = 'upgraded'
#             if 'DECOM' not in switch_reg.sn:
#                 import_oper += decom
#         elif switch_reg.upgrade_status == constants.outswitch_status.UPGRADING:
#             switch_dict['upgrade_status'] = 'upgrading'
#             import_oper = display_config + log
#         elif switch_reg.upgrade_status == constants.outswitch_status.NO_UPGRADE:
#             switch_dict['upgrade_status'] = '---'
#             if not task or task.status == 'executed' or task.status == 'error':
#                 import_oper += remove
#             if switch_dict.get('version', '') == sw_platform.up_to_date_version.split('/')[0] and \
#                     'DECOM' not in switch_dict['sn']:
#                 import_oper += decom
#         elif switch_reg.upgrade_status == constants.outswitch_status.UPGRADE_FAILED:
#             switch_dict['upgrade_status'] = 'upgrade failed'
#             import_oper = operations + log
        
#         if switch_dict.get('version', '').split('/')[0] != sw_platform.up_to_date_version.split('/')[0] and \
#                 switch_reg.upgrade_status != constants.outswitch_status.UPGRADING:
#             import_oper += upgrade
#         if switch_dict.get('mgt_ip') and 'DECOM' not in switch_dict['mgt_ip']:
#             import_oper += retrieve
        
#         if 'DECOM' in switch_dict['sn']:
#             import_oper = rma + import_oper
        
#         switch_dict['operations'] = import_oper
        
#         switch_re.append(switch_dict)
#     return json.dumps(
#         {'data': switch_re, 'recordsTotal': res.get('recordsTotal'), 'recordsFiltered': res.get('recordsFiltered')},
#         default=str)


# @rma_mold.route('/backup/version/<string:ip>')
# def conform_backup_version(ip):
#     rma_config = inven_db.get_switch_back(ip)
    
#     switch = inven_db.get_model(Switch, filters={'mgt_ip': [ip]})
#     if not switch:
#         return Response('Switch with ip %s not found' % ip, status=500)
    
#     model = inven_db.get_model(SwitchSystemInfo, filters={'model': [switch.platform_model]})
#     up_to_date_version = model.up_to_date_version.split('/')[0]
    
#     if rma_config:
#         rma_config_version_re = re.findall('/*PicOS Version\s+:\s+((\d+\.){2,}\d+)', rma_config, re.I)
#         if rma_config_version_re:
#             rma_config_version = rma_config_version_re[0][0]
#             if rma_config_version != up_to_date_version:
#                 return Response('Rma backup config version is %s,'
#                                 ' not up to date,up to date version is %s' %
#                                 (Markup.escape(rma_config_version), Markup.escape(up_to_date_version)), status=500)
#             else:
#                 return 'Rma backup config version is %s,' \
#                        ' up to date,up to date version is %s' % (Markup.escape(rma_config_version), Markup.escape(up_to_date_version))
#         else:
#             return Response('Can not find backup config version from backup file', status=500)
#     else:
#         return Response('Can not find backup config', status=500)


# def switch_data(args, extra_filter=None):
#     search_value = args.get('search[value]')
#     rule = None
#     if search_value and search_value != '':
#         search_value = '%' + search_value + '%'
#         rule = or_(*[Switch.mgt_ip.like(search_value),
#                      Switch.sn.like(search_value),
#                      Switch.status.like(search_value),
#                      ])
#     return utils.page_helper(request.args, Switch, rule=rule, json_data=False, extra_filter=extra_filter)


@new_rma_mold.route('/config/<string:ip>')
def rma_config(ip):
    return inven_db.get_switch_back(ip) or 'No Configuration'


@new_rma_mold.route('/up_config', methods=['POST'])
@utils.operation_log(method='upload_rma_config', contents='upload rma config file to {ip}')
def rma_up_config():
    try:
        config_file = request.files.get('config_file')
        sn = request.form.get('sn')
        ip = request.form.get('ip')
        
        if config_file and ip:
            
            back_str = config_file.read()
            inven_db.insert_or_update_back(ip, back_str.decode(), constants.RMA_BACK_MANUAL, sn)
            
            msg = {'status': 200, 'info': 'Upload config success'}
            inven_db.add_switch_log(sn, 'Upload config success', 'warn')
        else:
            msg = {'status': 400, 'info': 'please input the file and input the ip'}
    except Exception as e:
        LOG.error(e)
        msg = {'status': 500, 'info': 'Upload config failed'}
        inven_db.add_switch_log(sn, 'Upload config failed', 'warn')
    finally:
        return jsonify(msg)


# @rma_mold.route('/backup')
# @admin_permission.require(http_exception=403)
# @utils.operation_log(method='manual_backup', contents='manual backup switch ip:{ip}')
# def backup():
#     sn = request.args.get('sn')
#     ip = request.args.get('ip')
#     # execute ansible to collect switch config
#     try:
#         if collect_backup_config_single(ip, sn) == constants.RMA_ACTIVE:
#             msg = {'status': '200', 'info': 'Back-up success'}
#         else:
#             msg = {'status': '400', 'info': 'Back-up failed'}
#     except:
#         msg = {'status': '500', 'info': 'Back-up failed'}
#     return jsonify(msg)


@new_rma_mold.route('/rollback_config', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='rollback_config', contents='manual rollback switch config sn:{sn}')
def rollback_config():
    param = request.get_json()
    sn = param['sn']
    snapshot_id = param['snapshot_id']
    commit_wait_time = int(param['commit_wait_time'])
    
    snapshot_entry = inven_db.get_model(SwitchConfigSnapshot, filters={'sn': [sn], 'id': [snapshot_id]})
    snapshot_content = snapshot_entry.archive_config.decode()
    
    switch_entry = inven_db.get_model(Switch, filters={'sn': [sn]})
    host_ip = switch_entry.mgt_ip
    
    user, pw = utils.get_switch_default_user(sn=sn)
    status, msg = upload_rollback_config_paramiko(host_ip, user, pw, snapshot_content, 240, commit_wait_time)
    
    res = {'status': 200 if status else 500, 'info': msg}
    
    return jsonify(res)


@new_rma_mold.route('/rma/do', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='do_rma', contents='do rma to switch ip:{oldIp} to new switch sn:{newSN}')
def rma_do():
    param = request.get_json()
    old_sn = param['oldSN']
    old_ip = param['oldIp']
    new_sn = param['newSN']
    staged = param['staged']
    
    new_location = param.get('location', None)
    new_host_name = param.get('hostName', None)
    new_platform = param.get('platform', None)
    new_domain = param.get('domain', None)
    new_system_config_name = param.get('systemConfigName', constants.GLOBAL_CONFIG_TAG)
    new_system_config = inven_db.get_system_config_by_config_name(new_system_config_name)
    if not new_system_config:
        return jsonify({'status': 400, 'info': 'System Config %s not found' % new_system_config_name})

    if old_sn == new_sn:
        return jsonify({'status': 400, 'info': 'New sn number is same as original'})
    
    if 'RMA' in old_sn:
        return jsonify({'status': 400, 'info': 'Switch have already did rma, can not do again'})
    
    db_session = inven_db.get_session()
    with db_session.begin():
        
        switch_new = inven_db.get_model(Switch, filters={'sn': [new_sn]}, session=db_session)
        if switch_new:
            msg = {'status': 400, 'info': 'The new sn is already exist'}
            return jsonify(msg)
        
        rma_backup = inven_db.get_model(SwitchConfigBackup, filters={'sn': [old_sn]}, session=db_session)
        if not rma_backup:
            msg = {'status': 400, 'info': 'Please back up or upload config the switch '}
            return jsonify(msg)
        
        golden_snapshot = inven_db.get_model(SwitchConfigSnapshot, filters={'sn': [old_sn], 'tag': ['GOLDEN_CONFIG']},
                                             session=db_session)
        
        if old_ip != '':
            pure_ip = re.split(r'[_(]', old_ip)[0]
            if "DECOM" in old_ip or "RMA" in old_ip:
                rma_back_check = inven_db.get_model(SwitchConfigBackup, filters={'ip': [pure_ip]}, session=db_session)
                if rma_back_check:
                    msg = {'status': 400, 'info': 'The ip is used by switch %s , please check ' % rma_back_check.sn}
                    return jsonify(msg)
        else:
            pure_ip = ''
        
        # change old switch
        old_switch = inven_db.get_model(Switch, filters={'sn': [old_sn]}, session=db_session)
        if not old_switch:
            return jsonify({'status': 400, 'info': 'Switch can not found'})
        
        # need to delete the entry in switch
        inven_db.delete_collection(Switch, filters={'sn': [old_sn + '_RMA']}, session=db_session)
        inven_db.delete_collection(SwitchConfigBackup, filters={'sn': [old_sn + '_RMA']}, session=db_session)
        inven_db.delete_collection(License, filters={'sn_num': [old_sn]}, session=db_session)
        old_switch.sn = old_sn + '_RMA'
        if old_ip != '':
            old_switch.mgt_ip = old_ip + '(RMA)'
        old_switch.status = constants.SwitchStatus.RMA
        rma_backup.sn = rma_backup.sn + '_RMA'
        if old_ip != '':
            rma_backup.ip = old_ip + '(RMA)'
        else:
            rma_backup.ip = old_sn + '(RMA)'
        
        # create new switch
        rma_switch = Switch()
        rma_switch.sn = new_sn
        rma_switch.mgt_ip = pure_ip
        rma_switch.step = 0
        rma_switch.import_type = constants.ImportType.RMA
        rma_switch.system_config_id = new_system_config.id
        if staged:
            rma_switch.status = constants.SwitchStatus.STAGED
            rma_switch.enable = True
        
        else:
            rma_switch.status = constants.SwitchStatus.CONFIGURED
            rma_switch.enable = False
        rma_switch.platform_model = new_platform if new_platform else old_switch.platform_model
        rma_switch.version = old_switch.version
        rma_switch.revision = old_switch.revision.split('-')[0]
        rma_switch.topology = old_switch.topology
        rma_switch.address = new_location if new_location else old_switch.address
        rma_switch.current_user = old_switch.current_user
        rma_switch.current_password = old_switch.current_password
        rma_switch.host_name = new_host_name if new_host_name else old_switch.host_name
        rma_switch.domain = new_domain if new_domain else old_switch.domain
        inven_db.insert_or_update(rma_switch, primary_key='sn', session=db_session)
        
        # create new backup
        rma_backup_new = SwitchConfigBackup()
        rma_backup_new.sn = new_sn
        rma_backup_new.ip = pure_ip
        # use golden config if exist
        if golden_snapshot:
            rma_backup_new.config = zlib.compress(golden_snapshot.archive_config)
        else:
            rma_backup_new.config = rma_backup.config
        rma_backup_new.back_up_type = constants.RMA_BACK_MANUAL
        inven_db.insert_or_update(rma_backup_new, primary_key='sn', session=db_session)
        
        # send <NAME_EMAIL>
        title = 'automation rma configured'
        msg = 'old sn %s, new sn %s' % (old_sn, new_sn)
        utils.send_email(title, msg, [constants.RMA_EMAIL])
        
        inven_db.delete_collection(SwitchParking, filters={'sn': [new_sn]}, session=db_session)
        msg = {'status': 200, 'info': 'RMA Success'}
        
        db_session.query(AssociationGroup).filter(AssociationGroup.switch_sn == old_sn).delete()
        db_session.query(SwitchLog).filter(SwitchLog.switch_id == old_sn).update({SwitchLog.switch_id: old_sn + '_RMA'},
                                                                                 synchronize_session=False)
        db_session.query(SwitchAutoConfig).filter(SwitchAutoConfig.name == old_sn + '_site_config').update({
            SwitchAutoConfig.name: old_sn + '_RMA_site_config'
        })
        inven_db.update_switch_montior(new_sn, 1)

    save_site_result, save_site_result_msg = utils.save_switch_to_site('default', [new_sn], [])
    if not save_site_result:
        return jsonify({'status': 500, 'info': save_site_result_msg})

    save_fabric_result, save_fabric_result_msg = utils.save_switch_to_fabric('default', [new_sn], [])
    if not save_fabric_result:
        return jsonify({'status': 500, 'info': save_fabric_result_msg})

    if staged:
        # generate vpn key
        from server.vpn.vpn_utils import create_vpn_client
        try:
            create_vpn_client(new_sn)
        except Exception as e:
            return jsonify({'status': 500, 'info': 'Fail to generate vpn key %s.' % str(e)})
    return jsonify(msg)


@new_rma_mold.route('/dcom', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='dcom', contents='dcom switch sn:{sn}')
def dcom():
    param = request.get_json()
    sn = param['sn']
    switch = inven_db.get_model(Switch, filters={'sn': [sn]})
    if not switch:
        return jsonify({'status': 400, 'info': 'Can not find switch %s' % sn})
    
    # check mac_vlan or vtep controller
    host_switch = sdn_access_db.get_model(SdnAccessSwitch, filters={'sn': [sn]})
    if host_switch:
        return jsonify({'status': 400, 'info': 'The switch %s is in sdn access app, please delete first' % sn})
    
    vtep_switch = vtep_db.get_model(VtepControlSwitch, filters={'sn': [sn]})
    if vtep_switch:
        return jsonify({'status': 400, 'info': 'The switch %s is in vtep manage app, please delete first' % sn})
    
    # switch is upgrading
    jobs = AmpConBaseTask.get_running_jobs()
    if jobs:
        for job in jobs:
            if sn in job.task_name:
                return jsonify({'status': 400, 'info': 'Switch is in upgrade task, can not decom'})
    
    if switch.upgrade_status == constants.outswitch_status.UPGRADING:
        # return Response('switch is upgrading, can not dcom', status=400)
        return jsonify({'status': 400, 'info': 'Switch is upgrading, can not decom'})
    
    if not switch.mgt_ip:
        host = switch.tmp_ip
    else:
        host = switch.mgt_ip
    
    session = inven_db.get_session()
    decom_num = inven_db.get_next_decom_num(sn, session=session)
    decom_ip_num = inven_db.get_next_decom_ip_num(host)
    
    if decom_num >= 100:
        return jsonify({'status': 400, 'info': 'More than 100 decom times, please delete some records'})
    if decom_ip_num >= 100:
        return jsonify({'status': 400, 'info': 'More than 100 decom times, please delete some records'})
    
    # decom the license in the portal, otherwise just quit.
    license_key = inven_db.get_switch_local_license(sn)
    if not license_key:
        pica8_license_instance = pica8_license(sn=sn)
        step, status, _ = pica8_license_instance.license_exists(switch.hwid)
        if step == 0 and status and not pica8_license_instance.license_decom(switch.hwid):
            raise ValueError('can not decom the license in license portal')
    
    cmd = 'sudo /opt/auto-deploy/restore_pica8_config.sh'
    user, password = utils.get_switch_default_user(sn=sn)
    content, status = conn_client.interactive_shell_linux(cmd, host, username=user, password=password)
    if status == constants.RMA_ACTIVE:
        msg = {'info': 'DECOM success', 'status': 200}
        switch.status = constants.SwitchStatus.DECOM
        LOG.warning('%s DECOM success' % sn)
        inven_db.add_switch_log(sn, 'DECOM success', 'warn')
    
    elif status == constants.RMA_UN_REACHABLE:
        msg = {'info': 'cannot connect to switch, please check', 'status': 500}
        switch.status = constants.SwitchStatus.DECOM_MANUAL
        LOG.warning('%s cannot connect to switch, please check', sn)
        inven_db.add_switch_log(sn, 'cannot connect to switch, please check', 'warn')
    else:
        msg = {'info': 'DECOM failed, please manually initialize the switch', 'status': 500}
        switch.status = constants.SwitchStatus.DECOM_MANUAL
        LOG.warning('%s DECOM-PENDING and indicate you must manually run restore_pica8_config' % sn)
        inven_db.add_switch_log(sn, 'DECOM-PENDING and indicate you must manually run restore_pica8_config', 'warn')
    
    inven_db.delete_collection(License, filters={'sn_num': [sn]})

    session.query(SwitchCommonJob).filter(SwitchCommonJob.switch_sn == sn).delete()
    
    num = '_' + str(decom_num) if decom_num > 0 else ''
    ip_num = '_' + str(decom_ip_num) if decom_ip_num > 0 else ''
    new_sn = sn + '_DECOM' + num
    new_ip = switch.mgt_ip + '_DECOM' + ip_num if switch.mgt_ip else new_sn
    switch.mgt_ip = new_ip
    switch.sn = new_sn
    inven_db.merge(switch)
    
    inven_db.update_model(SwitchConfigBackup, filters={'sn': [sn], 'ip': [host]}, updates={
        SwitchConfigBackup.sn: new_sn, SwitchConfigBackup.ip: new_ip
    })

    session.query(SwitchLog).filter(SwitchLog.switch_id == sn).update({SwitchLog.switch_id: new_sn},
                                                                      synchronize_session=False)
    session.query(SwitchAutoConfig).filter(SwitchAutoConfig.name == sn + '_site_config').update({
        SwitchAutoConfig.name: new_sn + '_site_config'
    })
    if session.query(AssociationGroup).filter(AssociationGroup.switch_sn == sn).first():
        inven_db.update_model(AssociationGroup, filters={'switch_sn': [sn]}, updates={
            AssociationGroup.switch_sn: new_sn
        })
    return jsonify(msg)


@new_rma_mold.route('/switch/schedule/upgrade', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='/switch/schedule/upgrade',
                     contents='schedule upgrade task at {start_date}')
def switch_schedule_upgrade():
    try:
        info = request.form
        image_name = info.get('imageName')
        sn_list = info.getlist('sn')
        start_date = info.get('startDate')
        # end_date = info['end_date']

        errors = []
        success_sns = []

        with inven_db.get_session() as session:
            upgrading_switch_list = session.query(Switch).filter(and_(Switch.sn.in_(sn_list), Switch.upgrade_status == constants.outswitch_status.UPGRADING)).all()
            if upgrading_switch_list:
                return jsonify({'status': 400, 'info': f'Switches {", ".join([s.sn for s in upgrading_switch_list])} are already upgrading'})

            switch_image_info = session.query(SwitchImage).filter_by(image_name=image_name).first()
            if not switch_image_info:
                return jsonify({'status': 404, 'info': f'Image {image_name} not found'})

            current_timestamp = int(datetime.datetime.now(timezone.utc).timestamp())

            start_timestamp =int(datetime.datetime.strptime(start_date, "%Y-%m-%d %H:%M").timestamp())

            # end_datestamp = int(datetime.datetime.strptime(end_date, "%Y-%m-%d %H:%M").timestamp())

            if start_timestamp < current_timestamp:
                return jsonify({'status': 400, 'info': 'start time must be later than now'})

            # time_interval = abs(end_datestamp - start_datestamp)
            #
            # if (time_interval < 1800):
            #     return jsonify({'status': 400,
            #                     'info': 'The time interval between the start time and the end time cannot be less than 30 minutes'})

            for sn in sn_list:
                switch = inven_db.get_switch_info_by_sn(sn)
                running_task = AmpConBaseTask.get_running_job_by_sn(sn)
                if running_task:
                    return jsonify(
                        {'status': 500, 'info': 'upgrade in other task [%s]' % running_task.first().task_name})

                if switch.upgrade_status == constants.outswitch_status.UPGRADING:
                    return jsonify({'status': 500, 'info': 'Switch is upgrading'})

                if switch.revision == switch_image_info.revision:
                    return jsonify({'status': 500, 'info': 'Switch version is current'})

            files = request.files
            file_list = request.files.getlist('file')
            for sn in sn_list:
                try:
                    switch = inven_db.get_switch_info_by_sn(sn)

                    script_parent_dir = os.path.join(constants.UPGRADE_TMP_SCRIPTS, sn)
                    osutil.ensure_path(script_parent_dir)

                    have_need_file = False
                    sources = []
                    tmp_files = {}
                    if files:
                        for script in file_list:
                            if script.filename != '' and script.filename not in tmp_files:
                                LOG.info('save script %s', script)
                                if constants.POST_XORPLUS == script.filename:
                                    have_need_file = True
                                path = script_parent_dir + '/' + script.filename
                                if os.path.exists(path):
                                    os.remove(path)
                                script.save(path)
                                sources.append({'filename': script.filename, 'path': path, 'dest': path})
                                tmp_files[script.filename] = path
                    else:
                        have_need_file = True

                    if not have_need_file and tmp_files:
                        return jsonify({'status': 500, 'info': '%s must be existed' % constants.POST_XORPLUS})

                    LOG.info('schedule upgrade files %s', files)

                    system_config = inven_db.get_system_config_by_sn(sn)
                    user, password = system_config.switch_op_user, system_config.switch_op_password

                    switch_platform = switch.switch_model
                    mgt_ip = switch.mgt_ip

                    task_name = f'schedule_upgrade::{sn}'

                    job_name = f"{start_timestamp}-{sn}-switch_upgrade"

                    beat_task.add_job(task_name, 'start_upgrade_service', args=(mgt_ip, user, password, '',
                                                                                '', sn, switch_platform.model,
                                                                                tmp_files,
                                                                                task_name, job_name), once=True,
                                      start_time=start_date,
                                      expires=None,
                                      job_desc="automation upgrade switch once",
                                      kwargs={'celery_sn': sn, "schedule_type": "CRONTAB", "celery_group": "", 'image_name': image_name})

                    success_sns.append(sn)

                    # modify Switch upgrade status scheduled
                    automation_db.add_switch_common_job(sn, job_name, "switch_upgrade", start_timestamp, None,
                                                        'scheduled',
                                                        f"{sn} switch will be upgraded at {start_date} with image {image_name}",
                                                        switch_image_info.version, switch_image_info.revision)
                    session.query(Switch).filter(Switch.sn == sn).update({Switch.upgrade_status: constants.outswitch_status.SCHEDULED})

                except Exception as e:
                    errors.append(f'SN {sn} error: {str(e)}')
                    LOG.error("Failed to schedule SN %s", sn, exc_info=True)

        return jsonify({
            'status': 200 if not errors else 207,
            'info': f'Scheduled {len(success_sns)} switches',
            'details': errors
        })
    except Exception as e:
        LOG.critical("API crash: %s", str(e), exc_info=True)
        return jsonify({'status': 500, 'info': 'Internal server error'})



# @rma_mold.route('/group/upgrade', methods=['POST'])
# @admin_permission.require(http_exception=403)
# @utils.operation_log(method='/group/upgrade',
#                      contents='schedule upgrade task for group {name} from {start_date} to {end_date}')
# def group_image_upgrade():
#     info = request.form
#     name = info['name']
#
#     switches = inven_db.get_group_switchs_new(name)
#     session = inven_db.get_session()
#     group_task = session.query(GroupTask).filter(GroupTask.name == name,
#                                                  GroupTask.type == 'upgrade',
#                                                  GroupTask.status != 'finished').first()
#     if group_task:
#         return jsonify({'status': 400, 'message': '- group is already scheduled'})
#
#     files = request.files
#     script_parent_dir = constants.UPGRADE_TMP_SCRIPTS + 'group_' + name
#     osutil.ensure_path(script_parent_dir)
#     tmp_files = {}
#     have_need_file = False
#     sources = []
#     if files:
#         for script in files.values():
#             if script.filename != '' and script.filename not in tmp_files:
#                 LOG.info('save script %s', script)
#                 if constants.POST_XORPLUS == script.filename:
#                     have_need_file = True
#                 path = script_parent_dir + '/' + script.filename
#                 if os.path.exists(path):
#                     os.remove(path)
#                 script.save(path)
#                 sources.append({'filename': script.filename, 'path': path, 'dest': path})
#                 tmp_files[script.filename] = path
#     else:
#         have_need_file = True
#
#     if not have_need_file and tmp_files:
#         return Response('%s must be existed' % constants.POST_XORPLUS, status=400)
#
#     if sources:
#         # push files to sync server
#         http_client.start_transfer_file(flask_login.current_user.id, sources)
#
#     # clear previous group tasks status
#     if session.query(AutomationTask).filter(AutomationTask.task_name.like('%group_upgrade::' + name + '%'),
#                                             AutomationTask.task_status == "running").first():
#         return jsonify({'status': 400, 'message': '- group task already running'})
#     session.query(AutomationTask).filter(AutomationTask.task_name.like('%group_upgrade::' + name + '%')).delete(
#         synchronize_session=False)
#     beat_task.remove_like_job('group_upgrade::' + name)
#     session.query(GroupTask).filter(GroupTask.name == name, GroupTask.type == 'upgrade').delete(
#         synchronize_session=False)
#     msgs = {}
#     start_date = end_date = None
#     for switch in switches:
#         sn = switch.sn
#         task_name = 'group_upgrade::%s::%s' % (name, sn)
#
#         system_config = inven_db.get_system_config_by_sn(sn)
#         user, password = system_config.switch_op_user, system_config.switch_op_password
#
#         switch_platform = switch.switch_model
#         mgt_ip = switch.mgt_ip
#
#         if 'start_date' in info:
#             start_date = info['start_date']
#             start_date = datetime.datetime.strptime(start_date, DATE_FORMAT)
#         else:
#             start_date = datetime.datetime.now(timezone.utc).replace(tzinfo=None)
#         if 'end_date' in info:
#             end_date = info['end_date']
#             end_date = datetime.datetime.strptime(end_date, DATE_FORMAT)
#         else:
#             end_date = datetime.datetime.now(timezone.utc).replace(tzinfo=None) + datetime.timedelta(days=2)
#
#         if start_date < datetime.datetime.now():
#             return jsonify({'status': 400, 'message': '- start time must be later than now'})
#
#         if end_date < start_date:
#             return jsonify({'status': 400, 'message': '- end time must be later than start time'})
#
#         beat_task.add_job(task_name, 'start_upgrade_service', args=(mgt_ip, user, password, '',
#                                                                     '', sn, switch_platform.model, tmp_files,
#                                                                     task_name), once=True,
#                           start_time=start_date, expires=end_date,
#                           kwargs={'celery_sn': sn, "schedule_type": "CRONTAB", "celery_group": name})
#         msgs[sn] = 'add task ok'
#         # start_date += datetime.timedelta(minutes=3)
#
#     msgs['status'] = 200
#     inven_db.insert(GroupTask(name=name, start_date=start_date, end_date=end_date, status='Not-start'))
#     return jsonify(msgs)


# @rma_mold.route('/group/<string:name>/<string:task_type>/upgrade/run')
# @admin_permission.require(http_exception=403)
# @utils.operation_log(method='group/<name>/<task_type>/upgrade/run', contents='start upgrade task for group {name}')
# def group_upgrade_run(name, task_type):
#     now = datetime.datetime.now(timezone.utc).replace(tzinfo=None)
#     group = inven_db.get_model(GroupTask, filters={'name': [name], 'type': [task_type]})
#     if group.end_date < now:
#         return Response('group task is expired', status=400)
    
#     switches = inven_db.get_group_switchs_new(name)
    
#     inven_db.update_model(GroupTask, filters={'name': [name], 'type': [task_type]},
#                           updates={GroupTask.status: 'started', GroupTask.start_date: now})
#     try:
#         task_list = list()
#         for switch in switches:
#             task_name = '%s::%s::%s' % (constants.GROUP_TYPE_DICT[task_type], name, switch.sn)
#             job = beat_task.get_job_by_name(task_name)
#             if not isinstance(job, str):
#                 if now > job.expires:
#                     break
#                 task_list.append(task_name)
#         beat_task.run_jobs(task_list)
#                 # now += datetime.timedelta(minutes=3)
#     except Exception as e:
#         return jsonify({'status': 500, 'message': str(e)})
    
#     return jsonify({'status': 200, 'message': 'Group task run now success'})


# @rma_mold.route('/group/<string:name>/<string:task_type>/upgrade/hold')
# @admin_permission.require(http_exception=403)
# @utils.operation_log(method='group/<name>/<task_type>/upgrade/hold', contents='hold upgrade task for group {name}')
# def group_upgrade_hold(name, task_type):
#     switches = inven_db.get_group_switchs_new(name)
#     inven_db.update_model(GroupTask, filters={'name': [name], 'type': [task_type]}, updates={GroupTask.status: 'paused'})
#     try:
#         for switch in switches:
#             task_name = '%s::%s::%s' % (constants.GROUP_TYPE_DICT[task_type], name, switch.sn)
#             job = beat_task.get_job_by_name(task_name)
#             if not isinstance(job, str):
#                 beat_task.pause_job(task_name)
#                 AmpConBaseTask.kill_process_by_sn(switch.sn)
#     except Exception as e:
#         return jsonify({'status': 500, 'message': str(e)})
    
#     return jsonify({'status': 200, 'message': 'Group task hold now success'})


# @rma_mold.route('/group/<string:name>/<string:task_type>/upgrade/status')
# @admin_permission.require(http_exception=403)
# def group_upgrade_status(name, task_type):
#     switches = inven_db.get_group_switchs_new(name)
#     task_names = ['%s::%s::%s' % (constants.GROUP_TYPE_DICT[task_type], name, switch.sn.replace("_DECOM", "")) for switch in switches]
    
#     session = inven_db.get_session()
#     automation_task = session.query(AutomationTask)
#     tasks_info = automation_task.filter(AutomationTask.task_name.in_(task_names)).all()
#     executed_num = automation_task.filter(AutomationTask.task_name.in_(task_names),
#                                           AutomationTask.task_status == 'success').group_by(
#         AutomationTask.task_name).count()
#     running_num = automation_task.filter(AutomationTask.task_name.in_(task_names),
#                                          AutomationTask.task_status == 'running').group_by(
#         AutomationTask.task_name).count()
#     error_num = automation_task.filter(AutomationTask.task_name.in_(task_names),
#                                        AutomationTask.task_status == 'failure').group_by(
#         AutomationTask.task_name).count()
#     group_status = session.query(GroupTask).filter(GroupTask.name == name, GroupTask.type == task_type).first().status
    
#     tmp_task = dict()
#     for task_info in tasks_info:
#         if task_info.task_name not in tmp_task:
#             tmp_task[task_info.task_name] = {'name': task_info.task_name,
#                                              'start_date': task_info.create_time.strftime(DATE_FORMAT),
#                                              'end_date': task_info.modified_time.strftime(DATE_FORMAT),
#                                              'status': task_info.task_status}
#         else:
#             if task_info.create_time > datetime.datetime.strptime(tmp_task[task_info.task_name]['start_date'],
#                                                                   DATE_FORMAT):
#                 tmp_task[task_info.task_name] = {'name': task_info.task_name,
#                                                  'start_date': task_info.create_time.strftime(DATE_FORMAT),
#                                                  'end_date': task_info.modified_time.strftime(DATE_FORMAT),
#                                                  'status': task_info.task_status}
#     task_status_dicts = [i for i in tmp_task.values()]
    
#     marked_task_names = [task.task_name for task in tasks_info]
#     missed_task_names = set(task_names) - set(marked_task_names)
#     for miss_task_name in missed_task_names:
#         if group_status != 'paused':
#             task_status_dicts.append({'name': miss_task_name, 'start_date': '', 'end_date': '', 'status': 'missed'})
#         else:
#             task_status_dicts.append({'name': miss_task_name, 'start_date': '', 'end_date': '', 'status': 'paused'})
    
#     missed_num = len(missed_task_names)
#     return jsonify({'total': len(task_names),
#                     'success': executed_num, 'running': running_num,
#                     'error': error_num, 'missed': missed_num, 'group_status': group_status,
#                     'tasks': task_status_dicts})


@new_rma_mold.route('/upgrade', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='/upgrade', contents='start upgrade task for switch {sn}')
def upgrade_switch():
    info = request.form
    sn = info['sn']
    task_name = 'upgrade::%s' % sn

    running_task = AmpConBaseTask.get_running_job_by_sn(sn)
    if running_task:
        return jsonify({'status': 500, 'info': 'Upgrade in other task [%s]' % running_task.first().task_name})
    
    files = request.files
    file_list = request.files.getlist('file')
    script_parent_dir = constants.UPGRADE_TMP_SCRIPTS + sn
    osutil.ensure_path(script_parent_dir)
    tmp_files = {}
    have_need_file = False
    sources = []
    if files:
        for script in file_list:
            if script.filename != '' and script.filename not in tmp_files:
                LOG.info('save script %s', script)
                if constants.POST_XORPLUS == script.filename:
                    have_need_file = True
                path = script_parent_dir + '/' + script.filename
                if os.path.exists(path):
                    os.remove(path)
                script.save(path)
                sources.append({'filename': script.filename, 'path': path, 'dest': path})
                tmp_files[script.filename] = path
    else:
        have_need_file = True

    if not have_need_file and tmp_files:
        return jsonify({'status': 500, 'info': '%s must be existed' % constants.POST_XORPLUS})
    
    if sources:
        # push files to sync server
        http_client.start_transfer_file(flask_login.current_user.id, sources)
    
    LOG.info('upgrade files %s', files)
    switch = inven_db.get_switch_info_by_sn(sn)
    switch_platform = switch.switch_model
    mgt_ip = switch.mgt_ip
    
    if switch.revision == switch_platform.up_to_date_version.split('/')[1]:
        return jsonify({'status': 500, 'info': 'Switch version is current'})

    system_config = inven_db.get_system_config_by_sn(sn)
    user, password = system_config.switch_op_user, system_config.switch_op_password
    
    if switch.upgrade_status == constants.outswitch_status.UPGRADING:
        return jsonify({'status': 500, 'info': 'Switch is upgrading'})

    start_timestamp = int(datetime.datetime.now(timezone.utc).timestamp())
    job_name = f"{start_timestamp}-{sn}-switch_upgrade"

    upgrade_service.start.delay(mgt_ip, user, password, '', '', sn, switch_platform.model, tmp_files, task_name, job_name, celery_sn=sn, celery_task_name=task_name)
    switch.upgrade_status = constants.outswitch_status.UPGRADING
    inven_db.merge(switch)
    return jsonify({'status': 200, 'info': 'Upgrade job has been deployed'})


@new_rma_mold.route('/batch_upgrade', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='/batch_upgrade', contents='start upgrade task for switch {sn}')
def batch_upgrade_switch():
    info = request.form
    image_name = info.get('imageName')
    sn_list = info.getlist('sn')

    switch_image_info = inven_db.get_session().query(SwitchImage).filter(SwitchImage.image_name == image_name).first()
    if not switch_image_info:
        return jsonify({'status': 500, 'info': 'Cannot found image named [%s]' % image_name})

    for sn in sn_list:
        switch = inven_db.get_switch_info_by_sn(sn)
        running_task = AmpConBaseTask.get_running_job_by_sn(sn)
        if running_task:
            return jsonify({'status': 500, 'info': 'Upgrade in other task [%s]' % running_task.first().task_name})

        if switch.upgrade_status == constants.outswitch_status.UPGRADING:
            return jsonify({'status': 500, 'info': 'Switch is upgrading'})

        if switch.revision == switch_image_info.revision:
            return jsonify({'status': 500, 'info': 'Switch version is current'})

    files = request.files
    file_list = request.files.getlist('file')
    for sn in sn_list:
        script_parent_dir = constants.UPGRADE_TMP_SCRIPTS + sn
        osutil.ensure_path(script_parent_dir)
        tmp_files = {}
        have_need_file = False
        sources = []
        if files:
            for script in file_list:
                if script.filename != '' and script.filename not in tmp_files:
                    LOG.info('save script %s', script)
                    if constants.POST_XORPLUS == script.filename:
                        have_need_file = True
                    path = script_parent_dir + '/' + script.filename
                    if os.path.exists(path):
                        os.remove(path)
                    script.save(path)
                    sources.append({'filename': script.filename, 'path': path, 'dest': path})
                    tmp_files[script.filename] = path
        else:
            have_need_file = True

        if not have_need_file and tmp_files:
            return jsonify({'status': 500, 'info': '%s must be existed' % constants.POST_XORPLUS})

        LOG.info('upgrade files %s', files)
        switch = inven_db.get_switch_info_by_sn(sn)
        switch_platform = switch.switch_model
        mgt_ip = switch.mgt_ip

        system_config = inven_db.get_system_config_by_sn(sn)
        user, password = system_config.switch_op_user, system_config.switch_op_password

        task_name = 'upgrade::%s' % str(sn)

        start_timestamp = int(datetime.datetime.now(timezone.utc).timestamp())
        start_date = datetime.datetime.fromtimestamp(start_timestamp).strftime("%Y-%m-%d %H:%M")
        job_name = f"{start_timestamp}-{sn}-switch_upgrade"

        automation_db.add_switch_common_job(sn, job_name, "switch_upgrade", start_timestamp, None, 'running',
                                            f"{sn} switch will upgrade at {start_date} with image {image_name}",
                                            switch_image_info.version, switch_image_info.revision)

        upgrade_service.start.delay(mgt_ip, user, password, '', '', sn, switch_platform.model, tmp_files, task_name, job_name, image_name=image_name, celery_sn=sn, celery_task_name=task_name)
        switch.upgrade_status = constants.outswitch_status.UPGRADING
        inven_db.merge(switch)
    return jsonify({'status': 200, 'info': 'Batch upgrade job has been deployed'})


@new_rma_mold.route('/check_image', methods=['POST'])
@admin_permission.require(http_exception=403)
def check_image():
    info = json.loads(request.data)
    image_name = info.get('imageName', '')
    sn = info.get('sn', '')
    if not sn:
        return jsonify({'status': 500, 'info': 'SN is required'})
    switch = inven_db.get_switch_info_by_sn(sn)
    switch_platform = switch.switch_model
    mgt_ip = switch.mgt_ip

    system_config = inven_db.get_system_config_by_sn(sn)
    user, password = system_config.switch_op_user, system_config.switch_op_password

    try:
        us = upgrade_service.UpgradeService(mgt_ip, user, password, '', '', sn, switch_platform.model, [], image_name=image_name)
        us.check_image_by_size()
    except ValueError as v:
        return jsonify({'status': 500, 'info': "ERROR:[%s]" % v})
    except Exception as e:
        return jsonify({'status': 500, 'info': "ERROR:[%s]" % (e)})
    image_name = os.path.basename(us.d_image_path)
    if not us.check_image_flag:
        return jsonify({'status': 500, 'info': 'The image no need to push', 'image_name': image_name})
    else:
        if us.check_image_flag == 'SAME':
            return jsonify({'status': 200, 'info': 'The image exist, no need to push', 'image_name': image_name})
        else:
            return jsonify({'status': 200, 'info': 'The image not exist or integrity, need to push', 'image_name': image_name})


@new_rma_mold.route('/batch_push_image', methods=['POST'])
@admin_permission.require(http_exception=403)
def batch_push_image():
    info = request.form
    image_name = info.get('imageName', '')
    sn_list = info.getlist('sn')
    if not sn_list:
        return jsonify({'status': 500, 'info': 'SN is required'})

    for sn in sn_list:
        task_name = 'push_image::%s' % sn
        job = AmpConBaseTask.get_running_job_by_task_name(task_name)
        if job:
            return jsonify({'status': 500, 'info': 'Upgrade in other task [%s]' % task_name})

    for sn in sn_list:
        system_config = inven_db.get_system_config_by_sn(sn)
        user, password = system_config.switch_op_user, system_config.switch_op_password

        switch = inven_db.get_switch_info_by_sn(sn)
        switch_platform = switch.switch_model
        mgt_ip = switch.mgt_ip
        task_name = 'push_image::%s' % sn
        upgrade_service.push_image.delay(mgt_ip, user, password, '',
                                         '', sn, switch_platform.model, [], task_name, False, image_name=image_name,
                                         celery_task_name=task_name,
                                         celery_sn=sn)

    return jsonify({'status': 200, 'info': 'Push image scheduled success'})


@new_rma_mold.route('/push_image', methods=['POST'])
@admin_permission.require(http_exception=403)
def push_image():
    info = json.loads(request.data)
    image_name = info.get('imageName', '')
    sn = info.get('sn', '')
    if not sn:
        return jsonify({'status': 500, 'info': 'SN is required'})

    system_config = inven_db.get_system_config_by_sn(sn)
    user, password = system_config.switch_op_user, system_config.switch_op_password

    switch = inven_db.get_switch_info_by_sn(sn)
    switch_platform = switch.switch_model
    mgt_ip = switch.mgt_ip

    task_name = 'push_image::%s' % sn
    job = AmpConBaseTask.get_running_job_by_task_name(task_name)
    if job:
        return jsonify({'status': 500, 'info': 'Upgrade in other task [%s]' % task_name})
    
    upgrade_service.push_image.delay(mgt_ip, user, password, '',
                                     '', sn, switch_platform.model, [], task_name, True, image_name=image_name, celery_task_name=task_name,
                                     celery_sn=sn)
    
    return jsonify({'status': 200, 'info': 'Push image scheduled success'})


@new_rma_mold.route('/fetch_upgrade_task_table_data', methods=['POST'])
@readonly_permission.require(http_exception=403)
def fetch_upgrade_task_table_data():
    def get_state_order(state):
        return case(
            {
                "running": 1,
                "scheduled": 2,
                "failed": 3,
                "cancelled": 4,
                "success": 5
            },
            value=state,
            else_=6
        )

    try:
        session = automation_db.get_session()
        sn_list = list(map(lambda x: x.sn, utils.query_switch().all()))
        page_num, page_size, total_count, query_obj = utils.query_helper(SwitchCommonJob, pre_query=session.query(
            SwitchCommonJob).filter(SwitchCommonJob.switch_sn.in_(sn_list)), default_order_by_func=lambda model: (
            get_state_order(getattr(model, "state")),
            desc(getattr(model, "start_time"))
        ))
        response = {
            "data": [{
                "id": job_result.id,
                "switch_sn": job_result.switch_sn,
                "job_name": job_result.job_name,
                "job_type": job_result.job_type,
                "start_time": datetime.datetime.fromtimestamp(job_result.start_time).strftime(DATE_FORMAT),
                "end_time": datetime.datetime.fromtimestamp(job_result.end_time).strftime(DATE_FORMAT) if job_result.end_time else '',
                "state": job_result.state if job_result.state else '',
                "host_name": job_result.switch.host_name if job_result.switch else '',
                "mgt_ip": job_result.switch.mgt_ip if job_result.switch else '',
                "version": job_result.image_version if job_result.image_version else '',
                "revision": job_result.image_revision if job_result.image_revision else '',
                "link_ip_addr": job_result.switch.link_ip_addr if job_result.switch else '',
                "reachable_status": job_result.switch.reachable_status if job_result.switch else '',
            } for job_result in query_obj],
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        return jsonify(response)
    except Exception as e:
        return jsonify({'status': 400, 'info': 'Failed to list VPCs'})
    finally:
        if session:
            session.close()



@new_rma_mold.route('/cancel_upgrade_task', methods=['POST'])
@admin_permission.require(http_exception=403)
def cancel_upgrade_task():
    try:
        info = request.get_json()
        job_ids = info.get('jobIds', [])
        if not job_ids:
            return jsonify({'status': 400, 'info': 'Job ID is required'})

        automation_session = automation_db.get_session()
        celery_session = session_manager.session_factory(beat_dburi)

        results = []
        success_count = 0

        for job_id in job_ids:
            try:
                # 处理自动化数据库操作
                with automation_session.begin():
                    job_result = automation_session.query(SwitchCommonJob).filter(
                        SwitchCommonJob.id == job_id
                    ).first()

                    if not job_result:
                        results.append({'job_id': job_id, 'status': 404, 'info': 'Job not found'})
                        continue

                    if job_result.state not in ['scheduled']:
                        results.append({
                            'job_id': job_id,
                            'status': 400,
                            'info': f'Job is not in a cancellable state (current state: {job_result.state})'
                        })
                        continue

                    target_switch = automation_session.query(Switch).filter(
                        Switch.sn == job_result.switch_sn
                    ).first()

                    now_timestamp = int(datetime.datetime.now(timezone.utc).timestamp())
                    cancel_time = datetime.datetime.now().strftime(DATE_FORMAT)

                    job_result.state = 'cancelled'
                    job_result.end_time = now_timestamp

                    if target_switch:
                        target_switch.upgrade_status = constants.outswitch_status.NO_UPGRADE

                    job_result.job_log.append(
                        SwitchCommonJobLog(
                            log=f"{job_result.switch_sn} switch canceled the upgrade at {cancel_time}"
                        )
                    )

                # 处理 Celery 任务禁用
                with celery_session.begin():
                    celery_periodic_task_name = f"schedule_upgrade::{job_result.switch_sn}"
                    celery_job = celery_session.query(PeriodicTask).filter(
                        PeriodicTask.name == celery_periodic_task_name
                    ).first()

                    if celery_job:
                        celery_job.enabled = False
                        results.append({'job_id': job_id, 'status': 200, 'info': 'Job cancelled successfully'})
                        success_count += 1
                    else:
                        results.append({
                            'job_id': job_id,
                            'status': 404,
                            'info': 'Celery scheduled job not found'
                        })

            except Exception as e:
                # 如果自动化数据库事务尚未提交，回滚当前事务
                if automation_session.is_active:
                    automation_session.rollback()

                results.append({'job_id': job_id, 'status': 500, 'info': str(e)})

        # 检查所有任务是否都成功取消
        if success_count == len(job_ids):
            return jsonify({
                'status': 200,
                'info': 'All jobs cancelled successfully',
                'details': results
            })
        else:
            return jsonify({
                'status': 400,
                'info': f'{len(job_ids) - success_count} jobs failed to cancel',
                'details': results
            })

    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})


@new_rma_mold.route('/delete_upgrade_task', methods=['POST'])
@admin_permission.require(http_exception=403)
def delete_upgrade_task():
    try:
        info = request.get_json()
        job_ids = info.get('jobIds', [])
        if not job_ids:
            return jsonify({'status': 400, 'info': 'Job ID is required'})

        session = automation_db.get_session()
        results = []
        success_count = 0

        for job_id in job_ids:
            try:
                # 为每个任务开启事务
                with session.begin():
                    job_result = session.query(SwitchCommonJob).filter(SwitchCommonJob.id == job_id).first()

                    if not job_result:
                        results.append({'job_id': job_id, 'status': 404, 'info': 'Job not found'})
                        continue

                    if job_result.state in ['scheduled', 'running']:
                        results.append({
                            'job_id': job_id,
                            'status': 400,
                            'info': f'Job is not in a deletable state (current state: {job_result.state})'
                        })
                        continue

                    # 删除任务
                    session.query(SwitchCommonJob).filter(SwitchCommonJob.id == job_id).delete()
                    results.append({'job_id': job_id, 'status': 200, 'info': 'Job deleted successfully'})
                    success_count += 1

            except Exception as e:
                results.append({'job_id': job_id, 'status': 500, 'info': str(e)})

        # 检查所有任务是否都成功删除
        if success_count == len(job_ids):
            return jsonify({
                'status': 200,
                'info': 'All jobs deleted successfully',
                'details': results
            })
        else:
            return jsonify({
                'status': 400,
                'info': f'{len(job_ids) - success_count} jobs failed to delete',
                'details': results
            })

    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})

@new_rma_mold.route('/fetch_upgrade_task_log', methods=['POST'])
@readonly_permission.require(http_exception=403)
def fetch_upgrade_task_log():
    try:
        info = request.get_json()
        job_id = info.get('jobId', '')
        if not job_id:
            return jsonify({'status': 400, 'info': 'Job ID is required'})

        session = automation_db.get_session()

        job_result = session.query(SwitchCommonJob).filter(SwitchCommonJob.id == job_id).first()

        if not job_result:
            return jsonify({'status': 404, 'info': 'Job not found'})

        start_dt = datetime.datetime.fromtimestamp(job_result.start_time)
        end_dt = datetime.datetime.fromtimestamp(job_result.end_time) if job_result.end_time else None

        if not end_dt:
            switch_logs = session.query(SwitchLog).filter(and_(SwitchLog.switch_id == job_result.switch_sn, SwitchLog.create_time >= start_dt)).order_by(SwitchLog.create_time.asc()).all()
        else:
            switch_logs = session.query(SwitchLog).filter(and_(SwitchLog.switch_id == job_result.switch_sn, SwitchLog.create_time >= start_dt, SwitchLog.create_time <= end_dt)).order_by(SwitchLog.create_time.asc()).all()
        job_logs = job_result.job_log if job_result.job_log else []

        log_content = ''

        switch_log_index = 0
        task_log_index = 0
        while switch_log_index < len(switch_logs) or task_log_index < len(job_logs):
            if switch_log_index < len(switch_logs) and (task_log_index >= len(job_logs) or switch_logs[switch_log_index].create_time <= job_logs[task_log_index].create_time):
                log_content += f"{switch_logs[switch_log_index].create_time} - {switch_logs[switch_log_index].content}\n"
                switch_log_index += 1
            elif task_log_index < len(job_logs):
                log_content += f"{job_logs[task_log_index].create_time} - {job_logs[task_log_index].log}\n"
                task_log_index += 1

        return jsonify({'status': 200, 'logContent': log_content})
    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})


@new_rma_mold.route('/update_upgrade_task', methods=['POST'])
@admin_permission.require(http_exception=403)
def update_upgrade_task():
    try:
        info = request.get_json()
        job_ids = info.get('jobIds', [])
        if not job_ids:
            return jsonify({'status': 400, 'info': 'Job ID is required'})

        session = automation_db.get_session()
        celery_session = session_manager.session_factory(beat_dburi)

        # 存储所有任务的结果
        results = []
        success_count = 0
        start_date = info.get('start_date', '')

        # 验证开始时间是否有效
        current_timestamp = int(datetime.datetime.now(timezone.utc).timestamp())
        try:
            start_datestamp = int(datetime.datetime.strptime(start_date, "%Y-%m-%d %H:%M").timestamp())
            if start_datestamp < current_timestamp:
                return jsonify({'status': 400, 'info': 'start time must be later than now'})
        except Exception as e:
            return jsonify({'status': 400, 'info': f'Invalid start date format: {str(e)}'})

        # 为每个任务开启独立的事务
        for job_id in job_ids:
            try:
                # 为每个任务开启新的事务
                with session.begin():
                    job_result = session.query(SwitchCommonJob).filter(SwitchCommonJob.id == job_id).first()
                    if not job_result:
                        # 记录错误但不中断循环
                        results.append({'job_id': job_id, 'status': 404, 'info': 'Job not found'})
                        # 继续处理下一个任务
                        continue

                    job_result_log = session.query(SwitchCommonJobLog).filter(
                        SwitchCommonJobLog.switch_common_job_id == job_id).first()  # 修正了这里的条件

                    celery_periodic_task_name = f"schedule_upgrade::{job_result.switch_sn}"
                    celery_job = celery_session.query(PeriodicTask).filter(
                        PeriodicTask.name == celery_periodic_task_name).first()

                    if not celery_job:
                        results.append({'job_id': job_id, 'status': 404, 'info': 'Scheduled job not found'})
                        continue

                    # 更新任务
                    job_result.start_time = start_datestamp

                    args = ast.literal_eval(celery_job.args)
                    kwargs = ast.literal_eval(celery_job.kwargs)

                    # 添加新任务
                    beat_task.add_job(
                        celery_periodic_task_name,
                        'start_upgrade_service',
                        args=args,
                        once=True,
                        start_time=start_date,
                        expires=None,
                        job_desc="automation upgrade switch once",
                        kwargs=kwargs
                    )

                    if job_result_log:
                        job_result_log.log = f"{job_result.switch_sn} switch will be upgraded at {start_date} with image {kwargs.get('image_name')}"

                    results.append({'job_id': job_id, 'status': 200, 'info': 'Job updated successfully'})
                    success_count += 1

            except Exception as e:
                # 事务上下文会自动回滚
                results.append({'job_id': job_id, 'status': 500, 'info': str(e)})

        # 检查所有任务是否都成功
        if success_count == len(job_ids):
            return jsonify({
                'status': 200,
                'info': 'All jobs updated successfully',
                'details': results
            })
        else:
            return jsonify({
                'status': 400,
                'info': f'{len(job_ids) - success_count} jobs failed to update',
                'details': results
            })

    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})


@new_rma_mold.route('/upgrade_task_execute_now', methods=['POST'])
@admin_permission.require(http_exception=403)
def upgrade_task_execute_now():
    try:
        info = request.get_json()
        job_ids = info.get('jobIds', [])
        if not job_ids:
            return jsonify({'status': 400, 'info': 'Job ID is required'})

        session = automation_db.get_session()
        celery_session = session_manager.session_factory(beat_dburi)

        results = []
        errors = []

        for job_id in job_ids:
            try:
                job_result = session.query(SwitchCommonJob).filter(SwitchCommonJob.id == job_id).first()
                if not job_result:
                    errors.append(f'Job {job_id} not found')
                    continue

                switch = session.query(Switch).filter(Switch.sn == job_result.switch_sn).first()
                if not switch:
                    errors.append(f'Switch for job {job_id} not found')
                    continue

                celery_periodic_task_name = f"schedule_upgrade::{job_result.switch_sn}"
                celery_job = celery_session.query(PeriodicTask).filter(
                    PeriodicTask.name == celery_periodic_task_name
                ).first()

                if not celery_job:
                    errors.append(f'Scheduled job for job {job_id} not found')
                    continue

                celery_job.enabled = False
                celery_session.commit()

                args = ast.literal_eval(celery_job.args)
                kwargs = ast.literal_eval(celery_job.kwargs)

                with session.begin():
                    job_result.state = 'running'
                    switch.upgrade_status = constants.outswitch_status.UPGRADING

                upgrade_service.start.delay(
                    *args,
                    image_name=kwargs['image_name'],
                    celery_sn=kwargs['celery_sn'],
                    celery_task_name=args[8]
                )

                results.append(job_id)

            except Exception as e:
                errors.append(f'Error processing job {job_id}: {str(e)}')
                continue

        if errors:
            if results:
                return jsonify({
                    'status': 207,
                    'info': f'Partial success: {len(results)} jobs processed, {len(errors)} failed',
                    'successful_jobs': results,
                    'failed_jobs': errors
                })
            return jsonify({
                'status': 500,
                'info': 'All jobs failed',
                'errors': errors
            })

        return jsonify({
            'status': 200,
            'info': f'All {len(job_ids)} upgrade tasks will be executed now',
            'successful_jobs': results
        })

    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})


@new_rma_mold.route('/file/<path:filepath>')
def get_upgrade_image(filepath):
    allow_dirs = ['tmp', 'img', 'config_gen', 'license', 'agent']
    root_file_dir, _, _ = filepath.partition('/')
    if root_file_dir not in allow_dirs:
        return Response('not allowed download file', status=400)
    if 'Range' in request.headers:
        start, end = get_range(request)
        return partial_response(filepath, start, end)
    return send_from_directory(current_app.root_path, filepath, as_attachment=True)
