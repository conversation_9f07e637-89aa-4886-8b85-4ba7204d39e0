import base64
import configparser
import io
import json
import logging
import os
import platform
import re
import shutil
import ssl
import subprocess
import tarfile
import tempfile
import threading
import time
import traceback
import urllib.request
import zipfile
from datetime import datetime, timedelta, date
from typing import Dict, Optional

import flask_login
from flask import Blueprint, request, jsonify, send_file
from sqlalchemy import or_
from sqlalchemy.orm.session import make_transient

from server.common.exceptions import LicensePortalNetworkException, LicensePortalAuthException
from server.util.env_util import ampcon_pro_type

if platform.system() != 'Windows':
    from server.collect.rma_collect import collect_backup_config_single_group, collect_backup_config_single
from server.celery_app.automation_task import AmpConBaseTask, beat_task
from server.celery_app.beat_task import beat_update_db_license_count
from server.constants import AMPCON_BASE_DIR, AUTOMATION_BASE_DIR
from server import constants, is_enable_debug
from server.ansible_lib.pica_lic import pica8_license
from server.util import http_client, osutil, utils, ssh_util, str_helper, encrypt_util,wireless_util
from server.util.csp_util import csp_downloader
from server.util.utils import un_tar, get_search_models, batch_insert, is_name_valid
from server.util.encrypt_util import aes_cipher
from server.util.permission import super_admin_permission, admin_permission, super_user_permission, readonly_permission
from server import cfg
from server.db.models import inventory
from server.db.models import general
from server.db.models.monitor import monitor_db
from server.db.redis_common import RedisSessionFactory
from celery_app import my_celery_app
from server.license_check.license_check import licensechecker
from sqlalchemy.orm import joinedload

inven_db = inventory.inven_db
config_gen_root = os.path.join(AMPCON_BASE_DIR, 'config_gen')
db_backup_root = os.path.join(AMPCON_BASE_DIR, 'db_backup')
img_gen = os.path.join(AMPCON_BASE_DIR, 'img')
new_config_mold = Blueprint("new_config_mold", __name__, template_folder='templates')
key_reg = re.compile('key\s+([^\s]+)')
password_reg = re.compile('password\s+([^\s]+)')

LOG = logging.getLogger(__name__)

pre_image = "https://csp.pica8.com/sw-prod/PICOS-GA/{}"
redis_client = RedisSessionFactory.get_client()


@new_config_mold.route('/switch_model', methods=['GET'])
@readonly_permission.require(http_exception=403)
def switch_model():
    result = {'data': get_search_models(without_black_box=False), 'status': 200}
    return jsonify(result)


@new_config_mold.route('/deploy_configs_tree', methods=['POST'])
@readonly_permission.require(http_exception=403)
def deploy_configs_tree():
    session = inven_db.get_session()
    params = json.loads(request.data)
    type = params['type']

    page = int(params['page'])
    page_size = int(params['pageSize'])
    search_args = params.get('searchArgs')
    start = (page - 1) * page_size
    nodes = []
    query = session.query(inventory.SwitchAutoConfig)
    if search_args:
        query = query.filter(or_(inventory.SwitchAutoConfig.name.like('%' + search_args + '%'),
                                 inventory.SwitchAutoConfig.system_model.like('%' + search_args + '%')))
    configs_query = query.filter(inventory.SwitchAutoConfig.type == type).order_by(
        inventory.SwitchAutoConfig.name.asc())
    total = configs_query.count()
    configs = configs_query.slice(start, start + page_size).all()
    # get all model and create node
    models = []
    for config in configs:
        if config.system_model not in models:
            models.append(config.system_model)
    models_id = {}
    for index, item in enumerate(models):
        nodes.append({
            'title': item,
            'key': item,
            'open': True,
            'is_folder': True,
            'model': item,
            'children': []
        })
        models_id.update({item: index})

    for index, item in enumerate(configs, start=1):
        nodes[models_id[item.system_model]]['children'].append({
            'title': item.name,
            'key': item.name,
            'is_folder': False,
            'model': item.system_model
        })
    return jsonify(nodes)


@new_config_mold.route('/<string:name>/config_name')
def get_config_by_name(name):
    config = inven_db.get_model(inventory.SwitchAutoConfig, filters={'name': [name]})
    if config:
        config_str = config.config
        config_str = str_helper.mask_key_configuration(config_str)
    else:
        config_str = ''
    return jsonify({'data': config_str, 'status': 200})


@new_config_mold.route('/generate_config/save_config', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='save_config', contents='save {name} config')
def save_config():
    info = json.loads(request.data)
    name = info['name']
    config_str = info['config']
    model_name = info.get('modelName')
    model_type = info['modelType']
    if not is_name_valid(name):
        return jsonify({'info': 'name is invalid.', 'status': 500})

    if config_str:
        config_str = config_str.strip()

    if config_str:
        session = inven_db.get_session()
        switch_auto_config = inventory.SwitchAutoConfig()
        if model_name:
            switch_auto_config.system_model = model_name
        switch_auto_config.name = name
        switch_auto_config.type = model_type
        switch_auto_config.config = config_str
        db_config = inven_db.get_model(inventory.SwitchAutoConfig, filters={'name': [name]}, session=session)

        # SwitchYamlConfig
        yaml_config = inventory.SwitchYamlConfig()
        if model_type == 'global':
            yaml_pushconfig_dict = {'Global Config Name': name}
            yaml_config.pushconfig = str(yaml_pushconfig_dict)
        elif model_type == 'regional':
            yaml_pushconfig_dict = {'Regional Config Name': name}
            yaml_config.pushconfig = str(yaml_pushconfig_dict)

        if db_config:
            switch_auto_config.id = db_config.id
            inven_db.merge(switch_auto_config, session=session)

            # SwitchYamlConfig Setting
            yaml_config.autoconfig_id = db_config.id
            db_yaml_config = inven_db.get_model(inventory.SwitchAutoConfig, filters={'autoconfig_id': [db_config.id]},
                                                session=session)
            yaml_config.id = db_yaml_config.id
            inven_db.merge(db_yaml_config, session=session)
        else:
            # global_config.config = global_config_str
            inven_db.insert(switch_auto_config, session=session)
            db_global_config = inven_db.get_model(inventory.SwitchAutoConfig, filters={'name': [name]}, session=session)

            # SwitchYamlConfig Setting
            yaml_config.autoconfig_id = db_global_config.id
            inven_db.insert(yaml_config, session=session)

        return jsonify({'info': 'Configuration saved successfully.', 'status': 200})
    else:
        return jsonify({'info': 'Configuration should not be empty.', 'status': 500})


@new_config_mold.route('/check_glob_file', methods=['POST'])
@admin_permission.require(http_exception=403)
def check_glob_file():
    params = json.loads(request.data)
    model = params.get('model')
    glob_file = str(params.get('glob_file'))
    path = os.path.realpath(os.path.join(os.getcwd(), config_gen_root, model))
    if not path.startswith(AMPCON_BASE_DIR):
        return jsonify({'info': 'Path is invalid!', 'status': 500})
    if glob_file == 'generic':
        file_name = 'switch_global_generic_config'
    else:
        file_name = 'switch_global_security_config'
    content = ''
    is_file_existed = False
    if os.path.exists(path) and os.path.isdir(path):
        all_dir_list = os.listdir(path)
        for dir in all_dir_list:
            if file_name in dir:
                file_path = os.path.join(path, dir)
                is_file_existed = True
                with open(file_path) as f:
                    content = f.readlines()
                    break
    if not is_file_existed:
        return jsonify({'info': 'File not existed!', 'status': 500})
    content = str_helper.mask_key_configuration_list(content)
    return jsonify({'data': ''.join(content), 'status': 200})


@new_config_mold.route('/generate/global', methods=['POST'])
@super_admin_permission.require(http_exception=403)
@utils.operation_log(method='generate_global_config', contents='generate global config {name}')
def generate_global_config():
    info = request.form
    name = info['name']

    if not is_name_valid(name):
        msg = 'Global config name is invalid.'
        return jsonify({'status': 500, 'info': msg})

    model_name = info['model_name']
    generic_global_file = request.files['generic_global']
    security_global_file = request.files['security_global']

    LOG.info("model name %s [%s]", model_name, info)
    system_model = inven_db.get_model(inventory.SwitchSystemInfo, filters={'model': [model_name]})
    if not system_model:
        msg = 'Please Set Model Config First'
        return jsonify({'status': 500, 'url': msg})

    LOG.info("system_model %s", system_model)
    end_path = ''
    if system_model.up_to_date_version:
        version_prefix = re.findall('.*([2-3]\.[0-9]+).', system_model.up_to_date_version)
        if len(version_prefix) > 0:
            end_path = '_' + version_prefix[0].replace('.', '_')

    global_config_str = ''
    generic_save_path = os.path.join(config_gen_root, model_name, 'switch_global_generic_config' + end_path + '.config')
    if generic_global_file:
        generic_global_file.save(generic_save_path)
        with open(generic_save_path, 'r') as fi:
            for line in fi:
                if line.endswith("\n"):
                    global_config_str += line
                else:
                    global_config_str += line + "\n"
    else:
        if os.path.exists(generic_save_path):
            generic_default_path = generic_save_path
        else:
            generic_default_path = os.path.join(config_gen_root, model_name,
                                                'switch_global_generic_config_default.config')
        with open(generic_default_path, 'r') as fi:
            for line in fi:
                if line.endswith("\n"):
                    global_config_str += line
                else:
                    global_config_str += line + "\n"

    security_save_path = os.path.join(config_gen_root, model_name,
                                      'switch_global_security_config' + end_path + '.config')
    if security_global_file:
        security_global_file.save(security_save_path)
        with open(security_save_path, 'r') as fi:
            for line in fi:
                if line.endswith("\n"):
                    global_config_str += line
                else:
                    global_config_str += line + "\n"
    else:
        if os.path.exists(security_save_path):
            security_default_path = security_save_path
        else:
            security_default_path = os.path.join(config_gen_root, model_name,
                                                 'switch_global_security_config_default.config')
        with open(security_default_path, 'r') as fi:
            for line in fi:
                if line.endswith("\n"):
                    global_config_str += line
                else:
                    global_config_str += line + "\n"

    return jsonify({'status': 200, 'name': name, 'data': global_config_str})


@new_config_mold.route('/system_config/brief_all/info', methods=['GET'])
@readonly_permission.require(http_exception=403)
def system_config_brief_all_info():
    system_config = inven_db.get_all_system_config()
    system_config_list = []
    for config in system_config:
        system_config_list.append({'system_config_id': config.id, 'system_config_name': config.config_name})
    return jsonify({'status': 200, 'data': system_config_list})


@new_config_mold.route('/default_config/<string:platform>')
@admin_permission.require(http_exception=403)
def default_config_by_platform(platform):
    agent_conf = {
        'enable': 'True',
        'vpn_enable': 'True',
        'server_domain': 'pica8.com',
        'inband_native_vlan': '4094',
        'server_vpn_host': 'vpn.pica8.com',
        'inband_vlan': '4094',
        'server_hostname_prefix': 'ac',
        'inband_lacp': 'False',
        'uplink_ports': 'te-1/1/49,te-1/1/50',
        'uplink_speed': '1000'
    }
    if not platform:
        pass
    elif os.path.exists('config_gen/{0}/auto-deploy.conf'.format(platform)):
        parser = configparser.ConfigParser()
        parser.read('config_gen/{0}/auto-deploy.conf'.format(platform))
        agent_conf.update(dict(parser.items('DEFAULT')))
        agent_conf.update(dict(parser.items(platform)))
        agent_conf['uplink_ports'] = agent_conf['uplink'] if 'uplink' in agent_conf else agent_conf['uplink_ports']
    elif os.path.exists('agent/auto-deploy.conf'):
        parser = configparser.ConfigParser()
        parser.read('agent/auto-deploy.conf')
        agent_conf.update(dict(parser.items('DEFAULT')))
    return jsonify({'status': 200, 'data': agent_conf})


@new_config_mold.route('/<string:config_name>/edit', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='edit_config', contents='edit config {config_name}')
def edit_config(config_name):
    info = json.loads(request.data)
    config_str = info['configContent']
    general.general_db.update_model(inventory.SwitchAutoConfig,
                                    {'name': [config_name]},
                                    {inventory.SwitchAutoConfig.config_encrypted: aes_cipher.encrypt(config_str)})
    return jsonify({'status': 200, 'info': 'Configuration saved successfully.'})


@new_config_mold.route('/system_config/info/<string:system_config_name>', methods=['GET'])
@admin_permission.require(http_exception=403)
def system_config_info(system_config_name):
    db_system_config = inven_db.get_system_config_by_config_name(system_config_name)
    all_system_config_name = [config.config_name for config in inven_db.get_all_system_config()]
    allowed_source_ips = inven_db.get_collection(inventory.AllowedSourceIPPolicy)
    if db_system_config:
        db_system_config_dict = db_system_config.make_dict()
        db_system_config_dict['switch_op_password'] = constants.FAKE_PASSWORD
        db_system_config_dict['license_portal_password'] = constants.FAKE_PASSWORD
        db_system_config_dict['allowed_source_ip_policy'] = ','.join(
            allowed_source_ip.ip for allowed_source_ip in allowed_source_ips)
    else:
        db_system_config_dict = None
    db_backup_config = utils.get_db_backup_config()
    return jsonify({'status': 200,
                    'data': {'systemConfig': db_system_config_dict, 'dbBackupConfigInfo': db_backup_config,
                             'allSystemConfigName': all_system_config_name, 'enableDebug': is_enable_debug()}})


@new_config_mold.route('/file', methods=['POST'])
def check_file():
    info = json.loads(request.data)
    path = info.get('path')
    model = info.get('model')
    if model:
        path = os.path.realpath(os.path.join(os.path.abspath(''), 'patch_gen', model, path))
    else:
        path = os.path.realpath(path)
    if not path.startswith(AMPCON_BASE_DIR):
        return jsonify({'status': 500, 'info': 'Path is invalid!'})
    dir_list = []

    if path.endswith('.pica8'):
        file_path = path.replace('.pica8', '')
        need_decrypt = utils.is_need_to_decrypt_file(file_path)
        try:
            with open(file_path, 'r') as f:
                if need_decrypt:
                    s = ''
                    for line in f.readlines():
                        s += line
                    list1 = password_reg.sub('***', key_reg.sub('***', encrypt_util.aes_cipher.decrypt(s))).split('\n')
                else:
                    list1 = []
                    for line in f.readlines():
                        line = key_reg.sub('***', line)
                        line = password_reg.sub('***', line)
                        list1.append(line)
        except Exception as e:
            print(e)
            list1 = []
        return jsonify({'status': 200, 'data': '\n'.join(list1)})
    if os.path.exists(path) and os.path.isdir(path):
        all_dir_list = os.listdir(path)
        for dir in all_dir_list:
            path_abs = os.path.join(path, dir)
            file_dict = {'file_name': dir, 'file_path': path_abs}
            if os.path.isdir(path_abs):
                file_dict['type'] = 'dir'
            else:
                file_dict['file_path'] = path_abs + '.pica8'
                file_dict['type'] = 'file'
            dir_list.append(file_dict)
    return jsonify({'status': 200, 'data': dir_list})


@new_config_mold.route('/save/system', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='update_system_config', contents='update system config')
def save_system_config():
    info = request.form
    configuration_name = info['configuration_name']
    # is global config
    is_global_config = False
    if configuration_name == constants.GLOBAL_CONFIG_TAG:
        is_global_config = True

    # action check
    db_system_config = inven_db.get_system_config_by_config_name(configuration_name)

    # parameter check
    if not is_name_valid(configuration_name) or ' ' in configuration_name:
        return jsonify({'status': 500, 'info': 'Configuration name is invalid.'})
    elif info['action'] == 'add' and configuration_name.lower() == constants.GLOBAL_CONFIG_TAG.lower():
        return jsonify({'status': 500, 'info': 'Customized configuration name cannot be Global.'})

    # Global config does not distinguish between adding and updating
    if not is_global_config and db_system_config and info['action'] != 'update':
        return jsonify({'status': 500, 'info': 'Configuration name is already existed.'})
    elif not is_global_config and not db_system_config and info['action'] != 'add':
        return jsonify({'status': 500, 'info': 'Configuration name is not existed.'})

    if not info['switch_op_user']:
        return jsonify({'status': 500, 'info': 'Switch operation user is required.'})

    if not info['switch_op_password']:
        return jsonify({'status': 500, 'info': 'Switch operation password is required.'})
    elif info['switch_op_password'] == constants.FAKE_PASSWORD and info['action'] == 'add':
        return jsonify({'status': 500, 'info': 'Switch operation password is invalid.'})

    if not info['license_portal_url']:
        return jsonify({'status': 500, 'info': 'License portal url is required.'})

    if not info['license_portal_user']:
        return jsonify({'status': 500, 'info': 'License portal user is required.'})

    if not info['license_portal_password']:
        return jsonify({'status': 500, 'info': 'License portal password is required.'})
    elif info['license_portal_password'] == constants.FAKE_PASSWORD and info['action'] == 'add':
        return jsonify({'status': 500, 'info': 'License portal password is invalid.'})

    if is_global_config and not info['retrieve_config_num']:
        return jsonify({'status': 500, 'info': 'Retrieve config num is required.'})

    if is_global_config and not info['db_config_num']:
        return jsonify({'status': 500, 'info': 'DB config num is required.'})

    session = inven_db.get_session()
    config = inventory.SystemConfig()
    config.switch_op_user = info['switch_op_user']
    if info['switch_op_password'] != constants.FAKE_PASSWORD:
        config.switch_op_password = info['switch_op_password']
    config.license_portal_url = info['license_portal_url']
    config.license_portal_user = info['license_portal_user']
    if info['license_portal_password'] != constants.FAKE_PASSWORD:
        config.license_portal_password = info['license_portal_password']

    files = []

    if is_global_config:
        config.retrieve_config_num = info['retrieve_config_num']
        config.db_config_num = info['db_config_num']
        save_patch_dir = os.path.realpath(os.path.join(config_gen_root, 'security'))
    else:
        config.retrieve_config_num = None
        config.db_config_num = None
        save_patch_dir = os.path.realpath(os.path.join(config_gen_root, 'security', configuration_name))

    if not save_patch_dir.startswith(config_gen_root):
        return jsonify({'status': 500, 'info': 'Configuration save path is invalid.'})

    if not os.path.exists(save_patch_dir):
        os.makedirs(save_patch_dir)

    security_config_file = request.files.get('security_config')
    if security_config_file:
        temp_path = os.path.join(save_patch_dir, 'security_temp.config')
        save_path = os.path.join(save_patch_dir, 'security.config')
        security_config_file.save(temp_path)
        content = ''
        with open(save_path, 'w+') as f:
            with open(temp_path, 'r') as temp_file:
                for line in temp_file.readlines():
                    content += line
            f.write(encrypt_util.aes_cipher.encrypt(content))
        config.security_config = save_path.replace(AMPCON_BASE_DIR, '')
        encrypt_util.aes_cipher.remove_temp_file(temp_path)
        files.append({'filename': 'security.config',
                      'path': save_path,
                      'dest': save_path})

    parking_security_config_file = request.files.get('parking_security_config')
    if parking_security_config_file:
        temp_path = os.path.join(save_patch_dir, 'parking_security_temp.config')
        save_path = os.path.join(save_patch_dir, 'parking_security.config')
        parking_security_config_file.save(temp_path)
        content = ''
        with open(save_path, 'w+') as f:
            with open(temp_path, 'r') as temp_file:
                for line in temp_file.readlines():
                    content += line
                f.write(encrypt_util.aes_cipher.encrypt(content))
        config.parking_security_config = save_path.replace(AMPCON_BASE_DIR, '')
        encrypt_util.aes_cipher.remove_temp_file(temp_path)
        files.append({'filename': 'parking_security.config',
                      'path': save_path,
                      'dest': save_path})
    else:
        if not info.get('parking_security_config', ''):
            config.parking_security_config = ''

    config.config_name = configuration_name

    if files:
        # push security file to sync server
        http_client.start_transfer_file(flask_login.current_user.id, files)

    if db_system_config:
        config.id = db_system_config.id
        inven_db.merge(config, session=session)
        result = {'status': 200, 'info': 'Configuration updated successfully.'}
    else:
        inven_db.insert(config, session=session)
        result = {'status': 200, 'info': 'Configuration added successfully.'}

    def update_license_in_thread(_pica8, config_id):
        def task():
            try:
                _session = inven_db.get_session()
                refresh_single_system_config_account_info(_pica8, config_id, _session)
            except Exception as e:
                LOG.exception("Failed to update system config license info %s", e)

        t = threading.Thread(target=task)
        t.start()

    if is_global_config:
        # refresh license token
        try:
            session.query(inventory.SystemConfigLicenseAccountInfo).filter_by(
                system_config_id=db_system_config.id).delete()
            pica8 = pica8_license().fresh(user_name=info['license_portal_user'], password=db_system_config.license_portal_password
            if info['license_portal_password'] == constants.FAKE_PASSWORD else info['license_portal_password'],
                                          url=info['license_portal_url'])
            update_license_in_thread(pica8, config.id)
        except Exception as e:
            LOG.exception("Failed to connect license portal %s", e)

        allowed_source_ips = info.get('allowed_source_ip', '').split(',')
        inven_db.delete_collection(inventory.AllowedSourceIPPolicy)
        for allowed_source_ip in allowed_source_ips:
            if allowed_source_ip:
                inven_db.insert(inventory.AllowedSourceIPPolicy(ip=allowed_source_ip))

        # update license count after license_portal update
        beat_update_db_license_count.delay()

    return jsonify(result)


@new_config_mold.route('/system_config/remove', methods=['POST'])
@super_user_permission.require(http_exception=403)
def remove_system_config():
    info = json.loads(request.data)
    system_config_name = info.get('systemConfigName')
    if system_config_name == constants.GLOBAL_CONFIG_TAG:
        return jsonify({'status': 500, 'info': 'The global system config cannot be removed.'})
    else:
        result, msg = inven_db.delete_system_config_by_config_name(system_config_name)
        return jsonify({'status': 200 if result else 500, 'info': msg})


@new_config_mold.route('/license_connect', methods=['POST'])
@super_user_permission.require(http_exception=403)
def license_connect():
    info = request.get_json()
    url = info.get('url')
    user = info.get('user')
    password = info.get('password')
    configuration_name = info.get('configurationName')
    if password == constants.FAKE_PASSWORD:
        system_config = inven_db.get_system_config_by_config_name(configuration_name)
        password = system_config.license_portal_password if system_config else ''
    try:
        pica8_license().fresh(url, user_name=user, password=password)
    except Exception as e:
        return jsonify({'status': 500, 'info': 'The license connect failed.\n {}'.format(str(e))})
    else:
        return jsonify({'status': 200, 'info': 'The license connect success.'})


@new_config_mold.route('/update/encrypt_key', methods=['POST'])
@utils.operation_log(method='update_encrypt_key', contents='update encrypt key')
def update_encrypt_key():
    info = json.loads(request.data.decode('utf-8'))
    original_key = info['originKey']
    new_key = info['newKey']
    if not original_key == utils.get_encrypt_key():
        msg = {'status': 500, 'info': 'The original encrypt key is incorrect'}
    elif len(new_key) > 32:
        msg = {'status': 500, 'info': 'The length of new encrypt key is more than 32'}
    elif len(new_key) < 6:
        msg = {'status': 500, 'info': 'The length of new encrypt key is less than 6'}
    elif ' ' in new_key:
        msg = {'status': 500, 'info': 'The new password contains spaces'}
    else:
        if utils.update_encrypt_key_and_column(new_key):
            msg = {'status': 200, 'info': 'update encrypt key success'}
        else:
            msg = {'status': 500, 'info': 'update encrypt key failed'}
    return jsonify(msg)


@new_config_mold.route('/syslog_config_info', methods=['POST'])
@admin_permission.require(http_exception=403)
def syslog_config_info():
    data = request.get_json()

    syslog_dicts = []
    tcp_count = udp_count = 0

    if os.path.exists(constants.SYSLOG_CONFIG_PATH):
        with open(constants.SYSLOG_CONFIG_PATH, "r") as file:
            file_content = file.read()
            tcp_syslog_info_list = re.findall(r'.*(?P<level>(\d+){1}) then @{2}(?P<ip>(\d+\.){3}\d+):(?P<port>\d+)',
                                              file_content)
            if tcp_syslog_info_list:
                for tcp_syslog_info in tcp_syslog_info_list:
                    tcp_dict = dict()
                    tcp_dict["level"] = "info" if tcp_syslog_info[0] == "6" else "warning"
                    tcp_dict["ip"] = tcp_syslog_info[2]
                    tcp_dict["port"] = tcp_syslog_info[-1]
                    tcp_dict["protocol"] = "tcp"
                    tcp_count += 1
                    syslog_dicts.append(tcp_dict)
            udp_syslog_info_list = re.findall(r'.*(?P<level>(\d+){1}) then @{1}(?P<ip>(\d+\.){3}\d+):(?P<port>\d+)',
                                              file_content)
            if udp_syslog_info_list:
                for udp_syslog_info in udp_syslog_info_list:
                    udp_dict = dict()
                    udp_dict["level"] = "info" if udp_syslog_info[0] == "6" else "warning"
                    udp_dict["ip"] = udp_syslog_info[2]
                    udp_dict["port"] = udp_syslog_info[-1]
                    udp_dict["protocol"] = "udp"
                    udp_count += 1
                    syslog_dicts.append(udp_dict)

    for col in data.get("sortFields", []):
        sort_field = col.get("field")
        is_reverse = False if col.get("order") == "asc" else True
        syslog_dicts = sorted(syslog_dicts, key=lambda d: d.get(sort_field), reverse=is_reverse)

    return jsonify({'status': 200, "data": syslog_dicts})


@new_config_mold.route('/update/syslog_config', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='update_syslog_config', contents='update syslog config')
def update_syslog():
    info = request.get_json()
    syslog_ip = info.get("syslogIP", "")
    syslog_port = info.get("syslogPort", "")
    syslog_protocol = info.get("syslogProtocol", "")
    syslog_level = info.get("syslogLevel", "")

    if syslog_protocol != 'UDP' and syslog_protocol != "TCP":
        return jsonify({"status": 500, 'info': 'syslog protocol is invalid'})

    if syslog_level != "info" and syslog_level != "warning":
        return jsonify({"status": 500, 'info': 'syslog level is invalid'})

    syslog_level_num = 6 if syslog_level == "info" else 4

    msg = {"status": 500, 'info': 'syslog config update failed'}

    try:
        ip_query = f"sed -n '/{syslog_ip}/p' {constants.SYSLOG_CONFIG_PATH}"
        if os.path.exists(constants.SYSLOG_CONFIG_PATH):
            _, syslog_conf = subprocess.getstatusoutput(ip_query)
            if syslog_conf:
                raise ValueError("The syslog ip already exists.")
            with open(constants.SYSLOG_CONFIG_PATH, "r+") as f:
                syslog_config_list = re.findall(r'@{1,2}(?P<ip>(\d+\.){3}\d+):(?P<port>\d+)', f.read())
                if len(syslog_config_list) >= 5:
                    raise ValueError("A maximum of 5 items can be configured.")

        if syslog_protocol == 'UDP':
            _, udp_conf = subprocess.getstatusoutput(r"sed -n '/#$ModLoad imudp/p' /etc/rsyslog.conf")
            if udp_conf:
                subprocess.getstatusoutput("sed -i 's/#$ModLoad imudp/$ModLoad imudp/g' /etc/rsyslog.conf")
                subprocess.getstatusoutput("sed -i 's/#$UDPServerRun 514/$UDPServerRun 514/g' /etc/rsyslog.conf")
            with open(constants.SYSLOG_CONFIG_PATH, "a+") as file:
                file.seek(0)
                content = file.read()
                if content and not content.endswith("\n"):
                    file.write("\n")
                file.write(f"if $syslogseverity <= {syslog_level_num} then @{syslog_ip}:{syslog_port}")
        else:
            _, tcp_conf = subprocess.getstatusoutput(r"sed -n '/#$ModLoad imtcp/p' /etc/rsyslog.conf")
            if tcp_conf:
                subprocess.getstatusoutput(r"sed -i 's/#$ModLoad imtcp/$ModLoad imtcp/g' /etc/rsyslog.conf")
                subprocess.getstatusoutput(
                    r"sed -i 's/#$InputTCPServerRun 514/$InputTCPServerRun 514/g' /etc/rsyslog.conf")
            with open(constants.SYSLOG_CONFIG_PATH, "a+") as file:
                file.seek(0)
                content = file.read()
                if content and not content.endswith("\n"):
                    file.write("\n")
                file.write(f"if $syslogseverity <= {syslog_level_num} then @@{syslog_ip}:{syslog_port}")
    except ValueError as v:
        LOG.error(str(v))
        msg = {"status": 500, 'info': str(v)}
    except Exception as e:
        LOG.error(str(e))
        msg = {"status": 500, 'info': 'syslog config update failed'}
    else:
        msg = {"status": 200, 'info': 'syslog config update succeed'}
        subprocess.getstatusoutput("systemctl restart rsyslog")
    finally:
        return jsonify(msg)


@new_config_mold.route('/delete/syslog', methods=['POST'])
@super_user_permission.require(http_exception=403)
def del_syslog():
    result = {"status": 200, 'info': "delete syslog success"}
    try:
        info = request.get_json()
        ip = info["syslogIP"]
        execute_str = f"sed -i '/{ip}/d' {constants.SYSLOG_CONFIG_PATH}"
        subprocess.getstatusoutput(execute_str)
        subprocess.getstatusoutput("systemctl restart rsyslog")
    except Exception as e:
        result = {"status": 500, 'info': str(e)}
    return jsonify(result)


@new_config_mold.route('/switch_table/filter_by_system_config_name/data', methods=['POST'])
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='switch_table_filter_by_system_config_name',
                     contents='switch table filter by system_config_name')
def switch_table_filter_by_system_config_name():
    system_config_name = request.get_json().get('systemConfigName')
    if not system_config_name:
        return jsonify({"status": 500, "info": "system config name is required."})
    session = inven_db.get_session()
    system_config_id = session.query(inventory.SystemConfig.id).filter(
        inventory.SystemConfig.config_name == system_config_name).first()
    if not system_config_id:
        return jsonify({
            "data": [],
            "page": 1,
            "pageSize": 0,
            "total": 0,
            "status": 200
        })
    filter_rules = [inventory.Switch.system_config_id == system_config_id[0]]
    page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch,
                                                                     pre_query=utils.query_switch().filter(
                                                                         *filter_rules))
    # Format the response
    response = {
        "data": [{
            "id": switch.id,
            "host_name": switch.host_name,
            "sn": switch.sn,
            "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
            "version": switch.version,
            "revision": switch.revision,
            "status": switch.status,
            "mgt_ip": switch.mgt_ip,
            "platform_model": switch.platform_model
        } for switch in query_obj],
        "page": page_num,
        "pageSize": page_size,
        "total": total_count,
        "status": 200
    }
    return jsonify(response)


@new_config_mold.route('/switch_table/pin_by_system_config_name/data', methods=['POST'])
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='pin_by_system_config_name', contents='pin by system_config_name')
def switch_table_pin_by_system_config_name():
    system_config_name = request.get_json().get('systemConfigName')
    session = inven_db.get_session()
    if not system_config_name:
        return jsonify({"status": 500, "info": "system config name is required."})
    system_config_id = session.query(inventory.SystemConfig.id).filter(
        inventory.SystemConfig.config_name == system_config_name).first()
    if not system_config_id:
        return jsonify({
            "data": [],
            "page": 1,
            "pageSize": 0,
            "total": 0,
            "status": 200
        })
    page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch, pre_query=utils.query_switch(
        system_config_id=system_config_id[0]))
    # Format the response
    response = {
        "data": [{
            "id": switch.id,
            "host_name": switch.host_name,
            "sn": switch.sn,
            "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
            "version": switch.version,
            "revision": switch.revision,
            "status": switch.status,
            "mgt_ip": switch.mgt_ip,
            "platform_model": switch.platform_model,
            "selected": selected
        } for switch, selected in query_obj],
        "page": page_num,
        "pageSize": page_size,
        "total": total_count,
        "status": 200
    }
    return jsonify(response)


@new_config_mold.route('/switch_table/pin_by_sn_list/data', methods=['POST'])
@super_user_permission.require(http_exception=403)
@utils.operation_log(method='switch_table_pin_by_system_config_name',
                     contents='switch table pin by system_config_name')
def switch_table_pin_by_sn_list():
    sn_list = request.get_json().get('snList')
    page_num, page_size, total_count, query_obj = utils.query_helper(inventory.Switch,
                                                                     pre_query=utils.query_switch(sn_list=sn_list))
    if sn_list:
        # Format the response
        response = {
            "data": [{
                "id": switch.id,
                "host_name": switch.host_name,
                "sn": switch.sn,
                "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                "version": switch.version,
                "revision": switch.revision,
                "status": switch.status,
                "mgt_ip": switch.mgt_ip,
                "platform_model": switch.platform_model,
                "selected": selected,
                "reachable_status": switch.reachable_status
            } for switch, selected in query_obj],
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
    else:
        response = {
            "data": [{
                "id": switch.id,
                "host_name": switch.host_name,
                "sn": switch.sn,
                "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                "version": switch.version,
                "revision": switch.revision,
                "status": switch.status,
                "mgt_ip": switch.mgt_ip,
                "platform_model": switch.platform_model,
                "selected": False,
                "reachable_status": switch.reachable_status
            } for switch in query_obj],
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
    return jsonify(response)


@new_config_mold.route('/system_config/switch_management/update', methods=['POST'])
@admin_permission.require(http_exception=403)
def system_config_switch_management_update():
    info = request.get_json()
    system_config_name = info.get('systemConfigName')
    operations = info.get('operations')

    selected_system_config = inven_db.get_system_config_by_config_name(system_config_name)
    if not system_config_name:
        return jsonify({'code': 500, 'info': 'The system config name cannot be empty.'})
    elif not selected_system_config:
        return jsonify({'code': 500, 'info': 'The system config name is not existed.'})

    if not operations:
        return jsonify({'code': 500, 'info': 'The operation cannot be empty.'})

    global_config_id = inven_db.get_system_config_by_config_name(constants.GLOBAL_CONFIG_TAG).id
    selected_system_config_id = selected_system_config.id

    remove_selected_system_config_list = []
    add_selected_system_config_list = []

    for switch_id, operation in operations.items():
        if operation == 'remove':
            remove_selected_system_config_list.append(switch_id)
        elif operation == 'add':
            add_selected_system_config_list.append(switch_id)

    if remove_selected_system_config_list:
        inven_db.update_system_config_switch_operation_by_id(remove_selected_system_config_list, global_config_id)

    if add_selected_system_config_list:
        inven_db.update_system_config_switch_operation_by_id(add_selected_system_config_list, selected_system_config_id)

    return jsonify({'status': 200, 'info': 'The system config status used by switch has been updated.'})


@new_config_mold.route('/view_image', methods=['POST'])
@admin_permission.require(http_exception=403)
def view_image():
    page_num, page_size, total_count, query_obj = utils.query_helper(inventory.SwitchImage)
    # Format the response
    response = {
        "data": [switch_image.make_dict() for switch_image in query_obj],
        "page": page_num,
        "pageSize": page_size,
        "total": total_count,
        "status": 200
    }
    return jsonify(response)


@new_config_mold.route('/delete_image', methods=['POST'])
@super_user_permission.require(http_exception=403)
def delete_image():
    image_id = request.get_json().get('imageId')
    try:
        session = inven_db.get_session()
        with session.begin(subtransactions=True):
            switch_image_obj = session.query(inventory.SwitchImage).filter(inventory.SwitchImage.id == image_id).first()
            sys_image_obj = session.query(inventory.SwitchSystemInfo).filter(
                inventory.SwitchSystemInfo.up_to_date_image_path == switch_image_obj.image_path).first()
            if not sys_image_obj:
                image_path = os.path.realpath(os.path.join(constants.AMPCON_BASE_DIR, switch_image_obj.image_path))
                image_md5_path = os.path.realpath(
                    os.path.join(constants.AMPCON_BASE_DIR, switch_image_obj.image_md5_path))
                if os.path.exists(image_path) and image_path.startswith(constants.AMPCON_BASE_DIR):
                    os.remove(image_path)
                if os.path.exists(image_md5_path) and image_md5_path.startswith(constants.AMPCON_BASE_DIR):
                    os.remove(image_md5_path)
                session.delete(switch_image_obj)
            else:
                return jsonify({'status': 500, 'info': 'The image has been used, please remove first.'})
    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})
    else:
        return jsonify({'status': 200, 'info': 'Image deleted successfully'})


def download_img(url, tmp_path=None, proxies=None, headers=None, return_status=False):
    ssl._create_default_https_context = ssl._create_unverified_context
    if headers:
        down_req = urllib.request.Request(url, headers=headers)
    else:
        down_req = urllib.request.Request(url)
    try:
        if proxies:
            httpproxy_handler = urllib.request.ProxyHandler(proxies)
            opener = urllib.request.build_opener(httpproxy_handler)
            u_stream = opener.open(down_req, timeout=30)
        else:
            u_stream = urllib.request.urlopen(down_req, timeout=30)
        if return_status:
            return u_stream.read()
        osutil.ensure_path(os.path.dirname(tmp_path))
        with open(tmp_path, 'wb') as f:
            block_sz = 8192
            while True:
                image_buffer = u_stream.read(block_sz)
                if not image_buffer:
                    break
                f.write(image_buffer)
    except urllib.request.URLError as e:
        if hasattr(e, "code") and e.code == 401:
            raise ValueError("Authentication failed")
        if hasattr(e, "code") and e.code == 404:
            raise ValueError("Download file not found")
        else:
            raise ValueError("Download failed")


@new_config_mold.route('/upload_md5_img_by_file', methods=['POST'])
@super_user_permission.require(http_exception=403)
def upload_md5_img_by_file():
    info = request.form
    img_id = info.get('imageId')

    msg = {'status': 200, 'info': 'Upload success.'}
    session = inven_db.get_session()
    try:
        try:
            img_file = request.files['file']
            md5_name = img_file.filename
            md5_path = "img/%s" % md5_name
            img_file.save(md5_path)
        except:
            raise ValueError("Upload file failed")

        # update records
        with session.begin(subtransactions=True):
            session.query(inventory.SwitchImage).filter(inventory.SwitchImage.id == img_id).update(
                {inventory.SwitchImage.image_md5_path: md5_path,
                 inventory.SwitchImage.modified_time: datetime.now()})

    except ValueError as v:
        msg = {'status': 500, 'info': str(v)}
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    finally:
        return jsonify(msg)


@new_config_mold.route('/upload_md5_img_by_link', methods=['POST'])
@super_user_permission.require(http_exception=403)
def upload_md5_img_by_link():
    info = request.get_json()
    img_id = info.get('imageId')

    msg = {'status': 200, 'info': 'Upload success.'}
    session = inven_db.get_session()
    try:
        try:
            md5_link = info.get("md5Link", "")
            md5_name = md5_link.split("/")[-1]
            md5_path = "img/%s" % md5_name
            # push file
            download_img(md5_link, md5_path)
        except:
            raise ValueError("Upload file failed")

        # update records
        with session.begin(subtransactions=True):
            session.query(inventory.SwitchImage).filter(inventory.SwitchImage.id == img_id).update(
                {inventory.SwitchImage.image_md5_path: md5_path,
                 inventory.SwitchImage.modified_time: datetime.now()})

    except ValueError as v:
        msg = {'status': 500, 'info': str(v)}
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    finally:
        return jsonify(msg)


def add_image(*args):
    image_name, platform, version, revision, image_path, image_md5_path = args
    session = inven_db.get_session()
    image_name_objs = session.query(inventory.SwitchImage).with_entities(inventory.SwitchImage.image_name).all()
    image_name_list = [i[0] for i in image_name_objs]
    if image_name in image_name_list:
        raise ValueError("This image existed, Please check")
    with session.begin(subtransactions=True):
        upload_time = datetime.now()
        new_image_obj = inventory.SwitchImage()
        new_image_obj.image_name = image_name
        new_image_obj.platform = platform
        new_image_obj.version = version
        new_image_obj.revision = revision
        new_image_obj.image_path = image_path
        new_image_obj.image_md5_path = image_md5_path
        new_image_obj.create_time = new_image_obj.modified_time = upload_time
        session.add(new_image_obj)


@my_celery_app.task(name="download_latest_img", base=AmpConBaseTask)
def download_latest_img(suffix_url, *args, **kwargs):
    session = inven_db.get_session()
    proxies = cfg.CONF.license_portal_proxy if cfg.CONF.license_portal_proxy else None
    sys_config = inven_db.get_global_system_config()
    headers = {
        "Content-Type": "text/html;charset=UTF-8",
        "Authorization": "Basic %s" % base64.b64encode(
            "{}:{}".format(sys_config.license_portal_user, sys_config.license_portal_password).encode(
                "utf-8")).decode()
    }
    try:
        if args:
            save_path, image_name, platform, version, revision = args
            try:
                if suffix_url.split('/')[-1] in ['onie-installer-picos-4.4.5.7-8ffbb29f1a-as4610.bin', 'onie-installer-picos-4.2.3-0c35832999-n3000.bin', 'onie-installer-picos-4.2.3-0c35832999-n3100.bin', 'onie-installer-picos-4.4.5.7-8ffbb29f1a-x86.bin']:
                    if not all([sys_config.license_portal_user, sys_config.license_portal_password]):
                        raise ValueError("System User not exist")
                    download_img(pre_image.format(suffix_url), tmp_path=save_path, proxies=proxies, headers=headers)
                    download_img("{}.md5".format(pre_image.format(suffix_url)), tmp_path="{}.md5".format(save_path),
                                 proxies=proxies, headers=headers)
                else:
                    image_name = suffix_url.strip().split('/')[-1]
                    success, err_msg = csp_downloader.download_csp_image(image_name, save_path)
                    if not success:
                        raise ValueError(f"Download image failed: {err_msg or image_name}")
                monitor_db.add_event('', 'info', 'Download latest image success', session=session)
                http_client.start_transfer_file('', [{'filename': image_name, 'path': "img/{}".format(image_name),
                                                      'dest': "img/{}".format(image_name)}])
                http_client.start_transfer_file('',
                                                [{'filename': "{}.md5".format(image_name),
                                                  'path': "img/{}.md5".format(image_name),
                                                  'dest': "img/{}.md5".format(image_name)}])
                csp_downloader.clear_downloading(image_name)
            except Exception as e:
                image_path = os.path.realpath(os.path.join(constants.AMPCON_BASE_DIR, save_path))
                image_md5_path = os.path.realpath(os.path.join(constants.AMPCON_BASE_DIR, "{}.md5".format(save_path)))
                if os.path.exists(image_path) and image_path.startswith(constants.AMPCON_BASE_DIR):
                    os.remove(image_path)
                if os.path.exists(image_md5_path) and image_md5_path.startswith(constants.AMPCON_BASE_DIR):
                    os.remove(image_md5_path)
                monitor_db.add_event('', 'error', 'Download latest image failed, %s' % str(e), session=session)
            finally:
                add_image(image_name, platform, version, revision, "img/%s" % image_name, "img/%s.md5" % image_name)
        else:
            return download_img(pre_image.format(suffix_url), proxies=proxies, headers=headers, return_status=True)
    except Exception as e:
        LOG.info("Get latest image %s failed", pre_image.format(suffix_url))


@new_config_mold.route('/upload_image/<int:upload_type>', methods=['POST'])
@super_user_permission.require(http_exception=403)
def upload_image(upload_type):
    msg = {'status': 200, 'info': 'Upload success.'}
    try:
        if upload_type == 1:
            img_file = request.files['file']
            image_name = img_file.filename
            image_path = "img/%s" % image_name
            extra_info = request.form
            platform = extra_info.get("platform", "").lower() if extra_info.get("platform", "") else None
            version = extra_info.get("version", "")
            revision = extra_info.get("revision", "")
            osutil.ensure_path(os.path.dirname(image_path))
            img_file.save(image_path)

            img_md5_file = request.files.get('md5File', '')
            image_md5_path = ""

            if not platform:
                return jsonify({'status': 500, 'info': 'Platform is required.'})

            if img_md5_file:
                image_md5_name = img_md5_file.filename
                image_md5_path = "img/%s" % image_md5_name
                osutil.ensure_path(os.path.dirname(image_md5_path))
                img_md5_file.save(image_md5_path)
                http_client.start_transfer_file(flask_login.current_user.id, [
                    {'filename': image_md5_name, 'path': image_md5_path, 'dest': image_md5_path}])

            # add records
            add_image(image_name, platform, version, revision, image_path, image_md5_path)

            # sync with other server
            http_client.start_transfer_file(flask_login.current_user.id,
                                            [{'filename': image_name, 'path': image_path, 'dest': image_path}])

        elif upload_type == 2:
            image_link_info = request.get_json()
            if image_link_info:
                image_link = image_link_info.get("imageLink", "")
                image_md5_link = image_link_info.get("imageMd5Link", "")
                platform = image_link_info.get("platform", "")
                version = image_link_info.get("version", "")
                revision = image_link_info.get("revision", "")
                # push file
                path_list = [image_link, image_md5_link]
                add_path_list = ["img/%s" % image_link.split("/")[-1],
                                 "img/%s" % image_md5_link.split("/")[-1] if image_md5_link else ""]
                for url in path_list:
                    if url:
                        tmp_path = "img/%s" % url.split("/")[-1]
                        download_img(url, tmp_path)
                        # sync with other server
                        http_client.start_transfer_file(flask_login.current_user.id,
                                                        [{'filename': url.split("/")[-1], 'path': tmp_path,
                                                          'dest': tmp_path}])
                # add records
                add_image(image_link.split("/")[-1], platform, version, revision, *add_path_list)
        else:
            image_info = request.get_json()
            latest_image_list = image_info.get("latest_image_name", [])

            is_valid, auth_err = csp_downloader.is_auth_valid()
            if not is_valid:
                raise ValueError(f"Auth failed: {auth_err}")

            if latest_image_list:
                for image_tmp in latest_image_list:
                    image_name = image_tmp.split("/")[-1]
                    v_dict = utils.get_image_info(image_name)
                    if v_dict:
                        version = v_dict['version']
                        revision = v_dict['revision']
                        platform = v_dict.get('platform')

                        # push file on background
                        task_name = f"download_latest_img_{image_name}"
                        jobs = AmpConBaseTask.get_running_jobs()
                        if jobs:
                            for job in jobs:
                                if task_name in job.task_name:
                                    raise ValueError("The Task is running..")
                        csp_downloader.mark_downloading(image_name)
                        # download_latest_img(image_tmp, "img/{}".format(image_name), image_name, platform, version, revision)
                        download_latest_img.delay(image_tmp, "img/{}".format(image_name), image_name, platform, version,
                                                  revision, celery_task_name=task_name)
                    else:
                        raise ValueError("Can not resolve Image Url")
            msg = {'status': 200,
                   'info': 'The download image task is running in the background, please wait for the download to complete.'}
    except ValueError as v:
        msg = {'status': 500, 'info': str(v)}
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    finally:
        return jsonify(msg)


@new_config_mold.route('/download_image/<string:filename>', methods=['GET'])
def download_image(filename):
    image_path = "img/%s" % filename
    return send_file(image_path, as_attachment=True)


@new_config_mold.route('/configs_tree', methods=['POST'])
def configs_tree():
    session = inven_db.get_session()
    nodes = []
    query = session.query(general.GeneralConfig)
    nodes.append({
        'id': 0,
        'pid': -1,
        'name': 'configs',
        'title': 'click page to change',
        'open': True,
        'level': 0
    })

    configs = query.filter(general.GeneralConfig.id != 0).order_by(general.GeneralConfig.platform.asc()).all()
    for config in configs:
        config = {
            'id': config.id,
            'pid': config.pid,
            'name': config.name,
            'title': config.description,
            'count': len(config.children),
            'isParent': len(config.children) > 0,
            'level': config.level
        }
        nodes.append(config)
    return jsonify({'status': 200, 'data': nodes})


@new_config_mold.route('/general/add', methods=['POST'])
@admin_permission.require(http_exception=403)
def config_add():
    params = request.get_json()
    name = params['name'].strip()
    desc = params.get('desc', "")
    parent_id = int(params['pid'])
    level = int(params['level'])
    if not is_name_valid(name):
        msg = {'info': 'The general config is invalid!', 'status': 400, 'id': ''}
    elif general.general_db.get_model(general.GeneralConfig, filters={'name': [name]}):
        msg = {'status': 400, 'info': 'The general config Already Exists!', 'id': ''}
    else:
        config = general.GeneralConfig(name=name, description=desc, pid=parent_id, level=level)
        general.general_db.insert(config)
        config = general.general_db.get_model(general.GeneralConfig, filters={'name': [name]})
        if config:
            msg = {'status': 200, 'info': 'Config Added Successfully!', 'id': int(config.id)}
        else:
            msg = {'status': 400, 'info': 'Config Added Failed!', 'id': ''}
    return jsonify(msg)


@new_config_mold.route('/general/<string:config_name>/delete')
@utils.operation_log(method='delete_general_config', contents='delete config {config_name}')
def delete_general_config(config_name):
    if config_name == 'configs':
        msg = {'status': 400, 'info': 'Cannot remove root node!'}
    elif general.general_db.delete_collection(general.GeneralConfig, {'name': [config_name]}):
        msg = {'status': 200, 'info': 'ok'}
    else:
        msg = {'status': 400, 'info': 'Cannot remove {} node!'.format(config_name)}
    return jsonify(msg)


@new_config_mold.route('/general/<int:config_id>/config')
def get_general_config(config_id):
    config = general.general_db.get_model(general.GeneralConfig, filters={'id': [config_id]})
    return jsonify(
        {
            'data':
                {
                    'config': config.content,
                    'create_time': config.create_time.date().isoformat()
                },
            'status': 200
        })


@new_config_mold.route('/general/update', methods=['POST'])
@utils.operation_log(method='update_general_config', contents='update {name} config')
def update_config():
    info = request.get_json()
    name = info['name']
    config_str = info['config']
    general.general_db.update_model(general.GeneralConfig,
                                    {'name': [name]}, {'content_encrypted': aes_cipher.encrypt(config_str)})
    return {'status': 200, 'info': 'config update success'}


@new_config_mold.route('/general/save_as', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='update_general_save_as', contents='update {name} config')
def save_as_config():
    params = request.get_json()
    name = params['name'].strip()
    desc = params.get('desc', "")
    parent_id = int(params['pid'])
    level = int(params['level'])
    config_str = params['config']
    if not is_name_valid(name):
        msg = {'info': 'The general config is invalid!', 'status': 400, 'id': ''}
        return jsonify(msg)
    elif general.general_db.get_model(general.GeneralConfig, filters={'name': [name]}):
        msg = {'status': 400, 'info': 'The general config Already Exists!', 'id': ''}
        return jsonify(msg)
    config = general.GeneralConfig(name=name, description=desc, pid=parent_id, level=level,
                                   content=config_str)
    # general.general_db.update_model(general.GeneralConfig,
    #                                 {'name': [name]}, {general.GeneralConfig.content: config_str})
    general.general_db.insert_or_update(config, 'name')
    # nid = general.general_db.get_model(general.GeneralConfig, filters={'name': [name]})
    result_config = general.general_db.get_model(general.GeneralConfig, filters={'name': [name]})
    nid = result_config.id

    msg = {'status': 200, 'info': 'Config Save Successfully!', 'id': int(nid)}
    return jsonify(msg)


@new_config_mold.route('/general/move', methods=['POST'])
@admin_permission.require(http_exception=403)
def config_move():
    params = request.get_json()
    config_name = params['configName']
    new_parent_id = int(params['parentId'])
    general.general_db.update_model(general.GeneralConfig, filters={'name': [config_name]},
                                    updates={general.GeneralConfig.pid: new_parent_id})
    return jsonify({'status': 200, 'info': 'ok'})


@new_config_mold.route('/switch/<string:sn>/config/show', methods=['GET'])
def config_show(sn):
    switch = inven_db.get_switch_info_by_sn(sn)
    if not switch:
        return jsonify({'status': 500, 'info': 'switch not found'})

    if switch.configs or switch.general_configs:
        global_config = site_config = ''
        for switch_config in switch.configs:
            if switch_config.type == 'global':
                global_config = switch_config.config
            elif switch_config.type == 'site':
                site_config = switch_config.config
        config = global_config + '\n' + site_config
        for general_config in switch.general_configs:
            config += general_config.content
        config = key_reg.sub('key ***', config)
        config = password_reg.sub('password ***', config)
    else:
        config = '%s No Switch Config' % sn
    return jsonify({'status': 200, 'switchStatus': switch.status, 'content': config})


@new_config_mold.route('/config/data', methods=['POST'])
def config_data():
    page_num, page_size, total_count, query_obj = utils.query_helper(inventory.SwitchAutoConfig)
    return jsonify({"data": [config.make_dict() for config in query_obj], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_config_mold.route('/<int:config_id>/config')
def get_config(config_id):
    config = inven_db.get_model(inventory.SwitchAutoConfig, filters={'id': [config_id]})
    config_str = str_helper.mask_key_configuration(config.config)
    return config_str or ''


@new_config_mold.route('/<int:config_id>/attach')
def get_config_attach(config_id):
    mapps = inven_db.get_collection(inventory.SwitchSwitchAutoConfig, filters={'switchAutoConfig_id': [config_id]})
    switch_ids = [mapp.switch_id for mapp in mapps]
    switchs = inven_db.get_collection(inventory.Switch, filters={'id': switch_ids})
    return jsonify({"data": [switch.make_dict() for switch in switchs], "status": 200})


@new_config_mold.route('/<string:config_name>/delete')
@admin_permission.require(http_exception=403)
@utils.operation_log(method='delete_config', contents='delete config {config_name}')
def delete_config(config_name):
    config = inven_db.get_model(inventory.SwitchAutoConfig, filters={'name': [config_name]})
    attaches = inven_db.get_collection(inventory.SwitchSwitchAutoConfig, filters={'switchAutoConfig_id': [config.id]})
    if len(attaches) > 0:
        return jsonify({'status': 400, 'info': 'config is in use'})
    inven_db.delete_collection(inventory.SwitchAutoConfig, {'parent_id': [config.id]})
    inven_db.delete_collection(inventory.SwitchAutoConfig, {'id': [config.id]})
    return jsonify({'status': 200, 'info': 'config delete success'})


@new_config_mold.route('/model/<string:model_name>')
@readonly_permission.require(http_exception=403)
def model_index(model_name):
    db_system_config = inven_db.get_model(inventory.SwitchSystemInfo, filters={'model': [model_name]})
    mapping_dict = inven_db.get_model_port_mapping(model_name)
    if db_system_config:
        rt_dict = db_system_config.make_dict()
        # start to get the port number
        rt_dict.update(mapping_dict)
        return jsonify({'status': 200, "data": rt_dict})
    res = {'status': 400, "data": mapping_dict}
    # res.update(mapping_dict)
    return jsonify(res)


@new_config_mold.route('/all_model_physic_port_info', methods=['GET'])
@readonly_permission.require(http_exception=403)
def all_model_physic_port_info():
    session = inven_db.get_session()
    all_model_port_info = session.query(inventory.ModelPhysicPort).all()
    res = {}
    for model_port in all_model_port_info:
        if model_port.platform_name not in res:
            res[model_port.platform_name] = {
                'ge': [],
                'te': [],
                'qe': [],
                'xe': []
            }
        if model_port.port_type in res[model_port.platform_name].keys():
            res[model_port.platform_name][model_port.port_type].append(model_port.port_name)
    return jsonify({'status': 200, 'data': res})


@new_config_mold.route('/get_port_list', methods=['POST'])
@readonly_permission.require(http_exception=403)
def model_port_list():
    data = request.get_json()
    model_list = data.get("modelList", [])
    sn_list = data.get("snList", [])
    res_port_dict = {}
    model_to_sn_map = {}
    session = inven_db.get_session()
    for model in model_list:
        ports = session.query(inventory.ModelPhysicPort.port_name).filter(
            inventory.ModelPhysicPort.platform_name == model)
        if ports:
            res_port_dict[model] = list(map(lambda x: x[0], ports.all()))

    for sn in sn_list:
        switch = session.query(inventory.Switch).filter(inventory.Switch.sn == sn).first()
        if switch:
            model = switch.platform_model
            model_to_sn_map[sn] = model
            if model not in res_port_dict:
                ports = session.query(inventory.ModelPhysicPort.port_name).filter(
                    inventory.ModelPhysicPort.platform_name == model)
                if ports:
                    res_port_dict[model] = list(map(lambda x: x[0], ports.all()))

    final_result = {}
    for sn in sn_list:
        model = model_to_sn_map.get(sn)
        if model and model in res_port_dict:
            final_result[sn] = res_port_dict[model]

    return jsonify({'status': 200, "data": final_result})


@new_config_mold.route('/get_image_info/<string:model_name>', methods=['GET'])
@admin_permission.require(http_exception=403)
def get_image_info(model_name):
    session = inven_db.get_session()
    with session.begin(subtransactions=True):
        model_mapping_obj = session.query(inventory.SwitchSystemInfo).filter(
            inventory.SwitchSystemInfo.model == model_name).first()

        image_name_objs = session.query(inventory.SwitchImage).filter(
            inventory.SwitchImage.platform.in_([model_mapping_obj.platform, "other"])).all()

        tmp_dict = dict()

        for image_name_obj in image_name_objs:
            name = image_name_obj.image_path
            platform = image_name_obj.platform
            if platform not in tmp_dict:
                tmp_dict[platform] = [name]
            else:
                tmp_dict[image_name_obj.platform].append(name)

        selected_img_path = model_mapping_obj.up_to_date_image_path
        md5_path = model_mapping_obj.up_to_date_image_md5_path
        onie_path = model_mapping_obj.up_to_date_onie_path

    return jsonify({'status': 200,
                    "data": {'options': tmp_dict, 'selected_img_path': selected_img_path, 'md5_path': md5_path,
                             'onie_path': onie_path}})


@new_config_mold.route('/model/create', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='create_model', contents='create {name} model')
def model_create():
    info = request.get_json()

    switch_system = inventory.SwitchSystemInfo()
    platform_name = info['name']
    switch_system.model = info['name']
    switch_system.speed_for_license = '1G'
    switch_system.feature = 'EE'
    sources = []

    if not is_name_valid(platform_name):
        return jsonify({"info": 'Name is invalid.', 'status': 500})

    if not os.path.realpath(os.path.join(config_gen_root, info['name'])).startswith(config_gen_root):
        return jsonify({"info": 'Model name is invalid!', 'status': 500})

    # get old config record
    old_record = inven_db.get_model(inventory.SwitchSystemInfo, filters={'model': [info['name']]})

    if info.get('imagePath'):
        image_info = inven_db.get_model(inventory.SwitchImage, filters={'image_path': [info['imagePath']]})
        if image_info:
            version = image_info.version + '/' + image_info.revision
            switch_system.up_to_date_version = version
        else:
            return jsonify({"info": 'Cannot find this image.', 'status': 400})
        if not (old_record and old_record.up_to_date_image_path.split("/")[-1] == info['imagePath'].split("/")[-1]):
            switch_system.up_to_date_image_path = info.get('imagePath')

    image_md5_path = info.get('imageMd5Path')
    if image_md5_path and not (
            old_record and old_record.up_to_date_image_md5_path.split("/")[-1] == image_md5_path.split("/")[-1]):
        switch_system.up_to_date_image_md5_path = image_md5_path
    if not image_md5_path:
        switch_system.up_to_date_image_md5_path = ""

    onie_path = info.get('oniePath')
    if onie_path and not (old_record and old_record.up_to_date_onie_path.split("/")[-1] == onie_path.split("/")[-1]):
        switch_system.up_to_date_onie_path = onie_path

    if not info.get('patchTar'):
        patch_tar = request.files.get('patchTar')
        # ssh_config_file = request.files['ssh_config']
        if patch_tar:
            re_save_patch_dir = os.path.join(config_gen_root, info['name'])
            if not os.path.exists(re_save_patch_dir):
                os.makedirs(re_save_patch_dir)
            save_path = os.path.join(re_save_patch_dir, patch_tar.filename)
            # ssh_config_file_path = os.path.join(save_dir, ssh_config_file.filename)
            patch_tar.save(save_path)
            # ssh_config_file.save(ssh_config_file_path)
            un_tar(save_path, info['name'])
            switch_system.patched_tar_file = save_path

            sources.append({'filename': patch_tar.filename, 'path': save_path, 'dest': save_path})
        else:
            switch_system.patched_tar_file = ''

    if not info.get('scriptFile'):
        script_file = request.files.get('scriptFile')
        # ssh_config_file = request.files['ssh_config']
        if script_file:
            re_save_patch_dir = os.path.join(config_gen_root, info['name'])
            save_path = os.path.join(re_save_patch_dir, script_file.filename)
            # ssh_config_file_path = os.path.join(save_dir, ssh_config_file.filename)
            script_file.save(save_path)
            # ssh_config_file.save(ssh_config_file_path)
            switch_system.script_file_path = save_path

            sources.append({'filename': script_file.filename, 'path': save_path, 'dest': save_path})
        else:
            switch_system.script_file_path = ''
    else:
        switch_system.script_file_path = info.get('scriptFile')

    have_need_file = False
    need_to_check = False
    manual_upgrade_scripts = set()
    manual_upgrade_scripts_dir = os.path.join(config_gen_root, info['name'], 'manual_upgrade_scripts')
    if not manual_upgrade_scripts_dir.startswith(os.path.realpath(config_gen_root)):
        return jsonify({'info': 'Model name is invalid', 'status': '500'})
    osutil.ensure_path(manual_upgrade_scripts_dir)
    for field, script_file in request.files.items():
        if field.startswith('defaultUpgradeScript'):
            need_to_check = True
            filepath = os.path.join(manual_upgrade_scripts_dir, script_file.filename)
            if constants.POST_XORPLUS == script_file.filename:
                have_need_file = True
            script_file.save(filepath)
            if filepath in manual_upgrade_scripts:
                continue
            manual_upgrade_scripts.add(filepath)
            sources.append({'filename': script_file.filename, 'path': filepath, 'dest': filepath})

    for key, value in info.items():
        if key.startswith('defaultUpgradeScript') and value != '':
            need_to_check = True
            if constants.POST_XORPLUS == os.path.basename(value):
                have_need_file = True
            manual_upgrade_scripts.add(value)

    if not have_need_file and need_to_check:
        return jsonify({"info": '%s must be existed' % constants.POST_XORPLUS, 'status': 400})

    switch_system.patched_install_script = info.get('installingScript', "")
    switch_system.manual_upgrade_scripts = ','.join(manual_upgrade_scripts)

    if sources:
        # push files to sync server
        http_client.start_transfer_file(flask_login.current_user.id, sources)

    # switch_system.ssh_config = ssh_config_file_path
    inven_db.insert_or_update(switch_system, 'model')

    try:
        # switch_system.ssh_config = ssh_config_file_path
        inven_db.delete_collection(inventory.ModelPhysicPort, filters={'platform_name': [platform_name]})
        ge_start = int(info['geStart'])
        ge_end = int(info['geEnd'])
        if ge_end > ge_start > 0:
            for i in range(ge_start, ge_end + 1):
                inven_db.insert(inventory.ModelPhysicPort(platform_name=platform_name,
                                                          port_name='ge-1/1/%d' % i, port_type='ge'))
        elif (ge_start > 0 or ge_end > 0) and ge_start != 1 and ge_end != 1:
            return jsonify(
                {"info": 'ge num end must bigger than ge num start or ge num start must bigger than 0', 'status': 400})

        te_start = int(info['teStart'])
        te_end = int(info['teEnd'])
        if te_end > te_start > 0:
            for i in range(te_start, te_end + 1):
                inven_db.insert(inventory.ModelPhysicPort(platform_name=platform_name,
                                                          port_name='te-1/1/%d' % i, port_type='te'))
        elif (te_start > 0 or te_end > 0) and te_start != 1 and te_end != 1:
            return jsonify(
                {"info": 'te num end must bigger than te num start or te num start must bigger than 0', 'status': 400})

        qe_start = int(info['qeStart'])
        qe_end = int(info['qeEnd'])
        if qe_end > qe_start > 0:
            for i in range(qe_start, qe_end + 1):
                inven_db.insert(inventory.ModelPhysicPort(platform_name=platform_name,
                                                          port_name='qe-1/1/%d' % i, port_type='qe'))
        elif (qe_end > 0 or qe_start > 0) and qe_start != 1 and qe_end != 1:
            return jsonify(
                {"info": 'qe num end must bigger than qe num start or qe num start must bigger than 0', 'status': 400})

        xe_start = int(info['xeStart'])
        xe_end = int(info['xeEnd'])
        if xe_end > xe_start > 0:
            for i in range(xe_start, xe_end + 1):
                inven_db.insert(inventory.ModelPhysicPort(platform_name=platform_name,
                                                          port_name='xe-1/1/%d' % i, port_type='xe'))
        elif (xe_start > 0 or xe_end > 0) and xe_start != 1 and xe_end != 1:
            return jsonify(
                {"info": 'xe num end must bigger than xe num start or xe num start must bigger than 0', 'status': 400})

    except:
        inven_db.delete_collection(inventory.ModelPhysicPort, filters={'platform_name': [platform_name]})
        raise

    return jsonify({"info": 'Model add success', 'status': 200})


@new_config_mold.route('/edit_sysname', methods=['POST'])
@admin_permission.require(http_exception=403)
def edit_sysname():
    data = request.get_json()
    try:
        sn = data.get("sn")
        ip = data.get("mgt_ip")
        new_sysname = data.get("new_sysname")

        if not all([sn, ip, new_sysname]):
            return jsonify({'status': 400, 'info': 'Missing required parameters'})

        user, pw = utils.get_switch_default_user(sn=sn)

        cli_str = "\n".join([
            f"set system hostname {new_sysname}",
            "commit"
        ])

        result, code = ssh_util.interactive_shell_configure(
            cli_str,
            ip,
            username=user,
            password=pw,
            timeout=10
        )

        if code != constants.RMA_ACTIVE:
            return jsonify({'status': 500, 'info': f'Failed to set Sysname for {sn}', 'output': result})

        inven_db.update_model(inventory.Switch, filters={'sn': [sn], 'ip': [ip]}, updates={
            inventory.Switch.host_name: new_sysname
        })

        msg = {'status': 200, 'info': 'Success to update Sysname'}

    except Exception as e:
        LOG.error("Failed to update Sysname: %s", e)
        msg = {'status': 500, 'info': 'Failed to update Sysname'}

    return jsonify(msg)


@new_config_mold.route('/parking/data', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_parking_lot_data():
    page_num, page_size, total_count, query_obj = utils.query_helper(inventory.SwitchParking)
    return jsonify({"data": [config.make_dict() for config in query_obj], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_config_mold.route('/del_switch_from_parking', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='del_switch_from_parking', contents='del parking switch {sn}')
def del_switch_from_parking():
    info = request.get_json()
    inven_db.delete_collection(inventory.SwitchParking, filters={'sn': [info['sn']]})
    # db.delete_collection(DeployedSecuritySwitch, filters={'sn': [info['sn']]})
    msg = {'status': 200, 'info': 'The %s switch is deleted success ' % info['sn']}
    return jsonify(msg)


@new_config_mold.route('/investigate/<string:sn>')
@utils.operation_log(method='investigate', contents='investigate {sn}')
def investigate(sn):
    switch = inven_db.get_model(inventory.SwitchParking, filters={'sn': [sn]})
    if switch.investigate == 1:
        switch.investigate = 0
        msg = {'status': 200, 'info': 'The %s switch is un-investigate success ' % sn}
    else:
        switch.investigate = 1
        msg = {'status': 200, 'info': 'The %s switch is investigate success ' % sn}
    inven_db.merge(switch)
    return jsonify(msg)


#### config backup api ####

@new_config_mold.route('/collect_backup_config', methods=["POST"])
@admin_permission.require(http_exception=403)
def collect_backup_config():
    params = request.get_json()
    days = params.get("day", "")
    hour = params.get("hour", "")
    try:
        beat_task.add_job("collect-backup-config-all-task", "beat_collect_backup_config_all",
                          job_schedule="0 " + hour + " " + days + " * * ",
                          start_time=datetime.now(), job_desc="automation collect backup config",
                          kwargs={"celery_type": "CRONTAB"})
    except Exception as e:
        return jsonify({'status': 400, 'info': 'set collect backup config failed'})
    return jsonify({'status': 200, 'info': 'success'})


@new_config_mold.route('/retrieve_config')
@admin_permission.require(http_exception=403)
def retrieve_config():
    groups = utils.get_user_group().filter(inventory.Group.retrieve_config == True).all()
    report_time_list = get_last_reports(7, 'retrieve_config')
    task_obj = beat_task.get_active_job_by_name("collect-backup-config-all-task")
    target_str = str(task_obj).split(":")[1].strip().split(" ")
    job_view = {'day': target_str[2].split('/')[1] if '/' in target_str[2] else target_str[2], 'hour': target_str[1]}
    # return render_template('lifecycle/lifecycle_retrieve_config.html', active=active, report_time_list=report_time_list,
    #                        groups=groups, job=job_view, vpn_enable=cfg.CONF.vpn_enable)
    content = {
        "groups": [group.group_name for group in groups],
        "reportTimeList": report_time_list,
        "job": job_view,
    }
    return jsonify({'data': content, 'status': 200})


@new_config_mold.route('/lifecycle_retrieve/group/<string:group_name>')
@admin_permission.require(http_exception=403)
@utils.operation_log(method='/lifecycle_retrieve/group/', contents='retrieve configuration in group {group_name}')
def lifecycle_retrieve_config(group_name):
    date_time = (date.today()).strftime('%Y-%m-%d')
    switch_task_running = AmpConBaseTask.get_running_job_by_task_name(f'switch_retrieve_{group_name}')
    if switch_task_running:
        LOG.info('switch is retrieving  %s', date_time)
        return jsonify({'status': 200, 'info': f'switch is retrieving{date_time}'})
    else:
        db_session = inven_db.get_session()
        switches_sn_list = []
        ag_switches = db_session.query(inventory.AssociationGroup).filter(
            inventory.AssociationGroup.group_name == group_name).all()
        for group_switch in ag_switches:
            switches_sn_list.append(group_switch.switch_sn)
        report_time = time.strftime("%Y-%m-%d %H:%M", time.localtime()) + "<" + 'retrieve_config' + " report> "
        batch_retrieve_config.delay(switches_sn_list, report_time, celery_task_name=f"switch_retrieve_{group_name}",
                                    celery_group=group_name)
    return jsonify({'status': 200, 'info': 'Config backup successful'})


@new_config_mold.route('/lifecycle_retrieve/sn')
@admin_permission.require(http_exception=403)
@utils.operation_log(method='/lifecycle_retrieve/sn', contents='retrieve configuration in switch SN: {sn}, IP: {ip}.')
def lifecycle_sn_retrieve_config():
    sn = request.args.get('sn')
    ip = request.args.get('ip')
    # execute ansible to collect switch config
    # need to know the switch is import or is registed
    try:
        if collect_backup_config_single(ip, sn) == constants.RMA_ACTIVE:
            inven_db.add_switch_log(sn, "Retrieve config success", level='info')
            msg = {'status': 200, 'info': 'backup success'}
        else:
            inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
            msg = {'status': 400, 'info': 'backup failed'}
    except Exception as e:
        LOG.exception(e)
        inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
        msg = {'status': 500, 'info': 'backup failed'}
    return jsonify(msg)


def get_last_reports(days, report_action=''):
    current_time = datetime.utcnow()
    filter_time = current_time - timedelta(days=days)
    # Then we filter all log before the days
    db_session = inven_db.get_session()
    logs_register = db_session.query(inventory.SwitchLog).filter(inventory.SwitchLog.create_time > filter_time).filter(
        inventory.SwitchLog.report_action == report_action).order_by(inventory.SwitchLog.create_time.desc()).all()
    # Now we need get the list of report time
    logs_time_list = []
    if logs_register:
        for log in logs_register:
            if log.content[0:40] not in logs_time_list:
                logs_time_list.append(log.content[0:40])
    return logs_time_list


@my_celery_app.task(name="batch_retrieve_config", base=AmpConBaseTask)
def batch_retrieve_config(switches_sn_list, report_time, **kwargs):
    db_session = inven_db.get_session()
    for sn in switches_sn_list:
        switch = db_session.query(inventory.Switch).filter(inventory.Switch.sn == sn).first()
        if switch:
            collect_backup_config_single_group(switch.mgt_ip, switch.sn, report_time, action='retrieve_config')


@new_config_mold.route('/db_backup_management/get_backup_db_record', methods=['POST'])
def get_backup_db_record():
    page_num, page_size, total_count, query_db_record = utils.query_helper(inventory.DbBackup)
    return jsonify({"data": [db_record.make_dict() for db_record in query_db_record], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_config_mold.route('/db_backup_management/add_backup_db_record', methods=['POST'])
def add_backup_db_record():
    info = request.get_json()
    name = info['name']
    encrypt_secret_pin = info['encryptKey']
    version = info['version']
    try:
        backup_records = inven_db.get_session().query(inventory.DbBackup)
        current_version = constants.VERSION
        if not is_name_valid(name):
            msg = {'status': 500, 'info': f'name {name} is invalid'}
        elif not encrypt_secret_pin or len(encrypt_secret_pin) < 6 or len(encrypt_secret_pin) > 32 or not is_name_valid(encrypt_secret_pin):
            msg = {'status': 500, 'info': 'encrypt {} is invalid'.format(encrypt_secret_pin)}
        elif version.split("-")[-1] != current_version:
            msg = {'status': 500, 'info': 'backup version invalid, should be %s!' % current_version}
        elif len(name) > 64 or len(version) > 64:
            msg = {'status': 500, 'info': 'param invalid, please check!'}
        elif inven_db.get_model(inventory.DbBackup, filters={'name': [name]}):
            msg = {'status': 500, 'info': 'db backup name %s exists!' % name}
        elif backup_records.count() >= utils.get_db_backup_config()[0]:
            msg = {'status': 500,
                   'info': 'Db backup count already max count, recent backup name is %s' % backup_records.order_by(
                       'create_time').first().name}
        else:
            path = os.path.abspath(os.path.join(db_backup_root, name))
            backup_data = inventory.DbBackup()
            backup_data.name = name
            backup_data.version = version
            backup_data.backup_file_path = path
            inven_db.insert(backup_data)
            output_str = utils.run_db_backup(path, encrypt_secret_pin)
            if output_str == '':
                msg = {'status': 200, 'info': 'The %s backup add success' % name}
            else:
                msg = {'status': 500, 'info': 'db backup name failed, %s!' % output_str}
    except Exception as e:
        inven_db.delete_collection(inventory.DbBackup, filters={'name': [name]})
        LOG.exception('add backup failed', e)
        msg = {'status': 500, 'info': 'add backup failed'}
    return msg


@new_config_mold.route('/db_backup_management/update_backup_db_record', methods=['POST'])
def update_backup_db_record():
    info = request.get_json()
    name = info['name']
    modified_name = info['modified_name']
    if len(name) > 64 or len(modified_name) > 64:
        msg = {'status': 500, 'info': 'param invalid, please check!'}
        return msg
    backup_data = inven_db.get_model(inventory.DbBackup, filters={'name': [name]})
    if not backup_data:
        msg = {'status': 500, 'info': 'db backup name %s is not exists!' % name}
        return msg
    # get current user's id
    backup_data_name_id = backup_data.id
    # check list has modified_name
    backup_date_modified_name_info = inven_db.get_model(inventory.DbBackup, filters={'name': [modified_name]})
    if backup_date_modified_name_info:
        backup_data_modified_name_id = backup_date_modified_name_info.id
        if backup_data_modified_name_id and backup_data_modified_name_id != backup_data_name_id:
            msg = {'status': 500, 'info': 'db backup name %s exists!' % modified_name}
        else:
            msg = {'status': 500,
                   'info': 'db backup modifiedName {} is same as current name {}!'.format(modified_name, name)}
    else:
        backup_data.name = modified_name
        backup_data.backup_file_path = os.path.abspath(os.path.join(db_backup_root, modified_name))
        inven_db.merge(backup_data)
        os.rename(os.path.abspath(os.path.join(db_backup_root, name)),
                  os.path.abspath(os.path.join(db_backup_root, modified_name)))
        msg = {'status': 200, 'info': 'The backup {} name was successfully changed to {}'.format(name, modified_name)}
    return msg


@new_config_mold.route('/db_backup_management/delete_backup_db_record', methods=['POST'])
def delete_backup_db_record():
    info = request.get_json()
    name = info['name']
    if len(name) > 64:
        msg = {'status': 500, 'info': 'param invalid, please check!'}
        return msg
    backup_data = inven_db.get_model(inventory.DbBackup, filters={'name': [name]})
    if not backup_data:
        msg = {'status': 500, 'info': 'db backup name %s is not exists!' % name}
    else:
        if os.path.exists(backup_data.backup_file_path):
            shutil.rmtree(backup_data.backup_file_path)
        inven_db.delete_collection(inventory.DbBackup, filters={'name': [name]})
        msg = {'status': 200, 'info': 'The %s backup delete success' % name}
    return msg


@new_config_mold.route('/db_backup_management/restore_db_backup', methods=['POST'])
def restore_db_backup():
    info = request.get_json()
    name = info['name']
    encrypt_secret_pin = info['encryptKey']
    current_version = constants.VERSION
    if not encrypt_secret_pin or len(encrypt_secret_pin) < 6 or len(encrypt_secret_pin) > 32:
        msg = {'status': 500, 'info': 'encrypt {} is invalid'.format(encrypt_secret_pin)}
        return msg
    elif len(name) > 64:
        msg = {'status': 500, 'info': 'param invalid, please check!'}
        return msg
    backup_data = inven_db.get_model(inventory.DbBackup, filters={'name': [name]})
    if not backup_data:
        msg = {'status': 500, 'info': 'database backup name {} is not exists!'.format(name)}
    elif backup_data.version.split("-")[-1] != current_version:
        msg = {'status': 500,
               'info': 'database can only roll back backups of the same version, current version is {}, backup version is {}'.format(
                   current_version, backup_data.version)}
    else:
        if utils.restore_db_backup(backup_data.backup_file_path, encrypt_secret_pin):
            msg = {'status': 200, 'info': 'database restore {} success!'.format(name)}
        else:
            msg = {'status': 500,
                   'info': 'database restore {} failed, encrypt key is incorrect, please check!'.format(name)}
    return msg


@new_config_mold.route('/get_latest_image', methods=['GET'])
@super_user_permission.require(http_exception=403)
def get_latest_image():
    latest_model_type = ["AS4610", "N30xx", "N31xx", "X86"]
    session = inven_db.get_session()
    latest_ret = dict()
    msg = {}
    try:
        for model_type in latest_model_type:
            ret = download_latest_img(model_type).decode()
            if ret:
                latest_image_info = re.search(r'<a href="(?P<latest_image>onie.*?.bin)">', ret).groupdict()
                if latest_image_info:
                    latest_image = latest_image_info["latest_image"]
                    image_obj = session.query(inventory.SwitchImage).filter(
                        inventory.SwitchImage.image_name == latest_image).first()
                    if image_obj:
                        latest_ret[model_type + "/" + latest_image] = True
                    else:
                        latest_ret[model_type + "/" + latest_image] = False
        if not latest_ret:
            raise ValueError("Can not get latest image")
        msg = {"status": 200, "data": latest_ret}
    except ValueError as v:
        msg = {'status': 400, 'info': str(v)}
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    finally:
        return jsonify(msg)


@new_config_mold.route('/get_latest_image_for_campus', methods=['GET'])
@super_user_permission.require(http_exception=403)
def get_latest_image_for_campus():
    msg = {}
    try:
        latest_ret_str, err_msg = csp_downloader.get_csp_image_json()
        if not latest_ret_str:
            raise ValueError(f"Can not get latest image , info:{err_msg}")
        latest_ret = json.loads(latest_ret_str)

        # Check if the image names in the latest_ret are already in the database
        image_name_list_temp = []
        for data in latest_ret.get("data", []):
            if data.get('images', None):
                image_name_list_temp.extend(list(map(lambda x: x['name'], data.get('images', []))))
        all_image_name_set = set(image_name_list_temp)

        session = inven_db.get_session()
        existed_image_name_list = list(map(lambda x: x[0], session.query(inventory.SwitchImage.image_name).filter(
            inventory.SwitchImage.image_name.in_(list(all_image_name_set))).all()))

        for data in latest_ret.get("data", []):
            if data.get('images', None):
                for image in data.get('images', []):
                    image_name = image.get('name')
                    if image_name in existed_image_name_list or csp_downloader.is_downloading(image_name):
                        image['disabled'] = True
                    else:
                        image['disabled'] = False
        msg = {"status": 200, "data": latest_ret}
    except ValueError as v:
        msg = {'status': 400, 'info': str(v)}
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    finally:
        return jsonify(msg)


@new_config_mold.route('/update_switch_model', methods=['POST'])
@admin_permission.require(http_exception=403)
def update_switch_model():
    try:
        model_physic_port_add_list = []
        switch_system_info_add_list = []
        hardware_mapping_add_list = []
        black_box_model_add_list = []
        # download switch model zip file
        save_path = constants.SWITCH_MODEL_UPDATE_PATH
        url = constants.SWITCH_MODEL_UPDATE_URL
        if os.path.exists(save_path):
            shutil.rmtree(save_path)
        os.makedirs(save_path)
        proxies = cfg.CONF.license_portal_proxy if cfg.CONF.license_portal_proxy else None
        sys_config = inven_db.get_global_system_config()
        headers = {
            "Content-Type": "text/html;charset=UTF-8",
            "Authorization": "Basic %s" % base64.b64encode(
                "{}:{}".format(sys_config.license_portal_user, sys_config.license_portal_password).encode(
                    "utf-8")).decode()
        }
        to_be_updated_switch_model_zip_path = os.path.join(save_path, 'switch_model_update.zip')
        to_be_updated_switch_model_json_path = os.path.join(save_path, 'switch_model_update.json')
        to_be_updated_auto_deploy_path = os.path.join(save_path, 'auto-deploy.conf')
        to_be_updated_picos_patch_path = os.path.join(save_path, 'picos_patch.sh')
        try:
            download_img(url, to_be_updated_switch_model_zip_path, proxies=proxies, headers=headers)
        except Exception as e:
            msg = {'info': str(e), 'status': 500}
            return jsonify(msg)
        if not os.path.exists(to_be_updated_switch_model_zip_path):
            msg = {'info': 'Failed to download switch model zip file!', 'status': 500}
            return jsonify(msg)
        else:
            r = zipfile.is_zipfile(to_be_updated_switch_model_zip_path)
            if r:
                fz = zipfile.ZipFile(to_be_updated_switch_model_zip_path, 'r')
                for file in fz.namelist():
                    fz.extract(file, save_path)
                if not os.path.exists(to_be_updated_switch_model_json_path) or not os.path.exists(
                        to_be_updated_auto_deploy_path) or not os.path.exists(to_be_updated_picos_patch_path):
                    msg = {'info': 'Unzipped file check failed!', 'status': 500}
                    return jsonify(msg)
                else:
                    pass
            else:
                msg = {'info': 'Download file is not zip!', 'status': 500}
                return jsonify(msg)
        # load json data
        with open(to_be_updated_switch_model_json_path, 'r') as f:
            switch_model_dict = json.load(f)
        # get delta
        to_be_updated_switch_model = set(switch_model_dict.keys()).difference(set(utils.get_support_models()))
        if not to_be_updated_switch_model:
            return jsonify({"status": 200, 'info': 'There are no switch models to update!'})
        # database operation
        for switch_model in to_be_updated_switch_model:
            # table model_physic_port
            if switch_model_dict[switch_model].get('model_physic_port'):
                for port in switch_model_dict[switch_model]['model_physic_port']:
                    model_physic_port_temp = inventory.ModelPhysicPort()
                    model_physic_port_temp.platform_name = switch_model
                    model_physic_port_temp.port_name = port['port_name']
                    model_physic_port_temp.port_type = port['port_type']
                    model_physic_port_add_list.append(model_physic_port_temp)
            # table switch_system_info
            if switch_model_dict[switch_model].get('switch_system_info'):
                for switch_system_info in switch_model_dict[switch_model]['switch_system_info']:
                    switch_system_info_temp = inventory.SwitchSystemInfo()
                    switch_system_info_temp.model = switch_model
                    switch_system_info_temp.speed_for_license = switch_system_info['speed_for_license']
                    switch_system_info_temp.feature = switch_system_info['feature']
                    switch_system_info_temp.platform = switch_system_info['platform']
                    switch_system_info_temp.up_to_date_version = switch_system_info['up_to_date_version']
                    switch_system_info_temp.up_to_date_image_path = switch_system_info['up_to_date_image_path']
                    switch_system_info_temp.up_to_date_image_md5_path = switch_system_info['up_to_date_image_md5_path']
                    switch_system_info_temp.up_to_date_onie_path = switch_system_info['up_to_date_onie_path']
                    switch_system_info_temp.patched_tar_file = switch_system_info['patched_tar_file']
                    switch_system_info_temp.patched_install_script = switch_system_info['patched_install_script']
                    switch_system_info_temp.script_file_path = switch_system_info['script_file_path']
                    switch_system_info_temp.manual_upgrade_scripts = switch_system_info['manual_upgrade_scripts']
                    switch_system_info_add_list.append(switch_system_info_temp)
            # table hardware_mapping
            if switch_model_dict[switch_model].get('hardware_mapping'):
                for hardware_mapping in switch_model_dict[switch_model]['hardware_mapping']:
                    hardware_mapping_temp = inventory.HardwareMapping()
                    hardware_mapping_temp.switch_model = switch_model
                    hardware_mapping_temp.hardware_model = hardware_mapping['hardware_model']
                    hardware_mapping_add_list.append(hardware_mapping_temp)
            if switch_model_dict[switch_model].get('is_black_box_model', False):
                black_box_model_add_list.append(switch_model)
        insert_data_list = [model_physic_port_add_list, switch_system_info_add_list, hardware_mapping_add_list]
        if not batch_insert(insert_data_list, key={'SwitchSystemInfo': 'model', 'ModelPhysicPort': 'platform_name',
                                                   'HardwareMapping': 'switch_model'}):
            return jsonify({"status": 500, 'info': 'Failed to update switch model!'})
        # automation.ini
        automation_ini_path = constants.AUTOMATION_CONFIG_FILE
        config = configparser.ConfigParser()
        config.read(automation_ini_path, encoding="utf-8")
        support_model = config.get('DEFAULT', 'supports_models').split(',')
        support_model.extend(to_be_updated_switch_model)
        config.set('DEFAULT', 'supports_models', ','.join(support_model))

        with open(automation_ini_path, 'w+') as f:
            config.write(f)
        # config_gen
        auto_deploy_config = configparser.ConfigParser()
        agent_auto_deploy_path = f'{AUTOMATION_BASE_DIR}/server/agent/auto-deploy.conf'
        auto_deploy_config.read(agent_auto_deploy_path)
        vpn_server_host = auto_deploy_config.get('DEFAULT', 'server_vpn_host')
        server_hostname_prefix = auto_deploy_config.get('DEFAULT', 'server_hostname_prefix')
        server_domain = auto_deploy_config.get('DEFAULT', 'server_domain')
        server_vpn_ip = auto_deploy_config.get('DEFAULT', 'server_vpn_ip')
        for new_hw in to_be_updated_switch_model:
            new_model_auto_deploy_path = f'{AUTOMATION_BASE_DIR}/server/config_gen/{0}/auto-deploy.conf'.format(new_hw)
            new_model_config_gen_path = f'{AUTOMATION_BASE_DIR}/server/config_gen/{0}'.format(new_hw)
            if not os.path.exists(new_model_auto_deploy_path):
                hw_config_path = new_model_auto_deploy_path
                hw_config_file_path = new_model_config_gen_path
                if os.path.exists(hw_config_file_path):
                    shutil.rmtree(hw_config_file_path)
                os.makedirs(hw_config_file_path)
                shutil.copyfile(to_be_updated_auto_deploy_path, hw_config_path)
                shutil.copyfile(to_be_updated_picos_patch_path, os.path.join(hw_config_file_path, 'picos_patch.sh'))
                hw_config = configparser.ConfigParser()
                hw_config.read(hw_config_path)
                hw_config.set('DEFAULT', 'server_vpn_host', vpn_server_host)
                hw_config.set('DEFAULT', 'server_hostname_prefix', server_hostname_prefix)
                hw_config.set('DEFAULT', 'server_domain', server_domain)
                hw_config.set('DEFAULT', 'server_vpn_ip', server_vpn_ip)
                with open(hw_config_path, 'w+') as f:
                    hw_config.write(f)
        result = {"status": 200, 'info': 'update switch model success! New added model : {}'.format(
            ', '.join(to_be_updated_switch_model))}
    except Exception as e:
        result = {"status": 500, 'info': str(e)}
    return jsonify(result)


# email settings
@new_config_mold.route('/system/update_email_server_setting', methods=['POST'])
@admin_permission.require(http_exception=403)
def update_email_server_setting():
    try:
        info = request.get_json()
        email_server = info.get('emailServer')
        email_port = info.get('emailPort')
        email_sender_email = info.get('senderEmail')
        email_ssl = info.get('emailSSL', False)
        email_tls = info.get('emailTLS', False)
        email_is_authentication = info.get('isAuthentication', True)
        email_user = info.get('emailUser', '')
        email_password = info.get('emailPassword', '')
        if not email_server:
            return jsonify({"status": 500, 'info': 'Email server is required!'})
        if not email_port or str(email_port).isdigit() == False:
            return jsonify({"status": 500, 'info': 'Email port is invalid!'})
        if not email_sender_email or not re.match('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
                                                  email_sender_email):
            return jsonify({"status": 500, 'info': 'Sender email is invalid!'})
        if email_ssl is True and email_tls is True:
            return jsonify({"status": 500, 'info': 'Email SSL and TLS can not be enabled at the same time!'})
        if email_is_authentication:
            if not email_user:
                return jsonify({"status": 500, 'info': 'Email user cannot be empty!'})
            if not email_password:
                return jsonify({"status": 500, 'info': 'Email password is required!'})
        session = general.general_db.get_session()
        try:
            with session.begin(subtransactions=True):
                email_setting = session.query(general.EmailServerSetting).first()
                if email_setting:
                    email_setting.host = email_server
                    email_setting.port = email_port
                    email_setting.sender_email = email_sender_email
                    email_setting.is_authentication = email_is_authentication
                    email_setting.ssl = email_ssl
                    email_setting.tls = email_tls
                    if email_is_authentication:
                        email_setting.username = email_user
                        if email_password != '********':
                            email_setting.password = email_password
                    else:
                        email_setting.username = ''
                        email_setting.password = ''
                else:
                    email_setting = general.EmailServerSetting()
                    email_setting.host = email_server
                    email_setting.port = email_port
                    email_setting.sender_email = email_sender_email
                    email_setting.is_authentication = email_is_authentication
                    email_setting.ssl = email_ssl
                    email_setting.tls = email_tls
                    if email_is_authentication:
                        email_setting.username = email_user
                        if email_password == '********':
                            return jsonify({"status": 500, 'info': 'Email password is required!'})
                        email_setting.password = email_password
                    else:
                        email_setting.username = ''
                        email_setting.password = ''
                    session.add(email_setting)
        except Exception as db_error:
            LOG.error(traceback.format_exc())
            session.rollback()
        result = {"status": 200, 'info': 'update email server setting success!'}
    except Exception as e:
        result = {"status": 500, 'info': str(e)}
    return jsonify(result)


@new_config_mold.route('/system/get_email_server_setting', methods=['GET'])
@readonly_permission.require(http_exception=403)
def get_email_server_setting():
    session = general.general_db.get_session()
    email_setting = session.query(general.EmailServerSetting).first()
    if email_setting:
        if email_setting.is_authentication:
            email_server_setting = {
                'emailServer': email_setting.host,
                'emailPort': email_setting.port,
                'emailSSL': email_setting.ssl,
                'emailTLS': email_setting.tls,
                'senderEmail': email_setting.sender_email,
                'isAuthentication': email_setting.is_authentication,
                'emailUser': email_setting.username,
                'emailPassword': '********'
            }
        else:
            email_server_setting = {
                'emailServer': email_setting.host,
                'emailPort': email_setting.port,
                'emailSSL': email_setting.ssl,
                'emailTLS': email_setting.tls,
                'senderEmail': email_setting.sender_email,
                'isAuthentication': email_setting.is_authentication,
            }
    else:
        email_server_setting = {}
    email_server_setting['emailAlertAllLevels'] = ['error', 'warning', 'info']
    if ampcon_pro_type == 'ampcon-dc':
        # email_server_setting['emailAlertAllTypes'] = ['packet_loss_alert', 'resource_usage_alert',
        #                                               'ai_monitoring_alert', 'interface_monitoring_alert',
        #                                               'optical_module_alert']
        email_server_setting['emailAlertAllTypes'] = ['packet_loss_alert', 'resource_usage_alert',
                                                      'ai_monitoring_alert', 'interface_monitoring_alert',]
    elif ampcon_pro_type == 'ampcon-campus':
        # email_server_setting['emailAlertAllTypes'] = ['packet_loss_alert', 'resource_usage_alert',
        #                                               'interface_monitoring_alert', 'optical_module_alert']
        email_server_setting['emailAlertAllTypes'] = ['packet_loss_alert', 'resource_usage_alert',
                                                      'interface_monitoring_alert',]
    else:
        # email_server_setting['emailAlertAllTypes'] = ['packet_loss_alert', 'resource_usage_alert',
        #                                               'ai_monitoring_alert', 'interface_monitoring_alert',
        #                                               'optical_module_alert']
        email_server_setting['emailAlertAllTypes'] = ['packet_loss_alert', 'resource_usage_alert',
                                                      'ai_monitoring_alert', 'interface_monitoring_alert',]
    return jsonify({"status": 200, 'data': email_server_setting})


@new_config_mold.route('/system/get_email_rule_settings_table_data', methods=['POST'])
@readonly_permission.require(http_exception=403)
def get_email_rule_settings_table_data():
    page_num, page_size, total_count, query_obj = utils.query_helper(general.EmailRuleSettings)
    data = []
    for email_data in query_obj:
        temp_alarm_level = json.loads(email_data.alarm_level_settings) if email_data.alarm_level_settings else {}
        temp_alarm_type = json.loads(email_data.alarm_type_settings) if email_data.alarm_type_settings else {}
        data.append({
            'id': email_data.id,
            'rule_name': email_data.rule_name,
            'fabric_name_list': email_data.fabric_name_list.split(',') if email_data.fabric_name_list else '',
            'site_name_list': email_data.site_name_list.split(',') if email_data.site_name_list else '',
            'silent_time': email_data.silent_time,
            'create_user': email_data.create_user,
            'email': email_data.email,
            'enable': email_data.enable,
            'alarm_level_error': temp_alarm_level.get('alarm_level_error', False),
            'alarm_level_warning': temp_alarm_level.get('alarm_level_warning', False),
            'alarm_level_info': temp_alarm_level.get('alarm_level_info', False),
            'alarm_type_packet_loss_alert': temp_alarm_type.get('alarm_type_packet_loss_alert', False),
            'alarm_type_resource_usage_alert': temp_alarm_type.get('alarm_type_resource_usage_alert', False),
            'alarm_type_ai_monitoring_alert': temp_alarm_type.get('alarm_type_ai_monitoring_alert', False),
            'alarm_type_interface_monitoring_alert': temp_alarm_type.get('alarm_type_interface_monitoring_alert',
                                                                         False),
            'alarm_type_optical_module_alert': temp_alarm_type.get('alarm_type_optical_module_alert', False),
            'create_time': email_data.create_time,
            'modified_time': email_data.modified_time
        })
    return jsonify({"data": data, "page": page_num, "pageSize": page_size, "total": total_count, "status": 200})


@new_config_mold.route('/system/add_email_rule_setting', methods=['POST'])
@admin_permission.require(http_exception=403)
def add_email_rule_setting():
    try:
        info = request.get_json()
        rule_name = info.get('ruleName', '')
        fabric_name_list = info.get('fabric', [])
        site_name_list = info.get('site', [])
        silent_time = info.get('silentPeriod', 0)
        create_user = flask_login.current_user.id
        email_str = info.get('email', '')
        email_rule_is_enable = info.get('emailRuleIsEnable', False)
        alarm_level_error = info.get('enableError', False)
        alarm_level_warning = info.get('enableWarning', False)
        alarm_level_info = info.get('enableInfo', False)
        alarm_type_packet_loss_alert = info.get('enablePacketLossAlert', False)
        alarm_type_resource_usage_alert = info.get('enableResourceUsageAlert', False)
        alarm_type_ai_monitoring_alert = info.get('enableAIMonitoringAlert', False)
        alarm_type_interface_monitoring_alert = info.get('enableInterfaceMonitoringAlert', False)
        alarm_type_optical_module_alert = info.get('enableOpticalModuleAlert', False)
        for email_target in email_str.split(','):
            if not email_target or not re.match('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email_target):
                return jsonify({"status": 500, 'info': 'Email user is invalid!'})
        session = general.general_db.get_session()

        if session.query(general.EmailRuleSettings).count() >= 100:
            return jsonify(
                {"status": 500, 'info': 'Cannot add more than 100 email rule settings, please delete some first!'})

        try:
            with (session.begin(subtransactions=True)):
                email_rule_setting = session.query(general.EmailRuleSettings).filter(
                    general.EmailRuleSettings.rule_name == rule_name).first()
                if email_rule_setting:
                    return jsonify({"status": 500, 'info': 'Email user setting already exists!'})
                else:
                    email_rule_setting = general.EmailRuleSettings()
                    email_rule_setting.rule_name = rule_name
                    email_rule_setting.fabric_name_list = ','.join(fabric_name_list)
                    email_rule_setting.site_name_list = ','.join(site_name_list)
                    email_rule_setting.silent_time = silent_time
                    email_rule_setting.create_user = create_user
                    email_rule_setting.email = email_str
                    email_rule_setting.enable = email_rule_is_enable
                    email_rule_setting.alarm_level_settings = json.dumps({
                        'alarm_level_error': bool(alarm_level_error),
                        'alarm_level_warning': bool(alarm_level_warning),
                        'alarm_level_info': bool(alarm_level_info),
                    })
                    email_rule_setting.alarm_type_settings = json.dumps({
                        'alarm_type_packet_loss_alert': bool(alarm_type_packet_loss_alert),
                        'alarm_type_resource_usage_alert': bool(alarm_type_resource_usage_alert),
                        'alarm_type_ai_monitoring_alert': bool(alarm_type_ai_monitoring_alert),
                        'alarm_type_interface_monitoring_alert': bool(alarm_type_interface_monitoring_alert),
                        'alarm_type_optical_module_alert': bool(alarm_type_optical_module_alert),
                    })
                    session.add(email_rule_setting)
        except Exception as db_error:
            LOG.error(traceback.format_exc())
            session.rollback()
            return jsonify({"status": 500, 'info': 'add email rule setting failed!'})
        result = {"status": 200, 'info': 'add email rule setting success!'}
    except Exception as e:
        result = {"status": 500, 'info': str(e)}
    return jsonify(result)


@new_config_mold.route('/system/edit_email_rule_setting', methods=['POST'])
@admin_permission.require(http_exception=403)
def edit_email_rule_setting():
    try:
        info = request.get_json()
        email_id = info.get('emailId')
        rule_name = info.get('ruleName', '')
        fabric_name_list = info.get('fabric', [])
        site_name_list = info.get('site', [])
        silent_time = info.get('silentPeriod', 0)
        create_user = flask_login.current_user.id
        email_str = info.get('email', '')
        email_rule_is_enable = info.get('emailRuleIsEnable', False)
        alarm_level_error = info.get('enableError', False)
        alarm_level_warning = info.get('enableWarning', False)
        alarm_level_info = info.get('enableInfo', False)
        alarm_type_packet_loss_alert = info.get('enablePacketLossAlert', False)
        alarm_type_resource_usage_alert = info.get('enableResourceUsageAlert', False)
        alarm_type_ai_monitoring_alert = info.get('enableAIMonitoringAlert', False)
        alarm_type_interface_monitoring_alert = info.get('enableInterfaceMonitoringAlert', False)
        alarm_type_optical_module_alert = info.get('enableOpticalModuleAlert', False)
        session = general.general_db.get_session()
        email_rule_setting = session.query(general.EmailRuleSettings).filter(
            general.EmailRuleSettings.id == email_id).first()
        if not email_rule_setting:
            return jsonify({"status": 500, 'info': 'Email user setting is not exists!'})

        existing_name = session.query(general.EmailRuleSettings).filter(
            general.EmailRuleSettings.rule_name == rule_name,
            general.EmailRuleSettings.id != email_id
        ).first()
        if existing_name:
            return jsonify({
                "status": 500,
                'info': 'The rule name has already existed. Please enter a different rule name.'
            })

        for email_target in email_str.split(','):
            if not email_target or not re.match('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email_target):
                return jsonify({"status": 500, 'info': 'Email user is invalid!'})
        try:
            with session.begin(subtransactions=True):
                pre_name = email_rule_setting.rule_name
                email_rule_setting.rule_name = rule_name
                email_rule_setting.fabric_name_list = ','.join(fabric_name_list)
                email_rule_setting.site_name_list = ','.join(site_name_list)
                email_rule_setting.silent_time = silent_time
                email_rule_setting.create_user = create_user
                email_rule_setting.email = email_str
                email_rule_setting.enable = email_rule_is_enable
                email_rule_setting.alarm_level_settings = json.dumps({
                    'alarm_level_error': bool(alarm_level_error),
                    'alarm_level_warning': bool(alarm_level_warning),
                    'alarm_level_info': bool(alarm_level_info),
                })
                email_rule_setting.alarm_type_settings = json.dumps({
                    'alarm_type_packet_loss_alert': bool(alarm_type_packet_loss_alert),
                    'alarm_type_resource_usage_alert': bool(alarm_type_resource_usage_alert),
                    'alarm_type_ai_monitoring_alert': bool(alarm_type_ai_monitoring_alert),
                    'alarm_type_interface_monitoring_alert': bool(alarm_type_interface_monitoring_alert),
                    'alarm_type_optical_module_alert': bool(alarm_type_optical_module_alert),
                })
                if pre_name != rule_name:
                    session.query(general.EmailRuleLogs).filter(
                        general.EmailRuleLogs.email_rule_name == pre_name).update({"email_rule_name": rule_name})
        except Exception as db_error:
            LOG.error(traceback.format_exc())
            session.rollback()
            return jsonify({"status": 500, 'info': 'update email user setting failed!'})
        result = {"status": 200, 'info': 'update email user setting success!'}
    except Exception as e:
        result = {"status": 500, 'info': str(e)}
    return jsonify(result)


@new_config_mold.route('/system/delete_email_rule_setting', methods=['POST'])
@admin_permission.require(http_exception=403)
def delete_email_rule_setting():
    try:
        info = request.get_json()
        email_id = info.get('emailId')
        session = general.general_db.get_session()
        email_rule_setting = session.query(general.EmailRuleSettings).filter(
            general.EmailRuleSettings.id == email_id).first()
        if not email_rule_setting:
            return jsonify({"status": 500, 'info': 'Email user setting is not exists!'})
        try:
            with session.begin(subtransactions=True):
                session.delete(email_rule_setting)
        except Exception as db_error:
            LOG.error(traceback.format_exc())
            session.rollback()
            return jsonify({"status": 500, 'info': 'delete email user setting failed!'})
        result = {"status": 200, 'info': 'delete email success!'}
    except Exception as e:
        result = {"status": 500, 'info': str(e)}
    return jsonify(result)


@new_config_mold.route('/system/verify_email_server_connection', methods=['POST'])
@admin_permission.require(http_exception=403)
def verify_email_server_connection():
    try:
        info = request.get_json()
        email_is_authentication = info.get('isAuthentication', False)
        if not email_is_authentication:
            return jsonify({"status": 500, 'info': 'Email authentication is disabled!'})
        email_server = info.get('emailServer')
        email_port = info.get('emailPort')
        email_user = info.get('emailUser')
        email_password = info.get('emailPassword')
        is_enable_ssl = info.get('emailSSL', False)
        is_enable_tls = info.get('emailTLS', False)
        session = general.general_db.get_session()
        origin_email_server_setting = session.query(general.EmailServerSetting).first()
        if not email_server:
            return jsonify({"status": 500, 'info': 'Email server is required!'})
        if not email_port or str(email_port).isdigit() == False:
            return jsonify({"status": 500, 'info': 'Email port is invalid!'})
        if not email_user or not re.match('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email_user):
            return jsonify({"status": 500, 'info': 'Email user is invalid!'})
        if not email_password:
            return jsonify({"status": 500, 'info': 'Email password is required!'})
        if email_password == '********' and origin_email_server_setting:
            email_password = origin_email_server_setting.password
        try:
            if utils.is_email_server_available(email_server, email_port, email_user, email_password, is_enable_ssl,
                                               is_enable_tls):
                result = {"status": 200, 'info': 'Email server connection success!'}
            else:
                result = {"status": 500, 'info': 'Email server connection failed!'}
        except Exception as e:
            result = {"status": 500, 'info': str(e)}
    except Exception as e:
        result = {"status": 500, 'info': str(e)}
    return jsonify(result)


@new_config_mold.route('/system/send_test_email', methods=['POST'])
@admin_permission.require(http_exception=403)
def send_test_email():
    try:
        info = request.get_json()
        email_is_authentication = info.get('isAuthentication', False)
        email_server = info.get('emailServer')
        email_sender_address = info.get('senderEmail')
        email_port = info.get('emailPort')
        email_user = info.get('emailUser')
        email_password = info.get('emailPassword')
        is_enable_ssl = info.get('emailSSL', False)
        is_enable_tls = info.get('emailTLS', False)
        receivers_str = info.get('receivers')
        if email_is_authentication:
            if not email_password:
                return jsonify({"status": 500, 'info': 'Email password is required!'})
        if not email_server:
            return jsonify({"status": 500, 'info': 'Email server is required!'})
        if not email_port or str(email_port).isdigit() == False:
            return jsonify({"status": 500, 'info': 'Email port is invalid!'})
        if email_is_authentication:
            if not email_user or not re.match('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email_user):
                return jsonify({"status": 500, 'info': 'Email user is invalid!'})
            if not email_password:
                return jsonify({"status": 500, 'info': 'Email password is required!'})
        session = general.general_db.get_session()
        origin_email_server_setting = session.query(general.EmailServerSetting).first()
        if email_password == '********' and origin_email_server_setting:
            email_password = origin_email_server_setting.password
        for email_target in receivers_str.split(','):
            if not email_target or not re.match('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email_target):
                return jsonify({"status": 500, 'info': 'Test email address is invalid!'})

        if email_is_authentication:
            config = {
                "host": email_server,
                "port": email_port,
                "sender_email": email_sender_address,
                "is_authentication": email_is_authentication,
                "username": email_user,
                "password": email_password,
                "ssl": is_enable_ssl,
                "tls": is_enable_tls
            }
        else:
            config = {
                "host": email_server,
                "port": email_port,
                "sender_email": email_sender_address,
                "is_authentication": email_is_authentication,
                "ssl": is_enable_ssl,
                "tls": is_enable_tls
            }
        if utils.send_test_email(config, receivers_str):
            result = {"status": 200, 'info': 'send email success!'}
        else:
            result = {"status": 500, 'info': 'send email failed!'}
    except Exception as e:
        result = {"status": 500, 'info': str(e)}
    return jsonify(result)


@new_config_mold.route('/system/get_alarm_email_logs_table_data', methods=['POST'])
@readonly_permission.require(http_exception=403)
def get_alarm_email_logs_table_data():
    page_num, page_size, total_count, query_obj = utils.query_helper(general.EmailRuleLogs)
    data = []
    for email_data in query_obj:
        data.append({
            'id': email_data.id,
            'email_rule_name': email_data.email_rule_name,
            'target_sn': email_data.target_sn,
            'receivers': email_data.receivers,
            'subject': email_data.subject,
            'status': email_data.status,
            'send_time': email_data.create_time.strftime("%Y-%m-%d %H:%M:%S"),
            'email_search_key': email_data.email_search_key
        })
    return jsonify({"data": data, "page": page_num, "pageSize": page_size, "total": total_count, "status": 200})


@new_config_mold.route('/system/delete_alarm_email_logs', methods=['POST'])
@admin_permission.require(http_exception=403)
def delete_alarm_email_logs():
    try:
        info = request.get_json()
        email_log_id = info.get('emailLogId')
        session = general.general_db.get_session()
        email_rule_log = session.query(general.EmailRuleLogs).filter(general.EmailRuleLogs.id == email_log_id).first()
        if not email_rule_log:
            return jsonify({"status": 500, 'info': 'Email log is not exists!'})
        try:
            with session.begin(subtransactions=True):
                session.delete(email_rule_log)
        except Exception:
            LOG.error(traceback.format_exc())
            session.rollback()
            return jsonify({"status": 500, 'info': 'delete email log failed!'})
        return jsonify({"status": 200, 'info': 'delete email log success!'})
    except Exception as e:
        return jsonify({"status": 500, 'info': str(e)})


@new_config_mold.route('/system/view_email_detail', methods=['POST'])
@readonly_permission.require(http_exception=403)
def view_email_detail():
    try:
        info = request.get_json()
        email_log_id = info.get('emailLogId')
        session = general.general_db.get_session()
        email_rule_log = session.query(general.EmailRuleLogs).filter(general.EmailRuleLogs.id == email_log_id).first()
        if not email_rule_log:
            return jsonify({"status": 500, 'info': 'Email log is not exists!'})
        try:
            body_content = re.search(r'<body.*?>(.*?)</body>', email_rule_log.content, re.DOTALL).group(1)
            return jsonify({"status": 200, 'data': body_content})
        except Exception:
            LOG.error(traceback.format_exc())
            session.rollback()
            return jsonify({"status": 500, 'info': 'delete email log failed!'})
    except Exception as e:
        return jsonify({"status": 500, 'info': str(e)})


@new_config_mold.route('/get_quick_activation_table_data', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_quick_activation_table_data():
    """
    获取Quick Activate页面表格数据
    数据来源:
        1. SwitchParking的交换机
        2. 给SwitchParking创建SwitchConfig后，进入Switch中的带有hwid的交换机
    """
    activated_hwid_list = {
        hwid
        for license_data in licensechecker.get_license_info().get('data', [])
        for hwid, info in license_data.get('license_info', {}).items()
        if info.get('status') == 'VALID'
    }

    db_session = inven_db.get_session()
    pre_query1 = (db_session.query(
        inventory.SwitchParking.sn.label("id"),
        inventory.SwitchParking.sn.label("sn"),
        inventory.SwitchParking.hardware_id.label("hardware_id"),
        inventory.SwitchParking.model.label("model"))
                  .filter(inventory.SwitchParking.hardware_id.notin_(activated_hwid_list)))

    pre_query2 = (db_session.query(inventory.Switch.sn.label("id"),
                                   inventory.Switch.sn.label("sn"),
                                   inventory.Switch.hwid.label("hardware_id"),
                                   inventory.Switch.platform_model.label("model"))
                  .filter(inventory.Switch.status == "Configured")
                  .filter(inventory.Switch.hwid.isnot(None))
                  .filter(inventory.Switch.hwid.notin_(activated_hwid_list)))

    union_query = pre_query1.union(pre_query2).subquery()
    pre_query = db_session.query(union_query)

    page_num, page_size, total_count, q1 = utils.query_helper(union_query.c, pre_query=pre_query)

    data = [{
        "id": s.id,
        "sn": s.sn,
        "hardware_id": s.hardware_id,
        "model": s.model
    } for s in q1]

    return jsonify({"status": 200, "data": data, "total": total_count, "page": page_num, "pageSize": page_size})


@new_config_mold.route('/get_system_config_account_info', methods=['GET'])
@admin_permission.require(http_exception=403)
def get_system_config_account_info():
    """
    获取system config中的license portal账号的license信息
    目前只返回Global
    """
    db_session = inven_db.get_session()
    system_configs = (db_session.query(inventory.SystemConfig)
                      .filter(inventory.SystemConfig.config_name == constants.GLOBAL_CONFIG_TAG)
                      .options(joinedload(inventory.SystemConfig.license_account_info)).all())

    res = [{
        "system_config_name": config.config_name,
        "license_portal_username": config.license_portal_user,
        "license_portal_url": config.license_portal_url,
        "license_info": [
            {
                "expire_date": info.expire_date,
                "standard_license_count": info.standard_license_count,
                "trial_license_count": info.trial_license_count
            }
            for info in config.license_account_info
        ]
    } for config in system_configs]

    return jsonify({"status": 200, "data": res})


def refresh_single_system_config_account_info(pica8, system_config_id, db_session):
    """刷新单个system config对应的license info"""
    # 删除已有记录
    with db_session.begin():
        db_session.query(inventory.SystemConfigLicenseAccountInfo).filter_by(
            system_config_id=system_config_id).delete()

        res = pica8.get_license_account_info()
        new_infos = []
        for info in res.get('data', []):
            new_license_info = inventory.SystemConfigLicenseAccountInfo()
            new_license_info.system_config_id = system_config_id
            new_license_info.expire_date = info['expire_date']
            new_license_info.standard_license_count = info['standard']['license_count']
            new_license_info.trial_license_count = info['trial']['license_count']
            new_infos.append(new_license_info)
        db_session.add_all(new_infos)


@new_config_mold.route('/refresh_system_config_account_info', methods=['POST'])
@admin_permission.require(http_exception=403)
def refresh_system_config_account_info():
    """
    刷新global system config的license account info
    """
    db_session = inven_db.get_session()
    system_config = db_session.query(inventory.SystemConfig).filter(
        inventory.SystemConfig.config_name == constants.GLOBAL_CONFIG_TAG).scalar()

    try:
        pica8 = pica8_license().fresh(system_config.license_portal_url, user_name=system_config.license_portal_user,
                                      password=system_config.license_portal_password)
        refresh_single_system_config_account_info(pica8, system_config.id, db_session)
    except LicensePortalNetworkException:
        return jsonify({"status": 500, "info": "Abnormal network connection."})
    except LicensePortalAuthException:
        return jsonify({"status": 401})
    except Exception as e:
        LOG.error(f"refresh system config account info failed: {str(e)}")
        return jsonify({"status": 500, 'info': "The license data refresh failed."})

    return jsonify({"status": 200, "info": "The license data is refreshed successfully."})


@new_config_mold.route('/activate_ampcon_license_by_hwid', methods=['POST'])
@admin_permission.require(http_exception=403)
def activate_ampcon_license_by_hwid():
    """根据hwid_list创建ampcon license并安装"""
    data = request.get_json()
    hwid_list = data.get('hwid_list')
    expire_date = data.get('expire_date')
    license_type = data.get('license_type')

    db_session = inven_db.get_session()
    system_config = db_session.query(inventory.SystemConfig).filter(
        inventory.SystemConfig.config_name == constants.GLOBAL_CONFIG_TAG).scalar()

    if not hwid_list or license_type not in ('standard', 'trial'):
        return jsonify({"status": 500, "info": "Params is not valid"})

    pica8 = pica8_license()
    response = jsonify({"status": 200, "info": f"Activate Successful"})
    try:
        pica8.fresh(system_config.license_portal_url, user_name=system_config.license_portal_user,
                    password=system_config.license_portal_password)
        res = pica8.create_ampcon_license(exp_data=[{"expire_date": expire_date, "hwid_list": hwid_list}], license_type=license_type)
        for item in res['data']:
            install_res = licensechecker.install_license(item['license'])
            if install_res.get('status') != 200:
                LOG.error(f"install license failed, license: {item['license']}")
                raise Exception(install_res.get('msg'))

    except LicensePortalNetworkException:
        response = jsonify({"status": 500, "info": "Abnormal network connection."})
    except LicensePortalAuthException:
        response = jsonify({"status": 401})
    except Exception as e:
        LOG.error(f"Activation failed, {str(e)}")
        response = jsonify({"status": 500, "info": f"{str(e)}"})
    finally:
        try:
            refresh_single_system_config_account_info(pica8, system_config.id, db_session)
        except Exception as e:
            LOG.error(f"after activate license, refresh license account info failed, {str(e)}")

    return response


@new_config_mold.route("/export_not_activated_hwid_excel", methods=["POST"])
@admin_permission.require(http_exception=403)
def export_not_activated_hwid_excel():
    """导出选择的交换机相关列的表格"""
    req_json = request.get_json(force=True)
    labels: list[str] = req_json.get("label", [])
    data_rows: list[dict] = req_json.get("data", [])

    field_mapping = {"Model": "model", "SN": "sn", "Hardware ID": "hwid"}

    import xlwt

    def build_style(font_height_pt: float, *, is_header: bool = False) -> xlwt.XFStyle:
        font = xlwt.Font()
        font.name = "Calibri"
        font.height = int(font_height_pt * 20)  # 1/20 pt

        align = xlwt.Alignment()
        align.vert = xlwt.Alignment.VERT_CENTER
        align.wrap = 1  # 自动换行

        borders = xlwt.Borders()
        borders.left = borders.right = borders.top = borders.bottom = xlwt.Borders.THIN
        borders.left_colour = borders.right_colour = borders.top_colour = borders.bottom_colour = BORDER_GREY_IDX

        style = xlwt.XFStyle()
        style.font = font
        style.alignment = align
        style.borders = borders

        if is_header:
            pattern = xlwt.Pattern()
            pattern.pattern = xlwt.Pattern.SOLID_PATTERN
            pattern.pattern_fore_colour = HEADER_GREY_IDX
            style.pattern = pattern
        return style

    wb = xlwt.Workbook(encoding="utf-8")
    HEADER_GREY_IDX = 0x21
    BORDER_GREY_IDX = 0x22
    wb.set_colour_RGB(HEADER_GREY_IDX, 179, 179, 179)
    wb.set_colour_RGB(BORDER_GREY_IDX, 154, 154, 154)
    ws = wb.add_sheet("license-create-template")

    header_style = build_style(10.5, is_header=True)
    data_style = build_style(8.25)
    col_width_map = {
        "Model": int(13.29 * 256),
        "SN": int(13.29 * 256),
        "Hardware ID": int(23.29 * 256),
    }
    for col_idx, label in enumerate(labels):
        ws.col(col_idx).width = col_width_map.get(label, 10 * 256)
    for col_idx, label in enumerate(labels):
        ws.write(0, col_idx, label, header_style)
    for row_idx, row in enumerate(data_rows, start=1):
        for col_idx, label in enumerate(labels):
            ws.write(row_idx, col_idx, row.get(field_mapping[label]), data_style)

    buf = io.BytesIO()
    wb.save(buf)
    buf.seek(0)
    return send_file(
        buf,
        mimetype="application/vnd.ms-excel",
        as_attachment=True,
        download_name="license-create-template.xls",
    )


@new_config_mold.route('/check_license_type', methods=['GET'])
@admin_permission.require(http_exception=403)
def check_license_type():
    """
    检查已安装license type
    type: [none, trial, standard]
    """
    res = 'none'
    try:
        license_data = licensechecker.get_license_info().get('data', [])
        if not license_data:
            return jsonify({"status": 200, "data": "none"})
        res = license_data[0].get("license_type")
    except Exception as e:
        LOG.error(f"check license type failed, {str(e)}")
    return jsonify({"status": 200, "data": res})


@new_config_mold.route('/get_license_convert_switch', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_license_convert_switch():
    db_session = inven_db.get_session()
    query_switches = utils.query_helper_without_page(inventory.Switch, pre_query=db_session.query(inventory.Switch)
                                                     .filter(inventory.Switch.status.in_(['Provisioning Success', 'Imported'])))
    data = [{
        "id": switch.id,
        "platform_model": switch.platform_model,
        "sn": switch.sn,
        "hwid": switch.hwid
    } for switch in query_switches]
    return jsonify({"data": data, "status": 200})


@new_config_mold.route('/convert_trial_license', methods=['POST'])
@admin_permission.require(http_exception=403)
def convert_trial_license():
    """
    trial license转换为standard license
    流程：
        1. 给hwid_list创建standard license
        2. 查询要转换的hwid对应的switch，并与当前会话解绑
        3. 删除所有switch，并开始安装license
        4. 安装完成后，重新插入待转换的switch
    """
    switch_list = request.get_json()
    db_session = inven_db.get_session()
    system_config = db_session.query(inventory.SystemConfig).filter(
        inventory.SystemConfig.config_name == constants.GLOBAL_CONFIG_TAG).scalar()

    pica8 = pica8_license()
    exp_map = {}
    res = jsonify({"status": 200, "info": f"Convert Successful"})
    try:
        pica8.fresh(system_config.license_portal_url, user_name=system_config.license_portal_user,
                    password=system_config.license_portal_password)
        for s in switch_list:
            if not exp_map.get(s['expire_date']):
                exp_map[s['expire_date']] = [s['hwid']]
            else:
                exp_map[s['expire_date']].append(s['hwid'])

        exp_list = [
            {'expire_date': expire_date, 'hwid_list': hwid_list}
            for expire_date, hwid_list in exp_map.items()
        ]
        license_response = pica8.create_ampcon_license(exp_data=exp_list, license_type="standard")

        # 获取到license后，先清除未被选中的交换机
        hwid_list = []
        license_list = []

        for item in license_response['data']:
            hwid_list.extend(item['hwid_list'])
            license_list.append(item['license'])

        existing_switches = db_session.query(inventory.Switch).filter(
            inventory.Switch.hwid.in_(hwid_list)
        ).all()

        # 解绑旧session
        for switch in existing_switches:
            db_session.expunge(switch)
            make_transient(switch)
            switch.id = None

        with db_session.begin():
            db_session.query(inventory.Switch).delete()
            for license_str in license_list:
                install_res = licensechecker.install_license(license_str)
                if install_res.get('status') != 200:
                    LOG.error(f"install license failed, license: {license_str}")
                    raise Exception(install_res.get('msg'))

            for switch in existing_switches:
                db_session.add(switch)
    except LicensePortalNetworkException:
        res = jsonify({"status": 500, "info": "Abnormal network connection."})
    except LicensePortalAuthException:
        res = jsonify({"status": 401})
    except Exception as e:
        LOG.error(f"Convert failed, {str(e)}")
        res = jsonify({"status": 500, "info": f"{str(e)}"})
    finally:
        try:
            refresh_single_system_config_account_info(pica8, system_config.id, db_session)
        except Exception as e:
            LOG.error(f"after convert trial license, refresh license account info failed, {str(e)}")

    return res


@new_config_mold.route('/html_to_tar', methods=["POST"])
def html_to_tar():
    """
    将HTML文本保存为文件，并将其打包成tar格式的Base64字符串。
    """
    params = request.get_json()
    data = params.get("data", [])
    try:
        with tempfile.TemporaryDirectory() as tmpdirname:
            tar_bytes = io.BytesIO()
            with tarfile.open(fileobj=tar_bytes, mode="w:") as tar:
                for d in data:
                    html = d.get("html", "")
                    filename = d.get("filename", "")
                    file_path = os.path.join(tmpdirname, filename)
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(html)
                    tar.add(file_path, arcname=filename)
            tar_content = tar_bytes.getvalue()
            tar_base64 = base64.b64encode(tar_content).decode("utf-8")
        return jsonify({'status': 200, 'data': tar_base64})
    except Exception as e:
        return jsonify({'status': 500, 'info': 'Error: {}'.format(e)})


@new_config_mold.route('/tar_to_html', methods=["POST"])
def tar_to_html():
    """
    将Base64编码的tar文件内容解压，返回第一个文件的文件名和内容。
    """
    params = request.get_json()
    tar_base64 = params.get("data", "")
    try:
        tar_content = base64.b64decode(tar_base64)
        tar_bytes = io.BytesIO(tar_content)
        with tarfile.open(fileobj=tar_bytes, mode="r:") as tar:
            first_member = tar.next()
            if first_member is None:
                return jsonify({'status': 500, 'info': 'Tar file is empty'})

            file_content = tar.extractfile(first_member).read().decode('utf-8')
            return jsonify({
                'status': 200,
                'data': {
                    'filename': first_member.name,
                    'content': file_content
                }
            })
    except Exception as e:
        return jsonify({'status': 500, 'info': 'Error: {}'.format(e)})


@new_config_mold.route('/tar_append_2html', methods=["POST"])
def tar_append_2html():
    """
    将Base64编码的tar文件中追加两个html文件：allow.html和connected.html。
    """
    params = request.get_json()
    tar_base64 = params.get("data", "")
    try:
        new_tar_str = wireless_util.tar_append_2html(tar_base64)
        return jsonify({
            'status': 200,
            'data': new_tar_str
        })
    except Exception as e:
        return jsonify({'status': 500, 'info': 'Error: {}'.format(e)})
