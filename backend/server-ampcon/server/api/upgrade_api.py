import json
import logging
import os
import ssl
import urllib.request
import uuid
from datetime import datetime, timedelta

import flask_login
from flask import Blueprint, request, jsonify, send_file
from sqlalchemy import desc

from server import constants
from server.celery_app.automation_task import AmpConBaseTask, beat_task
from server.celery_app.beat_task import beat_upgrade_firmware_task
from server.db.models import upgrade, inventory, automation
from server.util import http_client, osutil, utils
from server.util.permission import super_user_permission, admin_permission

inven_db = inventory.inven_db
LOG = logging.getLogger(__name__)
upgrade_mold = Blueprint("upgrade_mold", __name__, template_folder='templates')
WIRELESS_MODEL_LIST = None


def add_image(*args):
    image_name, model, version, revision, image_path, image_md5_path = args
    session = inven_db.get_session()
    image_name_objs = session.query(upgrade.DeviceImage).with_entities(upgrade.DeviceImage.image_name).all()
    image_name_list = [i[0] for i in image_name_objs]

    MAX_LENGTH = 32  # 统一最大长度
    # 字段长度校验
    invalid_fields = []
    if version and len(version) > MAX_LENGTH:
        invalid_fields.append("version")
    if revision and len(revision) > MAX_LENGTH:
        invalid_fields.append("revision")

    if invalid_fields:
        raise ValueError(
            f"{' and '.join(field.capitalize() for field in invalid_fields)} input error. "
            f"Please enter a valid format (maximum {MAX_LENGTH} characters each)."
        )
    if image_name in image_name_list:
        raise ValueError("This image existed, Please check")
    with session.begin(subtransactions=True):
        upload_time = datetime.now()
        new_image_obj = upgrade.DeviceImage()
        new_image_obj.image_name = image_name
        new_image_obj.model = model
        new_image_obj.version = version
        new_image_obj.revision = revision
        new_image_obj.image_path = image_path
        new_image_obj.image_md5_path = image_md5_path
        new_image_obj.create_time = new_image_obj.modified_time = upload_time
        session.add(new_image_obj)


def download_img(url, tmp_path=None, proxies=None, headers=None, return_status=False):
    ssl._create_default_https_context = ssl._create_unverified_context
    if headers:
        down_req = urllib.request.Request(url, headers=headers)
    else:
        down_req = urllib.request.Request(url)
    try:
        if proxies:
            httpproxy_handler = urllib.request.ProxyHandler(proxies)
            opener = urllib.request.build_opener(httpproxy_handler)
            u_stream = opener.open(down_req, timeout=30)
        else:
            u_stream = urllib.request.urlopen(down_req, timeout=30)
        if return_status:
            return u_stream.read()
        osutil.ensure_path(os.path.dirname(tmp_path))
        with open(tmp_path, 'wb') as f:
            block_sz = 8192
            while True:
                image_buffer = u_stream.read(block_sz)
                if not image_buffer:
                    break
                f.write(image_buffer)
    except urllib.request.URLError as e:
        if hasattr(e, "code") and e.code == 401:
            raise ValueError("Authentication failed")
        if hasattr(e, "code") and e.code == 404:
            raise ValueError("Download file not found")
        else:
            raise ValueError("Download failed")


@upgrade_mold.route('/image/action/upload', methods=['POST'])
@super_user_permission.require(http_exception=403)
def upload_image():
    msg = {'status': 200, 'info': 'Upload success.'}
    extra_info = request.form
    # upload_type = int(extra_info.get("upload_type", ""))
    upload_type_str = ""
    if request.content_type and request.content_type.startswith("multipart/form-data"):
        upload_type_str = request.form.get("upload_type", "").strip()
    elif request.is_json:
        upload_type_str = request.json.get("upload_type", "").strip()
    else:
        upload_type_str = ""

    upload_type = int(upload_type_str or 0)
    try:
        if upload_type == 1:
            img_file = request.files['file']
            image_name = img_file.filename
            image_path = "img/wireless/%s" % image_name
            model = extra_info.get("model", "")
            version = extra_info.get("version", "")
            revision = extra_info.get("revision", "")
            osutil.ensure_path(os.path.dirname(image_path))
            img_file.save(image_path)

            # img_md5_file = request.files.get('md5File', '')
            image_md5_path = ""
            # if img_md5_file:
            #     image_md5_name = img_md5_file.filename
            #     image_md5_path = "img/%s" % image_md5_name
            #     osutil.ensure_path(os.path.dirname(image_md5_path))
            #     img_md5_file.save(image_md5_path)
            #     http_client.start_transfer_file(flask_login.current_user.id, [
            #         {'filename': image_md5_name, 'path': image_md5_path, 'dest': image_md5_path}])

            # add records
            add_image(image_name, model, version, revision, image_path, image_md5_path)

            # sync with other server
            http_client.start_transfer_file(flask_login.current_user.id,
                                            [{'filename': image_name, 'path': image_path, 'dest': image_path}])

        elif upload_type == 2:
            image_link_info = request.get_json()
            if image_link_info:
                image_link = image_link_info.get("imageLink", "")
                # image_md5_link = image_link_info.get("imageMd5Link", "")
                model = image_link_info.get("model", "")
                version = image_link_info.get("version", "")
                revision = image_link_info.get("revision", "")
                # push file
                path_list = [image_link]
                add_path_list = ["img/wireless/%s" % image_link.split("/")[-1],
                                 ""]
                for url in path_list:
                    if url:
                        tmp_path = "img/wireless/%s" % url.split("/")[-1]
                        download_img(url, tmp_path)
                        # sync with other server
                        http_client.start_transfer_file(flask_login.current_user.id,
                                                        [{'filename': url.split("/")[-1], 'path': tmp_path,
                                                          'dest': tmp_path}])
                # add records
                add_image(image_link.split("/")[-1], model, version, revision, *add_path_list)
        else:
            pass
            # image_infos = request.get_json()
            # latest_image_list = image_infos.get("latest_image_name", [])
            # if latest_image_list:
            #     for image_tmp in latest_image_list:
            #         image_name = image_tmp.split("/")[-1]
            #         v_dict = utils.get_image_info(image_name)
            #         if v_dict:
            #             version = v_dict['version']
            #             revision = v_dict['revision']
            #             platform = v_dict.get('platform')

            #             # push file on background
            #             task_name = f"download_latest_img_{image_name}"
            #             jobs = AmpConBaseTask.get_running_jobs()
            #             if jobs:
            #                 for job in jobs:
            #                     if task_name in job.task_name:
            #                         raise ValueError("The Task is running..")
            #             download_latest_img.delay(image_tmp, "img/{}".format(image_name), image_name, platform, version,
            #                                       revision, celery_task_name=task_name)
            #         else:
            #             raise ValueError("Can not resolve Image Url")
            # msg = {'status': 200,
            #        'info': 'The download image task is running in the background, please wait for the download to complete.'}
    except ValueError as v:
        msg = {'status': 500, 'info': str(v)}
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    finally:
        return jsonify(msg)


# get upgradeimage data
@upgrade_mold.route('/images', methods=['POST'])
@admin_permission.require(http_exception=403)
def view_image():
    page_num, page_size, total_count, query_obj = utils.query_helper(upgrade.DeviceImage)
    response = {
        "data": [device_image.make_dict() for device_image in query_obj],
        "page": page_num,
        "pageSize": page_size,
        "total": total_count,
        "status": 200
    }
    return jsonify(response)


# delete image
@upgrade_mold.route('/image/<int:id>', methods=['DELETE'])
@super_user_permission.require(http_exception=403)
def delete_image(id):
    try:
        session = inven_db.get_session()
        with session.begin(subtransactions=True):
            device_image_obj = session.query(upgrade.DeviceImage).filter(upgrade.DeviceImage.id == id).first()
            if not device_image_obj:
                return jsonify({'status': 404, 'info': 'Image not found'})
            upgrade_status = session.query(upgrade.DeviceLatestUpgradeStatus).filter(
                upgrade.DeviceLatestUpgradeStatus.image_id == id,
                upgrade.DeviceLatestUpgradeStatus.upgrade_status.in_([3, 4]))
            if upgrade_status.count():
                return jsonify({'status': 500, 'info': 'The image cannot be deleted while in use'})
            image_path = os.path.realpath(os.path.join(constants.AMPCON_BASE_DIR, device_image_obj.image_path))
            if os.path.exists(image_path) and image_path.startswith(constants.AMPCON_BASE_DIR):
                os.remove(image_path)
            session.delete(device_image_obj)
    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})
    else:
        return jsonify({'status': 200, 'info': 'Image deleted successfully'})


@upgrade_mold.route("/models", methods=["GET"])
def query_wireless_model():
    global WIRELESS_MODEL_LIST
    if WIRELESS_MODEL_LIST is not None:
        return jsonify({'status': 200, 'info': WIRELESS_MODEL_LIST})
    else:
        try:
            with open(f'{constants.AUTOMATION_BASE_DIR}/server/wireless/support_model.json', 'r', encoding='utf-8') as file:
                WIRELESS_MODEL_LIST = json.load(file)
                print(f"wireless model:{WIRELESS_MODEL_LIST}")
        except (FileNotFoundError, json.JSONDecodeError, Exception) as e:
            print(f"read wireless model exception: {e}")
            return jsonify({"status": 500, "info": "No wireless model found"})
    return jsonify({'status': 200, 'info': WIRELESS_MODEL_LIST})


@upgrade_mold.route('/image/action/download/<string:filename>', methods=['GET'])
def download_image(filename):
    image_path = "img/wireless/%s" % filename
    return send_file(image_path, as_attachment=True)


@upgrade_mold.route('/status', methods=['POST'])
def set_upgrade_status():
    info = json.loads(request.data)
    sn = info.get('sn')
    status = info.get('status')
    if sn and str(status):
        try:
            status = int(status)
            session = inven_db.get_session()
            with session.begin(subtransactions=True):
                obj = session.query(upgrade.DeviceLatestUpgradeStatus).filter(upgrade.DeviceLatestUpgradeStatus.sn == sn).first()
                if not obj:
                    return jsonify({'status': 500, 'info': 'sn can not find record'})
                obj.upgrade_status = status

                # log
                log_obj = upgrade.DeviceuUgradeOperationLog()
                log_obj.sn = sn
                log_info = {
                    1: 'upgrade successful',
                    2: 'upgrade failed',
                    3: 'upgrade scheduled',
                    4: 'upgrading'
                }.get(status, '--')
                if obj.image:
                    image_name = obj.image.image_name
                else:
                    image_name = ''
                log_obj.log_info = '{} {}'.format(image_name, log_info)
                session.add(log_obj)
            response = {'status': 200, 'info': 'Update upgrade status success'}
        except Exception as e:
            response = {'status': 500, 'info': str(e)}
    else:
        response = {'status': 500, 'info': 'sn or status empty'}
    return jsonify(response)


@upgrade_mold.route('/status', methods=['GET'])
@admin_permission.require(http_exception=403)
def get_upgrade_status():
    sn = request.args.get('sn')
    if sn:
        try:
            session = inven_db.get_session()
            sn_list = sn.split(',')
            objs = session.query(upgrade.DeviceLatestUpgradeStatus).filter(upgrade.DeviceLatestUpgradeStatus.sn.in_(sn_list)).all()
            response = [obj.make_dict() for obj in objs]
        except Exception as e:
            response = {'status': 500, 'info': str(e)}
    else:
        response = {'status': 500, 'info': 'sn empty'}
    return jsonify(response)


@upgrade_mold.route('/operation_log', methods=['POST'])
@admin_permission.require(http_exception=403)
def add_operation_log():
    info = json.loads(request.data)
    sn = info.get('sn')
    log_info = info.get('log_info')

    if sn and log_info:
        try:
            session = inven_db.get_session()
            with session.begin(subtransactions=True):
                obj = upgrade.DeviceuUgradeOperationLog()
                obj.sn = sn
                obj.log_info = log_info
                session.add(obj)
            response = {'status': 200, 'info': 'Add upgrade operation log success'}
        except Exception as e:
            response = {'status': 500, 'info': str(e)}
    else:
        response = {'status': 500, 'info': 'sn or log_info empty'}
    return jsonify(response)


@upgrade_mold.route('/<string:sn>/logs', methods=['GET'])
@admin_permission.require(http_exception=403)
def get_operation_log(sn):
    if sn:
        try:
            session = inven_db.get_session()
            ninety_days_ago = datetime.now() - timedelta(days=90)
            objs = session.query(upgrade.DeviceuUgradeOperationLog).filter(
                upgrade.DeviceuUgradeOperationLog.sn == sn,
                upgrade.DeviceuUgradeOperationLog.create_time >= ninety_days_ago
            ).order_by(desc(upgrade.DeviceuUgradeOperationLog.create_time)).all()
            info = ['{} {}'.format(obj.create_time, obj.log_info) for obj in objs]
            response = {'status': 200, 'info': info}
        except Exception as e:
            response = {'status': 500, 'info': str(e)}
    else:
        response = {'status': 500, 'info': 'sn empty'}
    return jsonify(response)


@upgrade_mold.route('/task/submit', methods=['POST'])
@admin_permission.require(http_exception=403)
def task_submit():
    info = json.loads(request.data)
    sn_list = info.get('sn_list', [])
    image_id = info.get('image_id')
    upgrade_type = info.get('upgrade_type')
    upgrade_time = info.get('upgrade_time')

    if sn_list and image_id and str(upgrade_type):
        try:
            upgrade_type = int(upgrade_type)
            session = inven_db.get_session()
            with session.begin(subtransactions=True):
                image = session.query(upgrade.DeviceImage).filter(upgrade.DeviceImage.id == image_id).first()
                if not image:
                    return jsonify({'status': 500, 'info': 'image can not find record'})
                for sn in sn_list:
                    device_status = session.query(upgrade.DeviceLatestUpgradeStatus).filter(
                        upgrade.DeviceLatestUpgradeStatus.sn == sn).first()
                    if not device_status:
                        device_status = upgrade.DeviceLatestUpgradeStatus()
                        device_status.sn = sn
                    else:
                        # 设备正在执行计划升级中，用户又推送了新的升级计划，未执行的原计划要删除
                        if device_status.upgrade_job_name and device_status.upgrade_status == 3:
                            beat_task.remove_job(device_status.upgrade_job_name)
                            AmpConBaseTask.kill_process_by_task_name(device_status.upgrade_job_name)
                            automation.automation_db.delete_ansible_job(device_status.upgrade_job_name)
                    device_status.upgrade_type = upgrade_type
                    device_status.image_id = image_id
                    # 立即升级
                    if upgrade_type == 0:
                        device_status.upgrade_job_name = None
                        device_status.upgrade_time = None
                        device_status.upgrade_status = 4
                        session.add(device_status)
                        log_info = 'set upgrade immediately'
                    # 计划升级
                    elif upgrade_type == 1:
                        if not upgrade_time:
                            return jsonify({'status': 500, 'info': 'upgrade_time empty'})
                        dt = datetime.strptime(upgrade_time, '%Y-%m-%d %H:%M:%S')
                        job_schedule = '{} seconds'.format((dt - datetime.now()).seconds)
                        job_name = '{}_{}'.format(sn, uuid.uuid1())
                        beat_task.add_job(job_name, 'beat_upgrade_firmware_task',
                                          job_type='interval',
                                          job_schedule=job_schedule,
                                          once=True,
                                          kwargs={'celery_type': 'INTERVAL', 'sn': sn, 'image_name': image.image_name},
                                          # start_time=upgrade_time,
                                          job_desc="set upgrade firmware task")
                        device_status.upgrade_job_name = job_name
                        device_status.upgrade_time = upgrade_time
                        device_status.upgrade_status = 3
                        session.add(device_status)
                        log_info = 'set upgrade {}'.format(upgrade_time)
                    else:
                        return jsonify({'status': 500, 'info': 'upgrade_type error'})
                    # log
                    log_obj = upgrade.DeviceuUgradeOperationLog()
                    log_obj.sn = sn
                    log_obj.log_info = log_info
                    session.add(log_obj)
            # 立即升级，需要等更新状态事务结束再调用异步接口
            if upgrade_type == 0:
                for sn in sn_list:
                    beat_upgrade_firmware_task.delay(sn=sn, image_name=image.image_name, immediately=True)
            response = {'status': 200, 'info': 'Submit batch success'}
        except Exception as e:
            response = {'status': 500, 'info': str(e)}
    else:
        response = {'status': 500, 'info': 'sn_list or image_id or upgrade_type empty'}
    return jsonify(response)


@upgrade_mold.route('/schedule_task', methods=['DELETE'])
@admin_permission.require(http_exception=403)
def schedule_task():
    info = json.loads(request.data)
    sn = info.get('sn')
    if sn:
        try:
            session = inven_db.get_session()
            with session.begin(subtransactions=True):
                obj = session.query(upgrade.DeviceLatestUpgradeStatus).filter(
                    upgrade.DeviceLatestUpgradeStatus.sn == sn).first()
                if not obj or not obj.upgrade_job_name:
                    return jsonify({'status': 500, 'info': 'sn can not find upgrade schedule'})
                if obj.upgrade_status != 3:
                    return jsonify({'status': 500, 'info': 'upgrade schedule already done'})
                log_info = 'stop upgrade schedule {}'.format(obj.upgrade_time)
                beat_task.remove_job(obj.upgrade_job_name)
                AmpConBaseTask.kill_process_by_task_name(obj.upgrade_job_name)
                automation.automation_db.delete_ansible_job(obj.upgrade_job_name)
                response = {'status': 200, 'info': 'Cancel upgrade schedule task success'}
                # 重置状态
                obj.upgrade_time = None
                obj.upgrade_status = 0
                obj.upgrade_job_name = None
                obj.upgrade_type = 2
                obj.image_id = None
                # log
                log_obj = upgrade.DeviceuUgradeOperationLog()
                log_obj.sn = sn
                log_obj.log_info = log_info
                session.add(log_obj)
        except Exception as e:
            response = {'status': 500, 'info': str(e)}
    else:
        response = {'status': 500, 'info': 'sn empty'}
    return jsonify(response)
