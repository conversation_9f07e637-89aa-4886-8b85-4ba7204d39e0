import json
import logging
import traceback
import threading
import ipaddress
from flask import Blueprint, jsonify, Response, request
from datetime import timedelta, datetime, date

from server.util.permission import admin_permission, readonly_permission
from server.util.utils import is_name_valid
from server.db.models.resource_pool import resource_pool_vni_db, resource_pool_asn_db,resource_pool_area_db,resource_pool_ip_db,is_ranges_conflict, ResourcePoolVni
from server.db.models import inventory


resource_pool_blueprint_mold = Blueprint("resource_pool_blueprint", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)


# For vni pool
@resource_pool_blueprint_mold.route("/resource_pool_vni_table_data", methods=["POST"])
@readonly_permission.require(http_exception=403)
def resource_pool_vni_table_data():
    try:
        return resource_pool_vni_db.query_all_resource_pool()
    except Exception as e:
        LOG.error(f"resource_pool_vni_table_data error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to get resource pool data'})
    
@resource_pool_blueprint_mold.route("/delete_vni_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_pool_vni_record():
    try:
        data = request.get_json()
        if resource_pool_vni_db.delete_resource_pool_by_id(data["id"]):
            return jsonify({'status': 200, 'info': 'Delete resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})
    except Exception as e:
        LOG.error(f"delete_pool_vni_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})
    
@resource_pool_blueprint_mold.route("/add_vni_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def add_vni_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        use = data.get('use')
        fabric_names = data.get('fabric')
        if not name or not is_name_valid(name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if resource_pool_vni_db.query_resource_pool_by_name(name).first():
            return jsonify({'status': 500, 'info': 'Resource pool already exists'})
        if len(ranges) == 0:
            return jsonify({'status': 500, 'info': 'Ranges is empty'})
        if not resource_pool_vni_db.is_vni_ranges_valid(ranges):
            return jsonify({'status': 500, 'info': 'VNI ranges must be between 1 and 16777215'})
        if is_ranges_conflict(ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_vni_db.exceeds_max_range_count(ranges):
            return jsonify({'status': 500, 'info': 'The number of resource pool ranges cannot exceed 1024'})
        is_vni_range_conflict, conflict_pool_names = resource_pool_vni_db.check_vni_ranges_conflict(ranges)
        if is_vni_range_conflict:
            return jsonify({'status': 500, 'info': f'VNI ranges conflict with existing pools: {", ".join(conflict_pool_names)}'})
        if resource_pool_vni_db.add_resource_pool(name, ranges, use=use) and add_vni_fabric_mapping(name, fabric_names):
            return jsonify({'status': 200, 'info': 'Add resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_vni_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    
def add_vni_fabric_mapping(pool_name, fabric_names):
    try:
        session = inventory.inven_db.get_session()
        pool = session.query(ResourcePoolVni).filter_by(name=pool_name).first()
        if not pool:
            LOG.error(f"Resource pool {pool_name} not found")
            return False
        with session.begin():
            for fabric_name in fabric_names:
                fabric = session.query(inventory.Fabric).filter_by(fabric_name=fabric_name).first()
                if not fabric:
                    LOG.error(f"Fabric with name {fabric_name} not found")
                    continue
                mapping = inventory.FabricVniMapping(fabric_id=fabric.id, vni_id=pool.id)
                session.add(mapping)
        session.close()
        return True
    except Exception as e:
        LOG.error(f"add_vni_fabric_mapping error: {e}")
        return False

@resource_pool_blueprint_mold.route("/edit_vni_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_vni_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        raw_ranges = data.get('rawRanges')
        pool_id = data.get('poolId')
        use = data.get('use')
        if not resource_pool_vni_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if not name or not is_name_valid(name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if not resource_pool_vni_db.is_vni_ranges_valid(raw_ranges):
            return jsonify({'status': 500, 'info': 'VNI ranges must be between 1 and 16777215'})
        add_ranges = ranges.get('add', [])
        modify_ranges = ranges.get('modify', [])
        if is_ranges_conflict(add_ranges + modify_ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        is_vni_range_conflict, conflict_pool_names = resource_pool_vni_db.check_vni_ranges_conflict(raw_ranges, pool_id)
        if is_vni_range_conflict:
            return jsonify({'status': 500, 'info': f'VNI ranges conflict with existing pools: {", ".join(conflict_pool_names)}'})
        if resource_pool_vni_db.exceeds_max_range_count(raw_ranges,pool_id=pool_id):
            return jsonify({'status': 500, 'info': 'The number of resource pool ranges cannot exceed 1024'})
        if resource_pool_vni_db.edit_resource_pool(pool_id, name, ranges, use=use) and edit_vni_fabric_mapping(pool_id, data.get('fabric')):
            return jsonify({'status': 200, 'info': 'Edit resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to edit resource pool'})
    except ValueError as v:
        LOG.error(f"edit_vni_pool error: {v}")
        return jsonify({'status': 500, 'info': f'Failed to edit resource pool: {v}'})
    except Exception as e:
        LOG.error(f"edit_vni_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to edit resource pool'})

def edit_vni_fabric_mapping(pool_id, fabric_names):
    try:
        session = inventory.inven_db.get_session()
        pool = session.query(ResourcePoolVni).filter_by(id=pool_id).first()
        if not pool:
            LOG.error(f"Resource pool with id {pool_id} not found")
            return False
        # Clear existing mappings
        session.query(inventory.FabricVniMapping).filter_by(vni_id=pool.id).delete()
        # Add new mappings
        with session.begin():
            for fabric_name in fabric_names:
                fabric = session.query(inventory.Fabric).filter_by(fabric_name=fabric_name).first()
                if not fabric:
                    LOG.error(f"Fabric with name {fabric_name} not found")
                    continue
                mapping = inventory.FabricVniMapping(fabric_id=fabric.id, vni_id=pool.id)
                session.add(mapping)
        session.close()
        return True
    except Exception as e:
        LOG.error(f"edit_vni_fabric_mapping error: {e}")
        return False

@resource_pool_blueprint_mold.route("/clone_vni_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def clone_vni_pool():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        new_pool_name = data.get('poolName')
        if not new_pool_name or not is_name_valid(new_pool_name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if not resource_pool_vni_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_vni_db.clone_resource_pool(pool_id, new_pool_name):
            return jsonify({'status': 200, 'info': 'Clone resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to clone resource pool'})
    except Exception as e:
        LOG.error(f"add_vni_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to clone resource pool'})

@resource_pool_blueprint_mold.route("/generate_vni_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def generate_vni_pool_record():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if record_num <= 0:
            return jsonify({'status': 500, 'info': 'Invalid record number'})
        if not resource_pool_vni_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_vni_db.generate_resource_from_pool(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Generate resource pool record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to generate resource pool record'})
    except Exception as e:
        LOG.error(f"add_vni_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to generate resource pool record'})
    
@resource_pool_blueprint_mold.route("/delete_first_ten_record_in_vni", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_first_ten_record_in_vni():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if resource_pool_vni_db.delete_first_n_used_record(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Delete first ten record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})
    except Exception as e:
        LOG.error(f"delete_first_ten_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})

@resource_pool_blueprint_mold.route("/get_vni_fabric_list/<int:vniID>", methods=["POST"])
@readonly_permission.require(http_exception=403)
def get_vni_fabric_list(vniID):
    try:
        session = inventory.inven_db.get_session()
        if not vniID:
            used_fabric_ids = list(set(map(lambda mapping: mapping.fabric_id, session.query(inventory.FabricVniMapping).all())))
        else:
            used_fabric_ids = list(set(map(lambda mapping: mapping.fabric_id, session.query(inventory.FabricVniMapping).filter(inventory.FabricVniMapping.vni_id != vniID).all())))
        fabric_list = session.query(inventory.Fabric).filter(inventory.Fabric.id.notin_(used_fabric_ids)).all()
        session.close()
        fabric_dropdown_list = [{'id': fabric.id, 'name': fabric.fabric_name} for fabric in fabric_list]
        return jsonify({'status': 200, 'data': fabric_dropdown_list})
    except Exception as e:
        LOG.error(f"get_vni_fabric_list error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to get vni fabric list'})

@resource_pool_blueprint_mold.route("/resource_pool_asn_table_data", methods=["POST"])
@readonly_permission.require(http_exception=403)
def resource_pool_asn_table_data():
    try:
        return resource_pool_asn_db.query_all_resource_pool()
    except Exception as e:
        LOG.error(f"resource_pool_asn_table_data error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to get resource pool data'})


@resource_pool_blueprint_mold.route("/delete_asn_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_pool_asn_record():
    try:
        data = request.get_json()
        if resource_pool_asn_db.delete_resource_pool_by_id(data["id"]):
            return jsonify({'status': 200, 'info': 'Delete resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})
    except Exception as e:
        LOG.error(f"delete_pool_asn_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})


@resource_pool_blueprint_mold.route("/add_asn_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def add_asn_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        if not name or not is_name_valid(name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if resource_pool_asn_db.query_resource_pool_by_name(name).first():
            return jsonify({'status': 500, 'info': 'Resource pool already exists'})
        if len(ranges) == 0:
            return jsonify({'status': 500, 'info': 'Ranges is empty'})
        if is_ranges_conflict(ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_asn_db.add_resource_pool(name, ranges):
            return jsonify({'status': 200, 'info': 'Add resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_asn_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/edit_asn_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_asn_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        pool_id = data.get('poolId')
        if not resource_pool_asn_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if not name or not is_name_valid(name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        add_ranges = ranges.get('add', [])
        modify_ranges = ranges.get('modify', [])
        if is_ranges_conflict(add_ranges + modify_ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_asn_db.edit_resource_pool(pool_id, name, ranges):
            return jsonify({'status': 200, 'info': 'Edit resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to edit resource pool'})
    except ValueError as v:
        LOG.error(f"add_asn_pool error: {v}")
        return jsonify({'status': 500, 'info': f'Failed to edit resource pool: {v}'})
    except Exception as e:
        LOG.error(f"add_asn_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to edit resource pool'})


@resource_pool_blueprint_mold.route("/clone_asn_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def clone_asn_pool():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        new_pool_name = data.get('poolName')
        if not new_pool_name or not is_name_valid(new_pool_name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if not resource_pool_asn_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_asn_db.clone_resource_pool(pool_id, new_pool_name):
            return jsonify({'status': 200, 'info': 'Clone resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to clone resource pool'})
    except Exception as e:
        LOG.error(f"add_asn_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to clone resource pool'})


@resource_pool_blueprint_mold.route("/generate_asn_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def generate_asn_pool_record():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if record_num <= 0:
            return jsonify({'status': 500, 'info': 'Invalid record number'})
        if not resource_pool_asn_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        res = resource_pool_asn_db.generate_resource_from_pool(pool_id, record_num)
        if res:
            return jsonify({'status': 200, 'info': 'Generate resource pool record successfully', 'data': res})
        else:
            return jsonify({'status': 500, 'info': 'Failed to generate resource pool'})
    except Exception as e:
        LOG.error(f"add_asn_pool error: {e}")
        return jsonify({'status': 500, 'info': f'Failed to generate resource pool: {e}'})
    

@resource_pool_blueprint_mold.route("/delete_record_in_asn_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_record_in_asn_pool():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        ip_records = data.get('record')
        records = []
        for record in ip_records:
            try:
                records.append(int(ipaddress.IPv4Address(record)))
            except ipaddress.AddressValueError:
                continue
        if resource_pool_asn_db.delete_record_by_value(pool_id, records):
            return jsonify({'status': 200, 'info': 'Delete record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete record'})
    except Exception as e:
        LOG.error(f"delete_first_ten_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete record'})


# todo just for test
@resource_pool_blueprint_mold.route("/delete_first_ten_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_first_ten_record():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if resource_pool_asn_db.delete_first_n_used_record(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Delete first ten record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})
    except Exception as e:
        LOG.error(f"delete_first_ten_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})


@resource_pool_blueprint_mold.route("/get_resource_pool_dropdown_list", methods=["POST"])
@readonly_permission.require(http_exception=403)
def get_resource_pool_dropdown_list():
    try:
        data = request.get_json()
        pool_type_list = data.get('poolTypeList')
        res_dropdown_list = {}
        for pool_type in pool_type_list:
            if pool_type == 'asn':
                asn_pool_data_list = resource_pool_asn_db.query_resource_pool_data_list()
                res_dropdown_list['asn'] = [{
                    'id': asn_pool_data.id,
                    'name': asn_pool_data.name,
                } for asn_pool_data in asn_pool_data_list]
            # todo add other pool type
            elif pool_type == 'ipv4':
                ip_pool_data_list = resource_pool_ip_db.query_resource_pool_data_list()
                res_dropdown_list['ipv4'] = [{
                    'id': ip_pool_data.id,
                    'name': ip_pool_data.name,
                } for ip_pool_data in ip_pool_data_list]
            elif pool_type == 'area':
                area_pool_data_list = resource_pool_area_db.query_resource_pool_data_list()
                res_dropdown_list['area'] = [{
                    'id': area_pool_data.id,
                    'name': area_pool_data.name,
                } for area_pool_data in area_pool_data_list]
            elif pool_type == 'vni':
                vni_pool_data_list = resource_pool_vni_db.query_resource_pool_data_list()
                res_dropdown_list['vni'] = [{
                    'id': vni_pool_data.id,
                    'name': vni_pool_data.name,
                } for vni_pool_data in vni_pool_data_list]
        return jsonify({'status': 200, 'data': res_dropdown_list})
    except Exception as e:
        LOG.error(f"get_resource_pool_data error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to get resource pool data'})


# area start
@resource_pool_blueprint_mold.route("/resource_pool_area_table_data", methods=["POST"])
@readonly_permission.require(http_exception=403)
def resource_pool_area_table_data():
    try:
        return resource_pool_area_db.query_all_resource_pool()
    except Exception as e:
        LOG.error(f"resource_pool_area_table_data error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to get resource pool data'})


@resource_pool_blueprint_mold.route("/delete_area_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_pool_area_record():
    try:
        data = request.get_json()
        if resource_pool_area_db.delete_resource_pool_by_id(data["id"]):
            return jsonify({'status': 200, 'info': 'Delete resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})
    except Exception as e:
        LOG.error(f"delete_pool_area_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})
    
    
@resource_pool_blueprint_mold.route("/delete_area_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_area_pool_n_record():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if resource_pool_area_db.delete_first_n_used_record(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Delete first ten record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})
    except Exception as e:
        LOG.error(f"delete_first_ten_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})

@resource_pool_blueprint_mold.route("/add_area_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def add_area_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        if not name or not is_name_valid(name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if resource_pool_area_db.query_resource_pool_by_name(name).first():
            return jsonify({'status': 500, 'info': 'Resource pool already exists'})
        if len(ranges) == 0:
            return jsonify({'status': 500, 'info': 'Ranges is empty'})
        if is_ranges_conflict(ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_area_db.add_resource_pool(name, ranges):
            return jsonify({'status': 200, 'info': 'Add resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_area_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/edit_area_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_area_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        pool_id = data.get('poolId')
        if not resource_pool_area_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if not name or not is_name_valid(name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        add_ranges = ranges.get('add', [])
        modify_ranges = ranges.get('modify', [])
        all_ranges = ranges.get('all', [])
        if is_ranges_conflict(all_ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_area_db.edit_resource_pool(pool_id, name, ranges):
            return jsonify({'status': 200, 'info': 'Edit resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to edit resource pool'})
    except ValueError as v:
        LOG.error(f"add_area_pool error: {v}")
        return jsonify({'status': 500, 'info': f'Failed to edit resource pool: {v}'})
    except Exception as e:
        LOG.error(f"add_area_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to edit resource pool'})


@resource_pool_blueprint_mold.route("/clone_area_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def clone_area_pool():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        new_pool_name = data.get('poolName')
        if not new_pool_name or not is_name_valid(new_pool_name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if not resource_pool_area_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_area_db.clone_resource_pool(pool_id, new_pool_name):
            return jsonify({'status': 200, 'info': 'Clone resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to clone resource pool'})
    except Exception as e:
        LOG.error(f"add_area_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to clone resource pool'})


@resource_pool_blueprint_mold.route("/generate_area_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def generate_area_pool_record():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if record_num <= 0:
            return jsonify({'status': 500, 'info': 'Invalid record number'})
        if not resource_pool_area_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_area_db.generate_resource_from_pool(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Generate resource pool record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to Generate resource pool record'})
    except Exception as e:
        LOG.error(f"add_area_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to Generate resource pool record'})
# area end

@resource_pool_blueprint_mold.route("/resource_pool_ip_table_data", methods=["POST"])
@readonly_permission.require(http_exception=403)
def resource_pool_ip_table_data():
    try:
        return resource_pool_ip_db.query_all_resource_pool()
    except Exception as e:
        LOG.error(f"resource_pool_ip_table_data error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to get resource pool data'})


@resource_pool_blueprint_mold.route("/delete_ip_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_pool_ip_record():
    try:
        data = request.get_json()
        if resource_pool_ip_db.delete_resource_pool_by_id(data["id"]):
            return jsonify({'status': 200, 'info': 'Delete resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})
    except Exception as e:
        LOG.error(f"delete_pool_ip_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete resource pool'})


@resource_pool_blueprint_mold.route("/add_ip_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def add_ip_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        if not name or not is_name_valid(name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if resource_pool_ip_db.query_resource_pool_by_name(name).first():
            return jsonify({'status': 500, 'info': 'Resource pool already exists'})
        if len(ranges) == 0:
            return jsonify({'status': 500, 'info': 'Ranges is empty'})
        if is_ranges_conflict(ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_ip_db.add_resource_pool(name, ranges):
            return jsonify({'status': 200, 'info': 'Add resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to add resource pool'})
    except Exception as e:
        LOG.error(f"add_ip_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to add resource pool'})


@resource_pool_blueprint_mold.route("/edit_ip_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def edit_ip_pool():
    try:
        data = request.get_json()
        name = data.get('poolName')
        ranges = data.get('ranges')
        pool_id = data.get('poolId')
        if not resource_pool_ip_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if not name or not is_name_valid(name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        add_ranges = ranges.get('add', [])
        modify_ranges = ranges.get('modify', [])
        if is_ranges_conflict(add_ranges + modify_ranges):
            return jsonify({'status': 500, 'info': 'Ranges conflict'})
        if resource_pool_ip_db.edit_resource_pool(pool_id, name, ranges):
            return jsonify({'status': 200, 'info': 'Edit resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to edit resource pool'})
    except ValueError as v:
        LOG.error(f"add_ip_pool error: {v}")
        return jsonify({'status': 500, 'info': f'Failed to edit resource pool: {v}'})
    except Exception as e:
        LOG.error(f"add_ip_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to edit resource pool'})


@resource_pool_blueprint_mold.route("/clone_ip_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def clone_ip_pool():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        new_pool_name = data.get('poolName')
        if not new_pool_name or not is_name_valid(new_pool_name):
            return jsonify({'status': 500, 'info': 'Invalid name'})
        if not resource_pool_ip_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        if resource_pool_ip_db.clone_resource_pool(pool_id, new_pool_name):
            return jsonify({'status': 200, 'info': 'Clone resource pool successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to clone resource pool'})
    except Exception as e:
        LOG.error(f"add_ip_pool error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to clone resource pool'})


@resource_pool_blueprint_mold.route("/generate_ip_pool_record", methods=["POST"])
@readonly_permission.require(http_exception=403)
def generate_ip_pool_record():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if record_num <= 0:
            return jsonify({'status': 500, 'info': 'Invalid record number'})
        if not resource_pool_ip_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        res = resource_pool_ip_db.generate_resource_from_pool(pool_id, record_num)
        if res:
            return jsonify({'status': 200, 'info': 'Generate resource pool record successfully', 'data': res})
        else:
            return jsonify({'status': 500, 'info': 'Failed to generate resource pool'})
    except Exception as e:
        LOG.error(f"add_ip_pool error: {e}")
        return jsonify({'status': 500, 'info': f'Failed to generate resource pool: {e}'})


@resource_pool_blueprint_mold.route("/delete_first_ten_record_in_ip", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_first_ten_record_in_ip():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        record_num = data.get('recordNum')
        if resource_pool_ip_db.delete_first_n_used_record(pool_id, record_num):
            return jsonify({'status': 200, 'info': 'Delete first ten record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})
    except Exception as e:
        LOG.error(f"delete_first_ten_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete first ten record'})
  
    
@resource_pool_blueprint_mold.route("/delete_record_in_ip_pool", methods=["POST"])
@readonly_permission.require(http_exception=403)
def delete_record_in_ip_pool():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        ip_records = data.get('record')
        records = []
        for record in ip_records:
            try:
                records.append(int(ipaddress.IPv4Address(record)))
            except ipaddress.AddressValueError:
                continue
        if resource_pool_ip_db.delete_record_by_value(pool_id, records):
            return jsonify({'status': 200, 'info': 'Delete record successfully'})
        else:
            return jsonify({'status': 500, 'info': 'Failed to delete record'})
    except Exception as e:
        LOG.error(f"delete_first_ten_record error: {e}")
        return jsonify({'status': 500, 'info': 'Failed to delete record'})
    
    
@resource_pool_blueprint_mold.route("/generate_routed_interface_address", methods=["POST"])
@readonly_permission.require(http_exception=403)
def generate_routed_interface_address():
    try:
        data = request.get_json()
        pool_id = data.get('poolId')
        link_num = data.get('linkNum')
        if link_num <= 0:
            return jsonify({'status': 500, 'info': 'Invalid record number'})
        if not resource_pool_ip_db.query_resource_pool_by_id(pool_id).first():
            return jsonify({'status': 500, 'info': 'Resource pool not found'})
        res = []
        for _ in range(link_num):
            ips = resource_pool_ip_db.generate_resource_from_pool(pool_id, 2)

            # 检查两个ip 网络地址是否相同 
            ip0_with_prefix = ips[0] + "/31"
            ip1_with_prefix = ips[1] + "/31"
            ip0_network_address = str(ipaddress.IPv4Interface(ip0_with_prefix).network.network_address)
            ip1_network_address = str(ipaddress.IPv4Interface(ip1_with_prefix).network.network_address)
            if ip0_network_address != ip1_network_address:
                resource_pool_ip_db.delete_record_by_value(pool_id, [int(ipaddress.IPv4Address(ips[0]))])
                new_ip = resource_pool_ip_db.generate_resource_from_pool_by_value(pool_id, int(ipaddress.IPv4Address(ips[1])) + 1 )[0]
                res.append([ip1_with_prefix, new_ip+ "/31"])
            else:
                res.append([ip0_with_prefix, ip1_with_prefix]) 
        
        if res:
            return jsonify({'status': 200, 'info': 'Generate resource pool record successfully', 'data': res})
        else:
            return jsonify({'status': 500, 'info': 'Failed to generate resource pool record'})
    except Exception as e:
        LOG.error(f"add_ip_pool error: {e}")
        print(traceback.format_exc())
        return jsonify({'status': 500, 'info': 'Failed to generate resource pool record'})