import logging
import logging.handlers
import datetime
import random
import string
from server import cfg

from flask_principal import Principal
from logging.config import dictConfig

dictConfig({
    'version': 1,
    'formatters': {
        'default': {
            # 'format': '[%(process)d] %(asctime)s %(levelname)s [%(filename)s:%(lineno)s] %(message)s',
            'format': '%(asctime)s %(levelname)s %(process)d %(thread)d %(module)s[line:%(lineno)d] %(message)s',
        }
    },
    'handlers': {
        'wsgi': {
            'class': 'logging.StreamHandler',
            'stream': 'ext://sys.stdout',
            'formatter': 'default'
        },
        # 'file': {
        #     'class': 'logging.handlers.RotatingFileHandler',
        #     'filename': 'app.log',
        #     'maxBytes': 10 * 1024 * 1024,  # 10 MB
        #     'backupCount': 5,
        #     'formatter': 'default',
        #     'level': 'INFO'
        # }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['wsgi']
    }
})


def is_enable_debug():
    log = logging.getLogger()
    return log.isEnabledFor(logging.DEBUG)


def enable_debug():
    log = logging.getLogger()
    log.setLevel(logging.DEBUG)


def init_log_level():
    log = logging.getLogger()
    # set log level
    if cfg.CONF.default_log_level is not None:
        log.setLevel(cfg.CONF.default_log_level)
    else:
        log.setLevel(logging.INFO)


# init app first
from flask import Flask
app = Flask(__name__)
app.jinja_env.add_extension('jinja2.ext.do')
principal = Principal()
principal.init_app(app)


def get_random_key():
    nowtime = datetime.datetime.utcnow()
    nowkey = ''.join(random.sample(string.ascii_letters + string.digits, 8))
    return [nowkey, nowtime]


random_key = get_random_key()
