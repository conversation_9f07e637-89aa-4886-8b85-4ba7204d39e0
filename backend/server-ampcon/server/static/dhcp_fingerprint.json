{"1,3,6,12,15,26,42,252": {"device_name": "3-D Printer Manuf. MakerBot", "score": 80}, "1,3,4,5,6,12,15,28,42": {"device_name": "Juniper networks JUNOS", "score": 80}, "1,3,6,12,15,28": {"device_name": "Operating System/Linux OS/Embedded Linux 1,3,6,12,15,28", "score": 29}, "1,3,6,12,15,28,33,40,41,42,58,59,119,121": {"device_name": "Printer or Scanner/Shandong New Beiyang Information Technology", "score": 87}, "1,2,3,4,6,15,42,43,54,66": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "1,3,4,6,12,15,28,42,44,69,70": {"device_name": "Printer or Scanner/Toshiba Printer/Toshiba Multifunction Printer", "score": 87}, "1,3,6,12,15,42,66,150": {"device_name": "VoIP Device/Cisco VoIP/Cisco ATA 186", "score": 87}, "1,3,6,43,66,67,150": {"device_name": "Switch and Wireless Controller/Cisco Switches/Cisco Catalyst 29xx", "score": 87}, "1,3,6,12,15,28,44,46,58,59,66,67,69,78,79,116": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,15,44,46,47": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 4.x/Microsoft Windows Kernel 4.0", "score": 87}, "1,3,58,59": {"device_name": "Audio, Imaging or Video Equipment/Extron/Extron TouchLink Touchpanels", "score": 87}, "1,3,4,23,43,66,67": {"device_name": "Switch and Wireless Controller/HP ProCurve Switches/HP ProCurve 3500yl", "score": 87}, "1,2,3,6,12,15,26,28,40,41,42,119": {"device_name": "Operating System/Linux OS/RedHat/Fedora-based Linux", "score": 87}, "1,3,6,15": {"device_name": "Phone, Tablet or Wearable/Apple Mobile Device/Apple iPod", "score": 87}, "1,3,6,12,15,23": {"device_name": "Printer or Scanner/Canon Printer", "score": 87}, "1,3,6,12,15,17,28,42": {"device_name": "TP-Link Wireless LAN Router", "score": 50}, "1,3,6,12,15,28,33,44": {"device_name": "Router, Access Point or Femtocell/Router/Belkin Wireless Router", "score": 87}, "1,3,6,15,44,46,47,95,119,252": {"device_name": "Operating System/Apple OS", "score": 87}, "1,3,6,15,33,43,44,46,47,60,121,212,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.2", "score": 87}, "1,2,3,4,6,7,12,15,23,26,28,43,50,51,54,55,60,72": {"device_name": "Router, Access Point or Femtocell/Router/Cisco/Linksys Router", "score": 87}, "1,3,6,15,66,150": {"device_name": "VoIP Device/Cisco VoIP/Cisco IP Phone", "score": 87}, "1,2,3,4,6,15,28,42,66,160": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "1,2,3,4,6,7,15,23,26,28,43,50,51,54,55,60,61,72": {"device_name": "Router, Access Point or Femtocell/Router/Actiontec Wireless Router", "score": 87}, "1,2,3,4,6,15,28,42,66": {"device_name": "VoIP Device/Polycom VoIP/Polycom SoundPoint IP/Polycom SoundPoint IP 430", "score": 87}, "1,3,6,7,9,12,15,17,23,28,29,31,33,40,41,42,44,45,46,47,119": {"device_name": "Operating System/Linux OS/SUSE Linux/Novell Desktop", "score": 87}, "1,3,6,12,15,28,44,51,53,54,58,59,60,69": {"device_name": "Phone, Tablet or Wearable/Intermec Handheld", "score": 87}, "1,3,6,12,15,28,33,42,51,58,59,119,121": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,3,6,15,51,54,120": {"device_name": "Phone, Tablet or Wearable/Sharp Phone", "score": 73}, "1,3,28,43": {"device_name": "VoIP Device/Siemens VoIP/Siemens optiPoint 410/420", "score": 87}, "1,3,4,23": {"device_name": "Switch and Wireless Controller/HP ProCurve Switches", "score": 87}, "1,3,6,12,15,17,23,28,29,31,33,40,41,42": {"device_name": "Operating System/Linux OS/Gentoo Linux", "score": 87}, "1,3,6,28": {"device_name": "Nokia", "score": 50}, "2,3,6,12,15,28": {"device_name": "Audio, Imaging or Video Equipment/Video Equipment (Smart TV, Smart Players, etc.)/TiVo TV", "score": 87}, "1,2,3,6,12,15,28,40,41,42": {"device_name": "Operating System/Linux OS/RedHat/Fedora-based Linux", "score": 87}, "1,3,6,15,78,79,95,112,113": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "1,3,6,12,15,23,44,47": {"device_name": "Printer or Scanner/Kyocera Printer", "score": 87}, "1,3,6,15,43": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Enterasys WAP/Enterasys or Trapeze Wireless Access Point", "score": 87}, "1,2,3,6,12,15,26,28,33,40,41,42,42,119,121,121,249,252": {"device_name": "Operating System/Linux OS/Generic Linux", "score": 87}, "1,3,6,12,15,28,51,54,58,59": {"device_name": "Printer or Scanner/E<PERSON>on Printer", "score": 87}, "1,3,6,12,15,42,66,67,120": {"device_name": "VoIP Device/Snom VoIP solutions", "score": 88}, "2,3,6,12,15,28,44,47": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.1,5.2", "score": 87}, "1,3,6,15,31,33,43,44,46,47,252": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.0", "score": 87}, "0,1,3,6,15,31,33,43,44,46,47,112,128,249": {"device_name": "Operating System/Linux OS/Generic Linux", "score": 87}, "1,2,3,6,12,15,26,28,44,47,119,121": {"device_name": "Operating System/Linux OS/Ubuntu/Debian 5/Knoppix 6", "score": 87}, "1,3,6,15,51,54,58,59,66,67": {"device_name": "VoIP Device/Panasonic VoIP/Panasonic KX-UDS124CE SIP-DECT Base Station", "score": 87}, "100": {"device_name": "Network Boot Agent/Novell Netware Client", "score": 87}, "3,6,15,28": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Apple Airport WAP", "score": 87}, "1,3,6,7,12,15,18,22,43,44,54,58,59,69,119,154": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,12,15,19": {"device_name": "Operating System/Other OS/BeOS", "score": 87}, "1,3,6,15,33,43,44,150": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Cisco WAP", "score": 87}, "1,2,3,4,6,7,15,42,54,66,128,144,157,160,191": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "1,2,3,4,6,15,28,42,160": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "1,2,3,5,6,11,12,13,15,16,17,18,43,54,60,67,128,129,130,131,132,133,134,135": {"device_name": "Network Boot Agent/PXE", "score": 87}, "1,3,5,6,13,15,17,23,28,42,50,51,53,54,56,56,66,67": {"device_name": "Samsung S8500", "score": 50}, "1,3,6,15,26,51,54,120": {"device_name": "Operating System/Embedded OS/Java ME OS", "score": 73}, "1,3,6,12,15,28,119,120": {"device_name": "Operating System/Symbian OS", "score": 73}, "1,3,6,15,28": {"device_name": "Gaming Console/Sony Gaming Console/Playstation/Playstation 3 or Playstation Portable (PSP)", "score": 87}, "1,2,3,6,12,15,26,28,42,42,44,47,119,121,121,249,252": {"device_name": "Operating System/Linux OS/Debian-based Linux/Ubuntu", "score": 87}, "1,3,6,12,15,31,33,43,44,46,47,249,252": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.1,5.2", "score": 87}, "1,3,6,44,46": {"device_name": "Printer or Scanner/Konica Minolta Printer/Konica Minolta Multifunction Printer", "score": 87}, "3,6,7,12,15,18,22,44,54,58,59,69,81,144": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,2,3,6,15,42": {"device_name": "VoIP Device/Uniden DTA VoIP Adapater", "score": 87}, "1,3,4,6,12,15,28,42,43,60,66,67": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Cisco WAP", "score": 87}, "1,3,4,23,67": {"device_name": "Switch and Wireless Controller/HP ProCurve Switches", "score": 87}, "3,6,7,9,12,15,28,42,48,49": {"device_name": "Operating System/Linux OS/Debian-based Linux", "score": 87}, "3,6,15,44,46,47": {"device_name": "Router, Access Point or Femtocell/Router/Cisco/Linksys Router", "score": 87}, "1,3,6,15,44,46,95,101,119,252": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "1,3,43,51,58,59,128,144,157,191,251": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone", "score": 87}, "1,3,6,15,44,51": {"device_name": "Operating System/Linux OS/Knoppix", "score": 87}, "1,2,3,6,7,15,42,58,59,66,150,151,159,160": {"device_name": "VoIP Device/Cisco VoIP/Cisco/Linksys SPA series IP Phone", "score": 87}, "1,2,3,6,12,15,17,23,28,29,31,33,40,41,42,43": {"device_name": "Audio, Imaging or Video Equipment/Set-top Box/Amino Aminet STB", "score": 87}, "3,6,15": {"device_name": "Gaming Console/Sony Gaming Console", "score": 87}, "1,3,6,12,15,19,23,26,28,37,38,39,40,42": {"device_name": "Tripplite UPS", "score": 50}, "1,3,6,15,114,120,125": {"device_name": "VoIP Device/Gigaset Communications", "score": 88}, "3,6,15,78,79,95,112,113": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "1,3,6,7,12,15,44,51,54,58,59": {"device_name": "Hardware Manufacturer/SEH COMPUTERTECHNIK GMBH", "score": 87}, "1,3,4,6,7,15,28,67": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Cisco WAP", "score": 87}, "1,2,3,6,12,15,28,44": {"device_name": "Printer or Scanner/Kyocera Printer", "score": 87}, "1,3,6,15,43,77": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Compex WAP", "score": 87}, "1,2,3,6,12,15,26,28,42,44,47,119,121": {"device_name": "Operating System/Linux OS/Ubuntu/Debian 5/Knoppix 6", "score": 87}, "1,3,6,15,27": {"device_name": "Switch and Wireless Controller/3Com Switches", "score": 87}, "1,3,12,23,44,47": {"device_name": "Router, Access Point or Femtocell/Router/Belkin Wireless Router", "score": 87}, "1,3,6,15,31,33,43,44,46,47,121,249,252": {"device_name": "Operating System/Windows OS", "score": 87}, "1,3,6,15,42,44,46,47,69": {"device_name": "Audio, Imaging or Video Equipment/Axis Communications", "score": 87}, "1,3,6,12,15,17,23,28,29,31,33,40,41,42,43": {"device_name": "Audio, Imaging or Video Equipment/Set-top Box/Amino Aminet STB", "score": 87}, "1,2,3,6,12,15,26,28,40,41,42,119,121": {"device_name": "Operating System/Linux OS/RedHat/Fedora-based Linux/Fedora/Fedora 14 based distro", "score": 87}, "1,3,6,12,15,28,44,46,58,59,69,78,79,81": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,15,119,252": {"device_name": "Operating System/Apple OS/iOS", "score": 87}, "203": {"device_name": "Monitoring and Testing Device/HP iLO Agent", "score": 87}, "1,3,6,12,15,28,42,43,186,187,188,189,191,192": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Motorola WAP/Motorola AP", "score": 70}, "1,3,6,15,51": {"device_name": "Audio, Imaging or Video Equipment/Video Equipment (Smart TV, Smart Players, etc.)/Sony Player", "score": 87}, "1,3,6,12,15,78,79,112,113": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "1,3,6,7,9,12,15,17,23,28,29,31,33,40,41,42,44,200": {"device_name": "Operating System/Linux OS/SUSE Linux/Novell Desktop", "score": 87}, "1,3,43,60": {"device_name": "Network Boot Agent/Apple Netboot", "score": 87}, "1,3,6,13,15,52,67,119,252": {"device_name": "Operating System/Apple OS/iOS", "score": 87}, "1,2,3,6,12,15,26,28,44,47,119": {"device_name": "Operating System/Linux OS/Debian-based Linux", "score": 87}, "1,2,3,4,6,15,28,33,42,43,44,58,59,100,101": {"device_name": "Monitoring and Testing Device/HP iLO Agent", "score": 87}, "1,3,6,15,121": {"device_name": "Router, Access Point or Femtocell/Router/Belkin Wireless Router", "score": 87}, "1,3,6,28,33,51,58,59,121": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,3,6,12,15": {"device_name": "Router, Access Point or Femtocell/Router/Netgear Router", "score": 87}, "1,2,3,4,6,7,15,42,54,66,160": {"device_name": "VoIP Device/Spectralink", "score": 87}, "3,5,6,15,44": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Trendnet WAP/Trendnet Access Point", "score": 87}, "1,3,6,15,23": {"device_name": "Printer or Scanner/Konica Minolta Printer/Konica Minolta Multifunction Printer", "score": 87}, "1,2,3,4,6,15,28,42,43,160": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "1,3,5,6,12,13,15,17,23,28,42,50,51,53,54,56,66,67": {"device_name": "Operating System/Samsung Bada OS", "score": 87}, "1,2,3,6,12,15,28,163": {"device_name": "<PERSON> and Noble <PERSON> (eReader)", "score": 50}, "3,6,15,43,44,46,47,77,252": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 4.x/Microsoft Windows Kernel 4.10", "score": 87}, "1,3,6,12,15,28,44,46,47": {"device_name": "Router, Access Point or Femtocell/Router/D-Link Wireless Router", "score": 87}, "1,3,6,12,15,28,60": {"device_name": "Audio, Imaging or Video Equipment/IP Camera/TRENDnet Camera/TV-IP512P PoE Network IP Camera", "score": 87}, "1,3,6,7,12,15,18,22,44,54,58,59,69,144": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,12,15,28,33,40,41,42,44,46,47": {"device_name": "Router, Access Point or Femtocell/Router/D-Link Wireless Router", "score": 87}, "1,3,6,15,28,35,66,176": {"device_name": "VoIP Device/Polycom VoIP/Unidentified Polycom", "score": 87}, "1,3,6,15,78,79,95,112,113,252": {"device_name": "Operating System/Apple OS", "score": 87}, "1,3,6,12,15,23,42,44,47": {"device_name": "Printer or Scanner/Oki Printer", "score": 87}, "1,2,3,6,12,15,26,28,44,45,46,47,85,86,87,88": {"device_name": "Printer or Scanner/Brother Printer", "score": 87}, "1,3,6,15,43,44,46,47,77": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 4.x/Microsoft Windows Kernel 4.10", "score": 87}, "1,3,6,12,15,28,42,44,46,58,59,66,67,69,78,79,116": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,12,15,26,28,40,41,42": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Meraki WAP", "score": 87}, "1,3,7,12,15,18,44,51,54,58,59,144": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,15,28,35,43,66,128,131,144,157,188,191,205,219,223,232,247,251": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "43,60": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "1,2,3,6,15,26,28,44,45,46,47,69,70,78,79,85,86,87,88,120": {"device_name": "Printer or Scanner/Xerox Printer", "score": 73}, "1,3,6": {"device_name": "Gaming Console/Microsoft Gaming Console/Xbox 360", "score": 29}, "1,3,6,58,59": {"device_name": "Robotics and Industrial Automation/Sensaphone Remote Monitoring", "score": 87}, "1,3,6,7,12,15,42,44,51,54,58,59,69": {"device_name": "Hardware Manufacturer/SEH COMPUTERTECHNIK GMBH", "score": 87}, "1,3,6,12,15,43,66,67,128,129,130,131,132,133,134,135": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Symbol WAP", "score": 87}, "1,3,6,15,28,33,40,41,42,51,58,59,119,121": {"device_name": "Printer or Scanner/Kyocera Printer", "score": 87}, "1,3,6,12,15,17,28,42,234": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Sophos WAP/Sophos Astaro AP 10 WAP", "score": 87}, "1,3,4,6,15,28,67": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Cisco WAP", "score": 87}, "1,3,6,7,12,15,18,23,26,44,46,51,54,58,59,78,79": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,12,15,28,33,44,121,249": {"device_name": "Router, Access Point or Femtocell/Router/Netgear Router", "score": 87}, "1,3,6,15,46,92,119,208,252": {"device_name": "Operating System/Apple OS/iOS", "score": 87}, "1,3,6,15,44,47,78,79,95,112,113,252": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "3,6,15,31,33,43,44,46,47,77": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 4.x/Microsoft Windows Kernel 4.90", "score": 87}, "1,3,6,15,51,54,58,59": {"device_name": "Audio, Imaging or Video Equipment/Video Equipment (Smart TV, Smart Players, etc.)/Panasonic TV", "score": 87}, "1,2,3,6,12,15,28,44,47,119,121": {"device_name": "Storage Device/Iomega NAS/Iomega Backup Center", "score": 87}, "1,3,6,12,15,66": {"device_name": "VoIP Device/Siemens VoIP/Siemens optiPoint 150 S", "score": 87}, "1,3,6,15,28,33,51,58,59": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,3,6,12,15,28,42,43,66,120,242": {"device_name": "Video Conferencing/Tandberg", "score": 73}, "1,3,6,15,42,43,44,46,47,119": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "1,3,6,15,28,42,43,66,176": {"device_name": "VoIP Device/Avaya IP Phone", "score": 87}, "1,2,3,6,12,15,28,33,42,43,120": {"device_name": "VoIP Device/Siemens VoIP/Siemens OpenStage IP Phones", "score": 88}, "1,3,6,12,15,28,33,40,41,42,51,58,59,119,121": {"device_name": "Operating System/Linux OS/Gentoo Linux", "score": 87}, "1,3,6,12,15,28,43": {"device_name": "OpenSolaris", "score": 50}, "1,3,4,6,7,12,26,42,44,51,54,58,59,190": {"device_name": "Printer or Scanner/Lexmark Printer", "score": 87}, "1,3,6,12,15,28,42,66,149,150": {"device_name": "VoIP Device/Cisco VoIP/Cisco IP Phone", "score": 87}, "1,3,6,12,15,28,44,46,58,59,66,67,69,78,79,81": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,15,31,33,43,44,46,47,249,252": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.1,5.2", "score": 87}, "1,3,6,7,15,43,66,69,176": {"device_name": "VoIP Device/Avaya IP Phone", "score": 87}, "1,3,6,28,33,51,54,58,59": {"device_name": "Operating System/Google OS/Chrome OS", "score": 87}, "1,3,6,12,15,35,66,150": {"device_name": "VoIP Device/Cisco VoIP/Cisco IP Phone", "score": 87}, "1,2,3,4,5,6,12,13,15,17,18,22,23,28,40,41,42,43,50,51,54,58,59,60,66,67,97,128,129,130,131,132,133,134,135": {"device_name": "Network Boot Agent/PXE", "score": 87}, "1,3,6,12,15,28,44,47,204": {"device_name": "Printer or Scanner/<PERSON>h Printer/Ricoh Multifunction Printer", "score": 87}, "3,6,7,9,12,15,17,23,28,29,31,33,40,41,42,44,200": {"device_name": "Operating System/Linux OS/Generic Linux", "score": 87}, "1,3,6,15,66": {"device_name": "VoIP Device/ZyXEL WiFi Phone", "score": 87}, "1,3,7,12,18,44,51,54,58,59,144": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,12,44": {"device_name": "Network Boot Agent/Generic Intel PXE", "score": 87}, "1,2,3,4,6,7,12,15,23,26,28,43,50,51,54,55,60,61": {"device_name": "Router, Access Point or Femtocell/Router/Cisco/Linksys Router", "score": 87}, "1,2,3,4,5,6,7,8,9,12,13,15,16,17,23,26,28,42,44,50,51,53,54,56,66,67": {"device_name": "Samsung S8000", "score": 50}, "1,3,6,12,15,28,33,44,121": {"device_name": "Router, Access Point or Femtocell/Router/Netgear Router", "score": 87}, "1,3,6,15,35,66,150": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,3,28,43,58,59": {"device_name": "VoIP Device/Alcatel IP Phone/Alcatel IP Touch 8 Series Phones", "score": 87}, "1,1,3,6,15,28": {"device_name": "Audio, Imaging or Video Equipment/Matsushita or Panasonic", "score": 87}, "1,2,3,6,12,15,28,42,157": {"device_name": "Video Conferencing/LifeSize Video Conferencing", "score": 87}, "1,3,6,12,15,42,252": {"device_name": "Meego Netbook", "score": 50}, "1,2,3,6,15,28,42,44,46,47,58,59,66,67,69,70,78,79,81,116,119,252": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,7,12,15,26,28,42,43,242": {"device_name": "VoIP Device/Avaya IP Phone", "score": 87}, "1,1,3,4,6,28": {"device_name": "Router, Access Point or Femtocell/Router/Motorola Router", "score": 87}, "1,3,6,15,31,33,43,44,46,47,121,200,249": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.1,5.2", "score": 87}, "1,3,6,51,58,59": {"device_name": "Physical Security/Paradox Card Access module", "score": 87}, "1,3,6,12,15,28,33,121": {"device_name": "Router, Access Point or Femtocell/Router/Netgear Router", "score": 87}, "1,3,6,15,35,51,66,150": {"device_name": "VoIP Device/Cisco VoIP/Cisco IP Phone", "score": 87}, "1,2,3,6,15,28,33,42,43,120": {"device_name": "VoIP Device/Siemens VoIP/Siemens optiPoint 410/420", "score": 88}, "1,3,6,12,15,33,43,44,66,67,150": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Cisco WAP", "score": 87}, "1,3,6,15,28,33,51,58,59,121": {"device_name": "Samsung Android", "score": 50}, "1,3,6,12,15,26,28,33,42,51,54,58,59,119,121": {"device_name": "Operating System/Linux OS/Gentoo Linux", "score": 87}, "1,3,6,15,35,66,150,151": {"device_name": "VoIP Device/Cisco VoIP/Cisco IP Phone", "score": 87}, "1,3,6,12,15,28,44,47": {"device_name": "Printer or Scanner/<PERSON>h Printer/Ricoh Multifunction Printer", "score": 87}, "1,3,6,12,15,26,28,40,41,42,44,45,46,47,85,86,87": {"device_name": "Operating System/Linux OS/Suse Linux Enterprise Desktop 11", "score": 87}, "1,3,6,15,43,60": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Aruba WAP", "score": 87}, "1,2,3,6,12,15,44": {"device_name": "Operating System/Apple OS", "score": 87}, "1,3,6,12,15,17,26,28,33,58,59,119,120,121": {"device_name": "VoIP Device/Biamp devices", "score": 57}, "3,6,15,78,79,95,112,113,252": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "1,3,6,7,12,15,18,22,43,44,54,58,59,69,119": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,15,56": {"device_name": "Phone, Tablet or Wearable/RIM BlackBerry/Blackberry Playbook", "score": 57}, "1,3,6,15,44,46,47,66,67": {"device_name": "Operating System/Windows OS/Windows CE", "score": 87}, "1,28": {"device_name": "Audio, Imaging or Video Equipment/Video Equipment (Smart TV, Smart Players, etc.)/Replay TV", "score": 87}, "1,3,7,12,26,44,51,54,58,59": {"device_name": "Printer or Scanner/Lexmark Printer", "score": 87}, "1,6,15,44,52": {"device_name": "Switch and Wireless Controller/Cisco Switches/Cisco Catalyst 35xx", "score": 87}, "1,3,6,12,15,17,43,60": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Aruba WAP", "score": 87}, "1,3,6,12,44": {"device_name": "Video Conferencing/Polycom Video Conferencing/Polycom ViewStation", "score": 87}, "1,3,7,12,26,44,51,54,58,59,190": {"device_name": "Printer or Scanner/Lexmark Printer", "score": 87}, "1,2,3,6,12,13,15,42,43,44,58,59,66,67": {"device_name": "Monitoring and Testing Device/APC/APC-Schneider Uninterruptible Power Supply", "score": 87}, "1,3,6,12,15,19,23,26,28,37,38,39,40,42,44": {"device_name": "Printer or Scanner/Lexmark Printer", "score": 87}, "1,3,6,15,42,66,150": {"device_name": "VoIP Device/Cisco VoIP/Cisco IP Phone", "score": 87}, "1,2,3,6,12,15,28,40,42": {"device_name": "Operating System/Linux OS/FortiOS", "score": 87}, "1,3,6,12,15,66,67,69,70": {"device_name": "Router, Access Point or Femtocell/Router/D-Link Wireless Router", "score": 87}, "1,3,6,7,12,15,18,22,44,54,58,59,69,81,144": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,12,13,15,44,66,67": {"device_name": "Printer or Scanner/Brother Printer", "score": 87}, "1,3,6,15,44,46,47,57": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 4.x/Microsoft Windows Kernel 4.10", "score": 87}, "1,2,3,6,12,15,26,28,44,47": {"device_name": "Operating System/Linux OS/Debian-based Linux", "score": 87}, "1,2,3,6,15,42,44,46,47,88": {"device_name": "Router, Access Point or Femtocell/Router/2Wire(Pace) Residential Gateway Router", "score": 87}, "1,3,4,6,12,15,42,44,69,70": {"device_name": "Printer or Scanner/Toshiba Printer/Toshiba Multifunction Printer", "score": 87}, "1,3,6,12,15,28,40,41,42": {"device_name": "DD-WRT Router, or amazon kindle firmware 3.1", "score": 50}, "1,2,3,3,4,6,15,15,42,54,66,160": {"device_name": "VoIP Device/Polycom VoIP/Polycom SoundStation IP/Polycom SoundStation IP 6000", "score": 87}, "1,3,42": {"device_name": "Audio, Imaging or Video Equipment/IP Camera/Arecont Vision IP Camera", "score": 87}, "1,3,6,33,43,44,46,47,121": {"device_name": "Router, Access Point or Femtocell/Router/TP-Link Wireless LAN Router", "score": 87}, "1,3": {"device_name": "Operating System/Windows OS", "score": 87}, "1,3,6,12,15,28,42": {"device_name": "Operating System/Linux OS", "score": 29}, "1,3,4,6,7,12,15,26,42,44,51,54,58,59,190": {"device_name": "Printer or Scanner/Lexmark Printer", "score": 87}, "3,6,15,31,33,43,44,46,47,249": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.1,5.2", "score": 87}, "0,1,3,6,15,31,33,43,44,46,47,67,121,176,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.0", "score": 87}, "1,3,6,15,28,54": {"device_name": "Router, Access Point or Femtocell/Router/Cisco/Linksys Router", "score": 87}, "1,3,4,6,12,15,28,72": {"device_name": "LG G2 F320", "score": 50}, "1,3,6,12,15,17,23,28,29,31,33,40,41,42,66,72,150": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone Model 1535", "score": 87}, "1,3,6,42,43,66,159,160": {"device_name": "VoIP Device/Aastra VoIP", "score": 87}, "1,3,6,7,12,26,44,51,54,58,59,190": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,13,15,66,67": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,4,6,7,12,15,26,42,51,54,58,59": {"device_name": "Printer or Scanner/Lexmark Printer", "score": 87}, "1,2,3,4,6,15,42,54,242": {"device_name": "VoIP Device/Polycom VoIP/Unidentified Polycom", "score": 87}, "1,2,3,6,12,13,15,42,44,66,67": {"device_name": "Printer or Scanner/Brother Printer", "score": 87}, "1,3,6,12,15,28,33,40,41,42,44,46,47,121,249": {"device_name": "Router, Access Point or Femtocell/Router/D-Link Wireless Router", "score": 87}, "1,3,6,12,15,26,28,33,51,54,58,59,119,121": {"device_name": "Operating System/Google OS/Chrome OS", "score": 87}, "1,3,6,12,15,44,46,47": {"device_name": "Router, Access Point or Femtocell/Router/Cisco/Linksys Router", "score": 87}, "1,2,3,6,12,15,28,42,121,249,252": {"device_name": "Operating System/Linux OS/Generic Linux", "score": 87}, "1,3,6,15,33,44,46,47,121": {"device_name": "Router, Access Point or Femtocell/Router/D-Link Wireless Router", "score": 87}, "0,1,3,6,15,31,33,43,44,46,47,98,112,121,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.1", "score": 87}, "1,3,12": {"device_name": "Switch and Wireless Controller/HP ProCurve Switches/HP ProCurve 1800-8G", "score": 87}, "1,3,6,43": {"device_name": "Network Boot Agent/Etherboot/gPXE", "score": 87}, "3,6,7,12,15,18,22,44,54,58,59,69,144": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,2,3,6,12,15,28,121": {"device_name": "Phone, Tablet or Wearable/Nokia Asha Phone", "score": 87}, "1,3,6,12,15,28,40,41,42,121": {"device_name": "Router, Access Point or Femtocell/Router/Cisco/Linksys Router", "score": 87}, "1,3,6,15,33,43,44,46,47,121,249": {"device_name": "Router, Access Point or Femtocell/Router/TP-Link Wireless LAN Router", "score": 87}, "1,2,3,6,15,28,42,51,54,58,59,66,139": {"device_name": "VoIP Device/UniData IP Phone", "score": 87}, "1,2,3,6,12,15,28,42": {"device_name": "Operating System/Linux OS/Debian-based Linux", "score": 87}, "1,3,7": {"device_name": "Video Conferencing/Tandberg", "score": 87}, "1,3,6,15,28,42,242": {"device_name": "VoIP Device/Avaya IP Phone", "score": 87}, "1,3,6,43,44,46,47": {"device_name": "Router, Access Point or Femtocell/Router/TP-Link Wireless LAN Router", "score": 87}, "1,3,6,15,44,47,78,79,95,112,113": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "1,3,6,7,9,12,15,17,26,28,33,40,41,42,44,45,46,47,58,59,119,120,121": {"device_name": "Datacenter Appliance/VMware vCenter Server Appliance", "score": 73}, "1,3,6,15,22,23,24,33,35": {"device_name": "Router, Access Point or Femtocell/Router/Quanta Microsystems Router", "score": 87}, "3,7,12,15,18,44,51,54,58,59,144": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,12,15,28,42,44": {"device_name": "Router, Access Point or Femtocell/Router/TP-Link Wireless LAN Router", "score": 87}, "1,3,6,13,15,44,66,67": {"device_name": "Epson Projectors", "score": 50}, "1,3,6,12,15,28,120": {"device_name": "Operating System/Symbian OS", "score": 73}, "1,3,6,15,28,33,44,51,58,59,119": {"device_name": "Phone, Tablet or Wearable/Generic Android/Motorola Android", "score": 87}, "1,2,3,6,12,15,28,42,44,58,59,69,78,79,81,116,119": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,7,12,15,18,22,43,44,54,58,59,69,81,119,153,154,252": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,66,67": {"device_name": "Switch and Wireless Controller/Cisco Switches/Cisco Catalyst 29xx", "score": 87}, "202": {"device_name": "Monitoring and Testing Device/HP iLO Agent", "score": 87}, "1,3,6,15,31,33,43,44,46,47,171,172,249": {"device_name": "Operating System/Windows OS/Microsoft Windows XP for embedded devices", "score": 87}, "1,3,4,6,15,23,43,66,67": {"device_name": "Switch and Wireless Controller/HP ProCurve Switches", "score": 87}, "1,2,3,6,12,15,28": {"device_name": "Operating System/Linux OS/Debian-based Linux", "score": 87}, "1,2,3,6,12,15,26,28,44,45,46,47,69,70,78,79,88": {"device_name": "Printer or Scanner/Xerox Printer", "score": 51}, "1,3,6,7,9,12,15,28,42,48,49,137,211,212,213,214,219": {"device_name": "Thin Client/Neoware e100 NeoLinux", "score": 87}, "1,3,6,12,15,23,28,29,31,33,40,41,42,44": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Buffalo WAP", "score": 87}, "1,3,6,7,12,15,18,23,26,44,46,51,54,58,59,78,79,81": {"device_name": "Printer or Scanner", "score": 87}, "1,3,6,12,51,58,59": {"device_name": "Operating System/Embedded OS/Java ME OS", "score": 87}, "1,3,6,12,15,28,33,50": {"device_name": "Router, Access Point or Femtocell/Router/Belkin Wireless Router", "score": 87}, "1,3,6,12,15,17,26,28,40": {"device_name": "Thin Client/Generic Thin Client", "score": 87}, "1,3,6,12,42,43,44,51,54,58,59,66,120,125,128,129,130,131,132,133,134,135,138,178,179,224": {"device_name": "VoIP Device/Mitel IP Phone", "score": 88}, "1,3,6,15,44": {"device_name": "Printer or Scanner/Kyocera Printer", "score": 87}, "1,3,6,7,12,15,18,22,43,44,54,58,59,69": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,15,31,33,43,44,46,47,121,195,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.0", "score": 87}, "1,3,6,15,28,51,54,58,59": {"device_name": "Audio, Imaging or Video Equipment/MagicJack Plus", "score": 87}, "1,3,6,15,33,43,44,46,47,121": {"device_name": "Operating System/Windows OS", "score": 87}, "1,3,6,12,15,28,33,121,249": {"device_name": "Router, Access Point or Femtocell", "score": 66}, "1,3,4,6,12,42": {"device_name": "Video Conferencing/VBrick Multimedia System", "score": 87}, "0,1,3,6,15,31,33,43,44,46,47,67,121,188,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.0", "score": 87}, "1,3,6,12,15,31,33,43,44,46,47,252": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.0", "score": 87}, "1,3,6,28,66,150": {"device_name": "VoIP Device/2N TELEKOMUNIKACE Helios Phones VoIP", "score": 87}, "1,3,6,15,78,79,95,119,252": {"device_name": "Operating System/Apple OS/iOS", "score": 87}, "1,2,3,6,42,43,66,159,160": {"device_name": "VoIP Device/Aastra VoIP", "score": 87}, "1,2,3,6,12,15,28,42,43,120": {"device_name": "VoIP Device/Siemens VoIP/Siemens optiPoint WL2 Professional", "score": 88}, "0,1,3,6,15,31,33,43,44,46,47,80,121,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.1", "score": 87}, "1,3,6,12,15,23,28,29,31,33,40,41,42": {"device_name": "Operating System/Linux OS/Generic Linux", "score": 87}, "1,2,3,4,6,7,15,28,42,43,66,160": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "1,2,3,4,6,7,15": {"device_name": "Audio, Imaging or Video Equipment/Video Equipment (Smart TV, Smart Players, etc.)/Replay TV", "score": 87}, "1,3,6,12,15,28,33,51,58,59,119": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,3,28,43,58,59,66,128,131,144,157,188,191,205,219,223,232,247,251": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone", "score": 87}, "0,1,3,6,15,31,32,33,43,44,46,47,67,121,176,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.0", "score": 87}, "3,6,15,44,47": {"device_name": "Printer or Scanner/Canon Printer", "score": 87}, "1,2,3,6,12,15,28,43,186,187,188,189,191": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Motorola WAP/Motorola AP", "score": 70}, "3,6,15,43,44,46,47,77": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 4.x/Microsoft Windows Kernel 4.10", "score": 87}, "0,1,3,6,15,31,33,43,44,46,47,64,112,121,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.0", "score": 87}, "1,3,6,28,33,42,51,58,59,121": {"device_name": "Unknown Android", "score": 50}, "1,2,3,4,6,15,28,42,66,128,144,157,160,191": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "1,3,6,7,15,66,151,152": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "1,3,6,15,44,51,54,58,59": {"device_name": "Printer or Scanner/Panasonic Printer/Panasonic MB2030CX", "score": 87}, "1,3,6,12,15,28,33,58,59": {"device_name": "Gaming Console/Nintendo Gaming Console/Nintendo Wii", "score": 87}, "1,2,3,6,12,15,28,119,121": {"device_name": "Storage Device/NAS4FREE NAS", "score": 87}, "1,3,6,15,28,33,44,51,58,59": {"device_name": "Phone, Tablet or Wearable/Generic Android/Motorola Android", "score": 87}, "1,3,6,15,31,33,43,44,46,47,121,249": {"device_name": "Operating System/Windows OS", "score": 87}, "1,3,6,12,15,17,23,28,29,31,33,40,41,42,66": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone Model 1535", "score": 87}, "1,2,3,6,15,28,33,42,43": {"device_name": "VoIP Device/Siemens VoIP/Siemens optiPoint 410/420", "score": 87}, "1,2,3,6,12,15,26,28,44,45,46,47,69,70,78,79,85,86,87,88,120": {"device_name": "Printer or Scanner/Xerox Printer", "score": 73}, "1,3,6,15,31,33,43,44,46,47,249": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.1,5.2", "score": 87}, "1,3,6,15,44,46,47,66": {"device_name": "VoIP Device/Sipura VoIP Adaptor", "score": 87}, "1,3,6,33,42,121": {"device_name": "Router, Access Point or Femtocell/Router/MikroTik (RouterOS) Router", "score": 87}, "1,2,3,6,12,15,28,119": {"device_name": "Operating System/Linux OS/Debian-based Linux", "score": 87}, "1,3,6,12,15,28,43,66,125": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,3,6,12,15,120,242": {"device_name": "Video Conferencing/Tandberg/Tandberg 1000", "score": 73}, "1,3,4,6,12,15,28,42,43,60": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Aruba WAP", "score": 87}, "1,2,3,6,12,15,28,42,43,44": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Ruckus WAP", "score": 87}, "1,3,6,11,15,22,23,24,33,35,44": {"device_name": "Printer or Scanner/Zebra Printer", "score": 87}, "1,3,6,7,9,12,15,17,23,28,29,31,33,40,41,42,44,45,46,47": {"device_name": "Operating System/Linux OS/SUSE Linux/Novell Desktop", "score": 87}, "1,3,6,15,28,33,43,44,58,59": {"device_name": "Monitoring and Testing Device/HP iLO Agent", "score": 87}, "1,3,6,12,15,28,44,78,79": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,12,15,28,40,41,42,119": {"device_name": "Operating System/Linux OS/Generic Linux", "score": 87}, "1,3,6,12,15,28,40,42,44,46": {"device_name": "Printer or Scanner/Brother Printer", "score": 87}, "1,3,6,15,44,46": {"device_name": "Printer or Scanner/Konica Minolta Printer/Konica Minolta Multifunction Printer", "score": 87}, "1,3,6,7,12,15,17,43,60,66,67,175,203": {"device_name": "Thin Client/Generic Thin Client", "score": 87}, "49,50,51,52,54,57,59,59,61,80,83,89,101,114,255": {"device_name": "Printer or Scanner/Lexmark Printer", "score": 87}, "1,3,6,7,12,15,18,22,44,54,58,59,144": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,15,43,44,46,47": {"device_name": "Router, Access Point or Femtocell/Router/Gemtek Wireless Router", "score": 87}, "3,6,15,44,47,78,79,95,112,113,252": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "1,3,4,6,12,42,50,51,54,69,116": {"device_name": "Switch and Wireless Controller/3Com Switches/3Com 4400 SE Switch", "score": 87}, "1,1,3,28": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone", "score": 87}, "0,1,3,6,15,31,33,43,44,46,47,64,112,121,168,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.0", "score": 87}, "1,2,3,6,12,15,28,42,48,161,162,163,164,181,182,184,185,186,187,188,189,190": {"device_name": "Thin Client/Wyse Technology Thin Client", "score": 87}, "1,3,6,12,15,28,40,41,42,44,46,47": {"device_name": "Router, Access Point or Femtocell/Router/D-Link Wireless Router", "score": 87}, "1,3,6,12,15,28,33,44,46,47,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.0", "score": 87}, "1,3,6,12,15,17,23,28,29,31,33,44,46,47": {"device_name": "Router, Access Point or Femtocell/Router/D-Link Wireless Router", "score": 87}, "1,3,5,6,13,15,17,23,28,32,42,50,51,53,54,56,66,67": {"device_name": "Samsung S8500", "score": 50}, "1,3,6,15,44,46,47,137,215": {"device_name": "Thin Client/Neoware Capio Windows CE", "score": 87}, "1,3,6,15,33,43,44,66,67,150": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Cisco WAP", "score": 87}, "1,3,6,33,43,44,46,47,121,249": {"device_name": "Router, Access Point or Femtocell/Router/TP-Link Wireless LAN Router", "score": 87}, "1,3,6,15,28,33": {"device_name": "Operating System/Other OS/OS/2 Warp", "score": 39}, "1,3,6,12,15,28,44,46,69,81": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,12,15,28,44": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Bluesocket WAP/Bluesocket BSC", "score": 87}, "1,3,6,12,15,28,33,44,249": {"device_name": "Router, Access Point or Femtocell/Router/Cisco/Linksys Router", "score": 87}, "1,3,6,15,28,43,58,59,66,128,131,144,157,188,191,205,219,223,224,227,230,232,235,238,241,244,247,249,251,254": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone", "score": 87}, "1,3,4,6,12,15,28,42,43,44,60": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Aruba WAP", "score": 87}, "1,2,3,6,12,15,28,43": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/HP Procurve WAP/HP ProCurve Access Point", "score": 87}, "1,3,6,15,28,33,51,58,59,119,121": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,3,6,12,15,26,28,42,120,242": {"device_name": "Video Conferencing/Tandberg", "score": 73}, "1,3,6,7,15,42,44,58,59,66,150,151": {"device_name": "VoIP Device/Cisco VoIP/Cisco/Linksys SPA series IP Phone", "score": 87}, "1,3,4,23,66,67": {"device_name": "Switch and Wireless Controller/HP ProCurve Switches", "score": 87}, "1,3,6,12,13,15,44,66,67,81": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "3,6,12,15,17,23,28,29,31,33,40,41,42,119": {"device_name": "Operating System/Linux OS/Generic Linux", "score": 87}, "1,3,6,7,9,12,15,28,42,48,49": {"device_name": "Audio, Imaging or Video Equipment/Video Equipment (Smart TV, Smart Players, etc.)/Sony TV", "score": 87}, "78,79": {"device_name": "Network Boot Agent/Novell Netware Client", "score": 87}, "1,3,6,7,12,15,28,40,41,42,225,226,227,228,229,230,231": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Aerohive WAP", "score": 87}, "1,3,6,7,9,12,15,26,28,42,48,49": {"device_name": "Network Boot Agent/Anaconda (RedHat) Installer", "score": 87}, "1,2,3,6,12,15,28,33,42,121,249,252": {"device_name": "Operating System/Linux OS/Debian-based Linux", "score": 87}, "1,3,43,54,60,67,128,129,130,131,132,133,134,135": {"device_name": "Network Boot Agent/PXE", "score": 87}, "1,3,6,12,15,28,33,43,121,249": {"device_name": "N300 Wireless Router", "score": 50}, "1,3,6,12,15,17,28": {"device_name": "Thin Client/Generic Thin Client", "score": 87}, "1,3,6,15,31,33,43,44,46,47": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.0", "score": 87}, "1,2,3,4,6,15,42,43,54,66,160": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "1,3,6,7,15,43,66,128,129,130,151,152": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "1,3,6,15,28,44": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Cisco WAP", "score": 87}, "1,3,6,15,58,59": {"device_name": "Audio, Imaging or Video Equipment/Extron/Extron TouchLink Touchpanels", "score": 87}, "1,3,6,12,15,28,44,212": {"device_name": "Router, Access Point or Femtocell/Router/Quanta Microsystems Router", "score": 87}, "1,3,28,43,58,59,128,131,144,157,188,191,205,219,223,232,247,251": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone", "score": 87}, "1,2,3,4,5,6,11,12,13,15,16,17,18,22,23,28,40,41,42,43,50,51,54,58,59,60,66,67,128,129,130,131,132,133,134,135": {"device_name": "Network Boot Agent/PXE", "score": 87}, "2,3,6": {"device_name": "Xbox 360", "score": 50}, "3,6,15,44,47,78,79,95,112,113": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "1,3,6,7,9,12,15,17,26,28,33,40,41,42,44,45,47,58,59,119,120,121": {"device_name": "Datacenter Appliance/VMware vCenter Server Appliance", "score": 73}, "1,3,6,28,43,44,46,47": {"device_name": "Router, Access Point or Femtocell/Router/TP-Link Wireless LAN Router", "score": 87}, "1,3,6,12,15,28,42,125": {"device_name": "Samsung SMART-TV", "score": 50}, "1,3,5,6,12,15,44,46,47,155,156,157,158,159,160,161,162,163,164,165,166,167,168,186,187": {"device_name": "Audio, Imaging or Video Equipment/IP Camera/Tattile IP Camera", "score": 87}, "1,3,6,15,33,42,44,45,46,47,69,70,71,74,78,79": {"device_name": "Operating System/Apple OS/Mac OS/Mac OS 9", "score": 87}, "1,3,6,12,15,42,43": {"device_name": "VoIP Device/Mediatrix VoIP Adapter", "score": 94}, "1,3,6,120": {"device_name": "Operating System/Embedded OS/Java ME OS", "score": 73}, "0,1,3,6,15,31,33,43,44,46,47,112,121,176,192,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.1", "score": 87}, "1,3,6,15,44,46,95,119,252": {"device_name": "Operating System/Apple OS", "score": 87}, "1,3,6,7,12,15,28,40,41,42,225,226,227,228": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Aerohive WAP", "score": 87}, "1,3,6,12,15,31,33,42,43": {"device_name": "Point of Sale Device/PCS Revenue Control Systems", "score": 87}, "1,3,6,12,15,42,50,51,58,59,69,70": {"device_name": "Projector/NEC Projectors", "score": 87}, "1,3,6,15,78,79,95,112,113,119,252": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "1,3,6,12,15,26,28,33,40,41,42,58,59,119,121": {"device_name": "Operating System/Linux OS/Gentoo Linux", "score": 87}, "1,2,3,6,12,15,28,42,43,66,120,125": {"device_name": "VoIP Device/Grandstream VoIP", "score": 88}, "1,2,3,6,12,13,15,42,43,58,59,66,67": {"device_name": "Monitoring and Testing Device/APC/APC UPS", "score": 87}, "1,3,4,6,11,42": {"device_name": "Datacenter Appliance/Precision time and frequency reference system", "score": 87}, "1,3,6,15,28,33,51,58,59,119": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,2,3,6,12,15,28,42,44,78,79": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,2,3,6,12,15,26,28,33,40,41,42,42,119,121,121,249": {"device_name": "Operating System/Linux OS/Debian-based Linux", "score": 87}, "1,3,6,12,15,17,28,40,41,42": {"device_name": "Nokia", "score": 50}, "1,1,3,28,43,58,59,128,131,144,157,188,191,205,219,223,232,247,251": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone", "score": 87}, "85,86": {"device_name": "Network Boot Agent/Novell Netware Client", "score": 87}, "1,3,6,12,15,28,66": {"device_name": "VoIP Device/Grandstream VoIP/GrandStream HandyTone 503 ATA", "score": 87}, "1,2,3,6,12,15,26,28,33,42,42,44,47,119,121,121,249,252": {"device_name": "Operating System/Linux OS/Debian-based Linux", "score": 87}, "1,2,3,4,6,12,15,43,44,161,162,181,182,184,185,186,187,188,190,192": {"device_name": "Thin Client/Wyse Technology Thin Client", "score": 87}, "1,2,3,6,12,15,17,23,28,29,31,33,40,41,42,43,72": {"device_name": "Audio, Imaging or Video Equipment/Video Equipment (Smart TV, Smart Players, etc.)/Amino", "score": 87}, "1,3,12,17,43,128,129,130,150": {"device_name": "Network Boot Agent/Etherboot/gPXE", "score": 87}, "1,3,6,15,42,51,66,150": {"device_name": "VoIP Device/Cisco VoIP/Cisco IP Phone", "score": 87}, "1,2,3,4,6,7,12,15,23,26,28,43,50,51,54,55,60,61,72": {"device_name": "Router, Access Point or Femtocell/Router/Gemtek Wireless Router", "score": 87}, "1,2,3,6,15,28,43": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Aruba WAP", "score": 87}, "1,3,6,15,44,46,47,95,101,119,252": {"device_name": "Operating System/Apple OS", "score": 87}, "1,2,3,5,6,12,15,19,28,33,40,41,64,65": {"device_name": "Operating System/Other OS/Solaris/Solaris 8 (SunOS 5.8)", "score": 87}, "1,3,28": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone", "score": 87}, "1,3,6,15,28,51,54,58,59,139": {"device_name": "VoIP Device/UniData IP Phone", "score": 87}, "1,3,6,12,42,43,44,51,54,58,59,66,120,125,128,129,130,131,132,133,134,135,138,224": {"device_name": "VoIP Device/Mitel IP Phone", "score": 88}, "1,2,3,6,12,15,28,42,44,46,58,59,66,67,69,78,79,81,116,119,252": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,12,15,33,43,44,66,67,125,150": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Cisco WAP", "score": 87}, "1,3,6,12,15,28,44,46,58,59,66,67,69,78,79": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,10,12,14,15,28,40,41,42,87": {"device_name": "Router, Access Point or Femtocell/Router/Zioncom Wireless Router", "score": 87}, "1,3,6,15,28,35,66,150": {"device_name": "VoIP Device/Cisco VoIP", "score": 87}, "85": {"device_name": "Network Boot Agent/Novell Netware Client", "score": 87}, "1,3,6,12,15,44": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,28,33,51,58,59,121": {"device_name": "HTC Android", "score": 50}, "1,3,12,15,44": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,12,15,26,28,33,40,41,42,42,44,45,46,47,85,86,87,119,121,121,249,252": {"device_name": "Operating System/Linux OS/Debian-based Linux", "score": 87}, "1,3,6,12,15,26,28,40,41,42,43": {"device_name": "VoIP Device/NetCODEC Co VoIP", "score": 87}, "1,3,6,15,31,33,43,44,46,47,77": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 4.x/Microsoft Windows Kernel 4.90", "score": 87}, "1,3,6,12,15,42": {"device_name": "Samsung Android", "score": 50}, "1,3,12,43": {"device_name": "Network Boot Agent/Etherboot/Sun Blade 100", "score": 87}, "1,3,6,12,15,17,26,28,33,40,41,42,58,59,119,120,121": {"device_name": "VoIP Device/Biamp devices", "score": 57}, "1,3,6,7,12,15,44,51,54,58,59,69": {"device_name": "Hardware Manufacturer/SEH COMPUTERTECHNIK GMBH", "score": 87}, "1,3,6,12,15,17,23,28,29,31,33,40,41,42,44": {"device_name": "Storage Device/Synology NAS", "score": 87}, "1,2,3,6,12,15,28,42,43,66,125": {"device_name": "VoIP Device/Grandstream VoIP", "score": 87}, "1,2,3,4,6,15,28,42,43,66,160": {"device_name": "VoIP Device/Polycom VoIP/Polycom Conference IP Phone", "score": 87}, "3,6": {"device_name": "Gaming Console/Microsoft Gaming Console/Xbox", "score": 87}, "1,3,4,28,42,66,156": {"device_name": "VoIP Device/ShoreTel IP Phone", "score": 87}, "1,3,6,7,15,42,44,58,59,66": {"device_name": "VoIP Device/Sipura VoIP Adaptor", "score": 87}, "1,3,6,15,33,44": {"device_name": "Switch and Wireless Controller/Cisco Switches/Cisco Catalyst 35xx", "score": 87}, "0,1,3,6,15,31,33,43,44,46,47,112,121,176,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.1", "score": 87}, "1,3,4,6,12,15,17,23,28,29,31,33,40,41,42": {"device_name": "Audio, Imaging or Video Equipment/IP Camera/ACTi Corporation IP Camera", "score": 87}, "1,1,3,3,6,6,15,15,44,44,46,46,47,47": {"device_name": "Router, Access Point or Femtocell/Router/Cisco/Linksys Router", "score": 87}, "1,2,3,6,7,15,42,44,58,59,66,150,151": {"device_name": "VoIP Device/Cisco VoIP/Cisco/Linksys SPA series IP Phone", "score": 87}, "1,3,6,12,15,28,33,43,121": {"device_name": "Router, Access Point or Femtocell/Router/Netgear Router", "score": 87}, "1,3,6,15,51,58,59,255": {"device_name": "Router, Access Point or Femtocell/Router/Freebox Wireless Router", "score": 87}, "1,3,6,28,33,51,58,59": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,3,6,12,15,28,33,51,58,59,119,121": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,3,6,44,46,47,58,59": {"device_name": "Printer or Sc<PERSON>r/Tally Printer", "score": 87}, "1,1,3,6,28,42,66": {"device_name": "VoIP Device/Aastra VoIP", "score": 87}, "1,3,3,5,6,11,12,13,15,16,17,18,43,54,60,67,128,129,130,131,132,133,134,135": {"device_name": "Network Boot Agent/PXE", "score": 87}, "1,3,4,6,15": {"device_name": "Internet of Things (IoT)/Alps Electric", "score": 87}, "1,3,6,7,15,42,58,59,66": {"device_name": "VoIP Device/Linksys PAP VoIP", "score": 87}, "1,3,6,12,15,26,28,33,51,54,58,59,119,121,252": {"device_name": "Operating System/Google OS/Chrome OS", "score": 87}, "1,3,6,15,28,33,44,51,58,59,119,121": {"device_name": "Phone, Tablet or Wearable/Generic Android/Motorola Android", "score": 87}, "1,3,6,12,15,28,44,66,67,78,79": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,5,6,15,28,44": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Trendnet WAP/Trendnet Access Point", "score": 87}, "1,3,6,15,28,43,58,59,66,99,128,131,144,157,188,191,205,219,223,224,227,230,232,235,238,241,244,247,249,251,254": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone", "score": 87}, "1,3,6,15,69,70": {"device_name": "Point of Sale Device/Moneris HiSpeed 3100IP", "score": 87}, "1,3,6,7,15,33,43,44,150": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Cisco WAP", "score": 87}, "1,3,6,12,15,28,33,50,121": {"device_name": "Router, Access Point or Femtocell/Router/Belkin Wireless Router", "score": 87}, "1,3,6,15,28,43,58,59,66,128,131,144,157,188,191,205,219,223,232,247,251": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone", "score": 87}, "3,6,26,28,58,59": {"device_name": "Gaming Console/Nintendo Gaming Console/Nintendo Gamecube", "score": 87}, "1,3,6,15,44,47": {"device_name": "Printer or Scanner/Canon Printer", "score": 87}, "3,6,15,31,33,43,44,46,47": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.0", "score": 87}, "78,79,85": {"device_name": "Network Boot Agent/Novell Netware Client", "score": 87}, "1,3,6,15,26,28,33,51,58,59": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "3,6,15,31,33,43,44,46,47,252": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.0", "score": 87}, "1,2,3,4,6,7,12,15,28,42,43,66,67,120": {"device_name": "VoIP Device/Yealink VoIP", "score": 88}, "1,2,3,6,42,43,66": {"device_name": "VoIP Device/Aastra VoIP", "score": 87}, "3,6,12,15,31,33,43,44,46,47,249,252": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.1,5.2", "score": 87}, "1,3,43,60,67": {"device_name": "Network Boot Agent/Apple Netboot", "score": 87}, "1,3,6,12,15,44,51,54,58,59,144": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,3,6,12,13,15,66,67": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,12,66,67,150": {"device_name": "Switch and Wireless Controller/Cisco Switches/Cisco Catalyst 29xx", "score": 87}, "1,3,6,15,43,44,46,47,77,252": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 4.x/Microsoft Windows Kernel 4.10", "score": 87}, "1,3,28,43,54,58,59,60": {"device_name": "VoIP Device/Alcatel IP Phone/Alcatel Advanced Reflex IP Phone", "score": 87}, "1,3,6,15,43,60,67": {"device_name": "Operating System/Apple OS", "score": 87}, "1,2,3,6,12,15,28,40,41": {"device_name": "Operating System/Linux OS/SUSE Linux/Novell Desktop", "score": 87}, "1,3,28,43,58,59,66,128,131,144,157,188,191,205,219,223,224,227,230,232,235,238,241,244,247,249,251,254": {"device_name": "VoIP Device/Nortel VoIP/Nortel IP Phone", "score": 87}, "1,3,6,12,15,43,66,67": {"device_name": "VoIP Device/Sunrocket VoIP Gizmo", "score": 87}, "0,1,3,6,15,31,33,43,44,46,47,64,121,128,249": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 6.x/Microsoft Windows Kernel 6.0", "score": 87}, "1,3,6,12,13,15,44,66,67,81,252": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,12,15,28,33,42,51,54,58,59,119,121": {"device_name": "Operating System/Linux OS/Generic Linux", "score": 87}, "1,3,6,15,44,46,47,137,215,224,226": {"device_name": "Thin Client/Neoware Capio Windows CE", "score": 87}, "1,2,3,6,12,15,26,28,40,41,42,42,119,121,121,249,252": {"device_name": "Operating System/Linux OS/RedHat/Fedora-based Linux/Fedora/Fedora 15 or 16 based distro", "score": 87}, "1,3,6,12,15,28,33,40,41,42,44,249": {"device_name": "Router, Access Point or Femtocell/Router/Trendnet Wireless Router", "score": 87}, "1,3,6,12,42,43,44,51,54,58,59,66,120,125,128,129,130,131,132,133,134,135,138,224,225,226": {"device_name": "VoIP Device/Mitel IP Phone", "score": 88}, "1,3,6,15,78,79,112,113": {"device_name": "Operating System/Apple OS/Mac OS X or macOS/Mac OS X", "score": 87}, "85,86,87": {"device_name": "Network Boot Agent/Novell Netware Client", "score": 87}, "1,3,4,43": {"device_name": "Switch and Wireless Controller/HP ProCurve Switches", "score": 87}, "1,2,3,6,12,15,26,28,40,41,42": {"device_name": "Operating System/Linux OS/RedHat/Fedora-based Linux", "score": 87}, "1,3,5,6,13,15,17,23,28,42,49,50,51,53,54,56,66,67": {"device_name": "Samsung S8500", "score": 50}, "1,2,3,6,12,15,28,44,46": {"device_name": "Operating System/Linux OS/Linspire", "score": 87}, "1,3,6,15,28,44,47": {"device_name": "Printer or Scanner/<PERSON>h Printer/Ricoh Multifunction Printer", "score": 87}, "1,3,6,7,12,15,18,22,43,44,54,58,59,69,81,119,153,154": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,7,9,12,15,23,28,29,31,33,40,41,42,44,200": {"device_name": "Operating System/Linux OS/Generic Linux", "score": 87}, "1,3,6,15,23,51,54": {"device_name": "Audio, Imaging or Video Equipment/Motorola NIM100", "score": 87}, "1,3,6,12,15,28,40,42": {"device_name": "Printer or Scanner/E<PERSON>on Printer", "score": 87}, "1,2,3,6,15,28": {"device_name": "<PERSON> and Noble <PERSON> (eReader)", "score": 50}, "1,1,3,3,6,6,15,15,44,44,46,46,47,47,66": {"device_name": "Router, Access Point or Femtocell/Router/Cisco/Linksys Router", "score": 87}, "1,2,3,6,12,15,26,28,44,45,46,47,69,70,78,79,85,86,87,88": {"device_name": "Printer or Scanner/Xerox Printer", "score": 87}, "1,2,3,6,12,15,28,44,47": {"device_name": "Operating System/Linux OS/Debian-based Linux", "score": 87}, "3,6,15,31,33,43,44,46,47,249,252": {"device_name": "Operating System/Windows OS/Microsoft Windows kernel 5.x/Microsoft Windows Kernel 5.1,5.2", "score": 87}, "1,3,6,12,15,28,33,42,51,58,59,119": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "2,3,6,12,15,28,40,41,42": {"device_name": "Operating System/Linux OS/RedHat/Fedora-based Linux", "score": 87}, "1,3,6,15,43,66,69,176": {"device_name": "VoIP Device/Avaya IP Phone", "score": 87}, "1,2,3,6,12,15,26,28,40,41,42,42,119,121,121,249": {"device_name": "Operating System/Linux OS/RedHat/Fedora-based Linux/RHEL 6.4 or Centos6.4", "score": 87}, "1,3,6,7,12,15,18,22,44,54,58,59,69,119,144": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,12,15,42,69,70,88": {"device_name": "Monitoring and Testing Device/APC/NetBotz WallBotz 400C", "score": 87}, "1,3,6,12,15,17,23,28,29,31,33,40,41,42,119": {"device_name": "Operating System/Linux OS/Gentoo Linux", "score": 87}, "1,3,6,15,54,66,67,100,129,131,132,150": {"device_name": "Switch and Wireless Controller/Cisco Switches/Cisco Small Business", "score": 87}, "1,3,6,7,12,15,42,43,44,51,66,67,120,150": {"device_name": "Switch and Wireless Controller/Juniper Switches", "score": 73}, "1,3,6,15,26,28,43,51,58,59": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,2,3,6,15,26,28,44,45,46,47,69,70,78,79,88,120": {"device_name": "Printer or Scanner/Xerox Printer", "score": 73}, "1,3,6,15,44,46,95,119,121,252": {"device_name": "Operating System/Apple OS", "score": 87}, "1,2,3,6,7,15,33,42,43,51,58,59,66,67,120,121,125": {"device_name": "VoIP Device", "score": 29}, "1,3,51,54": {"device_name": "Thin Client/Broadcom NetXtreme II", "score": 87}, "1,3,15,44,46": {"device_name": "Printer or Scanner/Konica Minolta Printer/Konica Minolta Multifunction Printer", "score": 87}, "1,3,6,15,54,66,67,100,125,129,150": {"device_name": "Switch and Wireless Controller/Cisco Switches/Cisco Small Business", "score": 87}, "1,3,6,7,12,15,18,22,43,44,54,58,59,69,81,119,144,153,154": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,12,15,26,33,42,121": {"device_name": "Operating System/Windows OS", "score": 87}, "1,2,3,6,12,15,26,28,33,42,44,47,119,121,249,252": {"device_name": "Operating System/Linux OS/Generic Linux", "score": 87}, "1,3,6,12,42,252": {"device_name": "Operating System/Tizen OS", "score": 87}, "1,2,3,6,12,13,15,42,44,66,67,120,125": {"device_name": "Printer or Scanner/Brother Printer", "score": 73}, "43,120": {"device_name": "Operating System/Windows OS", "score": 73}, "1,3,6,15,26,28,51,58,59": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,3,6,12,15,28,33,42,249": {"device_name": "Router, Access Point or Femtocell", "score": 66}, "1,3,6,7,12,15,18,43,44,66,67,69,81,150": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,15,212": {"device_name": "Router, Access Point or Femtocell/Router/Cisco/Linksys Router", "score": 87}, "1,3,6,12,15,28,33,42,121,249": {"device_name": "Router, Access Point or Femtocell", "score": 66}, "1,3,6,12,15,28,42,121,212": {"device_name": "Router, Access Point or Femtocell", "score": 66}, "1,3,6,12,15,28,42,121": {"device_name": "Router, Access Point or Femtocell", "score": 66}, "1,3,6,12,15,28,33,44,51,54,58,59,81,119,121,252": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,12,15,28,40,41,42,43,119,121": {"device_name": "Audio, Imaging or Video Equipment/Video Equipment (Smart TV, Smart Players, etc.)/Infomir IP TV", "score": 87}, "1,3,6,15,119,121,252": {"device_name": "Operating System/Apple OS", "score": 87}, "1,3,6,15,28,33,43,51,58,59": {"device_name": "Operating System/Google OS/Android OS", "score": 87}, "1,3,6,15,51,54,58,59,66,67,125": {"device_name": "VoIP Device/Panasonic VoIP/Panasonic KX-UDS124CE SIP-DECT Base Station", "score": 87}, "1,3,6,12,15,28,33,44,51,53,54,58,59,81,119,121,252": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,3,6,12,15,28,33,40,41,42,44,46,47,121,212,249": {"device_name": "Router, Access Point or Femtocell", "score": 66}, "1,3,6,12,15,26,28,33,40,41,42,44,45,46,47,85,86,87,119,121,249,252": {"device_name": "Operating System/Linux OS", "score": 87}, "1,3,6,12,13,15,44,66,67,81,119,252": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,2,3,6,7,15,35,42,43,58,59,66,150,159,160": {"device_name": "VoIP Device/Cisco VoIP", "score": 87}, "1,3,6,12,15,28,33,44,121,212,249": {"device_name": "Router, Access Point or Femtocell", "score": 66}, "1,3,6,12,15,28,33,121,212,249": {"device_name": "Router, Access Point or Femtocell/Router/Netgear Router", "score": 87}, "1,3,6,15,31,33,43,44,46,47,119,121,249,252": {"device_name": "Operating System/Windows OS/Microsoft Windows Kernel 10.0", "score": 87}, "1,3,4,6,12,15,26,42,44,51,54,58,59,190": {"device_name": "Printer or Scanner/Lexmark Printer", "score": 87}, "1,3,6,7,12,15,18,42,43,44,66,67,69,81,150": {"device_name": "Printer or Scanner/HP Printer", "score": 87}, "1,2,3,6,12,15,28,33,42,44,47,121,249,252": {"device_name": "Audio, Imaging or Video Equipment/IP Camera/WiLife IP Camera", "score": 87}, "1,3,6,33,42,43,121,138": {"device_name": "Router, Access Point or Femtocell/Router/MikroTik (RouterOS) Router", "score": 87}, "1,2,3,6,12,15,26,28,33,40,41,42,54,119,121,249,252": {"device_name": "Operating System/Linux OS", "score": 87}, "1,3,6,12,15,28,42,43": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/Adtran WAP", "score": 87}, "1,3,6,12": {"device_name": "Internet of Things (IoT)/Chamberlain MyQ Smart Home", "score": 87}, "1,3,6,10,12,14,15,28,42,87": {"device_name": "Audio, Imaging or Video Equipment/King Champion WiFi Speaker", "score": 87}, "1,3,6,22,23,24,33,42": {"device_name": "Router, Access Point or Femtocell/Wireless Access Point/AboCom WAP", "score": 87}, "1,3,6,15,28,31,33,43,44,46,47,121": {"device_name": "Internet of Things (IoT)/Generic IoT/Espressif", "score": 87}}