import logging

from server.db.models.inventory import SnmpDevice
from server.db.models.monitor import monitor_db
from server.util import prometheus_util

LOG = logging.getLogger(__name__)

UNIT_TO_BASE_MAPPING = {
    """
    单位字符串 -> 转换为基本单位的lambda函数
    - 电流: mA
    - 电压: V
    - 功率: W
    - 速度: bps
    - 光功率: dBm
    - 温度: 摄氏度
    - 存储: byte
    - 转速: rpm 或 %
    - 百分比: %
    """

    # 电压单位 (基本单位: V)
    "uv": lambda v: v / (1000 ** 6),  # 微伏 -> 伏特 (1V = 1e6 μV)
    "μv": lambda v: v / (1000 ** 6),
    "mv": lambda v: v / 1000,  # 毫伏 -> 伏特 (1V = 1000 mV)
    "v": lambda v: v,  # 伏特（基本单位）
    "kv": lambda v: v * 1000,  # 千伏 -> 伏特 (1kV = 1000 V)
    "100uv": lambda v: (v / 100) / (1000 ** 6),  # 100μV -> 伏特
    "voltage": lambda v: v,  # 直接表示伏特
    "mvolts": lambda v: v / 1000,  # 毫伏 -> 伏特

    # 字节单位 (基本单位: 字节)
    "b": lambda v: v,
    "bytes": lambda v: v,
    "kb": lambda v: v * 1024,
    "mb": lambda v: v * (1024 ** 2),
    "gb": lambda v: v * (1024 ** 3),

    # 电流单位 (基本单位: mA)
    "ua": lambda v: v / 1000,  # 微安 -> 毫安 (1mA = 1000 μA)
    "μa": lambda v: v / 1000,
    "ma": lambda v: v,  # 毫安（基本单位）
    "a": lambda v: v * 1000,  # 安培 -> 毫安 (1A = 1000 mA)
    "ka": lambda v: v * (1000 ** 2),  # 千安 -> 毫安 (1kA = 1e6 mA)
    "2ua": lambda v: (v / 2) / 1000,  # 2μA -> 毫安

    # 温度单位 (基本单位: 摄氏度)
    "c": lambda v: v,
    "°c": lambda v: v,
    "celsius": lambda v: v,
    "f": lambda v: (v - 32) / 1.8,
    "°f": lambda v: (v - 32) / 1.8,
    "fahrenheit": lambda v: (v - 32) / 1.8,
    "k": lambda v: v - 273.15,
    "kelvin": lambda v: v - 273.15,
    "0.00390625celsius": lambda v: v * 0.00390625,

    # 速率单位 (基本单位: bps)
    "bps": lambda v: v,
    "kbps": lambda v: v * 1000,
    "mbps": lambda v: v * (1000 ** 2),
    "gbps": lambda v: v * (1000 ** 3),

    # 时间
    "0.01s": lambda v: v / 100,

    # 光功率
    "0.01dbm": lambda v: v / 100,
}


def convert_to_base_unit(value: float, from_unit_str: str) -> tuple[float, str]:
    """
    将输入值转换为对应类型的基本单位

    Args:
        value: 输入数值
        from_unit_str: 输入单位（字符串）

    Returns:
        (转换后的基本单位数值, 基本单位名称)
    """
    # 字符串尝试转为float
    if type(value) is str:
        value = float(value)

    convert_func = UNIT_TO_BASE_MAPPING.get(from_unit_str.lower())
    # 未找到不做转换
    if not convert_func:
        raise ValueError(f"Not fount convert function {from_unit_str}")

    return convert_func(value)


class SnmpDataFilteringService:
    def __init__(self, ):
        super().__init__()
        self.name_dict = {}
        self.item_metric_map = {
            "device_location": ["snmp_device_label"],
            "sysname": ["snmp_device_label"],
            "system_uptime": ["snmp_system_uptime"],
            "device_mac": ["snmp_device_label"],
            "device_sw_version": ["snmp_device_label"],
            "device_hw_version": ["snmp_device_label"],
            "serial_number": ["snmp_device_label"],
            "cpu_utilization_5sec": ["snmp_cpu_used_percentage"],
            "device_temperature": ["snmp_device_temperature"],
            "memory_used": ["snmp_memory_used_percentage", "snmp_memory_usage_size", "snmp_memory_size"],
            "memory_free": ["snmp_memory_used_percentage", "snmp_memory_usage_size", "snmp_memory_size"],
            "memory_size": ["snmp_memory_used_percentage", "snmp_memory_usage_size", "snmp_memory_size"],
            "flash_used": ["snmp_flash_free_size", "snmp_flash_usage_size", "snmp_flash_size"],
            "flash_free": ["snmp_flash_free_size", "snmp_flash_usage_size", "snmp_flash_size"],
            "flash_size": ["snmp_flash_free_size", "snmp_flash_usage_size", "snmp_flash_size"],
            "fan_speed": ["snmp_fanindex", "snmp_fanindex_fan_speed"],
            "fan_status": ["snmp_fanindex", "snmp_fanindex_fan_speed"],
            "supply_status": ["snmp_device_label", "snmp_supplyindex"],
            "optical_module_temperature": ["snmp_ifindex", "snmp_ifindex_optical_module_temperature"],
            "rx_power": ["snmp_ifindex", "snmp_ifindex_rx_power"],
            "tx_power": ["snmp_ifindex", "snmp_ifindex_tx_power"],
            "bias_current": ["snmp_ifindex", "snmp_ifindex_bias_current"],
            "bias_voltage": ["snmp_ifindex", "snmp_ifindex_bias_voltage"],
            "poe_power": ["snmp_ifindex", "snmp_ifindex_poe_power"],
            "interface_type": ["snmp_ifindex"],
            "operational_status": ["snmp_ifindex"],
            "port_speed": ["snmp_ifindex", "snmp_ifindex_interface_speed"],
            "in_packets_with_errors": ["snmp_ifindex", "snmp_ifindex_in_packets_with_errors"],
            "in_packets_discarded": ["snmp_ifindex", "snmp_ifindex_in_packets_discarded"],
            "out_packets_with_errors": ["snmp_ifindex", "snmp_ifindex_out_packets_with_errors"],
            "out_packets_discarded": ["snmp_ifindex", "snmp_ifindex_out_packets_discarded"],
            "bits_sent": ["snmp_ifindex", "snmp_ifindex_bits_sent"],
            "bits_received": ["snmp_ifindex", "snmp_ifindex_bits_received"],
        }

    def _get_item_name_dimension(self, item_names: list[str]):
        query_name_set = set()
        for item_name in item_names:
            query_name_set.update(self.item_metric_map.get(item_name, []))
        return list(query_name_set)

    def _get_metric(self, filters):
        """
        获取指标数据，支持按sn、metric_name和index_value筛选
        :param filters: 筛选条件，包含__name__、sn，可选index_value
        """
        sn = filters['sn']
        metric_name = filters['__name__']
        index_value = filters.get('index_value')  # 可选参数：索引值

        # 从三维字典中查询数据
        metric_data = self.name_dict.get(metric_name, {}).get(sn, {})

        if index_value is not None:
            # 如果指定了index_value，返回该索引的数据
            return metric_data.get(index_value, [])
        else:
            # 未指定index_value，返回该sn下的所有索引数据（平铺为列表）
            return [item for items in metric_data.values() for item in items]

    def _query_fsos_flash_usage(self, target=None):
        metric_names = [
            "snmp_flash_free_size",
            "snmp_flash_usage_size",
            "snmp_flash_size",
        ]

        values = {}
        units = {}

        for metric_name in metric_names:
            result = self._get_metric({"__name__": metric_name, "sn": target})
            if result:
                # 直接取第一个元素的值
                values[metric_name] = float(result[0]["value"][1])
                # 记录单位（优先取第一个元素的单位）
                if "unit" in result[0]["metric"]:
                    units[metric_name] = result[0]["metric"]["unit"]

        # 提取三个关键值
        free = values.get("snmp_flash_free_size")
        used = values.get("snmp_flash_usage_size")
        total = values.get("snmp_flash_size")

        # 补全缺失值（三者知二求一）
        if free is not None and total is not None and used is None:
            used = total - free
        elif used is not None and total is not None and free is None:
            free = total - used
        elif used is not None and free is not None and total is None:
            total = used + free

        # 确定最终单位（优先级：总容量 > 已用 > 剩余）
        unit = units.get("snmp_flash_size") or units.get("snmp_flash_usage_size") or units.get("snmp_flash_free_size")

        return [{
            "values": [{
                "free_size": round(free, 2) if free is not None else None,
                "usage_size": round(used, 2) if used is not None else None,
                "total_size": round(total, 2) if total is not None else None
            }],
            "unit": unit
        }]

    def _query_fsos_memory_usage(self, target=None):
        metric_names = [
            "snmp_memory_used_percentage",
            "snmp_memory_usage_size",
            "snmp_memory_size",
        ]

        units = {}
        values = {}
        for metric_name in metric_names:
            result = self._get_metric({"__name__": metric_name, "sn": target})
            if result:
                values[metric_name] = float(result[0]["value"][1])
                if "unit" in result[0]["metric"]:
                    units[metric_name] = result[0]["metric"]["unit"]

        perc = values.get("snmp_memory_used_percentage")
        used = values.get("snmp_memory_usage_size")
        total = values.get("snmp_memory_size")

        if perc is not None and total is not None and used is None:
            used = total * perc / 100
        if used is not None and total is not None and perc is None and total > 0:
            perc = used / total * 100
        if used is not None and perc is not None and total is None and perc > 0:
            total = used / (perc / 100)

        unit = units.get("snmp_memory_used_percentage") or units.get("snmp_memory_usage_size") or units.get("snmp_memory_size")

        return [{
            "values": [{
                "used_percentage": round(perc, 2) if perc is not None else None,
                "used_size": round(used, 2) if used is not None else None,
                "total_size": round(total, 2) if total is not None else None,
                "unit": unit
            }]
        }]

    def _get_device_item_name(self, device: SnmpDevice, item_name: str):
        """
        获取item name对应的数据，转换为基本单位
        """
        parent_value = None
        children = []

        try:
            match item_name:
                case "device_location":
                    res = self._get_metric({"__name__": "snmp_device_label", "sn": device.sn})
                    parent_value = res[0]["metric"]["device_location"]
                case "sysname":
                    res = self._get_metric({"__name__": "snmp_device_label", "sn": device.sn})
                    parent_value = res[0]["metric"]["sysname"]
                case "system_uptime":
                    res = self._get_metric({"__name__": "snmp_system_uptime", "sn": device.sn})
                    parent_value = round(convert_to_base_unit(res[0]["value"][1], res[0]["metric"]["unit"]))
                case "device_mac":
                    res = self._get_metric({"__name__": "snmp_device_label", "sn": device.sn})
                    parent_value = prometheus_util.decode_mac_address(res[0]["metric"]["mac_address"])
                case "device_sw_version":
                    parent_value = device.version
                case "device_hw_version":
                    res = self._get_metric({"__name__": "snmp_device_label", "sn": device.sn})
                    parent_value = res[0]["metric"]["device_hw_version"]
                case "serial_number":
                    res = self._get_metric({"__name__": "snmp_device_label", "sn": device.sn})
                    parent_value = res[0]["metric"]["sn"]
                case "cpu_utilization_5sec":
                    res = self._get_metric({"__name__": "snmp_cpu_used_percentage", "sn": device.sn})
                    parent_value = res[0]["value"][1]
                case "device_temperature":
                    res = self._get_metric({"__name__": "snmp_device_temperature", "sn": device.sn})
                    parent_value = convert_to_base_unit(res[0]["value"][1], res[0]["metric"]["unit"])
                case "memory_used":
                    query_res = self._query_fsos_memory_usage(device.sn)
                    parent_value = query_res[0]["values"][-1]["used_percentage"]
                case "memory_free":
                    query_res = self._query_fsos_memory_usage(device.sn)
                    parent_value = convert_to_base_unit(query_res[0]["values"][-1]["total_size"] - query_res[0]["values"][-1]["used_size"], query_res[0]["values"][-1]["unit"])
                case "memory_size":
                    query_res = self._query_fsos_memory_usage(device.sn)
                    parent_value = convert_to_base_unit(query_res[0]["values"][-1]["total_size"], query_res[0]["values"][-1]["unit"])
                case "flash_used":
                    query_res = self._query_fsos_flash_usage(device.sn)
                    parent_value = convert_to_base_unit(query_res[0]["values"][-1]["usage_size"], query_res[0]["unit"])
                case "flash_free":
                    query_res = self._query_fsos_flash_usage(device.sn)
                    parent_value = convert_to_base_unit(query_res[0]["values"][-1]["free_size"], query_res[0]["unit"])
                case "flash_size":
                    query_res = self._query_fsos_flash_usage(device.sn)
                    parent_value = convert_to_base_unit(query_res[0]["values"][-1]["total_size"], query_res[0]["unit"])
                case "fan_speed":
                    snmp_fan_indexes = self._get_metric({"__name__": "snmp_fanindex", "sn": device.sn})
                    for index in snmp_fan_indexes:
                        index_value = index["value"][1]
                        snmp_fan_index_fan_speed = self._get_metric({"__name__": "snmp_fanindex_fan_speed", "sn": device.sn, "index_value": index_value})
                        name = snmp_fan_index_fan_speed[0].get("fan_name", f"fan{index_value}")
                        children.append({
                            "value": snmp_fan_index_fan_speed[0]["value"][1],
                            "unit": snmp_fan_index_fan_speed[0]["metric"]["unit"],
                            "item_name": f'{name}: Fan Speed',
                        })
                case "fan_status":
                    snmp_fan_indexes = self._get_metric({"__name__": "snmp_fanindex", "sn": device.sn})
                    for index in snmp_fan_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("fan_name", f"fan{index_value}")
                        children.append({
                            "value": index["metric"]["fan_status"],
                            "item_name": f'{name}: Fan Status',
                        })
                case "supply_status":
                    query_res = self._get_metric({"__name__": "snmp_supplyindex", "sn": device.sn})
                    for index in query_res:
                        index_value = index["value"][1]
                        name = index["metric"].get("supply_name", f"supply{index_value}")
                        children.append({
                            "value": index["metric"]["supply_status"],
                            "item_name": f'{name}: Supply Status',
                        })

                    count = len(children)
                    query_res = self._get_metric({"__name__": "snmp_device_label", "sn": device.sn})
                    if query_res[0]["metric"].get("power_status", None):
                        children.append({
                            "value": query_res[0]["metric"]["power_status"],
                            "item_name": f'supply{count}: Supply Status',
                        })
                case "optical_module_temperature":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_optical_module_temperature = self._get_metric({"__name__": "snmp_ifindex_optical_module_temperature", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": convert_to_base_unit(snmp_ifindex_optical_module_temperature[0]["value"][1], snmp_ifindex_optical_module_temperature[0]["metric"]["unit"]),
                            "item_name": f"{name}: Optical Module Temperature",
                        })
                case "rx_power":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_rx_power = self._get_metric({"__name__": "snmp_ifindex_rx_power", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": convert_to_base_unit(snmp_ifindex_rx_power[0]["value"][1], snmp_ifindex_rx_power[0]["metric"]["unit"]),
                            "item_name": f"{name}: Rx Power",
                        })
                case "tx_power":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_tx_power = self._get_metric({"__name__": "snmp_ifindex_tx_power", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": convert_to_base_unit(snmp_ifindex_tx_power[0]["value"][1], snmp_ifindex_tx_power[0]["metric"]["unit"]),
                            "item_name": f"{name}: Tx Power",
                        })
                case "bias_current":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_bias_current = self._get_metric({"__name__": "snmp_ifindex_bias_current", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": convert_to_base_unit(snmp_ifindex_bias_current[0]["value"][1], snmp_ifindex_bias_current[0]["metric"]["unit"]),
                            "item_name": f"{name}: Bias Current",
                        })
                case "bias_voltage":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_bias_voltage = self._get_metric({"__name__": "snmp_ifindex_bias_voltage", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": convert_to_base_unit(snmp_ifindex_bias_voltage[0]["value"][1], snmp_ifindex_bias_voltage[0]["metric"]["unit"]),
                            "item_name": f"{name}: Bias Voltage",
                        })
                case "poe_power":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_poe_power = self._get_metric({"__name__": "snmp_ifindex_poe_power", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": snmp_ifindex_poe_power[0]["value"][1],
                            "item_name": f"{name}: Poe Power",
                        })
                case "interface_type":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        children.append({
                            "value": index["metric"]["interface_type"],
                            "item_name": f"{name}: Interface Type",
                        })
                case "operational_status":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        children.append({
                            "value": index["metric"]["interface_status"],
                            "item_name": f"{name}: Operational Status",
                        })
                case "port_speed":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_interface_speed = self._get_metric({"__name__": "snmp_ifindex_interface_speed", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": convert_to_base_unit(snmp_ifindex_interface_speed[0]["value"][1], snmp_ifindex_interface_speed[0]["metric"]["unit"]),
                            "item_name": f"{name}: Port Speed",
                        })
                case "in_packets_with_errors":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_in_packets_with_errors = self._get_metric({"__name__": "snmp_ifindex_in_packets_with_errors", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": snmp_ifindex_in_packets_with_errors[0]["value"][1],
                            "item_name": f"{name}: In Packets With Errors",
                        })
                case "in_packets_discarded":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_in_packets_discarded = self._get_metric({"__name__": "snmp_ifindex_in_packets_discarded", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": snmp_ifindex_in_packets_discarded[0]["value"][1],
                            "item_name": f"{name}: In Packets Discarded",
                        })
                case "out_packets_with_errors":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_out_packets_with_errors = self._get_metric({"__name__": "snmp_ifindex_out_packets_with_errors", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": snmp_ifindex_out_packets_with_errors[0]["value"][1],
                            "item_name": f"{name}: Out Packets With Errors",
                        })
                case "out_packets_discarded":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_out_packets_discarded = self._get_metric({"__name__": "snmp_ifindex_out_packets_discarded", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": snmp_ifindex_out_packets_discarded[0]["value"][1],
                            "item_name": f"{name}: Out Packets Discarded",
                        })
                case "bits_sent":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_bits_sent = self._get_metric({"__name__": "snmp_ifindex_bits_sent", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": convert_to_base_unit(snmp_ifindex_bits_sent[0]["value"][1], snmp_ifindex_bits_sent[0]["metric"]["unit"]),
                            "item_name": f"{name}: Bits Sent",
                        })
                case "bits_received":
                    snmp_if_indexes = self._get_metric({"__name__": "snmp_ifindex", "sn": device.sn})
                    for index in snmp_if_indexes:
                        index_value = index["value"][1]
                        name = index["metric"].get("interface_name", f"interface{index_value}")
                        snmp_ifindex_bits_received = self._get_metric({"__name__": "snmp_ifindex_bits_received", "sn": device.sn, "index_value": index_value})
                        children.append({
                            "value": convert_to_base_unit(snmp_ifindex_bits_received[0]["value"][1], snmp_ifindex_bits_received[0]["metric"]["unit"]),
                            "item_name": f"{name}: Bits Received",
                        })
                case _:
                    pass
        except Exception:
            pass

        return parent_value, children

    def get_item_name_data(self, snmp_device_ids: list[int], item_names: list[str]):
        """
        获取item name对应的数据，转换为基本单位
        """

        session = monitor_db.get_session()
        snmp_devices: list[SnmpDevice] = session.query(SnmpDevice).filter(SnmpDevice.id.in_(snmp_device_ids)).all()
        sns_str = "|".join([device.sn for device in snmp_devices])

        query_name_list = self._get_item_name_dimension(item_names)
        item_names_str = "|".join(query_name_list)

        result = prometheus_util.query_prometheus(f'{{__name__=~"{item_names_str}", sn=~"{sns_str}"}}')

        # 按照__name__和sn分类查询结果
        for item in result:
            # 过滤异常值（-65535）
            try:
                if item.get('value') and item['value'][1] == '-65535':
                    continue
            except Exception as e:
                LOG.error(f'Filter abnormal value error: {e}')
                continue  # 跳过异常数据

            # 提取核心分组维度
            metric_name = item["metric"]["__name__"]
            sn = item["metric"]["sn"]
            # 获取index_value（如果存在，否则用None作为键）
            index_value = item["metric"].get('index_value', None)

            # 初始化三维字典：metric_name -> sn -> index_value -> 数据列表
            if metric_name not in self.name_dict:
                self.name_dict[metric_name] = {}
            if sn not in self.name_dict[metric_name]:
                self.name_dict[metric_name][sn] = {}
            if index_value not in self.name_dict[metric_name][sn]:
                self.name_dict[metric_name][sn][index_value] = []

            # 按维度存储数据
            self.name_dict[metric_name][sn][index_value].append(item)

        res = []
        for device in snmp_devices:
            for item_name in item_names:
                parent_value, children = self._get_device_item_name(device, item_name)
                res.append({
                    "id": f"{device.id}-{item_name}",
                    "sysname": device.sysname,
                    "sn": device.sn,
                    "item_name": item_name,
                    "value": f'{parent_value}' if parent_value else "--",
                    "children": children
                })
        return res
