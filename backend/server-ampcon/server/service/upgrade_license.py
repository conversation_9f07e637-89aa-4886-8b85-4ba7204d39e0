import json
import logging
import os
import platform
import time

from server.db.models import inventory
from server.ansible_lib.pica_lic import pica8_license
from server.util.ssh_util import interactive_push_file, get_interactive_session, \
    interactive_shell_linux_with_conn
from server import constants
from server.db.models.inventory import Switch
from server.db.models.inventory import inven_db as db

if platform.system() != 'Windows':
    from server.collect.single_collect_common import get_switch_license
from datetime import date
from server.collect.single_collect_common import update_import_switch_license_record
from server.db.models.inventory import inven_db, SystemConfig, Switch
from celery_app import my_celery_app
from celery_app.automation_task import AmpConBaseTask

LOG = logging.getLogger(__name__)
fmt = '%(asctime)s %(levelname)s %(module)s%(message)s'
format_str = logging.Formatter(fmt=fmt)
# portal license status
portal_license = {"INVALID_TOKEN": 0, 'LESS_30': 1, 'PORTAL_NO_LICNESE': 2, "LICENSE_EXIT": 3, 'NO_RESPONSE': 4}

audit_message = {"INFO_100": "License {action}. The license does not expire within the next 30 days for device {ip}",
                 "INFO_101": "License {action}. No license was found on the switch {ip}, a new license should be generated and installed",
                 "INFO_102": "License {action}. The license would be renewed on {ip} using an existing license registered in the license portal",
                 "INFO_103": "License {action}. No license was found on the switch {ip}, but one was found registered in the license portal's database and would need to be installed.",
                 "INFO_104": "License {action}. There are {count} {key_name} available licenses in the license portal",
                 "INFO_105": "License {action}. {value} {key_name} licenses should be generated and installed",
                 "INFO_106": "License {action}. The license would be renewed on license portal's database using an existing license registered in the license portal",
                 "INFO_107": "License {action}. The license should be renewed on {ip} using an existing license registered in the license portal"
                 }

action_message = {"INFO_200": "License {action}. The license does not expire within the next 30 days for device {ip}",
                  "INFO_201": "License {action}. No license was found on the switch {ip}, a new license was generated and installed",
                  "INFO_202": "License {action}. The license has been renewed on {ip}  using an existing license registered in the license portal",
                  "INFO_203": "License {action}. No license was found on the switch {ip}, but one was found registered in the license portal's database and was installed.",
                  }

error_message = {"ERR_402": "The number of available {key_name} licenses are less than needed",
                 "ERR_403": "No license was found on the switch {ip}, a new license was generated and installed",
                 "ERR_404": "There is no license registered matching that Hardware ID {hwd_id}",
                 "ERR_405": "Error in renewing the License",
                 "ERR_406": "The device with the IP {ip} is unreachable or does not run PicOS",
                 "ERR_407": "The license will expire within the next 30 days for device {ip}",
                 "ERR_417": "Invalid switch login credentials or error logging into the switch",
                 "ERR_418": "Invalid local server login credentials or server timeout error",
                 "ERR_419": "Error in getting license info from switch or error logging into the switch",
                 "ERR_420": "The license expire within the next 30 days for device{ip} in the license portal's database",
                 "ERR_421": "error in create license for {sn} switch",
                 "ERR_500": "Could not establish SSH connection to {ip}",
                 }


class UpgradeError(Exception):
    
    def __init__(self, sn, msg='', msg_status='error', db_status=''):
        self.sn = sn
        self.message = msg
        self.msg_status = msg_status
        self.db_status = db_status


class UpgradeLicense(object):
    def __init__(self, license, app_user, app_password, switch_op_user, switch_op_password, action, date_time,
                 report_time=''):
        self.license = license
        self.action = action
        self.hwid = None
        self.ip = None
        self.sn = None
        self.get_switch_info()
        used_system_config = inven_db.get_system_config_by_sn(self.sn)
        self.switch_op_user = used_system_config.switch_op_user
        self.switch_op_password = used_system_config.switch_op_password
        self.license_key = None
        self.app_user = app_user
        self.app_password = app_password
        self.remote_file = None
        self.license_detail = {}
        self.date_time = date_time
        self.report_time = report_time
    
    def get_switch_info(self):
        switch = db.get_model(Switch, filters={'sn': [self.license.sn_num]})
        self.hwid = switch.hwid
        self.ip = switch.mgt_ip
        self.sn = switch.sn
    
    def start(self):
        try:
            self.get_switch_info()
            # step 1: check switch local license whether available
            available = self.check_switch_license()
            
            # step 2: if not available, check portal if have license already
            if not available:
                license_key = self.check_license_portal_detail()
                
                # step 3: if need upgrade, then upgrade
                if self.action == 'action':
                    self.install_license(license_key)
        
        except UpgradeError as e:
            self.add_log(str(e).format(action=self.action), e.msg_status)
        except Exception as e:
            LOG.exception(e)
            self.add_log('license {action} failed, the error msg: {msg}'.format(action=self.action, msg=e))
    
    def check_license_portal_detail(self):
        _, license_detail = pica8_license(sn=self.sn).license_get(self.hwid)
        if 'less than 30 days' in license_detail:
            log_error = error_message['ERR_420'].format(ip=self.ip)
            raise UpgradeError(self.sn, log_error)
        elif 'There is no license registered matching that Hardware ID' in license_detail:
            log_info = error_message['ERR_404'].format(hwd_id=self.hwid)
            self.add_log(log_info, 'error')
            log_info = audit_message['INFO_106'].format(action=self.action)
            raise UpgradeError(self.sn, log_info, 'info')
        elif 'invalid token' in license_detail:
            log_error = error_message['ERR_418']
            raise UpgradeError(self.sn, log_error)
        elif not license_detail:
            log_error = error_message['ERR_418']
            raise UpgradeError(self.sn, log_error)
        elif 'error' in license_detail:
            log_error = error_message['ERR_405']
            raise UpgradeError(self.sn, log_error)
        else:
            return license_detail
    
    def install_license(self, license_key):
        # self.remote_file = BASE_DIR + 'switch.lic'
        self.remote_file = '/cftmp/' + self.sn + ".lic"
        lic_file_base = 'license/'
        if not os.path.exists(lic_file_base):
            os.makedirs(lic_file_base)
        lic_file_path = lic_file_base + self.sn + ".lic"
        with open(lic_file_path, 'w') as lic_file:
            lic_file.write(license_key)
        
        time.sleep(6)
        try_time_download = 0
        while try_time_download < 3:
            res, status = interactive_push_file(self.ip, self.switch_op_user, self.switch_op_password,
                                                lic_file_path,
                                                self.remote_file)
            self.check_ssh_res(res, status)
            invoke_shell, status, _ = get_interactive_session(self.ip, username=self.switch_op_user,
                                                              password=self.switch_op_password, timeout=180)
            # do sync
            cmd = 'sudo sync'
            res, status = self.ssh_excute(invoke_shell, status, cmd)
            time.sleep(3)
            cmd = 'ls ' + self.remote_file
            res, status = self.ssh_excute(invoke_shell, status, cmd)
            if 'No such file or directory' in res:
                LOG.error('Download license failed for %s switch', self.sn)
                time.sleep(5)
                try_time_download += 1
            else:
                LOG.info('Download license successful for %s switch', self.sn)
                break
        else:
            log_error = 'license download fail more than 3 times for switch %s' % self.sn
            raise UpgradeError(self.sn, log_error, )
        
        # we need store the local license detail before install new license
        cmd = 'sudo license -s'
        res, status = self.ssh_excute(invoke_shell, status, cmd)
        if 'No license installed' not in res:
            self.license_detail = json.loads(res.replace("Expire Date", "Support End Date"))
        else:
            self.license_detail.update({"Support End Date": 0})
        
        # Then, start to update license
        try_time = 0
        while try_time < 3:
            cmd = 'sudo license -i ' + self.remote_file
            res, status = self.ssh_excute(invoke_shell, status, cmd)
            if 'License successfully' in res:
                # should not restart the switch, otherwise, the traffic will broken in production network
                cmd = 'license -s'
                res, status = self.ssh_excute(invoke_shell, status, cmd)
                invoke_shell.close()
                # need consider the current version is 2.7 or 2.11, if 2.7, should be "Expire Date" imstead of "Support End Date"
                license_info = json.loads(res)
                license_info['Support End Date'] = license_info.get('Support End Date') or license_info.get(
                    'Expire Date')
                log_info = 'license upgrade success %s' % self.ip
                self.add_log(log_info)
                db.update_lic(self.sn,
                              expr_date=license_info['Support End Date'],
                              features=','.join(license_info['Feature']),
                              speed=license_info['Type'], status='Active')
                
                return
            
            LOG.error('install license failed in %s switch, %s', self.sn, res)
            time.sleep(2)
            # we need try again to install license
            try_time += 1
        
        log_error = 'license upgrade fail %s' % self.sn
        raise UpgradeError(self.sn, log_error)
    
    def add_log(self, log, level='info'):
        if self.report_time:
            db.add_switch_log(self.sn, self.report_time + log, self.action, level=level)
        else:
            db.add_switch_log(self.sn, self.report_time + log, level=level)
        
        if level == 'error':
            LOG.error(self.report_time + log)
        elif level == 'info':
            LOG.info(self.report_time + log)
    
    def ssh_excute(self, invoke_shell, status, cmd):
        if status == 1:
            log_error = error_message['ERR_417']
            raise UpgradeError(self.sn, log_error, )
        elif status == 2:
            log_error = error_message['ERR_500'].format(ip=self.ip)
            raise UpgradeError(self.sn, log_error)
        if status == 3:
            return interactive_shell_linux_with_conn(invoke_shell, cmd)
    
    def check_switch_license(self):
        license_info, status = get_switch_license(self.ip, self.switch_op_user, self.switch_op_password)
        
        self.check_ssh_res(license_info, status)
        
        # we need consider if 2.7, should be "'Support End Date'"
        if 'Expire Date' in license_info.keys():
            license_info.update({'Support End Date': license_info['Expire Date']})
        
        if status == constants.RMA_FAILED:
            db.update_lic(self.sn, key='None', speed=license_info['type'],
                          status='No License')
            
            log_info = audit_message['INFO_101'].format(action=self.action, ip=self.ip)
            self.add_log(log_info, 'info')
            return False
        
        # Support End Date > now time + 30 day
        if self.date_time < license_info['Support End Date']:
            log_info = audit_message['INFO_100'].format(action=self.action, ip=self.ip)
            self.add_log(log_info, 'info')
            db.update_lic(self.sn,
                          expr_date=license_info['Support End Date'],
                          features=','.join(license_info['Feature']),
                          speed=license_info['Type'], status='Active')
            return True
        
        log_info = audit_message['INFO_102'].format(action=self.action, ip=self.ip)
        self.add_log(log_info, 'info')
        log_info = audit_message['INFO_105'].format(action=self.action, value=self.sn, key_name=self.hwid)
        self.add_log(log_info, 'info')
        # judge switch license Support End Date compare with time now and Support End Date < now time + 30 day
        date_now = date.today().strftime('%Y-%m-%d')
        status = 'Expired' if date_now > license_info['Support End Date'] else 'Expiring'
        db.update_lic(self.sn,
                      expr_date=license_info['Support End Date'],
                      features=','.join(license_info['Feature']),
                      speed=license_info['Type'], status=status)
        log_error = error_message['ERR_407'].format(ip=self.ip)
        self.add_log(log_error, 'error')
        return False
    
    def check_ssh_res(self, res, status):
        if 'auth error' in res:
            log_error = error_message['ERR_417'].format(ip=self.ip)
            # self.add_log(log_info, 'error')
            raise UpgradeError(self.sn, log_error)
        elif 'connect failed' in res:
            log_error = error_message['ERR_500'].format(ip=self.ip)
            # self.add_log(log_info, 'error')
            raise UpgradeError(self.sn, log_error)
        elif status == constants.RMA_FAILED:
            return
        elif status != constants.RMA_ACTIVE:
            log_error = error_message['ERR_500'].format(ip=self.ip)
            raise UpgradeError(self.sn, log_error)


@my_celery_app.task(name="batch_upgrade_license", base=AmpConBaseTask)
def batch_upgrade_license(*args, **kwargs):
    sn, group, *rest = args
    kwargs.pop("celery_sn", "")
    kwargs.pop("celery_task_name", "")
    kwargs.pop("celery_group", "")
    db_session = db.get_session()
    # collect switch info
    system_config = inven_db.get_system_config_by_sn(sn)
    if not system_config:
        return 'Please configure System Management first'
    user = system_config.switch_op_user
    pw = system_config.switch_op_password
    switch = inven_db.get_model(Switch, filters={'sn': [sn]})
    if group:
        switch_licenses = []
        ag_switches = db_session.query(inventory.AssociationGroup).filter(
            inventory.AssociationGroup.group_name == group).all()
        if not ag_switches:
            raise ValueError("The group does not exist")
        for group_switch in ag_switches:
            license = db_session.query(inventory.License).filter(
                inventory.License.sn_num == group_switch.switch_sn).first()
            # update imported switch license record
            if license:
                switch_licenses.append(license)
            else:
                try:
                    update_import_switch_license_record(sn, switch.mgt_ip, user, pw)
                    switch_licenses = db_session.query(inventory.License).filter(inventory.License.sn_num == sn).all()
                except Exception as e:
                    LOG.info(str(e))
                    LOG.info('{} update licence failed'.format(sn))
    else:
        switch_licenses = db_session.query(inventory.License).filter(inventory.License.sn_num == sn).all()
        # update imported switch license record
        if not switch_licenses:
            try:
                update_import_switch_license_record(sn, switch.mgt_ip, user, pw)
                switch_licenses = db_session.query(inventory.License).filter(inventory.License.sn_num == sn).all()
            except Exception as e:
                LOG.info(str(e))
                LOG.info('{} update licence failed'.format(sn))

    for license in switch_licenses:
        upgrade_license = UpgradeLicense(license, *rest, **kwargs)
        upgrade_license.start()
