# revision identifiers, used by Alembic.
revision = 'v9'
down_revision = 'v8'
branch_labels = None
depends_on = None

from datetime import datetime
from alembic import op
import sqlalchemy as sa
from sqlalchemy import Enum
from sqlalchemy.dialects import mysql


def upgrade():
    op.create_table('monitor_target',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('sn', sa.String(length=128), nullable=True),
                    sa.<PERSON>umn('port', sa.Integer()),
                    sa.<PERSON>umn('device_type', sa.Integer()),
                    sa.<PERSON>umn('enable', sa.Integer()),
                    sa.Column('name', sa.String(length=64), nullable=True),
                    sa.Column('mac', sa.String(length=64), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )
    op.create_table('topology',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('name', sa.String(length=128), nullable=True),
                    sa.Column('description', sa.String(length=256), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    sa.Column('topology_type', Enum('topology', 'fabric', 'site', name='topology_type'), nullable=False,
                              default='topology'),
                    sa.Column('is_show_legend', sa.Boolean(), nullable=False, default=True),
                    sa.Column('zoom', sa.Float(), nullable=False, default=1.0),
                    sa.Column('translate_x', sa.Integer(), nullable=False, default=0),
                    sa.Column('translate_y', sa.Integer(), nullable=False, default=0),
                    sa.Column('is_in_tree_mode', sa.Boolean(), nullable=False, default=False),
                    sa.Column('is_show_default', sa.Boolean(), nullable=False, default=False),
                    )
    op.create_table('topology_node',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('node_label', sa.String(length=128), nullable=True),
                    sa.Column('topology_id', sa.Integer()),
                    sa.Column('monitor_target_id', sa.Integer()),
                    sa.Column('layer', sa.Integer(), nullable=True),
                    sa.Column('position_x', sa.Integer(), nullable=True),
                    sa.Column('position_y', sa.Integer(), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )
    op.create_table('topology_edge',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('topology_id', sa.Integer()),
                    sa.Column('source_id', sa.Integer()),
                    sa.Column('target_id', sa.Integer()),
                    sa.Column('source_port', sa.String(length=32)),
                    sa.Column('target_port', sa.String(length=32)),
                    sa.Column('description', sa.String(length=256), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('fabric',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('fabric_name', sa.String(length=64)),
                    sa.Column('description', sa.String(length=256), nullable=True),
                    sa.Column('topology_id', sa.Integer(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('association_fabric',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('fabric_id', sa.Integer()),
                    sa.Column('switch_id', sa.Integer()),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('site',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('site_name', sa.String(length=64)),
                    sa.Column('description', sa.String(length=256), nullable=True),
                    sa.Column('topology_id', sa.Integer(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('association_site',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('site_id', sa.Integer()),
                    sa.Column('switch_id', sa.Integer()),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_index('idx_monitor_switch_sn', 'monitor_target', ['sn'], unique=False)
    op.create_foreign_key('monitor_switch_sn_fk', 'monitor_target', 'switch', ['sn'], ['sn'], ondelete='CASCADE',
                          onupdate='CASCADE')

    op.create_foreign_key('node_topology_id_fk', 'topology_node', 'topology', ['topology_id'], ['id'],
                          ondelete='CASCADE')
    op.create_foreign_key('node_monitor_target_id_fk', 'topology_node', 'monitor_target', ['monitor_target_id'], ['id'],
                          ondelete='CASCADE')

    op.create_foreign_key('edge_topology_id_fk', 'topology_edge', 'topology', ['topology_id'], ['id'],
                          ondelete='CASCADE')
    op.create_foreign_key('edge_target_id_fk', 'topology_edge', 'topology_node', ['target_id'], ['id'],
                          ondelete='CASCADE')
    op.create_foreign_key('edge_source_id_fk', 'topology_edge', 'topology_node', ['source_id'], ['id'],
                          ondelete='CASCADE')

    op.create_index('idx_fabric_association_fabric_name', 'association_fabric', ['fabric_id'], unique=False)
    op.create_index('idx_fabric_association_switch_sn', 'association_fabric', ['switch_id'], unique=False)
    op.create_foreign_key('fabric_association_fabric_fk', 'association_fabric', 'fabric', ['fabric_id'], ['id'],
                          ondelete='CASCADE')
    op.create_foreign_key('fabric_association_switch_fk', 'association_fabric', 'switch', ['switch_id'], ['id'],
                          ondelete='CASCADE')

    op.create_foreign_key('fabric_topology_fk', 'fabric', 'topology', ['topology_id'], ['id'], ondelete='CASCADE')

    op.create_index('idx_site_association_site_id', 'association_site', ['site_id'], unique=False)
    op.create_index('idx_site_association_switch_id', 'association_site', ['switch_id'], unique=False)
    op.create_foreign_key('site_association_site_fk', 'association_site', 'site', ['site_id'], ['id'],
                          ondelete='CASCADE')
    op.create_foreign_key('site_association_switch_fk', 'association_site', 'switch', ['switch_id'], ['id'],
                          ondelete='CASCADE')

    op.create_foreign_key('site_topology_fk', 'site', 'topology', ['topology_id'], ['id'], ondelete='CASCADE')

    op.add_column('switch', sa.Column('mac_addr', sa.String(length=32), nullable=True))
    op.add_column('switch',
                  sa.Column('gnmi_interfaces', sa.Boolean(), nullable=True, server_default=sa.sql.expression.true()))
    op.add_column('switch',
                  sa.Column('gnmi_lldp', sa.Boolean(), nullable=True, server_default=sa.sql.expression.true()))
    op.add_column('switch',
                  sa.Column('gnmi_modules', sa.Boolean(), nullable=True, server_default=sa.sql.expression.true()))
    op.add_column('switch', sa.Column('gnmi_ai', sa.Boolean(), nullable=True, server_default=sa.sql.expression.true()))
    op.add_column('switch',
                  sa.Column('is_picos_v', sa.Boolean(), nullable=True, server_default=sa.sql.expression.false()))

    op.execute(
        """insert into topology (id, name, description, create_time, modified_time, topology_type, is_show_legend, zoom, translate_x, translate_y, is_in_tree_mode, is_show_default) values  (1, 'default', 'default', '2024-10-29 02:54:01', '2024-10-29 02:54:01', 'fabric', 1, 1, 0, 0, 0, 0);""")
    op.execute(
        """insert into topology (id, name, description, create_time, modified_time, topology_type, is_show_legend, zoom, translate_x, translate_y, is_in_tree_mode, is_show_default) values  (2, 'default', 'default', '2024-10-29 02:54:01', '2024-10-29 02:54:01', 'site', 1, 1, 0, 0, 0, 0);""")
    op.execute('commit')
    op.execute(
        """insert into fabric (id, fabric_name, description, topology_id, create_time, modified_time) values  (0, 'default', 'default', 1, '2024-10-29 02:54:01', '2024-10-29 02:54:01');""")
    op.execute(
        """insert into site (id, site_name, description, topology_id, create_time, modified_time) values  (0, 'default', 'default', 2, '2024-10-29 02:54:01', '2024-10-29 02:54:01');""")

    op.execute('''alter table license modify lic_feature varchar(128) null;''')

    op.execute('commit')


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('topology_edge')
    op.drop_table('topology_node')
    op.drop_table('topology')
    op.drop_table('monitor_target')
    op.drop_table('association_fabric')
    op.drop_table('fabric')
    op.drop_table('association_site')
    op.drop_table('site')
    op.drop_column('switch', 'mac_addr')
    op.drop_column('switch', 'gnmi_interfaces')
    op.drop_column('switch', 'gnmi_lldp')
    op.drop_column('switch', 'gnmi_modules')
    op.drop_column('switch', 'gnmi_ai')
    op.drop_column('switch', 'is_picos_v')
    # ### end Alembic commands ###
