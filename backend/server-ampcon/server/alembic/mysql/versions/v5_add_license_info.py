# revision identifiers, used by Alembic.
revision = 'v5'
down_revision = 'v4'
branch_labels = None
depends_on = None

from datetime import datetime
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

def upgrade():
    op.create_table('license_info',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.<PERSON>('license_data', sa.Text(65535)),
                    sa.<PERSON>umn('license_type', sa.String(32)),
                    sa.<PERSON>umn('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('license_info')
    # ### end Alembic commands ###
