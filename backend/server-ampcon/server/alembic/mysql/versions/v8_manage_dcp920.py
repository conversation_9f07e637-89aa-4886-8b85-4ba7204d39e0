# revision identifiers, used by Alembic.
revision = 'v8'
down_revision = 'v7'
branch_labels = None
depends_on = None

import sqlalchemy as sa

from alembic import op


def upgrade():
    op.create_table('distributed_lock',
                    sa.Column('lock_key', sa.String(128), primary_key=True),
                    sa.Column('lock_expiry', sa.DateTime(), nullable=False),
                    sa.Column('process_id', sa.String(255), nullable=True),
                    sa.Column('thread_id', sa.String(255), nullable=True),
                    sa.Column('host_name', sa.String(255), nullable=True),
                    sa.Column('acquired_time', sa.DateTime(), nullable=False)
                    )

    op.create_table('dcp_temp_data',
                    sa.Column('id', sa.String(128), primary_key=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('ip', sa.String(48), nullable=False, unique=True),
                    sa.Column('nmu', sa.String(64), nullable=True),
                    sa.Column('data', sa.Text(65535), nullable=True))

    op.create_table('dcp_device_basic',
                    sa.Column('id', sa.Integer(), nullable=False, primary_key=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('name', sa.String(64), nullable=False),
                    sa.Column('model', sa.String(32), nullable=False),
                    sa.Column('ip', sa.String(48), nullable=False, unique=True),
                    sa.Column('reachable_status', sa.Integer(), nullable=False),
                    sa.Column('longitude', sa.Float()),
                    sa.Column('latitude', sa.Float()))

    op.drop_constraint('switch_sn_fk', 'switch_ne_info', type_='foreignkey')

def downgrade():
    op.drop_table('distributed_lock')
    op.drop_table('dcp_temp_data')
    op.drop_table('dcp_device_basic')
    op.create_foreign_key('switch_sn_fk', 'switch_ne_info', 'switch', ['sn'], ['sn'], ondelete='CASCADE')
