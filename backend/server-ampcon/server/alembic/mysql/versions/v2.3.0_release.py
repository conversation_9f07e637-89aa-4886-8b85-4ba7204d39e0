# revision identifiers, used by Alembic.
revision = 'v2.3.0'
down_revision = 'v2.2.0'
branch_labels = None
depends_on = None

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy import inspect

connection = op.get_bind()

inspector = inspect(connection)


def upgrade():
    op.execute('''
        alter table client_device_info
            add port varchar(64) null;
    ''')

    op.execute('''
        alter table client_device_info
            add terminal_type varchar(256) null;
    ''')

    op.execute('''
        alter table client_device_info
            add ip_source varchar(128) null;
    ''')

    # add table system_config_license_account_info
    op.create_table(
        'system_config_license_account_info',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('system_config_id', sa.Integer(), nullable=False),
        sa.Column('expire_date', sa.String(length=255), nullable=True),
        sa.Column('standard_license_count', sa.Integer(), nullable=False),
        sa.Column('trial_license_count', sa.Integer(), nullable=False),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ['system_config_id'],
            ['system_config.id'],
            name='fk_system_config_license_account_info_system_config_id',
            ondelete='CASCADE',
            onupdate='CASCADE'
        ),
        sa.PrimaryKeyConstraint('id')
    )

    op.create_table(
        'snmp_device',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('mgt_ip', sa.String(length=32), nullable=False),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('sn', sa.String(length=64), nullable=False),
        sa.Column('model', sa.String(length=32), nullable=True),
        sa.Column('version', sa.String(length=32), nullable=True),
        sa.Column('revision', sa.String(length=32), nullable=True),
        sa.Column('reachable_status', sa.SmallInteger(), nullable=False, default=0),
        sa.Column('snmp_version', sa.Enum('v1', 'v2c', 'v3', name='snmp_version'), nullable=False),
        sa.Column('community', sa.String(length=32), nullable=True),
        sa.Column('context_name', sa.String(length=64), nullable=True),
        sa.Column('security_user', sa.String(length=64), nullable=True),
        sa.Column('security_level', sa.String(length=32), nullable=True),
        sa.Column('auth_protocol', sa.String(length=32), nullable=True),
        sa.Column('auth_key', sa.String(length=255), nullable=True),
        sa.Column('priv_protocol', sa.String(length=32), nullable=True),
        sa.Column('priv_key', sa.String(length=255), nullable=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True),
    )

    op.execute('''
        create table switch_common_job
        (
            create_time   datetime     NULL DEFAULT NULL,
            modified_time datetime     NULL DEFAULT NULL,
            id            int auto_increment
                primary key,
            switch_sn     varchar(64)  NOT NULL,
            job_name      varchar(128) NOT NULL,
            job_type      varchar(64)  NOT NULL,
            image_version varchar(32) NULL DEFAULT NULL,
            image_revision varchar(32) NULL DEFAULT NULL,
            start_time    bigint       NULL DEFAULT NULL,
            end_time      bigint       NULL DEFAULT NULL,
            state         varchar(64)  NULL DEFAULT NULL,
            CONSTRAINT fk_switch_common_job_switch_sn FOREIGN KEY (switch_sn)
                REFERENCES switch (sn)
                ON DELETE CASCADE
        );
    ''')

    op.execute('''
        create table switch_common_job_log 
        (
            create_time   datetime     NULL DEFAULT NULL,
            modified_time datetime     NULL DEFAULT NULL,
            id            int auto_increment
                primary key,
            switch_common_job_id int NOT NULL,
            log   mediumtext         NULL DEFAULT NULL,
            CONSTRAINT fk_switch_common_job_log_switch_common_job_id FOREIGN KEY (switch_common_job_id)
                REFERENCES switch_common_job (id)
                ON DELETE CASCADE
        )
    ''')

    ##### dc
    op.create_table('ddm_events',
                    sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
                    sa.Column('switch_sn', sa.String(64), nullable=True),
                    sa.Column('interface', sa.String(32), nullable=True),
                    sa.Column('channel', sa.String(32), nullable=True),
                    sa.Column('module_name', sa.String(64), nullable=True),
                    sa.Column('module_type', sa.String(64), nullable=True),
                    sa.Column('alert_level', sa.Enum('info', 'warning', 'error', name='alert_level_enum'),
                              nullable=False, server_default='info'),
                    sa.Column('alert_type', sa.Enum('SupplyVoltage', 'LaserTemperature', 'InputPower', 'OutputPower',
                                                    'LaserBiasCurrent', name='alert_type_enum'), nullable=False,
                              server_default='SupplyVoltage'),
                    sa.Column('count', sa.Integer(), nullable=False, server_default='1'),
                    sa.Column('alert_msg', sa.String(255), nullable=False),
                    sa.Column('resolved', sa.Boolean(), nullable=False, server_default='0'),
                    sa.Column('resolved_time', sa.DateTime(), nullable=True),
                    sa.Column('last_alert_time', sa.DateTime(), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True)
                    )

    # 创建索引
    op.create_index('idx_ddm_events_switch_sn', 'ddm_events', ['switch_sn'])
    op.create_index('idx_ddm_events_interface', 'ddm_events', ['interface'])
    op.create_index('idx_ddm_events_channel', 'ddm_events', ['channel'])
    op.create_index('idx_ddm_events_alert_type', 'ddm_events', ['alert_type'])
    op.create_index('idx_ddm_events_resolved', 'ddm_events', ['resolved'])

    op.execute('''
            CREATE TABLE modules_links (
                id               INTEGER PRIMARY KEY AUTO_INCREMENT,
                source_sn        VARCHAR(64),
                source_port      VARCHAR(64),
                target_sn        VARCHAR(64),
                target_port      VARCHAR(64),
                target_mac       VARCHAR(64),
                light_attenuation_threshold   INTEGER,
                link_status      BOOLEAN,
                create_time      datetime NULL DEFAULT NULL,
                modified_time    datetime NULL DEFAULT NULL,
                CONSTRAINT uq_network_link UNIQUE (source_sn, source_port, target_sn, target_port)
            );
        ''')

    # Create RoceEasyDeployConfiguration table
    op.create_table(
        'roce_easy_deploy_configuration',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('port', sa.JSON(), nullable=False),
        sa.Column('queue', sa.JSON(), nullable=False),
        sa.Column('enabled', sa.Boolean(), nullable=False),
        sa.Column('mode', sa.Enum('lossy', 'lossless')),
        sa.Column('config_data', sa.Text(), nullable=True),
        sa.Column('basic_info', sa.Text(), nullable=True),
        sa.Column('pcp_dscp_info', sa.Text(), nullable=True),
        sa.Column('lp_info', sa.Text(), nullable=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )
    op.create_foreign_key('fk_roce_easy_deploy_configuration_switch_sn', 'roce_easy_deploy_configuration', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # Create DLBConfiguration table
    op.create_table(
        'dlb_configuration',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('dlb_enabled', sa.Boolean(), nullable=False),
        sa.Column('mode', sa.Enum('dlb-normal', 'dlb-optimal', 'dlb-assigned')),
        sa.Column('config_data', sa.Text(), nullable=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )
    op.create_foreign_key('fk_dlb_configuration_switch_sn', 'dlb_configuration', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # Create PfcConfiguration table
    op.create_table(
        'pfc_configuration',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('profile_name', sa.String(length=64), nullable=False),
        sa.Column('port', sa.JSON(), nullable=False),
        sa.Column('queue', sa.JSON(), nullable=False),
        sa.Column('enabled', sa.Boolean(), nullable=False),
        sa.Column('config_data', sa.Text(), nullable=True),
        sa.Column('is_all_ports', sa.Boolean(), nullable=False, default=False),
        sa.Column('is_all_queues', sa.Boolean(), nullable=False, default=False),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )
    op.create_foreign_key('fk_pfc_configuration_switch_sn', 'pfc_configuration', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # Create PfcBufferIngressConfiguration table
    op.create_table(
        'pfc_buffer_ingress_configuration',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('port', sa.JSON(), nullable=False),
        sa.Column('queue', sa.JSON(), nullable=False),
        sa.Column('shared_ratio', sa.String(length=64), nullable=True),
        sa.Column('threshold', sa.String(length=64), nullable=True),
        sa.Column('guaranteed', sa.String(length=64), nullable=True),
        sa.Column('reset_offset', sa.String(length=64), nullable=True),
        sa.Column('headroom', sa.String(length=64), nullable=True),
        sa.Column('config_data', sa.Text(), nullable=True),
        sa.Column('is_all_ports', sa.Integer(), nullable=False, default=False),
        sa.Column('is_all_queues', sa.Integer(), nullable=False, default=False),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )
    op.create_foreign_key('fk_pfc_buffer_ingress_configuration_switch_sn', 'pfc_buffer_ingress_configuration', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # Create PfcBufferEgressConfiguration table
    op.create_table(
        'pfc_buffer_egress_configuration',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('port', sa.JSON(), nullable=False),
        sa.Column('queue', sa.JSON(), nullable=False),
        sa.Column('shared_ratio', sa.String(length=64), nullable=True),
        sa.Column('threshold', sa.String(length=64), nullable=True),
        sa.Column('config_data', sa.Text(), nullable=True),
        sa.Column('is_all_ports', sa.Integer(), nullable=False, default=False),
        sa.Column('is_all_queues', sa.Integer(), nullable=False, default=False),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )
    op.create_foreign_key('fk_pfc_buffer_egress_configuration_switch_sn', 'pfc_buffer_egress_configuration', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # Create PfcWdConfiguration table
    op.create_table(
        'pfc_wd_configuration',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('port', sa.JSON(), nullable=False),
        sa.Column('queue', sa.JSON(), nullable=False),
        sa.Column('enabled', sa.Boolean(), nullable=False),
        sa.Column('granularity', sa.String(length=64), nullable=True),
        sa.Column('detection_interval', sa.String(length=64), nullable=True),
        sa.Column('restore_interval', sa.String(length=64), nullable=True),
        sa.Column('threshold_period', sa.String(length=64), nullable=True),
        sa.Column('threshold_count', sa.String(length=64), nullable=True),
        sa.Column('restore_mode', sa.Enum('manual', 'auto'), nullable=True),
        sa.Column('restore_action', sa.Enum('drop', 'forward'), nullable=True),
        sa.Column('is_all_ports', sa.Integer(), nullable=False, default=False),
        sa.Column('is_all_queues', sa.Integer(), nullable=False, default=False),
        sa.Column('config_data', sa.Text(), nullable=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )
    op.create_foreign_key('fk_pfc_wd_configuration_switch_sn', 'pfc_wd_configuration', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # Create EcnConfiguration table
    op.create_table(
        'ecn_configuration',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('enabled', sa.Boolean(), default=True),
        sa.Column('mode', sa.Enum('latency-first', 'throughput-first')),
        sa.Column('config_data', sa.Text(), nullable=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )
    op.create_foreign_key('fk_ecn_configuration_switch_sn', 'ecn_configuration', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # Create EcnConfigurationDetail table
    op.create_table(
        'ecn_configuration_detail',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('ecn_config_id', sa.Integer(), nullable=False),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('port', sa.JSON(), nullable=False),
        sa.Column('queue', sa.JSON(), nullable=False),
        sa.Column('max_threshold', sa.Integer()),
        sa.Column('min_threshold', sa.Integer()),
        sa.Column('drop_probability', sa.Integer()),
        sa.Column('ecn_threshold', sa.Integer()),
        sa.Column('wred_enable', sa.Boolean()),
        sa.Column('is_all_ports', sa.Boolean(), nullable=False, default=False),
        sa.Column('is_all_queues', sa.Boolean(), nullable=False, default=False),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )
    op.create_foreign_key('fk_ecn_configuration_detail_switch_sn', 'ecn_configuration_detail', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # Create QosConfiguration table
    op.create_table(
        'qos_configuration',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('forwarding_class', sa.String(length=64), nullable=False),
        sa.Column('local_priority', sa.Integer(), nullable=False),
        sa.Column('scheduler', sa.String(length=64), nullable=False),
        sa.Column('mode', sa.Enum('SP', 'WRR', 'WFQ'), nullable=False, default='SP'),
        sa.Column('weight', sa.Integer(), nullable=True),
        sa.Column('guaranteed_rate', sa.Integer(), nullable=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True),
        sa.Column('config_data', sa.Text(), nullable=True)
    )
    op.create_foreign_key('fk_qos_configuration_switch_sn', 'qos_configuration', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # Create QosIngressConfiguration table
    op.create_table(
        'qos_ingress_configuration',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('classifier', sa.String(length=64), nullable=False),
        sa.Column('trust_mode', sa.Enum('dscp', 'ieee-802.1', 'inet-precedence'), nullable=False, default='dscp'),
        sa.Column('port', sa.JSON(), nullable=False),
        sa.Column('forwarding_class', sa.String(length=64), nullable=False),
        sa.Column('queue', sa.JSON(), nullable=False),
        sa.Column('is_all_ports', sa.Boolean(), nullable=False, default=False),
        sa.Column('is_all_queues', sa.Boolean(), nullable=False, default=False),
        sa.Column('config_data', sa.Text(), nullable=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )
    op.create_foreign_key('fk_qos_ingress_configuration_switch_sn', 'qos_ingress_configuration', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # Create QosEgressConfiguration table
    op.create_table(
        'qos_egress_configuration',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('scheduler_profile', sa.String(length=64), nullable=False),
        sa.Column('scheduler', sa.String(length=64), nullable=False),
        sa.Column('port', sa.JSON(), nullable=False),
        sa.Column('forwarding_class', sa.String(length=64), nullable=False),
        sa.Column('local_priority', sa.Integer(), nullable=False),
        sa.Column('is_all_ports', sa.Boolean(), nullable=False, default=False),
        sa.Column('is_all_queues', sa.Boolean(), nullable=False, default=False),
        sa.Column('config_data', sa.Text(), nullable=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )
    op.create_foreign_key('fk_qos_egress_configuration_switch_sn', 'qos_egress_configuration', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    op.create_table(
        'configuration_overview',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('sysname', sa.String(length=64), nullable=False),
        sa.Column('switch_sn', sa.String(length=64), nullable=False),
        sa.Column('basic_info', sa.JSON(), nullable=True),
        sa.Column('pcp_dscp_info', sa.JSON(), nullable=True),
        sa.Column('lp_info', sa.JSON(), nullable=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )
    op.create_foreign_key('fk_configuration_overview_switch_sn', 'configuration_overview', 'switch', ['switch_sn'], ['sn'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # overlay
    op.execute('''
            CREATE TABLE node_template (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                template_name VARCHAR(128),
                total_ports INT NOT NULL,
                template_info JSON,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL
            );
        ''')

    op.execute('''
            INSERT INTO `node_template` (`template_name`, `total_ports`, `template_info`) VALUES
                ('AI-Fabric-8x400G', 8, '{"AI-Fabric-8x400G_1": {"speed": 400, "port_num": 1}, "AI-Fabric-8x400G_2": {"speed": 400, "port_num": 1}, "AI-Fabric-8x400G_3": {"speed": 400, "port_num": 1}, "AI-Fabric-8x400G_4": {"speed": 400, "port_num": 1}, "AI-Fabric-8x400G_5": {"speed": 400, "port_num": 1}, "AI-Fabric-8x400G_6": {"speed": 400, "port_num": 1}, "AI-Fabric-8x400G_7": {"speed": 400, "port_num": 1}, "AI-Fabric-8x400G_8": {"speed": 400, "port_num": 1}}'),
                ('AI-Fabric-8x200G', 8, '{"AI-Fabric-8x200G_1": {"speed": 200, "port_num": 1}, "AI-Fabric-8x200G_2": {"speed": 200, "port_num": 1}, "AI-Fabric-8x200G_3": {"speed": 200, "port_num": 1}, "AI-Fabric-8x200G_4": {"speed": 200, "port_num": 1}, "AI-Fabric-8x200G_5": {"speed": 200, "port_num": 1}, "AI-Fabric-8x200G_6": {"speed": 200, "port_num": 1}, "AI-Fabric-8x200G_7": {"speed": 200, "port_num": 1}, "AI-Fabric-8x200G_8": {"speed": 200, "port_num": 1}}'),
                ('AI-Fabric-8x100G', 8, '{"AI-Fabric-8x100G_1": {"speed": 100, "port_num": 1}, "AI-Fabric-8x100G_2": {"speed": 100, "port_num": 1}, "AI-Fabric-8x100G_3": {"speed": 100, "port_num": 1}, "AI-Fabric-8x100G_4": {"speed": 100, "port_num": 1}, "AI-Fabric-8x100G_5": {"speed": 100, "port_num": 1}, "AI-Fabric-8x100G_6": {"speed": 100, "port_num": 1}, "AI-Fabric-8x100G_7": {"speed": 100, "port_num": 1}, "AI-Fabric-8x100G_8": {"speed": 100, "port_num": 1}}'),
                ('AI-Fabric-4x400G', 4, '{"AI-Fabric-4x400G_1": {"speed": 400, "port_num": 1}, "AI-Fabric-4x400G_2": {"speed": 400, "port_num": 1}, "AI-Fabric-4x400G_3": {"speed": 400, "port_num": 1}, "AI-Fabric-4x400G_4": {"speed": 400, "port_num": 1}}'),
                ('AI-Fabric-4x200G', 4, '{"AI-Fabric-4x200G_1": {"speed": 200, "port_num": 1}, "AI-Fabric-4x200G_2": {"speed": 200, "port_num": 1}, "AI-Fabric-4x200G_3": {"speed": 200, "port_num": 1}, "AI-Fabric-4x200G_4": {"speed": 200, "port_num": 1}}'),
                ('AI-Fabric-4x100G', 4, '{"AI-Fabric-4x100G_1": {"speed": 100, "port_num": 1}, "AI-Fabric-4x100G_2": {"speed": 100, "port_num": 1}, "AI-Fabric-4x100G_3": {"speed": 100, "port_num": 1}, "AI-Fabric-4x100G_4": {"speed": 100, "port_num": 1}}');           
        ''')

    op.execute('''
            CREATE TABLE node_group (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                az_id INT NOT NULL,
                fabric_id INT NOT NULL,
                node_template_id INT NOT NULL,
                nodegroup_name VARCHAR(128),
                description VARCHAR(128),
                status ENUM('Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed') NOT NULL DEFAULT 'Not Deployed',
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_node_group_template FOREIGN KEY (node_template_id) 
                    REFERENCES node_template(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_node_group_az FOREIGN KEY (az_id) 
                    REFERENCES virtual_resource_pool_az(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_node_group_fabric FOREIGN KEY (fabric_id) 
                    REFERENCES fabric(id) ON DELETE CASCADE ON UPDATE CASCADE
            );
        ''')

    op.execute('''
            CREATE TABLE node_host (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                host_name VARCHAR(512),
                node_group_id INT NOT NULL,
                ip_addr VARCHAR(64),
                username VARCHAR(128),
                password VARCHAR(128),
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_node_host_group FOREIGN KEY (node_group_id) 
                    REFERENCES node_group(id) ON DELETE CASCADE ON UPDATE CASCADE
            );
        ''')

    op.execute('''
            CREATE TABLE switch_portgroup (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                portgroup_name VARCHAR(512),
                node_group_id INT NOT NULL,
                vlan_domain_id BIGINT NOT NULL,
                connect_mode ENUM('access', 'trunk') NOT NULL,
                link_type ENUM('MLAG Leaf', 'Single Leaf') NOT NULL,
                link_count INT NOT NULL,
                status ENUM('Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed') NOT NULL DEFAULT 'Not Deployed',
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_switch_portgroup_group FOREIGN KEY (node_group_id) 
                    REFERENCES node_group(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_switch_portgroup_vlan_domain FOREIGN KEY (vlan_domain_id) 
                    REFERENCES resource_pool_vlan_domain(id) ON DELETE CASCADE ON UPDATE CASCADE
            );
        ''')

    op.execute('''
            CREATE TABLE node_nic_portgroup (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                portgroup_name VARCHAR(512),
                node_host_id INT NOT NULL,
                switch_portgroup_id INT NULL,
                network_id INT NULL,
                status ENUM('Connecting', 'Connect Successful', 'Connect Failed', 'Disconnecting', 'Disconnected', 'Disconnect Failed') NOT NULL DEFAULT 'Disconnected',
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_nic_portgroup_host FOREIGN KEY (node_host_id) 
                    REFERENCES node_host(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_nic_portgroup_switch FOREIGN KEY (switch_portgroup_id) 
                    REFERENCES switch_portgroup(id) ON DELETE SET NULL ON UPDATE CASCADE,
                CONSTRAINT fk_nic_portgroup_network FOREIGN KEY (network_id) 
                    REFERENCES virtual_resource_network(id) ON DELETE SET NULL ON UPDATE CASCADE
            );
        ''')

    op.execute('''
            CREATE TABLE switch_portgroup_info (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                portgroup_id INT NOT NULL,
                logic_device_id INT NOT NULL,
                switch_sn VARCHAR(128) NOT NULL,
                port_info JSON,
                status ENUM('Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed') NOT NULL DEFAULT 'Not Deployed',
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_switch_port_portgroup FOREIGN KEY (portgroup_id) 
                    REFERENCES switch_portgroup(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_switch_port_logic_device FOREIGN KEY (logic_device_id) 
                    REFERENCES dc_fabric_topology_node(id) ON DELETE CASCADE ON UPDATE CASCADE
            );
        ''')

    op.execute('''
            CREATE TABLE switch_portgroup_config (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                logic_device_id INT NOT NULL,
                switch_sn VARCHAR(128) NOT NULL,
                related_id INT NOT NULL,
                related_type ENUM('BareMetal', 'CloudPlatform') NOT NULL DEFAULT 'BareMetal',
                config JSON,
                status ENUM('Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed') NOT NULL DEFAULT 'Not Deployed',
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_switch_port_config_logic_device FOREIGN KEY (logic_device_id) 
                    REFERENCES dc_fabric_topology_node(id) ON DELETE CASCADE ON UPDATE CASCADE
            );
        ''')

    op.execute('''
            CREATE TABLE dc_logical_network (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(128),
                description VARCHAR(256),
                vpc_lock BOOLEAN,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL
            );                    
        ''')

    op.execute('''
            CREATE TABLE dc_logical_network_config_detail (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                logical_network_id INT,
                description VARCHAR(256),
                config_detail JSON,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_dc_network_config_detail_network FOREIGN KEY (logical_network_id) 
                    REFERENCES dc_logical_network(id) ON DELETE CASCADE ON UPDATE CASCADE
            );                    
        ''')

    op.execute('''
            CREATE TABLE dc_logical_router (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(128),
                description VARCHAR(256),
                fabric_id INT,
                logical_network_id INT,
                vrf_mode ENUM('auto', 'manual') NOT NULL,
                vrf_name VARCHAR(128),
                l3_anycast_mac_range VARCHAR(256),
                status INT,
                l3vni INT,
                vrf_vlan INT,
                type ENUM('Auto', 'Manual') NOT NULL DEFAULT 'Auto',
                position_x INT,
                position_y INT,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_dc_logical_router_fabric FOREIGN KEY (fabric_id) 
                    REFERENCES fabric(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_logical_router_logical_network FOREIGN KEY (logical_network_id) 
                    REFERENCES dc_logical_network(id) ON DELETE CASCADE ON UPDATE CASCADE
            );                    
        ''')

    op.execute('''
            CREATE TABLE dc_logical_switch (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(128),
                description VARCHAR(256),
                fabric_id INT,
                logical_network_id INT,
                arp_nd_suppress BOOLEAN,
                status INT,
                type ENUM('Auto', 'Manual') NOT NULL DEFAULT 'Auto',
                position_x INT,
                position_y INT,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_dc_logical_switch_fabric FOREIGN KEY (fabric_id) 
                    REFERENCES fabric(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_logical_switch_logical_network FOREIGN KEY (logical_network_id) 
                    REFERENCES dc_logical_network(id) ON DELETE CASCADE ON UPDATE CASCADE
            );                    
        ''')

    op.execute('''
            CREATE TABLE dc_virtual_network (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(128),
                fabric_id INT,
                az_id INT,
                logical_network_id INT,
                status INT,
                position_x INT,
                position_y INT,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_dc_virtual_network_fabric FOREIGN KEY (fabric_id) 
                    REFERENCES fabric(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_virtual_network_az FOREIGN KEY (az_id) 
                    REFERENCES virtual_resource_pool_az(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_virtual_network_logical_network FOREIGN KEY (logical_network_id) 
                    REFERENCES dc_logical_network(id) ON DELETE CASCADE ON UPDATE CASCADE
            );                    
        ''')

    op.execute('''
            CREATE TABLE dc_logical_interface (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                logical_network_id INT,
                logical_router_id INT,
                logical_switch_id INT,
                anycast_ipv4 VARCHAR(64),
                virtual_ipv4_range VARCHAR(256),
                anycast_mac VARCHAR(64),
                status INT,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_dc_logical_interface_network FOREIGN KEY (logical_network_id) 
                    REFERENCES dc_logical_network(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_logical_interface_router FOREIGN KEY (logical_router_id) 
                    REFERENCES dc_logical_router(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_logical_interface_switch FOREIGN KEY (logical_switch_id) 
                    REFERENCES dc_logical_switch(id) ON DELETE CASCADE ON UPDATE CASCADE
            );                  
        ''')

    op.execute('''
            CREATE TABLE dc_logical_port (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                az_id INT,
                logical_network_id INT,
                logical_switch_id INT,
                virtual_network_id INT,
                l2vni INT,
                status INT,
                vlan INT,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_dc_logical_port_network FOREIGN KEY (logical_network_id) 
                    REFERENCES dc_logical_network(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_logical_port_switch FOREIGN KEY (logical_switch_id) 
                    REFERENCES dc_logical_switch(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_logical_port_virtual_network FOREIGN KEY (virtual_network_id) 
                    REFERENCES dc_virtual_network(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_logical_port_az FOREIGN KEY (az_id) 
                    REFERENCES virtual_resource_pool_az(id) ON DELETE CASCADE ON UPDATE CASCADE
            );                    
        ''')

    op.execute('''
            CREATE TABLE dc_logical_router_config (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                logical_router_id INT,
                logic_device_id INT,
                l3_anycast_mac VARCHAR(64),
                status INT,
                config JSON,
                err_msg TEXT NULL DEFAULT NULL,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_dc_logical_router_config FOREIGN KEY (logical_router_id) 
                    REFERENCES dc_logical_router(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_logical_router_config_device FOREIGN KEY (logic_device_id) 
                    REFERENCES dc_fabric_topology_node(id) ON DELETE CASCADE ON UPDATE CASCADE
            );                  
        ''')

    op.execute('''
            CREATE TABLE dc_logical_interface_config (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                logical_interface_id INT,
                logic_device_id INT,
                virtual_ip VARCHAR(64),
                status INT,
                config JSON,
                err_msg TEXT NULL DEFAULT NULL,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_dc_logical_interface_config FOREIGN KEY (logical_interface_id) 
                    REFERENCES dc_logical_interface(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_logical_interface_config_device FOREIGN KEY (logic_device_id) 
                    REFERENCES dc_fabric_topology_node(id) ON DELETE CASCADE ON UPDATE CASCADE
            );                  
        ''')

    op.execute('''
            CREATE TABLE dc_logical_port_config (
                id INTEGER PRIMARY KEY AUTO_INCREMENT,
                logical_port_id INT,
                logic_device_id INT,
                status INT,
                config JSON,
                err_msg TEXT NULL DEFAULT NULL,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                CONSTRAINT fk_dc_logical_port_config FOREIGN KEY (logical_port_id) 
                    REFERENCES dc_logical_port(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_logical_port_config_device FOREIGN KEY (logic_device_id) 
                    REFERENCES dc_fabric_topology_node(id) ON DELETE CASCADE ON UPDATE CASCADE
            );                    
        ''')

    op.execute('''
            CREATE TABLE host_group_mapping (
                group_name VARCHAR(64) NOT NULL,
                device_name VARCHAR(64) NOT NULL,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                PRIMARY KEY (group_name, device_name),
                FOREIGN KEY (group_name) REFERENCES `group` (group_name) ON DELETE CASCADE,
                FOREIGN KEY (device_name) REFERENCES ansible_device (device_name) ON DELETE CASCADE
        );
        ''')

    op.execute('''ALTER TABLE virtual_resource_network ADD COLUMN vlan_id INT DEFAULT 0;''')
    op.execute('''    
            ALTER TABLE virtual_resource_network ADD COLUMN virtual_network_id INT NULL,
            ADD CONSTRAINT fk_virtual_resource_network_dc_virtual_network FOREIGN KEY (virtual_network_id) 
                REFERENCES dc_virtual_network(id) ON DELETE SET NULL ON UPDATE CASCADE;
        ''')
    op.execute('''ALTER TABLE `group` ADD COLUMN `group_type` ENUM('switch', 'host') NOT NULL DEFAULT 'switch';''')

    op.execute('''
            CREATE TABLE fabric_vni_mapping (
                id INT AUTO_INCREMENT PRIMARY KEY,
                fabric_id INT NOT NULL,
                vni_id BIGINT NOT NULL,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                UNIQUE KEY unique_fabric_vni (fabric_id, vni_id),
                FOREIGN KEY (fabric_id) REFERENCES fabric(id) ON DELETE CASCADE,
                FOREIGN KEY (vni_id) REFERENCES resource_pool_vni(id) ON DELETE CASCADE
        );
        ''')
    op.execute(
        '''ALTER TABLE `resource_pool_vni` ADD COLUMN `use` ENUM('default', 'reserve') NOT NULL DEFAULT 'default';''')

    op.execute('''
            CREATE TABLE vlan_domain_group (
                id INT AUTO_INCREMENT PRIMARY KEY,
                fabric_id INT NOT NULL,
                group_name VARCHAR(64) NOT NULL,
                description VARCHAR(128),
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                FOREIGN KEY (fabric_id) REFERENCES fabric(id) ON DELETE CASCADE
        );
        ''')

    op.execute('''
            ALTER TABLE resource_pool_bridge_domain_use_detail
            ADD COLUMN ls_id INT NOT NULL,
            ADD CONSTRAINT fk_resource_use_detail_ls 
                FOREIGN KEY (ls_id) 
                REFERENCES dc_logical_switch (id)
                ON DELETE CASCADE
                ON UPDATE CASCADE;
        ''')

    op.execute('''
            ALTER TABLE resource_pool_vrf_vlan_use_detail
            ADD COLUMN lr_id INT NOT NULL,
            ADD CONSTRAINT fk_resource_use_detail_lr 
                FOREIGN KEY (lr_id) 
                REFERENCES dc_logical_router (id)
                ON DELETE CASCADE
                ON UPDATE CASCADE;
        ''')

    op.execute('''ALTER TABLE virtual_resource_host ADD COLUMN connect_status BOOLEAN NOT NULL DEFAULT FALSE,
                        ADD COLUMN status ENUM(
                            'Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed') NOT NULL DEFAULT 'Not Deployed';''')
    op.execute('''ALTER TABLE virtual_resource_host_link ADD COLUMN status ENUM(
                        'Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed') NOT NULL DEFAULT 'Not Deployed';''')
    op.execute('''ALTER TABLE virtual_resource_host_linkport ADD COLUMN disable_port_list VARCHAR(512) NULL, 
                        ADD COLUMN lag_id INT NULL,
                        ADD COLUMN status ENUM(
                            'Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed') NOT NULL DEFAULT 'Not Deployed';''')

    op.execute('''ALTER TABLE virtual_resource_host_linkport 
                        DROP FOREIGN KEY fk_switch;
    ''')

    op.execute('''
            CREATE TABLE virtual_resource_hostlink_network_mapping (
                id INT AUTO_INCREMENT PRIMARY KEY,
                network_id INT NOT NULL,
                hostlink_id INT NOT NULL,
                status ENUM('Connecting', 'Connect Successful', 'Connect Failed', 'Disconnecting', 'Disconnected', 'Disconnect Failed') NOT NULL DEFAULT 'Disconnected',
                in_use BOOLEAN NOT NULL DEFAULT FALSE,
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                FOREIGN KEY (network_id) REFERENCES virtual_resource_network(id) ON DELETE CASCADE,
                FOREIGN KEY (hostlink_id) REFERENCES virtual_resource_host_link(id) ON DELETE CASCADE
            );
        ''')

    op.execute('''
            ALTER TABLE dc_fabric_topology_node DROP FOREIGN KEY fk_dc_fabric_topology_node_vd_pool_id;
        ''')
    op.execute('''
            ALTER TABLE dc_fabric_topology_node ADD CONSTRAINT fk_dc_fabric_topology_node_vd_pool_id FOREIGN KEY (vlan_domain_pool_id) 
                    REFERENCES resource_pool_vlan_domain (id) ON DELETE SET NULL ON UPDATE CASCADE;
        ''')

    op.execute('''
            ALTER TABLE config_distribution_task_for_dc ADD COLUMN type ENUM('underlay', 'overlay_router', 'overlay_switch', 'overlay_link', 
                                                                             'uplink_bm', 'uplink_cloud', 'roce') NOT NULL DEFAULT 'underlay';           
        ''')

    op.execute('''INSERT INTO association_site (switch_id, site_id)
                    SELECT s.id, d.id
                    FROM switch s
                    CROSS JOIN (
                        SELECT id FROM site WHERE site_name = 'default'
                    ) d
                    LEFT JOIN association_site a ON s.id = a.switch_id
                    WHERE a.site_id IS NULL
                    AND s.status IN (
                        'Provisioning Success',
                        'Imported',
                        'Staged',
                        'Registered Not-staged',
                        'Configured',
                        'Provisioning Failed',
                        'Registered',
                        'DECOM',
                        'DECOM-Manual',
                        'DECOM-Init',
                        'DECOM-Pending',
                        'RMA'
                    );
        ''')

    ###campus
    op.create_table('device_image_info',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('image_name', sa.String(255), nullable=True),
                    sa.Column('image_path', sa.String(255), nullable=True),
                    sa.Column('image_md5_path', sa.String(255), nullable=True),
                    sa.Column('model', sa.String(32), nullable=True),
                    sa.Column('version', sa.String(32), nullable=True),
                    sa.Column('revision', sa.String(32), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True)
                    )
    op.create_table('device_upgrade_operation_log',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('sn', sa.String(64)),
                    sa.Column('log_info', sa.String(128), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True)
                    )
    op.create_table('device_latest_upgrade_status',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('sn', sa.String(64)),
                    sa.Column('upgrade_type', sa.Integer(), default=0),
                    sa.Column('upgrade_job_name', sa.String(64), nullable=True),
                    sa.Column('image_id', sa.Integer()),
                    sa.Column('upgrade_time', sa.DateTime(), nullable=True),
                    sa.Column('upgrade_status', sa.Integer(), default=0),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True)
                    )
    op.create_index('idx_device_upgrade_operation_log_sn', 'device_upgrade_operation_log', ['sn'])
    op.create_index('idx_device_latest_upgrade_status_sn', 'device_latest_upgrade_status', ['sn'], unique=True)
    op.create_foreign_key('fk_device_latest_upgrade_status_image_id', 'device_latest_upgrade_status',
                          'device_image_info', ['image_id'], ['id'],
                          ondelete='SET NULL')

    op.execute('''
                    ALTER TABLE event ADD COLUMN resource_id VARCHAR(64) NULL;
                ''')

    op.execute('''
            CREATE TABLE snmp_metric_index (
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                id INT AUTO_INCREMENT PRIMARY KEY,
                index_name VARCHAR(128) NOT NULL,
                model VARCHAR(128) NOT NULL,
                base_oid VARCHAR(128) NOT NULL,
                description VARCHAR(256) NULL,
                is_internal INTEGER NOT NULL DEFAULT 1,
                UNIQUE KEY uq_model_indexname (model, index_name),
                INDEX idx_indexname_baseoid (index_name, base_oid)
            );
    ''')

    op.execute('''
            CREATE TABLE snmp_metric (
                create_time DATETIME NULL DEFAULT NULL,
                modified_time DATETIME NULL DEFAULT NULL,
                id INT AUTO_INCREMENT PRIMARY KEY,
                metric_name VARCHAR(128) NOT NULL,
                model VARCHAR(128) NOT NULL,
                base_oid VARCHAR(128) NOT NULL,
                method VARCHAR(32) NOT NULL,
                index_from_name VARCHAR(128) NULL,
                data_type enum('DIMENSION','METRIC') NOT NULL DEFAULT 'DIMENSION',
                unit VARCHAR(64) NULL,
                description VARCHAR(256) NULL,
                is_internal INTEGER NOT NULL DEFAULT 1,
                mapping_type VARCHAR(256) NULL,
                mapping_content LONGTEXT NULL,
                FOREIGN KEY (model, index_from_name) REFERENCES snmp_metric_index(model, index_name) ON DELETE CASCADE ON UPDATE CASCADE,
                UNIQUE KEY uq_metric_model (metric_name, model),
                INDEX idx_metric_model_oid (metric_name, base_oid, model)
            );
    ''')

    # ----------- snmp_import_detail ----------------
    op.create_table(
        'snmp_import_detail',
        sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True),
        sa.Column('mgt_ip', sa.String(32), nullable=False, comment='snmp_device mgt_ip 冗余字段'),
        sa.Column('status', sa.String(64), nullable=False, comment='success, failed, pending, running'),
    )
    op.create_index('idx_create_time', 'snmp_import_detail', ['create_time'])
    op.create_index('idx_status', 'snmp_import_detail', ['status'])

    # 无线相关表
    op.create_table(
        'wireless_rrm_task_log',
        sa.Column('id', sa.String(64), primary_key=True, nullable=False),
        sa.Column('create_time', sa.DateTime(), nullable=False),
        sa.Column('modified_time', sa.DateTime(), nullable=False),
        sa.Column('site_id', sa.Integer(), nullable=False),
        sa.Column('trigger_time', sa.DateTime()),
        sa.Column('is_schedule_task', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('online_num', sa.Integer(), nullable=True),
        sa.Column('success_num', sa.Integer(), nullable=True),
        sa.Column('failed_num', sa.Integer(), nullable=True),
    )
    op.create_unique_constraint('uq_wireless_rrm_task_log_id', 'wireless_rrm_task_log', ['id'])
    op.create_index('idx_wireless_rrm_task_log_site_id', 'wireless_rrm_task_log', ['site_id'])

    op.create_table(
        'wireless_rrm_task_result',
        sa.Column('task_id', sa.String(64), nullable=False),
        sa.Column('sn', sa.String(64), nullable=False),
        sa.Column('create_time', sa.DateTime(), nullable=False),
        sa.Column('modified_time', sa.DateTime(), nullable=False),
        sa.Column('result_type', sa.Integer(), nullable=False, server_default='1'),
    )
    op.create_index('idx_wireless_rrm_task_result_task_id', 'wireless_rrm_task_result', ['task_id'])


def downgrade():

    op.execute('''
        alter table client_device_info
            drop column port;
    ''')


    op.execute('''
        alter table client_device_info
            drop column terminal_type;
    ''')

    op.execute('''
        alter table client_device_info
            drop column ip_source;
    ''')
    # add table system_config_license_account_info
    op.drop_table('system_config_license_account_info')

    # 先删除 snmp_import_detail 再删除snmp_device
    op.drop_index('idx_status', 'snmp_import_detail')
    op.drop_index('idx_create_time', 'snmp_import_detail')
    op.drop_table('snmp_import_detail')

    op.drop_table('snmp_device')

    op.execute('''drop table if exists switch_common_job_log;''')
    op.execute('''drop table if exists switch_common_job;''')

    # 1. 首先删除外键约束和索引
    op.execute('ALTER TABLE device_latest_upgrade_status DROP FOREIGN KEY fk_device_latest_upgrade_status_image_id;')
    op.execute('DROP INDEX IF EXISTS idx_device_latest_upgrade_status_sn ON device_latest_upgrade_status;')
    op.execute('DROP INDEX IF EXISTS idx_device_upgrade_operation_log_sn ON device_upgrade_operation_log;')
    op.execute('DROP INDEX IF EXISTS idx_ddm_events_resolved ON ddm_events;')
    op.execute('DROP INDEX IF EXISTS idx_ddm_events_alert_type ON ddm_events;')
    op.execute('DROP INDEX IF EXISTS idx_ddm_events_channel ON ddm_events;')
    op.execute('DROP INDEX IF EXISTS idx_ddm_events_interface ON ddm_events;')
    op.execute('DROP INDEX IF EXISTS idx_ddm_events_switch_sn ON ddm_events;')

    # 2. 删除配置任务相关列
    op.execute('ALTER TABLE config_distribution_task_for_dc DROP COLUMN type;')

    # 3. 修改外键约束（先删除再重新添加）
    op.execute('ALTER TABLE dc_fabric_topology_node DROP FOREIGN KEY fk_dc_fabric_topology_node_vd_pool_id;')
    op.execute('''
        ALTER TABLE dc_fabric_topology_node 
        ADD CONSTRAINT fk_dc_fabric_topology_node_vd_pool_id 
        FOREIGN KEY (vlan_domain_pool_id) 
        REFERENCES resource_pool_vlan_domain (id) 
        ON DELETE CASCADE 
        ON UPDATE CASCADE;
    ''')

    # 4. 删除资源池相关外键和列（按依赖顺序）
    op.execute('ALTER TABLE resource_pool_vrf_vlan_use_detail DROP FOREIGN KEY fk_resource_use_detail_lr;')
    op.execute('ALTER TABLE resource_pool_vrf_vlan_use_detail DROP COLUMN lr_id;')
    op.execute('ALTER TABLE resource_pool_bridge_domain_use_detail DROP FOREIGN KEY fk_resource_use_detail_ls;')
    op.execute('ALTER TABLE resource_pool_bridge_domain_use_detail DROP COLUMN ls_id;')

    # 5. 删除虚拟资源相关表和列
    op.execute('DROP TABLE IF EXISTS virtual_resource_hostlink_network_mapping;')
    op.execute(
        'ALTER TABLE virtual_resource_host_linkport DROP COLUMN disable_port_list, DROP COLUMN lag_id, DROP COLUMN status;')
    op.execute('''
        ALTER TABLE virtual_resource_host_linkport 
        ADD CONSTRAINT fk_switch 
        FOREIGN KEY (switch_sn) 
        REFERENCES switch(sn) ON DELETE CASCADE ON UPDATE CASCADE;
    ''')

    op.execute('ALTER TABLE virtual_resource_host_link DROP COLUMN status;')
    op.execute('ALTER TABLE virtual_resource_host DROP COLUMN connect_status, DROP COLUMN status;')

    # 6. 删除网络相关表和列
    op.execute('ALTER TABLE virtual_resource_network DROP FOREIGN KEY fk_virtual_resource_network_dc_virtual_network;')
    op.execute('ALTER TABLE virtual_resource_network DROP COLUMN virtual_network_id, DROP COLUMN vlan_id;')

    # 7. 删除组相关表和列
    op.execute('DROP TABLE IF EXISTS host_group_mapping;')
    op.execute('ALTER TABLE `group` DROP COLUMN `group_type`;')

    # 8. 删除资源池相关表
    op.execute('DROP TABLE IF EXISTS vlan_domain_group;')
    op.execute('ALTER TABLE resource_pool_vni DROP COLUMN `use`;')
    op.execute('DROP TABLE IF EXISTS fabric_vni_mapping;')

    # 9. 删除事件表列
    op.execute('ALTER TABLE event DROP COLUMN resource_id;')

    # 10. 删除 overlay 相关表（按依赖顺序从最底层开始）
    op.execute('DROP TABLE IF EXISTS dc_logical_port_config;')
    op.execute('DROP TABLE IF EXISTS dc_logical_interface_config;')
    op.execute('DROP TABLE IF EXISTS dc_logical_router_config;')
    op.execute('DROP TABLE IF EXISTS dc_logical_port;')
    op.execute('DROP TABLE IF EXISTS dc_logical_interface;')
    op.execute('DROP TABLE IF EXISTS dc_virtual_network;')
    op.execute('DROP TABLE IF EXISTS dc_logical_switch;')
    op.execute('DROP TABLE IF EXISTS dc_logical_router;')
    op.execute('DROP TABLE IF EXISTS dc_logical_network_config_detail;')
    op.execute('DROP TABLE IF EXISTS dc_logical_network;')

    # 11. 删除节点和端口组相关表
    op.execute('DROP TABLE IF EXISTS switch_portgroup_config;')
    op.execute('DROP TABLE IF EXISTS switch_portgroup_info;')
    op.execute('DROP TABLE IF EXISTS node_nic_portgroup;')
    op.execute('DROP TABLE IF EXISTS switch_portgroup;')
    op.execute('DROP TABLE IF EXISTS node_host;')
    op.execute('DROP TABLE IF EXISTS node_group;')
    op.execute('DROP TABLE IF EXISTS node_template;')

    # 12. 删除配置相关表
    op.execute('DROP TABLE IF EXISTS configuration_overview;')
    op.execute('DROP TABLE IF EXISTS qos_egress_configuration;')
    op.execute('DROP TABLE IF EXISTS qos_ingress_configuration;')
    op.execute('DROP TABLE IF EXISTS qos_configuration;')
    op.execute('DROP TABLE IF EXISTS ecn_configuration_detail;')
    op.execute('DROP TABLE IF EXISTS ecn_configuration;')
    op.execute('DROP TABLE IF EXISTS pfc_wd_configuration;')
    op.execute('DROP TABLE IF EXISTS pfc_buffer_egress_configuration;')
    op.execute('DROP TABLE IF EXISTS pfc_buffer_ingress_configuration;')
    op.execute('DROP TABLE IF EXISTS pfc_configuration;')
    op.execute('DROP TABLE IF EXISTS dlb_configuration;')
    op.execute('DROP TABLE IF EXISTS roce_easy_deploy_configuration;')

    # 13. 删除模块链接表
    op.execute('DROP TABLE IF EXISTS modules_links;')

    # 14. 删除DDM事件表
    op.execute('DROP TABLE IF EXISTS ddm_events;')

    # 15. 删除设备升级相关表
    op.execute('DROP TABLE IF EXISTS device_latest_upgrade_status;')
    op.execute('DROP TABLE IF EXISTS device_upgrade_operation_log;')
    op.execute('DROP TABLE IF EXISTS device_image_info;')

    # 16. 最后删除枚举类型
    op.execute("DROP TYPE IF EXISTS alert_level_enum;")
    op.execute("DROP TYPE IF EXISTS alert_type_enum;")

    op.execute("DROP TABLE IF EXISTS snmp_metric;")
    op.execute("DROP TABLE IF EXISTS snmp_metric_index;")



    op.execute('DROP TABLE IF EXISTS wireless_rrm_task_result;')
    op.execute('DROP TABLE IF EXISTS wireless_rrm_task_log;')

