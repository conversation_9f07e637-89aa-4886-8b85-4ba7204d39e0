#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: automation
@file: check_generate_pwd_util.py
@function:
@time: 2023/2/1 14:12
"""

import re
import sys
import random


class CheckGeneratePasswd(object):
    def __init__(self, passwd_len=10, min_num=2, min_letter=2, min_LETTER=2, min_char=2):
        self.pwd_regex = re.compile(r"""(
            ^(?=.*[A-Z])
            (?=.*[^=+])
            (?=.*[0-9])
            (?=.*[a-z])
            .{1,}
            $
            )""", re.VERBOSE)
        self.min_num = min_num
        self.min_letter = min_letter
        self.min_LETTER = min_LETTER
        self.min_char = min_char
        self.passwd_len = passwd_len
        self.num_list = [chr(i) for i in range(48, 58)]
        self.let_list = [chr(i) for i in range(97, 123)]
        self.LET_list = [chr(i) for i in range(65, 91)]
        self.char_list = [i for i in '^=+']
        self.passwd = ''
    
    def get_pass_set(self):
        if self.min_num + self.min_char + self.min_LETTER + self.min_letter > self.passwd_len:
            print(
                "ERROR: If the minimum number of occurrences of each type is greater than the password length, set it again")
            sys.exit(1)
        pass_set_list = []
        if self.min_num == 0 and self.min_char == 0 and self.min_LETTER == 0 and self.min_letter == 0:
            pass_set_list = self.let_list + self.char_list + self.num_list + self.LET_list
        else:
            if self.min_num > 0:
                pass_set_list += self.num_list
            if self.min_letter > 0:
                pass_set_list += self.let_list
            if self.min_LETTER > 0:
                pass_set_list += self.LET_list
            if self.min_char > 0:
                pass_set_list += self.char_list
        return pass_set_list
    
    def update_min_num(self, char):
        char = str(char)
        if char in self.LET_list and self.min_LETTER > 0:
            self.min_LETTER -= 1
        elif char in self.let_list and self.min_letter > 0:
            self.min_letter -= 1
        elif char in self.num_list and self.min_num > 0:
            self.min_num -= 1
        elif char in self.char_list and self.min_char > 0:
            self.min_char -= 1
    
    def check_char(self, char):
        char = str(char)
        if len(self.passwd) <= 1:
            return True
        else:
            if ord(char) == ord(self.passwd[-1]) and ord(char) == ord(self.passwd[-2]):
                return False
            elif abs(ord(char) - ord(self.passwd[-1])) == 1 and abs(ord(char) - ord(self.passwd[-2])) == 2:
                return False
            else:
                return True
    
    def get_random_passwd(self):
        while self.passwd_len > 0:
            pass_set_list = self.get_pass_set()
            # print(self.passwd)
            # print(pass_set_list)
            char = random.choice(pass_set_list)
            if self.check_char(char):
                self.passwd += char
                self.update_min_num(char)
                self.passwd_len -= 1
        return self.passwd
    
    def check_random_passwd(self, pwd):
        if not self.pwd_regex.search(pwd):
            print("The password should contain a mix of uppercase, lowercase, numbers and at least one special symbol.")
            return False
        if len(pwd) < 10:
            print("The password should be at least 10 characters long.")
            return False
        return True


if __name__ == "__main__":
    cgp = CheckGeneratePasswd()
    password = cgp.get_random_passwd()
    print(password)
    print(cgp.check_random_passwd(password))
