#!/bin/sh
vpn_ip="********"
config_file_path="/opt/auto-deploy/auto-deploy.conf"
# get vpn_ip from /opt/auto-deploy/auto-deploy.conf
server_vpn_output=`grep -o -P '(?<=server_vpn_ip =).*\b' ${config_file_path} | sed 's/[ =]//g'`
if [ ! "$server_vpn_output" = "" ]; then
   vpn_ip=$server_vpn_output
fi

ovpn_service="/lib/systemd/system/openvpn@.service"
vrf_name=`grep -o -P '(?<=exec ).*(?= /usr)' $ovpn_service`

if [ -n "$vrf_name" ]; then
    vrf_id=`/sbin/ip vrf show $vrf_name 2>/dev/null | cut -f2 -d' '`
    sudo /sbin/ip link set tun0 master $vrf_name
    sudo /sbin/ip route add table $vrf_id $vpn_ip dev tun0
else
    sudo /sbin/ip route add table 252 $vpn_ip dev tun0
fi

if [ $(sudo iptables -nvL | grep -c tun0) -eq 0 ]; then
    sudo iptables -I INPUT 4 -i tun0 -p tcp -m multiport --dport 22,23,8080 -j ACCEPT
fi

./update_vpn_ip.sh $vrf_name &

exit 0
