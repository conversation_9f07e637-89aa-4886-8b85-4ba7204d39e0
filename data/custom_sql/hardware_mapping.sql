/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 100321
 Source Host           : ************:3306
 Source Schema         : automation

 Target Server Type    : MySQL
 Target Server Version : 100321
 File Encoding         : 65001

 Date: 05/03/2024 11:35:19
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for hardware_mapping
-- ----------------------------
DROP TABLE IF EXISTS `hardware_mapping`;
CREATE TABLE `hardware_mapping`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `hardware_model` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `switch_model` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMAR<PERSON> KEY (`id`) USING BTREE,
  UNIQUE INDEX `hardware_model`(`hardware_model` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 86 CHARACTER SET = utf8 COLLATE = utf8_general_ci;

-- ----------------------------
-- Records of hardware_mapping
-- ----------------------------
BEGIN;
INSERT INTO `hardware_mapping` (`create_time`, `modified_time`, `id`, `hardware_model`, `switch_model`) VALUES ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 1, '6921 48XGT', 'HPE AL 6921-54T'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 3, '6921 48SFP', 'HPE AL 6921-54X'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 5, '5712-54X', 'as5712_54x'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 7, '4610-30P', 'as4610_30p'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 9, '4610-30T', 'as4610_30t'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 11, '4610-54T-O-AC-B', 'as4610_54t_b'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 13, '4610-54T', 'as4610_54t'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 15, '4610-54P', 'as4610_54p'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 17, '5812-54X', 'as5812_54x'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 19, '5812-54T', 'as5812_54t'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 21, '5835-54X', 'as5835_54x'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 23, '5835-54T', 'as5835_54t'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 25, 'n3132px_armv7a', 'N3132PX-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 27, 'n3k2k_armv7a', 'N3048ET-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 29, 'n3248pxe', 'N3248PXE-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 31, 'N3248PXE-ON', 'N3248PXE-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 33, 's4000', 'S4048-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 35, 'S4048ON', 'S4048-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 37, 'N3024ET-ON', 'N3024ET-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 39, 'S5224F-ON', 'S5224F-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 41, 'S5296F-ON', 'S5296F-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 43, 'S4128T-ON', 'S4128T-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 45, 'S4128F-ON', 'S4128F-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 47, 'N3024EP-ON', 'N3024EP-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 49, 'N3048ET-ON', 'N3048ET-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 51, 'N3048EP-ON', 'N3048EP-ON'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 53, '7326-56X', 'as7326_56x'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 55, '7312-54XS', 'as7312_54x'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 57, '4630-54TE', 'AS4630-54TE'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 59, '4630-54PE', 'AS4630-54PE'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 61, '4630-54NPE', 'AS4630-54NPE'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 63, '6812-32X', 'as6812_32x'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 65, '7712-32X', 'as7712_32x'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 67, '7726-32X', 'as7726_32x'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 69, '7816-64X', 'as7816_64x'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 71, 'AG5648', 'ag5648'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 73, 'AG7648', 'ag7648'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 75, 'AG9032', 'ag9032'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 77, '4625-54P', 'AS4625-54P'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 79, '4625-54T', 'AS4625-54T'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 81, 'N8550-48B8C', 'N8550-48B8C'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 83, 'N5850-48S6Q', 'N5850-48S6Q'), ('2023-01-09 08:08:08', '2023-01-09 08:08:08', 85, 'N8550-32C', 'N8550-32C');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
