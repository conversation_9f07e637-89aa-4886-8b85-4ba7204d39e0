INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'CPU Used Percentage',
            'S5800-48T4S',
            '*******.4.1.52642.*******.0',
            'get',
            NULL,
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Used Percentage',
            'S5800-48T4S',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Usage Size',
            'S5800-48T4S',
            '*******.4.1.52642.********.0',
            'get',
            NULL,
            'metric',
            'KB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Size',
            'S5800-48T4S',
            '*******.4.1.52642.*******.0',
            'get',
            NULL,
            'metric',
            'KB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Name',
            'S5800-48T4S',
            '*******.*******.1.2',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets Discarded',
            'S5800-48T4S',
            '*******.*******.1.19',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets with Errors',
            'S5800-48T4S',
            '*******.*******.1.14',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets Discarded',
            'S5800-48T4S',
            '*******.*******.1.13',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets with Errors',
            'S5800-48T4S',
            '*******.*******.1.20',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Sent',
            'S5800-48T4S',
            '*******.********.1.1.10',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Received',
            'S5800-48T4S',
            '*******.********.1.1.6',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Speed',
            'S5800-48T4S',
            '*******.*******.1.5',
            'get',
            'ifIndex',
            'metric',
            'bps',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Status',
            'S5800-48T4S',
            '*******.*******.1.8',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"Up\",\"2\":\"Down\",\"3\":\"Testing\",\"4\":\"Unknown\",\"5\":\"Dormant\",\"6\":\"NotPresent\",\"7\":\"LowerLayerDown\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface MTU',
            'S5800-48T4S',
            '*******.*******.1.4',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Physical Address',
            'S5800-48T4S',
            '*******.*******.1.6',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Temperature',
            'S5800-48T4S',
            '*******.4.1.52642.********.*******.1',
            'get',
            NULL,
            'metric',
            'Celsius',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'MAC Address',
            'S5800-48T4S',
            '*******.********.1.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Usage Size',
            'S5800-48T4S',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Free Size',
            'S5800-48T4S',
            '*******.4.1.52642.*******.*******.1',
            'get',
            NULL,
            'metric',
            'GB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Size',
            'S5800-48T4S',
            '*******.4.1.52642.*******.*******.1',
            'get',
            NULL,
            'metric',
            'GB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'PoE Power',
            'S5800-48T4S',
            '',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Rx Power',
            'S5800-48T4S',
            '*******.4.1.52642.********0.6.1.5',
            'get',
            'ifIndex',
            'metric',
            'dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Tx Power',
            'S5800-48T4S',
            '*******.4.1.52642.********0.5.1.5',
            'get',
            'ifIndex',
            'metric',
            'dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Current',
            'S5800-48T4S',
            '*******.4.1.52642.********0.4.1.5',
            'get',
            'ifIndex',
            'metric',
            'mA',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Voltage',
            'S5800-48T4S',
            '*******.4.1.52642.********0.3.1.5',
            'get',
            'ifIndex',
            'metric',
            'Voltage',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Optical Module Temperature',
            'S5800-48T4S',
            '*******.4.1.52642.********0.2.1.5',
            'get',
            'ifIndex',
            'metric',
            'Celsius',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Name',
            'S5800-48T4S',
            '',
            'get',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Speed',
            'S5800-48T4S',
            '*******.4.1.52642.********.*******.1',
            'get',
            'fanIndex',
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Status',
            'S5800-48T4S',
            '*******.4.1.52642.********.*******.1',
            'get',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            'expression',
            'lambda x: \"Active\" if x == \"1\" else \"Deactive\" if x == \"2\" else \"Not Installed\" if x == \"3\" else \"Unsupported\" if x == \"4\" else \"Unknown\"'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Name',
            'S5800-48T4S',
            '',
            'get',
            'supplyIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Status',
            'S5800-48T4S',
            '*******.4.1.52642.********.1.2',
            'get',
            'supplyIndex',
            'dimension',
            '',
            '',
            1,
            'expression',
            'lambda x: \"Present\" if x == \"1\" else \"Absent\" if x == \"2\" else \"Unsupport\" if x == \"3\" else \"Unknown\"'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Location',
            'S5800-48T4S',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Sysname',
            'S5800-48T4S',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'System Uptime',
            'S5800-48T4S',
            '*******.*******.0',
            'get',
            NULL,
            'metric',
            '0.01s',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device SW Version',
            'S5800-48T4S',
            '*******.********.********.1',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device HW Version',
            'S5800-48T4S',
            '*******.4.1.52642.********.*******',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Type',
            'S5800-48T4S',
            '*******.*******.1.3',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"other\",\"2\":\"regular1822\",\"3\":\"hdh1822\",\"4\":\"ddn-x25\",\"5\":\"rfc877-x25\",\"6\":\"ethernet-csmacd\",\"7\":\"iso88023-csmacd\",\"8\":\"iso88024-tokenBus\",\"9\":\"iso88025-tokenRing\",\"10\":\"iso88026-man\",\"11\":\"starLan\",\"12\":\"proteon-10Mbit\",\"13\":\"proteon-80Mbit\",\"14\":\"hyperchannel\",\"15\":\"fddi\",\"16\":\"lapb\",\"17\":\"sdlc\",\"18\":\"ds1\",\"19\":\"e1\",\"20\":\"basicISDN\",\"21\":\"primaryISDN\",\"22\":\"propPointToPointSerial\",\"23\":\"ppp\",\"24\":\"softwareLoopback\",\"25\":\"eon\",\"26\":\"ethernet-3Mbit\",\"27\":\"nsip\",\"28\":\"slip\",\"29\":\"ultra\",\"30\":\"ds3\",\"31\":\"sip\",\"32\":\"frame-relay\",\"53\":\"propVirtual\",\"117\":\"gigabitEthernet\",\"136\":\"l2vlan\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Power Status',
            'S5800-48T4S',
            '',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'CPU Used Percentage',
            'S5850C-24XMG2C',
            '*******.4.1.52642.*******.0',
            'get',
            NULL,
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Used Percentage',
            'S5850C-24XMG2C',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Usage Size',
            'S5850C-24XMG2C',
            '*******.4.1.52642.********.0',
            'get',
            NULL,
            'metric',
            'KB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Size',
            'S5850C-24XMG2C',
            '*******.4.1.52642.*******.0',
            'get',
            NULL,
            'metric',
            'KB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Name',
            'S5850C-24XMG2C',
            '*******.*******.1.2',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets Discarded',
            'S5850C-24XMG2C',
            '*******.*******.1.19',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets with Errors',
            'S5850C-24XMG2C',
            '*******.*******.1.14',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets Discarded',
            'S5850C-24XMG2C',
            '*******.*******.1.13',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets with Errors',
            'S5850C-24XMG2C',
            '*******.*******.1.20',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Sent',
            'S5850C-24XMG2C',
            '*******.********.1.1.10',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Received',
            'S5850C-24XMG2C',
            '*******.********.1.1.6',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Speed',
            'S5850C-24XMG2C',
            '*******.*******.1.5',
            'get',
            'ifIndex',
            'metric',
            'bps',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Status',
            'S5850C-24XMG2C',
            '*******.*******.1.8',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"Up\",\"2\":\"Down\",\"3\":\"Testing\",\"4\":\"Unknown\",\"5\":\"Dormant\",\"6\":\"NotPresent\",\"7\":\"LowerLayerDown\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface MTU',
            'S5850C-24XMG2C',
            '*******.*******.1.4',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Physical Address',
            'S5850C-24XMG2C',
            '*******.*******.1.6',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Temperature',
            'S5850C-24XMG2C',
            '*******.4.1.52642.********.*******.1',
            'get',
            NULL,
            'metric',
            'Celsius',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'MAC Address',
            'S5850C-24XMG2C',
            '*******.********.1.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Usage Size',
            'S5850C-24XMG2C',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Free Size',
            'S5850C-24XMG2C',
            '*******.4.1.52642.*******.*******.1',
            'get',
            NULL,
            'metric',
            'GB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Size',
            'S5850C-24XMG2C',
            '*******.4.1.52642.*******.*******.1',
            'get',
            NULL,
            'metric',
            'GB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'PoE Power',
            'S5850C-24XMG2C',
            '',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Rx Power',
            'S5850C-24XMG2C',
            '*******.4.1.52642.********0.6.1.5',
            'get',
            'ifIndex',
            'metric',
            'dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Tx Power',
            'S5850C-24XMG2C',
            '*******.4.1.52642.********0.5.1.5',
            'get',
            'ifIndex',
            'metric',
            'dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Current',
            'S5850C-24XMG2C',
            '*******.4.1.52642.********0.4.1.5',
            'get',
            'ifIndex',
            'metric',
            'mA',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Voltage',
            'S5850C-24XMG2C',
            '*******.4.1.52642.********0.3.1.5',
            'get',
            'ifIndex',
            'metric',
            'Voltage',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Optical Module Temperature',
            'S5850C-24XMG2C',
            '*******.4.1.52642.********0.2.1.5',
            'get',
            'ifIndex',
            'metric',
            'Celsius',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Name',
            'S5850C-24XMG2C',
            '',
            'get',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Speed',
            'S5850C-24XMG2C',
            '*******.4.1.52642.********.*******.1',
            'get',
            'fanIndex',
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Status',
            'S5850C-24XMG2C',
            '*******.4.1.52642.********.*******.1',
            'get',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            'expression',
            'lambda x: \"Active\" if x == \"1\" else \"Deactive\" if x == \"2\" else \"Not Installed\" if x == \"3\" else \"Unsupported\" if x == \"4\" else \"Unknown\"'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Name',
            'S5850C-24XMG2C',
            '',
            'get',
            'supplyIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Status',
            'S5850C-24XMG2C',
            '*******.4.1.52642.********.1.2',
            'get',
            'supplyIndex',
            'dimension',
            '',
            '',
            1,
            'expression',
            'lambda x: \"Present\" if x == \"1\" else \"Absent\" if x == \"2\" else \"Unsupport\" if x == \"3\" else \"Unknown\"'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Location',
            'S5850C-24XMG2C',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Sysname',
            'S5850C-24XMG2C',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'System Uptime',
            'S5850C-24XMG2C',
            '*******.*******.0',
            'get',
            NULL,
            'metric',
            '0.01s',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device SW Version',
            'S5850C-24XMG2C',
            '*******.********.********.1',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device HW Version',
            'S5850C-24XMG2C',
            '*******.4.1.52642.********.*******',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Type',
            'S5850C-24XMG2C',
            '*******.*******.1.3',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"other\",\"2\":\"regular1822\",\"3\":\"hdh1822\",\"4\":\"ddn-x25\",\"5\":\"rfc877-x25\",\"6\":\"ethernet-csmacd\",\"7\":\"iso88023-csmacd\",\"8\":\"iso88024-tokenBus\",\"9\":\"iso88025-tokenRing\",\"10\":\"iso88026-man\",\"11\":\"starLan\",\"12\":\"proteon-10Mbit\",\"13\":\"proteon-80Mbit\",\"14\":\"hyperchannel\",\"15\":\"fddi\",\"16\":\"lapb\",\"17\":\"sdlc\",\"18\":\"ds1\",\"19\":\"e1\",\"20\":\"basicISDN\",\"21\":\"primaryISDN\",\"22\":\"propPointToPointSerial\",\"23\":\"ppp\",\"24\":\"softwareLoopback\",\"25\":\"eon\",\"26\":\"ethernet-3Mbit\",\"27\":\"nsip\",\"28\":\"slip\",\"29\":\"ultra\",\"30\":\"ds3\",\"31\":\"sip\",\"32\":\"frame-relay\",\"53\":\"propVirtual\",\"117\":\"gigabitEthernet\",\"136\":\"l2vlan\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Power Status',
            'S5850C-24XMG2C',
            '',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'CPU Used Percentage',
            'S3410-48TS-P',
            '*******.4.1.52642.********.36.*******',
            'get',
            NULL,
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Used Percentage',
            'S3410-48TS-P',
            '*******.4.1.52642.********.35.*******.1',
            'get',
            NULL,
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Usage Size',
            'S3410-48TS-P',
            '*******.4.1.52642.********.********.13.1',
            'get',
            NULL,
            'metric',
            'MB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Size',
            'S3410-48TS-P',
            '*******.4.1.52642.********.35.********.1',
            'get',
            NULL,
            'metric',
            'MB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Name',
            'S3410-48TS-P',
            '*******.*******.1.2',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets Discarded',
            'S3410-48TS-P',
            '*******.*******.1.19',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets with Errors',
            'S3410-48TS-P',
            '*******.*******.1.14',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets Discarded',
            'S3410-48TS-P',
            '*******.*******.1.13',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets with Errors',
            'S3410-48TS-P',
            '*******.*******.1.20',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Sent',
            'S3410-48TS-P',
            '*******.********.1.1.10',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Received',
            'S3410-48TS-P',
            '*******.********.1.1.6',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Speed',
            'S3410-48TS-P',
            '*******.*******.1.5',
            'get',
            'ifIndex',
            'metric',
            'bps',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Status',
            'S3410-48TS-P',
            '*******.*******.1.8',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"Up\",\"2\":\"Down\",\"3\":\"Testing\",\"4\":\"Unknown\",\"5\":\"Dormant\",\"6\":\"NotPresent\",\"7\":\"LowerLayerDown\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface MTU',
            'S3410-48TS-P',
            '*******.*******.1.4',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Physical Address',
            'S3410-48TS-P',
            '*******.*******.1.6',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Temperature',
            'S3410-48TS-P',
            '*******.4.1.52642.********.********',
            'get',
            NULL,
            'metric',
            'Celsius',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'MAC Address',
            'S3410-48TS-P',
            '*******.4.1.52642.********.10.*******1.2',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Usage Size',
            'S3410-48TS-P',
            '*******.4.1.52642.********.********.4',
            'walk',
            NULL,
            'metric',
            'MB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Free Size',
            'S3410-48TS-P',
            '*******.4.1.52642.********.47.*******',
            'walk',
            NULL,
            'metric',
            'MB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Size',
            'S3410-48TS-P',
            '*******.4.1.52642.********.47.*******',
            'walk',
            NULL,
            'metric',
            'MB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'PoE Power',
            'S3410-48TS-P',
            '1.0.8802.*******.5.4795.********.1',
            'get',
            'ifIndex',
            'metric',
            'W',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Rx Power',
            'S3410-48TS-P',
            '*******.4.1.52642.********.105.*******1',
            'get',
            'ifIndex',
            'metric',
            'dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Tx Power',
            'S3410-48TS-P',
            '*******.4.1.52642.********.105.*******1',
            'get',
            'ifIndex',
            'metric',
            'dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Current',
            'S3410-48TS-P',
            '*******.4.1.52642.********.105.*******1',
            'get',
            'ifIndex',
            'metric',
            'uA',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Voltage',
            'S3410-48TS-P',
            '*******.4.1.52642.********.*********.19',
            'get',
            'ifIndex',
            'metric',
            'mVolts',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Optical Module Temperature',
            'S3410-48TS-P',
            '*******.4.1.52642.********.*********.17',
            'get',
            'ifIndex',
            'metric',
            'Celsius',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Name',
            'S3410-48TS-P',
            '*******.4.1.52642.********.********.3',
            'get',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Speed',
            'S3410-48TS-P',
            '*******.4.1.52642.********.********.6.1',
            'walk',
            'fanIndex',
            'metric',
            'rpm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Status',
            'S3410-48TS-P',
            '*******.4.1.52642.********.********.4.1',
            'walk',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            'expression',
            'lambda x: \"NoExist\" if x == \"1\" else \"ExistNoPower\" if x == \"2\" else \"ExistReadyPower\" if x == \"3\" else \"Normal\" if x == \"4\" else \"PowerButAbnormal\" if x == \"5\" else \"Unknow\"'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Name',
            'S3410-48TS-P',
            '*******.4.1.52642.********.********.3',
            'get',
            'supplyIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Status',
            'S3410-48TS-P',
            '*******.4.1.52642.********.********.3.1',
            'get',
            'supplyIndex',
            'dimension',
            '',
            '',
            1,
            'expression',
            'lambda x: \"NoExist\" if x == \"1\" else \"ExistNoPower\" if x == \"2\" else \"ExistReadyPower\" if x == \"3\" else \"Normal\" if x == \"4\" else \"PowerButAbnormal\" if x == \"5\" else \"Unknow\"'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Location',
            'S3410-48TS-P',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Sysname',
            'S3410-48TS-P',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'System Uptime',
            'S3410-48TS-P',
            '*******.*******.0',
            'get',
            NULL,
            'metric',
            '0.01s',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device SW Version',
            'S3410-48TS-P',
            '*******.4.1.52642.********.*******',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device HW Version',
            'S3410-48TS-P',
            '*******.4.1.52642.********.*******',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Type',
            'S3410-48TS-P',
            '*******.*******.1.3',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"other\",\"2\":\"regular1822\",\"3\":\"hdh1822\",\"4\":\"ddn-x25\",\"5\":\"rfc877-x25\",\"6\":\"ethernet-csmacd\",\"7\":\"iso88023-csmacd\",\"8\":\"iso88024-tokenBus\",\"9\":\"iso88025-tokenRing\",\"10\":\"iso88026-man\",\"11\":\"starLan\",\"12\":\"proteon-10Mbit\",\"13\":\"proteon-80Mbit\",\"14\":\"hyperchannel\",\"15\":\"fddi\",\"16\":\"lapb\",\"17\":\"sdlc\",\"18\":\"ds1\",\"19\":\"e1\",\"20\":\"basicISDN\",\"21\":\"primaryISDN\",\"22\":\"propPointToPointSerial\",\"23\":\"ppp\",\"24\":\"softwareLoopback\",\"25\":\"eon\",\"26\":\"ethernet-3Mbit\",\"27\":\"nsip\",\"28\":\"slip\",\"29\":\"ultra\",\"30\":\"ds3\",\"31\":\"sip\",\"32\":\"frame-relay\",\"53\":\"propVirtual\",\"117\":\"gigabitEthernet\",\"136\":\"l2vlan\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Power Status',
            'S3410-48TS-P',
            '',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'CPU Used Percentage',
            'S5500-48T6SP-R',
            '*******.4.1.52642.********.11',
            'walk',
            NULL,
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Used Percentage',
            'S5500-48T6SP-R',
            '*******.4.1.52642.********.12',
            'walk',
            NULL,
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Usage Size',
            'S5500-48T6SP-R',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Size',
            'S5500-48T6SP-R',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Name',
            'S5500-48T6SP-R',
            '*******.*******.1.2',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets Discarded',
            'S5500-48T6SP-R',
            '*******.*******.1.19',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets with Errors',
            'S5500-48T6SP-R',
            '*******.*******.1.14',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets Discarded',
            'S5500-48T6SP-R',
            '*******.*******.1.13',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets with Errors',
            'S5500-48T6SP-R',
            '*******.*******.1.20',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Sent',
            'S5500-48T6SP-R',
            '*******.********.1.1.10',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Received',
            'S5500-48T6SP-R',
            '*******.********.1.1.6',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Speed',
            'S5500-48T6SP-R',
            '*******.*******.1.5',
            'get',
            'ifIndex',
            'metric',
            'bps',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Status',
            'S5500-48T6SP-R',
            '*******.*******.1.8',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"Up\",\"2\":\"Down\",\"3\":\"Testing\",\"4\":\"Unknown\",\"5\":\"Dormant\",\"6\":\"NotPresent\",\"7\":\"LowerLayerDown\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface MTU',
            'S5500-48T6SP-R',
            '*******.*******.1.4',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Physical Address',
            'S5500-48T6SP-R',
            '*******.*******.1.6',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Temperature',
            'S5500-48T6SP-R',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'MAC Address',
            'S5500-48T6SP-R',
            '*******.4.1.52642.*********.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Usage Size',
            'S5500-48T6SP-R',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Free Size',
            'S5500-48T6SP-R',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Size',
            'S5500-48T6SP-R',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'PoE Power',
            'S5500-48T6SP-R',
            '',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Rx Power',
            'S5500-48T6SP-R',
            '*******.4.1.52642.********.1.3',
            'get',
            'ifIndex',
            'metric',
            '0.01dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Tx Power',
            'S5500-48T6SP-R',
            '*******.4.1.52642.********.1.2',
            'get',
            'ifIndex',
            'metric',
            '0.01dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Current',
            'S5500-48T6SP-R',
            '*******.4.1.52642.********.1.6',
            'get',
            'ifIndex',
            'metric',
            '2uA',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Voltage',
            'S5500-48T6SP-R',
            '*******.4.1.52642.********.1.5',
            'get',
            'ifIndex',
            'metric',
            '100uV',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Optical Module Temperature',
            'S5500-48T6SP-R',
            '*******.4.1.52642.********.1.4',
            'get',
            'ifIndex',
            'metric',
            '0.00390625Celsius',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Name',
            'S5500-48T6SP-R',
            '',
            'get',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Speed',
            'S5500-48T6SP-R',
            '',
            'get',
            'fanIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Status',
            'S5500-48T6SP-R',
            '',
            'get',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Name',
            'S5500-48T6SP-R',
            '',
            'get',
            'supplyIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Status',
            'S5500-48T6SP-R',
            '',
            'get',
            'supplyIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Location',
            'S5500-48T6SP-R',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Sysname',
            'S5500-48T6SP-R',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'System Uptime',
            'S5500-48T6SP-R',
            '*******.*******.0',
            'get',
            NULL,
            'metric',
            '0.01s',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device SW Version',
            'S5500-48T6SP-R',
            '*******.4.1.52642.********.6.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device HW Version',
            'S5500-48T6SP-R',
            '*******.4.1.52642.********.5.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Type',
            'S5500-48T6SP-R',
            '*******.*******.1.3',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"other\",\"2\":\"regular1822\",\"3\":\"hdh1822\",\"4\":\"ddn-x25\",\"5\":\"rfc877-x25\",\"6\":\"ethernet-csmacd\",\"7\":\"iso88023-csmacd\",\"8\":\"iso88024-tokenBus\",\"9\":\"iso88025-tokenRing\",\"10\":\"iso88026-man\",\"11\":\"starLan\",\"12\":\"proteon-10Mbit\",\"13\":\"proteon-80Mbit\",\"14\":\"hyperchannel\",\"15\":\"fddi\",\"16\":\"lapb\",\"17\":\"sdlc\",\"18\":\"ds1\",\"19\":\"e1\",\"20\":\"basicISDN\",\"21\":\"primaryISDN\",\"22\":\"propPointToPointSerial\",\"23\":\"ppp\",\"24\":\"softwareLoopback\",\"25\":\"eon\",\"26\":\"ethernet-3Mbit\",\"27\":\"nsip\",\"28\":\"slip\",\"29\":\"ultra\",\"30\":\"ds3\",\"31\":\"sip\",\"32\":\"frame-relay\",\"53\":\"propVirtual\",\"117\":\"gigabitEthernet\",\"136\":\"l2vlan\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Power Status',
            'S5500-48T6SP-R',
            '*******.4.1.52642.*********',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            'expression',
            'lambda x: \"Power-A-Normal\" if x == \"1\" else \"Power-B-Normal\" if x == \"2\" else \"Power-A-B-Normal\" if x == \"3\" else \"Other\"'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'CPU Used Percentage',
            'S5850-48S6Q-R',
            '*******.4.1.52642.*******.0',
            'get',
            NULL,
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Used Percentage',
            'S5850-48S6Q-R',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Usage Size',
            'S5850-48S6Q-R',
            '*******.4.1.52642.********.0',
            'get',
            NULL,
            'metric',
            'KB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Size',
            'S5850-48S6Q-R',
            '*******.4.1.52642.*******.0',
            'get',
            NULL,
            'metric',
            'KB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Name',
            'S5850-48S6Q-R',
            '*******.*******.1.2',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets Discarded',
            'S5850-48S6Q-R',
            '*******.*******.1.19',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets with Errors',
            'S5850-48S6Q-R',
            '*******.*******.1.14',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets Discarded',
            'S5850-48S6Q-R',
            '*******.*******.1.13',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets with Errors',
            'S5850-48S6Q-R',
            '*******.*******.1.20',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Sent',
            'S5850-48S6Q-R',
            '*******.********.1.1.10',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Received',
            'S5850-48S6Q-R',
            '*******.********.1.1.6',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Speed',
            'S5850-48S6Q-R',
            '*******.*******.1.5',
            'get',
            'ifIndex',
            'metric',
            'bps',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Status',
            'S5850-48S6Q-R',
            '*******.*******.1.8',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"Up\",\"2\":\"Down\",\"3\":\"Testing\",\"4\":\"Unknown\",\"5\":\"Dormant\",\"6\":\"NotPresent\",\"7\":\"LowerLayerDown\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface MTU',
            'S5850-48S6Q-R',
            '*******.*******.1.4',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Physical Address',
            'S5850-48S6Q-R',
            '*******.*******.1.6',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Temperature',
            'S5850-48S6Q-R',
            '*******.4.1.52642.********.*******.1',
            'get',
            NULL,
            'metric',
            'Celsius',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'MAC Address',
            'S5850-48S6Q-R',
            '*******.********.1.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Usage Size',
            'S5850-48S6Q-R',
            '',
            'get',
            NULL,
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Free Size',
            'S5850-48S6Q-R',
            '*******.4.1.52642.*******.*******.1',
            'get',
            NULL,
            'metric',
            'GB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Size',
            'S5850-48S6Q-R',
            '*******.4.1.52642.*******.*******.1',
            'get',
            NULL,
            'metric',
            'GB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'PoE Power',
            'S5850-48S6Q-R',
            '',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Rx Power',
            'S5850-48S6Q-R',
            '*******.4.1.52642.********0.6.1.5',
            'get',
            'ifIndex',
            'metric',
            'dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Tx Power',
            'S5850-48S6Q-R',
            '*******.4.1.52642.********0.5.1.5',
            'get',
            'ifIndex',
            'metric',
            'dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Current',
            'S5850-48S6Q-R',
            '*******.4.1.52642.********0.4.1.5',
            'get',
            'ifIndex',
            'metric',
            'mA',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Voltage',
            'S5850-48S6Q-R',
            '*******.4.1.52642.********0.3.1.5',
            'get',
            'ifIndex',
            'metric',
            'Voltage',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Optical Module Temperature',
            'S5850-48S6Q-R',
            '*******.4.1.52642.********0.2.1.5',
            'get',
            'ifIndex',
            'metric',
            'Celsius',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Name',
            'S5850-48S6Q-R',
            '',
            'get',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Speed',
            'S5850-48S6Q-R',
            '*******.4.1.52642.********.*******.1',
            'get',
            'fanIndex',
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Status',
            'S5850-48S6Q-R',
            '*******.4.1.52642.********.*******.1',
            'get',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            'expression',
            'lambda x: \"Active\" if x == \"1\" else \"Deactive\" if x == \"2\" else \"Not Installed\" if x == \"3\" else \"Unsupported\" if x == \"4\" else \"Unknown\"'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Name',
            'S5850-48S6Q-R',
            '',
            'get',
            'supplyIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Status',
            'S5850-48S6Q-R',
            '*******.4.1.52642.********.1.2',
            'get',
            'supplyIndex',
            'dimension',
            '',
            '',
            1,
            'expression',
            'lambda x: \"Present\" if x == \"1\" else \"Absent\" if x == \"2\" else \"Unsupport\" if x == \"3\" else \"Unknown\"'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Location',
            'S5850-48S6Q-R',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Sysname',
            'S5850-48S6Q-R',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'System Uptime',
            'S5850-48S6Q-R',
            '*******.*******.0',
            'get',
            NULL,
            'metric',
            '0.01s',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device SW Version',
            'S5850-48S6Q-R',
            '*******.********.********.1',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device HW Version',
            'S5850-48S6Q-R',
            '*******.4.1.52642.********.*******',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Type',
            'S5850-48S6Q-R',
            '*******.*******.1.3',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"other\",\"2\":\"regular1822\",\"3\":\"hdh1822\",\"4\":\"ddn-x25\",\"5\":\"rfc877-x25\",\"6\":\"ethernet-csmacd\",\"7\":\"iso88023-csmacd\",\"8\":\"iso88024-tokenBus\",\"9\":\"iso88025-tokenRing\",\"10\":\"iso88026-man\",\"11\":\"starLan\",\"12\":\"proteon-10Mbit\",\"13\":\"proteon-80Mbit\",\"14\":\"hyperchannel\",\"15\":\"fddi\",\"16\":\"lapb\",\"17\":\"sdlc\",\"18\":\"ds1\",\"19\":\"e1\",\"20\":\"basicISDN\",\"21\":\"primaryISDN\",\"22\":\"propPointToPointSerial\",\"23\":\"ppp\",\"24\":\"softwareLoopback\",\"25\":\"eon\",\"26\":\"ethernet-3Mbit\",\"27\":\"nsip\",\"28\":\"slip\",\"29\":\"ultra\",\"30\":\"ds3\",\"31\":\"sip\",\"32\":\"frame-relay\",\"53\":\"propVirtual\",\"117\":\"gigabitEthernet\",\"136\":\"l2vlan\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Power Status',
            'S5850-48S6Q-R',
            '',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'CPU Used Percentage',
            'S5860-24MG-U',
            '*******.4.1.52642.********.36.*******',
            'get',
            NULL,
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Used Percentage',
            'S5860-24MG-U',
            '*******.4.1.52642.********.35.*******.1',
            'get',
            NULL,
            'metric',
            '%',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Usage Size',
            'S5860-24MG-U',
            '*******.4.1.52642.********.********.13.1',
            'get',
            NULL,
            'metric',
            'MB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Memory Size',
            'S5860-24MG-U',
            '*******.4.1.52642.********.35.********.1',
            'get',
            NULL,
            'metric',
            'MB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Name',
            'S5860-24MG-U',
            '*******.*******.1.2',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets Discarded',
            'S5860-24MG-U',
            '*******.*******.1.19',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets with Errors',
            'S5860-24MG-U',
            '*******.*******.1.14',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'In Packets Discarded',
            'S5860-24MG-U',
            '*******.*******.1.13',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Out Packets with Errors',
            'S5860-24MG-U',
            '*******.*******.1.20',
            'get',
            'ifIndex',
            'metric',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Sent',
            'S5860-24MG-U',
            '*******.********.1.1.10',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bits Received',
            'S5860-24MG-U',
            '*******.********.1.1.6',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Speed',
            'S5860-24MG-U',
            '*******.*******.1.5',
            'get',
            'ifIndex',
            'metric',
            'bps',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Status',
            'S5860-24MG-U',
            '*******.*******.1.8',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"Up\",\"2\":\"Down\",\"3\":\"Testing\",\"4\":\"Unknown\",\"5\":\"Dormant\",\"6\":\"NotPresent\",\"7\":\"LowerLayerDown\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface MTU',
            'S5860-24MG-U',
            '*******.*******.1.4',
            'get',
            'ifIndex',
            'metric',
            'bytes',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Physical Address',
            'S5860-24MG-U',
            '*******.*******.1.6',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Temperature',
            'S5860-24MG-U',
            '*******.4.1.52642.********.********',
            'get',
            NULL,
            'metric',
            'Celsius',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'MAC Address',
            'S5860-24MG-U',
            '*******.4.1.52642.********.10.*******1.2',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Usage Size',
            'S5860-24MG-U',
            '*******.4.1.52642.********.********.4',
            'walk',
            NULL,
            'metric',
            'MB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Free Size',
            'S5860-24MG-U',
            '*******.4.1.52642.********.47.*******',
            'walk',
            NULL,
            'metric',
            'MB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Flash Size',
            'S5860-24MG-U',
            '*******.4.1.52642.********.47.*******',
            'walk',
            NULL,
            'metric',
            'MB',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'PoE Power',
            'S5860-24MG-U',
            '1.0.8802.*******.5.4795.********.1',
            'get',
            'ifIndex',
            'metric',
            'W',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Rx Power',
            'S5860-24MG-U',
            '*******.4.1.52642.********.105.*******1',
            'get',
            'ifIndex',
            'metric',
            'dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Tx Power',
            'S5860-24MG-U',
            '*******.4.1.52642.********.105.*******1',
            'get',
            'ifIndex',
            'metric',
            'dBm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Current',
            'S5860-24MG-U',
            '*******.4.1.52642.********.105.*******1',
            'get',
            'ifIndex',
            'metric',
            'uA',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Bias Voltage',
            'S5860-24MG-U',
            '*******.4.1.52642.********.*********.19',
            'get',
            'ifIndex',
            'metric',
            'mVolts',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Optical Module Temperature',
            'S5860-24MG-U',
            '*******.4.1.52642.********.*********.17',
            'get',
            'ifIndex',
            'metric',
            'Celsius',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Name',
            'S5860-24MG-U',
            '*******.4.1.52642.********.********.3',
            'get',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Speed',
            'S5860-24MG-U',
            '*******.4.1.52642.********.********.6.1',
            'walk',
            'fanIndex',
            'metric',
            'rpm',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Fan Status',
            'S5860-24MG-U',
            '*******.4.1.52642.********.********.4.1',
            'walk',
            'fanIndex',
            'dimension',
            '',
            '',
            1,
            'expression',
            'lambda x: \"NoExist\" if x == \"1\" else \"ExistNoPower\" if x == \"2\" else \"ExistReadyPower\" if x == \"3\" else \"Normal\" if x == \"4\" else \"PowerButAbnormal\" if x == \"5\" else \"Unknow\"'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Name',
            'S5860-24MG-U',
            '*******.4.1.52642.********.********.3',
            'get',
            'supplyIndex',
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Supply Status',
            'S5860-24MG-U',
            '*******.4.1.52642.********.********.3.1',
            'get',
            'supplyIndex',
            'dimension',
            '',
            '',
            1,
            'expression',
            'lambda x: \"NoExist\" if x == \"1\" else \"ExistNoPower\" if x == \"2\" else \"ExistReadyPower\" if x == \"3\" else \"Normal\" if x == \"4\" else \"PowerButAbnormal\" if x == \"5\" else \"Unknow\"'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device Location',
            'S5860-24MG-U',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Sysname',
            'S5860-24MG-U',
            '*******.*******.0',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'System Uptime',
            'S5860-24MG-U',
            '*******.*******.0',
            'get',
            NULL,
            'metric',
            '0.01s',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device SW Version',
            'S5860-24MG-U',
            '*******.4.1.52642.********.*******',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Device HW Version',
            'S5860-24MG-U',
            '*******.4.1.52642.********.*******',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Interface Type',
            'S5860-24MG-U',
            '*******.*******.1.3',
            'get',
            'ifIndex',
            'dimension',
            '',
            '',
            1,
            'static',
            '{\"1\":\"other\",\"2\":\"regular1822\",\"3\":\"hdh1822\",\"4\":\"ddn-x25\",\"5\":\"rfc877-x25\",\"6\":\"ethernet-csmacd\",\"7\":\"iso88023-csmacd\",\"8\":\"iso88024-tokenBus\",\"9\":\"iso88025-tokenRing\",\"10\":\"iso88026-man\",\"11\":\"starLan\",\"12\":\"proteon-10Mbit\",\"13\":\"proteon-80Mbit\",\"14\":\"hyperchannel\",\"15\":\"fddi\",\"16\":\"lapb\",\"17\":\"sdlc\",\"18\":\"ds1\",\"19\":\"e1\",\"20\":\"basicISDN\",\"21\":\"primaryISDN\",\"22\":\"propPointToPointSerial\",\"23\":\"ppp\",\"24\":\"softwareLoopback\",\"25\":\"eon\",\"26\":\"ethernet-3Mbit\",\"27\":\"nsip\",\"28\":\"slip\",\"29\":\"ultra\",\"30\":\"ds3\",\"31\":\"sip\",\"32\":\"frame-relay\",\"53\":\"propVirtual\",\"117\":\"gigabitEthernet\",\"136\":\"l2vlan\"}'
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
INSERT INTO snmp_metric
        (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal, mapping_type, mapping_content)
        VALUES (
            'Power Status',
            'S5860-24MG-U',
            '',
            'get',
            NULL,
            'dimension',
            '',
            '',
            1,
            '',
            ''
        ) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            method=new.method,
            index_from_name=new.index_from_name,
            data_type=new.data_type,
            unit=new.unit,
            description=new.description,
            is_internal=new.is_internal,
            mapping_type=new.mapping_type,
            mapping_content=new.mapping_content;
