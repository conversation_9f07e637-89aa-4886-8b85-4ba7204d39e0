create database automation DEFAULT CHARACTER SET utf8;
create database `celery-schedule` DEFAULT CHARACTER SET utf8;

USE mysql;
GRANT All PRIVILEGES ON *.* to 'root'@'localhost' WITH GRANT OPTION;
FLUSH PRIVILEGES;

GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;

create user automation@'localhost' identified by 'automation';
GRANT All PRIVILEGES ON *.* to 'automation'@'localhost' WITH GRANT OPTION;
FLUSH PRIVILEGES;

create user automation@'%' identified by 'automation';
GRANT All PRIVILEGES ON *.* to 'automation'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;

create user repl@'%' identified by 'repl';
GRANT REPLICATION SLAVE ON *.* to 'repl'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;
