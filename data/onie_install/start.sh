#!/bin/sh

#start to collect the switch ONIE information to prepare the installation
hardware=`onie-syseeprom -g 0x21`
sn_eeprom=`onie-syseeprom -g 0x23`
sysinfo=`onie-sysinfo`
server_host="ac.ampcon.local"

for FILE in 'picos.bin' '/root/auto-deploy.conf' '/root/picos_patch.sh'
do
  if test -f "$FILE"; then
    rm -f $FILE
  fi
done

#start to download the images
wget http://${server_host}:80/onie/download_onie --post-data "&sn_eeprom=${sn_eeprom}&hardware=${hardware}&sysinfo=${sysinfo}" -O picos.bin
#check wheterh the .bin file
if [[ $(stat -c%s "picos.bin") -lt 1000 ]]; then
   cat picos.bin && echo
   exit 1
fi

#start to download the onie config
wget http://${server_host}:80/onie/download_file --post-data "&hardware=${hardware}&sysinfo=${sysinfo}&file_name=auto-deploy.py" -O /root/auto-deploy.py
wget http://${server_host}:80/onie/download_file --post-data "&sn_eeprom=${sn_eeprom}&hardware=${hardware}&sysinfo=${sysinfo}&file_name=auto-deploy.conf" -O /root/auto-deploy.conf
wget http://${server_host}:80/onie/download_file --post-data "&hardware=${hardware}&sysinfo=${sysinfo}&file_name=picos_patch.sh" -O /root/picos_patch.sh

#start to install onie
onie-nos-install picos.bin