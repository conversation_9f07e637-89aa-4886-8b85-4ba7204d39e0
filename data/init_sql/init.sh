#!/bin/sh
set -e

echo ">>>>>>>>>>>>>>>>>>>>>>>>>init db started."

mysql -uroot -proot < /opt/sql/init.sql;
mysql -uroot -proot automation < /opt/sql/automation.sql;
mysql -uroot -proot automation < /opt/sql/cli_tree.sql;
mysql -uroot -proot automation < /opt/sql/compatibility.sql;

mysql -uroot -proot automation < /opt/sql/hardware_mapping.sql;
mysql -uroot -proot automation < /opt/sql/model_physic_port.sql;
mysql -uroot -proot automation < /opt/sql/switch_systeminfo.sql;

mysql -uroot -proot automation < /opt/sql/init_user_and_encrypt_key.sql;

touch /var/lib/mysql/load_all_data_done
echo ">>>>>>>>>>>>>>>>>>>>>>>>>init db success":