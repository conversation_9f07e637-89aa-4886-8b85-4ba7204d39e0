[DEFAULT]

# set log level:
#CRITICAL 50
#ERROR	  40
#WARNING  30
#INFO     20
#DEBUG	  10
#NOTSET	  0
#set the number log level, default is 30
# default_log_level: 10

# whether show debug message, True debug mode, false log level is info
# verbose: True

#
# use_stderr: False

# whether use syslog default is false
# use_syslog: True

# the file which write log to default is current path
log_file: /var/log/automation/gunicorn_app_server.log
# log_file: automation.log

# which ip address server listen
bind: 0.0.0.0

# which port server listen
port: 443

# how many child process to run for deploy switch
workers: 10

license_fresh_interval: 86400
# license_portal_proxy: http:http://**************:8080,https:http://**************:8080

# verify license portal certificate, default is False
license_portal_cert_verify: False

# server global which switch can connect to
global_ip : ***********

rma_interval: 86400

verizon_feature: False

# whether switch vpn conntect with server keep alive
vpn_keepalive: True

vpn_enable: True

ssl_private_key_path: server_keys/server.key.unsecure
ssl_crt_path: server_keys/server.crt

vpn_server_key: /etc/openvpn/server/ca.key
vpn_server_crt: /etc/openvpn/server/ca.crt

# switch log may be large, we should not get all switch log once
display_log_cycle: 200

# mac_vlan table limit
mac_vlan_limit: 2000

#rabbitmq-pwd
rabbitmq_pwd: admin

#prometheus url
prometheus_url: prometheus:9090
prometheus_rules_path: /usr/share/automation/server/monitor/rules
prometheus_template_path: /usr/share/automation/server/monitor/prometheus_rules.j2
prometheus_default_values: /usr/share/automation/server/monitor/prometheus_default_values.yml

# Alarm thread count
alarm_thread_count: 8

# For apscheduler task pool, 
# The default value will be CPU processors number +2 if not set 
# task_pool_size: 6

# no need to auth urls , regex type
no_auth_urls: ^/login, ^/reg, ^/vpn_update_ip, ^/management/vpn, ^/management/vpn_reg, ^/rma/file, ^/static, ^/favicon.ico, ^/monitor/server/available, ^/monitor/server/status, ^/management/upload_switch_status, ^/deploy/failed, ^/rma/vpn, ^/otn_device_lic, ^/file/transfer, ^/check_lic, ^/install_lic, ^/del_switch_with_check, ^/get_local_key, ^/monitor/alert_log, ^/monitor/get_montior_target, ^/config/download_image, ^/upgrade/image/action/download, ^/upgrade/models, ^/upgrade/status, ^/config/tar_append_2html, ^/wireless/site/channel, ^/wireless/configure/query, ^/wireless/monitor/client, ^/monitor/get_snmp_target

supports_models: as4610_54p,as4610_54t,as4610_54t_b,as4610_30p,as4610_30t,AS4630-54PE,AS4630-54NPE,as5812_54x,as5812_54t,as5835_54x,N3024EP-ON,N3024ET-ON,N3048EP-ON,N3048ET-ON,N3132PX-ON,S4048-ON,S4148T-ON,S4148F-ON,N3248P-ON,N3248PXE-ON,S5224F-ON,S5296F-ON,S4128F-ON,S4128T-ON,as7326_56x,as7312_54x,N3248X-ON,N3248TE-ON,N2224X-ON,N2224PX-ON,N2248X-ON,N2248PX-ON,N3208PX-ON,N3224P-ON,N3224PX-ON,N3224X-ON,N3224F-ON,N3224T-ON,S5212F-ON,S5248F-ON,S5232F-ON,as7712_32x,as7726_32x,as7816_64x,Z9100-ON,Z9264F-ON,as5712_54x,HPE AL 6921-54T,HPE AL 6921-54X,as6812_32x,as5835_54t,AS4630-54TE,ag9032,ag5648,ag7648,AS4625-54P,AS4625-54T,S5860-20SQ,S5860-24XB-U,S5810-48TS-P,S5810-28TS,S5810-28FS,S5810-48TS,S5810-48FS,N8560-32C,N8550-48B8C,N5850-48S6Q,N8550-32C,S5860-24MG-U,S5860-48XMG-U,S5860-24XMG,S5860-48MG-U,S5860-48XMG,S5870-48T6BC,S5870-48T6BC-U,N5850-48X6C,S5870-48T6S,S5870-48T6S-U,N5850-48S6C,N8550-64C,N9550-32D,S3410L-24TF,S3410L-24TF-P,S3410-24TS,S3410-24TS-P,S3410C-16TF,S3410C-16TF-P,S3410C-16TMS-P,S3410C-8TMS-P,S5870-48MX6BC-U,S6860-24CD8D,S5890-32C,S4720-48BC,S4520-48X6C,S4320M-48MX6BC-U,S3270-10TM,S3270-24TM,S3270-48TM,S3270-10TM-P,S3270-24TM-P,N8550-24CD8D,S3410L-48TF,S3410-48TS,S3410-48TS-P,S5580-48Y

[database]
# This line MUST be changed to actually run the automation.
# Example:
# connection: sqlite:////var/lib/automation/automation.sqlite
# local
connection: mysql+pymysql://automation:automation@mysql-service/automation?charset=utf8
# connection: mysql+pymysql://automation:automation@10.10.53.109/automation?charset=utf8


# Minimum number of SQL connections to keep open in a pool
min_pool_size: 3

# Maximum number of SQL connections to keep open in a pool
max_pool_size: 30


#
# Timeout in seconds before idle sql connections are reaped
# idle_timeout: 3600

# If set, use this value for max_overflow with sqlalchemy
# max_overflow: 20

# Verbosity of SQL debugging information. 0=None, 100=Everything
# connection_debug: 100

# Add python stack traces to SQL as comment strings
# connection_trace: True

# If set, use this value for pool_timeout with sqlalchemy
# pool_timeout: 10


[mail]
server: smtp.office365.com
port: 587
user: xxxx
password: xxxxx

[map]
#In default, using Openstreemap geo-api
geocoding_api: osm
nominatim_server_url: None
map_central_latlng: 37.431809, -122.103149
maxZoom: 16
tileLayer: http://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png


[pg_database]
connection: postgresql+psycopg2://smb:smb@postgresql/smbdb

# Maximum number of SQL connections to keep open in a pool
max_pool_size: 50

# Timeout in seconds before idle sql connections are reaped
# idle_timeout: 3600

# If set, use this value for max_overflow with sqlalchemy
# max_overflow: 20

# Verbosity of SQL debugging information. 0=None, 100=Everything
# connection_debug: 100

# Add python stack traces to SQL as comment strings
# connection_trace: True

# If set, use this value for pool_timeout with sqlalchemy
# pool_timeout: 10
