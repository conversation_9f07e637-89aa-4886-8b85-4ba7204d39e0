[client]
port = 3306
socket = /var/lib/mysql/mysql.sock
default-character-set = utf8mb4

[mysqld]
bind-address=0.0.0.0
port = 3306
sql_mode= "STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION"

server-id=1
auto_increment_offset=1
auto_increment_increment=2

collation-server=utf8mb4_bin
character-set-server=utf8mb4
user = mysql
socket = /var/lib/mysql/mysql.sock
pid-file = /var/lib/mysql/mysql.pid

basedir=/usr
datadir=/var/lib/mysql

log-bin= mysqld-bin
# format statement, row, mixed
binlog-format=ROW
# only binlog-format is row: full, minimal, noblob
binlog_row_image=noblob
#relay-log=relay-bin
#relay-log-index=relay-bin-index
#binlog-do-db=test
binlog-do-db=automation
binlog-ignore-db=mysql,information_schema
#replicate-do-db=test
replicate-do-db=automation
replicate_ignore_db=mysql,information_schema
sync-binlog = 1
slave-skip-errors=all

# allow max sql packet size
max_allowed_packet = 64M

log_error = /var/log/mysql/error.log
connect_timeout = 20
net_read_timeout = 100
interactive_timeout = 288000
wait_timeout = 288000

# skip-name-resolve
skip-log-bin

max_connections=1000