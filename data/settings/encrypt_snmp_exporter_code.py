#!/usr/bin/python
import os
import shutil
import compileall
from pathlib import Path


def main():
    origin = "/app/snmp_exporter"
    dest = "/tmp"
    origin_path = Path(origin)
    for src_file in origin_path.rglob("*"):
        # Skip directories
        if src_file.is_dir():
            continue

        # Move non-Python files to the destination folder
        if not src_file.suffix == ".py":
            dest_folder = dest + "/" + str(src_file.relative_to(origin_path).parent)
            os.makedirs(dest_folder, exist_ok=True)
            dest_file = dest_folder + "/" + src_file.name
            print(f"move {src_file.relative_to(origin_path)}")
            shutil.move(src_file, dest_file)

    # Remove existing Python compiled files
    for src_file in origin_path.rglob("*.pyc"):
        os.remove(src_file)

    # Compile Python files and copy them to the destination folder
    compileall.compile_dir(origin_path, force=True)
    for src_file in origin_path.glob("**/*.pyc"):
        relative_path = src_file.relative_to(origin_path)
        dest_folder = dest + "/" + str(relative_path.parent.parent)
        os.makedirs(dest_folder, exist_ok=True)
        dest_file = dest_folder + "/" + (src_file.stem.rsplit(".", 1)[0] + src_file.suffix)
        print(f"install {relative_path}")
        shutil.copyfile(src_file, dest_file)


if __name__ == "__main__":
    main()

