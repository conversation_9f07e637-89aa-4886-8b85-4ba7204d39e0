#!/usr/bin/python3

import subprocess
import re,time,os
import urllib.request, urllib.parse, urllib.error,urllib.request,urllib.error,urllib.parse
import fcntl,sys,socket
import logging,logging.handlers

_get_copp_stat_cmd = '/pica/bin/sif/tools/print_copp -s'
_get_interface_stat_cmd = '/pica/bin/sif/tools/print_interfaces -d'
_get_mac_address_cmd = '/pica/bin/lcmgr/tools/print_fdb -b -f 2'
_get_arp_cmd = '/pica/bin/sif/tools/print_arp -m ARP'
_get_syslog_linkdown_cmd = 'tail -n 1000 /tmp/log/messages | grep "changed state to down"'
_get_syslog_err_cmd = 'tail -n 1000 /tmp/log/messages | grep "\.err"'
_get_syslog_login_cmd = 'tail -n 1000 /tmp/log/messages | grep "Accepted password for"'
_get_syslog_login_failed_cmd = 'tail -n 1000 /tmp/log/messages | grep "Failed password for"'
_get_syslog_bootup_cmd = 'tail -n 1000 /tmp/log/messages | grep "[RTRMGR]Config file parsed completely"'
_get_route_stat_cmd = '/pica/bin/sif/tools/show_routes -b ribout all'
_get_vlan_stat_cmd = '/pica/bin/sif/tools/print_vlans -b'

data_server = 'https://vpn.pica8.com'
server_port = '443'

_lock_fd = 0

def log_warn(msg):
    _logger.warning(msg)

def check_already_running():
    global _lock_fd
    _lock_fd = open(os.path.realpath(__file__), "r")
    try:
        fcntl.flock(_lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
    except:
        log_warn("Auto-Collector already running, aborting")
        sys.exit(1)

def time_format_check(str):
    month_map = {'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
                 'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'}
    if len(str) == 23:
        return str
    else:
        month = month_map[str[0:3]]
        day = str[4:6].replace(' ', '0')
        year = str[7:11]
        time_str = str[12:20]
    return year + '-' + month + '-'+ day + ' '+ time_str + '.000'

def get_serial_num():
    cmd = "/pica/bin/system/fan_status -s | grep MotherBoard"
    result, rt = subprocess.getstatusoutput(cmd)
    sn = re.findall("MotherBoard Serial Number : (.*)", rt)[0]
    log_warn("Got serial num %s" % sn)
    return sn

def get_platform_name():
    cmd = "/usr/bin/version | grep Hardware"
    result, rt = subprocess.getstatusoutput(cmd)
    model = re.findall(": (.*)", rt)[0]
    log_warn("Got platform name %s" % model)
    return model

def get_local_ip():
    local_ip = ''
    result, rt = subprocess.getstatusoutput('ip addr | grep "inet.*vlan"')
    if rt:
        ips = re.findall(r"inet ((?:[0-9]{1,3}\.){3}[0-9]{1,3})", rt)
        if ips:
            local_ip = ips[0]
    return local_ip

def logger_init():
    try:
        logger = logging.getLogger('Auto-Collector')
        logger.setLevel(logging.INFO)
        sh = logging.handlers.SysLogHandler('/dev/log')
        sh.setFormatter(logging.Formatter(" [%(name)s]%(message)s"))
        logger.addHandler(sh)
        return logger
    except Exception as e:
        print(e)
        sys.exit(1)

def send_data_server(server, server_port, sn, ip, model, values):
    url = server + ':' + server_port + '/management/upload_switch_status'
    log_warn("Sending collected dat to server url %s" % url)
    #values = {'param1' : '3','param2' : '29'}
    values.update({'sn':sn, 'ip': ip, 'model': model})
    data = urllib.parse.urlencode(values)
    req = urllib.request.Request(url)
    req.add_data(data)
    try:
        res_data = urllib.request.urlopen(req, timeout=10)
        res = res_data.read()
        log_warn("Received reply message: %s" % res)
    except urllib.error.URLError as e:
        log_warn("Send counter to server failed: %s" % repr(e))
        return False

def collector_data():
    data = {}
    data.update({'copp': get_copp_stauts()})
    data.update({'interface_status': get_interface_stauts()})
    data.update({'fdb_status': get_mac_table_stauts()})
    data.update({'arp_status': get_arp_stauts()})
    data.update({'route_status': get_route_table_stauts()})
    data.update({'vlan_status': get_vlan_stauts()})
    data.update({'linkdown_event': get_linkdown_event()})
    data.update({'err_event': get_err_event()})
    data.update({'login_event': get_login_event()})
    data.update({'login_failed_event': get_login_failed_event()})
    data.update({'bootup_event': get_bootup_event()})
    return data

def get_copp_stauts():
    copp_status = {}
    result, rt = subprocess.getstatusoutput(_get_copp_stat_cmd)
    if rt:
        traffic_type = rt.split('\n\n')
        for each_type in traffic_type:
            type_name = re.findall("(.*) Traffic statistics:", each_type)[0]
            input_pps = re.findall("Input rate [0-9]+ bits/sec, ([0-9]+) packets/sec", each_type)[0]
            drop_pps = re.findall("Drop rate [0-9]+ bits/sec, ([0-9]+) packets/sec", each_type)[0]
            input_total = re.findall("Input Packets............................([0-9]+)", each_type)[0]
            drop_total = re.findall("Drop Packets.............................([0-9]+)", each_type)[0]
            copp_status.update({type_name:[input_pps, drop_pps, input_total, drop_total]})
    return copp_status

def get_interface_stauts():
    interface_status = {}
    result, rt = subprocess.getstatusoutput(_get_interface_stat_cmd)
    if rt:
        ports = rt.split('\n\n')
        for port in ports:
            portName = re.findall("Physical interface: (.*), Enable", port)[0]
            link = re.findall("Physical link is (Down|Up)",port)[0]
            speed = re.findall("Speed: (Auto|100M|1Gb|10Gb|25Gb|40Gb|100Gb)", port)[0]
            inputRate = re.findall("5 sec input rate ([0-9]+) bits/sec", port)[0]
            outputRate = re.findall("5 sec output rate ([0-9]+) bits/sec", port)[0]
            inputOct = re.findall("Input Octets.............................([0-9]+)", port)[0]
            outputOct = re.findall("Output Octets............................([0-9]+)", port)[0]
            inputDropOct = re.findall("Discarded Packets......................([0-9]+)", port)[1]
            outputDropOct = re.findall("Discarded Packets......................([0-9]+)", port)[0]
            ifType = "RJ45"
            interface_status.update({portName:[link, speed, inputRate, outputRate, inputOct, outputOct, inputDropOct, outputDropOct, ifType]})
    return interface_status

def get_mac_table_stauts():
    fdb_status = {}
    result, rt = subprocess.getstatusoutput(_get_mac_address_cmd)
    if rt:
        fdb_total = re.findall("Total entries in switching table:   ([0-9]+)",rt)[0]
        static_mac = re.findall("Static entries in switching table:  ([0-9]+)",rt)[0]
        learned_mac = re.findall("Dynamic entries in switching table: ([0-9]+)",rt)[0]
        fdb_status.update({'fdb_total': fdb_total,'static_mac': static_mac, 'learned_mac':learned_mac })
    return fdb_status

def get_arp_stauts():
    arp_status = {}
    result, rt = subprocess.getstatusoutput(_get_arp_cmd)
    if rt:
        arp_total = re.findall("Total count        : ([0-9]+)",rt)[0]
        arp_status.update({'arp_total': arp_total})
    return arp_status

def get_route_table_stauts():
    route_status = {}
    result, rt = subprocess.getstatusoutput(_get_route_stat_cmd)
    if rt:
        #If use do not configure any VLAN-INTERFACE, there is no IPv4
        if 'IPv4 Routing table' in rt:
            ipv4_route = re.findall("IPv4 Routing table: ([0-9]+) routes",rt)[0]
        else:
            ipv4_route = 0
        if 'IPv6 Routing table' in rt:
            ipv6_route = re.findall("IPv6 Routing table: ([0-9]+) routes",rt)[0]
        else:
            ipv6_route = 0
        route_status.update({'ipv4_route': ipv4_route,'ipv6_route': ipv6_route})
    return route_status

def get_vlan_stauts():
    vlan_list = []
    result, rt = subprocess.getstatusoutput(_get_vlan_stat_cmd)
    if rt:
        vlans = rt.split('\n\n')
        for vlan in vlans:
            vlan_id = re.findall("([0-9]+).*untagged", vlan)[0]
            vlan_list.append(vlan_id)
    return vlan_list

def get_linkdown_event():
    linkdown_event = {}
    result, rt = subprocess.getstatusoutput(_get_syslog_linkdown_cmd)
    if rt:
        logs = rt.split('\n')
        for log in logs:
            log_time = re.findall("([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]{3}|[a-zA-Z]{3} +[0-9]+ [0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2}) ", log)[0]
            content = re.findall(" : (.*)", log)[0]
            linkdown_event.update({time_format_check(log_time): content})
    return linkdown_event

def get_err_event():
    err_event = {}
    result, rt = subprocess.getstatusoutput(_get_syslog_err_cmd)
    if rt:
        logs = rt.split('\n')
        for log in logs:
            log_time = re.findall("([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]{3}|[a-zA-Z]{3} +[0-9]+ [0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2}) ", log)[0]
            content = re.findall(" : (.*)", log)[0]
            err_event.update({time_format_check(log_time): content})
    return err_event

def get_login_event():
    login_event = {}
    result, rt = subprocess.getstatusoutput(_get_syslog_login_cmd)
    if rt:
        logs = rt.split('\n')
        for log in logs:
            log_time = re.findall("([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]{3}|[a-zA-Z]{3} +[0-9]+ [0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2}) ", log)[0]
            content = re.findall(" : (.*)", log)[0]
            login_event.update({time_format_check(log_time): content})
    return login_event

def get_login_failed_event():
    login_failed_event = {}
    result, rt = subprocess.getstatusoutput(_get_syslog_login_failed_cmd)
    if rt:
        logs = rt.split('\n')
        for log in logs:
            log_time = re.findall("([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]{3}|[a-zA-Z]{3} +[0-9]+ [0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2}) ", log)[0]
            content = re.findall(" : (.*)", log)[0]
            login_failed_event.update({time_format_check(log_time): content})
    return login_failed_event

def get_bootup_event():
    bootup_event = {}
    result, rt = subprocess.getstatusoutput(_get_syslog_bootup_cmd)
    if rt:
        logs = rt.split('\n')
        for log in logs:
            log_time = re.findall("([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]{3}|[a-zA-Z]{3} +[0-9]+ [0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2}) ", log)[0]
            content = re.findall(" : (.*)", log)[0]
            bootup_event.update({time_format_check(log_time): content})
    return bootup_event

if __name__ == '__main__':
    # Agent should be called by root
    _logger = logger_init()
    time.sleep(2)
    check_already_running()

    try:
        values = collector_data()
        sn = get_serial_num()
        ip = get_local_ip()
        model = get_platform_name()
        send_data_server(data_server, server_port, sn, ip, model, values)
    except Exception as e:
        log_warn("Exit with exception %s" % str(e))
        sys.exit(1)
