---
- name: Nvidia Check Playbook
  hosts: all
  become: yes
  become_method: sudo
  gather_facts: no
  tasks:
    - name: IB toolkit detection
      shell: "{{ item }}"
      register: cmd_output1
      ignore_errors: no
      loop:
        - "dpkg -l | grep -i infiniband-diags"
        - "dpkg -l | grep -i ibutils"
        - "dpkg -l | grep -i rdma-core"

    - name: Drive detection
      shell: "{{ item }}"
      register: cmd_output2
      ignore_errors: no
      loop:
        - "lsmod | grep mlx5_ib"
        - "lsmod | grep mlx5_core"

    - name: Instruction set check
      ignore_errors: no
      shell: "{{ item }}"
      register: cmd_output3
      loop:
        - "dpkg -l | grep mlnx-ofed-kernel-dkms"
        - "dpkg -l | grep -i mft"
        - "dpkg -l | grep -i mlnx-tools"

    - name: IB device query
      ignore_errors: no
      shell: ibdev2netdev
      register: cmd_output4

    - name: Get Port Name
      set_fact:
        ports: "{{ cmd_output4.stdout | regex_findall('=>\\s(\\S+)') }}"
      when: cmd_output4.rc == 0 and cmd_output4.stdout_lines is defined

    - name: Check all RoCE devices with Ethernet link status
      ignore_errors: no
      shell: ibstatus|grep -i -B 8 'ethernet' |grep -i "infiniband device"|awk -F "'" '{print $2}'
      register: cmd_output5

    - name: Get Device Name
      set_fact:
        devices: "{{ cmd_output5.stdout.splitlines() | default([]) }}"
      when: cmd_output5.rc == 0 and cmd_output5.stdout_lines is defined

    - name: Determine PCI address
      ignore_errors: no
      shell: >
        readlink /sys/class/infiniband/{{ item }}/device
      loop: "{{ devices }}"
      register: cmd_output6
      when: devices | length > 0

    - name: Get PCI Addresses
      set_fact:
        pci_addresses: "{{ pci_addresses | default([]) + [item.stdout | regex_search('[0-9a-f]{4}:[0-9a-f]{2}:[0-9a-f]{2}\\.\\d')] | select('defined') | list }}"
      loop: "{{ cmd_output6.results }}"
      when: item.stdout is defined and item.stdout | length > 0 and item.stdout | regex_search('[0-9a-f]{4}:[0-9a-f]{2}:[0-9a-f]{2}\\.[0-9]') is not none

    - name: RoCE function check
      ignore_errors: no
      shell: >
        cat /sys/class/infiniband/{{ item }}/ports/1/gid_attrs/types/3
      loop: "{{ devices }}"
      register: cmd_output7
      when: devices | length > 0

    - name: RoCE configuration query part 1
      ignore_errors: no
      shell: sysctl net.ipv4.tcp_ecn
      register: cmd_output8

    - name: RoCE configuration query part 2
      ignore_errors: no
      shell: >
        mlnx_qos -i {{ item }}
      loop: "{{ ports }}"
      register: cmd_output9
      when: ports | length > 0

    - name: Display inspection equipment part 1
      ignore_errors: yes
      shell: >
        readlink /sys/class/infiniband/{{ item }}/device
      loop: "{{ devices }}"
      register: cmd_output10
      when: devices | length > 0

    - name: Display inspection equipment part 2
      ignore_errors: yes
      shell: >
        sudo lspci -vvvs {{ item }} |grep -i "part number"
      loop: "{{ pci_addresses }}"
      register: cmd_output11
      when: devices | length > 0