declare -a netdev
for i in $ibdev; do
    dev=$(ibdev2netdev | grep -w "$i" | awk '{print $5}')
    if [ -n "$dev" ]; then
        netdev+=("$dev")
    fi
done
for i in "${netdev[@]}";
do
    # Get PCI device ID
    ETH_NAME=$(ethtool -i "$i" 2>/dev/null | awk '/bus-info/ {print $2}')
    if [ -z "$ETH_NAME" ]; then
        echo ">>>Error: Unable to get the PCI device ID for NIC $i, check if NIC exists!"
        continue
    fi
    # Get RDMA device name
    RDMA_NAME=$(readlink -f /sys/class/infiniband/* 2>/dev/null | grep -i "$ETH_NAME" | awk -F "/" '{print $NF}')
    # Check if RDMA device already exists
    if [ -n "$RDMA_NAME" ]; then
        echo "RDMA device ($RDMA_NAME) already exists, skipping RoCEv2 enable step."
    else
        echo "RDMA device does not exist, enabling RoCEv2 mode..."
        cma_roce_mode -d "$i" -p 1 -m 2
        sleep 5  # Wait for RDMA device initialization
        RDMA_NAME=$(readlink -f /sys/class/infiniband/* 2>/dev/null | grep -i "$ETH_NAME" | awk -F "/" '{print $NF}')
        if [ -z "$RDMA_NAME" ]; then
            echo "RoCEv2 enabling failed, please check the network card support or driver status."
            continue
        fi
        echo "RoCEv2 enabled successfully, RDMA device ($RDMA_NAME) has been created."
    fi
    # Execute PFC, ECN, QoS configuration
    echo "Configuring NIC PFC, ECN, QoS parameters..."
    mlnx_qos -i $i --pfc '{{ pfc_param }}'             # Enable flow control for queue 3
    mlnx_qos -i $i --trust dscp                        # Set priority trust status to DSCP
    mlnx_qos -i $i --dscp2prio set,{{ dscp_param }}        #Map DSCP value 30 to priority queue 3
    echo {{ cnp_802p_prio }} > /sys/class/net/$i/ecn/roce_np/cnp_802p_prio   # Based on link-level flow control 3
    echo {{ roce_np }} > /sys/class/net/$i/ecn/roce_np/cnp_dscp              # Configure CNP_dscp message value
    echo "NIC $i configuration completed!"
done
