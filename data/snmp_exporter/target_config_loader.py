class TargetConfigLoader(object):
    def __init__(self, target_ip, target_username, target_password, target_port=22):
        self.target_ip = target_ip
        self.target_username = target_username
        self.target_password = target_password
        self.target_port = target_port

    def load_config(self):
        print(f"Loading configuration for {self.target_ip} as {self.target_username} on port {self.target_port}")
        return {
            "ip": self.target_ip,
            "username": self.target_username,
            "port": self.target_port
        }