import re

OID_CONFIG = {
        "system_description": "*******.*******.0",
        "system_name": "*******.*******.0",
        "version": "*******.********.********.1",
        "serial_number": "*******.********.********.1",
        "model": "*******.********.********.1",
        "if_index": "*******.*******.1.1",
        "if_name": "*******.*******.1.2",
        "if_oper_status": "*******.*******.1.8",
        "if_speed": "*******.*******.1.5",
        "if_phys_address": "*******.*******.1.6",  # MAC address
        "if_in_errors": "*******.*******.1.14",  # Input error packets
        "if_in_discards": "*******.*******.1.13",  # Input discarded packets
        "if_out_errors": "*******.*******.1.20",  # Output error packets
        "if_out_discards": "*******.*******.1.19",  # Output discarded packets
        "if_hc_in_octets": "*******.********.1.1.6",  # Input bytes (high capacity counter)
        "if_hc_out_octets": "*******.2.1.31.********"  # Output bytes (high capacity counter)
    }

RJ_OID_CONFIG = {
}

SK_OID_CONFIG = {
}

HH_OID_CONFIG = {
    "S3400C-24MG4S": {
        "version": "*******.********.********.67108992",
        "serial_number": "*******.********.********.67108992",
    }
}

BD_OID_CONFIG = {
    "S5500-48T6SP-R": {
        "version": "*******.4.1.52642.********.6.0",
        "serial_number": "*******.4.1.52642.*********.0",
    }
}


MODEL_VENDOR_MAPPING = {
        "S3100-8TMS-P": "RJ",
        "S3100-16TMS-P": "RJ",
        "S3100-16TF": "RJ",
        "S3100-16TF-P": "RJ",
        "S3270-10TM": "RJ",
        "S3270-10TM-P": "RJ",
        "S3270-24TM": "RJ",
        "S3270-24TM-P": "RJ",
        "S3270-48TM": "RJ",
        "S3410-24TS": "RJ",
        "S3410-24TS-P": "RJ",
        "S3410-48TS": "RJ",
        "S3410-48TS-P": "RJ",
        "S3410-24TF-P": "RJ",
        "S3910-24TS": "RJ",
        "S3910-24TF": "RJ",
        "S3910-48TF": "RJ",
        "S3910-48TS": "RJ",
        "S5810-48FS": "RJ",
        "S5810-28FS": "RJ",
        "S5810-28TS": "RJ",
        "S5810-48TS": "RJ",
        "S5810-48TS-P": "RJ",
        "S5860-20SQ": "RJ",
        "S5860-48SC": "RJ",
        "S5860-24XMG": "RJ",
        "S5860-24MG-U": "RJ",
        "S5860-48XMG": "RJ",
        "S5860-48XMG-U": "RJ",
        "S5860-48MG-U": "RJ",
        "S5860-24XB-U": "RJ",
        "SC9405": "RJ",
        
        "S3950-4T12S-R": "SK",
        "S5800-24T8S": "SK",
        "S5800-48T4S": "SK",
        "S5800-48F4SR": "SK",
        "S5850-24S2Q": "SK",
        "S5850-48B8C": "SK",
        "S5850-16T16BS2Q": "SK",
        "S5850-48S6Q-R": "SK",
        "S5850-24B4C": "SK",
        "S5850-24S2C": "SK",
        "S5850-48S8C": "SK",
        "S8550-6Q2C": "SK",
        "S8550-32C": "SK",
        "S8550-16Q8C": "SK",
        
        "S3200-8MG4S-U": "HH",
        "S3400C-24MG4S": "HH",
        "S3400C-24MG4S-P": "HH",
        "S5800-48MBQ": "HH",
        "S5850-48T4Q": "HH",
        "S5850-24XMG": "HH",
        "S5850-24XMG-U": "HH",
        "S5850-48XMG8C": "HH",
        "S5850C-12XMS2C": "HH",
        "S5850C-24S2C": "HH",
        "S5850C-24XMG2C": "HH",
        
        "S3150-8T2F": "BD",
        "S3150-8T2FP": "BD",
        "S3260-8T2FP": "BD",
        "S3260-16T4FP": "BD",
        "S3400-24T4FP": "BD",
        "S3400-24T4SP": "BD",
        "S3400-48T6SP": "BD",
        "S3700-24T4F": "BD",
        "S3900-48T6S-R": "BD",
        "S3900-24F4S-R": "BD",
        "S3900-24T4S-R": "BD",
        "S5300-12S": "BD",
        "S5500-48T6SP-R": "BD",
        "S5500-24TF6S": "BD",
        "S5500-48T6S": "BD",
        "S5500-48F6S": "BD",
        "IES3100-8TF": "BD",
        "IES3100-8TF-P": "BD",
        "IES3100-16TM": "BD",
        "IES5100-24TF": "BD",
        "IES5100-16TS": "BD",
        "IES5100-24FS": "BD",
        "IES5100-24TS-P": "BD",
        
        "IES3220-8T4F": "HS",
        "IES3220-8T4F-U": "HS",
        "IES3220-4T2F": "HS",
        "IES2100-8FE": "HS",
        "IES2100-5FE": "HS",
        "IES5120-28TS": "HS",
        "IES5120-28TS-P": "HS",
        "IES5120-48T4S": "HS",
        "IES5120-28TF": "HS",
        "IES5220-6S-3ES": "HS",
        "IES5220-30TS-3ES": "HS",
        "TSN3220-10S-U": "HS",
        "TSN3220-10S": "HS",
        
        "IES5110-20FMS": "PL"
    }

BRACKET_PATTERN = r"\(([^()]+)\)"
MODEL_PATTERN = r"\b([A-Za-z]+\d+[A-Za-z]*(-[A-Za-z\d]+)*)\b"
NUM_IGNORE_UNIT_PATTERN = re.compile(r'^[+-]?\d*\.?\d+')

MODEL_MAX_OIDS_MAPPING = {
    "S3410-48TS-P": 20
}