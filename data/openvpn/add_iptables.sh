#!/bin/sh

# Function to check if the URL returns HTTP status code 200
check_url() {
    http_status=$(curl -k --output /dev/null --silent --head -w "%{http_code}" "$1")
    [ "$http_status" -eq 200 ]
}

while true; do
    NGINX_CONTAINER_IP=$(getent hosts nginx-service | awk '{ print $1 }')
    VPN_CONTAINER_IP=$(getent hosts openvpn-service | awk '{ print $1 }')
    LOGIN_URL="https://$NGINX_CONTAINER_IP/login"
    if check_url "$LOGIN_URL"; then
        iptables -t nat -A POSTROUTING -o tun0 -j MASQUERADE
        iptables -t nat -A PREROUTING -p tcp --dport 443 -j DNAT --to-destination "$NGINX_CONTAINER_IP":443
        iptables -t nat -A POSTROUTING -p tcp -d "$NGINX_CONTAINER_IP" --dport 443 -j SNAT --to-source "$VPN_CONTAINER_IP"
        echo "iptables rules add success"
        break
    fi
    echo "wait for nginx-service start up ..."
    sleep 5
done