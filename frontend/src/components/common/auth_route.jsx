import {useDispatch} from "react-redux";
import {checkStatus} from "@/store/modules/common/user_slice";
import {useEffect, useState} from "react";

export function AuthRoute({children}) {
    const [isCheckStatusDone, setIsCheckStatusDone] = useState(false);
    const dispatch = useDispatch();

    useEffect(() => {
        const fetchStatus = async () => {
            await dispatch(checkStatus());
            setIsCheckStatusDone(true);
        };

        fetchStatus();
    }, [dispatch]);

    if (!isCheckStatusDone) {
        return null; // 或者你可以在这里返回一个加载指示器
    }

    return children;
}
