import {DatePicker, Input} from "antd";
import dayjs from "dayjs";
import {formatUnitsMap} from "@/modules-otn/utils/util";

const DisableInput = ({config: {type, units, length}, value}) => {
    const formatUnits = formatUnitsMap[units] ?? units;
    if (type === "timeticks64") {
        return (
            <DatePicker
                format={() => {
                    return dayjs(parseInt(value) / 1000000).format();
                }}
                defaultValue={dayjs()}
                style={{width: 280}}
                showTime
                disabled
            />
        );
    }

    if (length?.split("..").pop() > 20) {
        return <Input.TextArea disabled addonAfter={formatUnits} value={value} style={{width: 280}} />;
    }

    return <Input disabled addonAfter={formatUnits} value={value} style={{width: 280}} />;
};

export default DisableInput;
