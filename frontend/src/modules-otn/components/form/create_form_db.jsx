import {ConfigProvider, Form, message} from "antd";
import {useEffect, useState} from "react";
import enUS from "antd/locale/en_US";
import zhCN from "antd/locale/zh_CN";
import {TableConfig} from "@/modules-otn/config/table_config";
import {objectAdd, objectGet} from "@/modules-otn/apis/api";
import {gLabelList, gLanguage} from "@/store/modules/otn/languageOTNSlice";
import EditInputDB from "@/modules-otn/components/form/edit_input_db";
import {convertToArray, getText} from "@/modules-otn/utils/util";

import styles from "./create_form.module.scss";
import {middleModal} from "../modal/custom_modal";

export const CreateDBForm = ({type, setForm, onCancel, processFail, msg, success, fail, submit, initData}) => {
    const [form] = Form.useForm();
    const [allData, setAllData] = useState({});
    const [defaultValue, setDefaultValue] = useState({});
    const config = TableConfig[type];
    const itemShow = {};
    useEffect(() => {
        const _data = {};
        const _default = {...defaultValue};
        config.columns.forEach(item => {
            if (item.options) {
                _data[item.dataIndex] = item.options;
            }
            if (item.defaultValue) {
                form.setFieldValue(item.dataIndex, item.defaultValue);
                _default[item.dataIndex] = item.defaultValue;
            }
        });
        if (initData) {
            Object.keys(initData).map(key => {
                form.setFieldValue(key, initData[key].value);
            });
        }
        setDefaultValue(_default);
    }, [initData]);
    if (!config) {
        message.error(`can not found the type: ${type}`).then();
        return;
    }
    const {dbKey, columns, addAPI, height} = config;

    setForm?.(form);

    let saved = false;

    const handleSubmit = async values => {
        if (saved) return;

        try {
            saved = true;
            const diffValue = {...values};
            Object.keys(values).forEach(_key => {
                if (!itemShow[_key]) {
                    delete values[_key];
                    delete diffValue[_key];
                }
            });

            Object.keys(defaultValue).forEach(_key => {
                if (defaultValue[_key] === values[_key]) {
                    delete diffValue[_key];
                }
            });
            let uniqCheck = true;
            const uniqList = config.columns.filter(item => item.uniq);
            await Promise.all(
                uniqList.map(async item => {
                    await objectGet(type, {[item.dataIndex]: values[item.dataIndex]}).then(rs => {
                        if (rs.total > 0) {
                            message.error(
                                `${gLabelList[item.title] ?? item.title ?? gLabelList[item.dataIndex]} ${
                                    values[item.dataIndex]
                                }${gLabelList.exists}`
                            );
                            uniqCheck = false;
                        }
                    });
                })
            );
            if (!uniqCheck) {
                processFail();
                saved = false;
                return;
            }
            if (submit) {
                let toContinue = false;
                await submit(
                    values,
                    defaultValue,
                    diffValue,
                    onCancel,
                    rs => {
                        processFail(rs);
                        saved = false;
                    },
                    (toContinueValue = false) => {
                        toContinue = toContinueValue;
                    }
                );
                if (!toContinue) {
                    return;
                }
            }
            if (addAPI) {
                addAPI.API(values).then(rs => {
                    if (rs.apiResult !== "fail") {
                        if (msg) {
                            if (config.addAPI.successMessage) {
                                message.success(gLabelList[config.addAPI.successMessage]);
                            } else {
                                message.success(gLabelList.save_success);
                            }
                        }
                        if (success) success({...rs, formValue: values});
                        onCancel?.(rs);
                        saved = false;
                    } else {
                        if (msg) {
                            if (config.addAPI.failMessage) {
                                message.error(gLabelList[config.addAPI.failMessage]);
                            } else {
                                message.error(gLabelList.save_failed);
                            }
                        }
                        if (fail) fail(rs);
                        processFail();
                        saved = false;
                    }
                });
            } else {
                const save = {
                    entity: type,
                    data: values,
                    msg,
                    success: rs => {
                        success?.(rs);
                        onCancel?.(rs);
                        saved = false;
                    },
                    fail: rs => {
                        processFail?.();
                        fail?.(rs);
                        saved = false;
                    }
                };
                if (dbKey) {
                    save.DBKey = dbKey(values);
                }
                await objectAdd(save);
            }
        } catch (e) {
            message.error(e);
            saved = false;
        }
    };

    const resetDefaultValue = v => {
        if (v) {
            const _default = {...defaultValue};
            Object.keys(v).forEach(_key => {
                if (columns.filter(item => item.dataIndex === _key).length > 0) {
                    if (v[_key] === null) {
                        form.resetFields([_key]);
                        delete _default[_key];
                    } else {
                        form.setFieldValue(_key, v[_key]);
                        _default[_key] = v[_key];
                    }
                }
            });
            setDefaultValue(_default);
        }
    };

    // tableConfig的resetConfig字段决定了当表单对应的值改变时其他依赖值清空，如{name: ["test1", "test2"]}即当name的值变化时清空掉test1和test2的值
    const onValuesChange = changedValues => {
        if (config.resetConfig) {
            Object.entries(config.resetConfig).forEach(([key, value]) => {
                if (changedValues?.[key]) {
                    const resetValue = Object.fromEntries(value.map(item => [item, undefined]));
                    form.setFieldsValue(resetValue);
                }
            });
        }
    };
    let labelMuxSize = 0;
    columns.map(item => {
        if (item?.create?.show === false) {
            return;
        }
        const label =
            gLabelList[item.title] ?? gLabelList[item.dataIndex] ?? getText(item.title) ?? getText(item.dataIndex);
        labelMuxSize = label.length > labelMuxSize ? label.length : labelMuxSize;
    });
    let labelColSpan = Math.ceil(labelMuxSize / 3);
    if (labelColSpan < 3) labelColSpan = 3;
    return (
        <ConfigProvider locale={gLanguage === "en" ? enUS : zhCN}>
            <>
                {/* <div className={styles.create_form_header} style={headerStyle} /> */}
                <div className={styles.create_form_content} style={{margin: 0}}>
                    <Form
                        labelCol={{span: 8}}
                        wrapperCol={{span: 14}}
                        labelAlign="left"
                        layout="horizontal"
                        form={form}
                        onFinish={handleSubmit}
                        className={["label-wrap", height || styles.create_form]}
                        style={{overflow: "auto"}}
                        onValuesChange={onValuesChange}
                    >
                        {columns.map(item => {
                            if (item?.create?.show === false) {
                                return;
                            }
                            const whenValue = !(item.when && !item.when(allData));
                            itemShow[item.dataIndex] = whenValue;
                            if (!item.inputType) {
                                item.inputType = "text";
                            }
                            const rules = [];
                            if (whenValue) {
                                if (item.required) {
                                    if (typeof item.required === "function") {
                                        if (item.required(allData, form.getFieldsValue())) {
                                            rules.push({required: true, message: gLabelList.required});
                                        }
                                    } else {
                                        rules.push({required: true, message: gLabelList.required});
                                    }
                                }
                                if (item.pattern) {
                                    const p = {pattern: item.pattern};
                                    if (item.message) {
                                        p.message = gLabelList[item.message] ?? item.message;
                                    }
                                    rules.push(p);
                                }
                                if (item.selectLimit) {
                                    const numberLimit = item.selectLimit(allData, form?.getFieldsValue());
                                    if (numberLimit) {
                                        const p = {
                                            validator: (r, v) => {
                                                const len = convertToArray(v).reduce((c, p) => {
                                                    let _p = p;
                                                    let l;
                                                    if (p instanceof Array) {
                                                        l = "1";
                                                    } else {
                                                        if (typeof p !== "string" && typeof p !== "number") {
                                                            _p = p.value;
                                                        }
                                                        l = _p
                                                            .toString()
                                                            .match(/\(\d+\)/g)?.[0]
                                                            ?.replace(/\(|\)/g, "");
                                                    }
                                                    if (l) {
                                                        c += parseInt(l);
                                                    } else {
                                                        c++;
                                                    }
                                                    return c;
                                                }, 0);
                                                // console.log("len", len, numberLimit);
                                                if (numberLimit instanceof Array) {
                                                    if (len < numberLimit[0] || len > numberLimit[1]) {
                                                        return Promise.reject(
                                                            new Error(
                                                                `Please select ${convertToArray(numberLimit).join("~")} items.`
                                                            )
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                }
                                                if (len !== numberLimit) {
                                                    return Promise.reject(
                                                        new Error(
                                                            `Please select ${convertToArray(numberLimit).join("~")} items.`
                                                        )
                                                    );
                                                }
                                                return Promise.resolve();
                                            }
                                        };
                                        rules.push(p);
                                    }
                                }
                            }
                            return (
                                <Form.Item
                                    style={{width: "100%", display: !whenValue ? "none" : ""}}
                                    key={item.dataIndex}
                                    label={
                                        gLabelList[item.title] ??
                                        gLabelList[item.dataIndex] ??
                                        getText(item.title) ??
                                        getText(item.dataIndex)
                                    }
                                    tooltip={item?.description}
                                    name={item.dataIndex}
                                    rules={rules}
                                >
                                    {EditInputDB({
                                        config: item,
                                        formType: "create",
                                        allData,
                                        setAllData,
                                        resetDefaultValue,
                                        initData: initData?.[item.dataIndex],
                                        form
                                    })}
                                </Form.Item>
                            );
                        })}
                    </Form>
                </div>
            </>
        </ConfigProvider>
    );
};

export const openDBModalCreate = ({
    type,
    title,
    msg = true,
    success,
    fail,
    submit,
    okText,
    initData,
    cancelEnable = true
}) => {
    const config = TableConfig[type];
    if (!config) {
        message.error(`can not found the type: ${type}`).then();
        return;
    }
    let form;
    const handle = fun => {
        form = fun;
    };

    let saved = false;

    const afterFinish = success => {
        saved = false;
        if (success) {
            modal.destroy();
        } else {
            modal.update({okButtonProps: {loading: false}});
        }
    };

    const processFail = () => {
        saved = false;
        modal.update({okButtonProps: {loading: false}});
        setTimeout(() => {
            saved = false;
        }, 1000);
    };

    const modal = middleModal({
        title: title || gLabelList[config?.title] || getText(config?.title) || getText(type),
        okText: okText ?? gLabelList.applyText,
        closable: cancelEnable,
        ...(cancelEnable ? {} : {cancelText: " ", cancelButtonProps: {style: {display: "none"}}}),
        // eslint-disable-next-line no-unused-vars
        onOk: _ => {
            form.validateFields()
                .then(() => {
                    if (saved) return;

                    saved = true;
                    modal.update({okButtonProps: {loading: true}});
                    form.submit();
                })
                .catch(() => {});
        },
        content: (
            <CreateDBForm
                processFail={processFail}
                onCancel={afterFinish}
                setForm={handle}
                type={type}
                success={success}
                fail={fail}
                msg={msg}
                submit={submit}
                initData={initData}
            />
        )
    });
};
