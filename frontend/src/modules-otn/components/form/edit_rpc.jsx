import {Form, Input, message, Tabs} from "antd";
import {useState} from "react";
import {getAttrValue, getText, removeJsonNS, rootModal} from "@/modules-otn/utils/util";
import YANG_CONFIG from "@/modules-otn/config/yang_config";
import styles from "@/modules-otn/components/form/edit_form.module.scss";
import {apiRpc, getYangRpc} from "@/modules-otn/apis/api";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import EditInput from "./edit_input";
import {middleModal, smallModal} from "../modal/custom_modal";

/**
 * rpcName: RPC name
 * input: RPC input
 * initVals: Initial Values like {name: "1-1", type: "upgrade"}
 * readOnly: Array of readOnly field name like ["name"]
 * setForm: Callback for passing form instance to parent
 * afterFinish: Callback after Finish
 */
const FormRpc = ({rpcName, afterFinish, input, initVals, readOnly, setForm, ne_id, objectName, submitFunc}) => {
    const [form] = Form.useForm();
    setForm(form);
    const [datas, setDatas] = useState({});

    const afterCommitted = (result, msg, data, apiResult, apiMessage, response) => {
        let success = false;
        if (apiResult === 0) {
            message.success(gLabelList.save_success).then();
            success = true;
        } else if (response) {
            if (!response.result) {
                message.error(gLabelList.save_failed).then();
            } else {
                message.success(gLabelList.save_success).then();
                success = true;
            }
        } else if (apiResult === "fail" || msg === "FAIL" || apiResult === -1) {
            message.error(gLabelList.operation_fail).then();
        } else if (!result) {
            message
                .error({
                    content: gLabelList.operation_fail,
                    key: "error1",
                    duration: 0,
                    onClick: () => message.destroy("error1")
                })
                .then();
        } else if (
            getAttrValue(
                data?.["rpc-reply"],
                rpcName === "get-oduk-delay" ? "get-delay-result" : `${rpcName}-result`
            ) === "FAIL"
        ) {
            message
                .error({
                    content: gLabelList.operation_fail,
                    key: "error2",
                    duration: 0,
                    onClick: () => message.destroy("error2")
                })
                .then();
        } else if (rpcName === "get-oduk-delay") {
            // get-oduk-delay
            const _data = data.value.data["delay-data-value"];
            rootModal.info({
                title: "DM",
                okText: gLabelList.ok,
                content: (
                    <table border={1} width="100%">
                        <tr>
                            <th>{gLabelList.oduk_avg}</th>
                            <th>{gLabelList.oduk_max}</th>
                            <th>{gLabelList.oduk_min}</th>
                        </tr>
                        <tr style={{textAlign: "center"}}>
                            <td>{_data.avg} us</td>
                            <td>{_data.max} us</td>
                            <td>{_data.min} us</td>
                        </tr>
                    </table>
                )
            });
            success = true;
        } else {
            // todo: Parse result for get-oduk-delay
            const _content = (
                <span>
                    <div>{gLabelList.operation_success}</div>
                    <div>{removeJsonNS(data)}</div>
                </span>
            );
            message
                .success({
                    content: _content,
                    maxCount: 1
                })
                .then();
            success = true;
        }
        if (typeof afterFinish === "function") {
            afterFinish(success);
        }
    };

    const onFinish = values => {
        if (submitFunc) {
            submitFunc(values).then(({result, message: msg, data, apiResult, apiMessage, response}) => {
                afterCommitted(result, msg, data, apiResult, apiMessage, response);
            });
            return;
        }
        const requestObj = {};
        Object.entries(values).map(([key, val]) => {
            const temp = key.split("&");
            if (temp.length === 0) {
                requestObj[key] = val;
            } else {
                let _obj = requestObj;
                temp.map((i, index) => {
                    if (!_obj[i]) {
                        _obj[i] = index === temp.length - 1 ? val : {};
                    }
                    _obj = _obj[i];
                });
            }
        });
        apiRpc({ne_id, rpcName, rpcConfig: requestObj}).then(({result, message: msg, data, apiResult, apiMessage}) => {
            afterCommitted(result, msg, data, apiResult, apiMessage);
        });
    };

    // let _input = {...input};
    // if (Object.keys(_input).length === 1 && !Object.values(_input)[0].type) {
    //     _input = Object.values(_input)[0];
    // }
    // delete _input.definition;
    const userDefine = YANG_CONFIG[rpcName];
    const parseYang = (_yang, parentKey) => {
        return Object.entries(_yang).map(([key, val]) => {
            if (typeof val === "string") {
                return;
            }
            if (!val.yangType && !val.type) {
                return (
                    <Tabs key={`${parentKey ? `${parentKey}&${key}` : key}tab`} className={styles.edit_tab}>
                        <Tabs.TabPane
                            key={`${key}tabpanel`}
                            tab={getText(key).toUpperCase()}
                            className={styles.edit_tab_pane}
                        >
                            {parseYang(val, parentKey ? `${parentKey}&${key}` : key)}
                        </Tabs.TabPane>
                    </Tabs>
                );
            }
            if (key !== "definition") {
                const _key = parentKey ? `${parentKey}&${key}` : key;
                const rules = [];
                if (userDefine?.[key]?.required) {
                    rules.push({required: true, message: gLabelList.required});
                }
                if (userDefine?.[key]?.pattern) {
                    const p = {pattern: userDefine[key].pattern};
                    if (userDefine?.[key]?.message) {
                        p.message = gLabelList[userDefine?.[key]?.message] ?? userDefine?.[key]?.message;
                    }
                    rules.push(p);
                }
                if (userDefine?.[key]?.when && !userDefine[key].when(form.getFieldsValue())) {
                    return;
                }
                return (
                    <Form.Item
                        key={key}
                        name={_key}
                        label={getText(key)}
                        tooltip={val.description}
                        initialValue={initVals[key] ?? val.default}
                        rules={rules}
                        labelCol={{span: 8}}
                        wrapperCol={{span: 14}}
                        labelAlign="left"
                        layout="horizontal"
                        LabelWrap
                        className="label-wrap"
                    >
                        {readOnly.includes(key) ? (
                            <Input disabled addonAfter={val.units} style={{width: 280}} />
                        ) : (
                            EditInput({
                                config: val,
                                key: _key,
                                form,
                                userDefine: userDefine?.[key],
                                parameter: {ne_id, objectName},
                                datas,
                                setDatas,
                                initVals
                            })
                        )}
                    </Form.Item>
                );
            }
        });
    };

    return (
        <>
            {/* <div className={styles.edit_form_header} /> */}
            <div className={styles.edit_form_content} style={{margin: 0}}>
                <Form
                    form={form}
                    onFinish={onFinish}
                    wrapperCol={{span: 16}}
                    labelAlign="left"
                    className={styles.edit_form}
                >
                    {parseYang(input)}
                </Form>
            </div>
        </>
    );
};

const openModalRpc = (
    ne_id,
    rpcName,
    type,
    title = getText(rpcName),
    initVals = {},
    readOnlyPars = [],
    callbackFunc = null,
    warningConfig = null,
    submitFunc = null,
    readOnly = false
) => {
    getYangRpc(rpcName, type).then(rs => {
        let form;
        // eslint-disable-next-line no-return-assign
        const handle = f => (form = f);
        let commitValues;
        const afterFinish = success => {
            if (success) {
                callbackFunc?.(commitValues);
                modal.destroy();
            } else {
                modal.update({okButtonProps: {loading: false}});
            }
        };
        const commit = () => {
            modal.update({okButtonProps: {loading: true}});
            form.submit();
        };

        const modal = middleModal({
            title,
            okButtonProps: {
                disabled: readOnly
            },
            // eslint-disable-next-line no-unused-vars
            onOk: _ => {
                form.validateFields().then(() => {
                    commitValues = form.getFieldsValue();
                    if (warningConfig) {
                        const warning = warningConfig(commitValues);
                        if (warning) {
                            const modal2 = smallModal({
                                // eslint-disable-next-line no-unused-vars
                                onOk: _ => {
                                    modal2.destroy();
                                    commit();
                                },
                                okText: gLabelList.ok,
                                cancelText: gLabelList.cancel,
                                content: warning
                            });
                            return;
                        }
                    }
                    commit();
                });
            },
            okText: gLabelList.ok,
            cancelText: gLabelList.cancel,
            content: (
                <FormRpc
                    setForm={handle}
                    afterFinish={afterFinish}
                    rpcName={rpcName}
                    initVals={initVals}
                    input={rs.input}
                    readOnly={readOnlyPars}
                    ne_id={ne_id}
                    objectName={title}
                    submitFunc={submitFunc}
                />
            )
        });
    });
};

export {openModalRpc, FormRpc};
