import {useState} from "react";
import {Button, Form, message, Select} from "antd";
import {g<PERSON>abelList} from "@/store/modules/otn/languageOTNSlice";
import {MAX_FREQ_PARAM, MIN_FREQ_PARAM, openModalFrequency} from "@/modules-otn/components/common/frequency";
import YANG_CONFIG from "@/modules-otn/config/yang_config";
import {convertToArray, getText, getValueByJPath} from "@/modules-otn/utils/util";
import {LeafRefs} from "@/modules-otn/config/leafref";
import {apiEditRpc, apiGetCategory, apiGetYang, netconfGetByXMLWithXPath} from "@/modules-otn/apis/api";
import styles from "./create_form.module.scss";
import EditInput from "./edit_input";
import {bigModal} from "../modal/custom_modal";

/**
 *
 * @param setForm
 * @param Yang
 * @param category
 * @param ne_id
 * @param initValue
 * @param operationType
 * @param categoryName
 * @param onCancel
 * @param callback
 * @param keys
 * @param type
 * @returns {JSX.Element|string}
 * @constructor
 */
export const CreateForm = ({
    setForm,
    Yang,
    category,
    onCancel,
    callback,
    keys,
    ne_id,
    initValue = {},
    operationType,
    categoryName,
    type
}) => {
    const [form] = Form.useForm();
    const [options, setOptions] = useState([]);
    const [datas, setDatas] = useState({});
    const [buttonLoading, setButtonLoading] = useState(false);
    const rootPath = category.configRootPath ?? category.rootPath;
    const yang = getValueByJPath(Yang, rootPath);
    if (!yang) return "";
    const useDefine = YANG_CONFIG[categoryName];
    const yangObj = yang.config ?? yang;
    setForm?.(form);

    // const neNeed = true;

    keys = convertToArray(keys);

    const handleSubmit = async () => {
        const values = await form.getFieldsValue();
        const changedValues = {};
        // eslint-disable-next-line no-restricted-syntax
        for (const i in values) {
            if (values[i] !== undefined) {
                changedValues[i] = values[i];
            } else if (yangObj[i]?.mandatory) {
                changedValues[i] = "";
            }
        }
        if (Object.keys(changedValues).length === 0) {
            message.info("No change");
        } else {
            const request = {};
            const _keys = [...keys];
            rootPath.reduce(
                // eslint-disable-next-line no-return-assign
                (p, c) => [
                    (p[0][c] = Object.fromEntries(
                        p[1][c].definition.key ? p[1][c].definition.key.split(" ").map(k => [k, _keys.shift()]) : []
                    )),
                    p[1][c]
                ],
                [request, Yang]
            );
            let obj = null;
            // const neID = changedValues.ne_id;
            // delete changedValues.ne_id;
            // eslint-disable-next-line no-return-assign
            rootPath.reduce((p, c) => (obj = p[c]), request);
            if (operationType !== "edit") {
                obj["@nc:operation"] = "create";
            }
            yang.definition.key?.split(" ").forEach(k => {
                obj[k] = changedValues[k];
            });
            if (yang.config) {
                obj.config = {};
                obj = obj.config;
            }
            // eslint-disable-next-line guard-for-in,no-restricted-syntax
            for (const i in changedValues) {
                obj[i] = `${changedValues[i]}`;
            }

            await apiEditRpc({
                ne_id,
                params: request,
                success: () => {
                    onCancel(true);
                    callback();
                },
                fail: () => {
                    onCancel(false);
                }
            });
        }
    };

    const getUsedFrequency = async () => {
        const request = {};
        const _keys = [...keys];
        rootPath.reduce(
            // eslint-disable-next-line no-return-assign
            (p, c) => [
                (p[0][c] = Object.fromEntries(
                    p[1][c].definition.key ? p[1][c].definition.key.split(" ").map(k => [k, _keys.shift()]) : []
                )),
                p[1][c]
            ],
            [request, Yang]
        );
        let obj = null;
        // eslint-disable-next-line no-return-assign
        rootPath.reduce((p, c) => (obj = p[c]), request);
        obj["min-edge-freq"] = "";
        obj["max-edge-freq"] = "";
        const response = await netconfGetByXMLWithXPath({ne_id, xml: request, type});
        if (response) {
            const data = convertToArray(getValueByJPath(response, rootPath));
            return data.map(item => {
                return {min: item["min-edge-freq"], max: item["max-edge-freq"]};
            });
        }
        return [];
    };

    const popupFrequency = async () => {
        try {
            setButtonLoading(true);
            let current = null;
            if (form.getFieldValue(MIN_FREQ_PARAM) && form.getFieldValue(MAX_FREQ_PARAM)) {
                current = {min: form.getFieldValue(MIN_FREQ_PARAM), max: form.getFieldValue(MAX_FREQ_PARAM)};
            }
            openModalFrequency(
                v => {
                    if (v) {
                        form.setFieldsValue({...form.getFieldsValue(), ...v});
                    } else {
                        const newValues = {...form.getFieldsValue()};
                        newValues[MIN_FREQ_PARAM] = "";
                        newValues[MAX_FREQ_PARAM] = "";
                        form.setFieldsValue(newValues);
                    }
                },
                async () => {
                    const usedList = await getUsedFrequency();
                    return usedList;
                },
                current
            );
        } catch (e) {
            // console.log(e);
        } finally {
            setButtonLoading(false);
        }
    };
    return (
        <Form
            labelCol={{span: 6}}
            wrapperCol={{span: 18}}
            form={form}
            initialValues={initValue}
            className={styles.create_form}
            onFinish={handleSubmit}
            labelAlign="left"
        >
            {Object.entries(yangObj)
                .filter(([, val]) => val.config !== "false")
                .map(([key]) => {
                    if (key !== "definition" && yangObj[key]?.type) {
                        if (useDefine?.[key]?.when) {
                            if (!useDefine?.[key].when(form.getFieldsValue())) {
                                return;
                            }
                        }
                        const label = getText(key);
                        const rules = [];
                        if (yang.definition?.key?.split(" ").some(k => k === key)) rules.push({required: true});
                        if (useDefine?.[key]?.required && !rules.find(item => item.required !== undefined))
                            rules.push({required: true});
                        if (yangObj[key]?.pattern) {
                            rules.push({pattern: yangObj[key].pattern});
                        }
                        if (yangObj[key].type === "enumeration" && yangObj[key]?.mandatory) {
                            rules.push({required: true});
                        }

                        if (key === MIN_FREQ_PARAM) {
                            return (
                                <Form.Item
                                    style={{marginBottom: 10, width: "100%"}}
                                    key={label}
                                    label={label}
                                    tooltip={yangObj[key]?.description}
                                >
                                    <div style={{display: "flex"}}>
                                        <Form.Item
                                            style={{display: "inline-block", marginBottom: 0, width: "auto"}}
                                            tooltip={yangObj[key]?.description}
                                            name={key}
                                            rules={rules}
                                        >
                                            {EditInput({
                                                key,
                                                form,
                                                config: yangObj[key],
                                                useDefine: useDefine?.[key],
                                                datas,
                                                setDatas,
                                                parameter: {ne_id, keys}
                                            })}
                                        </Form.Item>
                                        <Form.Item style={{display: "inline-block", marginBottom: 0, marginLeft: 16}}>
                                            <Button
                                                disabled={buttonLoading}
                                                onClick={popupFrequency}
                                                style={{width: "auto"}}
                                            >
                                                Wavelength Selection
                                            </Button>
                                        </Form.Item>
                                    </div>
                                </Form.Item>
                            );
                        }
                        const temp = getValueByJPath(LeafRefs, [...category.rootPath]);
                        const leafRef = temp?.config?.[key] ?? temp?.[key];
                        return (
                            <Form.Item
                                style={{width: "100%"}}
                                key={label}
                                label={label}
                                tooltip={yangObj[key]?.description}
                                name={key}
                                rules={rules}
                            >
                                {leafRef ? (
                                    <Select
                                        notFoundContent={gLabelList.loading}
                                        onDropdownVisibleChange={async open => {
                                            if (open) {
                                                const values = await leafRef.getList?.({form, keys});
                                                setOptions(values);
                                            } else {
                                                setOptions([]);
                                            }
                                        }}
                                        options={options.map(op => {
                                            if (typeof op === "string") {
                                                return {
                                                    label: op,
                                                    value: op
                                                };
                                            }
                                            return {label: op.label, value: op.value};
                                        })}
                                    />
                                ) : (
                                    EditInput({
                                        key,
                                        form,
                                        config: yangObj[key],
                                        useDefine: useDefine?.[key],
                                        datas,
                                        setDatas,
                                        parameter: {ne_id, keys}
                                    })
                                )}
                            </Form.Item>
                        );
                    }
                })}
        </Form>
    );
};

export const openModalCreate = ({categoryName, type, title, keys, ne_id, callback, initValue = {}, operationType}) => {
    apiGetCategory(categoryName, type).then(categoryRs => {
        apiGetYang(type).then(yang => {
            // objectGet("config:ne", {}).then(neRs => {
            let form;
            // eslint-disable-next-line no-return-assign
            const handle = f => (form = f);

            const afterFinish = success => {
                if (success) {
                    modal.destroy();
                } else {
                    modal.update({okButtonProps: {loading: false}});
                }
            };
            const modal = bigModal({
                title,
                // eslint-disable-next-line no-unused-vars
                onOk: _ => {
                    form.validateFields().then(() => {
                        modal.update({okButtonProps: {loading: true}});
                        form.submit();
                    });
                },
                content: (
                    <CreateForm
                        setForm={handle}
                        onCancel={afterFinish}
                        Yang={yang}
                        category={categoryRs}
                        // neList={neRs.documents}
                        categoryName={categoryName}
                        keys={keys}
                        ne_id={ne_id}
                        initValue={initValue}
                        callback={callback}
                        operationType={operationType}
                        type={type}
                    />
                )
            });
            // });
        });
    });
};
