import {Form, message, Tabs} from "antd";
import {useEffect, useState} from "react";
import {isEmpty} from "lodash";
import {TableConfig} from "@/modules-otn/config/table_config";
import {LoadingOutlined} from "@ant-design/icons";
import {objectGet, objectUpdate} from "@/modules-otn/apis/api";
import EditInputDB from "@/modules-otn/components/form/edit_input_db";
import {getText} from "@/modules-otn/utils/util";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import styles from "./edit_form.module.scss";
import {middleModal} from "../modal/custom_modal";

const EditForm = ({type, setForm, config, onCancel, processFail, msg, success, fail, dataSource, loadDataFun}) => {
    const [form] = Form.useForm();
    setForm?.(form);
    const {columns, initData, dynamicColumns} = config;
    const [originalData, SetOriginalData] = useState(null);
    const [originalFormData, setOriginalFormData] = useState({});
    const [loading, setLoading] = useState(false);

    const parseInitFormData = rs => {
        const columnsConfig = [];
        columns.forEach(item => {
            columnsConfig.push({...item});
        });
        let {value} = rs;
        if (dynamicColumns) {
            if (!(value instanceof Array)) {
                return;
            }
            const newValue = {};
            value.forEach(i => {
                Object.entries(i).map(([k, v]) => {
                    Object.entries(v).map(([k1, v1]) => {
                        newValue[k === "" ? k1 : `${k}&${k1}`] = v1;
                    });
                    if (k !== "") {
                        dynamicColumns.forEach(c => {
                            columnsConfig.push({...c, dataIndex: `${k}&${c.dataIndex}`});
                        });
                    }
                });
            });
            value = newValue;
        }
        SetOriginalData(rs);
        if (initData?.path) {
            initData.path.forEach(i => {
                value = value[i];
            });
        }
        const _originalFormData = {};
        columnsConfig.map(item => {
            if (item?.edit?.show === false) {
                return;
            }
            if (item?.edit?.dataIndex) {
                const paths = item.edit.dataIndex.split(".");
                let objValue = structuredClone(value);
                for (let i = 0; i < paths.length; i++) {
                    if (objValue instanceof Array) {
                        const subObj = [];
                        objValue.map(_sub => {
                            subObj.push(_sub[paths[i]]);
                        });
                        objValue = subObj;
                    } else {
                        objValue = objValue[paths[i]];
                    }
                }
                _originalFormData[item.dataIndex] = objValue;
            } else {
                _originalFormData[item.dataIndex] = value[item.dataIndex];
                if (item.dataIndex === "type") {
                    const option = item.edit.options.find(items => items.value === value[item.dataIndex]);
                    _originalFormData[item.dataIndex] = option ? option.label : null;
                }
            }
        });
        setOriginalFormData(_originalFormData);
        form.setFieldsValue(_originalFormData);
    };

    const onFinish = async () => {
        const value = form.getFieldsValue();
        const diffValue = structuredClone(value);
        Object.keys(diffValue).map(k => {
            if (originalFormData[k] === value[k] || (originalFormData[k] === undefined && value[k] === "")) {
                delete diffValue[k];
            }
        });
        if (isEmpty(diffValue)) {
            onCancel?.({});
            return;
        }

        let uniqCheck = true;
        const uniqList = columns.filter(item => item.uniq);
        await Promise.all(
            uniqList.map(async item => {
                if (diffValue[item.dataIndex]) {
                    await objectGet(type, {[item.dataIndex]: value[item.dataIndex]}).then(rs => {
                        if (rs.total > 0) {
                            message.error(`${item.title}:${value[item.dataIndex]} already exists.`);
                            uniqCheck = false;
                        }
                    });
                }
            })
        );
        if (uniqCheck) {
            await objectUpdate({
                key: originalData.id,
                data: diffValue,
                msg,
                success: rs => {
                    success?.(rs);
                    onCancel?.(rs);
                },
                fail: rs => {
                    processFail?.();
                    fail(rs);
                }
            });
        } else {
            processFail();
        }
    };

    const reloadData = () => {
        setLoading(true);
        loadDataFun().then(rs => {
            parseInitFormData(rs);
            setLoading(false);
        });
    };

    useEffect(() => {
        parseInitFormData(dataSource);
    }, []);

    const createColumns = (columnsConfig, key) => {
        const columns = [];
        columnsConfig.forEach(item => {
            if (key) {
                columns.push({...item, dataIndex: `${key}&${item.dataIndex}`});
            } else {
                columns.push({...item});
            }
        });
        return columns.map(item => {
            if (item.edit?.show === false) {
                return;
            }
            const whenValue = !(item.when && !item.when(originalFormData)) ?? true;
            if (!item.inputType) {
                item.inputType = "text";
            }
            const rules = [];
            if (item.edit?.required === false || !whenValue) {
                //
            } else {
                if (item.required) rules.push({required: true});
                if (item.pattern) {
                    const p = {pattern: item.pattern};
                    if (item.message) {
                        p.message = gLabelList[item.message] ?? item.message;
                    }
                    rules.push(p);
                }
            }
            return (
                <Form.Item
                    style={{marginBottom: "10px", width: "100%", display: !whenValue ? "none" : ""}}
                    key={item.dataIndex}
                    label={
                        // gLabelList[item.title] ??
                        // gLabelList[item.dataIndex.split("&").pop()] ??
                        getText(item.title) ?? getText(item.dataIndex.split("&").pop())
                    }
                    tooltip={item?.description}
                    name={item.dataIndex}
                    rules={rules}
                >
                    {EditInputDB({config: item, formType: "edit"})}
                </Form.Item>
            );
        });
    };

    const getColumns = () => {
        if (dynamicColumns) {
            if (!(dataSource?.value instanceof Array)) {
                message.error("Wrong Data Type");
                return [];
            }
            const items = [
                {
                    key: "component",
                    style: {overflow: "auto"},
                    label: "COMPONENT",
                    children: createColumns(columns)
                }
            ];
            dataSource?.value?.forEach(i => {
                Object.entries(i).map(([k]) => {
                    if (k !== "") {
                        items.push({
                            key: k,
                            style: {overflow: "auto"},
                            label: k,
                            children: createColumns(dynamicColumns, k)
                        });
                    }
                });
            });
            return <Tabs items={items} className="radioGroupTabs" />;
        }
        return createColumns(columns);
    };

    return (
        <>
            {config.refresh && (
                <div
                    className={styles.edit_form_header}
                    style={{justifyContent: "end", paddingTop: 8, paddingBottom: 8}}
                >
                    <a
                        key="refresh"
                        style={{color: loading ? "#b9b9b9" : "", cursor: loading ? "not-allowed" : "pointer"}}
                        onClick={() => {
                            if (!loading) {
                                reloadData();
                            }
                        }}
                    >
                        {gLabelList.refresh}
                    </a>
                </div>
            )}
            <div className={styles.edit_form_content} style={{margin: 0}}>
                <Form
                    form={form}
                    onFinish={onFinish}
                    loading
                    className={[styles.edit_form, "label-wrap"]}
                    labelCol={{span: 8}}
                    wrapperCol={{span: 14}}
                    labelAlign="left"
                    layout="horizontal"
                >
                    {getColumns()}
                </Form>
            </div>
        </>
    );
};

export const openDBModalEdit = ({type, title, keys = {}, msg = true, success, fail, loadData}) => {
    const config = {ok: true, cancel: true, ...TableConfig[type]};
    if (!config) {
        message.error(`can not found the type: ${type}`).then();
        return;
    }

    const _title = title || gLabelList[config?.title] || config?.title || type;

    const fetchData = async () => {
        if (loadData) {
            // parseInitFormData();
            const rs = await loadData();
            return {value: rs ?? {}};
        }
        if (config.dataAPI) {
            const rs = await config.dataAPI?.apiName?.(keys);
            return rs?.[0];
        }
        const rs = await objectGet(type, keys);
        return rs?.documents?.[0] ?? {value: {}};
    };

    let form;
    const handle = fun => {
        form = fun;
    };

    const afterFinish = success => {
        if (success) {
            modal.destroy();
        } else {
            modal.update({okButtonProps: {loading: false}});
        }
    };

    const processFail = () => {
        modal.update({okButtonProps: {loading: false}});
    };

    const modal = middleModal({
        title: _title,
        okText: gLabelList.applyText,
        // eslint-disable-next-line no-unused-vars
        onOk: _ => {
            form.validateFields().then(() => {
                modal.update({okButtonProps: {loading: true}});
                form.submit();
            });
        },
        content: (
            <div style={{width: "100%", textAlign: "center"}}>
                <a>
                    <LoadingOutlined style={{fontSize: 32, fill: "#14C9BB", color: "#14C9BB"}} />
                </a>
            </div>
        )
    });

    fetchData().then(rs => {
        if (!modal) {
            return;
        }
        modal.update({
            content: (
                <EditForm
                    processFail={processFail}
                    onCancel={afterFinish}
                    setForm={handle}
                    type={type}
                    keys={keys}
                    config={config}
                    success={success}
                    fail={fail}
                    msg={msg}
                    dataSource={rs}
                    loadDataFun={fetchData}
                />
            )
        });
    });
};
