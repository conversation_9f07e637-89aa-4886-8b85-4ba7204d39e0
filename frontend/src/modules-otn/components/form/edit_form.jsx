import {Button, Form, message, Select, Tabs, Row, Col} from "antd";
import React, {useEffect, useLayoutEffect, useState} from "react";
import {LoadingOutlined, WifiOutlined} from "@ant-design/icons";
import merge from "lodash/merge";
import {max} from "lodash";
import {apiEditRpc, apiGetCategory, apiGetYang, netconfGetByXML} from "@/modules-otn/apis/api";
import {LeafRefs} from "@/modules-otn/config/leafref";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {
    addNS,
    getText,
    getValueByJPath,
    getYangByPath,
    removeNSForObj,
    convertToArray,
    needAscii16FormatKeys,
    needAscii16FormatWithFlagKeys,
    classNames
} from "@/modules-otn/utils/util";
import YANG_CONFIG from "@/modules-otn/config/yang_config";
import AsciiHexAndPlainTextConverter from "@/modules-otn/components/input/asciihex_and_plaintext_converter";
import DisableInput from "./disable_input";
import styles from "./edit_form.module.scss";
import EditInput from "./edit_input";
import {MAX_FREQ_PARAM, MIN_FREQ_PARAM, openModalFrequency} from "../common/frequency";
import {bigModal} from "../modal/custom_modal";

const specialTableName = ["q-value"];

const EditForm = ({
    setForm,
    afterFinish,
    Yang,
    category,
    title,
    refresh,
    initData,
    keys,
    ne_id,
    type,
    categoryName,
    readOnly,
    formPanel = false,
    formatTabName
}) => {
    const [form] = Form.useForm();
    setForm?.(form);
    const [requestConfig, setRequestConfig] = useState({});
    const [initialValues, setInitialValues] = useState(null);
    const [options, setOptions] = useState([]);
    const [selectTab, setSelectTab] = useState();
    const [labelWidth, setLabelWidth] = useState(0);

    const {rootPath, configRootPath, tabs} = category;

    const UpperDef = configRootPath != null;

    const [datas, setDatas] = useState({});
    const useDefine = YANG_CONFIG[categoryName];
    const [buttonLoading, setButtonLoading] = useState(false);
    const [saveLoading, setSaveLoading] = useState(false);
    const parseData = _v => {
        const _data = _v;
        setInitialValues(_data);
        form.setFieldsValue(_data);

        const req = {};
        let _keys = [...keys];
        // eslint-disable-next-line no-return-assign
        const newRequest = (p, c) => [
            (p[0][c] = Object.fromEntries(
                p[1][c].definition.key ? p[1][c].definition.key.split(" ").map(k => [k, _keys.shift()]) : []
            )),
            p[1][c]
        ];
        rootPath.reduce(newRequest, [req, Yang]);
        if (UpperDef) {
            _keys = [...keys];
            const req1 = {};
            configRootPath?.reduce(newRequest, [req1, Yang]);
            setRequestConfig(req1);
        } else {
            setRequestConfig(req);
        }
    };

    const fetchData = () => {
        if (initData) {
            parseData(initData);
        } else {
            let req = {};
            let _keys = [...keys];
            // eslint-disable-next-line no-return-assign
            const newRequest = (p, c) => [
                (p[0][c] = Object.fromEntries(
                    p[1][c].definition.key ? p[1][c].definition.key.split(" ").map(k => [k, _keys.shift()]) : []
                )),
                p[1][c]
            ];
            rootPath.reduce(newRequest, [req, Yang]);
            if (UpperDef) {
                _keys = [...keys];
                const req1 = {};
                configRootPath?.reduce(newRequest, [req1, Yang]);
                req = merge(req, req1);
            }
            netconfGetByXML({ne_id, msg: true, xml: addNS(req, Yang)})
                .then(rs => {
                    parseData(removeNSForObj(rs));
                })
                .catch(e => {
                    // eslint-disable-next-line no-console
                    console.log(e.message);
                    afterFinish?.(true);
                });
        }
    };

    useEffect(() => {
        setInitialValues(null);
        fetchData();
    }, [title, categoryName]);

    const onFinish = async () => {
        const changedValues = form.getFieldsValue(true, meta => {
            return (
                useDefine?.[meta.name?.at(-1)]?.commit ||
                (meta.touched && getValueByJPath(initialValues, meta.name) !== form.getFieldValue(meta.name))
            );
        });
        const changedValuesCopy = structuredClone(changedValues);
        if (Object.keys(changedValues).length === 0) {
            message.info("No change");
            afterFinish?.(false);
            setSaveLoading(false);
        } else {
            await apiEditRpc({
                ne_id,
                params: merge(changedValues, requestConfig),
                success: () => {
                    setInitialValues?.(merge(initialValues, changedValuesCopy));
                    afterFinish?.(true);
                    setSaveLoading(false);
                },
                fail: () => {
                    afterFinish?.(false);
                    setSaveLoading(false);
                }
            });
        }
    };

    const getUsedFrequency = async keys => {
        const request = {};
        const _keys = [...keys];
        rootPath.reduce(
            // eslint-disable-next-line no-return-assign
            (p, c) => [
                (p[0][c] = Object.fromEntries(
                    p[1][c].definition.key ? p[1][c].definition.key.split(" ").map(k => [k, _keys.shift()]) : []
                )),
                p[1][c]
            ],
            [request, Yang]
        );
        let obj = null;
        // eslint-disable-next-line no-return-assign
        rootPath.reduce((p, c) => (obj = p[c]), request);
        obj["min-edge-freq"] = "";
        obj["max-edge-freq"] = "";
        const response = await netconfGetByXML({ne_id, xml: request});
        if (response.result && response.data) {
            const data = convertToArray(getValueByJPath(response.data, rootPath));
            return data.map(item => {
                return {min: item["min-edge-freq"], max: item["max-edge-freq"]};
            });
        }
        return [];
    };

    const popupFrequency = async () => {
        try {
            setButtonLoading(true);
            const _keys = [...keys];
            _keys.pop();
            const usedList = await getUsedFrequency(_keys);
            let current = null;
            if (
                form.getFieldValue([...rootPath, MIN_FREQ_PARAM]) &&
                form.getFieldValue([...rootPath, MAX_FREQ_PARAM])
            ) {
                current = {
                    min: form.getFieldValue([...rootPath, MIN_FREQ_PARAM]),
                    max: form.getFieldValue([...rootPath, MAX_FREQ_PARAM])
                };
            }
            openModalFrequency(
                v => {
                    if (v) {
                        const newValues = {...form.getFieldsValue()};
                        let obj = null;
                        // eslint-disable-next-line no-return-assign
                        (configRootPath ?? rootPath).reduce((p, c) => (obj = p[c]), newValues);
                        Object.assign(obj, v);
                        form.setFieldsValue(newValues);
                    }
                },
                usedList,
                current
            );
        } catch (e) {
            // console.log(e);
        } finally {
            setButtonLoading(false);
        }
    };

    const createTabItems = ({field, statePath, configPath, mode, key, y}) => {
        const labelCol = labelWidth ? {width: labelWidth} : {};
        const formItemWrapWidth = labelWidth ? labelWidth + 280 : "auto";
        const allItems = Object.entries(field)
            .filter(
                ([k]) =>
                    useDefine?.[k]?.needDisplay ||
                    getValueByJPath(initialValues, [...statePath, k]) !== undefined ||
                    getValueByJPath(initialValues, [...configPath, k]) !== undefined
            )
            .map(([k, v]) => {
                if (useDefine?.[k]?.when) {
                    if (!useDefine?.[k].when(form.getFieldsValue(), initialValues)) {
                        return;
                    }
                }
                const temp = getValueByJPath(LeafRefs, [...configPath]);
                const leafRef = temp?.config?.[k] ?? temp?.[k];
                return ((type === "5" && mode === 0 && v.configMode === 2) || UpperDef) &&
                    !key.includes(k) &&
                    !readOnly &&
                    (useDefine?.[k]?.needDisplay ||
                        getValueByJPath(initialValues, [...configPath, k]) !== undefined) ? (
                    <Form.Item
                        style={{width: formItemWrapWidth, marginBottom: 0}}
                        labelCol={{style: {...labelCol}}}
                        wrapperCol={{flex: "0 0 280px"}}
                        key={k}
                        label={getText(k)}
                        tooltip={
                            (useDefine?.[k]?.description ?? y[k].description) ? (
                                <span style={{whiteSpace: "pre-line"}}>
                                    {useDefine?.[k]?.description ?? y[k].description}
                                </span>
                            ) : undefined
                        }
                    >
                        <div className={styles.multiColumnForm}>
                            <Form.Item
                                key="config"
                                name={[...configPath, k]}
                                style={{
                                    display: "inline-block",
                                    width: type === "5" ? "50%" : "100%"
                                }}
                            >
                                {leafRef ? (
                                    <Select
                                        notFoundContent={gLabelList.loading}
                                        onDropdownVisibleChange={async open => {
                                            if (open) {
                                                if (!leafRef.empty?.(form)) {
                                                    const _values = await leafRef.getList?.({
                                                        form,
                                                        keys,
                                                        ne_id
                                                    });
                                                    setOptions(_values);
                                                }
                                            } else {
                                                setOptions([]);
                                            }
                                        }}
                                        options={options.map(op => {
                                            if (typeof op === "string") {
                                                return {
                                                    label: op,
                                                    value: op
                                                };
                                            }
                                            return {label: op.label, value: op.value};
                                        })}
                                    />
                                ) : (
                                    EditInput({
                                        config: y[k],
                                        useDefine: useDefine?.[k],
                                        form,
                                        datas,
                                        setDatas,
                                        initVals: initialValues,
                                        key: k,
                                        path: [...configPath, k]
                                    })
                                )}
                            </Form.Item>
                            {type === "5" && (
                                <Form.Item
                                    key="state"
                                    name={[...statePath, k]}
                                    style={{
                                        display: "inline-block",
                                        width: k === MIN_FREQ_PARAM ? "40%" : "50%"
                                    }}
                                >
                                    {[...needAscii16FormatKeys, ...needAscii16FormatWithFlagKeys].includes(k) ? (
                                        <AsciiHexAndPlainTextConverter
                                            disabled
                                            defaultValue={getValueByJPath(initialValues, [...statePath, k])}
                                            isContainFlag={needAscii16FormatWithFlagKeys.includes(k)}
                                        />
                                    ) : (
                                        <DisableInput config={y[k]} />
                                    )}
                                </Form.Item>
                            )}
                            {k === MIN_FREQ_PARAM ? (
                                <Form.Item key="frequency" style={{display: "inline-block", width: "10%"}}>
                                    <Button
                                        loading={buttonLoading}
                                        disabled={buttonLoading}
                                        onClick={popupFrequency}
                                        style={{width: "100%"}}
                                        icon={<WifiOutlined />}
                                    />
                                </Form.Item>
                            ) : null}
                        </div>
                    </Form.Item>
                ) : (
                    <Form.Item
                        style={{width: formItemWrapWidth}}
                        labelCol={{style: {...labelCol}}}
                        wrapperCol={{flex: "0 0 280px"}}
                        key={k}
                        name={[...statePath, k]}
                        label={getText(k)}
                        tooltip={y[k].description ? y[k].description : false}
                    >
                        {[...needAscii16FormatKeys, ...needAscii16FormatWithFlagKeys].includes(k) ? (
                            <AsciiHexAndPlainTextConverter
                                disabled
                                defaultValue={getValueByJPath(initialValues, [...statePath, k])}
                                isContainFlag={needAscii16FormatWithFlagKeys.includes(k)}
                            />
                        ) : (
                            <DisableInput config={y[k]} />
                        )}
                    </Form.Item>
                );
            });
        const oneElementComponents = [];
        const twoElementComponents = [];
        allItems.forEach(i => {
            if (i.props?.children?.length > 1) {
                twoElementComponents.push(i);
            } else {
                oneElementComponents.push(i);
            }
        });
        const leftElementComponents = [];
        const rightElementComponents = [];
        for (let i = 0; i < allItems.length; i++) {
            if (i % 2 === 0) {
                leftElementComponents.push(
                    oneElementComponents.length > 0
                        ? oneElementComponents.splice(0, 1)
                        : twoElementComponents.splice(0, 1)
                );
            } else {
                rightElementComponents.push(
                    twoElementComponents.length > 0
                        ? twoElementComponents.splice(0, 1)
                        : oneElementComponents.splice(0, 1)
                );
            }
        }
        return (
            <div
                style={{
                    display: "flex",
                    flexWrap: "wrap",
                    columnGap: 80,
                    marginTop: 8,
                    visibility: labelWidth ? "visible" : "hidden"
                }}
            >
                {allItems}
                {formPanel && (
                    <Form.Item
                        label="save"
                        style={{
                            width: "100%",
                            visibility: labelWidth ? "visible" : "hidden"
                        }}
                        labelCol={{
                            style: {...labelCol, visibility: "hidden"}
                        }}
                    >
                        <Button
                            style={{width: 100}}
                            type="primary"
                            disabled={readOnly}
                            loading={saveLoading}
                            onClick={onSave}
                        >
                            Save
                        </Button>
                    </Form.Item>
                )}
            </div>
        );
    };

    useLayoutEffect(() => {
        setLabelWidth(0);

        const catchFunc = () => {
            setLabelWidth(220);
        };
        const startTime = Date.now();
        const timeout = 2000; // 2s
        const timer = setInterval(() => {
            try {
                const labelElements = Array.from(
                    document.querySelectorAll(".ant-tabs-tabpane-active .ant-form-item-label")
                );
                if (Date.now() - startTime > timeout) {
                    throw new Error("clac timeout!!!");
                }
                if (Array.isArray(labelElements) && labelElements.length) {
                    const maxWidth =
                        parseInt(
                            labelElements.reduce((max, labelElement) => {
                                const labelWidth = labelElement.offsetWidth;
                                return Math.max(max, labelWidth);
                            }, 0)
                        ) + 32;
                    if (typeof maxWidth === "number") {
                        setLabelWidth(maxWidth);
                        clearInterval(timer);
                    } else {
                        throw new Error("calc error, maxWidth is not a number!");
                    }
                }
            } catch (e) {
                catchFunc();
            }
        }, 16);
        return () => {
            clearInterval(timer);
        };
    }, [selectTab]);

    const tabsChildren = Object.entries(tabs)
        .map(([tabName, {path, mode, field}]) => {
            const tName =
                !specialTableName.includes(tabName) && tabName.charAt(1) === "-" ? tabName.substring(2) : tabName;
            const statePath = UpperDef || mode === 1 ? path : [...path, "state"];
            // eslint-disable-next-line no-nested-ternary
            const configPath = UpperDef ? configRootPath : mode === 0 ? [...path, "config"] : statePath;
            if (UpperDef || getValueByJPath(initialValues, statePath) || getValueByJPath(initialValues, configPath)) {
                const f = Object.entries(field).filter(
                    ([k]) =>
                        getValueByJPath(initialValues, [...statePath, k]) !== undefined ||
                        getValueByJPath(initialValues, [...configPath, k]) !== undefined
                );
                if (f.length === 0) return null;
                return getText(tName).toUpperCase();
            }
        })
        .filter(item => typeof item === "string");

    const onSave = () => {
        form.validateFields().then(() => {
            setSaveLoading(true);
            form.submit();
        });
    };

    return initialValues ? (
        <>
            {!formPanel && title && refresh && (
                <div className={styles.edit_form_header}>
                    <span className={styles.edit_form_header_title}>{title}</span>
                    <div className={styles.edit_form_header_operation}>
                        {refresh ? (
                            <a key="refresh" onClick={fetchData}>
                                {gLabelList.refresh}
                            </a>
                        ) : (
                            ""
                        )}
                    </div>
                </div>
            )}
            <div className={styles.edit_form_content}>
                <Form
                    form={form}
                    onFinish={onFinish}
                    className={styles.edit_form}
                    style={formPanel ? {height: "auto"} : {}}
                    labelAlign="left"
                    key={title}
                >
                    <Tabs
                        className={classNames([
                            styles.edit_tab,
                            tabsChildren?.length > 1 ? "" : styles.hiddenTabsNav,
                            "radioGroupTabs"
                        ])}
                        id={formPanel ? "formPanel" : ""}
                        onChange={tabKey => {
                            setSelectTab(tabKey);
                        }}
                    >
                        {Object.entries(tabs).map(([tabName, {path, mode, field}]) => {
                            const tName =
                                !specialTableName.includes(tabName) && tabName.charAt(1) === "-"
                                    ? tabName.substring(2)
                                    : tabName;
                            const statePath = UpperDef || mode === 1 ? path : [...path, "state"];
                            // eslint-disable-next-line no-nested-ternary
                            const configPath = UpperDef ? configRootPath : mode === 0 ? [...path, "config"] : statePath;
                            if (
                                UpperDef ||
                                getValueByJPath(initialValues, statePath) ||
                                getValueByJPath(initialValues, configPath)
                            ) {
                                let y = getYangByPath(Yang, UpperDef ? statePath : path);
                                const key = y.definition.key ? y.definition.key.split(" ") : [];
                                if (!UpperDef && mode === 0) y = y.state;
                                const f = Object.entries(field).filter(
                                    ([k]) =>
                                        getValueByJPath(initialValues, [...statePath, k]) !== undefined ||
                                        getValueByJPath(initialValues, [...configPath, k]) !== undefined
                                );
                                if (f.length === 0) return null;
                                return (
                                    <Tabs.TabPane
                                        key={tabName}
                                        tab={formatTabName ? formatTabName(tName) : getText(tName).toUpperCase()}
                                        className={classNames([tabName, styles.edit_tab_pane])}
                                        // forceRender
                                    >
                                        {createTabItems({tabName, field, statePath, configPath, mode, key, y})}
                                    </Tabs.TabPane>
                                );
                            }
                        })}
                    </Tabs>
                </Form>
            </div>
        </>
    ) : (
        <div style={{width: "100%", textAlign: "center"}}>
            <a>
                <LoadingOutlined style={{fontSize: 32, fill: "#14C9BB", color: "#14C9BB"}} />
            </a>
        </div>
    );
};

const openModalEdit = (categoryName, keys, type, db_key, ne_id, initData, onUpdate, readOnly, title) => {
    if (!categoryName) return;
    // console.log(categoryName);
    // console.log(keys);
    // console.log(type);
    // console.log(db_key);
    apiGetCategory(categoryName, type).then(categoryRs => {
        apiGetYang(type).then(yang => {
            let form;
            // eslint-disable-next-line no-return-assign
            const handle = f => (form = f);
            let modal;
            const afterFinish = success => {
                if (success) {
                    modal.destroy();
                    onUpdate?.();
                } else {
                    modal.update({okButtonProps: {loading: false}});
                }
            };
            modal = bigModal({
                title: title ?? (Array.isArray(keys) ? keys[keys.length - 1] : keys),
                okText: gLabelList.ok,
                cancelText: gLabelList.cancel,
                // eslint-disable-next-line no-unused-vars
                onOk: _ => {
                    modal.update({okButtonProps: {loading: true}});
                    form.submit();
                },
                okButtonProps: {
                    disabled: readOnly
                },
                content: (
                    <EditForm
                        categoryName={categoryName}
                        setForm={handle}
                        afterFinish={afterFinish}
                        Yang={yang}
                        category={categoryRs}
                        db_key={db_key}
                        ne_id={ne_id}
                        keys={typeof keys === "string" ? [keys] : keys}
                        initData={initData}
                        type={type}
                        readOnly={readOnly}
                    />
                )
            });
        });
    });
};

export {openModalEdit, EditForm};
