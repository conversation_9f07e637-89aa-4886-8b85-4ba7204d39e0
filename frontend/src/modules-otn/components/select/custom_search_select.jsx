import Icon from "@ant-design/icons/lib/components/Icon";
import {Input, Select} from "antd";
import {useEffect, useState} from "react";
import {searchIcon} from "@/modules-otn/pages/otn/device/device_icons";

export default function CustomSelect({value, onChange, options, ...rest}) {
    const [searchValue, setSearchValue] = useState("");
    const [selectedGroup, setSelectedGroup] = useState(undefined);
    const [filteredOptions, setFilteredOptions] = useState(options);

    const handleSelectChange = value => {
        setSelectedGroup(value);
        onChange(value);
        setSearchValue("");
        setFilteredOptions(options);
    };

    useEffect(() => {
        setSelectedGroup(value);
    }, [value]);

    useEffect(() => {
        setFilteredOptions(options);
    }, [options]);

    const handleSearch = value => {
        setSearchValue(value);
        const filtered = options.filter(option => option.label.toLowerCase().includes(value.toLowerCase()));
        setFilteredOptions(filtered);
    };

    const dropDownRender = menu => {
        const isHiddenSearchInput =
            filteredOptions?.length === 2 &&
            filteredOptions?.every(item => ["true", "false", "on", "off"].includes(item.label.toLowerCase()));
        return (
            <div>
                {isHiddenSearchInput ? null : (
                    <div>
                        <Input
                            value={searchValue}
                            onChange={e => handleSearch(e.target.value)}
                            placeholder="Search"
                            prefix={<Icon component={searchIcon} />}
                            allowClear
                            style={{width: "100%", height: "32px", marginBottom: "3px"}}
                        />
                    </div>
                )}
                {menu}
            </div>
        );
    };

    return (
        <Select
            {...rest}
            value={selectedGroup}
            onChange={value => {
                handleSelectChange(value);
                onChange(value);
            }}
            dropdownRender={menu => dropDownRender(menu)}
            options={filteredOptions}
        />
    );
}
