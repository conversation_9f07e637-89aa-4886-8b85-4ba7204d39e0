.chassisView {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    margin: 0;
    border: 1px solid #95b8e7;
    border-radius: 3px;
}

.wrap {
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    font-size: 12px;
    border-bottom: 1px solid #ddd;
    flex-direction: row-reverse;
}

.diagram {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow-x: auto;
}

.button {
    margin-left: 50px;
}

.select-entity-view {
    padding: 5px;
}

.loading {
    font-size: 18px;
    font-weight: 800;
}

.head_div {
    width: 1308px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    text-align: center;
    padding-bottom: 24px;
}

.head_div a {
    height: 32px;
    padding: 0 16px;
    font-size: 14px;
    border: solid 1px #B3BBC8;
    display: inline-block;
    align-content: center;
}
