export const SIDES = ["front", "rear", "mux51", "mux53", "mux55", "mux57"];

const CARDWIDTH = 305;
const INIT = 44;

const CHASSIS_CONFIG = {
    "1-1": {
        side: SIDES[0],
        left: INIT
    },
    "1-2": {
        side: SID<PERSON>[0],
        left: INIT + CARDWIDTH
    },
    "1-3": {
        side: SIDES[0],
        left: INIT + CARDWIDTH * 2
    },
    "1-4": {
        side: SIDES[0],
        left: INIT + CARDWIDTH * 3
    },
    "1-5": {
        side: SIDES[0],
        left: INIT,
        top: 136
    },
    "1-6": {
        side: SIDES[0],
        left: INIT + CARDWIDTH,
        top: 136
    },
    "1-7": {
        side: SIDES[0],
        left: INIT + CARDWIDTH * 2,
        top: 136
    },
    "1-8": {
        side: SIDES[0],
        left: INIT + CARDWIDTH * 3,
        top: 136
    },
    "1-21": {
        side: SIDES[1],
        left: 62,
        top: 3
    },
    "1-22": {
        side: SIDES[1],
        left: 62,
        top: 137
    },
    "1-31": {
        side: SIDES[1],
        left: 308.5
    },
    "1-32": {
        side: SIDES[1],
        left: 556
    },
    "1-33": {
        side: SIDES[1],
        left: 804
    },
    "1-40": {
        top: 4,
        side: SIDES[1],
        left: 1177.5
    },
    "1-41": {
        top: 137,
        side: SIDES[1],
        left: 1033
    },
    "1-42": {
        top: 4,
        side: SIDES[1],
        left: 1033
    },
    "1-51": {
        top: 0,
        side: SIDES[2]
    },
    "1-53": {
        top: 0,
        side: SIDES[3]
    },
    "1-55": {
        top: 0,
        side: SIDES[4]
    },
    "1-57": {
        top: 0,
        side: SIDES[5]
    }
};

const OA_CONFIG = {
    PAIN: {
        top: 30,
        left: 221
    },
    PAOUT: {
        top: 30,
        left: 149
    },
    BAIN: {
        top: 30,
        left: 172
    },
    BAOUT: {
        top: 30,
        left: 198
    },
    MON1: {
        top: 30,
        left: 259
    },
    MON2: {
        top: 30,
        left: 282
    },
    OTDR1: {
        top: 30,
        left: 307
    },
    OTDR2: {
        top: 30,
        left: 330
    },
    led: {
        top: 82,
        left: 31
    },
    LED_BAIN: {
        top: 73,
        left: 166
    },
    LED_PAIN: {
        top: 73,
        left: 215
    }
};

const OLA_CONFIG = {
    LA2OUT: {
        top: 30,
        left: 123
    },
    LA1IN: {
        top: 30,
        left: 146
    },
    LA1OUT: {
        top: 30,
        left: 172
    },
    LA2IN: {
        top: 30,
        left: 195
    },
    MON1: {
        top: 30,
        left: 220
    },
    MON2: {
        top: 30,
        left: 244
    },
    OTDR1: {
        top: 30,
        left: 268
    },
    OTDR2: {
        top: 30,
        left: 293
    },
    OTDR3: {
        top: 30,
        left: 317
    },
    OTDR4: {
        top: 30,
        left: 341
    },
    led: {
        top: 82,
        left: 31
    },
    LED_MON1: {
        top: 72,
        left: 140
    },
    LED_MON2: {
        top: 72,
        left: 188
    }
};

const WSS_CONFIG = {
    BAOUT: {
        top: 29,
        left: 19
    },
    PAIN: {
        top: 29,
        left: 43
    },
    MON1: {
        top: 30,
        left: 498
    },
    MON2: {
        top: 30,
        left: 521
    },
    OTDR1: {
        top: 30,
        left: 545
    },
    OTDR2: {
        top: 30,
        left: 570
    },
    AD1: {
        top: 28,
        left: 67
    },
    AD2: {
        top: 28,
        left: 115
    },
    AD3: {
        top: 28,
        left: 163
    },
    AD4: {
        top: 28,
        left: 210
    },
    AD5: {
        top: 28,
        left: 258
    },
    AD6: {
        top: 28,
        left: 306
    },
    AD7: {
        top: 28,
        left: 354
    },
    AD8: {
        top: 28,
        left: 401
    },
    AD9: {
        top: 28,
        left: 449
    },
    led: {
        top: 72,
        left: 15
    },
    LED_PAIN: {
        top: 72,
        left: 36
    }
};

const TFF_CONFIG = {
    MUX: {
        top: 28,
        left: 9
    },
    CH1: {
        top: 28,
        left: 57
    },
    CH2: {
        top: 28,
        left: 106
    },
    CH3: {
        top: 28,
        left: 154
    },
    CH4: {
        top: 28,
        left: 203
    },
    PT1: {
        top: 28,
        left: 251
    },
    led: {
        top: 73,
        left: 14
    },
    LED_MUX: {
        top: 73,
        left: 43
    },
    LED_CH1: {
        top: 73,
        left: 90
    },
    LED_CH2: {
        top: 73,
        left: 134
    },
    LED_CH3: {
        top: 73,
        left: 179
    },
    LED_CH4: {
        top: 73,
        left: 220
    },
    LED_PT1: {
        top: 73,
        left: 259
    }
};

const H2B2_CONFIG = {
    L1: {
        top: 26,
        left: 98
    },
    C1: {
        top: 69,
        left: 104
    },
    C2: {
        top: 69,
        left: 204
    },
    led: {
        top: 69,
        left: 23
    },
    LED_L1: {
        top: 54,
        left: 23
    },
    LED_C1: {
        top: 106,
        left: 123
    },
    LED_C2: {
        top: 106,
        left: 223
    }
};

const H4B4_CONFIG = {
    L1: {
        top: 26,
        left: 98
    },
    C1: {
        top: 69,
        left: 53
    },
    C2: {
        top: 69,
        left: 104
    },
    C3: {
        top: 69,
        left: 155
    },
    C4: {
        top: 69,
        left: 204
    },
    led: {
        top: 69,
        left: 23
    },
    LED_L1: {
        top: 54,
        left: 23
    },
    LED_C1: {
        top: 106,
        left: 73
    },
    LED_C2: {
        top: 106,
        left: 123
    },
    LED_C3: {
        top: 106,
        left: 173
    },
    LED_C4: {
        top: 106,
        left: 223
    }
};

const H1T10B2_CONFIG = {
    L1: {
        top: 18,
        left: 21
    },
    C1: {
        top: 30,
        left: 135
    },
    C2: {
        top: 30,
        left: 182
    },
    C3: {
        top: 30,
        left: 228
    },
    C4: {
        top: 30,
        left: 275
    },
    C5: {
        top: 30,
        left: 322
    },
    C6: {
        top: 30,
        left: 370
    },
    C7: {
        top: 30,
        left: 417
    },
    C8: {
        top: 30,
        left: 464
    },
    C9: {
        top: 30,
        left: 511
    },
    C10: {
        top: 30,
        left: 558
    },
    C21: {
        top: 65,
        left: 53
    },
    led: {
        top: 70,
        left: 8
    },
    LED_L1: {
        top: 55,
        left: 8
    },
    LED_C1: {
        top: 21,
        left: 151
    },
    LED_C2: {
        top: 21,
        left: 198
    },
    LED_C3: {
        top: 21,
        left: 245
    },
    LED_C4: {
        top: 21,
        left: 292
    },
    LED_C5: {
        top: 21,
        left: 339
    },
    LED_C6: {
        top: 21,
        left: 387
    },
    LED_C7: {
        top: 21,
        left: 434
    },
    LED_C8: {
        top: 21,
        left: 480
    },
    LED_C9: {
        top: 21,
        left: 528
    },
    LED_C10: {
        top: 21,
        left: 575
    },
    LED_C21: {
        top: 99,
        left: 72
    }
};

const T20B2_CONFIG = {
    L1: {
        top: 18,
        left: 22
    },
    C1: {
        top: 30,
        left: 135
    },
    C2: {
        top: 30,
        left: 182
    },
    C3: {
        top: 30,
        left: 228
    },
    C4: {
        top: 30,
        left: 275
    },
    C5: {
        top: 30,
        left: 322
    },
    C6: {
        top: 30,
        left: 370
    },
    C7: {
        top: 30,
        left: 417
    },
    C8: {
        top: 30,
        left: 464
    },
    C9: {
        top: 30,
        left: 511
    },
    C10: {
        top: 30,
        left: 558
    },
    C11: {
        top: 66,
        left: 135
    },
    C12: {
        top: 66,
        left: 182
    },
    C13: {
        top: 66,
        left: 228
    },
    C14: {
        top: 66,
        left: 275
    },
    C15: {
        top: 66,
        left: 322
    },
    C16: {
        top: 66,
        left: 370
    },
    C17: {
        top: 66,
        left: 417
    },
    C18: {
        top: 66,
        left: 464
    },
    C19: {
        top: 66,
        left: 511
    },
    C20: {
        top: 66,
        left: 558
    },
    led: {
        top: 70,
        left: 8
    },
    LED_L1: {
        top: 55,
        left: 8
    },
    LED_C1: {
        top: 20,
        left: 149
    },
    LED_C2: {
        top: 20,
        left: 195
    },
    LED_C3: {
        top: 20,
        left: 243
    },
    LED_C4: {
        top: 20,
        left: 290
    },
    LED_C5: {
        top: 20,
        left: 336
    },
    LED_C6: {
        top: 20,
        left: 385
    },
    LED_C7: {
        top: 20,
        left: 431
    },
    LED_C8: {
        top: 20,
        left: 478
    },
    LED_C9: {
        top: 20,
        left: 525
    },
    LED_C10: {
        top: 20,
        left: 572
    },
    LED_C11: {
        top: 20,
        left: 159
    },
    LED_C12: {
        top: 20,
        left: 206
    },
    LED_C13: {
        top: 20,
        left: 253
    },
    LED_C14: {
        top: 20,
        left: 300
    },
    LED_C15: {
        top: 20,
        left: 347
    },
    LED_C16: {
        top: 20,
        left: 395
    },
    LED_C17: {
        top: 20,
        left: 441
    },
    LED_C18: {
        top: 20,
        left: 488
    },
    LED_C19: {
        top: 20,
        left: 535
    },
    LED_C20: {
        top: 20,
        left: 582
    }
};

const PSU_CONFIG = {
    led: {
        top: 7.5,
        left: 7.5
    }
};

export const chassisConfigFS = {
    "D7000-CH2U": CHASSIS_CONFIG,
    OPB2: {
        "1-APSC": {
            top: 28,
            left: 8.8
        },
        "1-APSP": {
            top: 28,
            left: 57
        },
        "1-APSS": {
            top: 28,
            left: 106
        },
        led: {
            top: 74,
            left: 14
        },
        "LED_1-APSC": {
            top: 74,
            left: 44
        },
        "LED_1-APSP": {
            top: 74,
            left: 91
        },
        "LED_1-APSS": {
            top: 74,
            left: 135
        }
    },
    "OPB2-I": {
        "1-APSC": {
            top: 28,
            left: 8.8
        },
        "1-APSP": {
            top: 28,
            left: 57
        },
        "1-APSS": {
            top: 28,
            left: 106
        },
        "2-APSC": {
            top: 28,
            left: 154
        },
        "2-APSP": {
            top: 28,
            left: 203
        },
        "2-APSS": {
            top: 28,
            left: 251
        },
        led: {
            top: 74,
            left: 14
        },
        "LED_1-APSC": {
            top: 74,
            left: 44
        },
        "LED_1-APSP": {
            top: 74,
            left: 91
        },
        "LED_1-APSS": {
            top: 74,
            left: 135
        },
        "LED_2-APSC": {
            top: 74,
            left: 179
        },
        "LED_2-APSP": {
            top: 74,
            left: 221
        },
        "LED_2-APSS": {
            top: 74,
            left: 259
        }
    },
    OCM08: {
        OCM1: {
            top: 30,
            left: 58
        },
        OCM2: {
            top: 30,
            left: 81
        },
        OCM3: {
            top: 30,
            left: 107
        },
        OCM4: {
            top: 30,
            left: 129
        },
        OCM5: {
            top: 30,
            left: 155
        },
        OCM6: {
            top: 30,
            left: 178
        },
        OCM7: {
            top: 30,
            left: 204
        },
        OCM8: {
            top: 30,
            left: 227
        },
        led: {
            top: 73,
            left: 14
        }
    },
    OTDR08: {
        OTDR1: {
            top: 30,
            left: 58
        },
        OTDR2: {
            top: 30,
            left: 81
        },
        OTDR3: {
            top: 30,
            left: 107
        },
        OTDR4: {
            top: 30,
            left: 129
        },
        OTDR5: {
            top: 30,
            left: 155
        },
        OTDR6: {
            top: 30,
            left: 178
        },
        OTDR7: {
            top: 30,
            left: 204
        },
        OTDR8: {
            top: 30,
            left: 227
        },
        led: {
            top: 73,
            left: 14
        }
    },
    "ROADM-09T": WSS_CONFIG,
    OA1825: OA_CONFIG,
    OA1835: OA_CONFIG,
    OLA2525: OLA_CONFIG,
    TFF1619: TFF_CONFIG,
    TFF2023: TFF_CONFIG,
    TFF2528: TFF_CONFIG,
    TFF2932: TFF_CONFIG,
    TFF3437: TFF_CONFIG,
    TFF3841: TFF_CONFIG,
    TFF4346: TFF_CONFIG,
    TFF4750: TFF_CONFIG,
    TFF5255: TFF_CONFIG,
    TFF5659: TFF_CONFIG,
    "2MC2": H2B2_CONFIG,
    "4MC4": H4B4_CONFIG,
    "11MC2": H1T10B2_CONFIG,
    "20MC2": T20B2_CONFIG,
    OMD48ECM: {
        MUX: {
            top: 70,
            left: 89
        },
        MON: {
            top: 33,
            left: 89
        },
        CH1: {
            top: 33,
            left: 134
        },
        CH2: {
            top: 33,
            left: 179
        },
        CH3: {
            top: 33,
            left: 225
        },
        CH4: {
            top: 33,
            left: 270
        },
        CH5: {
            top: 33,
            left: 315
        },
        CH6: {
            top: 33,
            left: 360
        },
        CH7: {
            top: 33,
            left: 406
        },
        CH8: {
            top: 33,
            left: 451
        },
        CH9: {
            top: 33,
            left: 496
        },
        CH10: {
            top: 33,
            left: 541.5
        },
        CH11: {
            top: 33,
            left: 587
        },
        CH12: {
            top: 33,
            left: 632
        },
        CH13: {
            top: 33,
            left: 677
        },
        CH14: {
            top: 33,
            left: 723
        },
        CH15: {
            top: 33,
            left: 768
        },
        CH16: {
            top: 33,
            left: 813
        },
        CH17: {
            top: 33,
            left: 858
        },
        CH18: {
            top: 33,
            left: 904
        },
        CH19: {
            top: 33,
            left: 949
        },
        CH20: {
            top: 33,
            left: 994
        },
        CH21: {
            top: 33,
            left: 1039
        },
        CH22: {
            top: 33,
            left: 1085
        },
        CH23: {
            top: 33,
            left: 1130
        },
        CH24: {
            top: 33,
            left: 1175
        },
        CH25: {
            top: 70,
            left: 134
        },
        CH26: {
            top: 70,
            left: 179
        },
        CH27: {
            top: 70,
            left: 225
        },
        CH28: {
            top: 70,
            left: 270
        },
        CH29: {
            top: 70,
            left: 315
        },
        CH30: {
            top: 70,
            left: 360
        },
        CH31: {
            top: 70,
            left: 406
        },
        CH32: {
            top: 70,
            left: 451
        },
        CH33: {
            top: 70,
            left: 496
        },
        CH34: {
            top: 70,
            left: 541.5
        },
        CH35: {
            top: 70,
            left: 587
        },
        CH36: {
            top: 70,
            left: 632
        },
        CH37: {
            top: 70,
            left: 677
        },
        CH38: {
            top: 70,
            left: 723
        },
        CH39: {
            top: 70,
            left: 768
        },
        CH40: {
            top: 70,
            left: 813
        },
        CH41: {
            top: 70,
            left: 858
        },
        CH42: {
            top: 70,
            left: 904
        },
        CH43: {
            top: 70,
            left: 949
        },
        CH44: {
            top: 70,
            left: 994
        },
        CH45: {
            top: 70,
            left: 1039
        },
        CH46: {
            top: 70,
            left: 1085
        },
        CH47: {
            top: 70,
            left: 1130
        },
        CH48: {
            top: 70,
            left: 1175
        }
    },
    "D7000-PSU-AC": PSU_CONFIG,
    "D7000-PSU-DC": PSU_CONFIG,
    "D7000-FAN": {
        led: {
            top: 249,
            left: 9
        }
    },
    "D7000-EMU": {
        led: {
            top: 90,
            left: 11
        }
    },
    "D7000-AUX": {
        NM1: {
            top: 20,
            left: 25
        },
        NM2: {
            top: 73,
            left: 25
        },
        ETH: {
            top: 135,
            left: 25
        },
        CON: {
            top: 188,
            left: 25
        }
    }
};
