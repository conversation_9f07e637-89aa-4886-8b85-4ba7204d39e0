import {useSelector} from "react-redux";
import {useDeferredValue, useEffect, useRef, useState} from "react";
import CardInfo from "@/modules-otn/pages/otn/device/card_info";
import PortList from "@/modules-otn/pages/otn/device/port_list";
import Chassis5 from "@/modules-otn/components/chassis/chassis_5";
import {getStateData, NEGet, netconfGetByXML, objectGet} from "@/modules-otn/apis/api";
import {removeNS1} from "@/modules-otn/utils/util";
import Chassis2 from "@/modules-otn/components/chassis/chassis_2";

const cardInfoHide = ["slot"];
const portListShow = ["chassis", "D7000-AUX", "LINECARD", "OA", "TFF", "MUX", "WSS", "OTDR", "OCM", "OLP", "OLA"];

const ChassisContainer = () => {
    const [data, setData] = useState({});
    const [loadTimer] = useState(null);
    const loadTimerRef = useRef(loadTimer);
    const {tableFilter} = useSelector(state => state.map);
    const {selectedItem} = useSelector(state => state.map);
    const deferredTableFilter = useDeferredValue(tableFilter);

    const loadData_ne5 = async needLoadingState => {
        let name = "CHASSIS-1";
        let type = "chassis";
        if (deferredTableFilter.resource) {
            name = deferredTableFilter.resource.value;
            type = deferredTableFilter.resource.type;
        }
        if (type === "card") {
            if (name === "MCU-1-40") {
                type = "D7000-AUX";
            } else {
                type = name.substring(0, name.indexOf("-"));
            }
        } else if (type === "port" || type === "TRANSCEIVER") {
            const data = await objectGet("", {
                DBKey: `ne:5:component:${deferredTableFilter.id}:${name.replace("TRANSCEIVER", "PORT")}`
            });
            name = data.documents[0].value.data.state.parent;
            type = name.substring(0, name.indexOf("-"));
            if (name === "MCU-1-40") type = "D7000-AUX";
        }
        const rs = await netconfGetByXML({
            msg: false,
            ne_id: deferredTableFilter.id,
            xml: {
                components: {
                    $: {
                        xmlns: "http://openconfig.net/yang/platform"
                    },
                    component: {
                        name
                    }
                }
            }
        });
        if (rs.apiResult === "fail") {
            return;
        }
        const stateData =
            (
                await getStateData({
                    ne_id: deferredTableFilter.id
                })
            )?.data?.value?.data?.["state-data"] ?? {};

        let chassisData = {};
        if (!portListShow.includes(type)) {
            const chassisRs = await netconfGetByXML({
                msg: false,
                ne_id: deferredTableFilter.id,
                xml: {
                    components: {
                        $: {
                            xmlns: "http://openconfig.net/yang/platform"
                        },
                        component: {
                            name: "CHASSIS-1"
                        }
                    }
                }
            });
            if (chassisRs.apiResult === "fail") {
                return;
            }
            chassisData = {
                ne_id: deferredTableFilter.id,
                name: "CHASSIS-1",
                type: "chassis",
                neData: removeNS1(chassisRs)?.components?.component ?? {},
                stateData,
                needLoadingState
            };
        }

        setData({
            ne_id: deferredTableFilter.id,
            name,
            type,
            neData: removeNS1(rs)?.components?.component ?? {},
            stateData,
            needLoadingState,
            chassisData
        });
    };

    const loadData_ne2 = async needLoadingState => {
        const rs = await NEGet({
            ne_id: deferredTableFilter.id,
            parameter: {
                commonAdminGroup: {},
                commonDeviceInfoEntry: {},
                ramanChannelEntry: {},
                ramanPumpEntry: {},
                ramanDCPowerEntry: {}
            }
        });
        const data = {
            manufacture: "FS",
            "ne-type": rs.ramanChannelEntry?.[0]?.ramanChannelWorkMode,
            gain: rs.ramanChannelEntry?.[0]?.ramanChannelConfigGainMax,
            "channel-num": rs.ramanPumpEntry.length === 3 ? "96CH" : "80CH",
            "power-type": rs.ramanDCPowerEntry?.[0]?.ramanDCPowerType === "1" ? "AC 220V" : "DC 48V",
            ...(rs?.commonAdminGroup ? rs.commonAdminGroup : {}),
            ...(rs?.commonDeviceInfoEntry ? rs.commonDeviceInfoEntry[0] : {}),
            ramanDCPowerEntry: rs.ramanDCPowerEntry
        };
        setData({
            ne_id: deferredTableFilter.id,
            name,
            type: "RAMAN_BOX",
            neData: {state: data},
            stateData: {},
            needLoadingState
        });
    };

    const loadData = async needLoadingState => {
        if (deferredTableFilter?.type !== "NODE_NE") {
            setData({});
            return;
        }
        const {runState} = (await objectGet("config:ne", {ne_id: deferredTableFilter.id})).documents[0].value;
        if (!runState || runState === 0) {
            setData({
                type: "offline"
            });
            return;
        }
        if (selectedItem.value.type === "5") {
            await loadData_ne5(needLoadingState);
        } else if (selectedItem.value.type === "2") {
            await loadData_ne2(needLoadingState);
        }
    };

    const clearTime = () => {
        if (loadTimerRef.current) {
            clearInterval(loadTimerRef.current);
            loadTimerRef.current = null;
        }
    };

    useEffect(() => {
        clearTime();
        loadData(true).then();
        loadTimerRef.current = setInterval(() => {
            loadData(false).then();
        }, 15000);
    }, [deferredTableFilter]);

    useEffect(() => {
        return () => {
            clearTime();
        };
    }, []);
    return (
        selectedItem?.value && (
            <div style={{overflow: "auto", background: "#f0f2f5", width: "100%"}}>
                <div style={{background: "#fff", borderRadius: "0 0 10px 10px", paddingBottom: 24}}>
                    {selectedItem.value.type === "5" && <Chassis5 />}
                    {selectedItem.value.type === "2" && <Chassis2 data={data} />}
                </div>
                {["5"].includes(selectedItem.value.type) &&
                    data.type &&
                    !portListShow.includes(data.type) &&
                    data.type !== "offline" && <CardInfo data={data.chassisData} refresh={loadData} />}
                {(["2"].includes(selectedItem.value.type) ||
                    (["5"].includes(selectedItem.value.type) && !cardInfoHide.includes(data.type))) &&
                    data.type !== "offline" && <CardInfo data={data} refresh={loadData} />}
                {(["2"].includes(selectedItem.value.type) ||
                    (["5"].includes(selectedItem.value.type) && portListShow.includes(data.type))) &&
                    data.type !== "offline" && <PortList data={data} refresh={loadData} />}
            </div>
        )
    );
};

export default ChassisContainer;
