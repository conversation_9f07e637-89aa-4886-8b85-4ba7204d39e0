import {useEffect, useState} from "react";
import {message, Select, Spin} from "antd";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {bigModal} from "@/modules-otn/components/modal/custom_modal";
import styles from "./frequency.module.scss";

const CELL_STATUS = {
    FREE: "free",
    USED: "used",
    CURRENT: "currentSelect"
};
const MIN_FREQUENCY = 191300000;
const CELL_AMOUNT = 784;
const CELL_GAP = 6250;
const BW_50G = 50000;
const BW_75G = 75000;
const BW_100G = 100000;

const ALL_CELLS = new Array(CELL_AMOUNT).fill(CELL_STATUS.FREE);

export const MIN_FREQ_PARAM = "min-edge-freq";
export const MAX_FREQ_PARAM = "max-edge-freq";

const FrequencyChooser = ({_usedList, current, updateData}) => {
    const [cells, setCells] = useState(ALL_CELLS);
    const [selectable, setSelectable] = useState(true);
    const [option, setOption] = useState(BW_100G);
    const [usedList, setUsedList] = useState();
    const [loading, setLoading] = useState(false);

    const frequencyToArray = (min, max, cellList) => {
        if ([BW_50G, BW_75G, BW_100G].includes(max - min)) {
            const status =
                current && current.min === min && current.max === max ? CELL_STATUS.CURRENT : CELL_STATUS.USED;
            const startIdx = (min - MIN_FREQUENCY) / CELL_GAP;
            for (let i = startIdx; i < startIdx + (max - min) / CELL_GAP; i++) {
                cellList[i] = status;
            }
        } else {
            // eslint-disable-next-line no-console
            console.error("invalid bandwidth");
        }
    };

    async function updateCells() {
        const initCells = [...ALL_CELLS];
        usedList?.forEach(({min, max}) => {
            frequencyToArray(min, max, initCells);
        });

        setCells(initCells);
    }

    async function fetchData() {
        try {
            setLoading(true);
            const usedList = await _usedList();
            setUsedList(usedList);
        } catch (error) {
            // error
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        fetchData().then();
    }, []);

    useEffect(() => {
        const initCells = [...ALL_CELLS];
        usedList?.forEach(({min, max}) => {
            frequencyToArray(min, max, initCells);
        });
        if (current && [BW_50G, BW_75G, BW_100G].includes(current.max - current.min)) {
            setOption(current.max - current.min);
            setSelectable(false);
            frequencyToArray(current.min, current.max, initCells);
        }
        setCells(initCells);
    }, [usedList]);

    const handleCellClick = frequency => {
        const gap = option / CELL_GAP;
        const startCell = (frequency - MIN_FREQUENCY) / CELL_GAP;
        if (cells[startCell] === CELL_STATUS.CURRENT) {
            const newCells = [...cells];
            for (let i = startCell; i >= 0; --i) {
                if (cells[i] !== CELL_STATUS.CURRENT) break;
                newCells[i] = CELL_STATUS.FREE;
            }
            const currentEnd = current ? (current.max - MIN_FREQUENCY) / CELL_GAP : 0;
            const end = currentEnd > startCell + gap ? currentEnd : gap;
            for (let i = 1; i < end; ++i) {
                if (cells[startCell + i] !== CELL_STATUS.CURRENT) break;
                newCells[startCell + i] = CELL_STATUS.FREE;
            }

            setSelectable(true);
            updateData?.(null);
            setCells(newCells);
        } else if (selectable) {
            for (let i = 0; i < gap; ++i) {
                if (cells[startCell + i] !== CELL_STATUS.FREE) {
                    message.info(gLabelList.warn_no_bandwidth).then();
                    return;
                }
            }

            const newCells = [...cells];
            for (let i = 0; i < gap; ++i) {
                newCells[startCell + i] = CELL_STATUS.CURRENT;
            }
            setSelectable(false);
            updateData?.({
                [MIN_FREQ_PARAM]: MIN_FREQUENCY + startCell * CELL_GAP,
                [MAX_FREQ_PARAM]: MIN_FREQUENCY + (startCell + gap) * CELL_GAP
            });
            setCells(newCells);
        }
    };

    return (
        <>
            <div className={styles.frequency_header}>
                <Select
                    value={option}
                    style={{width: 120}}
                    onChange={v => {
                        setOption(v);
                        updateCells();
                        setSelectable(true);
                    }}
                >
                    <Select.Option value={BW_50G}>50G</Select.Option>
                    <Select.Option value={BW_75G}>75G</Select.Option>
                    <Select.Option value={BW_100G}>100G</Select.Option>
                </Select>
                <div className={styles.sample}>
                    <div className={`${styles.sample_block} ${styles.block_free}`} />
                    <span>{gLabelList.free}</span>
                    <div className={`${styles.sample_block} ${styles.block_occupied}`} />
                    <span>{gLabelList.occupied}</span>
                    <div className={`${styles.sample_block} ${styles.block_currentSelect}`} />
                    <span>{gLabelList.selected}</span>
                </div>
            </div>
            <div className={styles.wrap}>
                {loading && (
                    <div className={styles.loading}>
                        <Spin />
                    </div>
                )}
                <div className={styles.container}>
                    {new Array(25).fill(1).map((_, idx) => {
                        const rowItems = [];
                        for (let i = 0; i < 32; ++i) {
                            const cellIndex = idx * 32 + i;
                            if (cellIndex < CELL_AMOUNT) {
                                rowItems.push(
                                    <div
                                        key={i}
                                        className={`${styles.block} ${styles[`block_${[cells[cellIndex]]}`]}`}
                                        onClick={() => handleCellClick(MIN_FREQUENCY + cellIndex * CELL_GAP)}
                                        title={`${MIN_FREQUENCY + cellIndex * CELL_GAP} - ${
                                            MIN_FREQUENCY + (cellIndex + 1) * CELL_GAP
                                        }`}
                                    >
                                        {i + 1}
                                    </div>
                                );
                            }
                        }
                        return (
                            // eslint-disable-next-line react/no-array-index-key
                            <div key={`${idx}_${CELL_GAP}`} className={styles.container_row}>
                                <span className={styles.container_left}>
                                    <div>{191300000 + idx * 32 * CELL_GAP}</div>
                                </span>
                                <span className={styles.container_right}>{rowItems}</span>
                            </div>
                        );
                    })}
                </div>
            </div>
        </>
    );
};

const openModalFrequency = (onClose, _usedList, current) => {
    const Data = (() => {
        let _data;
        return {
            getData() {
                return _data;
            },
            setData(d) {
                _data = d;
            }
        };
    })();
    // const usedList = await _usedList?.();

    const modal = bigModal({
        title: "Wavelength Selection",
        content: <FrequencyChooser _usedList={_usedList} updateData={Data.setData} current={current} />,
        okText: gLabelList.ok,
        cancelText: gLabelList.cancel,
        // eslint-disable-next-line no-unused-vars
        onOk: _ => {
            onClose?.(Data.getData());
            modal?.destroy();
        },
        onCancel: () => {
            modal?.destroy();
        }
    });
};

export {openModalFrequency};
