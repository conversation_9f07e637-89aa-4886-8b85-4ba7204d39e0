import {Input, Button, Space} from "antd";
import {RetweetOutlined} from "@ant-design/icons";
import {useState} from "react";
import {useToggle} from "ahooks";
import {
    plainTextConvertToAsciiHex,
    asciiHexConvertToPlainText,
    addFlagToAsciiHex,
    isAsciiHexStr
} from "@/modules-otn/utils/util";

export default function AsciiHexAndPlainTextConverter({
    defaultValue,
    isContainFlag = false,
    onChange = () => {},
    disabled
}) {
    const [value, setValue] = useState(
        isAsciiHexStr(defaultValue) ? asciiHexConvertToPlainText(defaultValue, isContainFlag) : defaultValue
    );
    const [showType, {toggle}] = useToggle("Hex", "Text");
    const isErrorAsciiHex = showType === "Text" && !isAsciiHexStr(value);

    return (
        <div>
            <Space.Compact style={{width: "100%"}}>
                <Input
                    value={value}
                    onChange={e => {
                        const newInputValue = e.target.value;
                        setValue(newInputValue);
                    }}
                    status={isErrorAsciiHex && "error"}
                    onBlur={() => {
                        // 如果是Hex且字符不是合法的16进制字符，只保留前面的合法字符
                        if (isErrorAsciiHex) {
                            const safeValue = value.match(/^(\d{2})*/)[0];
                            setValue(safeValue);
                        }
                        const asciiHex = showType === "Hex" ? plainTextConvertToAsciiHex(value) : value;
                        const savedValue = isContainFlag ? addFlagToAsciiHex(asciiHex) : asciiHex;
                        onChange?.(savedValue);
                    }}
                    disabled={disabled}
                />
                <Button
                    type="primary"
                    icon={<RetweetOutlined />}
                    onClick={() => {
                        toggle();
                        if (/^0+$/.test(defaultValue) && value === "" && showType === "Hex") {
                            setValue(defaultValue);
                            return;
                        }
                        setValue(
                            showType === "Hex"
                                ? plainTextConvertToAsciiHex(value)
                                : asciiHexConvertToPlainText(value, isContainFlag)
                        );
                    }}
                >
                    {showType}
                </Button>
            </Space.Compact>
        </div>
    );
}
