/**
 * @description: 绑定了theme的modal
 */
import {Button} from "antd";
import Icon from "@ant-design/icons";
import ToolButton from "@/modules-otn/pages/otn/common/tool_button";
import React, {useState} from "react";

// eslint-disable-next-line import/no-mutable-exports
export let rootModal;

export const setRootModal = modal => {
    rootModal = modal;
};

export const MODAL_WIDTH = {
    oneCol: "680px",
    twoCol: "1360px"
};

// chassis config
export const getLocationAndSizeMap = (LocationMap, size, prefix) => {
    const [width, height] = size;
    const res = {};
    LocationMap.forEach((location, slotNo) => {
        if (!Array.isArray(location)) return;
        const [top, left] = location;
        res[`${prefix}-${slotNo}`] = {
            top,
            left,
            width,
            height
        };
    });
    return res;
};
// const c = 1000 * 60 * 60 * 24;
// const d = () => {
//     return new Date().getTime();
// };
// const f = JSON;
// const v = 16;
// const s = "SGxrNHlMWVBIczJ";
// eslint-disable-next-line no-extend-native, func-names
String.prototype.format = function (args) {
    let result = this;
    if (arguments.length > 0) {
        if (arguments.length === 1 && typeof args === "object") {
            // eslint-disable-next-line no-restricted-syntax
            for (const key in args) {
                if (args[key] !== undefined) {
                    const reg = new RegExp(`({${key}})`, "g");
                    result = result.replace(reg, args[key]);
                }
            }
        } else {
            for (let i = 0; i < arguments.length; i++) {
                // eslint-disable-next-line prefer-rest-params
                if (arguments[i] !== undefined) {
                    // var reg = new RegExp("({[" + i + "]})", "g");//这个在索引大于9时会有问题
                    const reg2 = new RegExp(`({)${i}(})`, "g");
                    // eslint-disable-next-line prefer-rest-params
                    result = result.replace(reg2, arguments[i]);
                }
            }
        }
    }
    return result;
};

export const SUPPORT_NE_TYPES = ["2", "5", "6", "7"];

export const NE_TYPE_CONFIG = {
    2: "D7000-DRA",
    5: "D7000",
    6: "DCP920",
    7: "FMT"
};

export const NE_PROTOCOL_TYPE = {
    2: "snmp",
    5: "netConf",
    6: ""
};

export const OPERATIONAL_MODE_MAP = {
    7: "200G-FOIC2-oFEC-QPSK-1-S",
    9: "200G-200ZR-oFEC-QPSK-1-S",
    10: "200G-FOIC2-oFEC-8QAM-1-SE",
    11: "200G-200ZR-oFEC-8QAM-1-SE",
    12: "200G-FOIC2-oFEC-16QAM-1-SE",
    13: "200G-200ZR-oFEC-16QAM-1-SE",
    23: "400G-FOIC4-oFEC-16QAM-1-SE",
    25: "400G-400ZR-oFEC-16QAM-1-SE"
};

export const PORT_MAPPING_MAP = {
    "11MC2": {
        PROT_10GE_LAN: "10GE->ODU2->ODU4->ODUC2->OTUC2",
        PROT_10GE_WAN: "10GE->ODU2->ODU4->ODUC2->OTUC2",
        PROT_FC8G: "FC8G->ODU2->ODU4->ODUC2->OTUC2",
        PROT_FC10G: "FC10G->ODU2->ODU4->ODUC2->OTUC2",
        PROT_FC16G: "FC16G->ODUF->ODU4->ODUC2->OTUC2",
        PROT_FC32G: "FC32G->ODUF->ODU4->ODUC2->OTUC2",
        PROT_OTU2: "OTU2->ODU2->ODU4->ODUC2->OTUC2",
        PROT_OTU2E: "OTU2->ODU2->ODU4->ODUC2->OTUC2",
        PROT_STM64: "STM64->ODU2->ODU4->ODUC2->OTUC2",
        PROT_100GE: "100GE->ODU4->ODUC2->OTUC2",
        PROT_OTU4: "OTU4->ODU4->ODUC2->OTUC2"
    },
    "4MC4": {
        PROT_100GE: "100GE->ODU4->ODUC4->OTUC4",
        PROT_400GE: "400GE->ODUC4->OTUC4",
        PROT_OTU4: "OTU4->ODU4->ODUC4->OTUC4"
    },
    "2MC2": {
        PROT_100GE: "100GE->ODU4->ODUC2->OTUC2",
        PROT_OTU4: "OTU4->ODU4->ODUC2->OTUC2"
    }
};

export const OCH_MODE_SUPPORT_MAP = {
    H4B4E: [25],
    H4B4: [23, 25],
    H2B2: [7, 9, 10, 11, 12, 13],
    T20B2: [7, 10, 12],
    T20B2Z: [7, 10, 12],
    H1T10B2: [7, 10, 12],
    H1T10B2Z: [7, 10, 12],
    "4MC4": [23, 25],
    "2MC2": [7, 9, 10, 11, 12, 13],
    "20MC2": [7, 10, 12],
    "11MC2": [7, 10, 12]
};

export const ALARM_COLOR = {
    CRITICAL: {
        color: "#F53F3F",
        backgroundColor: "rgba(245,63,63,0.1)",
        borderColor: "#F53F3F"
    },
    MAJOR: {
        color: "#FF7B43",
        backgroundColor: "rgba(255,123,67,0.1)",
        borderColor: "#FF7B43"
    },
    MINOR: {
        color: "#FFBB00",
        backgroundColor: "rgba(255,187,0,0.1)",
        borderColor: "#FFBB00"
    },
    WARNING: {
        color: "#00B7FF",
        backgroundColor: "rgba(0,183,255,0.1)",
        borderColor: "#00B7FF"
    }
};

export const getNeType = type => {
    if (isNaN(type)) {
        return Object.entries(NE_TYPE_CONFIG).find(([k, v]) => v === type)?.[0];
    }
    return type;
};

/**
 * 将对象，字符串，数字封装为数组
 * @param {*} obj
 * @returns {Array} array
 */
export const convertToArray = obj => {
    if (Array.isArray(obj)) return obj;
    if (["string", "object", "number"].includes(typeof obj)) return [obj];
    return [];
};

// const x = "FREc1N0JnL3NZPT";

// get value for attribute, even with namespace
export const getAttrValue = (obj, attrName) => {
    if (!obj || !attrName || Array.isArray(obj) || typeof obj !== "object") return null;
    if (obj?.[attrName] && typeof obj[attrName] !== "object") return obj[attrName];
    // eslint-disable-next-line guard-for-in,no-restricted-syntax
    for (const key in obj) {
        const realKey = key.split(":").pop(); // some key has namespace
        if (realKey !== attrName || obj[key] == null) continue;
        if (Array.isArray(obj[key])) {
            const rtnValue = [];
            obj[key].forEach(attrValue => {
                if (typeof attrValue !== "object") rtnValue.push(attrValue);
            });
            return rtnValue.join(", ");
        }
        if (typeof obj[key] === "object") {
            if (obj[key]?.["#text"]) {
                return obj[key]["#text"];
            }
            return "";
        }

        return obj[key];
    }
    return null;
};

/**
 * 根据路径返回Json对象的值
 * @param {Object} obj
 * @param {Array} path
 * @example getValueByJPath({a: {c: {name: "a", test: 111}, b: 2}, ff: 1}, ["a", "c"]) => {name: "a", test: 111}
 */
export const getValueByJPath = (obj, path) =>
    path.reduce(
        (a, c) =>
            // eslint-disable-next-line no-nested-ternary
            typeof c === "string"
                ? Array.isArray(a)
                    ? a.map(i => i?.[c])
                    : a?.[c]
                : Array.isArray(a) && Array.isArray(a[0])
                  ? a.map(b => b?.find(i => getValueByJPath(i, c[0]) === c[1]))
                  : a?.find(i => getValueByJPath(i, c[0]) === c[1]),
        obj
    );

/**
 * Recurve all nodes and remove fields start with @, convert {f: {@xmlns: "", #text: "val"}} to {f: "val"}
 * If value is array, also removeNS()
 * No change for object (null, undefined)
 * @param o
 * @returns {{}}
 */
export const removeNS1 = o =>
    Object.entries(o).reduce((prev, [k, v]) => {
        const a = k.split(":");
        return {
            ...prev,
            ...(a[0][0] !== "@"
                ? {
                      [a.pop()]: removeNS(
                          v?.["#text"] ??
                              // eslint-disable-next-line no-nested-ternary
                              (v?.constructor === Object
                                  ? removeNS1(v)
                                  : Array.isArray(v)
                                    ? v.map(i => removeNS(i))
                                    : v)
                      )
                  }
                : null)
        };
    }, {});
export const getYangByPath = (yang, path) => getValueByJPath(yang, path);

export const UPPER_CASES = [
    "pm",
    "mcu",
    "cpu",
    "lldp",
    "aps",
    "ntp",
    "tti",
    "bbe",
    "es",
    "ses",
    "uas",
    "fec",
    "als",
    "mac",
    "ad",
    "voa",
    "id",
    "tlv",
    "fan",
    "tff",
    "oeo",
    "ber",
    "tec",
    "lcd",
    "wss",
    "apr",
    "otdr",
    "tx",
    "rx",
    "crc",
    "pcs",
    "bip",
    "usz",
    "ip",
    "ttl",
    "osnr",
    "src",
    "dst",
    "nat",
    "pa",
    "ba",
    "la1",
    "la2",
    "att",
    "odu",
    "ptp",
    "ctp",
    "ftp",
    "tcp",
    "eq",
    "mtu",
    "sdh",
    "ts",
    "ne",
    "nni",
    "nni2",
    "uni",
    "cir",
    "pir",
    "cbs",
    "pbs",
    "vlan",
    "lcas",
    "vc",
    "wtr",
    "tsd",
    "eh",
    "xc",
    "tcm",
    "eth",
    "tpid",
    "eos",
    "pac",
    "vcg",
    "mc",
    "vlan",
    "uuid",
    "vlan",
    "pvid",
    "tp",
    "sd",
    "pg",
    "tca",
    "tim",
    "ssm",
    "clk",
    "sa",
    "sapi",
    "dapi",
    "sn",
    "pn",
    "otn",
    "num"
];

export const getText = text => {
    if (!text) return null;
    return (
        text
            .split("-")
            ?.map(i => (UPPER_CASES.some(u => u === i) ? i.toUpperCase() : i[0].toUpperCase() + i.substring(1)))
            .join(" ") ?? text
    );
};

export const addNS = (param, yang) => {
    return parseNameSpace(param, yang, yang.namespace);
};

export const parseNameSpace = (param, yang, ns) => {
    try {
        if (Array.isArray(param)) {
            return param.map(item => parseNameSpace(item, yang, ns));
        }
        Object.keys(param).forEach(key => {
            const o = param[key];
            const y = yang[key];
            if (key.startsWith("@")) {
                delete param[key];
                if (!param.$) {
                    param.$ = {};
                }
                const _k = key.replace("@", "");
                param.$[_k] = o?.toString();
            } else if (typeof o === "object") {
                parseNameSpace(o, y, ns);
                if (y.definition) {
                    if (ns[y.definition.namespace] && ns[y.definition.namespace] !== "") {
                        o.$ = {xmlns: ns[y.definition.namespace], ...o.$};
                    }
                }
            } else if (y) {
                if (ns[y.namespace] && ns[y.namespace] !== "") {
                    param[key] = {$: {xmlns: ns[y.namespace]}, _: o?.toString()};
                }
            }
        });
        return param;
    } catch (e) {
        return param;
    }
};

/**
 *
 * @param {array} strArr  需要排序的字符串数组
 * @param {array} compareTargetPath  排序依据的路径,不填默认是字符串本身，支持多级路径如["a","b","c"]为a.b.c
 * @returns {array} 排序后的字符串数组
 * @example
 * sortArr(["b", "A", "C"]) -> ["A", "b", "C"]
 * sortArr([{a: "b"}, {a: "A"}, {a: "C"}], ["a"]) -> [{a: "A"}, {a: "b"}, {a: "C"}]
 * sortArr([{a: {b: "b"}}, {a: {b: "A"}}, {a: {b: "C"}}], ["a", "b"]) -> [{a: {b: "A"}}, {a: {b: "b"}}, {a: {b: "C"}}]
 */
export const sortArr = (strArr, compareTargetPath) => {
    if ((strArr && !Array.isArray(strArr)) || (compareTargetPath && !Array.isArray(compareTargetPath))) return strArr;
    return strArr?.sort((a, b) => {
        const lowerCaseA =
            compareTargetPath === undefined
                ? a?.toLowerCase()
                : compareTargetPath.reduce((res, pathItem) => res[pathItem], a);
        const lowerCaseB =
            compareTargetPath === undefined
                ? b?.toLowerCase()
                : compareTargetPath.reduce((res, pathItem) => res[pathItem], b);
        return lowerCaseA?.toString()?.localeCompare(lowerCaseB?.toString(), "ZH-CN", {numeric: true});
    });
};

export const removeNS = val => {
    if (Array.isArray(val)) return val.map(v => removeNS(v));
    const s = val?._ && val.$ ? val._.split(":").pop() : val;
    if (typeof s === "string" && s.startsWith("oc-") && s.indexOf(":") > -1) {
        return s.substring(s.indexOf(":") + 1);
    }
    return s;
};

export const removeNSForObj = o =>
    Object.fromEntries(
        Object.entries(o).map(([k, v]) => {
            const val = removeNS(v);
            if (k === "used-service-port-type-preconf") {
                return [k, val.split("_")[1]];
            }
            if (val?.constructor === Object) {
                delete val.$;
                return [k, removeNSForObj(val)];
            }
            return [k, val];
        })
    );

// const y = "I4TmJIVUk1SmZSU";

export const shortNumber = (number, decimalSize) => {
    if (number) {
        try {
            const num = parseFloat(number);
            if (num !== 0) {
                const p = Math.floor(Math.log(num) / Math.LN10);
                const n = num * 10 ** -p;
                return `${n.toFixed(decimalSize)}E${p}`;
            }
            return 0;
        } catch (e) {
            return number;
        }
    }
    return number;
};

export const sortLabel = () => {
    try {
        return (optionA, optionB) =>
            (optionA?.label ?? "")
                .toString()
                .toLowerCase()
                .localeCompare((optionB?.label ?? "").toString().toLowerCase(), "ZH-CN", {numeric: true});
        // eslint-disable-next-line no-unreachable
    } catch (e) {
        return () => 1;
    }
};

export const TableWidthConfig = {
    "add-drop-channel": {
        index: {
            width: 200
        }
    },
    "pass-through-channel": {
        index: {
            width: 200
        }
    },
    "lldp-interface": {
        name: {
            width: 160
        },
        age: {
            width: 60
        },
        ttl: {
            width: 60
        },
        "port-id": {
            width: 150
        },
        "management-address": {
            width: 110
        },
        "system-name": {
            width: 100
        }
    },
    connection: {
        index: {
            width: 70
        }
    }
};

export const needAscii16FormatKeys = [
    "j0-actual-rx",
    "j0-expected-rx",
    "j0-actual-tx",
    "j1-actual-rx",
    "j1-expected-rx",
    "j1-actual-tx",
    "pmtrail-trace-actual-rx",
    "pmtrail-trace-actual-tx",
    "pmtrail-trace-expected-rx",
    "smtrail-trace-actual-rx",
    "smtrail-trace-actual-tx",
    "smtrail-trace-expected-rx"
];

export const needAscii16FormatWithFlagKeys = ["tti-msg-transmit", "tti-msg-expected", "tti-msg-recv"];

/**
 * 用于将明文字符转换为16进制Ascii字符串
 * @param {string} str 待转换字符串
 * @returns {string}
 */
export function plainTextConvertToAsciiHex(str) {
    try {
        if (!str || typeof str !== "string") return str;
        const hexArray = [...str].map(char => {
            const ascii = char.charCodeAt(0);
            return ascii.toString(16).padStart(2, "0");
        });
        return hexArray.join("");
    } catch (e) {
        return "";
    }
}

/**
 * 用于将16进制Ascii字符串转换为明文
 * @param {string} str 待转换字符串
 * @param {boolean} isContainFlag 是否包含标志位
 * @returns {string}
 */
export function asciiHexConvertToPlainText(str, isContainFlag = false) {
    try {
        if (!str || typeof str !== "string") return "";
        const _str = isContainFlag
            ? [
                  str.slice(0, 2) === "00" ? str.slice(2, 32) : str.slice(0, 30),
                  str.slice(32, 34) === "00" ? str.slice(34, 64) : str.slice(30, 60),
                  str.slice(64, 66) === "00" ? str.slice(66) : str.slice(60)
              ].join("")
            : str;
        if (/^0+$/.test(_str)) return "";
        return (
            _str
                .match(/.{1,2}/g)
                .map(hexStr => {
                    if (hexStr === "00") return "";
                    const ascii = parseInt(hexStr, 16);
                    return String.fromCharCode(ascii);
                })
                .join("") ?? ""
        );
    } catch (e) {
        return "";
    }
}

/**
 * 返回字符是否是16位Ascii码
 * @param {string} str 待验证字符
 * @returns boolean
 * @example
 * isAsciiHexStr("561234") => true
 * isAsciiHexStr("5a123") => false
 */
export const isAsciiHexStr = str => typeof str === "string" && /^([\dA-Fa-f]{2})*$/.test(str);

// const z = "Uo1RHNVR3FyOUo=";

/**
 * 16位Ascii码添加标志位
 * @param {string} str 待验证字符
 * @returns string
 * @example
 * isAsciiHexStr("313233343536373839303132333435313233343536373839393132333435313233") => "003132333435363738393031323334350031323334353637383939313233343500313233"
 */
export const addFlagToAsciiHex = str => {
    try {
        if (!str || typeof str !== "string") return str;
        return [str.slice(0, 30), str.slice(30, 60), str.slice(60)].reduce((res, strPart) => {
            if (strPart === "") return res;
            return `${res}00${strPart}`;
        }, "");
    } catch (e) {
        return "";
    }
};

/**
 * 遍历对象，执行操作,会对原对象造成影响
 * @param {object} obj
 * @param {function} func
 * @returns object
 * @example
 * traverse({a: {b: {c: 1, d: 2}, e: 3}, f: 4}, (key, value) => {
 *   if (key === "c" || key === "f")} return value * 2;
 *   return value;
 * )  -> {a: {b: {c: 2, d: 2}, e: 3}, f: 8}
 */
export function traverse(obj, func) {
    // eslint-disable-next-line no-restricted-syntax
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = obj[key];
            if (typeof value === "object") {
                traverse(value, func);
            } else {
                obj[key] = func(key, value);
            }
        }
    }
    return obj;
}

/**
 * Remove namespace and convert to JSX
 * @param o
 * @returns {[string, unknown]}
 */
export const removeJsonNS = o =>
    Object.entries(o["rpc-reply"]).reduce((prev, [k, v]) => {
        const a = k.split(":");
        return a[0][0] !== "@"
            ? [
                  ...prev,
                  <div key={k}>
                      {a.pop()}: {v["#text"] ?? JSON.stringify(v)}
                  </div>
              ]
            : prev;
    }, []);

export function extractNameFromNamespace(data) {
    if (typeof data !== "string") return data;
    const [namespace, name] = data.split(":");
    if (namespace === "/" || namespace.includes(".") || name === undefined) {
        return data;
    }
    return name;
}

export function getFilter(dataIndex, filterFunc, dataSource) {
    const filter_values = [];
    dataSource?.forEach(i => {
        let key = extractNameFromNamespace(getAttrValue(i, dataIndex));
        if (filterFunc) key = filterFunc(key);
        if (!filter_values.some(item => item.text === key)) {
            filter_values.push({
                text: key,
                value: key
            });
        }
    });

    return filter_values.sort((a, b) => (a.text >= b.text ? 1 : -1));
}

/**
 * 获取ISO8601格式的时区
 * @returns string
 * @example
 * getISO8601Timezone("2021-03-01T00:00:00+08:00") -> "UTC+8"
 * @param timeStr
 */
export function getISO8601Timezone(timeStr) {
    const symbol = timeStr.slice(-6, -5);
    const number = parseInt(timeStr.slice(-5, -3));
    return number === 0 ? "UTC" : `UTC${symbol}${number}`;
}

/**
 * 返回字符是否是纯数字字符串
 * @param {string} str 待检查字符
 * @returns {boolean} boolean
 * @example
 * isNumberStr("123") => true
 * isNumberStr("-123.123") => true
 * isNumberStr("123.") => false
 * isNumberStr("123.1a") => false
 */
export const isNumberStr = str => typeof str === "string" && /^-?\d+(\.\d+)?$/.test(str);

export const randomRange = (min, max) => {
    let returnStr = "";
    const range = max ? Math.round(Math.random() * (max - min)) + min : min;
    const charStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    for (let i = 0; i < range; i++) {
        const index = Math.round(Math.random() * (charStr.length - 1));
        returnStr += charStr.substring(index, index + 1);
    }
    return returnStr;
};

export const sensorPathList = {
    transceiver: {
        value: "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state",
        path: ["components", "component", "transceiver", "state"],
        request: {
            type: "ne:5:component",
            filter: {type: "transceiver"}
        }
    },
    port: {
        value: "/openconfig-platform:components/component/port/openconfig-transport-line-common:optical-port/state",
        path: ["components", "component", "port", "optical-port", "state"],
        request: {
            type: "ne:5:component",
            filter: {type: "port"}
        }
    },
    otn: {
        value: "/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state",
        path: ["terminal-device", "logical-channels", "channel", "otn", "state"],
        request: {
            type: "ne:5:channel"
            // filter: {logical_channel_type: "channel"}
        }
    },
    ethernet: {
        value: "/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state",
        path: ["terminal-device", "logical-channels", "channel", "ethernet", "state"],
        request: {
            type: "ne:5:channel"
            // filter: {logical_channel_type: "ethernet"}
        }
    },
    amplifier: {
        value: "/openconfig-optical-amplifier:optical-amplifier/amplifiers/amplifier/state",
        path: ["optical-amplifier", "amplifiers", "amplifier", "state"],
        request: {
            type: "ne:5:amplifier"
            // filter: {type: "amplifier"}
        }
    },
    och: {
        value: "/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state",
        path: ["components", "component", "optical-channel", "state"],
        request: {
            type: "ne:5:component",
            filter: {type: "OPTICAL_CHANNEL"}
        }
    }
};

/**
 * 根据传感器路径获取传感器编码
 */
export const encodeSensorPath = path => Object.entries(sensorPathList).find(item => path === item[1].value)?.[0];

/**
 * 返回数组是否为空
 * @param {Array} array
 * @returns {Boolean} boolean
 */
export const isEmptyArray = array => Array.isArray(array) && array.length === 0;

/**
 * 返回对象是否为空
 * @param {Object} object
 * @returns {Boolean} boolean
 */
export const isEmptyObject = object => Object.keys(object).length === 0;

export const sleep = ms => {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve();
        }, ms);
    });
};
export const filterEvents = (newArray, tableFilter) => {
    return newArray.filter(item => {
        const result = item.ne_id === tableFilter.id;
        if (!result) return false;
        if (!tableFilter.resource || tableFilter.resource.type === "chassis") return true;
        const fltArr = tableFilter.resource.value.split("-");
        const newArr = item.resource.split("-");
        if (fltArr.length === 3) return fltArr[1] === newArr[1] && fltArr[2] === newArr[2];
        return fltArr[1] === newArr[1] && fltArr[2] === newArr[2] && fltArr[3] === newArr[3];
    });
};

export const getDeviceStateValue = (state, point, parameter) => {
    if (!parameter) {
        return state?.find?.(i => i["state-point"] === point);
    }
    const data = state?.find?.(i => i["state-point"] === point && i["state-parameter"] === parameter);
    return data?.["state-value"];
};

export const NULL_VALUE = "--";

/**
 * Simplifier multi className
 * @returns String
 * @example
 * classNames([styles.test1, styles.test2]) => "style1 style2"
 * @param classNameArray
 */
export const classNames = classNameArray => classNameArray.join(" ");

export const openConfirmDialog = ({warning, onOK}) => {
    const modal = rootModal.confirm({
        title: "Note",
        closable: true,
        content: warning,
        centered: true,
        // eslint-disable-next-line no-unused-vars
        onOk: _ => {
            onOK();
            modal.destroy();
        }
    });
};

export const mHz_to_nm = value => {
    try {
        if (!value) {
            return NULL_VALUE;
        }
        value = parseInt(value);
        if (value === 0) {
            return value;
        }
        const tHz = value / 1000000;
        return (Math.round(((((2.99792458 * 10 ** 8) / tHz) * 1000000) / 1000000000) * 100) / 100).toFixed(2);
    } catch (e) {
        return value;
    }
};
export const DebounceButton = props => {
    const [receiveEvent, setReceiveEvent] = useState(true);
    const handleClick = e => {
        if (!receiveEvent) {
            return;
        }
        setReceiveEvent(false);
        props.onClick(e);
        setTimeout(() => {
            setReceiveEvent(true);
        }, 500);
    };
    const {containerType, ...rest} = props;
    if (containerType === "Icon") {
        return <Icon {...rest} onClick={handleClick} />;
    }
    if (containerType === "div") {
        return <div {...rest} onClick={handleClick} />;
    }
    if (containerType === "a") {
        return <a {...rest} onClick={handleClick} />;
    }
    if (containerType === "ToolButton") {
        return <ToolButton {...rest} onClick={handleClick} />;
    }
    return <Button {...rest} onClick={handleClick} />;
};

export const formatUnitsMap = {milliseconds: "ms", nanoseconds: "ns"};
