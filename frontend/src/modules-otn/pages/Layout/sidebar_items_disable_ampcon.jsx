import React from "react";
import {Tooltip} from "antd";
import Icon from "@ant-design/icons";
import {
    dashBoardSvg,
    dashBoardSvg2,
    maintainSvg,
    monitorSvg,
    resourceSvg,
    serviceSvg,
    settingSvg
} from "@/utils/common/iconSvg";

const all_items = [
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Dashboard</div>} placement="right">
                Dashboard
            </Tooltip>
        ),
        key: "/dashboard",
        icon: (
            <>
                <Icon component={dashBoardSvg} className="dash-board-light-svg" />
                <Icon component={dashBoardSvg2} className="dash-board-dark-svg" />
            </>
        )
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">NewDashboard</div>} placement="right">
                NewDashboard
            </Tooltip>
        ),
        key: "/newdashboard",
        icon: (
            <>
                <Icon component={dashBoardSvg} className="dash-board-light-svg" />
                <Icon component={dashBoardSvg2} className="dash-board-dark-svg" />
            </>
        )
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Resource</div>} placement="right">
                Resource
            </Tooltip>
        ),
        key: "/resource",
        icon: <Icon component={resourceSvg} />,
        children: [
            {
                key: "/resource/device_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Device View</div>} placement="right">
                        Device View
                    </Tooltip>
                )
            },
            {
                key: "/resource/upgrade_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Upgrade Management</div>} placement="right">
                        Upgrade Management
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Service</div>} placement="right">
                Service
            </Tooltip>
        ),
        key: "/service",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/service/l0_config",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">L0 Config</div>} placement="right">
                        L0 Config
                    </Tooltip>
                )
            },
            {
                key: "/service/l1_config",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">L1 Config</div>} placement="right">
                        L1 Config
                    </Tooltip>
                )
            },
            {
                key: "/service/e2e_service_config",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">E2E Service Config</div>} placement="right">
                        E2E Service Config
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Monitor</div>} placement="right">
                Monitor
            </Tooltip>
        ),
        key: "/monitor",
        icon: <Icon component={monitorSvg} />,
        children: [
            {
                key: "/monitor/alarm",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Alarm</div>} placement="right">
                        Alarm
                    </Tooltip>
                )
            },
            {
                key: "/monitor/performance",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Performance</div>} placement="right">
                        Performance
                    </Tooltip>
                )
            },
            {
                key: "/monitor/performance_subscription",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Performance Subscription</div>} placement="right">
                        Performance Subscription
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Maintain</div>} placement="right">
                Maintain
            </Tooltip>
        ),
        key: "/maintain",
        icon: <Icon component={maintainSvg} />,
        children: [
            {
                key: "/maintain/link_measure",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Link Measurement</div>} placement="right">
                        Link Measurement
                    </Tooltip>
                )
            },
            {
                key: "/maintain/database_manager",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Database Management</div>} placement="right">
                        Database Management
                    </Tooltip>
                )
            },
            {
                key: "/maintain/log_manager",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Log Management</div>} placement="right">
                        Log Management
                    </Tooltip>
                )
            },
            {
                key: "/maintain/openconfig_model",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">OpenConfig Model</div>} placement="right">
                        OpenConfig Model
                    </Tooltip>
                )
            },
            {
                key: "/maintain/cli_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">CLI Configuration</div>} placement="right">
                        CLI Configuration
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                System
            </Tooltip>
        ),
        key: "/system",
        icon: <Icon component={settingSvg} />,
        children: [
            {
                key: "/system/user_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">User Management</div>} placement="right">
                        User Management
                    </Tooltip>
                )
            },
            {
                key: "/system/software_license",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Software License</div>} placement="right">
                        Software License
                    </Tooltip>
                )
            },
            {
                key: "/system/time_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Time Management</div>} placement="right">
                        Time Management
                    </Tooltip>
                )
            }
        ]
    }
];

const excludeItemsByKey = (items, excludeKeys) => {
    const excludeRecursive = list => {
        return list.reduce((acc, item) => {
            if (!excludeKeys.includes(item.key)) {
                if (item.children) {
                    const filteredChildren = excludeRecursive(item.children);
                    acc.push({...item, children: filteredChildren});
                } else {
                    acc.push(item);
                }
            }
            return acc;
        }, []);
    };

    return excludeRecursive(items);
};

const adminExcludeKeys = [
    "/resource/auth_management/group_management",
    "/service/switch/system_config",
    "/system/user_management"
];
const operatorExcludeKeys = [
    "/resource/auth_management/group_management",
    "/service/switch/system_config",
    "/system",
    "/service/switch/system_management"
];
const readonlyExcludeKeys = [
    "/resource/upgrade_management",
    "/resource/auth_management",
    "/maintain/network_config",
    "/service/switch/system_config",
    "/service/switch/global_configuration",
    "/service/switch/switch_configuration",
    "/service/switch/switch_model",
    "/service/switch/system_management",
    "/system"
];

const sidebar_items = {
    superuser: all_items,
    superadmin: excludeItemsByKey(all_items, adminExcludeKeys),
    operator: excludeItemsByKey(all_items, operatorExcludeKeys),
    readonly: excludeItemsByKey(all_items, readonlyExcludeKeys)
};

export default sidebar_items;
