import {useEffect, useState} from "react";
import {useSelector} from "react-redux";
import {Button} from "antd";
import {CreateDBForm} from "@/modules-otn/components/form/create_form_db";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {TableConfig} from "@/modules-otn/config/table_config";
import {objectGet} from "@/modules-otn/apis/api";
import styles from "./reset_counter.module.scss";

const CustomForm = ({type = "reset-counter", msg = true, success, fail, submit, initDataAuto}) => {
    const {labelList} = useSelector(state => state.languageOTN);
    const userRight = useUserRight();
    const [initData, setInitData] = useState({});
    const [loading, setLoading] = useState(false);
    const config = TableConfig[type];

    let form;
    const handle = fun => {
        form = fun;
    };

    let saved = false;

    const afterFinish = () => {
        setLoading(false);
    };

    const processFail = () => {
        setLoading(false);
        setTimeout(() => {
            saved = false;
        }, 1000);
    };

    const commitForm = () => {
        form.validateFields().then(() => {
            if (saved) return;
            saved = true;
            setLoading(true);
            form.submit();
        });
    };

    const loadData = data => {
        const initValues = {};
        Object.entries(data).map(([k, v]) => {
            if (config?.columns?.filter(i => i.dataIndex === k)?.[0]?.init === false) return;
            initValues[k] = {value: v};
        });
        setInitData(initValues);
    };

    useEffect(() => {
        if (initDataAuto) {
            objectGet("", {DBKey: initDataAuto.key}, null, false).then(rs => {
                if (rs?.documents?.length > 0) {
                    loadData(rs.documents[0].value);
                }
            });
        }
    }, []);

    return (
        <div className={styles.container}>
            <div style={{width: 500, paddingBottom: 12}}>
                <CreateDBForm
                    headerStyle={{padding: 0, borderBottom: "none"}}
                    type={type}
                    processFail={processFail}
                    onCancel={afterFinish}
                    setForm={handle}
                    success={success}
                    fail={fail}
                    msg={msg}
                    submit={submit}
                    initData={initData}
                />
            </div>
            <div style={{paddingLeft: 165}}>
                <Button disabled={loading || userRight.disabled} type="primary" onClick={commitForm}>
                    {labelList.save}
                </Button>
            </div>
        </div>
    );
};

export default CustomForm;
