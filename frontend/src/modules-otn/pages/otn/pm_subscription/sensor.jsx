import {useSelector} from "react-redux";
import {Tag, message, theme} from "antd";
import {useEffect, useState} from "react";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {netconfByXML, netconfChange, objectGet} from "@/modules-otn/apis/api";
import {convertToArray, encodeSensorPath} from "@/modules-otn/utils/util";
import {openDBModalCreate} from "@/modules-otn/components/form/create_form_db";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import Icon from "@ant-design/icons";
import {plusDisableIcon, plusIcon} from "@/modules-otn/pages/otn/device/device_icons";

const Sensor = () => {
    const {labelList} = useSelector(state => state.languageOTN);
    const {neNameMap} = useSelector(state => state.neName);
    const userRight = useUserRight();
    const [data, setData] = useState([]);
    const {dataChanged} = useSelector(state => state.notification);

    const {
        token: {colorPrimary}
    } = theme.useToken();

    const loadData = () => {
        try {
            objectGet("ne:5:sensor-group").then(rs => {
                setData(
                    rs.documents.map(item => {
                        return {
                            ...item.value.data,
                            ne_id: item.value.ne_id,
                            ne_name: neNameMap[item.value.ne_id]
                        };
                    })
                );
            });
        } catch (e) {
            // console.log(e);
        }
    };

    useEffect(() => {
        loadData();
    }, [neNameMap]);

    useEffect(() => {
        if (dataChanged.data?.type === "telemetry-system") {
            loadData();
        }
    }, [dataChanged]);

    return (
        <CustomTable
            type="sensor-group"
            initTitle=""
            scroll={false}
            initDataSource={data}
            refreshParent={loadData}
            buttons={[
                {
                    label: "create",
                    disabled: userRight.disabled,
                    type: "primary",
                    icon: <Icon component={userRight.disabled ? plusDisableIcon : plusIcon} />,
                    onClick() {
                        openDBModalCreate({
                            type: "ne:sensor-group",
                            title: labelList.create_sensor_group,
                            submit: async (values, defaultValue, diffValue, cancel, fail) => {
                                const rs = (
                                    await objectGet("ne:5:sensor-group", {
                                        ne_id: values.ne_id,
                                        sensor_group_id: values["sensor-group-id"]
                                    })
                                ).documents;
                                if (rs.length > 0) {
                                    message.error(labelList.same_group_id);
                                    fail({});
                                    return;
                                }
                                await netconfByXML({
                                    ne_id: values.ne_id,
                                    msg: true,
                                    action: "create",
                                    timeout: 60 * 1000,
                                    xml: {
                                        "telemetry-system": {
                                            $: {
                                                xmlns: "http://openconfig.net/yang/telemetry"
                                            },
                                            "sensor-groups": {
                                                "sensor-group": {
                                                    "sensor-group-id": values["sensor-group-id"],
                                                    config: {
                                                        "sensor-group-id": values["sensor-group-id"]
                                                    },
                                                    "sensor-paths": {
                                                        "sensor-path": values?.path?.map(item => {
                                                            return {
                                                                path: item,
                                                                config: {
                                                                    path: item
                                                                }
                                                            };
                                                        })
                                                    },
                                                    includes: {
                                                        include: values?.["object-name"]?.map(item => {
                                                            return {
                                                                "object-name": item,
                                                                config: {
                                                                    "object-name": item
                                                                }
                                                            };
                                                        })
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    success: () => {
                                        cancel(true);
                                        loadData();
                                    },
                                    fail: () => {
                                        fail({});
                                    },
                                    sync: {
                                        type: "telemetry-system"
                                    }
                                });
                            }
                        });
                    }
                }
            ]}
            initRowOperation={[
                {
                    label: labelList.del,
                    confirm: {
                        title: labelList.delete_confirm
                    },
                    onClick() {
                        const originValue = this;
                        objectGet("ne:5:persistent-subscription", {ne_id: originValue.ne_id}).then(rs => {
                            let found = false;
                            for (let i = 0; i < rs.documents.length; i++) {
                                const subscriptionSensors = convertToArray(
                                    rs.documents[i].value.data?.["sensor-profiles"]?.["sensor-profile"]
                                );
                                if (
                                    subscriptionSensors.filter(
                                        item => item["sensor-group"] === originValue["sensor-group-id"]
                                    ).length > 0
                                ) {
                                    found = true;
                                    break;
                                }
                            }
                            if (found) {
                                message.error(labelList.sensor_del_warning).then();
                                return;
                            }
                            netconfChange({
                                ne_id: originValue.ne_id,
                                operation: "delete",
                                entity: "sensor-group",
                                keys: [originValue["sensor-group-id"]],
                                values: {
                                    config: {}
                                },
                                msg: false,
                                success: rs => {
                                    if (rs.result) {
                                        message.success(labelList.delete_success);
                                    } else {
                                        message.error(labelList.delete_fail);
                                    }
                                    setTimeout(() => {
                                        loadData();
                                    }, 1000);
                                },
                                sync: {
                                    type: "telemetry-system"
                                }
                            }).then();
                        });
                    },
                    disabled: userRight?.disabled ? () => true : undefined
                }
            ]}
            columnFormat={{
                "sensor-paths": data => {
                    try {
                        const tagList = [];
                        if (data && data["sensor-path"]) {
                            convertToArray(data["sensor-path"]).map(item => {
                                const tagText = encodeSensorPath(item.path);
                                tagList.push(
                                    <Tag
                                        key={tagText}
                                        style={{
                                            margin: "0 8px 0 0",
                                            padding: "0 7px",
                                            background: "#14C9BB1A",
                                            color: "#14C9BB",
                                            borderColor: "#14C9BB"
                                        }}
                                    >
                                        {tagText}
                                    </Tag>
                                );
                            });
                        }
                        return tagList;
                    } catch (e) {
                        // console.log(e);
                    }
                },
                includes: data => {
                    try {
                        const tagList = [];
                        if (data && data.include) {
                            convertToArray(data.include).map(item => {
                                tagList.push(
                                    <Tag
                                        key={item["object-name"]}
                                        style={{
                                            margin: "0 8px 0 0",
                                            padding: "0 7px",
                                            background: "#14C9BB1A",
                                            color: "#14C9BB",
                                            borderColor: "#14C9BB"
                                        }}
                                    >
                                        {item["object-name"]}
                                    </Tag>
                                );
                            });
                        }
                        return tagList;
                    } catch (e) {
                        // console.log(e);
                    }
                }
            }}
        />
    );
};

export default Sensor;
