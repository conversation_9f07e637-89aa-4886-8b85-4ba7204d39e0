import {Tabs} from "antd";
import {useSelector} from "react-redux";
import {useLocation, useNavigate} from "react-router-dom";
import {useEffect, useState} from "react";
import serviceLayer0Styles from "@/modules-otn/pages/otn/service/service_layer0.module.scss";
import NTPTemplate from "./ntp_template";
import DevicesTime from "./device_time";

export default function TimeManagement() {
    const {labelList} = useSelector(state => state.languageOTN);
    const items = [
        {
            key: "time_management",
            label: labelList.time_manage,
            children: <DevicesTime />
        },
        {
            key: "ntp_template",
            label: labelList.ntp_template,
            children: <NTPTemplate />
        }
    ];
    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();
    const pathReg = /(time_management|ntp_template)$/;
    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <Tabs
            items={items}
            destroyInactiveTabPane
            activeKey={currentActiveKey}
            onChange={onChange}
            className={serviceLayer0Styles.tabs}
        />
    );
}
