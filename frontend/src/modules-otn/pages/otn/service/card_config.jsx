export default {
    OA: {
        card: {x: 150, y: 100, width: 500, height: 250},
        node: [
            {id: "BAIN", group: "left", param: ["BA", "input-power"]},
            {id: "BAOUT", group: "right", param: ["BA", "output-power"]},
            {id: "PAIN", group: "right", param: ["PA", "input-power"]},
            {id: "PAOUT", group: "left", param: ["PA", "output-power"]},
            {id: "OTDR1", group: "top"},
            {id: "OTDR2", group: "top"},
            {id: "MON1", group: "top"},
            {id: "MON2", group: "top"},
            {id: "VOA", group: "voa2", param: ["PA", "target-voa-attenuation"]}
        ],
        line: [
            {source: "BAIN", target: "BAOUT"},
            {source: "PAIN", target: "PAOUT"},
            {x1: 543, y1: 203, x2: 543, y2: 164},
            {x1: 543, y1: 287, x2: 543, y2: 248},
            {x1: 475, y1: 302, x2: 505, y2: 272}
        ],
        polygon: [
            {
                x: 350,
                y: 113,
                width: 100,
                height: 100,
                points: "0,0 2,1 0,2",
                label: "BA",
                param: ["amplifier", "EDFA", "BA"]
            },
            {
                x: 350,
                y: 238,
                width: 100,
                height: 100,
                points: "0,1 2,0 2,2",
                label: "PA",
                param: ["amplifier", "EDFA", "PA"]
            },
            {
                x: 500,
                y: 204,
                width: 86,
                height: 43,
                points: "0,0 1,0 1,1 0,1",
                label: "OSC",
                param: ["component", "TRANSCEIVER", "OSC"]
            }
        ]
    },
    WSS: {
        card: {x: 150, y: 100, width: 500, height: 250},
        node: [
            {id: "BAIN", group: "linewss", param: ["BA", "input-power"]},
            {id: "BAOUT", group: "right", param: ["BA", "output-power"]},
            {id: "PAIN", group: "right", param: ["PA", "input-power"]},
            {id: "PAOUT", group: "linewss", param: ["PA", "output-power"]},
            {id: "OTDR1", group: "top"},
            {id: "OTDR2", group: "top"},
            {id: "MON1", group: "top"},
            {id: "MON2", group: "top"},
            {id: "AD1", group: "linead"},
            {id: "AD2", group: "linead"},
            {id: "AD3", group: "linead"},
            {id: "AD4", group: "linead"},
            {id: "AD5", group: "linead"},
            {id: "AD6", group: "linead"},
            {id: "AD7", group: "linead"},
            {id: "AD8", group: "linead"},
            {id: "AD9", group: "linead"},
            {id: "VOA", group: "voa2", param: ["PA", "target-voa-attenuation"]}
        ],
        line: [
            {source: "BAIN", target: "BAOUT"},
            {source: "PAIN", target: "PAOUT"},
            {source: "AD1", x2: 200, y2: 141, arrow: "both"},
            {source: "AD2", x2: 200, y2: 162, arrow: "both"},
            {source: "AD3", x2: 200, y2: 183, arrow: "both"},
            {source: "AD4", x2: 200, y2: 204, arrow: "both"},
            {source: "AD5", x2: 200, y2: 225, arrow: "both"},
            {source: "AD6", x2: 200, y2: 246, arrow: "both"},
            {source: "AD7", x2: 200, y2: 267, arrow: "both"},
            {source: "AD8", x2: 200, y2: 288, arrow: "both"},
            {source: "AD9", x2: 200, y2: 309, arrow: "both"},
            {x1: 543, y1: 203, x2: 543, y2: 164},
            {x1: 543, y1: 287, x2: 543, y2: 248},
            {x1: 475, y1: 302, x2: 505, y2: 272}
        ],
        polygon: [
            {
                x: 350,
                y: 113,
                width: 100,
                height: 100,
                points: "0,0 2,1 0,2",
                label: "BA",
                param: ["amplifier", "EDFA", "BA"]
            },
            {
                x: 350,
                y: 239,
                width: 100,
                height: 100,
                points: "0,1 2,0 2,2",
                label: "PA",
                param: ["amplifier", "EDFA", "PA"]
            },
            {
                x: 500,
                y: 204,
                width: 86,
                height: 43,
                points: "0,0 1,0 1,1 0,1",
                label: "OSC",
                param: ["component", "TRANSCEIVER", "OSC"]
            },
            {
                x: 200,
                y: 120,
                width: 40,
                height: 210,
                points: "200,330 200,120 250,150 250,300",
                label: "MUX"
            }
        ]
    },
    OLA: {
        card: {x: 150, y: 100, width: 500, height: 250},
        node: [
            {id: "LA1IN", group: "left", param: ["LA1", "input-power"]},
            {id: "LA1OUT", group: "right", param: ["LA1", "output-power"]},
            {id: "LA2IN", group: "right", param: ["LA2", "input-power"]},
            {id: "LA2OUT", group: "left", param: ["LA2", "output-power"]},
            {id: "OTDR1", group: "top"},
            {id: "OTDR2", group: "top"},
            {id: "OTDR3", group: "top"},
            {id: "OTDR4", group: "top"},
            {id: "MON1", group: "top"},
            {id: "MON2", group: "top"},
            {id: "VOA1", group: "voa1", param: ["LA1", "target-voa-attenuation"]},
            {id: "VOA2", group: "voa2", param: ["LA2", "target-voa-attenuation"]}
        ],
        line: [
            {source: "LA1IN", target: "LA1OUT"},
            {source: "LA2IN", target: "LA2OUT"},
            {x1: 543, y1: 203, x2: 543, y2: 164},
            {x1: 543, y1: 287, x2: 543, y2: 248},
            {x1: 243, y1: 164, x2: 243, y2: 203},
            {x1: 243, y1: 248, x2: 243, y2: 287},
            {x1: 475, y1: 302, x2: 505, y2: 272},
            {x1: 285, y1: 176, x2: 315, y2: 146}
        ],
        polygon: [
            {
                x: 350,
                y: 113,
                width: 100,
                height: 100,
                points: "0,0 2,1 0,2",
                label: "LA1",
                param: ["amplifier", "EDFA", "LA1"]
            },
            {
                x: 350,
                y: 238,
                width: 100,
                height: 100,
                points: "0,1 2,0 2,2",
                label: "LA2",
                param: ["amplifier", "EDFA", "LA2"]
            },
            {
                x: 200,
                y: 204,
                width: 86,
                height: 43,
                points: "0,0 1,0 1,1 0,1",
                label: "OSC1",
                param: ["component", "TRANSCEIVER", "OSC1"]
            },
            {
                x: 500,
                y: 204,
                width: 86,
                height: 43,
                points: "0,0 1,0 1,1 0,1",
                label: "OSC2",
                param: ["component", "TRANSCEIVER", "OSC2"]
            }
        ]
    },
    TFF: {
        card: {x: 250, y: 20, width: 150, height: 420},
        node: [
            {id: "CH1", group: "right"},
            {id: "CH2", group: "right"},
            {id: "CH3", group: "right"},
            {id: "CH4", group: "right"},
            {id: "MUX", group: "left"},
            {id: "PT1", group: "right"}
        ]
    },
    OP1: {
        card: {x: 250, y: 20, width: 150, height: 420},
        node: [
            {
                id: "1-APSC-OUT",
                name: "1-APSC",
                group: "linec1",
                param: ["1-APSC", 1, "common-output"]
            },
            {id: "1-APSC-IN", name: "1-APSC", group: "linec1", param: ["1-APSC", 1, "common-in"]},
            {
                id: "1-APSP-IN",
                name: "1-APSP",
                group: "linep1",
                param: ["1-APSP", 1, "line-primary-in"]
            },
            {
                id: "1-APSP-OUT",
                name: "1-APSP",
                group: "linep1",
                param: ["1-APSP", 1, "line-primary-out"]
            },
            {
                id: "1-APSS-IN",
                name: "1-APSS",
                group: "lines1",
                param: ["1-APSS", 1, "line-secondary-in"]
            },
            {
                id: "1-APSS-OUT",
                name: "1-APSS",
                group: "lines1",
                param: ["1-APSS", 1, "line-secondary-out"]
            }
        ],
        line: [
            {source: "1-APSC-IN", target: "1-APSP-OUT"},
            {source: "1-APSC-IN", target: "1-APSS-OUT"},
            {source: "1-APSP-IN", target: "1-APSC-OUT", param: [1, "PRIMARY"], active: false},
            {source: "1-APSS-IN", target: "1-APSC-OUT", param: [1, "SECONDARY"], active: false}
        ]
    },
    OP2: {
        card: {x: 250, y: 20, width: 150, height: 420},
        node: [
            {
                id: "1-APSC-OUT",
                name: "1-APSC",
                group: "linec1",
                param: ["1-APSC", 1, "common-output"]
            },
            {id: "1-APSC-IN", name: "1-APSC", group: "linec1", param: ["1-APSC", 1, "common-in"]},
            {
                id: "1-APSP-IN",
                name: "1-APSP",
                group: "linep1",
                param: ["1-APSP", 1, "line-primary-in"]
            },
            {
                id: "1-APSP-OUT",
                name: "1-APSP",
                group: "linep1",
                param: ["1-APSP", 1, "line-primary-out"]
            },
            {
                id: "1-APSS-IN",
                name: "1-APSS",
                group: "lines1",
                param: ["1-APSS", 1, "line-secondary-in"]
            },
            {
                id: "1-APSS-OUT",
                name: "1-APSS",
                group: "lines1",
                param: ["1-APSS", 1, "line-secondary-out"]
            },
            {
                id: "2-APSC-OUT",
                name: "2-APSC",
                group: "linec2",
                param: ["2-APSC", 2, "common-output"]
            },
            {id: "2-APSC-IN", name: "2-APSC", group: "linec2", param: ["2-APSC", 2, "common-in"]},
            {
                id: "2-APSP-IN",
                name: "2-APSP",
                group: "linep2",
                param: ["2-APSP", 2, "line-primary-in"]
            },
            {
                id: "2-APSP-OUT",
                name: "2-APSP",
                group: "linep2",
                param: ["2-APSP", 2, "line-primary-out"]
            },
            {
                id: "2-APSS-IN",
                name: "2-APSS",
                group: "lines2",
                param: ["2-APSS", 2, "line-secondary-in"]
            },
            {
                id: "2-APSS-OUT",
                name: "2-APSS",
                group: "lines2",
                param: ["2-APSS", 2, "line-secondary-out"]
            }
        ],
        line: [
            {source: "1-APSC-IN", target: "1-APSP-OUT"},
            {source: "1-APSC-IN", target: "1-APSS-OUT"},
            {source: "1-APSP-IN", target: "1-APSC-OUT", param: [1, "PRIMARY"], active: false},
            {source: "1-APSS-IN", target: "1-APSC-OUT", param: [1, "SECONDARY"], active: false},
            {source: "2-APSC-IN", target: "2-APSP-OUT"},
            {source: "2-APSC-IN", target: "2-APSS-OUT"},
            {source: "2-APSP-IN", target: "2-APSC-OUT", param: [2, "PRIMARY"], active: false},
            {source: "2-APSS-IN", target: "2-APSC-OUT", param: [2, "SECONDARY"], active: false}
        ]
    },
    MUX48: {
        card: {x: 100, y: 100, width: 800, height: 250},
        node: [
            {id: "CH1", group: "linemux1"},
            {id: "CH2", group: "linemux1"},
            {id: "CH3", group: "linemux1"},
            {id: "CH4", group: "linemux1"},
            {id: "CH5", group: "linemux1"},
            {id: "CH6", group: "linemux1"},
            {id: "CH7", group: "linemux1"},
            {id: "CH8", group: "linemux1"},
            {id: "CH9", group: "linemux1"},
            {id: "CH10", group: "linemux1"},
            {id: "CH11", group: "linemux1"},
            {id: "CH12", group: "linemux1"},
            {id: "CH13", group: "linemux2"},
            {id: "CH14", group: "linemux2"},
            {id: "CH15", group: "linemux2"},
            {id: "CH16", group: "linemux2"},
            {id: "CH17", group: "linemux2"},
            {id: "CH18", group: "linemux2"},
            {id: "CH19", group: "linemux2"},
            {id: "CH20", group: "linemux2"},
            {id: "CH21", group: "linemux2"},
            {id: "CH22", group: "linemux2"},
            {id: "CH23", group: "linemux2"},
            {id: "CH24", group: "linemux2"},
            {id: "CH25", group: "linemux3"},
            {id: "CH26", group: "linemux3"},
            {id: "CH27", group: "linemux3"},
            {id: "CH28", group: "linemux3"},
            {id: "CH29", group: "linemux3"},
            {id: "CH30", group: "linemux3"},
            {id: "CH31", group: "linemux3"},
            {id: "CH32", group: "linemux3"},
            {id: "CH33", group: "linemux3"},
            {id: "CH34", group: "linemux3"},
            {id: "CH35", group: "linemux3"},
            {id: "CH36", group: "linemux3"},
            {id: "CH37", group: "linemux4"},
            {id: "CH38", group: "linemux4"},
            {id: "CH39", group: "linemux4"},
            {id: "CH40", group: "linemux4"},
            {id: "CH41", group: "linemux4"},
            {id: "CH42", group: "linemux4"},
            {id: "CH43", group: "linemux4"},
            {id: "CH44", group: "linemux4"},
            {id: "CH45", group: "linemux4"},
            {id: "CH46", group: "linemux4"},
            {id: "CH47", group: "linemux4"},
            {id: "CH48", group: "linemux4"},
            {id: "NM1", group: "top"},
            {id: "MON", group: "top"},
            {id: "MUX", group: "top"}
        ]
    },
    MUX96: {
        card: {x: 100, y: 100, width: 800, height: 450},
        node: [
            {id: "CH1", group: "linemux1"},
            {id: "CH2", group: "linemux1"},
            {id: "CH3", group: "linemux1"},
            {id: "CH4", group: "linemux1"},
            {id: "CH5", group: "linemux1"},
            {id: "CH6", group: "linemux1"},
            {id: "CH7", group: "linemux1"},
            {id: "CH8", group: "linemux1"},
            {id: "CH9", group: "linemux1"},
            {id: "CH10", group: "linemux1"},
            {id: "CH11", group: "linemux1"},
            {id: "CH12", group: "linemux1"},
            {id: "CH13", group: "linemux2"},
            {id: "CH14", group: "linemux2"},
            {id: "CH15", group: "linemux2"},
            {id: "CH16", group: "linemux2"},
            {id: "CH17", group: "linemux2"},
            {id: "CH18", group: "linemux2"},
            {id: "CH19", group: "linemux2"},
            {id: "CH20", group: "linemux2"},
            {id: "CH21", group: "linemux2"},
            {id: "CH22", group: "linemux2"},
            {id: "CH23", group: "linemux2"},
            {id: "CH24", group: "linemux2"},
            {id: "CH25", group: "linemux3"},
            {id: "CH26", group: "linemux3"},
            {id: "CH27", group: "linemux3"},
            {id: "CH28", group: "linemux3"},
            {id: "CH29", group: "linemux3"},
            {id: "CH30", group: "linemux3"},
            {id: "CH31", group: "linemux3"},
            {id: "CH32", group: "linemux3"},
            {id: "CH33", group: "linemux3"},
            {id: "CH34", group: "linemux3"},
            {id: "CH35", group: "linemux3"},
            {id: "CH36", group: "linemux3"},
            {id: "CH37", group: "linemux4"},
            {id: "CH38", group: "linemux4"},
            {id: "CH39", group: "linemux4"},
            {id: "CH40", group: "linemux4"},
            {id: "CH41", group: "linemux4"},
            {id: "CH42", group: "linemux4"},
            {id: "CH43", group: "linemux4"},
            {id: "CH44", group: "linemux4"},
            {id: "CH45", group: "linemux4"},
            {id: "CH46", group: "linemux4"},
            {id: "CH47", group: "linemux4"},
            {id: "CH48", group: "linemux4"},
            {id: "CH49", group: "linemux5"},
            {id: "CH50", group: "linemux5"},
            {id: "CH51", group: "linemux5"},
            {id: "CH52", group: "linemux5"},
            {id: "CH53", group: "linemux5"},
            {id: "CH54", group: "linemux5"},
            {id: "CH55", group: "linemux5"},
            {id: "CH56", group: "linemux5"},
            {id: "CH57", group: "linemux5"},
            {id: "CH58", group: "linemux5"},
            {id: "CH59", group: "linemux5"},
            {id: "CH60", group: "linemux5"},
            {id: "CH61", group: "linemux6"},
            {id: "CH62", group: "linemux6"},
            {id: "CH63", group: "linemux6"},
            {id: "CH64", group: "linemux6"},
            {id: "CH65", group: "linemux6"},
            {id: "CH66", group: "linemux6"},
            {id: "CH67", group: "linemux6"},
            {id: "CH68", group: "linemux6"},
            {id: "CH69", group: "linemux6"},
            {id: "CH70", group: "linemux6"},
            {id: "CH71", group: "linemux6"},
            {id: "CH72", group: "linemux6"},
            {id: "CH73", group: "linemux7"},
            {id: "CH74", group: "linemux7"},
            {id: "CH75", group: "linemux7"},
            {id: "CH76", group: "linemux7"},
            {id: "CH77", group: "linemux7"},
            {id: "CH78", group: "linemux7"},
            {id: "CH79", group: "linemux7"},
            {id: "CH80", group: "linemux7"},
            {id: "CH81", group: "linemux7"},
            {id: "CH82", group: "linemux7"},
            {id: "CH83", group: "linemux7"},
            {id: "CH84", group: "linemux7"},
            {id: "CH85", group: "linemux8"},
            {id: "CH86", group: "linemux8"},
            {id: "CH87", group: "linemux8"},
            {id: "CH88", group: "linemux8"},
            {id: "CH89", group: "linemux8"},
            {id: "CH90", group: "linemux8"},
            {id: "CH91", group: "linemux8"},
            {id: "CH92", group: "linemux8"},
            {id: "CH93", group: "linemux8"},
            {id: "CH94", group: "linemux8"},
            {id: "CH95", group: "linemux8"},
            {id: "CH96", group: "linemux8"},
            {id: "NM1", group: "top"},
            {id: "MON", group: "top"},
            {id: "MUX", group: "top"}
        ]
    },
    OTDR_CARD: {
        card: {x: 150, y: 100, width: 300, height: 400},
        node: [
            {id: "OTDR1", group: "right"},
            {id: "OTDR2", group: "right"},
            {id: "OTDR3", group: "right"},
            {id: "OTDR4", group: "right"},
            {id: "OTDR5", group: "right"},
            {id: "OTDR6", group: "right"},
            {id: "OTDR7", group: "right"},
            {id: "OTDR8", group: "right"},
            {id: "1", group: "absolute", type: "OTDR"}
        ],
        line: [
            {source: "1", target: "OTDR1", hidden: true},
            {source: "1", target: "OTDR2", hidden: true},
            {source: "1", target: "OTDR3", hidden: true},
            {source: "1", target: "OTDR4", hidden: true},
            {source: "1", target: "OTDR5", hidden: true},
            {source: "1", target: "OTDR6", hidden: true},
            {source: "1", target: "OTDR7", hidden: true},
            {source: "1", target: "OTDR8", hidden: true}
        ],
        polygon: [
            {
                x: 274,
                y: 258,
                width: 43,
                height: 86,
                points: "0,0 1,0 1,1 0,1",
                label: "OTDR",
                param: ["otdr", "OTDR", "1"]
            }
        ]
    },
    OCM_CARD: {
        card: {x: 150, y: 100, width: 300, height: 400},
        node: [
            {id: "OCM1", group: "right"},
            {id: "OCM2", group: "right"},
            {id: "OCM3", group: "right"},
            {id: "OCM4", group: "right"},
            {id: "OCM5", group: "right"},
            {id: "OCM6", group: "right"},
            {id: "OCM7", group: "right"},
            {id: "OCM8", group: "right"},
            {id: "1", group: "absolute", type: "OCM"}
        ],
        line: [
            {source: "OCM1", target: "1", hidden: true},
            {source: "OCM2", target: "1", hidden: true},
            {source: "OCM3", target: "1", hidden: true},
            {source: "OCM4", target: "1", hidden: true},
            {source: "OCM5", target: "1", hidden: true},
            {source: "OCM6", target: "1", hidden: true},
            {source: "OCM7", target: "1", hidden: true},
            {source: "OCM8", target: "1", hidden: true}
        ],
        polygon: [
            {
                x: 274,
                y: 258,
                width: 43,
                height: 86,
                points: "0,0 1,0 1,1 0,1",
                label: "OCM",
                param: ["channel-monitor", "OCM", "1"]
            }
        ]
    },
    PTMW: {
        card: {x: 150, y: 100, width: 500, height: 250},
        node: [
            {id: "LINE", group: "left"},
            {id: "SIG", group: "left"},
            {id: "C60", group: "top"},
            {id: "S60", group: "top"},
            {id: "S61", group: "top"}
        ],
        line: [
            {source: "S60", x2: 400, y2: 150, arrow: "both"},
            {source: "S61", x2: 567, y2: 150, arrow: "both"},
            {x1: 429, y1: 200, x2: 540, y2: 200, arrow: "both"}
        ],
        polygon: [
            {
                x: 370,
                y: 150,
                width: 60,
                height: 90,
                points: "0,0 1,0 1,1 0,1",
                label: "SFP",
                param: ["component", "TRANSCEIVER", "S60"]
            },
            {
                x: 540,
                y: 150,
                width: 60,
                height: 90,
                points: "0,0 1,0 1,1 0,1",
                label: "SFP",
                param: ["component", "TRANSCEIVER", "S61"]
            }
        ]
    },
    PTME: {
        card: {x: 150, y: 100, width: 500, height: 250},
        node: [
            {id: "LINE", group: "left"},
            {id: "SIG", group: "left"},
            {id: "C61", group: "top"},
            {id: "S60", group: "top"},
            {id: "S61", group: "top"}
        ],
        line: [
            {source: "S60", x2: 400, y2: 150, arrow: "both"},
            {source: "S61", x2: 567, y2: 150, arrow: "both"},
            {x1: 429, y1: 200, x2: 540, y2: 200, arrow: "both"}
        ],
        polygon: [
            {
                x: 370,
                y: 150,
                width: 60,
                height: 90,
                points: "0,0 1,0 1,1 0,1",
                label: "SFP",
                param: ["component", "TRANSCEIVER", "S60"]
            },
            {
                x: 540,
                y: 150,
                width: 60,
                height: 90,
                points: "0,0 1,0 1,1 0,1",
                label: "SFP",
                param: ["component", "TRANSCEIVER", "S61"]
            }
        ]
    }
};
