.dockLayout {
    flex: 1;
    display: flex;
    position: relative;
    padding-bottom:20px;

    :global {
        .dock-tab {
             height: auto;
            background: #fff;
            border: 0;
            margin: 0;
        }

        .dock-tab-btn {
            padding: 4px 16px;
        }

        .dock-panel-max-btn,
        .dock-panel-min-btn {
            display: none;
        }

        .dock-hbox {
            gap: 10px;
        }

        .dock-vbox {
            gap: 1.9px;
        }
        .dock-panel {
            border: none !important;
        }
        .dock-panel, .dock-top {
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .dock-bar, .dock-top {
            background: #fff !important;
        }

        .dock-bar {
            font-size: 18px;
            border-bottom: 0 !important;
        }

        .drag-initiator {
            padding: 10px 0;
        }


        .dock-nav-wrap {
            padding-left: 8px;
        }

        .dock-tab {
            margin-right: 0;
        }

        .drag-initiator:not(.dock-divider) {
            padding-top: 0;
        }

        .dock-top {
            &>.drag-initiator {
                padding: 0;
                color: #929A9E;

                .dock-tab-btn {
                    padding: 24px  16px;

                    .drag-initiator {
                        padding-bottom: 8px;
                    }
                }

                .dock-tab-active {
                    margin-left: 0;
                }
            }
        }

        div[id*="service_list"] {
            padding: 0 24px;

            &>div {
                flex: 1;
            }

            .drag-initiator {
                color: #212519 !important;
                font-weight: 600 !important;
                border-bottom: none;
            }

            .ant-tree-node-selected {
                color: var(--primary-color);
            }
        }
    }

}
.contentWrap {
    width: 100%;
    height: 100%;
    display: flex;
    padding: 0 24px;
}

