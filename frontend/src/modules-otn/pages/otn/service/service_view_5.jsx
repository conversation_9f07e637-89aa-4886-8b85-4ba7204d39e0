import {useEffect, useRef, useState} from "react";
import {Graph} from "@antv/x6";
import {useRequest} from "ahooks";
import {<PERSON><PERSON>, <PERSON><PERSON>, message, Tooltip} from "antd";
import {useSelector} from "react-redux";
import {apiGetOpticalpower, objectGet} from "@/modules-otn/apis/api";
import {ALARM_COLOR, OPERATIONAL_MODE_MAP} from "@/modules-otn/utils/util";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {openModalEdit} from "@/modules-otn/components/form/edit_form";
import {TooltipTool} from "@/modules-otn/pages/otn/service/component/graph_tools";
import {Export} from "@antv/x6-plugin-export";
import {debounce} from "lodash";

const ServiceView5 = props => {
    const {
        tableFilter: {provision, type, typeClient, nodeData},
        updateServiceView
    } = useSelector(state => state.map);
    const {neNameMap} = useSelector(state => state.neName);
    const [service, setService] = useState(provision);
    const [powers, setPowers] = useState({});
    const readyOnlyRight = useUserRight();
    const [offLineCards, setOffLineCards] = useState([]);
    const [portTooltipData, setPortTooltipData] = useState([]);
    const portTipRef = useRef();
    portTipRef.current = document.querySelector(".x6-tooltip");

    const {labelList} = useSelector(state => state.languageOTN);
    // 销毁时执行，销毁提示告警
    useEffect(() => {
        return () => {
            unDraw();
        };
    }, []);

    useEffect(() => {
        setService(provision);
        handleOnClick();
    }, [provision]);
    const {alarms, dataChanged} = useSelector(state => state.notification);

    const {loading: powerLoad, runAsync} = useRequest(apiGetOpticalpower, {manual: true});
    const refDIV = useRef();
    const refGraph = useRef();
    const refAlarm = useRef();
    refAlarm.current = alarms;

    const sizeOSC = {width: 85, height: 15, x: 37.5, y: 70};
    // 在右侧的OSC
    const sizeOSCR = {width: 85, height: 15, x: 187.5, y: 70};
    const size = {width: 300, height: 150};
    const gap = 460;
    const initX = 100;
    const yUpper = 80;
    const yMiddle = 200;
    const yLower = 320;

    const delayHandleOnClick = () => {
        setTimeout(handleOnClick, 3000);
    };

    const drawNode = (node, x, y, graph, olpDouble) => {
        const {
            portLeftBI,
            portLeftIn,
            portLeftOut,
            portLeftArr,
            portLeftGroup,
            portRightGroup,
            portRightBI,
            portRightIn,
            portRightOut,
            portRightArr,
            ramanPost,
            ramanPre,
            ramanPostLeft,
            ramanPostRight,
            ramanPreLeft,
            ramanPreRight,
            id,
            name,
            card,
            vendorType
        } = node;
        // const powerInfo = service?.power?.[ne_id] ?? {};
        // ...portLeftGroup?.map(item => ({suffix: "BI", value: item}))
        const itemsLeft = [
            {suffix: "BI", value: portLeftBI},
            {suffix: "In", value: portLeftIn},
            {suffix: "Out", value: portLeftOut},
            {suffix: "BI", value: portLeftArr?.[0].portLeftBI},
            {suffix: "BI", value: portLeftArr?.[1].portLeftBI},
            ...(portLeftGroup?.map(item => ({suffix: "BI", value: item})) || [])
        ]
            .filter(i => i.value !== undefined)
            .map(item => {
                const text = `${item.value.split("-").slice(3).join("-")}`;
                return {
                    id: item.value,
                    group: "left",
                    attrs: {
                        text: {text}
                    }
                };
            });

        const itemsRight = [
            {suffix: "BI", value: portRightBI},
            {suffix: "Out", value: portRightOut},
            {suffix: "In", value: portRightIn},
            {suffix: "BI", value: portRightArr?.[0]},
            {suffix: "BI", value: portRightArr?.[1]},
            ...(portRightGroup?.map(item => ({suffix: "BI", value: item})) || [])
        ]
            .filter(i => i.value !== undefined)
            .map(item => {
                const text = `${item.value.split("-").slice(3).join("-")}`;
                return {
                    id: item.value,
                    group: "right",
                    attrs: {
                        text: {text}
                    }
                };
            });

        const cardNode = {
            id,
            label: `${name}\n${card}(${vendorType})`,
            width: size.width,
            // height: type === "OLP" ? 450 : size.height,
            height: size.height,
            x,
            y,
            attrs: {
                body: {
                    rx: 5,
                    ry: 5,
                    strokeWidth: 3,
                    stroke: "#31d0c6"
                },
                label: {
                    fontSize: 13,
                    // y: type === "OLP" ? 245 : 95
                    y: 95
                }
            },
            ports: {
                groups: {
                    left: {
                        attrs: {
                            circle: {
                                r: 6,
                                strokeWidth: 2,
                                fill: "#fff",
                                cursor: "pointer",
                                stroke: "#31d0c6"
                            }
                        }
                    },
                    right: {
                        position: "right",
                        label: {position: "right"},
                        attrs: {
                            circle: {
                                r: 6,
                                strokeWidth: 2,
                                fill: "#fff",
                                cursor: "pointer",
                                stroke: "#31d0c6"
                            }
                        }
                    },
                    // 右侧voa
                    voa0: {
                        position: {name: "absolute", args: {x: 200, y: 113}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 8, stroke: "#000", strokeWidth: 1, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    voa0_1: {
                        position: {name: "absolute", args: {x: 215, y: 93}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    voa0_2: {
                        position: {name: "absolute", args: {x: 188, y: 127}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    line0_1: {
                        position: {name: "absolute", args: {x: 230, y: 39}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    line0_2: {
                        position: {name: "absolute", args: {x: 230, y: 113}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    // 左侧voa
                    voa1: {
                        position: {name: "absolute", args: {x: 110, y: 39}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 8, stroke: "#000", strokeWidth: 1, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    voa1_1: {
                        position: {name: "absolute", args: {x: 125, y: 19}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    voa1_2: {
                        position: {name: "absolute", args: {x: 98, y: 53}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    line1_1: {
                        position: {name: "absolute", args: {x: 80, y: 39}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    line1_2: {
                        position: {name: "absolute", args: {x: 80, y: 113}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    // tff voa 左侧
                    voa2: {
                        position: {name: "absolute", args: {x: 80, y: 36}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 8, stroke: "#000", strokeWidth: 1, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    voa2_1: {
                        position: {name: "absolute", args: {x: 95, y: 16}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    voa2_2: {
                        position: {name: "absolute", args: {x: 68, y: 50}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    line2_1: {
                        position: {name: "absolute", args: {x: 50, y: 36}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    line2_2: {
                        position: {name: "absolute", args: {x: 50, y: 110}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    // tff voa 右侧
                    voa3: {
                        position: {name: "absolute", args: {x: 220, y: 36}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 8, stroke: "#000", strokeWidth: 1, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    voa3_1: {
                        position: {name: "absolute", args: {x: 235, y: 16}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    voa3_2: {
                        position: {name: "absolute", args: {x: 208, y: 50}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    line3_1: {
                        position: {name: "absolute", args: {x: 190, y: 36}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    line3_2: {
                        position: {name: "absolute", args: {x: 190, y: 110}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    // asisstent port Wss  wss辅助端口  MUX
                    aPort0_1: {
                        position: {name: "absolute", args: {x: 0, y: 39}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    aPort0_2: {
                        position: {name: "absolute", args: {x: 0, y: 113}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    aPort0_3: {
                        position: {name: "absolute", args: {x: 0, y: 75}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    // 右侧
                    aPort1_1: {
                        position: {name: "absolute", args: {x: 300, y: 39}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    aPort1_2: {
                        position: {name: "absolute", args: {x: 300, y: 113}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    aPort1_3: {
                        position: {name: "absolute", args: {x: 300, y: 75}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    }
                },
                items: [...itemsLeft, ...itemsRight]
            }
        };
        const _cardNode = graph.addNode(cardNode);
        let ramanPreExist = false;
        let ramanPostExist = false;
        if (olpDouble) {
            if (
                [ramanPostLeft, ramanPreLeft].some(i => i !== undefined) ||
                (itemsLeft.length === 2 && [ramanPost, ramanPre].some(i => i !== undefined))
            ) {
                const inputName = ramanPreLeft ? "ramanPreLeft" : "ramanPre";
                const outputName = ramanPostLeft ? "ramanPostLeft" : "ramanPost";
                const ramanUpName = itemsLeft[0].id.endsWith("IN") ? outputName : inputName;
                const ramanDownName = itemsLeft?.[1]?.id?.endsWith("IN") ? outputName : inputName;
                if (ramanPreLeft || (ramanPre && !ramanPreExist)) {
                    drawRamanNode({left: true, up: false}, _cardNode, graph, ramanDownName, ramanPreLeft || ramanPre);
                }
                if (ramanPostLeft || (ramanPost && !ramanPostExist)) {
                    drawRamanNode({left: true, up: true}, _cardNode, graph, ramanUpName, ramanPostLeft || ramanPost);
                }
            }
        } else {
            if (
                [ramanPostRight, ramanPreRight].some(i => i !== undefined) ||
                (itemsRight.length === 2 && [ramanPost, ramanPre].some(i => i !== undefined))
            ) {
                const inputName = ramanPreRight ? "ramanPreRight" : "ramanPre";
                const outputName = ramanPostRight ? "ramanPostRight" : "ramanPost";
                const ramanUpName = itemsRight[0].id.endsWith("IN") ? outputName : inputName;
                const ramanDownName = itemsRight[1].id.endsWith("IN") ? outputName : inputName;
                if (ramanPreRight || ramanPre) {
                    if (ramanPre) ramanPreExist = true;
                    drawRamanNode({left: false, up: true}, _cardNode, graph, ramanUpName, ramanPreRight || ramanPre);
                }
                if (ramanPostRight || ramanPost) {
                    if (ramanPost) ramanPostExist = true;
                    drawRamanNode(
                        {left: false, up: false},
                        _cardNode,
                        graph,
                        ramanDownName,
                        ramanPostRight || ramanPost
                    );
                }
            }
            if (
                [ramanPostLeft, ramanPreLeft].some(i => i !== undefined) ||
                (itemsLeft.length === 2 && [ramanPost, ramanPre].some(i => i !== undefined))
            ) {
                const inputName = ramanPreLeft ? "ramanPreLeft" : "ramanPre";
                const outputName = ramanPostLeft ? "ramanPostLeft" : "ramanPost";
                const ramanUpName = itemsLeft[0].id.endsWith("IN") ? outputName : inputName;
                const ramanDownName = itemsLeft?.[1]?.id?.endsWith("IN") ? outputName : inputName;
                if (ramanPreLeft || (ramanPre && !ramanPreExist)) {
                    drawRamanNode({left: true, up: false}, _cardNode, graph, ramanDownName, ramanPreLeft || ramanPre);
                }
                if (ramanPostLeft || (ramanPost && !ramanPostExist)) {
                    drawRamanNode({left: true, up: true}, _cardNode, graph, ramanUpName, ramanPostLeft || ramanPost);
                }
            }
        }
    };

    const drawClientNode = (node, x, y, graph) => {
        const {id, param, type} = node;
        const {label, port, name, card, vendorType} = param;
        const {left, right, data} = port;
        const items = [];
        // data表示OCH的显示数据
        if (data) {
            items.push({
                id: `${id}-1`,
                group: "b1",
                attrs: {
                    text: {text: neNameMap[data[0].ne]}
                }
            });
            items.push({
                id: `${id}-2`,
                group: "b2",
                attrs: {
                    text: {text: neNameMap[data[1].ne]}
                }
            });
        }
        // 添加左右端口
        if (left?.length > 0) {
            left.forEach(l => {
                items.push({
                    id: l,
                    group: "left",
                    attrs: {
                        text: {text: l.split("/")[1]}
                    }
                });
            });
        }
        if (right?.length > 0) {
            right.forEach(r => {
                items.push({
                    id: r,
                    group: "right",
                    attrs: {
                        text: {text: r.split("/")[1]}
                    }
                });
            });
        }
        if (right?.length > 1 || left?.length > 1) {
            const text = `${name}\n${card}(${vendorType})`;
            items.push({
                id: "bottom",
                group: "bottom",
                attrs: {
                    text: {text}
                }
            });
        }
        // type === "component" ? 600 :
        const cardNode = {
            id,
            label,
            width: size.width,
            height: size.height,
            x,
            y,
            attrs: {
                body: {
                    rx: 5,
                    ry: 5,
                    strokeWidth: 3,
                    stroke: "#31d0c6"
                },
                label: {
                    fontSize: 13
                }
            },
            param: {type, label},
            ports: {
                groups: {
                    left: {
                        attrs: {
                            circle: {
                                r: 6,
                                strokeWidth: 2,
                                fill: "#fff",
                                cursor: "pointer",
                                stroke: "#31d0c6"
                            }
                        }
                    },
                    right: {
                        position: "right",
                        label: {position: "right"},
                        attrs: {
                            circle: {
                                r: 6,
                                strokeWidth: 2,
                                fill: "#fff",
                                cursor: "pointer",
                                stroke: "#31d0c6"
                            }
                        }
                    },
                    bottom: {
                        position: "bottom",
                        label: {
                            position: {
                                name: "bottom"
                            }
                        },
                        attrs: {
                            circle: {
                                r: 0,
                                magnet: true,
                                strokeWidth: 0,
                                fill: "#fff"
                            }
                        }
                    },
                    // 绝对定位左下角/右下角
                    b1: {
                        position: {name: "absolute", args: {x: 0, y: 150}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    },
                    b2: {
                        position: {name: "absolute", args: {x: 300, y: 150}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff", cursor: "pointer"}
                        }
                    }
                },
                items
            }
        };
        const _cardNode = graph.addNode(cardNode);
        // 画OLP的保护 保证olp保护children[0] 和 [1] 为第一第二子节点，便于设置保护
        if (right?.length > 1) {
            [
                {
                    source: {cell: id, port: left[0]},
                    target: {cell: id, port: right[0]},
                    attrs: {
                        line: {stroke: "#9af", strokeWidth: 1, sourceMarker: null, targetMarker: null}
                    }
                },
                {
                    source: {cell: id, port: left[0]},
                    target: {cell: id, port: right[1]},
                    attrs: {
                        line: {stroke: "#9af", strokeWidth: 1, sourceMarker: null, targetMarker: null}
                    }
                }
            ].forEach(edge => {
                graph.addEdge(edge);
            });
        } else if (left?.length > 1) {
            [
                {
                    source: {cell: id, port: right[0]},
                    target: {cell: id, port: left[0]},
                    attrs: {
                        line: {stroke: "#9af", strokeWidth: 1, sourceMarker: null, targetMarker: null}
                    }
                },
                {
                    source: {cell: id, port: right[0]},
                    target: {cell: id, port: left[1]},
                    attrs: {
                        line: {stroke: "#9af", strokeWidth: 1, sourceMarker: null, targetMarker: null}
                    }
                }
            ].forEach(edge => {
                graph.addEdge(edge);
            });
        }
        const _x = _cardNode.position().x;
        const _y = _cardNode.position().y;
        // 给OCH添加子节点 TRANSCEIVER
        if (data) {
            // 添加的图形宽高 固定值
            const cub = {width: 20, height: 10, points: "0,0 1,0 1,1 0,1"};
            [
                {
                    id: `${left[0].split("/")[0]},${left[0].split("/")[1]?.replace(/^PORT/, "TRANSCEIVER")}`,
                    x: 9,
                    y: 70,
                    param: {type: "TRANSCEIVER"},
                    ...cub
                },
                {
                    id: `${right[0].split("/")[0]},${right[0].split("/")[1]?.replace(/^PORT/, "TRANSCEIVER")}`,
                    x: 270,
                    y: 70,
                    param: {type: "TRANSCEIVER"},
                    ...cub
                }
            ].forEach(p => {
                const node = graph.addNode({
                    shape: "polygon",
                    ...p,
                    x: p.x + _x,
                    y: p.y + _y,
                    attrs: {
                        body: {stroke: "#31d0c6"}
                    }
                });
                _cardNode.addChild(node);
            });
        } else {
            // const apsCard = {
            //     id: `${ne_id}/${apsName}`,
            //     label: apsName,
            //     param: {type: "APS"},
            //     x: _x + 100,
            //     y: _y + 65,
            //     width: 100,
            //     height: 20,
            //     attrs: {
            //         body: {stroke: "#31d0c6"}
            //     }
            // };
            // const node = graph.addNode(apsCard);
            // _cardNode.addChild(node);
        }
    };

    const registerPortTooltip = (container, text) => {
        container.addEventListener("mouseenter", e => {
            const tooltip = document.querySelector(".x6-tooltip");
            const content = tooltip?.querySelector(".ant-tooltip-inner");
            if (content) {
                setPortTooltipData(text);
                // 控制显示位置
                tooltip.style.left = `${e.clientX - content.offsetWidth / 2}px`;
                tooltip.style.top = `${e.clientY + 20}px`;
            }
        });

        container.addEventListener("mouseout", () => {
            const tooltip = document.querySelector(".x6-tooltip");
            tooltip.style.left = "-1000px";
            tooltip.style.top = "-1000px";
        });
    };

    const draw = () => {
        refGraph.current?.dispose();
        Graph.registerNodeTool("tooltip", TooltipTool, true);
        TooltipTool.config({
            tagName: "div",
            isSVGElement: false
        });
        refGraph.current = new Graph({
            container: refDIV.current,
            panning: true,
            autoResize: true,
            onPortRendered({contentContainer, port, node}) {
                const text = node.portProp(port.id, "tip");
                if (text) {
                    registerPortTooltip(contentContainer, text);
                }
            },

            interacting(cellView) {
                if (cellView.isEdgeView()) {
                    return {edgeMovable: false};
                }
                if (cellView.cell.getProp().parent) {
                    return {nodeMovable: false};
                }
                return true;
            },
            mousewheel: true
        });
        const graph = refGraph.current;
        graph.use(new Export());
        let x = initX;
        let y = yMiddle;
        if (type === "client") {
            const {nodeList, edgeList} = service;
            nodeList.forEach((cards, x_gap) => {
                cards.forEach((card, y_gap) => {
                    let temp = 0;
                    if (cards.length > 1) {
                        temp = y / 2;
                    }
                    // + (x_gap === 2 ? 300 : 0)
                    drawClientNode(card, x + gap * x_gap, y * y_gap - temp, graph);
                });
            });
            edgeList.forEach(edge => {
                graph.addEdge(edge);
            });

            graph.on("node:dblclick", ({e, node}) => {
                const [ne_id, card] = node.id.split("/");
                const {type, label} = node.store.data.param;
                // 通过判断是否存在port属性区分点击node或port
                if (e.target.hasAttribute("port")) {
                    const [ne_id, port] = e.target.getAttribute("port").split("/");
                    openModalEdit(
                        "component",
                        port,
                        "5",
                        `ne:5:component:${ne_id}:${port}`,
                        ne_id,
                        null,
                        delayHandleOnClick,
                        readyOnlyRight.disabled
                    );
                } else if (type === "TRANSCEIVER") {
                    const _node = node.id?.split(",");
                    openModalEdit(
                        "component",
                        _node[1],
                        "5",
                        `ne:5:component:${ne_id}:${_node}`,
                        _node[0],
                        null,
                        delayHandleOnClick,
                        readyOnlyRight.disabled
                    );
                } else if (type === "protection") {
                    openModalEdit(
                        "component",
                        card,
                        "5",
                        `ne:5:component:${ne_id}:${card}`,
                        ne_id,
                        null,
                        delayHandleOnClick,
                        readyOnlyRight.disabled
                    );
                } else if (type === "APS") {
                    //
                    openModalEdit(
                        "aps",
                        card,
                        "5",
                        `ne:5:aps-module:${ne_id}:${card}`,
                        ne_id,
                        null,
                        delayHandleOnClick,
                        readyOnlyRight.disabled
                    );
                } else {
                    // setFindInfo({type: "UN_SELECTED"});
                    const _label = label.replace("OCH: ", "");
                    const obj = {
                        type: "och",
                        dbKey: `nms:provision:${_label}`,
                        name: _label
                    };
                    props.clientSelectTransfer(obj);
                }
            });
            graph.centerContent();
            return;
        }
        let node = service.startNode;
        const ramanGap = 160;
        function drawRamanGap(node, olpDouble) {
            const {
                portLeftIn,
                portLeftOut,
                portLeftArr,
                portLeftGroup,
                portRightGroup,
                portRightIn,
                portRightOut,
                portRightArr,
                ramanPost,
                ramanPre,
                ramanPostLeft,
                ramanPostRight,
                ramanPreLeft,
                ramanPreRight
            } = node;
            if ([ramanPostLeft, ramanPreLeft].some(i => i !== undefined)) {
                x += ramanGap;
            } else if (
                [portLeftIn, portLeftOut, portLeftArr, portLeftGroup].some(i => i !== undefined) &&
                [ramanPost, ramanPre].some(i => i !== undefined)
            ) {
                x += ramanGap;
            }

            drawNode(node, x, y, graph, olpDouble);

            if ([ramanPostRight, ramanPreRight].some(i => i !== undefined)) {
                x += ramanGap;
            } else if (
                [portRightGroup, portRightIn, portRightOut, portRightArr].some(i => i !== undefined) &&
                [ramanPost, ramanPre].some(i => i !== undefined)
            ) {
                x += ramanGap;
            }
        }
        while (node) {
            drawRamanGap(node);

            let node1;
            let node2;
            const tmpX = x;

            if (node.next1 || node.next2) {
                let len1 = 0;
                let len2 = 0;
                const ramanR1 = {};
                const ramanR2 = {};
                let hasOlp1 = false;
                let hasOlp2 = false;
                let count;
                if (node.next1) {
                    node1 = node.next1;
                    count = 0;
                    while (true) {
                        ++len1;
                        const {ramanPost, ramanPre, ramanPostLeft, ramanPostRight, ramanPreLeft, ramanPreRight} = node1;
                        if (
                            [ramanPost, ramanPre].some(i => i !== undefined) ||
                            [ramanPostLeft, ramanPreLeft].some(i => i !== undefined)
                        ) {
                            ramanR1[count - 1] = ramanR1[count - 1] ? 2 : 1;
                        }
                        if ([ramanPostRight, ramanPreRight].some(i => i !== undefined)) {
                            ramanR1[count + 1] = ramanR1[count + 1] ? 2 : 1;
                        }
                        if (!ramanR1[count]) ramanR1[count] = 0;
                        if (
                            (node1.next?.card?.startsWith("OLP") && node1.next?.next?.card?.startsWith("OLP")) ||
                            (node1.next?.card?.startsWith("OLP") && node1?.card?.startsWith("OLP"))
                        ) {
                            hasOlp1 = false;
                        } else hasOlp1 = !!node1.next?.card?.startsWith("OLP");
                        if (hasOlp1 || !node1.next) break;
                        node1 = node1.next;
                        ++count;
                    }
                }
                if (node.next2) {
                    node2 = node.next2;
                    count = 0;
                    while (true) {
                        ++len2;
                        const {ramanPost, ramanPre, ramanPostLeft, ramanPostRight, ramanPreLeft, ramanPreRight} = node2;
                        if (
                            [ramanPost, ramanPre].some(i => i !== undefined) ||
                            [ramanPostLeft, ramanPreLeft].some(i => i !== undefined)
                        ) {
                            ramanR2[count - 1] = ramanR2[count - 1] ? 2 : 1;
                        }
                        if ([ramanPostRight, ramanPreRight].some(i => i !== undefined)) {
                            ramanR2[count + 1] = ramanR2[count + 1] ? 2 : 1;
                        }
                        if (!ramanR2[count]) ramanR2[count] = 0;
                        if (
                            (node2.next?.card?.startsWith("OLP") && node2.next?.next?.card?.startsWith("OLP")) ||
                            (node2.next?.card?.startsWith("OLP") && node2?.card?.startsWith("OLP"))
                        ) {
                            hasOlp2 = false;
                        } else hasOlp2 = !!node2.next?.card?.startsWith("OLP");
                        if (hasOlp2 || !node2.next) break;
                        node2 = node2.next;
                        ++count;
                    }
                }
                if (node.next1) {
                    node1 = node.next1;
                    x = tmpX + gap;
                    y = yUpper;
                    count = 0;
                    let olpDouble = false;
                    if (node1 && node1?.card?.startsWith("OLP") && node1?.next?.card?.startsWith("OLP")) {
                        olpDouble = true;
                    } else olpDouble = !node1?.card?.startsWith("OLP");
                    while (node1 && olpDouble) {
                        drawRamanGap(node1, node1.next?.card?.startsWith("OLP"));
                        if (node1.next?.card?.startsWith("OLP-")) {
                            if (
                                !(
                                    (node1.next?.card?.startsWith("OLP") &&
                                        node1.next?.next?.card?.startsWith("OLP")) ||
                                    (node1.next?.card?.startsWith("OLP") && node1?.card?.startsWith("OLP"))
                                )
                            ) {
                                y = yMiddle;
                            }
                        }
                        node1 = node1.next;
                        ++count;
                        if (count === len1 - 1 && len2 > len1) {
                            x += gap * (1 + len2 - len1);
                        } else {
                            x += gap;
                        }
                        if (ramanR1[count] < ramanR2[count]) {
                            x += ramanGap * (ramanR2[count] - ramanR1[count]);
                        }
                    }
                }
                if (node.next2) {
                    node2 = node.next2;
                    x = tmpX + gap;
                    y = yLower;
                    count = 0;
                    let olpDouble = false;
                    if (node2 && node2?.card?.startsWith("OLP") && node2?.next?.card?.startsWith("OLP")) {
                        olpDouble = true;
                    } else olpDouble = !node2?.card?.startsWith("OLP");
                    while (node2 && olpDouble) {
                        drawRamanGap(node2, node2.next?.card?.startsWith("OLP"));
                        if (node2.next?.card?.startsWith("OLP-")) {
                            if (
                                !(
                                    (node2.next?.card?.startsWith("OLP") &&
                                        node2.next?.next?.card?.startsWith("OLP")) ||
                                    (node2.next?.card?.startsWith("OLP") && node2?.card?.startsWith("OLP"))
                                )
                            ) {
                                y = yMiddle;
                            }
                        }
                        node2 = node2.next;
                        ++count;
                        if (count === len2 - 1 && len1 > len2) {
                            x += gap * (1 + len1 - len2);
                        } else {
                            x += gap;
                        }
                        if (ramanR1[count] > ramanR2[count]) {
                            x += ramanGap * (ramanR1[count] - ramanR2[count]);
                        }
                    }
                }

                node = node2?.card?.startsWith("OLP") ? node2 : null;
                if (node1?.card?.startsWith("OLP")) node = node1;
                y = yMiddle;
            } else {
                node = node.next;
                x += gap;
            }
        }
        service.edgeList.forEach(edge => {
            graph.addEdge(edge);
        });
        // graph.scaleContentToFit();
        const graphNode = refGraph.current?.getNodes();
        if (graphNode?.length === 2 && graphNode?.filter(f => f.id.includes("LINECARD"))?.length === 2) {
            const [l1, l2] = graphNode;
            const gap = 200;
            l1.position(l1.position().x - gap, l1.position().y);
            l2.position(l2.position().x + gap, l2.position().y);
        }
        graphNode?.map(item => {
            drawLinePort(item, graph, graphNode);
        });
        //  contextmenu
        graph.on("node:dblclick", ({e, node}) => {
            const ne_id = `${node.id?.split(":")[0]}:${node.id?.split(":")[1]}`;
            if (node.id.includes("raman")) return;
            if (ne_id?.length < 35 && !node.id?.endsWith("_inner")) {
                let _id = node.id?.replace(/\(([^)]+)\)/g, "");
                let _node = _id?.split(":")[2];
                // 通过判断是否存在port属性区分点击node或port
                if (e.target.hasAttribute("port")) {
                    const port = e.target.getAttribute("port");
                    // 忽略TFF内部组件端口
                    if (["r", "PT1", "CH1", "CH2", "CH3", "CH4"].includes(port) || port.includes("VOA")) return;
                    openModalEdit(
                        "component",
                        port,
                        "5",
                        `ne:5:component:${ne_id}:${port}`,
                        ne_id,
                        null,
                        delayHandleOnClick,
                        readyOnlyRight.disabled
                    );
                } else {
                    if (
                        _node.startsWith("TRANSCEIVER") ||
                        node.getChildren() ||
                        _node.endsWith("OSC") ||
                        _node.endsWith("OSC1") ||
                        _node.endsWith("OSC2")
                    ) {
                        if (_node.endsWith("OSC")) {
                            _id = _id?.replace("PORT", "TRANSCEIVER");
                            _node = _id?.split(":")[2];
                        }
                        openModalEdit(
                            "component",
                            _node,
                            "5",
                            `ne:5:component:${_id}`,
                            ne_id,
                            null,
                            delayHandleOnClick,
                            readyOnlyRight.disabled
                        );
                        return;
                    }
                    if (_node?.startsWith("WSS") && _node?.endsWith("mux")) return;
                    openModalEdit(
                        "amplifier",
                        _node,
                        "5",
                        `ne:5:amplifier:${_id}`,
                        ne_id,
                        null,
                        delayHandleOnClick,
                        readyOnlyRight.disabled
                    );
                    // openModalEdit("component", _node, `ne:5:component:${_id}`, ne_id);
                }
            }
        });
        graph.centerContent();
    };

    const drawRamanNode = (direction, item, graph, name, ne) => {
        const x = item.position().x + (direction.left ? -150 : 390);
        const y = item.position().y + (direction.up ? 8 : 84);
        const ramanPort = direction.left ? "left" : "right";
        const innerPort = !direction.left ? "left" : "right";
        const id = `${item.id}_${name}`;
        const _name = name.includes("Pre") ? "Co-RFA" : "RFA";
        const lefts = item.getPortsByGroup("left");
        const rights = item.getPortsByGroup("right");
        const portList = direction.left ? lefts : rights;
        const ramanNode = {
            id,
            x,
            y,
            width: 60,
            height: 60,
            label: `${neNameMap[ne]}(${_name})`,
            attrs: {
                ramanNe: ne,
                body: {
                    rx: 5,
                    ry: 5,
                    strokeWidth: 3,
                    stroke: "#31d0c6"
                },
                label: {
                    fontSize: 13,
                    refX: 0.5,
                    refY: direction.up ? "0" : "115%",
                    refY2: direction.up ? -6 : 0,
                    textAnchor: "middle",
                    textVerticalAnchor: direction.up ? "bottom" : "top"
                }
            },
            ports: {
                groups: {
                    left: {
                        attrs: {
                            circle: {
                                r: 6,
                                strokeWidth: 2,
                                fill: "#fff",
                                cursor: "pointer",
                                stroke: "#31d0c6"
                            }
                        }
                    },
                    right: {
                        position: "right",
                        label: {position: "right"},
                        attrs: {
                            circle: {
                                r: 6,
                                strokeWidth: 2,
                                fill: "#fff",
                                cursor: "pointer",
                                stroke: "#31d0c6"
                            }
                        }
                    }
                },
                items: [
                    {
                        id: "raman",
                        group: ramanPort
                    },
                    {
                        id: "raman_inner",
                        group: innerPort
                    }
                ]
            }
        };
        const _ramanNode = graph.addNode(ramanNode);
        item.addChild(_ramanNode);
        const port = direction.up ? portList[0] : portList[1];
        const portDirect = port?.attrs?.text?.text?.includes("OUT");
        if (portDirect) {
            graph.addEdge({
                source: {cell: item.id, port: port.id},
                target: {cell: id, port: "raman_inner"},
                attrs: {
                    line: {
                        stroke: "#9af",
                        strokeWidth: 1,
                        targetMarker: {
                            name: "block"
                        }
                    }
                }
            });
        } else {
            graph.addEdge({
                source: {cell: id, port: "raman_inner"},
                target: {cell: item.id, port: port.id},
                attrs: {
                    line: {
                        stroke: "#9af",
                        strokeWidth: 1,
                        targetMarker: {
                            name: "block"
                        }
                    }
                }
            });
        }
    };

    const drawLinePort = (item, graph, graphNode) => {
        const type = item?.id.split(":")[2].split("-")[0];
        const isRaman = item.id.includes("raman");
        const portType = item.port.ports[0]?.id?.split("-")[3];
        const portTypeR = item.port.ports[1]?.id?.split("-")[3];
        const _x = item.position().x;
        const _y = item.position().y;
        if ((type === "OA" || type === "OLA") && !isRaman) {
            if (!item.port.ports[3]?.id && item.port.ports[0]?.group === "right") {
                [
                    {
                        group: "aPort0_1",
                        id: `${item.id}aPort0_1`
                    },
                    {
                        group: "aPort0_2",
                        id: `${item.id}aPort0_2`
                    }
                ].forEach(port => {
                    item.addPort(port);
                });
            }
            if (!item.port.ports[3]?.id && item.port.ports[0]?.group === "left") {
                [
                    {
                        group: "aPort1_1",
                        id: `${item.id}aPort1_1`
                    },
                    {
                        group: "aPort1_2",
                        id: `${item.id}aPort1_2`
                    }
                ].forEach(port => {
                    item.addPort(port);
                });

                [
                    {
                        source: {cell: item.id, port: item.port.ports[0]?.id},
                        target: {cell: item.id, port: `${item.id}aPort1_1`},
                        attrs: {
                            line: {
                                stroke: "#9af",
                                strokeWidth: 1,
                                targetMarker: {
                                    name: "block"
                                }
                            }
                        }
                    },
                    {
                        source: {cell: item.id, port: `${item.id}aPort1_2`},
                        target: {cell: item.id, port: item.port.ports[1]?.id},
                        attrs: {
                            line: {
                                stroke: "#9af",
                                strokeWidth: 1,
                                targetMarker: {
                                    name: "block"
                                }
                            }
                        }
                    }
                ].forEach(edge => {
                    graph.addEdge(edge);
                });
            }
            if (item.port.ports[3]?.id) {
                if (portType === "BAOUT") {
                    [
                        {
                            source: {cell: item.id, port: item.port.ports[2]?.id},
                            target: {cell: item.id, port: item.port.ports[0]?.id},
                            attrs: {
                                line: {
                                    stroke: "#9af",
                                    strokeWidth: 1,
                                    targetMarker: {
                                        name: "block"
                                    }
                                }
                            }
                        },
                        {
                            source: {cell: item.id, port: item.port.ports[1]?.id},
                            target: {cell: item.id, port: item.port.ports[3]?.id},
                            attrs: {
                                line: {
                                    stroke: "#9af",
                                    strokeWidth: 1,
                                    targetMarker: {
                                        name: "block"
                                    }
                                }
                            }
                        }
                    ].forEach(edge => {
                        graph.addEdge(edge);
                    });
                } else {
                    [
                        {
                            source: {cell: item.id, port: item.port.ports[0]?.id},
                            target: {cell: item.id, port: item.port.ports[2]?.id},
                            attrs: {
                                line: {
                                    stroke: "#9af",
                                    strokeWidth: 1,
                                    targetMarker: {
                                        name: "block"
                                    }
                                }
                            }
                        },
                        {
                            source: {cell: item.id, port: item.port.ports[3]?.id},
                            target: {cell: item.id, port: item.port.ports[1]?.id},
                            attrs: {
                                line: {
                                    stroke: "#9af",
                                    strokeWidth: 1,
                                    targetMarker: {
                                        name: "block"
                                    }
                                }
                            }
                        }
                    ].forEach(edge => {
                        graph.addEdge(edge);
                    });
                }
            }

            // 显示BA、PA和OSC
            if (type === "OLA") {
                const _id = item.id?.replace("OLA", "EDFA");
                const _osc = item.id?.replace("OLA", "PORT");
                const _voa = filterNeId(_id);
                // 添加节点
                [
                    {
                        group: "line0_1",
                        id: `${item.id}line0_1`
                    },
                    {
                        group: "line0_2",
                        id: `${item.id}line0_2`
                    },
                    {
                        group: "line1_1",
                        id: `${item.id}line1_1`
                    },
                    {
                        group: "line1_2",
                        id: `${item.id}line1_2`
                    }
                ].forEach(port => {
                    item.addPort(port);
                });
                // 画线
                [
                    {
                        source: {cell: item.id, port: `${item.id}line0_2`},
                        target: {cell: item.id, port: `${item.id}line0_1`},
                        attrs: {
                            line: {
                                stroke: "#9af",
                                strokeWidth: 1,
                                targetMarker: {
                                    name: "block"
                                }
                            }
                        }
                    },
                    {
                        source: {cell: item.id, port: `${item.id}line1_1`},
                        target: {cell: item.id, port: `${item.id}line1_2`},
                        attrs: {
                            line: {
                                stroke: "#9af",
                                strokeWidth: 1,
                                targetMarker: {
                                    name: "block"
                                }
                            }
                        }
                    }
                ].forEach(edge => {
                    graph.addEdge(edge);
                });

                if (portType === "LA1IN") {
                    // 添加节点
                    [
                        {
                            group: "voa0",
                            id: `${_voa}-LA2-VOA`,
                            attrs: {
                                text: {text: "VOA2"}
                            }
                        },
                        {
                            group: "voa0_1",
                            id: `${item.id}voa0_1`
                        },
                        {
                            group: "voa0_2",
                            id: `${item.id}voa0_2`
                        },
                        {
                            group: "voa1",
                            id: `${_voa}-LA1-VOA`,
                            attrs: {
                                text: {text: "VOA1"}
                            }
                        },
                        {
                            group: "voa1_1",
                            id: `${item.id}voa1_1`
                        },
                        {
                            group: "voa1_2",
                            id: `${item.id}voa1_2`
                        }
                    ].forEach(port => {
                        item.addPort(port);
                    });
                    // 画线
                    [
                        {
                            source: {cell: item.id, port: `${item.id}voa0_2`},
                            target: {cell: item.id, port: `${item.id}voa0_1`},
                            attrs: {
                                line: {stroke: "#000", strokeWidth: 1}
                            }
                        },
                        {
                            source: {cell: item.id, port: `${item.id}voa1_2`},
                            target: {cell: item.id, port: `${item.id}voa1_1`},
                            attrs: {
                                line: {stroke: "#000", strokeWidth: 1}
                            }
                        }
                    ].forEach(edge => {
                        graph.addEdge(edge);
                    });

                    const LAS = ["LA1", "LA2", "OSC1", "OSC2"];
                    [
                        {
                            id: `${_id}-LA1`,
                            x: 140,
                            y: 25,
                            width: 25,
                            height: 25,
                            points: "0,0 2,1 0,2"
                        },
                        {
                            id: `${_id}-LA2`,
                            x: 140,
                            y: 100,
                            width: 25,
                            height: 25,
                            points: "0,1 2,0 2,2"
                        },
                        {
                            id: `${_osc}-OSC1`,
                            ...sizeOSC,
                            points: "0,0 1,0 1,1 0,1",
                            param: ["component", "TRANSCEIVER", "OSC"]
                        },
                        {
                            id: `${_osc}-OSC2`,
                            ...sizeOSCR,
                            points: "0,0 1,0 1,1 0,1",
                            param: ["component", "TRANSCEIVER", "OSC"]
                        }
                    ].forEach((p, i) => {
                        const node = graph.addNode({
                            shape: "polygon",
                            ...p,
                            x: p.x + _x,
                            y: p.y + _y,
                            label: `${LAS[i]}`,
                            attrs: {
                                body: {fill: "#31d0c6", stroke: "#31d0c6"}
                            }
                        });
                        item.addChild(node);
                    });
                }
                if (portType === "LA2IN") {
                    // 添加节点
                    [
                        {
                            group: "voa0",
                            id: `${_voa}-LA1-VOA`,
                            attrs: {
                                text: {text: "VOA1"}
                            }
                        },
                        {
                            group: "voa0_1",
                            id: `${item.id}voa0_1`
                        },
                        {
                            group: "voa0_2",
                            id: `${item.id}voa0_2`
                        },
                        {
                            group: "voa1",
                            id: `${_voa}-LA2-VOA`,
                            attrs: {
                                text: {text: "VOA2"}
                            }
                        },
                        {
                            group: "voa1_1",
                            id: `${item.id}voa1_1`
                        },
                        {
                            group: "voa1_2",
                            id: `${item.id}voa1_2`
                        }
                    ].forEach(port => {
                        item.addPort(port);
                    });
                    // 画线
                    [
                        {
                            source: {cell: item.id, port: `${item.id}voa0_2`},
                            target: {cell: item.id, port: `${item.id}voa0_1`},
                            attrs: {
                                line: {stroke: "#000", strokeWidth: 1}
                            }
                        },
                        {
                            source: {cell: item.id, port: `${item.id}voa1_2`},
                            target: {cell: item.id, port: `${item.id}voa1_1`},
                            attrs: {
                                line: {stroke: "#000", strokeWidth: 1}
                            }
                        }
                    ].forEach(edge => {
                        graph.addEdge(edge);
                    });

                    const LAS = ["LA2", "LA1", "OSC2", "OSC1"];
                    [
                        {
                            id: `${_id}-LA2`,
                            x: 140,
                            y: 25,
                            width: 25,
                            height: 25,
                            points: "0,0 2,1 0,2"
                        },
                        {
                            id: `${_id}-LA1`,
                            x: 140,
                            y: 100,
                            width: 25,
                            height: 25,
                            points: "0,1 2,0 2,2"
                        },
                        {
                            id: `${_osc}-OSC2`,
                            ...sizeOSC,
                            points: "0,0 1,0 1,1 0,1",
                            param: ["component", "TRANSCEIVER", "OSC"]
                        },
                        {
                            id: `${_osc}-OSC1`,
                            ...sizeOSCR,
                            points: "0,0 1,0 1,1 0,1",
                            param: ["component", "TRANSCEIVER", "OSC"]
                        }
                    ].forEach((p, i) => {
                        const node = graph.addNode({
                            shape: "polygon",
                            ...p,
                            x: p.x + _x,
                            y: p.y + _y,
                            label: `${LAS[i]}`,
                            attrs: {
                                body: {fill: "#31d0c6", stroke: "#31d0c6"}
                            }
                        });
                        item.addChild(node);
                    });
                }
            }
            if (type === "OA") {
                const _id = item.id?.replace("OA", "EDFA");
                const _osc = item.id?.replace("OA", "PORT");
                const _voa = filterNeId(_id);
                // BAIN PAIN
                if (item.port.ports[0]?.group === "right" || portType === "BAIN") {
                    // 添加节点
                    [
                        {
                            group: "line0_1",
                            id: `${item.id}line0_1`
                        },
                        {
                            group: "line0_2",
                            id: `${item.id}line0_2`
                        },
                        {
                            group: "voa0",
                            id: `${_voa}-PA-VOA`,
                            attrs: {
                                text: {text: "VOA"}
                            }
                        },
                        {
                            group: "voa0_1",
                            id: `${item.id}voa0_1`
                        },
                        {
                            group: "voa0_2",
                            id: `${item.id}voa0_2`
                        }
                    ].forEach(port => {
                        item.addPort(port);
                    });
                    // 画线
                    [
                        {
                            source: {cell: item.id, port: `${item.id}line0_2`},
                            target: {cell: item.id, port: `${item.id}line0_1`},
                            attrs: {
                                line: {
                                    stroke: "#9af",
                                    strokeWidth: 1,
                                    targetMarker: {
                                        name: "block"
                                    }
                                }
                            }
                        },
                        {
                            source: {cell: item.id, port: `${item.id}voa0_2`},
                            target: {cell: item.id, port: `${item.id}voa0_1`},
                            attrs: {
                                line: {stroke: "#000", strokeWidth: 1}
                            }
                        }
                    ].forEach(edge => {
                        graph.addEdge(edge);
                    });
                    [
                        {
                            id: `${_id}-BA`,
                            x: 140,
                            y: 25,
                            width: 25,
                            height: 25,
                            points: "0,0 2,1 0,2",
                            label: "BA"
                        },
                        {
                            id: `${_id}-PA`,
                            x: 140,
                            y: 100,
                            width: 25,
                            height: 25,
                            points: "0,1 2,0 2,2",
                            label: "PA",
                            param: ["amplifier", "EDFA", "PA"]
                        },
                        {
                            id: `${_osc}-OSC`,
                            ...sizeOSCR,
                            points: "0,0 1,0 1,1 0,1",
                            label: "OSC",
                            param: ["component", "TRANSCEIVER", "OSC"]
                        }
                    ].forEach(p => {
                        const node = graph.addNode({
                            shape: "polygon",
                            ...p,
                            x: p.x + _x,
                            y: p.y + _y,
                            attrs: {
                                body: {fill: "#31d0c6", stroke: "#31d0c6"}
                            }
                        });
                        item.addChild(node);
                    });
                }
                if (portType === "PAIN") {
                    item.addPort({
                        group: "voa1",
                        id: `${_voa}-PA-VOA`,
                        attrs: {
                            text: {text: "VOA"}
                        }
                    });
                    item.addPort({
                        group: "voa1_1",
                        id: `${item.id}voa1_1`
                    });
                    item.addPort({
                        group: "voa1_2",
                        id: `${item.id}voa1_2`
                    });
                    graph.addEdge({
                        source: {cell: item.id, port: `${item.id}voa1_2`},
                        target: {cell: item.id, port: `${item.id}voa1_1`},
                        attrs: {
                            line: {stroke: "#000", strokeWidth: 1}
                        }
                    });

                    item.addPort({
                        group: "line1_1",
                        id: `${item.id}line1_1`
                    });
                    item.addPort({
                        group: "line1_2",
                        id: `${item.id}line1_2`
                    });
                    graph.addEdge({
                        source: {cell: item.id, port: `${item.id}line1_1`},
                        target: {cell: item.id, port: `${item.id}line1_2`},
                        attrs: {
                            line: {
                                stroke: "#9af",
                                strokeWidth: 1,
                                targetMarker: {
                                    name: "block"
                                }
                            }
                        }
                    });

                    [
                        {
                            id: `${_id}-PA`,
                            x: 140,
                            y: 25,
                            width: 25,
                            height: 25,
                            points: "0,0 2,1 0,2",
                            label: "PA"
                        },
                        {
                            id: `${_id}-BA`,
                            x: 140,
                            y: 100,
                            width: 25,
                            height: 25,
                            points: "0,1 2,0 2,2",
                            label: "BA",
                            param: ["amplifier", "EDFA", "PA"]
                        },
                        {
                            id: `${_osc}-OSC`,
                            ...sizeOSC,
                            points: "0,0 1,0 1,1 0,1",
                            label: "OSC",
                            param: ["component", "TRANSCEIVER", "OSC"]
                        }
                    ].forEach(p => {
                        const node = graph.addNode({
                            shape: "polygon",
                            ...p,
                            x: p.x + _x,
                            y: p.y + _y,
                            attrs: {
                                body: {fill: "#31d0c6", stroke: "#31d0c6"}
                            }
                        });
                        item.addChild(node);
                    });
                }
            }
        }
        if (type === "OLP") {
            // item?.prop("attrs/body", {
            //     refWidth: "75%",
            //     refWidth2: 20
            // });

            // const pos = item?.position();
            // item?.position(pos.x, pos.y - 150);

            // item?.resize(300, 450);
            // const size = item?.size();
            if (item.port.ports[1]?.group === "right") {
                [
                    {
                        source: {cell: item.id, port: item.port.ports[0]?.id},
                        target: {cell: item.id, port: item.port.ports[2]?.id},
                        attrs: {
                            line: {stroke: "#9af", strokeWidth: 1, sourceMarker: null, targetMarker: null}
                        }
                    },
                    {
                        source: {cell: item.id, port: item.port.ports[0]?.id},
                        target: {cell: item.id, port: item.port.ports[1]?.id},
                        attrs: {
                            line: {stroke: "#9af", strokeWidth: 1, sourceMarker: null, targetMarker: null}
                        }
                    }
                ].forEach(edge => {
                    graph.addEdge(edge);
                });
            }
            if (item.port.ports[1]?.group === "left") {
                [
                    {
                        source: {cell: item.id, port: item.port.ports[0]?.id},
                        target: {cell: item.id, port: item.port.ports[2]?.id},
                        attrs: {
                            line: {stroke: "#9af", strokeWidth: 1, sourceMarker: null, targetMarker: null}
                        }
                    },
                    {
                        source: {cell: item.id, port: item.port.ports[2]?.id},
                        target: {cell: item.id, port: item.port.ports[1]?.id},
                        attrs: {
                            line: {stroke: "#9af", strokeWidth: 1, sourceMarker: null, targetMarker: null}
                        }
                    }
                ].forEach(edge => {
                    graph.addEdge(edge);
                });
            }
        }
        if (type === "WSS") {
            const _id = item.id?.replace("WSS", "EDFA");
            const _osc = item.id?.replace("WSS", "PORT");
            const _voa = filterNeId(_id);
            const _muxPort = filterNeId(item.id?.replace("WSS", "PORT"));
            if (portType?.startsWith("AD") || portType === "BAOUT") {
                [
                    {
                        group: "aPort0_1",
                        id: `${item.id}aPort0_1`
                    },
                    {
                        group: "aPort0_2",
                        id: `${item.id}aPort0_2`
                    },
                    {
                        group: "line0_1",
                        id: `${item.id}line0_1`
                    },
                    {
                        group: "line0_2",
                        id: `${item.id}line0_2`
                    },
                    {
                        group: "voa0",
                        id: `${_voa}-PA-VOA`,
                        attrs: {
                            text: {text: "VOA"}
                        }
                    },
                    {
                        group: "voa0_1",
                        id: `${item.id}voa0_1`
                    },
                    {
                        group: "voa0_2",
                        id: `${item.id}voa0_2`
                    }
                ].forEach(port => {
                    item.addPort(port);
                });

                if (portType?.startsWith("AD")) {
                    [
                        {
                            source: {cell: item.id, port: `${item.id}aPort0_1`},
                            target: {cell: item.id, port: item.port.ports[1]?.id},
                            attrs: {
                                line: {
                                    stroke: "#9af",
                                    strokeWidth: 1,
                                    targetMarker: {
                                        name: "block"
                                    }
                                }
                            }
                        },
                        {
                            source: {cell: item.id, port: item.port.ports[2]?.id},
                            target: {cell: item.id, port: `${item.id}aPort0_2`},
                            attrs: {
                                line: {
                                    stroke: "#9af",
                                    strokeWidth: 1,
                                    targetMarker: {
                                        name: "block"
                                    }
                                }
                            }
                        }
                    ].forEach(edge => {
                        graph.addEdge(edge);
                    });
                }
                if (portType === "BAOUT") {
                    [
                        {
                            source: {cell: item.id, port: `${item.id}aPort0_1`},
                            target: {cell: item.id, port: item.port.ports[0]?.id},
                            attrs: {
                                line: {
                                    stroke: "#9af",
                                    strokeWidth: 1,
                                    targetMarker: {
                                        name: "block"
                                    }
                                }
                            }
                        },
                        {
                            source: {cell: item.id, port: item.port.ports[1]?.id},
                            target: {cell: item.id, port: `${item.id}aPort0_2`},
                            attrs: {
                                line: {
                                    stroke: "#9af",
                                    strokeWidth: 1,
                                    targetMarker: {
                                        name: "block"
                                    }
                                }
                            }
                        }
                    ].forEach(edge => {
                        graph.addEdge(edge);
                    });
                }

                [
                    {
                        source: {cell: item.id, port: `${item.id}line0_2`},
                        target: {cell: item.id, port: `${item.id}line0_1`},
                        attrs: {
                            line: {
                                stroke: "#9af",
                                strokeWidth: 1,
                                targetMarker: {
                                    name: "block"
                                }
                            }
                        }
                    },
                    {
                        source: {cell: item.id, port: `${item.id}voa0_2`},
                        target: {cell: item.id, port: `${item.id}voa0_1`},
                        attrs: {
                            line: {stroke: "#000", strokeWidth: 1}
                        }
                    }
                ].forEach(edge => {
                    graph.addEdge(edge);
                });
                [
                    {
                        id: `${_id}-BA`,
                        x: 140,
                        y: 25,
                        width: 25,
                        height: 25,
                        points: "0,0 2,1 0,2",
                        label: "BA"
                    },
                    {
                        id: `${_id}-PA`,
                        x: 140,
                        y: 100,
                        width: 25,
                        height: 25,
                        points: "0,1 2,0 2,2",
                        label: "PA",
                        param: ["amplifier", "EDFA", "PA"]
                    },
                    {
                        id: `${_osc}-OSC`,
                        ...sizeOSCR,
                        points: "0,0 1,0 1,1 0,1",
                        label: "OSC",
                        param: ["component", "TRANSCEIVER", "OSC"]
                    },
                    {
                        id: `${item.id}mux`,
                        x: 25,
                        y: 11,
                        width: 30,
                        height: 130,
                        points: "1 0.5,1 3,2 2.5,2 1",
                        label: "WSS",
                        ports: {
                            groups: {
                                right_1: {
                                    position: {name: "absolute", args: {x: 30, y: 28}},
                                    label: {
                                        position: {
                                            name: "right",
                                            args: {
                                                x: 5,
                                                y: -5
                                            }
                                        }
                                    },
                                    attrs: {
                                        circle: {
                                            r: 4,
                                            strokeWidth: 2,
                                            fill: "#fff",
                                            cursor: "default",
                                            stroke: "#31d0c6"
                                        }
                                    }
                                },
                                right_2: {
                                    position: {name: "absolute", args: {x: 30, y: 102}},
                                    label: {
                                        position: {
                                            name: "right",
                                            args: {
                                                x: 5,
                                                y: -5
                                            }
                                        }
                                    },
                                    attrs: {
                                        circle: {
                                            r: 4,
                                            strokeWidth: 2,
                                            fill: "#fff",
                                            cursor: "default",
                                            stroke: "#31d0c6"
                                        }
                                    }
                                }
                            },
                            items: [
                                {
                                    id: `${_muxPort}-BAIN`,
                                    group: "right_1",
                                    attrs: {
                                        text: {text: "BAIN"}
                                    }
                                },
                                {
                                    id: `${_muxPort}-PAOUT`,
                                    group: "right_2",
                                    attrs: {
                                        text: {text: "PAOUT"}
                                    }
                                }
                            ]
                        }
                    }
                ].forEach(p => {
                    const node = graph.addNode({
                        shape: "polygon",
                        ...p,
                        x: p.x + _x,
                        y: p.y + _y,
                        attrs: {
                            body: {
                                fill: `${p.label === "WSS" ? "#fff" : "#31d0c6"}`,
                                stroke: "#31d0c6"
                            }
                        }
                    });
                    item.addChild(node);
                });
                // console.log(item.getChildren());
            }
            if (portType === "PAIN") {
                [
                    {
                        group: "aPort1_1",
                        id: `${item.id}aPort1_1`
                    },
                    {
                        group: "aPort1_2",
                        id: `${item.id}aPort1_2`
                    },
                    {
                        group: "line1_1",
                        id: `${item.id}line1_1`
                    },
                    {
                        group: "line1_2",
                        id: `${item.id}line1_2`
                    },
                    {
                        group: "voa1",
                        id: `${_voa}-PA-VOA`,
                        attrs: {
                            text: {text: "VOA"}
                        }
                    },
                    {
                        group: "voa1_1",
                        id: `${item.id}voa1_1`
                    },
                    {
                        group: "voa1_2",
                        id: `${item.id}voa1_2`
                    }
                ].forEach(port => {
                    item.addPort(port);
                });

                [
                    {
                        source: {cell: item.id, port: item.port.ports[0]?.id},
                        target: {cell: item.id, port: `${item.id}aPort1_1`},
                        attrs: {
                            line: {
                                stroke: "#9af",
                                strokeWidth: 1,
                                targetMarker: {
                                    name: "block"
                                }
                            }
                        }
                    },
                    {
                        source: {cell: item.id, port: `${item.id}aPort1_2`},
                        target: {cell: item.id, port: item.port.ports[1]?.id},
                        attrs: {
                            line: {
                                stroke: "#9af",
                                strokeWidth: 1,
                                targetMarker: {
                                    name: "block"
                                }
                            }
                        }
                    },
                    {
                        source: {cell: item.id, port: `${item.id}line1_1`},
                        target: {cell: item.id, port: `${item.id}line1_2`},
                        attrs: {
                            line: {
                                stroke: "#9af",
                                strokeWidth: 1,
                                targetMarker: {
                                    name: "block"
                                }
                            }
                        }
                    },
                    {
                        source: {cell: item.id, port: `${item.id}voa1_2`},
                        target: {cell: item.id, port: `${item.id}voa1_1`},
                        attrs: {
                            line: {stroke: "#000", strokeWidth: 1}
                        }
                    }
                ].forEach(edge => {
                    graph.addEdge(edge);
                });
                [
                    {
                        id: `${_id}-PA`,
                        x: 140,
                        y: 25,
                        width: 25,
                        height: 25,
                        points: "0,0 2,1 0,2",
                        label: "PA"
                    },
                    {
                        id: `${_id}-BA`,
                        x: 140,
                        y: 100,
                        width: 25,
                        height: 25,
                        points: "0,1 2,0 2,2",
                        label: "BA",
                        param: ["amplifier", "EDFA", "PA"]
                    },
                    {
                        id: `${_osc}-OSC`,
                        ...sizeOSC,
                        points: "0,0 1,0 1,1 0,1",
                        label: "OSC",
                        param: ["component", "TRANSCEIVER", "OSC"]
                    },
                    {
                        id: `${item.id}mux`,
                        x: 240,
                        y: 11,
                        width: 30,
                        height: 130,
                        points: "1 1.35,1 2.65,2 3,2 1",
                        label: "WSS",
                        ports: {
                            groups: {
                                left_1: {
                                    position: {name: "absolute", args: {x: 0, y: 28}},
                                    label: {
                                        position: {
                                            args: {
                                                x: -5,
                                                y: -5
                                            }
                                        }
                                    },
                                    attrs: {
                                        circle: {
                                            r: 4,
                                            strokeWidth: 2,
                                            fill: "#fff",
                                            cursor: "default",
                                            stroke: "#31d0c6"
                                        }
                                    }
                                },
                                left_2: {
                                    position: {name: "absolute", args: {x: 0, y: 102}},
                                    label: {
                                        position: {
                                            args: {
                                                x: -5,
                                                y: -5
                                            }
                                        }
                                    },
                                    attrs: {
                                        circle: {
                                            r: 4,
                                            strokeWidth: 2,
                                            fill: "#fff",
                                            cursor: "default",
                                            stroke: "#31d0c6"
                                        }
                                    }
                                },
                                right: {
                                    position: "right",
                                    label: {position: "right"},
                                    attrs: {
                                        circle: {
                                            r: 4,
                                            strokeWidth: 2,
                                            fill: "#fff",
                                            cursor: "default",
                                            stroke: "#31d0c6"
                                        }
                                    }
                                }
                            },
                            items: [
                                {
                                    id: `${_muxPort}-PAOUT`,
                                    group: "left_1",
                                    attrs: {
                                        text: {text: "PAOUT"}
                                    }
                                },
                                {
                                    id: `${_muxPort}-BAIN`,
                                    group: "left_2",
                                    attrs: {
                                        text: {text: "BAIN"}
                                    }
                                }
                            ]
                        }
                    }
                ].forEach(p => {
                    const node = graph.addNode({
                        shape: "polygon",
                        ...p,
                        x: p.x + _x,
                        y: p.y + _y,
                        attrs: {
                            body: {
                                fill: `${p.label === "WSS" ? "#fff" : "#31d0c6"}`,
                                stroke: "#31d0c6"
                            }
                        }
                    });
                    item.addChild(node);
                });
            }
        }
        if (type === "TFF") {
            drawTFFCard(item, graph, {_x, _y});
        }
        // || type === "TFF"
        if (type === "MUX") {
            if (item.port.ports[0]?.group === "right" && portType?.startsWith("MUX")) {
                [
                    {
                        group: "aPort0_3",
                        id: `${item.id}aPort0_3`
                    }
                ].forEach(port => {
                    item.addPort(port);
                });
                [
                    {
                        source: {cell: item.id, port: `${item.id}aPort0_3`},
                        target: {cell: item.id, port: item.port.ports[0]?.id},
                        attrs: {
                            line: {stroke: "#9af", strokeWidth: 1, sourceMarker: "block", targetMarker: "block"}
                        }
                    }
                ].forEach(edge => {
                    graph.addEdge(edge);
                });
                // 内部图形
                [
                    {
                        x: 130,
                        y: 11,
                        width: 30,
                        height: 130,
                        points: "1 0.5,1 3,2 2.5,2 1",
                        label: "MUX"
                    }
                ].forEach(p => {
                    const node = graph.addNode({
                        shape: "polygon",
                        ...p,
                        x: p.x + _x,
                        y: p.y + _y,
                        attrs: {
                            body: {
                                fill: `${p.label === "MUX" ? "#fff" : "#31d0c6"}`,
                                // stroke: `${p.label === "MUX" ? "#000" : "#31d0c6"}`
                                stroke: "#31d0c6"
                            }
                        }
                    });
                    item.addChild(node);
                });
                // 复用MUX 做TFF
                // if (type === "TFF") {
                //     let label;
                //     if (portTypeR?.startsWith("PT")) {
                //         label = "PT";
                //     }
                //     if (portTypeR?.startsWith("CH")) {
                //         label = "CH";
                //     }
                //     [
                //         {
                //             x: v
                //             y: 55,
                //             label: label ?? "CH"
                //         }
                //     ].forEach(p => {
                //         const node = graph.addNode({
                //             shape: "polygon",
                //             ...p,
                //             x: p.x + _x,
                //             y: p.y + _y
                //         });
                //         item.addChild(node);
                //     });
                // }
            }
            // 右侧node
            if (item.port.ports[0]?.group === "left" && portType?.startsWith("MUX")) {
                // 判断第二个口是否存在
                if (portTypeR) {
                    [
                        {
                            source: {cell: item.id, port: item.port.ports[0]?.id},
                            target: {cell: item.id, port: item.port.ports[1]?.id},
                            attrs: {
                                line: {stroke: "#9af", strokeWidth: 1, sourceMarker: "block", targetMarker: "block"}
                            }
                        }
                    ].forEach(edge => {
                        graph.addEdge(edge);
                    });
                } else {
                    [
                        {
                            group: "aPort1_3",
                            id: `${item.id}aPort1_3`
                        }
                    ].forEach(port => {
                        item.addPort(port);
                    });
                    [
                        {
                            source: {cell: item.id, port: item.port.ports[0]?.id},
                            target: {cell: item.id, port: `${item.id}aPort1_3`},
                            attrs: {
                                line: {stroke: "#9af", strokeWidth: 1, sourceMarker: "block", targetMarker: "block"}
                            }
                        }
                    ].forEach(edge => {
                        graph.addEdge(edge);
                    });
                }
                // 内部图形
                [
                    {
                        x: 130,
                        y: 11,
                        width: 30,
                        height: 130,
                        points: "1 1.35,1 2.65,2 3,2 1",
                        label: "MUX"
                    }
                ].forEach(p => {
                    const node = graph.addNode({
                        shape: "polygon",
                        ...p,
                        x: p.x + _x,
                        y: p.y + _y,
                        attrs: {
                            body: {
                                fill: `${p.label === "MUX" ? "#fff" : "#31d0c6"}`,
                                stroke: "#31d0c6"
                            }
                        }
                    });
                    item.addChild(node);
                });
                // 复用MUX 做TFF
                if (type === "TFF") {
                    let label;
                    if (portTypeR?.startsWith("PT")) {
                        label = "PT";
                    }
                    if (portTypeR?.startsWith("CH")) {
                        label = "CH";
                    }
                    if (portType?.startsWith("PT")) {
                        label = "PT";
                    }
                    if (portType?.startsWith("CH")) {
                        label = "CH";
                    }
                    [
                        {
                            x: 230,
                            y: 55,
                            label: label ?? "CH"
                        }
                    ].forEach(p => {
                        const node = graph.addNode({
                            shape: "polygon",
                            ...p,
                            x: p.x + _x,
                            y: p.y + _y
                        });
                        item.addChild(node);
                    });
                }
            }
            if (portType?.startsWith("CH") || portType?.startsWith("PT")) {
                [
                    {
                        source: {cell: item.id, port: item.port.ports[0]?.id},
                        target: {cell: item.id, port: item.port.ports[1]?.id},
                        attrs: {
                            line: {stroke: "#9af", strokeWidth: 1, sourceMarker: "block", targetMarker: "block"}
                        }
                    }
                ].forEach(edge => {
                    graph.addEdge(edge);
                });

                [
                    {
                        // id: "MUX-1",
                        x: 130,
                        y: 11,
                        width: 30,
                        height: 130,
                        points: "1 0.5,1 3,2 2.5,2 1",
                        label: "MUX"
                    }
                ].forEach(p => {
                    const node = graph.addNode({
                        shape: "polygon",
                        ...p,
                        x: p.x + _x,
                        y: p.y + _y,
                        attrs: {
                            body: {
                                fill: `${p.label === "MUX" ? "#fff" : "#31d0c6"}`,
                                // stroke: `${p.label === "MUX" ? "#000" : "#31d0c6"}`
                                stroke: "#31d0c6"
                            }
                        }
                    });
                    item.addChild(node);
                });
                // 复用MUX 做TFF
                // if (type === "TFF") {
                //     let label;
                //     if (portTypeR?.startsWith("PT")) {
                //         label = "PT";
                //     }
                //     if (portTypeR?.startsWith("CH")) {
                //         label = "CH";
                //     }
                //     if (portType?.startsWith("PT")) {
                //         label = "PT";
                //     }
                //     if (portType?.startsWith("CH")) {
                //         label = "CH";
                //     }
                //     [
                //         {
                //             x: 70,
                //             y: 55,
                //             label: label ?? "CH"
                //         }
                //     ].forEach(p => {
                //         const node = graph.addNode({
                //             shape: "polygon",
                //             ...p,
                //             x: p.x + _x,
                //             y: p.y + _y
                //         });
                //         item.addChild(node);
                //     });
                // }
            }
        }
        if (type === "LINECARD") {
            const _id = item.id?.replace("LINECARD", "TRANSCEIVER");
            // 获取端口数量
            const portsLength = item.port.ports?.length;
            // 存储内部图形
            const innerArrs = [];
            // 添加的图形宽高 固定值
            const cub = {width: 20, height: 10, points: "0,0 1,0 1,1 0,1"};
            if (portsLength >= 4) {
                const pos = item?.position();
                if (item.id === graphNode[0].id) {
                    item?.position(pos.x + 150, pos.y - 75);
                } else {
                    item?.position(pos.x, pos.y - 75);
                }
                item?.resize(150, 300);
                item?.prop("attrs/label", {y: 170});
                // 坐标发生更改 需要重置
                const _x = item.position().x;
                const _y = item.position().y;
                // 编写内部图形 右側信息
                if (portType === "L1") {
                    for (let i = 1; i <= portsLength; i++) {
                        if (portsLength >= 20) {
                            if (i <= 20) {
                                const gap = (300 / 20) * (i - 1);
                                const client = {
                                    id: `${_id}-C${i}`,
                                    x: 120,
                                    y: 3 + gap,
                                    ...cub
                                };
                                innerArrs.push(client);
                            } else {
                                const line = {
                                    id: `${_id}-L1`,
                                    x: 9,
                                    y: 145,
                                    ...cub
                                };
                                innerArrs.push(line);
                            }
                        } else if (portsLength >= 11) {
                            if (i <= 11) {
                                const gap = (300 / 11) * (i - 1);
                                const client = {
                                    id: `${_id}-C${i === 11 ? 21 : i}`,
                                    x: 120,
                                    y: 9.2 + gap,
                                    ...cub
                                };
                                innerArrs.push(client);
                            } else {
                                const line = {
                                    id: `${_id}-L1`,
                                    x: 9,
                                    y: 145,
                                    ...cub
                                };
                                innerArrs.push(line);
                            }
                        } else if (portsLength >= 4) {
                            if (i <= 4) {
                                const gap = 75 * (i - 1);
                                const client = {
                                    id: `${_id}-C${i}`,
                                    x: 120,
                                    y: 33 + gap,
                                    ...cub
                                };
                                innerArrs.push(client);
                            } else {
                                const line = {
                                    id: `${_id}-L1`,
                                    x: 9,
                                    y: 145,
                                    ...cub
                                };
                                innerArrs.push(line);
                            }
                        }
                    }
                } else {
                    for (let i = 1; i <= portsLength; i++) {
                        if (portsLength >= 20) {
                            if (i <= 20) {
                                const gap = (300 / 20) * (i - 1);
                                const client = {
                                    id: `${_id}-C${i}`,
                                    x: 9,
                                    y: 3 + gap,
                                    ...cub
                                };
                                innerArrs.push(client);
                            } else {
                                const line = {
                                    id: `${_id}-L1`,
                                    x: 120,
                                    y: 145,
                                    ...cub
                                };
                                innerArrs.push(line);
                            }
                        } else if (portsLength >= 11) {
                            if (i <= 11) {
                                const gap = (300 / 11) * (i - 1);
                                const client = {
                                    id: `${_id}-C${i === 11 ? 21 : i}`,
                                    x: 9,
                                    y: 9.2 + gap,
                                    ...cub
                                };
                                innerArrs.push(client);
                            } else {
                                const line = {
                                    id: `${_id}-L1`,
                                    x: 120,
                                    y: 145,
                                    ...cub
                                };
                                innerArrs.push(line);
                            }
                        } else if (portsLength >= 4) {
                            if (i <= 4) {
                                const gap = 75 * (i - 1);
                                const client = {
                                    id: `${_id}-C${i}`,
                                    x: 9,
                                    y: 33 + gap,
                                    ...cub
                                };
                                innerArrs.push(client);
                            } else {
                                const line = {
                                    id: `${_id}-L1`,
                                    x: 120,
                                    y: 145,
                                    ...cub
                                };
                                innerArrs.push(line);
                            }
                        }
                    }
                }
                innerArrs.forEach(p => {
                    const node = graph.addNode({
                        shape: "polygon",
                        ...p,
                        x: p.x + _x,
                        y: p.y + _y,
                        attrs: {
                            body: {stroke: "#31d0c6"}
                        }
                    });
                    item.addChild(node);
                });
            } else {
                if (portType === "L1") {
                    for (let i = 1; i <= portsLength; i++) {
                        if (i <= 2) {
                            const gap = 75 * (i - 1);
                            const client = {
                                id: `${_id}-C${i}`,
                                x: 270,
                                y: 33 + gap,
                                ...cub
                            };
                            innerArrs.push(client);
                        } else {
                            const line = {
                                id: `${_id}-L1`,
                                x: 9,
                                y: 70,
                                ...cub
                            };
                            innerArrs.push(line);
                        }
                    }
                } else {
                    for (let i = 1; i <= portsLength; i++) {
                        if (i <= 2) {
                            const gap = 75 * (i - 1);
                            const client = {
                                id: `${_id}-C${i}`,
                                x: 9,
                                y: 33 + gap,
                                ...cub
                            };
                            innerArrs.push(client);
                        } else {
                            const line = {
                                id: `${_id}-L1`,
                                x: 270,
                                y: 70,
                                ...cub
                            };
                            innerArrs.push(line);
                        }
                    }
                }
                innerArrs.forEach(p => {
                    const node = graph.addNode({
                        shape: "polygon",
                        ...p,
                        x: p.x + _x,
                        y: p.y + _y,
                        attrs: {
                            body: {stroke: "#31d0c6"}
                        }
                    });
                    item.addChild(node);
                });
            }
        }
    };
    const drawTFFCard = (node, graph, size) => {
        const {_x, _y} = size;
        // 获取左右端口标签值
        const left = node.getPortsByGroup("left")?.[0]?.attrs?.text?.text;
        const right = node.getPortsByGroup("right")?.[0]?.attrs?.text?.text;
        // 定义变量方便左右倒换
        let points = "1 0.5,1 3,2 2.5,2 1";
        let port_1 = "left";
        let port_2 = "right";
        // 根据MUX的值进行转换
        if (left === "MUX") {
            points = "1 1.35,1 2.65,2 3,2 1";
            port_1 = "right";
            port_2 = "left";
        }
        // 设置内置节点并添加为子组件
        const innerNode = {
            shape: "polygon",
            id: `${node.id}_TFF_inner`,
            x: 130 + _x + Number(port_2 === "right" ? 30 : -24),
            y: 11 + _y,
            width: 40,
            height: 130,
            points,
            label: "MUX",
            attrs: {
                body: {
                    fill: `${"#fff"}`,
                    stroke: "#31d0c6"
                }
            },
            ports: {
                groups: {
                    left: {
                        attrs: {
                            circle: {
                                r: 4,
                                strokeWidth: 2,
                                fill: "#fff",
                                cursor: "default",
                                stroke: "#31d0c6"
                            }
                        }
                    },
                    right: {
                        position: "right",
                        label: {position: "right"},
                        attrs: {
                            circle: {
                                r: 4,
                                strokeWidth: 2,
                                fill: "#fff",
                                cursor: "default",
                                stroke: "#31d0c6"
                            }
                        }
                    },
                    pt_assist: {
                        position: {name: "absolute", args: {x: 40, y: 24}},
                        label: {position: "bottom"},
                        attrs: {
                            circle: {r: 0, stroke: "#000", strokeWidth: 0, fill: "#fff"}
                        }
                    }
                },
                items: [
                    {
                        id: "PT1",
                        group: port_1,
                        attrs: {
                            text: {text: "PT"}
                        }
                    },
                    {
                        id: "pt_assist_1",
                        group: "pt_assist"
                    },
                    {
                        id: "CH1",
                        group: port_1,
                        attrs: {
                            text: {text: "L1"}
                        }
                    },
                    {
                        id: "CH2",
                        group: port_1,
                        attrs: {
                            text: {text: "L2"}
                        }
                    },
                    {
                        id: "CH3",
                        group: port_1,
                        attrs: {
                            text: {text: "L3"}
                        }
                    },
                    {
                        id: "CH4",
                        group: port_1,
                        attrs: {
                            text: {text: "L4"}
                        }
                    },
                    {
                        id: "r",
                        group: port_2,
                        attrs: {
                            text: {text: ""}
                        }
                    }
                ]
            }
        };
        const _innerNode = graph.addNode(innerNode);
        node.addChild(_innerNode);
        // 获取左右端口的id进行连线
        const leftId = node.getPortsByGroup("left")?.[0]?.id;
        const rightId = node.getPortsByGroup("right")?.[0]?.id;
        // 定义temp进行左右倒换
        let temp_1 = leftId;
        let temp_2 = rightId;
        let port_index = ["PT1", "CH1", "CH2", "CH3", "CH4"].indexOf(left);
        let temp_x = 0;
        if (left === "MUX") {
            temp_2 = leftId;
            temp_1 = rightId;
            temp_x = 200;
            port_index = ["PT1", "CH1", "CH2", "CH3", "CH4"].indexOf(right);
        }
        if (temp_1 && temp_2) {
            const _edges = [
                getTFFedge(node, {port_1: temp_1, port_2: "PT1"}, {direct_1: "block", direct_2: {}}, [
                    {x: 50 + temp_x + _x, y: 75 + _y},
                    {x: 50 + temp_x + _x, y: 25 + _y}
                ]),
                getTFFedge(node, {port_1: temp_1, port_2: "CH1"}, {direct_1: "block", direct_2: "block"}, [
                    {x: 50 + temp_x + _x, y: 75 + _y},
                    {x: 50 + temp_x + _x, y: 50 + _y}
                ]),
                getTFFedge(node, {port_1: temp_1, port_2: "CH2"}, {direct_1: "block", direct_2: "block"}, null),
                getTFFedge(node, {port_1: temp_1, port_2: "CH3"}, {direct_1: "block", direct_2: "block"}, [
                    {x: 50 + temp_x + _x, y: 75 + _y},
                    {x: 50 + temp_x + _x, y: 100 + _y}
                ]),
                getTFFedge(node, {port_1: temp_1, port_2: "CH4"}, {direct_1: "block", direct_2: "block"}, [
                    {x: 50 + temp_x + _x, y: 75 + _y},
                    {x: 50 + temp_x + _x, y: 128 + _y}
                ])
            ];

            const edges = [
                _edges[port_index],
                getTFFedge(node, {port_1: temp_2, port_2: "r"}, {direct_1: "block", direct_2: "block"}, null)
            ];
            if (left === "PT1") {
                edges.push(
                    getTFFedge(node, {port_1: temp_1}, {direct_2: "block"}, [
                        {x: _x, y: 85 + _y},
                        {x: 60 + _x, y: 85 + _y},
                        {x: 60 + _x, y: 35 + _y},
                        {x: 160 + _x, y: 35 + _y}
                    ])
                );
            } else if (right === "PT1") {
                edges.push(
                    getTFFedge(node, {port_1: temp_1}, {direct_2: "block", direct_3: true}, [
                        {x: 300 + _x, y: 85 + _y},
                        {x: 40 + temp_x + _x, y: 85 + _y},
                        {x: 40 + temp_x + _x, y: 35 + _y}
                    ])
                );
            }
            edges.forEach(item => graph.addEdge(item));
            const _voa = filterNeId(node.id);
            // 使VOA显示高于线
            if (left === "PT1") {
                [
                    {
                        group: "line2_1",
                        id: `${node.id}line2_1`
                    },
                    {
                        group: "line2_2",
                        id: `${node.id}line2_2`
                    },
                    {
                        group: "voa2",
                        id: `${_voa}-VOA`,
                        attrs: {
                            text: {text: "VOA"}
                        }
                    },
                    {
                        group: "voa2_1",
                        id: `${node.id}voa2_1`
                    },
                    {
                        group: "voa2_2",
                        id: `${node.id}voa2_2`
                    }
                ].forEach(port => {
                    node.addPort(port);
                });
                // 画线
                [
                    {
                        source: {cell: node.id, port: `${node.id}voa2_2`},
                        target: {cell: node.id, port: `${node.id}voa2_1`},
                        attrs: {
                            line: {stroke: "#000", strokeWidth: 1}
                        }
                    }
                ].forEach(edge => {
                    graph.addEdge(edge);
                });
            } else if (right === "PT1") {
                [
                    {
                        group: "line3_1",
                        id: `${node.id}line3_1`
                    },
                    {
                        group: "line3_2",
                        id: `${node.id}line3_2`
                    },
                    {
                        group: "voa3",
                        id: `${_voa}-VOA`,
                        attrs: {
                            text: {text: "VOA"}
                        }
                    },
                    {
                        group: "voa3_1",
                        id: `${node.id}voa3_1`
                    },
                    {
                        group: "voa3_2",
                        id: `${node.id}voa3_2`
                    }
                ].forEach(port => {
                    node.addPort(port);
                });
                // 画线
                [
                    {
                        source: {cell: node.id, port: `${node.id}voa3_2`},
                        target: {cell: node.id, port: `${node.id}voa3_1`},
                        attrs: {
                            line: {stroke: "#000", strokeWidth: 1}
                        }
                    }
                ].forEach(edge => {
                    graph.addEdge(edge);
                });
            }
        } else if (temp_2) {
            graph.addEdge(
                getTFFedge(node, {port_1: temp_2, port_2: "r"}, {direct_1: "block", direct_2: "block"}, null)
            );
        }
    };
    const getTFFedge = (node, port, direct, vertices) => {
        const {port_1, port_2} = port;
        const {direct_1, direct_2, direct_3} = direct;
        if (direct_3) {
            return {
                source: {cell: node.id, port: port_1},
                target: {cell: `${node.id}_TFF_inner`, port: "pt_assist_1"},
                vertices,
                attrs: {
                    line: {
                        stroke: "#9af",
                        strokeWidth: 1,
                        sourceMarker: direct_1,
                        targetMarker: direct_2
                    }
                }
            };
        }
        return {
            source: {cell: node.id, port: port_1},
            target: {cell: `${node.id}_TFF_inner`, port: port_2},
            vertices,
            attrs: {
                line: {
                    stroke: "#9af",
                    strokeWidth: 1,
                    sourceMarker: direct_1,
                    targetMarker: direct_2
                }
            }
        };
    };
    const getText = (suffix, bi, power, type) => {
        // 类型一 不换行 在电卡（LINECAED）中使用
        if (type === 1) {
            return `${suffix} ${
                bi
                    ? `${power?.[0] != null ? ` In: ${power[0]}` : ""}${
                          power?.[1] != null
                              ? ` Out: ${power[1]}${
                                    power?.[3] != null ? `PreFec: ${Number(power[3])?.toExponential(1)}` : ""
                                }${power?.[4] != null ? `OSNR: ${power[4]}` : ""}`
                              : ""
                      }${power?.[2] != null ? ` Type: ${OPERATIONAL_MODE_MAP[power[2]] ?? power[2]}` : ""}`
                    : ""
            }${suffix.slice(-3).endsWith("IN") && power ? `${power[0] != null ? ` In: ${power[0]}` : ""}` : ""}${
                suffix.slice(-3).endsWith("OUT") && power ? `${power[0] != null ? ` Out: ${power[0]}` : ""}` : ""
            }`;
        }
        // 类型二 换行 power
        if (type === 2) {
            return `${suffix} ${
                bi
                    ? `${power?.[0] != null ? `\nIn: ${power[0]}` : ""}${
                          power?.[1] != null
                              ? `\nOut: ${power[1]}${
                                    power?.[3] != null ? `\nPreFec: ${Number(power[3])?.toExponential(1)}` : ""
                                }${power?.[4] != null ? `\nOSNR: ${power[4]}` : ""}`
                              : ""
                      }${power?.[2] != null ? `\nType: ${OPERATIONAL_MODE_MAP[power[2]] ?? power[2]}` : ""}`
                    : ""
            }${suffix.slice(-3).endsWith("IN") && power ? `${power[0] != null ? `\nIn: ${power[0]}` : ""}` : ""}${
                suffix.slice(-3).endsWith("OUT") && power ? `${power[0] != null ? `\nOut: ${power[0]}` : ""}` : ""
            }`;
        }
    };

    const getPower = async () => {
        const graphNode = refGraph.current?.getNodes();
        const graph = refGraph.current;
        const primery = {sourceMarker: "block", strokeDasharray: {}, targetMarker: "block"};
        const secondery = {sourceMarker: {}, strokeDasharray: "5 5", targetMarker: "block"};
        const secondery_left = {sourceMarker: "block", strokeDasharray: "5 5", targetMarker: {}};
        if (powers) {
            if (type === "client") {
                // 设置port的光功率 数据
                const data = [];
                graphNode?.map(item => {
                    const [ne_id] = item.id.split("/");
                    const _type = item.store.data.param.type;
                    const left = item.getPortsByGroup("left");
                    const right = item.getPortsByGroup("right");
                    left.forEach(l => data.push(`${item.id},${l.id.split("/")[0]},${l.id}`));
                    right.forEach(r => data.push(`${item.id},${r.id.split("/")[0]},${r.id}`));
                    if (_type === "protection") {
                        const port = right[0].id.split("-")?.splice(1, 3).join("-");
                        const powerInfo = powers?.[ne_id] ?? {};
                        // edge 也可以通过此方法获得
                        const children = item.getChildren();
                        // 编写保护逻辑
                        if (powerInfo[`APS-${port}`]) {
                            // APSS/APSP 上下会发生变化
                            const apsType = powerInfo[`APS-${port}`][0];
                            if (apsType === "SECONDARY") {
                                if (children?.[0]?.getTarget()?.port?.endsWith("APSS")) {
                                    children?.[0].prop("attrs/line", primery);
                                    children?.[1].prop("attrs/line", secondery);
                                } else if (children?.[0]?.getTarget()?.port?.endsWith("APSP")) {
                                    children?.[0].prop("attrs/line", secondery);
                                    children?.[1].prop("attrs/line", primery);
                                }
                            } else if (apsType === "PRIMARY") {
                                if (children?.[0]?.getTarget()?.port?.endsWith("APSS")) {
                                    children?.[0].prop("attrs/line", secondery);
                                    children?.[1].prop("attrs/line", primery);
                                } else if (children?.[0]?.getTarget()?.port?.endsWith("APSP")) {
                                    children?.[0].prop("attrs/line", primery);
                                    children?.[1].prop("attrs/line", secondery);
                                }
                            }
                        } else {
                            children?.[1].prop("attrs/line", {
                                strokeDasharray: {},
                                targetMarker: {}
                            });
                            children?.[0].prop("attrs/line", {
                                sourceMarker: {},
                                strokeDasharray: {},
                                targetMarker: {}
                            });
                        }
                    }
                });

                data.forEach(d => {
                    const [cellId, ne_id, portId] = d.split(",");
                    const _portId = portId.split("/")[1];
                    const powerInfo = powers?.[ne_id] ?? {};
                    const node = graph.getCellById(cellId);
                    const text = getText(_portId, "IN", powerInfo[_portId], 2);
                    node?.portProp(portId, "attrs/text", {text});
                });
                return;
            }
            graphNode?.map(item => {
                const ne_id = `${item?.id?.split(":")[0]}:${item?.id?.split(":")[1]}`;
                const _type = item?.id?.split(":")[2]?.split("-")[0];
                Object.keys(powers).forEach(key => {
                    // raman
                    if (key === item?.attr("ramanNe") && powers[key]) {
                        const suffix = item.attr("text/text");
                        item.setAttrs({
                            label: {
                                text: `${suffix}\nIN-Power: ${+powers[key].ramanChannelSignalPower / 10}\nGain: ${+powers[key].ramanChannelConfigGain / 10}`
                            }
                        });
                    }
                    const powerInfo = powers?.[key] ?? {};
                    const childNode = item.id?.replace(/\(([^)]+)\)/g, "")?.split(":")[2];
                    if (_type === "OLP" && ne_id === key) {
                        const children = item.getChildren();
                        const port = item.port.ports[0].id.split("-")?.splice(1, 3).join("-");
                        const _port = `APS-${port}`;
                        const isLeft = item.getPortsByGroup("left").length > 1;
                        // secondery_left
                        if (powerInfo[_port]) {
                            if (powerInfo[_port][0] === "SECONDARY") {
                                if (item.port.ports[1]?.group === "left") {
                                    children?.[0].prop("attrs/line", isLeft ? secondery_left : secondery);
                                    children?.[1].prop("attrs/line", primery);
                                }
                                if (item.port.ports[1]?.group === "right") {
                                    children?.[1].prop("attrs/line", isLeft ? secondery_left : secondery);
                                    children?.[0].prop("attrs/line", primery);
                                }
                            } else {
                                if (item.port.ports[1]?.group === "left") {
                                    children?.[0].prop("attrs/line", primery);
                                    children?.[1].prop("attrs/line", isLeft ? secondery_left : secondery);
                                }
                                if (item.port.ports[1]?.group === "right") {
                                    children?.[1].prop("attrs/line", primery);
                                    children?.[0].prop("attrs/line", isLeft ? secondery_left : secondery);
                                }
                            }
                        }
                    }
                    item.port.ports?.map(i => {
                        const suffix = i.id.split("-").slice(3).join("-");
                        const power = powerInfo[i.id];
                        const bi = !(suffix.slice(-3).endsWith("IN") || suffix.slice(-3).endsWith("OUT"));
                        // 获取ne_id 通过 ne_id 与 key 添加数据
                        const portId = item.id?.replace(/\(([^)]+)\)/g, "");
                        const _portId = `${portId.split(":")[0]}:${portId.split(":")[1]}`;
                        if (_type === "LINECARD") {
                            const _portName = i.id?.replace("PORT", "LINECARD");
                            // L口 C口
                            const L = i.id?.split("-")[3].startsWith("L");
                            // client 非保护判断
                            if (nodeData?.och?.length === 2 && !L && typeClient) {
                                if (
                                    key === _portId &&
                                    ([_portName, i.id].includes(nodeData.z.port) ||
                                        [_portName, i.id].includes(nodeData.a.port))
                                ) {
                                    const text = getText(suffix, bi, power, 2);
                                    item?.portProp(i.id, "attrs/text", {text});
                                } else if (key === _portId) {
                                    item?.portProp(i.id, "attrs/text", {text: ""});
                                }
                                return;
                            }
                            if (item.port.ports?.length >= 10) {
                                if (power) {
                                    if (key === _portId) {
                                        // 判断是否为L端口
                                        if (!L) {
                                            const text = getText(suffix, bi, power, 1);
                                            item?.portProp(i.id, "attrs/text", {text});
                                        } else {
                                            const text = getText(suffix, bi, power, 2);
                                            item?.portProp(i.id, "attrs/text", {text});
                                        }
                                    }
                                }
                            } else {
                                const text = getText(suffix, bi, power, 2);
                                if (power) {
                                    if (key === _portId) {
                                        item?.portProp(i.id, "attrs/text", {text});
                                    }
                                }
                            }
                        } else {
                            let text = getText(suffix, bi, power, 2);
                            if (["voa0", "voa1"].includes(i.group)) {
                                // PA LA1 LA2 转换
                                const _suffix = `${suffix.split("-")[0] === "PA" ? "VOA" : ""}${
                                    suffix.split("-")[0] === "LA1" ? "VOA1" : ""
                                }${suffix.split("-")[0] === "LA2" ? "VOA2" : ""}`;
                                text = `${_suffix}${power?.[0] != null ? `:${power[0]}` : ""}`;
                            } else if (["voa2", "voa3"].includes(i.group)) {
                                text = `${suffix}${power?.[0] != null ? `:${power[0]}` : ""}`;
                            } else if (["right_1", "right_2", "left_1", "left_2"].includes(i.group)) {
                                text = `${suffix}${
                                    power?.[0] != null
                                        ? `${
                                              suffix.slice(-3).endsWith("IN")
                                                  ? `\nIn: ${power[0]}`
                                                  : `\nOut: ${power[0]}`
                                          }`
                                        : ""
                                }`;
                            }
                            if (power) {
                                // 获取ne_id 通过 ne_id 与 key 添加数据
                                if (key === _portId) {
                                    item?.portProp(i.id, "attrs/text", {text});
                                }
                            }
                        }
                    });
                    if (childNode) {
                        const _power = item.id.startsWith(key) ? powerInfo[childNode] : "";
                        if (_power) {
                            const suffix = childNode.split("-").slice(3).join("-");
                            // 设置card内部组件显示数据
                            const _text = _power[0] || "";
                            let text = `${suffix}${_text !== "" ? `:\n${_text}` : ""}`;
                            if (suffix === "OSC" || suffix === "OSC1" || suffix === "OSC2") {
                                text = `${suffix}${_text !== "" ? `:${_text}` : ""}`;
                                // item?.resize(85, 18);
                                // const pos = item?.position();
                                // item?.position(pos.x - 25, pos.y);
                            }
                            item?.prop("attrs/text", {text});
                        }
                    }
                });
            });
        }
    };
    const filterNeId = id => {
        return id.slice(id.lastIndexOf(":") + 1, id.indexOf("("));
    };

    const filterOffLine = (a, b) => {
        const setB = new Set(b.map(item => item));
        return a.filter(item => !setB.has(item));
    };

    const getOffNe = () => {
        const graph = refGraph.current;
        const graphNode = refGraph.current?.getNodes();
        try {
            // 获取所有，判断返回离线
            objectGet("config:ne", {}).then(rs => {
                const _offLineCards = [];
                const _rs = rs.documents
                    .map(item => {
                        if (item.value.runState === 0) {
                            return {neId: item.value.ne_id};
                        }
                    })
                    .filter(i => i !== undefined);
                _rs?.map(item => {
                    graphNode?.map(node => {
                        if (type === "client") {
                            const [_ne] = node.id.split("/");
                            if (_ne === item.neId) {
                                graph?.getCellById(node.id)?.attr("body/fill", "#f0f0f0");
                                _offLineCards.push(node.id);
                            }
                        } else {
                            const _ne = node.id.slice(0, node.id.lastIndexOf(":"));
                            if (node?.getChildren() && _ne === item.neId) {
                                graph?.getCellById(node.id)?.attr("body/fill", "#f0f0f0");
                                _offLineCards.push(node.id);
                            }
                        }
                    });
                });
                setOffLineCards(_offLineCards);

                if (offLineCards.length > 0) {
                    const changeNodes = filterOffLine(offLineCards, _offLineCards);
                    if (changeNodes.length > 0) {
                        changeNodes?.map(nodeId => {
                            graph?.getCellById(nodeId)?.attr("body/fill", "#fff");
                        });
                    }
                }
            });
        } catch (er) {
            //
        }
    };
    // 告警状态
    const getAlarmStates = alarms => {
        const graph = refGraph.current;
        const ALARM_SEVERITY = ["UNKNOWN", "WARNING", "MINOR", "MAJOR", "CRITICAL"];
        const graphNode = refGraph.current?.getNodes();
        const alarmNodes = [];
        const alarmPorts = [];
        const nodes = [];
        const ports = [];

        const clearData = {nodes: [], ports: []};
        if (type === "client") {
            graphNode?.map(item => {
                const [ne_id, card] = item.id.split("/");
                const _type = item.store.data.param.type;
                const left = item.getPortsByGroup("left");
                const right = item.getPortsByGroup("right");
                if (item?.attr("tooltip")) {
                    clearData.nodes.push({ne_id, cellId: item.id, card});
                }
                [...right, ...left].forEach(port => {
                    ports.push(`${item.id},${port.id.split("/")[0]},${port.id}`);
                    if (port?.tip) {
                        clearData.ports.push({ne_id, cellId: item.id, portId: port.id});
                    }
                });
                if (["TRANSCEIVER", "protection"].includes(_type)) {
                    if (_type === "TRANSCEIVER") {
                        // ne_id,TRANSCEIVER
                        const card = item.id.split(",")[1];
                        nodes.push(`${item.id},${card}`);
                    } else {
                        nodes.push(`${item.id},${ne_id},${card}`);
                    }
                }
            });
            alarms.forEach(alarm => {
                ports.forEach(port => {
                    const [cellId, ne_id, portId] = port.split(",");
                    if (ne_id === alarm?.ne_id && alarm?.resource === portId.split("/")[1]) {
                        alarmPorts.push({
                            ...alarm,
                            cell: cellId,
                            resource: portId,
                            _resource: alarm.resource
                        });
                    }
                });
                nodes.forEach(node => {
                    const [cellId, ne_id, card] = node.split(",");
                    //
                    let _ne_id = ne_id;
                    let _cellId = cellId;
                    if (ne_id === card) {
                        _ne_id = cellId;
                        _cellId = `${cellId},${ne_id}`;
                    }
                    if (_ne_id === alarm?.ne_id && alarm?.resource === card) {
                        alarmNodes.push({
                            ...alarm,
                            _resource: alarm.resource,
                            resource: _cellId
                        });
                    }
                });
            });
        } else {
            graphNode?.map(item => {
                const ne_id = item.id?.slice(0, item.id.lastIndexOf(":"));
                const left = item.getPortsByGroup("left");
                const right = item.getPortsByGroup("right");
                const left_1 = item.getPortsByGroup("left_1");
                const left_2 = item.getPortsByGroup("left_2");
                const right_1 = item.getPortsByGroup("right_1");
                const right_2 = item.getPortsByGroup("right_2");
                const card = item.id?.replace(/\(([^)]+)\)/g, "");
                if (item?.attr("tooltip")) {
                    clearData.nodes.push({ne_id, cellId: item.id, card});
                }
                [...left, ...left_1, ...left_2, ...right, ...right_1, ...right_2].forEach(port => {
                    ports.push({ne_id, cellId: item.id, portId: port.id});
                    if (port?.tip) {
                        clearData.ports.push({ne_id, cellId: item.id, portId: port.id});
                    }
                });
                // nodes
                nodes.push({ne_id, cellId: item.id, card});
            });
            alarms.forEach(alarm => {
                ports.forEach(port => {
                    const {cellId, ne_id, portId} = port;
                    if (ne_id === alarm?.ne_id && alarm?.resource === portId) {
                        alarmPorts.push({
                            ...alarm,
                            cell: cellId,
                            resource: portId,
                            _resource: alarm.resource
                        });
                    }
                });
                nodes.forEach(node => {
                    // card *************:830:LINECARD-1-3
                    const {cellId, card} = node;
                    const nodeId = `${alarm.ne_id}:${alarm.resource}`;
                    if (nodeId === card || nodeId.startsWith(`${card}-`)) {
                        alarmNodes.push({
                            ...alarm,
                            _resource: alarm.resource,
                            resource: cellId
                        });
                    }
                });
            });
        }

        // clear之前告警 通过graph获取tip与tooltip属性存在告警
        if (clearData.ports.length > 0) {
            // const changeNodes = filterAlarmPort(clearData.ports, _alarmPorts);
            clearData.ports?.forEach(alarm => {
                const alarmNode = graph?.getCellById(alarm.cellId);
                alarmNode.portProp(alarm.portId, "attrs/circle", {stroke: "#31d0c6"});
                alarmNode.portProp(alarm.portId, "tip", null);
            });
            clearData.ports = [];
        }
        if (clearData.nodes.length > 0) {
            // const changeNodes = filterAlarmNode(clearData.nodes, _alarmNodes);
            clearData.nodes?.forEach(alarm => {
                const alarmNode = graph?.getCellById(alarm.cellId);
                alarmNode?.attr("body/stroke", "#31d0c6");
                alarmNode?.attr("tooltip", null);
                alarmNode?.removeTool("tooltip");
            });
            clearData.nodes = [];
        }

        const _alarmNodes = alarmNodes.reduce((prev, alarm) => {
            const existingObj = prev.find(item => item.alarm.resource === alarm.resource);
            const _card = alarm._resource;

            if (existingObj) {
                const index = prev.indexOf(existingObj);
                if (ALARM_SEVERITY.indexOf(alarm.severity) > ALARM_SEVERITY.indexOf(existingObj.alarm.severity)) {
                    prev[index].alarm = alarm;
                }
                prev[index].tdata.push(`${alarm["alarm-abbreviate"]} ${_card},${ALARM_COLOR[alarm.severity].color}`);
            } else {
                prev.push({
                    alarm,
                    tdata: [`${alarm["alarm-abbreviate"]} ${_card},${ALARM_COLOR[alarm.severity].color}`]
                });
            }
            return prev;
        }, []);
        _alarmNodes.forEach(alarm => {
            const alarmNode = graph?.getCellById(alarm.alarm.resource);
            alarmNode?.attr("body/stroke", ALARM_COLOR[alarm.alarm.severity].color);
            const {tdata} = alarm;
            // 去重
            const _tdata = tdata.filter((item, index) => tdata.indexOf(item) === index);
            const toTipData = alarmNode?.attr("tooltip");
            if (toTipData) {
                if (!arraysAreEqual(toTipData, _tdata)) {
                    alarmNode?.attr("tooltip", null);
                    alarmNode?.attr("tooltip", _tdata);
                }
            } else {
                alarmNode?.attr("tooltip", _tdata);
            }
            if (!alarmNode?.hasTool("tooltip")) {
                alarmNode?.addTools([
                    {
                        name: "tooltip"
                    }
                ]);
            }
        });
        const _alarmPorts = alarmPorts.reduce((acc, alarm) => {
            const existingObj = acc.find(
                item => item.alarm.resource === alarm.resource && item.alarm.ne_id === alarm.ne_id
            );
            if (existingObj) {
                const index = acc.indexOf(existingObj);
                if (ALARM_SEVERITY.indexOf(alarm.severity) > ALARM_SEVERITY.indexOf(existingObj.alarm.severity)) {
                    acc[index].alarm = alarm;
                }
                acc[index].tdata.push(
                    `${alarm["alarm-abbreviate"]} ${alarm._resource},${ALARM_COLOR[alarm.severity].color}`
                );
            } else {
                acc.push({
                    alarm,
                    tdata: [`${alarm["alarm-abbreviate"]} ${alarm._resource},${ALARM_COLOR[alarm.severity].color}`]
                });
            }
            return acc;
        }, []);
        _alarmPorts.forEach(alarm => {
            const alarmNode = graph?.getCellById(alarm.alarm.cell);
            alarmNode?.port.ports.map(port => {
                if (port.id === alarm.alarm.resource) {
                    const {tdata} = alarm;
                    const toTipData = alarmNode.portProp(alarm.alarm.resource, "tip");
                    alarmNode.portProp(alarm.alarm.resource, "attrs/circle", {
                        stroke: ALARM_COLOR[alarm.alarm.severity].color
                    });
                    // 去重
                    const _tdata = tdata.filter((item, index) => tdata.indexOf(item) === index);
                    if (toTipData) {
                        if (!arraysAreEqual(toTipData, _tdata)) {
                            alarmNode.portProp(alarm.alarm.resource, "tip", null);
                            alarmNode.portProp(alarm.alarm.resource, "tip", _tdata);
                        }
                    } else {
                        alarmNode.portProp(alarm.alarm.resource, "tip", _tdata);
                    }
                }
            });
        });
    };
    // 判断数组完全相等
    const arraysAreEqual = (arr1, arr2) => JSON.stringify(arr1) === JSON.stringify(arr2);

    useEffect(() => {
        if (service) {
            draw();
        }
    }, [service]);

    useEffect(() => {
        if (dataChanged.data?.type === "aps" && service) {
            handleOnClick();
        }
    }, [dataChanged]);

    useEffect(() => {
        if (powers) {
            getPower();
            getOffNe();
        }
    }, [powers]);
    useEffect(() => {
        if (alarms) {
            debounce(() => {
                getAlarmStates(refAlarm.current);
            }, 1000);
        }
    }, [alarms]);

    useEffect(() => {
        draw();
    }, [updateServiceView]);

    // 执行销毁，Tooltip 闭包
    const unDraw = () => {
        refGraph.current?.dispose();
        if (!portTipRef.current) return;
        portTipRef.current.style.left = "-1000px";
        portTipRef.current.style.top = "-1000px";
    };
    const handleOnClick = () => {
        // 刷新时 port tooltip消失
        if (portTipRef.current) {
            portTipRef.current.style.left = "-1000px";
            portTipRef.current.style.top = "-1000px";
        }
        if (provision?.portList && service) {
            try {
                runAsync({entity: provision.portList}).then(res => {
                    if (res?.apiResult === "fail") {
                        message.error(labelList.get_data_fail).then();
                        setOffLineCards([]);
                        return;
                    }
                    setPowers(res);
                    getAlarmStates(refAlarm.current);
                });
            } catch (error) {
                /* empty */
            }
        }
    };

    // Tooltip title 显示的内容
    const _div = (
        <>
            {portTooltipData.map(item => (
                <div key={item} style={{display: "flex", alignItems: "center"}}>
                    <Badge count={1} color={item.split(",")[1]} dot />{" "}
                    <span style={{marginLeft: "10px"}}>{item.split(",")[0]}</span>
                </div>
            ))}
            {!portTooltipData && <div>Loading...</div>}
        </>
    );

    const exportBtn = () => {
        if (refGraph.current) {
            const graph = refGraph.current;
            graph.zoomToFit({padding: 100});
            graph.exportSVG("chart", {viewBox: 100, padding: 100, copyStyles: false});
        }
    };

    return (
        <>
            <div
                ref={refDIV}
                style={{
                    background: "#e6f7ff",
                    position: "absolute",
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0
                }}
            />
            <Tooltip arrow={false} overlayStyle={{maxWidth: "100%"}} title={_div} overlayClassName="x6-tooltip" open>
                <span style={{position: "relative", left: -1000, top: -1000}} />
            </Tooltip>
            <div
                style={{
                    position: "relative",
                    display: "flex",
                    justifyContent: "flex-end",
                    paddingTop: 2,
                    paddingRight: 2,
                    height: "43px"
                }}
            >
                <Button onClick={exportBtn}>{labelList.export}</Button>
                <div style={{width: "10px"}} />
                <Button onClick={handleOnClick} loading={powerLoad}>
                    {labelList.refresh}
                </Button>
            </div>
        </>
    );
};

export default ServiceView5;
