import {message} from "antd";
import {apiEditRpc, apiRpc, getStateData, netconfGetByXML, objectGet, requestSyncTimer} from "@/modules-otn/apis/api";
import {sleep, getDeviceStateValue, rootModal} from "@/modules-otn/utils/util";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";

const getDataNE5 = async selectNe => {
    const configRs = await netconfGetByXML({
        ne_id: selectNe,
        type: "get-config",
        msg: true,
        xml: {
            components: {
                $: {
                    xmlns: "http://openconfig.net/yang/platform"
                },
                component: {}
            },
            "terminal-device": {
                $: {
                    xmlns: "http://openconfig.net/yang/terminal-device"
                },
                "logical-channels": {
                    channel: {}
                }
            }
        }
    });
    const stateRs = await getStateData({
        ne_id: selectNe
    });
    return {config: configRs, state: stateRs?.data?.value?.data?.["state-data"]};
};

const getDefaultSignalType = async (selectNe, selectPort) => {
    // const portComponent = await netconfGetByXML({
    //     ne_id: selectNe,
    //     msg: true,
    //     xml: {
    //         components: {
    //             $: {
    //                 xmlns: "http://openconfig.net/yang/platform"
    //             },
    //             component: {
    //                 name: selectPort,
    //                 port: {
    //                     config: {}
    //                 }
    //             }
    //         }
    //     }
    // });
    const portComponent = (
        await objectGet("ne:5:component", {
            ne_id: selectNe,
            name: selectPort
        })
    )?.documents?.[0];
    if (portComponent) {
        const defaultSignalType = portComponent?.value?.data?.port?.config?.["layer-protocol-name"];
        return defaultSignalType?.substring?.(defaultSignalType.indexOf("_") + 1);
    }
    return "";
};

const getSignalType = async (selectNe, selectPort) => {
    // const rs = await netconfGetByXML({
    //     ne_id: selectNe,
    //     msg: true,
    //     xml: {
    //         components: {
    //             $: {
    //                 xmlns: "http://openconfig.net/yang/platform"
    //             },
    //             component: {
    //                 $: {
    //                     xmlns: "http://openconfig.net/yang/platform"
    //                 },
    //                 name: selectPort
    //             }
    //         }
    //     }
    // });
    const options = [];
    const rs = (
        await objectGet("ne:5:component", {
            ne_id: selectNe,
            name: selectPort
        })
    )?.documents?.[0];
    if (rs) {
        const {port} = rs.value.data;
        (
            port?.state?.["supported-layer-protocol-names"]?.["supported-layer-protocol-name"] ??
            port?.config?.["supported-layer-protocol-names"]?.["supported-layer-protocol-name"]
        )?.map(i => {
            const l = i.substring(i.indexOf("_") + 1);
            if (l !== "NA") {
                options.push({label: l, value: i});
            }
        });
    }
    return options;
};

const parsePortManagerData = async (rs, selectNe, cardMach) => {
    try {
        const prRs = await objectGet("nms:provision", {});
        const services = prRs.documents;
        const _dataList = [];
        const components = rs?.config?.components?.component ?? [];
        const channels = rs?.config?.["terminal-device"]?.["logical-channels"]?.channel ?? [];
        components.forEach(component => {
            if (component.name.startsWith("LINECARD-")) {
                if (cardMach && component.name !== cardMach) {
                    return true;
                }
                const card = {
                    card: component.name,
                    "actual-vendor-type":
                        component?.config?.["vendor-type-preconf"] ??
                        getDeviceStateValue(rs.state, component.name, "actual-vendor-type")
                }; // card
                const cardNameKeys = `${component.name.replace("LINECARD", "")}-`;
                const ports = components.filter(cp => cp.name.startsWith(`PORT${cardNameKeys}`));
                ports?.forEach(portComponent => {
                    const port = {
                        ...card,
                        port: portComponent.name,
                        key: portComponent.name,
                        "signal-type":
                            portComponent.port?.config?.["layer-protocol-name"]?._?.split(":")?.pop() ??
                            getDeviceStateValue(rs.state, portComponent.name, "layer-protocol-name")
                    };
                    // loopback
                    const _portNameInfo = portComponent.name.replace("PORT", "");
                    if (port.port.indexOf("L") > -1) {
                        const ochPort = portComponent.name.replace("PORT", "OCH");
                        const ochObject = components.find(i => i.name === ochPort)?.["optical-channel"]?.config;
                        if (ochObject) {
                            port.modulation = ochObject["operational-mode"];
                            port["target-output-power"] = ochObject["target-output-power"];
                        } else {
                            port.modulation = getDeviceStateValue(rs.state, ochPort, "operational-mode");
                            port["target-output-power"] = getDeviceStateValue(rs.state, ochPort, "target-output-power");
                        }
                        const ochWavelength = components.reduce((acc, item) => {
                            if (item["optical-channel"] && item["optical-channel"].config && item.name === ochPort) {
                                acc.push(item["optical-channel"].config.frequency);
                            }
                            return acc;
                        }, []);
                        port.wavelength_origin = ochWavelength[0];
                        channels.forEach(c => {
                            const channelName = c.config?.description ?? c.state?.description;
                            if (
                                channelName &&
                                channelName.endsWith(_portNameInfo) &&
                                c.otn &&
                                c.config?.description.startsWith("OTUC")
                            ) {
                                port["signal-type"] = c.config?.description?.split("-")?.[0];
                                return false;
                            }
                        });
                    } else {
                        port["port-used"] = services.find(
                            service =>
                                service.value.type === "client" &&
                                service.value.ne.find(neInfo => neInfo.ne_id === selectNe && neInfo.port === port.port)
                        )
                            ? "True"
                            : "False";
                    }
                    _dataList.push(port);
                });
            }
        });
        _dataList.sort((a, b) => {
            if (a.card !== b.card) {
                return a.card.localeCompare(b.card, "ZH-CN", {numeric: true});
            }
            return a.port.localeCompare(b.port, "ZH-CN", {numeric: true});
        });
        // updateTableData("port", cardMach, _dataList);
        return _dataList;
    } catch (e) {
        console.log(e);
        return [];
    }
};

const alertConfirm = async port => {
    return new Promise(resolve => {
        const modal2 = rootModal.confirm({
            title: gLabelList.warning,
            // eslint-disable-next-line no-unused-vars
            onOk: _ => {
                modal2.destroy();
                resolve(true);
            },
            onCancel: _ => {
                modal2.destroy();
                resolve(false);
            },
            closable: true,
            centered: true,
            content: gLabelList.effect_service_update.format(port)
        });
    });
};

const checkEffectService = async (lineCardType, portData, portType, port) => {
    let effectService = false;
    let effectServicePort = 0;
    let changeFC32G = false;
    if (lineCardType) {
        if (["2MC2", "4MC4"].includes(lineCardType)) {
            const p = portData.find(i => i.port.indexOf("C") > -1 && i["port-used"] === "True");
            if (p) {
                effectService = true;
                effectServicePort = p.port;
            }
        }
        if (["11MC2", "20MC2"].includes(lineCardType)) {
            if (portType === "PROT_FC32G") {
                // update to FC32, will update all port, so check exist port used.
                const p = portData.find(i => i.port.indexOf("C") > -1 && i["port-used"] === "True");
                if (p) {
                    effectService = true;
                    effectServicePort = p.port;
                }
            } else {
                // update to not FC32, check if exist FC32 and port used.
                const p = portData.find(i => i["signal-type"] === "PROT_FC32G" && i["port-used"] === "True");
                if (p) {
                    effectService = true;
                    effectServicePort = p.port;
                }
                if (!effectService) {
                    if (portType === "PROT_FC16G") {
                        const portTemp = port.split("C");
                        const nextPort = portData.find(i => i.port === `${portTemp[0]}C${parseInt(portTemp[1]) + 1}`);
                        if (nextPort?.["port-used"] === "True") {
                            effectService = true;
                            effectServicePort = nextPort.port;
                        }
                    } else {
                        const portTemp = port.split("C");
                        const prePort = portData.find(i => i.port === `${portTemp[0]}C${parseInt(portTemp[1]) - 1}`);
                        if (prePort?.["signal-type"] === "PROT_FC16G" && prePort?.["port-used"] === "True") {
                            effectService = true;
                            effectServicePort = prePort.port;
                        }
                    }
                }
            }
            if (portType === "PROT_FC32G" || portData.find(i => i["signal-type"] === "PROT_FC32G")) {
                changeFC32G = true;
            }
        }
    }
    // if (changeFC32G) {
    //     message.info(gLabelList.start_update_fc32, 10);
    // }
    if (effectService) {
        return await alertConfirm(effectServicePort);
    }
    return true;
};

const updatePortSignalType = async ({
    selectNe,
    data,
    portType,
    portManagerList,
    reloadData,
    setLoading,
    lineCard,
    lineCardType
}) => {
    if (!portManagerList) {
        const rs = await getDataNE5(selectNe);
        portManagerList = await parsePortManagerData(rs, selectNe, lineCard);
        console.log("portManagerList", portManagerList);
        data = portManagerList.find(i => i.port === data.port);
    }
    if (!(await checkEffectService(lineCardType, portManagerList, portType, data.port))) {
        return false;
    }

    if (!reloadData) {
        reloadData = () => {
            //
        };
    }
    if (!setLoading) {
        setLoading = () => {
            //
        };
    }

    const portTransceiverMapping = {
        "1GE": "PT_1000M",
        "10GE_LAN": "PT_10GBASE",
        "10GE_WAN": "PT_STM64",
        OCH_OTU2: "PT_OTU2",
        OTU2: "PT_OTU2",
        OCH_OTU2E: "PT_OTU2",
        OTU2E: "PT_OTU2",
        ODU2: "PT_OTU2",
        ODU2E: "PT_OTU2",
        "100GE": "PT_100GBASE",
        "400GE": "PT_400G",
        STM64: "PT_STM64",
        FC8G: "PT_FC8G",
        FC10G: "PT_FC10G",
        FC16G: "PT_FC16G",
        FC32G: "PT_FC32G",
        OTU4: "PT_OTU4"
    };

    const supportFC16 = {
        H1T10B2: [1, 3, 5, 7, 9],
        "11MC2": [1, 3, 5, 7, 9],
        T20B2: [1, 3, 5, 7, 9, 11, 13, 15, 17, 19],
        "20MC2": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19]
    };

    const support400GE = {
        H4B4: {support: [1], unSupport: [2, 3, 4]},
        "4MC4": {support: [1], unSupport: [2, 3, 4]},
        H4B4E: {support: [1], unSupport: [2, 3, 4]}
    };

    const supportFC32 = {
        H1T10B2: {support: [1, 2, 3], unSupport: [4, 5, 6, 7, 8, 9, 10]},
        "11MC2": {support: [1, 2, 3], unSupport: [4, 5, 6, 7, 8, 9, 10]},
        T20B2: {support: [1, 2, 3, 4, 5, 6], unSupport: [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]},
        "20MC2": {support: [1, 2, 3, 4, 5, 6], unSupport: [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]}
    };

    const addSyncTimer = (timeout = 1000) => {
        requestSyncTimer({
            ne_id: selectNe,
            sync: {
                type: ["components", "terminal-device"],
                interval: 20000,
                timeout: 60000,
                startTime: 5000
            }
        });
        setTimeout(() => {
            reloadData(true);
        }, timeout);
    };

    const editTransceiverType = async (port, type, callback) => {
        try {
            const portType = type.split(":").pop().replace("PROT_", "");
            let transceiverType = portTransceiverMapping[portType];
            if (!transceiverType) {
                transceiverType = `PT_${portType}`;
                console.error(
                    `No corresponding data found for this type ${portType}, it will set to ${transceiverType}`
                );
                // return {
                //     apiResult: "fail",
                //     apiMessage: `No corresponding data found for this type ${portType}`
                // };
            }
            const rs = apiEditRpc({
                ne_id: selectNe,
                params: {
                    components: {
                        component: {
                            name: port.replace("PORT", "TRANSCEIVER"),
                            transceiver: {
                                config: {
                                    "used-service-port-type-preconf": transceiverType
                                }
                            }
                        }
                    }
                },
                msg: false
            });
            if (rs.apiResult === "fail") {
                message.error(gLabelList.update_fail).then();
                setLoading(false);
                return rs;
            }
            if (callback) {
                callback();
            }
            return rs;
        } catch (e) {
            // console.log(e);
        }
    };

    const editPortTypeFor5 = async (data, portType, sync = true) => {
        const currentPortType = portManagerList.filter(i => i.port === data.port)?.[0]?.["signal-type"];
        if (currentPortType && currentPortType === portType) {
            return {apiResult: "SUCCESS", apiMessage: "No change"};
        }
        if (!data["signal-type"].endsWith("_NA")) {
            const rs1 = await editPortType(data.port, "PROT_NA");
            if (rs1.apiResult === "fail") {
                return rs1;
            }
            await sleep(1000);
        }
        const rs2 = await editPortType(data.port, portType);
        if (rs2.apiResult === "fail") {
            return rs2;
        }
        await sleep(1000);
        const rs3 = await editTransceiverType(data.port, portType);
        if (rs3.apiResult === "fail") {
            return rs3;
        }
        await sleep(5000);
        if (sync) {
            addSyncTimer();
        }
        return rs3;
    };

    const editPortToFC16 = async (data, portType) => {
        if (supportFC16[data["actual-vendor-type"]] && portType === "PROT_FC16G") {
            const portIndex = parseInt(data.port.split("C")[1]);
            if (supportFC16[data["actual-vendor-type"]].includes(portIndex)) {
                const rs = await editPortType(`${data.port.split("C")[0]}C${portIndex + 1}`, "PROT_NA");
                if (rs.apiResult === "fail") {
                    return false;
                }
            }
            return true;
        }
    };

    const editPortFC16ToOther = async (data, portType) => {
        if (portType === "PROT_FC16G") {
            return true;
        }
        const portIndex = parseInt(data.port.split("C")[1]);
        if (portIndex % 2 === 0) {
            const newPortIndex = `${data.port.split("C")[0]}C${portIndex - 1}`;
            const prePortType = portManagerList.filter(i => i.port === newPortIndex)?.[0]?.["signal-type"];
            if (prePortType && prePortType === "PROT_FC16G") {
                const rs = await editPortType(newPortIndex, "PROT_NA");
                if (rs.apiResult === "fail") {
                    return false;
                }
            }
            return true;
        }
    };

    const editPortToFC32And400GE = async (data, portType, supportPortType, supportObj) => {
        if (supportObj[data["actual-vendor-type"]] && portType === supportPortType) {
            if (supportObj[data["actual-vendor-type"]].support.includes(parseInt(data.port.split("C")[1]))) {
                let success = true;
                const toNAPortList = [
                    ...supportObj[data["actual-vendor-type"]].support,
                    ...supportObj[data["actual-vendor-type"]].unSupport
                ];
                const portNameTemp = `${data.port.split("C")[0]}C`;
                for (let i = 0; i < toNAPortList.length; i++) {
                    const rs = await editPortType(portNameTemp + toNAPortList[i], "PROT_NA");
                    if (rs.apiResult === "fail") {
                        success = false;
                        return false;
                    }
                    if (rs.apiMessage !== "No change") {
                        await sleep(1000);
                    }
                }
                if (success) {
                    for (let i = 0; i < supportObj[data["actual-vendor-type"]].support.length; i++) {
                        const portIndex = supportObj[data["actual-vendor-type"]].support[i];
                        const rs = await editPortTypeFor5(
                            {
                                "signal-type": "PROT_NA",
                                port: portNameTemp + portIndex
                            },
                            portType,
                            false
                        );
                        if (rs.apiResult === "fail") {
                            success = false;
                            return false;
                        }
                        if (rs.apiMessage !== "No change") {
                            await sleep(1000);
                        }
                        if (portType === "PROT_FC32G" && i === 0 && lineCard) {
                            const restartRS = await apiRpc({
                                ne_id: selectNe,
                                rpcName: "reboot",
                                rpcConfig: {
                                    "component-name": lineCard,
                                    "reboot-type": "cold"
                                }
                            });
                            if (!restartRS.result) {
                                return false;
                            }
                            await sleep(5000);
                        }
                    }
                }
                await sleep(2000);
                if (!success) {
                    addSyncTimer();
                }
            }
            return true;
        }
    };

    const editPortFC32And400GEToOther = async (data, portType, supportPortType, supportObj) => {
        if (supportObj[data["actual-vendor-type"]] && portType !== supportPortType) {
            let success = true;
            let hasFC32 = false;
            const portNameTemp = `${data.port.split("C")[0]}C`;
            for (let i = 0; i < portManagerList.length; i++) {
                if (
                    portManagerList[i].port.startsWith(portNameTemp) &&
                    portManagerList[i]["signal-type"] === supportPortType
                ) {
                    hasFC32 = true;
                    const rs = await editPortType(portManagerList[i].port, "PROT_NA");
                    if (rs.apiResult === "fail") {
                        success = false;
                        return false;
                    }
                    await sleep(1000);
                }
            }
            if (success && hasFC32) {
                const rs = await editPortTypeFor5(data, portType, false);
                if (rs.apiResult === "fail") {
                    message.error(rs.apiMessage);
                    setLoading(false);
                    return false;
                }
                if (supportPortType === "PROT_FC32G" && parseInt(data.port.split("C")[1]) === 1 && lineCard) {
                    const restartRS = await apiRpc({
                        ne_id: selectNe,
                        rpcName: "reboot",
                        rpcConfig: {
                            "component-name": lineCard,
                            "reboot-type": "cold"
                        }
                    });
                    if (!restartRS.result) {
                        addSyncTimer();
                        return false;
                    }
                    await sleep(5000);
                }
            }
            if (hasFC32) {
                return true;
            }
        }
    };

    const editPortType = async (port, type, callback) => {
        const currentPortType = portManagerList.filter(i => i.port === port)?.[0]?.["signal-type"];
        if (currentPortType && currentPortType === type) {
            return {apiResult: "SUCCESS", apiMessage: "No change"};
        }
        return await apiEditRpc({
            ne_id: selectNe,
            params: {
                components: {
                    component: {
                        name: port,
                        port: {
                            config: {
                                "layer-protocol-name": type
                            }
                        }
                    }
                }
            },
            msg: false,
            success: () => {
                if (callback) {
                    callback();
                }
            },
            fail: () => {
                message.error(gLabelList.update_fail).then();
                setLoading(false);
            }
        });
    };

    const updateSignalType = async (data, portType) => {
        if (portType.endsWith("_NA")) {
            setLoading(true);
            editPortType(data.port, portType, () => {
                //
            }).then();
        }
        setLoading(true);
        if (lineCardType && ["2MC2", "4MC4"].includes(lineCardType)) {
            const linePort = portManagerList.find(i => i.port.indexOf("L") > -1);
            const rs = await apiRpc({
                ne_id: selectNe,
                rpcName: "config-electrical-service",
                rpcConfig: {
                    card: lineCard,
                    "client-mode": portType,
                    "line-mode": linePort.modulation,
                    "line-frequency": linePort.wavelength_origin,
                    "line-target-output-power": linePort["target-output-power"]
                }
            });
            if (!rs.result) {
                message.error(gLabelList.save_failed);
                return false;
            }
            return true;
        }
        const r1 = await editPortFC32And400GEToOther(data, portType, "PROT_FC32G", supportFC32);
        if (r1 !== undefined) {
            return r1;
        }
        const r2 = await editPortToFC32And400GE(data, portType, "PROT_FC32G", supportFC32);
        if (r2 !== undefined) {
            return r2;
        }
        const r3 = await editPortFC32And400GEToOther(data, portType, "PROT_400GE", support400GE);
        if (r3 !== undefined) {
            return r3;
        }
        const r4 = await editPortToFC32And400GE(data, portType, "PROT_400GE", support400GE);
        if (r4 !== undefined) {
            return r4;
        }
        const r5 = await editPortToFC16(data, portType);
        if (r5 === undefined) {
            const r6 = await editPortFC16ToOther(data, portType);
            if (r6 === false) {
                return r6;
            }
        } else if (r5 === false) {
            return r5;
        }
        const rs = await editPortTypeFor5(data, portType, false);
        if (rs.apiResult === "fail") {
            message.error(rs.apiMessage);
            setLoading(false);
            addSyncTimer(3000);
            return false;
        }
        return true;
    };
    const r = await updateSignalType(data, portType);
    addSyncTimer(3000);
    return r;
};

export {updatePortSignalType, getSignalType, getDefaultSignalType};
