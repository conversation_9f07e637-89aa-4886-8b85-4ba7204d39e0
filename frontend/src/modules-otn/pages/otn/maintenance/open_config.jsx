import {DividerBox} from "rc-dock";
import stylesDevice from "@/modules-otn/pages/otn/device/device.module.scss";
import {Select, message} from "antd";
import {getValueByJPath, removeNS, getAttrValue, convertToArray, UPPER_CASES} from "@/modules-otn/utils/util";
import React, {useDeferredValue, useEffect, useRef, useState} from "react";
import {useSelector} from "react-redux";
import {useRequest} from "ahooks";
import ResourceTree from "@/modules-otn/components/chassis/resource_tree";
import {apiGetCategory, apiGetYang, getServiceData, objectGet} from "@/modules-otn/apis/api";
import {EditForm} from "@/modules-otn/components/form/edit_form";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import styles from "./open_config.module.scss";

const OpenConfig = () => {
    const [selectNe, setSelectNe] = useState();
    const readyOnlyRight = useUserRight();
    const selectNeRef = useRef(selectNe);
    const [neOptions, setNeOptions] = useState([]);
    const [drawerTreeData, setDrawerTreeData] = useState([]);
    const [formData, setFormData] = useState(null);
    const [selectNode, setSelectNode] = useState();
    const deferredSelectNode = useDeferredValue(selectNode);
    const {labelList} = useSelector(state => state.languageOTN);

    const seperator = "___";

    const generateComponentTree = data => {
        const allComponents = {};
        convertToArray(data).forEach(component => {
            const entity_type = getAttrValue(component.state, "type");
            if (entity_type) {
                allComponents[component.name] = {
                    parent: component.state?.parent,
                    treeData: {
                        id: component.name,
                        value: {
                            title: component.name
                        },
                        key: `component${seperator}${removeNS(entity_type)}${seperator}${component.name}`,
                        title: component.name,
                        objectType: "component",
                        entityType: removeNS(entity_type),
                        preconfType: getAttrValue(component.state, "vendor-type-preconf"),
                        location: getAttrValue(component.state, "location"),
                        children: []
                    }
                };
            }
        });
        return allComponents;
    };

    const generateInterfaceTree = (interfaces, componentTree) => {
        const allInterface = {};
        convertToArray(interfaces)?.forEach(interfaceObj => {
            const parentName =
                getAttrValue(interfaceObj.state, "transceiver") ?? getAttrValue(interfaceObj.state, "hardware-port");
            if (parentName && componentTree[parentName]) {
                const entity_type = getAttrValue(interfaceObj.state, "type");
                if (entity_type && typeof entity_type === "string") {
                    const treeData = {
                        id: interfaceObj.name,
                        value: {
                            title: interfaceObj.name
                        },
                        title: interfaceObj.name,
                        key: `interface${seperator}${removeNS(entity_type)}${seperator}${interfaceObj.name}`,
                        objectType: "interface",
                        entityType: removeNS(entity_type),
                        children: []
                    };
                    allInterface[interfaceObj.name] = treeData;
                    componentTree[parentName].treeData.children.push(treeData);

                    // add addresses and neighbors
                    const ipv4Addresses = interfaceObj.subinterfaces?.subinterface?.ipv4?.addresses?.address;
                    if (ipv4Addresses) {
                        convertToArray(ipv4Addresses).forEach(address => {
                            if (address.ip) {
                                treeData.children.push({
                                    id: address.ip,
                                    value: {
                                        title: address.ip
                                    },
                                    title: address.ip,
                                    key: `ipv4-address${seperator}address${seperator}${address.ip}`,
                                    objectType: "ipv4-address",
                                    parent: interfaceObj.name,
                                    entityType: "address",
                                    children: []
                                });
                            }
                        });
                    }
                    const ipv6Addresses = interfaceObj.subinterfaces?.subinterface?.ipv6?.addresses?.address;
                    if (ipv6Addresses) {
                        convertToArray(ipv6Addresses).forEach(address => {
                            if (address.ip) {
                                treeData.children.push({
                                    id: address.ip,
                                    value: {
                                        title: address.ip
                                    },
                                    title: address.ip,
                                    key: `ipv4-address${seperator}address${seperator}${address.ip}`,
                                    objectType: "ipv4-address",
                                    parent: interfaceObj.name,
                                    entityType: "address",
                                    children: []
                                });
                            }
                        });
                    }
                    const ipv4Neighbors = interfaceObj.subinterfaces?.subinterface?.ipv4?.neighbors?.neighbor;
                    if (ipv4Neighbors) {
                        convertToArray(ipv4Neighbors).forEach(neighbor => {
                            treeData.children.push({
                                id: neighbor.ip,
                                value: {
                                    title: neighbor.ip
                                },
                                title: neighbor.ip,
                                key: `ipv4-neighbor${seperator}neighbor${seperator}${neighbor.ip}`,
                                objectType: "ipv4-neighbor",
                                parent: interfaceObj.name,
                                entityType: "neighbor",
                                children: []
                            });
                        });
                    }
                    const ipv6Neighbors = interfaceObj.subinterfaces?.subinterface?.ipv6?.neighbors?.neighbor;
                    if (ipv6Neighbors) {
                        convertToArray(ipv6Neighbors).forEach(neighbor => {
                            treeData.children.push({
                                id: neighbor.ip,
                                value: {
                                    title: neighbor.ip
                                },
                                title: neighbor.ip,
                                key: `ipv4-neighbor${seperator}neighbor${seperator}${neighbor.ip}`,
                                objectType: "ipv4-neighbor",
                                parent: interfaceObj.name,
                                entityType: "neighbor",
                                children: []
                            });
                        });
                    }
                }
            }
        });
        return allInterface;
    };

    const {runAsync: getServiceDataRunAsync} = useRequest(() => getServiceData({ne_id: selectNeRef.current}), {
        manual: true,
        onSuccess: res => {
            const {apiResult, data} = res;
            if (apiResult === "complete") {
                const {components, interfaces, otdrs, aps} = data;
                const allComponents = generateComponentTree(components);
                // 当componet出问题（无parent）时，删除掉
                Object.entries(allComponents)?.forEach(([key, value]) => {
                    if (!value?.parent && !Object.values(allComponents)?.some(item => item?.parent === key)) {
                        delete allComponents[key];
                    }
                });
                // interface
                const allInterfaces = generateInterfaceTree(interfaces, allComponents);

                // amplifier
                convertToArray(data?.amplifier)?.forEach(amplifierObj => {
                    const entity_type = getAttrValue(amplifierObj.state, "type");
                    if (entity_type && typeof entity_type === "string") {
                        const treeData = {
                            id: amplifierObj.name,
                            key: `amplifier${seperator}${removeNS(entity_type)}${seperator}${amplifierObj.name}`,
                            title: amplifierObj.name,
                            objectType: "amplifier",
                            entityType: removeNS(entity_type),
                            children: []
                        };
                        allComponents[amplifierObj.name]?.treeData.children.push(treeData);
                    }
                });
                convertToArray(data?.["supervisory-channel"])?.forEach(supervisoryChannel => {
                    const parentName = supervisoryChannel.interface;
                    if (parentName) {
                        const entity_type = "supervisory-channel";
                        const treeData = {
                            id: supervisoryChannel.interface,
                            key: `supervisory-channel${seperator}${entity_type}${seperator}${supervisoryChannel.interface}`,
                            title: supervisoryChannel.interface,
                            objectType: "supervisory-channel",
                            entityType: entity_type,
                            children: []
                        };
                        allInterfaces[parentName]?.children.push(treeData);
                    }
                });
                // otdrs
                convertToArray(otdrs)?.forEach(otdr => {
                    const parentName = otdr.name;
                    if (parentName) {
                        const entity_type = "otdr";
                        const treeData = {
                            id: otdr.name,
                            key: `otdr${seperator}${entity_type}${seperator}${otdr.name}`,
                            title: otdr.name,
                            objectType: "otdr",
                            entityType: entity_type,
                            children: []
                        };
                        allComponents[parentName]?.treeData.children.push(treeData);
                    }
                });
                // channel-monitor
                convertToArray(data?.["channel-monitors"]?.["channel-monitor"])?.forEach(channelMonitor => {
                    if (channelMonitor.name) {
                        const treeData = {
                            id: channelMonitor.name,
                            key: `channel-monitor${seperator}channel-monitor${seperator}${channelMonitor.name}`,
                            title: channelMonitor.name,
                            objectType: "channel-monitor",
                            entityType: "channel-monitor",
                            children: []
                        };
                        allComponents[channelMonitor.name]?.treeData.children.push(treeData);
                    }
                });

                // aps
                convertToArray(aps?.["aps-modules"]?.["aps-module"])?.forEach(aps => {
                    if (allComponents[aps.name]) {
                        const entity_type = "aps";
                        const treeData = {
                            id: aps.name,
                            key: `aps${seperator}${entity_type}${seperator}${aps.name}`,
                            title: aps.name,
                            objectType: "aps",
                            entityType: entity_type,
                            children: []
                        };
                        allComponents[aps.name]?.treeData.children.push(treeData);
                    }
                });

                let rootNode;

                const cardData = [];
                const channels = convertToArray(
                    getValueByJPath(data, ["terminal-device", "logical-channels", "channel"])
                );
                const allChannels = {};
                channels?.forEach(channel => {
                    const assignment = channel["logical-channel-assignments"]?.assignment?.state;
                    if (assignment) {
                        const parentType = assignment["assignment-type"].toLowerCase().replace("_", "-");
                        allChannels[channel.index] = {
                            parentType,
                            parent: assignment[parentType],
                            treeData: {
                                id: channel.index,
                                value: {
                                    title: channel.state.description
                                },
                                key: `channel${seperator}${channel.index}`,
                                title: channel.state.description,
                                objectType: "channel",
                                delayTestMode: channel.config?.["delay-test-mode"] ?? null,
                                children: []
                            }
                        };
                    }
                });
                Object.values({...allComponents, ...allChannels, ...allInterfaces}).forEach(
                    ({parent, parentType, treeData}) => {
                        if (treeData) {
                            if (!parent) {
                                rootNode = treeData;
                            } else if (parentType === "logical-channel") {
                                allChannels[parent]?.treeData.children.push(treeData);
                            } else {
                                allComponents[parent]?.treeData.children.push(treeData);
                            }
                        }
                    }
                );

                rootNode?.children.forEach(child => {
                    if (child.entityType === "LINECARD" && !cardData.find(item => item.id === child.id)) {
                        cardData.push(child);
                    }
                    if (!["LINECARD", "FAN", "CONTROLLER_CARD", "SLOT", "POWER_SUPPLY"].includes(child.entityType)) {
                        if (child.entityType === "WSS") {
                            const portGroup = {
                                key: "PortGroup",
                                title: "Port AD List",
                                objectType: "PortGroup",
                                children: []
                            };
                            for (let i = child.children.length - 1; i >= 0; --i) {
                                const subChild = child.children[i];
                                if (subChild.entityType === "PORT" && subChild.title?.match(/AD\d+$/) != null) {
                                    portGroup.children.push(subChild);
                                    child.children.splice(i, 1);
                                }
                            }
                            portGroup.children.reverse();
                            if (portGroup.children.length > 0) {
                                child.children.push(portGroup);
                            }
                        }

                        cardData.push(child);
                    }
                });
                const sortByTitle = arr => {
                    if (!arr || !Array.isArray(arr)) {
                        return arr;
                    }

                    arr.forEach(item => {
                        if (item.children && item.children.length > 0) {
                            item.children = sortByTitle(item.children);
                        }
                    });

                    arr.sort((a, b) => {
                        const lowerCaseA = `${a.title}`;
                        const lowerCaseB = `${b.title}`;
                        return lowerCaseA.localeCompare(lowerCaseB, "ZH-CN", {numeric: true});
                    });

                    return arr;
                };
                sortByTitle(cardData);
                setDrawerTreeData(
                    cardData.sort((a, b) => {
                        const lowerCaseA = a.title.split("-").at(-1);
                        const lowerCaseB = b.title.split("-").at(-1);
                        return lowerCaseA.localeCompare(lowerCaseB, "ZH-CN", {numeric: true});
                    })
                );
            }
        }
    });

    useEffect(() => {
        if (selectNe) {
            getServiceDataRunAsync().then();
        } else {
            setDrawerTreeData([]);
        }
    }, [selectNe]);

    useEffect(() => {
        if (deferredSelectNode) {
            const {objectType: categoryName, id: name} = deferredSelectNode;
            apiGetCategory(categoryName, "5").then(category => {
                apiGetYang("5").then(Yang => {
                    setFormData({
                        Yang,
                        categoryName,
                        category,
                        keys: ["ipv4-address", "ipv6-address", "ipv4-neighbor", "ipv6-neighbor"].includes(categoryName)
                            ? [deferredSelectNode.parent, deferredSelectNode.title]
                            : [name],
                        title: name,
                        ne_id: selectNeRef.current
                    });
                });
            });
        } else {
            setFormData(null);
        }
    }, [deferredSelectNode]);

    return (
        <div style={{flex: 1, overflow: "hidden", display: "flex", flexDirection: "column"}}>
            <DividerBox className={stylesDevice.dockLayout} style={{height: "100%"}}>
                <DividerBox className={styles.tree}>
                    <div
                        style={{
                            flex: 1,
                            display: "flex",
                            flexDirection: "column",
                            padding: 24,
                            overflow: "auto"
                        }}
                    >
                        <div style={{display: "flex", flexDirection: "row", alignItems: "center"}}>
                            NE
                            <Select
                                value={selectNe ?? null}
                                placeholder={labelList.please_select}
                                // filterSort={sortLabel()}
                                allowClear
                                style={{paddingLeft: 32, flex: 1}}
                                onDropdownVisibleChange={open => {
                                    if (open) {
                                        objectGet("config:ne", {}).then(rs => {
                                            const {apiResult, apiMessage, documents} = rs;
                                            if (apiResult === "fail") {
                                                message.error(apiMessage).then();
                                                return;
                                            }
                                            setNeOptions(
                                                documents.map(item => {
                                                    return {
                                                        label: item.value.name,
                                                        value: item.value.ne_id,
                                                        type: item.value.type,
                                                        disabled: !(item?.value.state === 1 || item?.value.state < 0)
                                                    };
                                                })
                                            );
                                        });
                                    }
                                }}
                                onChange={v => {
                                    setSelectNe(v);
                                    selectNeRef.current = v;
                                    setFormData(null);
                                }}
                                options={neOptions}
                            />
                        </div>
                        <div style={{marginTop: 24, overflow: "auto", flex: 1, marginLeft: -5}}>
                            {drawerTreeData.length > 0 && (
                                <ResourceTree
                                    treeData={drawerTreeData}
                                    ne_id={selectNe}
                                    dbClickEnable={false}
                                    contextMenuEnable={false}
                                    updateSelectedNode={v => {
                                        setSelectNode(v);
                                    }}
                                />
                            )}
                        </div>
                    </div>
                </DividerBox>
                <DividerBox className={styles.container}>
                    <div className={styles.content}>
                        {formData && (
                            <EditForm
                                {...formData}
                                setForm={() => {}}
                                afterFinish={() => {}}
                                type="5"
                                opationType="edit"
                                formPanel
                                formatTabName={name => {
                                    try {
                                        return name
                                            .split("-")
                                            .map(item => {
                                                if (UPPER_CASES.some(value => name === value))
                                                    return name.toUpperCase();
                                                return `${item.slice(0, 1).toUpperCase()}${item.slice(1).toLowerCase()}`;
                                            })
                                            .join(" ");
                                    } catch (e) {
                                        return name;
                                    }
                                }}
                                readOnly={readyOnlyRight.disabled}
                            />
                        )}
                    </div>
                </DividerBox>
            </DividerBox>
        </div>
    );
};
export default OpenConfig;
