import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import {useEffect, useState} from "react";
import serviceLayer0Styles from "@/modules-otn/pages/otn/service/service_layer0.module.scss";
import Ocm from "./ocm";
import Otdr from "./otdr";

export default function LinkMeasurement() {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const items = [
        {
            key: "otdr",
            label: "OTDR",
            children: <Otdr />
        },
        {
            key: "ocm",
            label: "OCM",
            children: <Ocm />
        }
    ];

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();
    const pathReg = /(otdr|ocm)$/;
    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <Tabs
            items={items}
            destroyInactiveTabPane
            activeKey={currentActiveKey}
            onChange={onChange}
            className={serviceLayer0Styles.tabs}
        />
    );
}
