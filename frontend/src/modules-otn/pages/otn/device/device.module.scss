.container {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100%;

    .sidebar {
        background: #fff;
        width: 270px;
        height: 100%;
        border-right: 1px solid #ccc;
        overflow-y: auto;
    }

    .main {
        flex: 1;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        position: relative;
        padding: 5px;

        .bottomBarContainer {
            max-height: 350px;
            width: 100%;
            overflow: hidden;
            position: relative;
            z-index: 998;
            background-color: #fff;
            // transition: all .5s ease-in

            .foldBar {
                display: flex;
                justify-content: center;
                align-items: center;
                border-top: 1px solid #ccc;
            }
        }
    }
}

.connectMapContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: relative;
    flex: 1;
    overflow: hidden;
}

.mapToolsContainer {
    position: absolute;
    top: 10px;
    left: 22px;
    z-index: 4;
    background: #FFF;
    border-radius: 3px;
    box-shadow: 0 2px 2px rgba(0, 0, 0, .15);
    padding: 4px 2px;
    display: flex;
    flex-direction: row;

    .mapToolsItem {
        padding: 0 6px;
        margin: 4px 0;
        border-right: 1px #E7E7E7 solid;
    }

    .notBorderRight {
        border-right: none;
    }

    .mapToolsIcon {
        fill: #14C9BB;
    }

    .mapToolsIcon:hover {
        fill: #34DCCF;
        cursor: pointer;
    }

    .mapToolsIconDisabled,
    .mapToolsIconDisabled:hover{
        fill: #d9d9d9;
        cursor: not-allowed;
    }
}

.dockWrap {
    width: 100%;
    height: 100%;
    position: relative;
    background: rgb(240, 242, 245);
    padding: 28px;
    overflow: hidden;
}

.dockLayout {
    flex: 1;
    display: flex;
    position: relative;

    :global {
        .dock-tab {
            height: auto;
            background: #fff;
            border: 0;
            margin: 0;
        }

        .dock-tab-btn {
            padding: 4px 16px;
        }

        .dock-panel-max-btn,
        .dock-panel-min-btn {
           display: none;
        }

        .dock-hbox {
            gap: 10px;
        }

        .dock-vbox {
            gap: 1.9px;
        }

        .dock-panel, .dock-top {
            border-radius: 5px;
        }

        .dock-bar, .dock-top {
            background: #fff !important;
        }

        .dock-bar {
            font-size: 18px;
            border-bottom: 0 !important;
        }

        .drag-initiator {
            padding: 10px 0;
        }

        .dock-tab:hover,
        .dock-tab-active,
        .dock-tab-active:hover {
            color: #14C9BB;
        }

        .dock-ink-bar {
            background-color: #14C9BB;
            margin-top: 0;
        }

        .dock-extra-content {
            position: relative;
            right: 30px;
            top: 15px;
        }

        .dock-panel.dock-style-main {
            border: none;

            .dock-bar {
                background: none;
                /* border-bottom: 1px solid d; */
            }

            .dock-tab {
                /* background: rgb(79, 208, 200); */
            }
        }

        .dock-panel {
            border: none;
        }

        // card
        .dock-panel.dock-style-card {
            .dock-tab {
                color: #000;
                border: 0;
            }

            .dock-panel-min-btn, .dock-panel-max-btn {
                display: none;
            }
        }
        .ant-tree-treenode-selected .ant-tree-node-selected .ant-tree-title{
            color:#14c9bb
        }
    }
}
