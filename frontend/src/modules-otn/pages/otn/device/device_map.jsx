import {useState, useMemo, useEffect, useRef} from "react";
import {useSelector, useDispatch} from "react-redux";
import {Scene, PointLayer, LineLayer, PolygonLayer, Scale, Mapbox} from "@antv/l7";
import {DrawPoint, DrawEvent} from "@antv/l7-draw";
import {message, Badge, Dropdown, Form, Input, Space, Button} from "antd";
import Icon from "@ant-design/icons";
import {inRange, debounce} from "lodash";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {
    setLocationInfo,
    apiConfigMapToken,
    apiGetMapToken,
    apiConnectionByGroup,
    getOfflineMapData
} from "@/modules-otn/apis/api";

import {getSwitchTreeData, setSwitchTreeLocation} from "@/modules-ampcon/apis/inventory_api";
import groupSvg from "@/assets/images/device_map/group.svg?react";
import switchSvg from "@/assets/images/device_map/switch.svg?react";
import switchWarnSvg from "@/assets/images/device_map/switch_warn.svg?react";
import switchOfflineSvg from "@/assets/images/device_map/switch_offline.svg?react";
import otnSvg from "@/assets/images/device_map/otn.svg?react";
import otnWarnSvg from "@/assets/images/device_map/otn_warn.svg?react";
import otnOfflineSvg from "@/assets/images/device_map/otn_offline.svg?react";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {sortArr, isEmptyArray, isEmptyObject, DebounceButton} from "@/modules-otn/utils/util";
import {setSelectedItem, setTableFilter} from "@/store/modules/otn/mapSlice";
import {middleModal} from "@/modules-otn/components/modal/custom_modal";
import {getOTNDeviceList, setOTNTreeLocation} from "@/modules-ampcon/apis/otn";
import styles from "./device.module.scss";
import {
    getDownGroupsByGroupId,
    getUpGroupsByGroupId,
    getNEsByGroupID,
    getGeojsonData,
    getNeListByGroupId,
    isRootNode,
    getNeedUpdateGroupKeys,
    isOtnNode,
    isSwitchNode,
    formatGroupAndNeInfo,
    isOtnNe,
    isSwitchNe,
    isNeNode,
    getNeOrGroupDetailById,
    getRootNode
} from "./device_map_helper";
import {
    UpOneLevelIcon,
    cancelEditLocationIcon,
    editLocationIcon,
    editTokenIcon,
    rootTopologyIcon,
    saveViewIcon,
    selectMapIcon,
    unLocatedIcon
} from "./device_icons";

export default function DeviceMap() {
    const dispatch = useDispatch();
    const {labelList, language} = useSelector(state => state.languageOTN);
    const {
        neInfoListMap,
        selectedItem,
        connections,
        selectedFiberConnectionInfo,
        tableFilter,
        treeItemChangedTag,
        rootKey
    } = useSelector(state => state.map);
    const {alarms} = useSelector(state => state.notification);
    const [neAndGroupData, setNeAndGroupData] = useState({});
    const neAndGroupDataRef = useRef(neAndGroupData);
    neAndGroupDataRef.current = neAndGroupData;
    const rootKeyRef = useRef(rootKey);
    rootKeyRef.current = rootKey;
    const mapContainerRef = useRef(null);
    const [scene, setScene] = useState(null);
    const sceneRef = useRef(scene);
    sceneRef.current = scene;
    const [neLayer, setNeLayer] = useState(null);
    const neLayerRef = useRef(neLayer);
    neLayerRef.current = neLayer;
    const [groupLayer, setGroupLayer] = useState(null);
    const [lineLayer, setLineLayer] = useState(null);
    const [editable, setEditable] = useState(false);
    const [showNodes, setShowNodes] = useState([]);
    const showNodesRef = useRef(showNodes);
    showNodesRef.current = showNodes;
    const mapType = (JSON.parse(localStorage.getItem("mapConfig")) ?? {}).mapType ?? "offline";
    const [mapTokens, setMapTokens] = useState({});
    const neLabelLayerRef = useRef(null);
    const selectUnlocationNeRef = useRef(null);
    const unLocationNEList = useMemo(() => {
        return neAndGroupData?.neInfo?.filter(item => !item.value.lng || !item.value.lat) ?? [];
    }, [neAndGroupData]);
    const unLocationNEListRef = useRef(unLocationNEList);
    unLocationNEListRef.current = unLocationNEList;
    const neInfoListMapRef = useRef(neInfoListMap);
    neInfoListMapRef.current = neInfoListMap;

    const updateNeAndGroupData = callback => {
        if (!selectedItem?.id) return;
        if (selectedItem.id === "otnRoot" || isOtnNode(selectedItem?.value?.nodeType)) {
            if (!["6", "7"].includes(selectedItem?.value?.type)) {
                apiConnectionByGroup().then(res => {
                    const {apiResult, apiMessage} = res;
                    if (apiResult === "fail") {
                        message.error(apiMessage).then();
                        return;
                    }
                    const {groupInfo, neInfo} = res;
                    const {formatGroups: formatOtnGroups, formatNes: formatOtnNes} = formatGroupAndNeInfo(
                        {groupInfo, neInfo},
                        "otn"
                    );
                    setNeAndGroupData({...res, groupInfo: formatOtnGroups, neInfo: formatOtnNes});
                    callback?.();
                });
            } else {
                const ampconOTNAPi = getOTNDeviceList().then(res => {
                    const {apiResult, apiMessage} = res;
                    if (apiResult === "fail") {
                        message.error(apiMessage).then();
                        return [];
                    }
                    const {groupInfo, neInfo} = res;
                    const {formatGroups: formatAmpconOTNGroups, formatNes: formatAmpconOTNNes} = formatGroupAndNeInfo(
                        {groupInfo, neInfo},
                        "otn"
                    );
                    return {groupInfo: formatAmpconOTNGroups, neInfo: formatAmpconOTNNes};
                });
                const OTNApi = apiConnectionByGroup().then(res => {
                    const {apiResult, apiMessage} = res;
                    if (apiResult === "fail") {
                        message.error(apiMessage).then();
                        return;
                    }
                    const {groupInfo, neInfo} = res;
                    const {formatGroups: formatOtnGroups, formatNes: formatOtnNes} = formatGroupAndNeInfo(
                        {groupInfo, neInfo},
                        "otn"
                    );
                    return {groupInfo: formatOtnGroups, neInfo: formatOtnNes};
                });

                Promise.all([ampconOTNAPi, OTNApi]).then(([ampconOTNAPiResult, OTNApiReult]) => {
                    const groupInfo = [...ampconOTNAPiResult.groupInfo, ...OTNApiReult.groupInfo];
                    const neInfo = [...ampconOTNAPiResult.neInfo, ...OTNApiReult.neInfo];
                    setNeAndGroupData({groupInfo, neInfo});
                });
            }
        }

        if (selectedItem.id === "switchRoot" || isSwitchNode(selectedItem?.value?.nodeType)) {
            getSwitchTreeData().then(res => {
                const {apiResult, apiMessage} = res;
                if (apiResult === "fail") {
                    message.error(apiMessage).then();
                    return;
                }
                const {groupInfo, neInfo} = res;
                const {formatGroups: formatSwitchGroups, formatNes: formatSwitchs} = formatGroupAndNeInfo(
                    {groupInfo, neInfo},
                    "switch"
                );
                setNeAndGroupData({...res, groupInfo: formatSwitchGroups, neInfo: formatSwitchs});
                callback?.();
            });
        }
    };

    const neToGroupMap = useMemo(() => {
        if (!neInfoListMap) return {};

        const {groupInfo = []} = neAndGroupData ?? {};
        const hasGroupNeList = Object.values(neInfoListMap).filter(item => item.group);
        const resultObj = {};
        hasGroupNeList.forEach(item => {
            const {group, host, port} = item;
            const ne_id = `${host}:${port}`;
            let neBelongGroupName = group;
            // eslint-disable-next-line no-loop-func
            while (neBelongGroupName && !showNodes.some(item => item?.properties?.id === neBelongGroupName)) {
                neBelongGroupName =
                    // eslint-disable-next-line no-loop-func
                    groupInfo.find(item => item.id === neBelongGroupName)?.value?.parentKey ?? null;
            }
            const groupTitle = groupInfo.find(item => item.id === neBelongGroupName)?.value?.title;
            if (groupTitle) {
                resultObj[ne_id] = groupTitle;
            }
        });
        return resultObj;
    }, [showNodes, neInfoListMap]);

    const readyOnlyRight = useUserRight();

    const getMapScene = (mapType, config) => {
        const {zoom = 0.86, center = [52, 42]} = JSON.parse(localStorage.getItem("mapConfig")) ?? {};
        if (mapType === "mapbox") {
            const {token} = config;
            return new Scene({
                id: mapContainerRef.current,
                logoVisible: false,
                map: new Mapbox({
                    center,
                    zoom,
                    style: "light",
                    token,
                    rotateEnable: false,
                    pitchEnable: false
                })
            });
        }

        if (mapType === "offline") {
            return new Scene({
                id: mapContainerRef.current,
                logoVisible: false,
                map: new Mapbox({
                    style: "blank",
                    center,
                    zoom,
                    rotateEnable: false,
                    pitchEnable: false
                })
            });
        }
    };

    const getRootShowNodes = () => {
        const {neInfo = [], groupInfo = []} = neAndGroupData;
        const newShowNodes = [];
        neInfo.forEach(item => {
            const {group, lng, lat, ne_id, runState} = item.value;
            if (!isRootNode(group) || !lng || !lat) return;
            const isCritical = isCriticalNe(ne_id);
            const isOnline = Boolean(runState);
            newShowNodes.push(
                getGeojsonData({
                    id: item.id,
                    value: {
                        ...item.value,
                        isCritical,
                        isOnline
                    }
                })
            );
        });
        groupInfo.forEach(item => {
            const {parentKey, lng, lat} = item.value;
            if (!isRootNode(parentKey) || !lng || !lat) return;
            newShowNodes.push(getGeojsonData(item));
        });
        return newShowNodes;
    };

    const isCriticalNe = ne_id => {
        const criticalAlarmSize =
            alarms?.filter?.(alarm => alarm?.ne_id === ne_id && alarm?.severity === "CRITICAL")?.length ?? 0;
        return !!criticalAlarmSize;
    };

    const setActiveFeature = (layers, callback = () => {}) => {
        layers.forEach(layer => layer?.setActiveFeature(null));
        callback();
    };

    useEffect(() => {
        const asyncMain = async () => {
            let {mapType = "offline"} = JSON.parse(localStorage.getItem("mapConfig")) ?? {};
            let mapConfig = mapTokens;
            if (isEmptyObject(mapTokens)) {
                mapConfig = await apiGetMapToken().then(rs => {
                    const {apiResult, gaodeConfig, mapboxConfig} = rs;
                    if (apiResult === "fail") return;
                    setMapTokens({
                        gaodeConfig,
                        mapboxConfig
                    });
                    return {
                        gaodeConfig,
                        mapboxConfig
                    };
                });
            }

            const {mapboxConfig} = mapConfig;
            if (
                isEmptyObject(mapConfig) ||
                !mapType ||
                mapType === "offline" ||
                (mapType === "mapbox" && !mapboxConfig?.token)
            )
                mapType = "offline";

            const mapScene = getMapScene(mapType, mapboxConfig);

            mapScene.on("loaded", async () => {
                if (mapType === "offline") {
                    const {countrysFrontierGeojson} = await getOfflineMapData();
                    const offlineMapStyle = {
                        fillColor: "#f7f7f7",
                        frontierLineColor: "#dfdfdf",
                        frontierLineSize: 0.5,
                        placeLabelColor: "#666666",
                        placeLabelSize: 14
                    };
                    const worldFillLayer = new PolygonLayer({zIndex: 1})
                        .source(countrysFrontierGeojson)
                        .color(offlineMapStyle.fillColor)
                        .shape("fill")
                        .style({
                            opacity: 0.9
                        });
                    const countrysFrontierlayer = new PolygonLayer({zIndex: 2})
                        .source(countrysFrontierGeojson)
                        .size(offlineMapStyle.frontierLineSize)
                        .color(offlineMapStyle.frontierLineColor)
                        .shape("line");
                    const nowSceneZoom = mapScene.getZoom();
                    if (nowSceneZoom > 2) {
                        const countryLabelLayer = new PointLayer({zIndex: 3, name: "countryLabelLayer"})
                            .source(
                                countrysFrontierGeojson.features.map(item => item.properties),
                                {
                                    parser: {type: "json", x: "longitude", y: "latitude"}
                                }
                            )
                            .shape(language === "cn" ? "cname" : "name", "text")
                            .color(offlineMapStyle.placeLabelColor)
                            .size(offlineMapStyle.placeLabelSize)
                            .style({
                                fontWeight: 500,
                                spacing: 2,
                                scale: [1, 1],
                                textAllowOverlap: true
                            });
                        mapScene.addLayer(countryLabelLayer);
                    }

                    mapScene.addLayer(worldFillLayer);
                    mapScene.addLayer(countrysFrontierlayer);

                    mapScene.on("zoomend", e => {
                        const zoom = e.target.getZoom();

                        if (zoom < 2) {
                            const countryLabelLayer = sceneRef.current?.getLayerByName("countryLabelLayer");
                            if (countryLabelLayer) mapScene?.removeLayer(countryLabelLayer);
                        } else {
                            let countryLableLayer = sceneRef.current?.getLayerByName("countryLabelLayer");
                            if (!countryLableLayer) {
                                countryLableLayer = new PointLayer({zIndex: 3, name: "countryLabelLayer"})
                                    .source(
                                        countrysFrontierGeojson.features.map(item => item.properties),
                                        {
                                            parser: {type: "json", x: "longitude", y: "latitude"}
                                        }
                                    )
                                    .shape(language === "cn" ? "cname" : "name", "text")
                                    .color(offlineMapStyle.placeLabelColor)
                                    .size(offlineMapStyle.placeLabelSize)
                                    .style({
                                        fontWeight: 500,
                                        spacing: 2,
                                        scale: [1, 1],
                                        textAllowOverlap: true
                                    });
                                mapScene.addLayer(countryLableLayer);
                            }
                        }
                    });
                }

                mapScene.on("click", onSetLocation);
                const scale = new Scale({
                    position: "rightbottom"
                });
                mapScene?.addControl?.(scale);

                mapScene.addImage("group", groupSvg);
                mapScene.addImage("switch", switchSvg);
                mapScene.addImage("switch_warn", switchWarnSvg);
                mapScene.addImage("switch_offline", switchOfflineSvg);
                mapScene.addImage("otn", otnSvg);
                mapScene.addImage("otn_warn", otnWarnSvg);
                mapScene.addImage("otn_offline", otnOfflineSvg);

                setScene(mapScene);

                updateNeAndGroupData();
            });
        };
        asyncMain();
    }, []);

    useEffect(() => {
        if (scene?.loaded) {
            const {neInfo = [], groupInfo = []} = neAndGroupData;
            if (isEmptyArray(showNodes) || isRootNode(selectedItem?.id)) {
                const newShowNodes = getRootShowNodes();
                setShowNodes(newShowNodes);
            } else {
                const newShowNodes = [];
                const groups = showNodes.reduce((res, showNode) => {
                    const {id, nodeType} = showNode.properties ?? {};
                    const groupID = isNeNode(nodeType)
                        ? neInfo.find(item => item.id === id).value.group
                        : groupInfo.find(item => item.id === id).value.parentKey;
                    if (!res.includes(groupID)) res.push(groupID);
                    return res;
                }, []);

                groups.forEach(groupID => {
                    groupInfo.forEach(group => {
                        const {parentKey, lng, lat} = group.value ?? {};
                        if (parentKey === groupID && lng && lat) newShowNodes.push(getGeojsonData(group));
                    });
                    neInfo.forEach(ne => {
                        const {group, lng, lat, ne_id, runState} = ne.value;
                        if (group === groupID && lng && lat) {
                            const isCritical = isCriticalNe(ne_id);
                            const isOnline = Boolean(runState);
                            newShowNodes.push(
                                getGeojsonData({
                                    id: ne.id,
                                    value: {
                                        ...ne.value,
                                        isCritical,
                                        isOnline
                                    }
                                })
                            );
                        }
                    });
                });
                const newShowNodesClone = [];
                newShowNodes.forEach(newShowNode => {
                    const {id, nodeType} = newShowNode.properties ?? {};

                    if (isNeNode(nodeType)) {
                        newShowNodesClone.push(newShowNode);
                        return;
                    }
                    const childs = [].concat(
                        getDownGroupsByGroupId(id, groupInfo).filter(item => item.id !== id),
                        getNEsByGroupID(id, groupInfo, neInfo)
                    );
                    if (!newShowNodes.some(item => childs?.find(child => child.id === item?.properties?.id)))
                        newShowNodesClone.push(newShowNode);
                });
                setShowNodes(newShowNodesClone);
            }
        }
    }, [scene, neAndGroupData]);

    useEffect(() => {
        if (scene && isRootNode(selectedItem?.id)) {
            updateNeAndGroupData();
            return;
        }

        if (
            selectedItem?.id &&
            !getNeOrGroupDetailById(selectedItem.id, neAndGroupData?.groupInfo, neAndGroupData?.neInfo)
        ) {
            setShowNodes([]);
            setTimeout(() => {
                updateNeAndGroupData();
            }, 100);
            return;
        }

        if (scene && selectedItem && (neLayer || groupLayer)) {
            if (lineLayer) {
                const oldLineData = structuredClone(lineLayer?.sourceOption?.data);
                oldLineData.forEach(lineItem => {
                    lineItem.properties.isActive = false;
                });
                lineLayer.setData(oldLineData);
            }

            const {neInfo = [], groupInfo = []} = neAndGroupData;
            const {isExpand: isExpandProps = false} = selectedItem;
            if (!selectedItem?.id || selectedItem?.id === rootKey) return;

            const isSelectedNE = isNeNode(selectedItem.value.nodeType);

            if (isSelectedNE) {
                dispatch(setTableFilter({...selectedItem, type: "NODE_NE", id: selectedItem.value.ne_id}));
            } else {
                const idList = getNEsByGroupID(selectedItem.id, groupInfo, neInfo)?.map(item => item?.value?.ne_id);
                dispatch(setTableFilter({type: "NODE_GROUP", idList}));
            }

            if (!selectedItem.value.lng || !selectedItem.value.lat) {
                setActiveFeature([neLayer, groupLayer], () => {});
                return;
            }

            const selectedFeature = showNodes.find(item => item.properties.id === selectedItem.id);

            if (selectedFeature && !isExpandProps) {
                setActiveFeature([neLayer, groupLayer], () => {
                    (isSelectedNE ? neLayer : groupLayer).setActiveFeature(selectedFeature);
                });
            } else if (isSelectedNE && selectedItem.value.group === rootKey) {
                const {ne_id, runState} = selectedItem.value;
                const isCritical = isCriticalNe(ne_id);
                const isOnline = Boolean(runState);
                setShowNodes(
                    showNodes.concat(
                        getGeojsonData({
                            id: selectedItem.id,
                            value: {
                                ...selectedItem.value,
                                isCritical,
                                isOnline
                            }
                        })
                    )
                );
            } else {
                const targetGroupDetail = isSelectedNE
                    ? groupInfo.find(group => String(group.id) === String(selectedItem.value.group))
                    : selectedItem;
                const targetGroupChildGroups = isSelectedNE
                    ? []
                    : getDownGroupsByGroupId(targetGroupDetail.id, groupInfo);
                const targetGroupChildNEs = isSelectedNE
                    ? []
                    : getNEsByGroupID(targetGroupDetail.id, groupInfo, neInfo);

                const targetGroupFatherGroups = getUpGroupsByGroupId(targetGroupDetail.id, groupInfo);
                /*
                    1. 双击组导致的展开操作
                    2. 选中网元但地图上没有该网元（网元只有折叠的操作和已存在选中，其他情况都是展开）
                    3. 选中组且地图上没有选中组的子组和子网元
                */
                const isExpand =
                    isExpandProps ||
                    isSelectedNE ||
                    !showNodes.some(showNode => {
                        const childIDs = [].concat(
                            targetGroupChildGroups.map(item => item.id),
                            targetGroupChildNEs.map(item => item.id)
                        );
                        return childIDs.includes(showNode.properties.id);
                    });

                let newShowNodes = [];

                if (isExpand) {
                    let aboutGroupList = [];
                    const fatherGroups = isSelectedNE ? targetGroupFatherGroups : targetGroupFatherGroups.slice(1);
                    let parentKey = isSelectedNE ? targetGroupDetail.id : targetGroupDetail.value.parentKey;
                    while (parentKey && parentKey !== rootKey) {
                        const virtualNodes = [];
                        // eslint-disable-next-line no-loop-func
                        groupInfo.forEach(group => {
                            const {lng, lat, parentKey: pKey} = group.value;
                            if (
                                !lng ||
                                !lat ||
                                parentKey !== pKey ||
                                fatherGroups.some(fatherGroup => fatherGroup.id === group.id)
                            )
                                return;
                            virtualNodes.push(getGeojsonData(group));
                        });
                        // eslint-disable-next-line no-loop-func
                        neInfo.forEach(ne => {
                            const {lng, lat, group, runState} = ne.value;
                            if (!lng || !lat || parentKey !== group) return;
                            const isCritical = isCriticalNe(ne.value.ne_id);
                            const isOnline = Boolean(runState);
                            virtualNodes.push(
                                getGeojsonData({
                                    id: ne.id,
                                    value: {
                                        ...ne.value,
                                        isCritical,
                                        isOnline
                                    }
                                })
                            );
                        });
                        if (
                            virtualNodes.some(virtualNode => {
                                const {parentKey, id} = virtualNode?.properties ?? {};
                                if (parentKey) {
                                    const virtualNodeChildGroups = getDownGroupsByGroupId(id, groupInfo);
                                    const virtualNodeChildNes = getNEsByGroupID(id, groupInfo, neInfo);
                                    if (
                                        showNodes.some(showNode =>
                                            []
                                                .concat(
                                                    virtualNodeChildGroups.map(
                                                        virtualNodeChildGroup => virtualNodeChildGroup.id
                                                    ),
                                                    virtualNodeChildNes.map(
                                                        virtualNodeChildNes => virtualNodeChildNes.id
                                                    )
                                                )
                                                .includes(showNode.properties.id)
                                        )
                                    ) {
                                        return true;
                                    }
                                }
                                if (showNodes.some(showNode => showNode.properties.id === id)) return true;
                            })
                        ) {
                            parentKey = rootKey;
                            continue;
                        }
                        aboutGroupList = aboutGroupList.concat(virtualNodes);
                        // eslint-disable-next-line no-loop-func
                        parentKey = groupInfo.find(group => group.id === parentKey)?.value?.parentKey;
                    }

                    const needExpandGroupDetail = showNodes.find(showNode => {
                        return fatherGroups.findLast(fatherGroup => fatherGroup.id === showNode.properties.id);
                    });
                    newShowNodes = showNodes
                        .filter(showNode => showNode.properties.id !== needExpandGroupDetail?.properties?.id)
                        .concat(aboutGroupList);
                } else {
                    newShowNodes = showNodes.filter(showNode => {
                        const ids = [].concat(
                            targetGroupChildGroups.map(item => item.id),
                            targetGroupChildNEs.map(item => item.id)
                        );

                        return !ids.includes(showNode.properties.id);
                    });

                    newShowNodes.push(getGeojsonData(selectedItem));
                }

                setShowNodes(newShowNodes);
            }

            const {lng, lat} = selectedItem.value;
            if (!isInView(lng, lat)) scene.panTo([lng, lat]);
        }
    }, [selectedItem]);

    useEffect(() => {
        if (!lineLayer) return;
        const nowDrawLineData = structuredClone(lineLayer?.sourceOption?.data) ?? [];
        const targetLine = nowDrawLineData?.find(
            item =>
                item?.properties?.sourceIP === selectedFiberConnectionInfo?.sourceIP &&
                item?.properties?.destIP === selectedFiberConnectionInfo?.destIP
        );

        if (targetLine) {
            targetLine.properties.isActive = true;
            lineLayer.setData(nowDrawLineData);
        } else {
            nowDrawLineData.forEach(nowDrawLineItem => {
                nowDrawLineItem.properties.isActive = false;
            });
            lineLayer.setData(nowDrawLineData);
        }
    }, [selectedFiberConnectionInfo]);
    useEffect(() => {
        const newShowNodes = showNodes.map(showNode => {
            const {ne_id, runState, nodeType} = showNode.properties;
            if (isNeNode(nodeType)) {
                showNode.properties.isCritical = isCriticalNe(ne_id);
                showNode.properties.isOnline = Boolean(runState);
            }
            return showNode;
        });
        setShowNodes(newShowNodes);
    }, [alarms]);

    useEffect(() => {
        if (scene && scene.loaded) {
            if (mapType === "offline") {
                const clearLayerList = scene
                    ?.getLayers()
                    ?.filter(item =>
                        ["neLabelLayer", "neLayer", "groupLabelLayer", "groupLayer", "lineLayer"].includes(item.name)
                    );
                clearLayerList?.forEach(item => scene?.removeLayer(item));
            } else {
                scene.removeAllLayer();
            }
            if (isEmptyArray(showNodes)) return;

            const neShowNodes = [];
            const groupShowNodes = [];
            showNodes.forEach(geojsonItem => {
                if (geojsonItem?.properties?.type === "group") {
                    groupShowNodes.push(geojsonItem);
                    return;
                }
                neShowNodes.push(geojsonItem);
            });

            const neLabelLayer = new PointLayer({zIndex: 5, name: "neLabelLayer"})
                .source({
                    type: "FeatureCollection",
                    features: neShowNodes
                })
                .shape("label", "text")
                .size(12)
                .color("#3a5a40")
                .style({
                    textAnchor: "center",
                    textOffset: [-2, -34],
                    spacing: 2,
                    padding: [1, 1],
                    stroke: "#ffffff",
                    strokeWidth: 0.3,
                    strokeOpacity: 1.0,
                    fontWeight: 500,
                    textAllowOverlap: true
                });

            neLabelLayerRef.current = neShowNodes;
            scene.addLayer(neLabelLayer);

            const neLayer = new DrawPoint(scene, {
                initialData: neShowNodes,
                helper: {pointHover: ""},
                style: {
                    point: {
                        callback([layer]) {
                            layer.zIndex = 5;
                            layer.name = "neLayer";
                            layer.color(undefined);
                            layer
                                .shape(["nodeType", "isCritical", "isOnline"], (nodeType, isCritical, isOnline) => {
                                    const key = nodeType === "otn_ne" ? "otn" : "switch";
                                    let shape = key;
                                    if (isCritical) shape = `${key}_warn`;
                                    if (!isOnline) shape = `${key}_offline`;
                                    return shape;
                                })
                                .size(["isActive", "isHover"], (isActive, isHover) => (isActive || isHover ? 28 : 16));
                        }
                    }
                }
            });
            neLayer.options.editable = editable;
            const neLayerRaw = neLayer.getRenderLayers().point.at(0);
            neLayerRaw.on("click", e => {
                const {id} = e.feature.properties;
                const neDetail = neAndGroupData?.neInfo?.find(item => item?.id === id);
                dispatch(setSelectedItem(neDetail));
            });
            neLayer.on(DrawEvent.Dragging, endPoint => {
                neLabelLayerRef.current = neLabelLayerRef?.current?.map(item => {
                    if (item?.properties?.id === endPoint?.properties?.id) {
                        return {
                            ...item,
                            geometry: {
                                ...item.geometry,
                                coordinates: endPoint.geometry.coordinates
                            }
                        };
                    }
                    return item;
                });
                neLabelLayer.setData({
                    type: "FeatureCollection",
                    features: neLabelLayerRef.current
                });
            });

            neLayer.on(
                DrawEvent.Dragging,
                debounce(endPoint => {
                    const {id, nodeType, originData} = endPoint.properties;
                    const [lng, lat] = endPoint?.geometry?.coordinates ?? [];

                    const groupList = getNeedUpdateGroupKeys({
                        id,
                        neAndGroupData: neAndGroupDataRef.current
                    });

                    if (isOtnNe(nodeType)) {
                        let setNeLocationApi;
                        switch (originData.value.type) {
                            case ("6", "7"):
                                setNeLocationApi = setOTNTreeLocation({
                                    neList: [id],
                                    lng,
                                    lat
                                });
                                break;
                            default:
                                // 对于不需要特殊处理的设备类型，默认返回一个已成功的 Promise
                                setNeLocationApi = Promise.resolve({apiResult: true});
                        }

                        // const isDp = originData.value.type === "6";
                        // const setNeLocationApi = isDp
                        //     ? setDCP920TreeLocation({
                        //           neList: [id],
                        //           lng,
                        //           lat
                        //       })
                        //     : Promise.resolve({apiResult: true});
                        const setGroupLocationApi = setLocationInfo({
                            neList: ["6", "7"].includes(originData.value.type) ? [] : [id],
                            groupList,
                            lng,
                            lat
                        });

                        Promise.all([setNeLocationApi, setGroupLocationApi])
                            .then(rs => {
                                const [neRes, groupRes] = rs;
                                if (neRes.apiResult === "fail") {
                                    message.error(gLabelList.save_failed.format(neRes.apiMessage));
                                    return;
                                }
                                if (groupRes.apiResult === "fail") {
                                    message.error(gLabelList.save_failed.format(groupRes.apiMessage));
                                    return;
                                }
                                message.success(gLabelList.save_success);
                                updateNeAndGroupData();
                            })
                            .finally(() => {
                                selectUnlocationNeRef.current = null;
                            });
                    } else if (isSwitchNe(nodeType)) {
                        setSwitchTreeLocation({
                            neList: [id],
                            groupList,
                            lng,
                            lat
                        })
                            .then(async res => {
                                const {apiResult, apiMessage} = res;
                                if (apiResult === "fail") {
                                    message.error(gLabelList.save_failed.format(apiMessage));
                                    return;
                                }
                                message.success(gLabelList.save_success);
                                updateNeAndGroupData();
                            })
                            .finally(() => {
                                selectUnlocationNeRef.current = null;
                            });
                    }
                }, 300)
            );
            setNeLayer(neLayer);

            const groupLabelLayer = new PointLayer({zIndex: 5, name: "groupLabelLayer"})
                .source({
                    type: "FeatureCollection",
                    features: groupShowNodes
                })
                .shape("label", "text")
                .size(12)
                .color("#3a5a40")
                .style({
                    textAnchor: "center",
                    textOffset: [-2, -48],
                    spacing: 2,
                    padding: [1, 1],
                    stroke: "#ffffff",
                    strokeWidth: 0.3,
                    strokeOpacity: 1.0,
                    fontWeight: 500,
                    textAllowOverlap: true
                });
            scene?.addLayer?.(groupLabelLayer);

            const groupLayer = new DrawPoint(scene, {
                initialData: groupShowNodes,
                helper: {pointHover: ""},
                style: {
                    point: {
                        callback([layer]) {
                            layer.zIndex = 5;
                            layer.name = "groupLayer";
                            layer.color(undefined);
                            layer
                                .shape(["nodeType", "isCritical", "isOnline"], () => {
                                    return "group";
                                })
                                .size(["isActive", "isHover"], (isActive, isHover) => (isActive || isHover ? 20 : 12));
                        }
                    }
                }
            });
            groupLayer.options.editable = false;
            const groupLayerRaw = groupLayer.getRenderLayers().point.at(0);
            groupLayerRaw
                .on("click", e => {
                    const {id} = e.feature.properties;
                    const selectGroupDetail = neAndGroupData.groupInfo.find(item => item.id === id);
                    dispatch(setSelectedItem(selectGroupDetail));
                })
                .on("dblclick", e => {
                    const {id} = e.feature.properties;
                    const {neInfo = [], groupInfo = []} = neAndGroupData;
                    setShowNodes(showNodes => {
                        const expandChild = [];
                        groupInfo.forEach(group => {
                            const {parentKey, lng, lat} = group.value;
                            if (parentKey !== id || !lng || !lat) return;
                            expandChild.push(getGeojsonData(group));
                        });
                        neInfo.forEach(ne => {
                            const {group, lng, lat, ne_id, runState} = ne.value;
                            if (group !== id || !lng || !lat) return;
                            const isCritical = isCriticalNe(ne_id);
                            const isOnline = Boolean(runState);
                            expandChild.push(
                                getGeojsonData({
                                    id: ne.id,
                                    value: {
                                        ...ne.value,
                                        isCritical,
                                        isOnline
                                    }
                                })
                            );
                        });
                        const newShowNodes = showNodes
                            .map(showNode => (showNode.properties.id === id ? expandChild : showNode))
                            .flat();
                        dispatch(setTableFilter({type: "ROOT"}));
                        return newShowNodes;
                    });
                });
            setGroupLayer(groupLayer);

            if (selectedItem && selectedItem?.id?.includes("config:ne")) {
                const iconDrawerData = neLayer.getData();
                const activeFeature = iconDrawerData.find(item => item?.properties?.id === selectedItem?.id);
                if (activeFeature) neLayer.setActiveFeature(activeFeature);
            }

            if (selectedItem && selectedItem?.id?.includes("nms:group")) {
                const groupLayerData = groupLayer.getData();
                const activeFeature = groupLayerData.find(item => item?.properties?.id === selectedItem?.id);
                if (activeFeature) groupLayer.setActiveFeature(activeFeature);
            }

            if (editable) return;

            const linkInfo = connections.reduce((res, {destIP, sourceIP}) => {
                if (destIP === sourceIP) return res;
                const sourceType = neToGroupMap[sourceIP] ? "group" : "ne";
                const destType = neToGroupMap[destIP] ? "group" : "ne";
                const sourceIPtoGroup = neToGroupMap[sourceIP] ?? sourceIP;
                const destIPtoGroup = neToGroupMap[destIP] ?? destIP;
                const sourceLngLat = showNodes.find(
                    item => item?.properties?.label === sourceIPtoGroup || item?.properties?.ne_id === sourceIPtoGroup
                )?.geometry?.coordinates;
                const destLngLat = showNodes.find(
                    item => item?.properties?.label === destIPtoGroup || item?.properties?.ne_id === destIPtoGroup
                )?.geometry?.coordinates;

                if (destIPtoGroup === sourceIPtoGroup) return res;
                if (
                    res.some(item => {
                        const switchArr = [item.source, item.target];
                        return switchArr.includes(destIPtoGroup) && switchArr.includes(sourceIPtoGroup);
                    })
                )
                    return res;
                return [
                    ...res,
                    {
                        source: sourceIPtoGroup,
                        target: destIPtoGroup,
                        sourceType,
                        destType,
                        sourceLngLat,
                        destLngLat,
                        sourceIP,
                        destIP
                    }
                ];
            }, []);
            let lineDrawer = null;

            if (linkInfo.some(item => item.sourceLngLat && item.destLngLat)) {
                const lineData = linkInfo
                    .filter(item => item.sourceLngLat && item.destLngLat)
                    .map(item => {
                        const {sourceLngLat, destLngLat, sourceType, source, target, destType, sourceIP, destIP} = item;
                        return {
                            lng: sourceLngLat[0],
                            lat: sourceLngLat[1],
                            lng1: destLngLat[0],
                            lat1: destLngLat[1],
                            properties: {source, sourceType, target, destType, isActive: false, sourceIP, destIP}
                        };
                    });

                lineDrawer = new LineLayer({
                    zIndex: 4,
                    name: "lineLayer"
                })
                    .source(lineData, {
                        parser: {
                            type: "json",
                            x: "lng",
                            y: "lat",
                            x1: "lng1",
                            y1: "lat1"
                        }
                    })
                    .shape("line")
                    .size(2)
                    .color("properties", properties => {
                        const {isActive} = properties;
                        return isActive ? "#ed9d48" : "#1990ff";
                    });
                scene.addLayer(lineDrawer);
                lineDrawer.on("click", e => {
                    setActiveFeature([neLayer, groupLayer], () => {});

                    const {sourceType, destType, source, target} = e?.feature?.properties ?? {};
                    const oldLineData = structuredClone(lineDrawer?.sourceOption?.data);
                    const clickLine = oldLineData?.find(oldLineDataItem => {
                        const {source: oldLineDataItemSource, target: oldLineDataItemTarget} =
                            oldLineDataItem?.properties ?? {};
                        const {source: clickLineSource, target: clickLineTarget} = e?.feature?.properties ?? {};
                        if (
                            oldLineDataItemSource === undefined &&
                            oldLineDataItemTarget === undefined &&
                            clickLineSource === undefined &&
                            clickLineTarget === undefined
                        )
                            return false;
                        if (oldLineDataItemSource === clickLineSource && oldLineDataItemTarget === clickLineTarget)
                            return true;
                        return false;
                    });
                    if (clickLine === undefined) return;
                    clickLine.properties.isActive = true;
                    lineDrawer.setData(oldLineData);
                    const sourceList = sourceType === "group" ? getNeListByGroupId(neToGroupMap, source) : [source];
                    const targetList = destType === "group" ? getNeListByGroupId(neToGroupMap, target) : [target];

                    dispatch(setTableFilter({type: "CELL_FILTER", sourceList, targetList}));
                });
                setLineLayer(lineDrawer);
            }
        }
    }, [scene, showNodes, connections]);

    useEffect(() => {
        if (["ots", "oms", "och", "client", "si"].includes(tableFilter?.type)) {
            if (!lineLayer) return;

            const {connection = []} = tableFilter;
            const lineLayerData = structuredClone(lineLayer?.sourceOption?.data ?? []);
            connection?.forEach(connectionItem => {
                const [source, target] = connectionItem;

                const activeFeature = lineLayerData.find(lineLayerDataItem => {
                    const {sourceIP, destIP} = lineLayerDataItem.properties;
                    const gateArr = [sourceIP, destIP];
                    return gateArr.includes(source) && gateArr.includes(target);
                });

                if (activeFeature) activeFeature.properties.isActive = true;
            });
            if (lineLayer) lineLayer.setData(lineLayerData);
        }
    }, [tableFilter]);

    useEffect(() => {
        updateNeAndGroupData();
    }, [treeItemChangedTag]);

    const isInView = (lng, lat) => {
        if (!scene) return false;
        const [startPointBound, endPointBound] = scene.getBounds();
        return inRange(lng, startPointBound[0], endPointBound[0]) && inRange(lat, startPointBound[1], endPointBound[1]);
    };

    const onClickEdit = () => {
        cancelNeLocationStyle();
        if (!showNodes?.length) return;
        lineLayer?.hide?.();
        neLayer.options.editable = true;
        setEditable(true);
    };

    const onClickCancel = () => {
        neLayer.options.editable = false;
        lineLayer?.show?.();
        setEditable(false);
    };

    const onClickUpOneLevel = () => {
        cancelNeLocationStyle();
        if (!selectedItem?.id || disabledUpOneLevelBtn) return;
        const {neInfo = [], groupInfo = []} = neAndGroupData;
        const selectItemType = isNeNode(selectedItem.value.nodeType) ? "ne" : "group";
        const foldGroupID = selectItemType === "ne" ? selectedItem.value.group : selectedItem.value.parentKey;
        if (isRootNode(foldGroupID)) {
            const rootNode = getRootNode(foldGroupID);
            dispatch(setSelectedItem(rootNode));
        } else {
            const foldGroupDetail = groupInfo.find(item => item.id === foldGroupID);
            const foldGroupNeIDs = getNEsByGroupID(foldGroupID, groupInfo, neInfo) ?? [];
            dispatch(setSelectedItem(foldGroupDetail));
            dispatch(setTableFilter({type: "NODE_GROUP", idList: foldGroupNeIDs?.map(item => item.value.ne_id)}));
        }
    };

    const onSaveViewPoint = () => {
        cancelNeLocationStyle();
        if (readyOnlyRight.disabled) return;
        const oldMapConfig = JSON.parse(localStorage.getItem("mapConfig")) ?? {};
        const newMapConfig = {
            ...oldMapConfig,
            zoom: scene.getZoom(),
            center: [scene.getCenter().lng, scene.getCenter().lat]
        };
        localStorage.setItem("mapConfig", JSON.stringify(newMapConfig));
        message.success(gLabelList.save_success);
    };

    const onConfigToken = () => {
        cancelNeLocationStyle();
        if (readyOnlyRight.disabled) return;
        const afterFun = values => {
            setMapTokens({...mapTokens, ...values});
        };
        const onCancel = () => {
            modal.destroy();
        };
        let form;
        const bindForm = f => {
            form = f;
        };

        const onOk = () => {
            form.validateFields()
                .then(() => {
                    form.submit();
                })
                .catch(() => {});
        };

        const modal = middleModal({
            title: `${gLabelList.edit} Token`,
            content: <MapboxTokenForm afterFun={afterFun} onCancel={onCancel} bindForm={bindForm} />,
            footer: (
                <Space
                    style={{display: "flex", flex: 1, justifyContent: "flex-end"}}
                    className="ant-modal-confirm-btns"
                >
                    <Button>
                        <a
                            href="https://docs.mapbox.com/help/getting-started/access-tokens/"
                            target="_blank"
                            rel="noreferrer"
                        >
                            {`${gLabelList.get} Token`}
                        </a>
                    </Button>
                    <Button onClick={onCancel}>{gLabelList.cancel}</Button>
                    <Button type="primary" onClick={onOk}>
                        {gLabelList.ok}
                    </Button>
                </Space>
            )
        });
    };

    const onSelectMap = ({key}) => {
        cancelNeLocationStyle();
        const oldMapConfig = JSON.parse(localStorage.getItem("mapConfig")) ?? {};
        const newMapConfig = {
            ...oldMapConfig,
            mapType: key
        };
        localStorage.setItem("mapConfig", JSON.stringify(newMapConfig));
        window.location.reload();
    };

    const cancelNeLocationStyle = () => {
        if (selectUnlocationNeRef.current?.value) {
            const mapContainerEL = document.querySelector(".l7-marker-container");
            mapContainerEL.classList?.remove("locationing");
        }
    };

    const onSetLocation = ev => {
        cancelNeLocationStyle();

        if (!selectUnlocationNeRef.current?.value) return;
        const {lng, lat} = ev.lngLat ?? ev.lnglat;
        const {id, value} = selectUnlocationNeRef.current;
        const groupList = getNeedUpdateGroupKeys({
            id,
            neAndGroupData: neAndGroupDataRef.current
        });
        if (isOtnNe(value.nodeType)) {
            const isAmpconOTN = ["6", "7"].includes(value.type);
            const setNeLocationApi = isAmpconOTN
                ? setOTNTreeLocation({
                      neList: [id],
                      lng,
                      lat
                  })
                : Promise.resolve({apiResult: true});
            const setGroupLocationApi = setLocationInfo({
                neList: isAmpconOTN ? [] : [id],
                groupList,
                lng,
                lat
            });
            Promise.all([setNeLocationApi, setGroupLocationApi])
                .then(rs => {
                    const [neRes, groupRes] = rs;
                    if (neRes.apiResult === "fail") {
                        message.error(gLabelList.save_failed.format(neRes.apiMessage));
                        return;
                    }
                    if (groupRes.apiResult === "fail") {
                        message.error(gLabelList.save_failed.format(groupRes.apiMessage));
                        return;
                    }
                    message.success(gLabelList.save_success);
                    updateNeAndGroupData(() => {
                        setTimeout(() => {
                            dispatch(
                                setSelectedItem({
                                    id,
                                    value: {
                                        ...value,
                                        lng,
                                        lat
                                    }
                                })
                            );
                        }, 50);
                    });
                })
                .finally(() => {
                    selectUnlocationNeRef.current = null;
                });
        } else if (isSwitchNe(value.nodeType)) {
            setSwitchTreeLocation({
                neList: [id],
                groupList,
                lng,
                lat
            })
                .then(async res => {
                    const {apiResult, apiMessage} = res;
                    if (apiResult === "fail") {
                        message.error(gLabelList.save_failed.format(apiMessage));
                        return;
                    }
                    message.success(gLabelList.save_success);
                    updateNeAndGroupData(() => {
                        setTimeout(() => {
                            dispatch(
                                setSelectedItem({
                                    id,
                                    value: {
                                        ...value,
                                        lng,
                                        lat
                                    }
                                })
                            );
                        }, 50);
                    });
                })
                .finally(() => {
                    selectUnlocationNeRef.current = null;
                });
        }
    };

    const onClickUnLocationNe = ({key}) => {
        const findNeInfo = neAndGroupData.neInfo.find(item => item.id === key);
        const mapContainerEL = document.querySelector(".l7-marker-container");
        mapContainerEL.classList?.add("locationing");

        selectUnlocationNeRef.current = findNeInfo;
    };

    const onClickUpToTopLevel = () => {
        cancelNeLocationStyle();
        const newShowNodes = getRootShowNodes();
        const {groupInfo, neInfo} = neAndGroupData;
        [...(Array.isArray(groupInfo) ? groupInfo : []), ...(Array.isArray(neInfo) ? neInfo : [])].some(item => {
            if (isSwitchNode(item.value.nodeType)) {
                dispatch(
                    setSelectedItem({action: 0, id: "switchRoot", value: {name: "Switch", nodeType: "switch_group"}})
                );
                return true;
            }
            if (isOtnNode(item.value.nodeType)) {
                dispatch(setSelectedItem({action: 0, id: "otnRoot", value: {name: "OTN", nodeType: "otn_group"}}));
                return true;
            }
        });
        setShowNodes(newShowNodes);
    };

    const disabledUpOneLevelBtn = !selectedItem?.id || isRootNode(selectedItem.id);

    return (
        <div style={{flex: 1}}>
            <div ref={mapContainerRef} style={{height: "100%"}} />
            <div className={styles.mapToolsContainer}>
                <Icon
                    component={UpOneLevelIcon}
                    title={labelList.up_one_level}
                    onClick={onClickUpOneLevel}
                    className={[
                        styles.mapToolsItem,
                        styles.mapToolsIcon,
                        disabledUpOneLevelBtn ? styles.mapToolsIconDisabled : ""
                    ]}
                />
                <Icon
                    component={rootTopologyIcon}
                    title={labelList.up_to_top_level}
                    onClick={onClickUpToTopLevel}
                    className={[styles.mapToolsItem, styles.mapToolsIcon]}
                />
                {editable ? (
                    <Icon
                        component={cancelEditLocationIcon}
                        title={labelList.cancel}
                        onClick={onClickCancel}
                        className={[styles.mapToolsItem, styles.mapToolsIcon]}
                    />
                ) : (
                    <Icon
                        component={editLocationIcon}
                        title={labelList.edit_location}
                        onClick={onClickEdit}
                        className={[
                            styles.mapToolsItem,
                            styles.mapToolsIcon,
                            !showNodes?.length ? styles.mapToolsIconDisabled : ""
                        ]}
                    />
                )}
                <Icon
                    component={saveViewIcon}
                    title={labelList.save_view}
                    onClick={onSaveViewPoint}
                    className={[
                        styles.mapToolsItem,
                        styles.mapToolsIcon,
                        readyOnlyRight.disabled ? styles.mapToolsIconDisabled : ""
                    ]}
                />
                <Dropdown
                    menu={{
                        items: [
                            {key: "mapbox", label: labelList.mapbox_map},
                            {
                                key: "offline",
                                label: labelList.offline_map
                            }
                        ].map(item => {
                            const {key, label: labelValue} = item;
                            const isSelectedItem = key === mapType;
                            let disabled = isSelectedItem;
                            let label = labelValue;
                            if (isSelectedItem) label = `${labelValue}(${labelList.using})`;

                            if (key === "mapbox" && !mapTokens?.mapboxConfig?.token) {
                                disabled = true;
                                label = `${labelValue}(${labelList.tokenless})`;
                            }

                            return {label, key, disabled};
                        }),
                        style: {maxHeight: 200, overflowY: "auto"},
                        selectable: true,
                        defaultSelectedKeys: mapType,
                        onClick: onSelectMap
                    }}
                    placement="bottomLeft"
                >
                    <Icon
                        component={selectMapIcon}
                        title={labelList.select_map}
                        className={[styles.mapToolsItem, styles.mapToolsIcon]}
                    />
                </Dropdown>
                <Badge count={unLocationNEList.length} size="small">
                    <Dropdown
                        disabled={unLocationNEList?.length === 0 || readyOnlyRight.disabled}
                        className={styles.mapToolsBtn}
                        menu={{
                            items: sortArr(
                                unLocationNEList?.map(item => ({
                                    key: item.id,
                                    label: item.value.name,
                                    value: item
                                })),
                                ["label"]
                            ),
                            style: {maxHeight: 200, overflowY: "auto"},
                            onClick: onClickUnLocationNe
                        }}
                        placement="bottomLeft"
                    >
                        <Icon
                            component={unLocatedIcon}
                            title={labelList.un_located}
                            className={[
                                styles.mapToolsItem,
                                styles.mapToolsIcon,
                                unLocationNEList?.length === 0 || readyOnlyRight.disabled
                                    ? styles.mapToolsIconDisabled
                                    : ""
                            ]}
                        />
                    </Dropdown>
                </Badge>
                <DebounceButton
                    containerType="Icon"
                    component={editTokenIcon}
                    title={`${labelList.edit} Token`}
                    disabled={readyOnlyRight.disabled}
                    onClick={onConfigToken}
                    className={[
                        styles.mapToolsItem,
                        styles.mapToolsIcon,
                        styles.notBorderRight,
                        readyOnlyRight.disabled ? styles.mapToolsIconDisabled : ""
                    ]}
                />
            </div>
        </div>
    );
}

const MapboxTokenForm = ({bindForm, afterFun, onCancel}) => {
    const [form] = Form.useForm();
    bindForm(form);
    const onFinish = values => {
        const params = {
            mapboxConfig: values
        };
        apiConfigMapToken(params).then(rs => {
            const {apiResult, apiMessage} = rs;
            if (apiResult === "fail") {
                message.error(apiMessage);
                return;
            }
            message.success(gLabelList.Operation_success);
            afterFun(params);
            onCancel();
        });
    };

    return (
        <Form
            form={form}
            onFinish={onFinish}
            labelCol={{span: 8}}
            wrapperCol={{span: 14}}
            labelAlign="left"
            layout="horizontal"
            className="label-wrap"
        >
            <Form.Item label="Token" name="token" rules={[{required: true, message: "Token is required"}]}>
                <Input style={{width: 280}} />
            </Form.Item>
        </Form>
    );
};
