import {useEffect, useState} from "react";
import {useSelector} from "react-redux";
import {getDeviceStateValue} from "@/modules-otn/utils/util";
import {getStateData, netconfGetByXML, objectGet} from "@/modules-otn/apis/api";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {openDBModalEdit} from "@/modules-otn/components/form/edit_form_db";

const RelatePort = ({serviceType}) => {
    const [dataList, setDataList] = useState([]);
    const {tableFilter} = useSelector(state => state.map);
    const {neNameMap, neTypeMap} = useSelector(state => state.neName);
    const {labelList} = useSelector(state => state.languageOTN);

    useEffect(() => {
        async function getData() {
            let list = [];
            try {
                if (tableFilter?.type === "NODE_NE") {
                    if (!tableFilter.resource) {
                        if (neTypeMap[tableFilter.id] === "5") {
                            list = (await objectGet("ne:5:component", {ne_id: tableFilter.id})).documents.filter(
                                item => item.value.data?.state?.["serial-no"]
                            );
                        }
                    } else {
                        if (tableFilter.resource.type === "chassis") {
                            list = (
                                await objectGet("ne:5:component", {
                                    ne_id: tableFilter.id,
                                    name: tableFilter.resource.value
                                })
                            ).documents.filter(item => item.value.data?.state?.["serial-no"]);
                        }
                        if (["card", "fan"].includes(tableFilter.resource.type)) {
                            // port
                            const neResourceList = (await objectGet("ne:5:component", {ne_id: tableFilter.id}))
                                .documents;
                            const card = neResourceList.filter(
                                item => item.value.data.name === tableFilter.resource.value
                            );
                            list = list.concat(card);
                            const port = neResourceList.filter(
                                item => item.value.data.state?.parent === tableFilter.resource.value
                            );
                            list = list.concat(port);
                            const portNameList = port.map(item => item.value.data.name);
                            const trans = neResourceList.filter(item =>
                                portNameList.includes(item.value.data.state?.parent)
                            );
                            list = list.concat(trans);
                        }
                        if (tableFilter.resource.type === "port") {
                            // trans
                            list = (
                                await objectGet("ne:5:component", {
                                    ne_id: tableFilter.id,
                                    parent: tableFilter.resource.value
                                })
                            ).documents.filter(item => item.value.data?.state?.["serial-no"]);
                        }
                    }
                } else if (tableFilter?.type === "NODE_GROUP" && tableFilter.idList.length > 0) {
                    for (let i = 0; i < tableFilter.idList.length; i++) {
                        list = list.concat(
                            (await objectGet("ne:5:component", {ne_id: tableFilter.idList[i]})).documents
                        );
                    }
                } else if (
                    tableFilter?.type &&
                    ["ots", "oms", "och", "client"].includes(tableFilter?.type) &&
                    tableFilter.servicePortList
                ) {
                    const filterNes = {};
                    const filterCards = [];
                    const filterPorts = [];
                    const filterTransceiver = [];
                    for (let i = 0; i < tableFilter.servicePortList.length; i++) {
                        const item = tableFilter.servicePortList[i];
                        if (!filterNes[item.ne_id]) {
                            filterNes[item.ne_id] = (
                                await objectGet("ne:5:component", {ne_id: item.ne_id})
                            ).documents.filter(item => item.value.data?.state?.["serial-no"]);
                        }
                        if (!filterCards.includes(`${item.ne_id}_${item.card}`)) {
                            filterCards.push(`${item.ne_id}_${item.card}`);
                            list = list.concat(
                                filterNes[item.ne_id].filter(i => i.value.data?.state?.name === item.card)
                            );
                        }
                        if (item.ports) {
                            for (let j = 0; j < item.ports.length; j++) {
                                const _port = item.ports[j];
                                let port = _port;
                                if (typeof _port !== "string") {
                                    [port] = Object.values(_port) ?? [];
                                }
                                if (!filterPorts.includes(`${item.ne_id}_${item.card}_${port}`)) {
                                    filterPorts.push(`${item.ne_id}_${item.card}_${port}`);
                                    const transPort = item.card.replace(/\w+-/, "TRANSCEIVER-");
                                    if (!filterTransceiver.includes(`${item.ne_id}_${item.card}_${transPort}`)) {
                                        filterTransceiver.push(`${item.ne_id}_${item.card}_${transPort}`);
                                        list = list.concat(
                                            filterNes[item.ne_id].filter(item =>
                                                item.value?.data?.name.startsWith(transPort)
                                            )
                                        );
                                    }
                                }
                            }
                        }
                    }
                } else if (serviceType) {
                    if (serviceType === "optics") {
                        list = (await objectGet("ne:5:component", {})).documents;
                    }
                } else {
                    list = (await objectGet("ne:5:component", {})).documents;
                }
                list = list.filter(
                    item =>
                        item.value.data.state &&
                        item.value.data?.state?.["serial-no"] &&
                        (item.value.data?.name === "CHASSIS-1" ||
                            (item.value.data?.name.startsWith("TRANSCEIVER") &&
                                ["L", "C"].includes(item.value.data?.name.split("-").pop().at(0))) ||
                            item.value.data?.state?.parent === "CHASSIS-1")
                );
                list = list.map(item => {
                    if (item.value.data?.state) {
                        const type =
                            item.value.data?.state?.parent === "CHASSIS-1"
                                ? "CARD"
                                : item.value.data.state?.name?.split("-")?.[0];
                        let adminState = item.value.data.state?.["admin-state"];
                        if (type === "CARD") {
                            adminState =
                                item.value.data.state["actual-vendor-type"] ===
                                item.value.data.state["vendor-type-preconf"]
                                    ? "ENABLED"
                                    : "DISABLED";
                        }
                        const obj = {
                            ...item.value.data.state,
                            componentType: type,
                            ne_id: item.value.ne_id,
                            ne_name: neNameMap[item.value.ne_id],
                            "admin-state": adminState
                        };
                        if (adminState === "DISABLED") {
                            // obj["vendor-type-preconf"] = "";
                            obj["oper-status"] = "";
                            obj["serial-no"] = "";
                            obj.description = "";
                        }
                        return obj;
                    }
                });
                list.sort((a, b) => a.och.localeCompare(b.name, "ZH-CN", {numeric: true}));
            } catch (e) {
                // console.log(e);
            }
            return list;
        }

        getData().then(rs => {
            setDataList(rs);
        });
    }, [tableFilter, neNameMap]);

    const openMoreDialog = value => {
        const cardType = value.name.split("-")[0];
        let showType = cardType;
        if (["OA", "OLA", "WSS"].includes(cardType)) {
            showType = "AMPLIFIER";
        } else if (["OCM", "OTDR"].includes(cardType)) {
            showType = "LINECARD";
        }

        openDBModalEdit({
            type: `${showType}_info`,
            title: value.name,
            loadData: async () => {
                const rs = await netconfGetByXML({
                    msg: true,
                    ne_id: value.ne_id,
                    xml: {
                        components: {
                            $: {
                                xmlns: "http://openconfig.net/yang/platform"
                            },
                            component: {
                                name: value.name
                            }
                        }
                    }
                });
                const initData = rs.components.component.state ?? {};
                Object.entries(initData).map(([k, v]) => {
                    if (v?.instant) {
                        initData[k] = v?.instant ?? "";
                    }
                });
                const componentInfo = rs.components.component[cardType.toLowerCase()];
                if (componentInfo) {
                    Object.entries(componentInfo.state)?.map?.(([k, v]) => {
                        if (v?.instant) {
                            initData[k] = v?.instant ?? "";
                        }
                    });
                }
                if (showType === "LINECARD") {
                    //
                } else {
                    const stateRs = await getStateData({
                        ne_id: value.ne_id
                    });
                    const stateData = stateRs.data?.value?.data?.["state-data"] ?? [];
                    if (showType === "TRANSCEIVER") {
                        const componentName = value.name.replace("TRANSCEIVER", "OCH");
                        initData.osnr = getDeviceStateValue(stateData, componentName, "osnr");
                        initData["post-fec-ber"] = getDeviceStateValue(stateData, componentName, "post-fec-ber");
                        initData["pre-fec-ber"] = getDeviceStateValue(stateData, componentName, "pre-fec-ber");
                    } else if (showType === "AMPLIFIER") {
                        let BAL1Name = `EDFA${value.name.substring(value.name.indexOf("-"))}-BA`;
                        let PAL2Name = `EDFA${value.name.substring(value.name.indexOf("-"))}-PA`;
                        if (cardType === "OLA") {
                            BAL1Name = `EDFA${value.name.substring(value.name.indexOf("-"))}-LA1`;
                            PAL2Name = `EDFA${value.name.substring(value.name.indexOf("-"))}-LA2`;
                        }
                        initData["ba-output-power"] = getDeviceStateValue(stateData, BAL1Name, "output-power");
                        initData["ba-actual-gain"] = getDeviceStateValue(stateData, BAL1Name, "actual-gain");
                        initData["pa-output-power"] = getDeviceStateValue(stateData, PAL2Name, "output-power");
                        initData["pa-actual-gain"] = getDeviceStateValue(stateData, PAL2Name, "actual-gain");
                        // initData["pa-output-power"] = getDeviceStateValue(stateData, componentName, "pre-fec-ber");
                    } else if (cardType === "OLP") {
                        const dataList = [
                            {
                                "": initData
                            }
                        ];
                        for (let i = 1; i <= 2; i++) {
                            const apsName = `APS${value.name.substring(value.name.indexOf("-"))}-${i}`;
                            if (getDeviceStateValue(stateData, apsName)) {
                                dataList.push({
                                    [apsName]: {
                                        "input-power-c": getDeviceStateValue(stateData, apsName, "common-in"),
                                        "output-power-c": getDeviceStateValue(stateData, apsName, "common-out"),
                                        "input-power-p": getDeviceStateValue(stateData, apsName, "line-primary-in"),
                                        "output-power-p": getDeviceStateValue(stateData, apsName, "line-primary-out"),
                                        "input-power-s": getDeviceStateValue(stateData, apsName, "line-secondary-in"),
                                        "output-power-s": getDeviceStateValue(stateData, apsName, "line-secondary-out"),
                                        revertive: getDeviceStateValue(stateData, apsName, "revertive"),
                                        "force-to-port": getDeviceStateValue(stateData, apsName, "force-to-port"),
                                        "active-path": getDeviceStateValue(stateData, apsName, "active-path")
                                    }
                                });
                            }
                        }
                        return dataList;
                    }
                }
                return initData;
            }
        });
    };

    return (
        <CustomTable
            type="relate_port"
            initDataSource={dataList}
            initHead={false}
            columnFormat={{
                name: r => r
            }}
            initRowOperation={[
                {
                    label: labelList.more,
                    disabled: item => {
                        return item["admin-state"] === "DISABLED";
                    },
                    async onClick() {
                        const originValue = {...this};
                        openMoreDialog(originValue);
                    },
                    display: item => {
                        return (
                            (item.componentType === "CARD" &&
                                ["LINECARD", "OA", "OLA", "OLP", "WSS", "OCM", "OTDR"].includes(
                                    item.name.split("-")[0]
                                )) ||
                            (item.componentType === "TRANSCEIVER" &&
                                ["L", "C"].includes(item.name?.split("-")?.pop()?.at(0)))
                        );
                    }
                }
            ]}
        />
    );
};

export default RelatePort;
