import {Tree, Input, message, Badge, Form} from "antd";
import React, {useDeferredValue, useEffect, useRef, useState} from "react";
import Icon, {SearchOutlined} from "@ant-design/icons";
import {useSelector, useDispatch} from "react-redux";
import {objectAdd, objectGet, objectDel, apiDelNE, objectUpdate, setLocationInfo} from "@/modules-otn/apis/api";
import {openDBModalCreate} from "@/modules-otn/components/form/create_form_db";
import {openDBModalEdit} from "@/modules-otn/components/form/edit_form_db";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {
    ALARM_COLOR,
    convertToArray,
    NE_TYPE_CONFIG,
    rootModal,
    SUPPORT_NE_TYPES,
    DebounceButton
} from "@/modules-otn/utils/util";
import {setTreeItemChangedTag, setSelectedItem, setTableFilter, setOnSelectItem} from "@/store/modules/otn/mapSlice";
import {setNeNameMap} from "@/store/modules/otn/neNameSlice";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {
    addObjectDisabledIcon,
    addObjectFocusIcon,
    addObjectIcon,
    addNEDisabledIcon,
    addNEFocusIcon,
    addNEIcon,
    commonViewIcon,
    deleteObjectDisabledIcon,
    deleteObjectFocusIcon,
    deleteObjectIcon,
    editNodeDisabledIcon,
    editNodeFocusIcon,
    editNodeIcon,
    expandFocusIcon,
    expandIcon,
    otnDeviceDisabledIcon,
    otnDeviceIcon,
    otnGroupIcon,
    switchDeviceDisabledIcon,
    switchDeviceIcon,
    switchGroupIcon
} from "@/modules-otn/pages/otn/device/device_icons";
import {
    addSwitchTreeGroup,
    addSwitchTreeInfo,
    delSwitchTreeGroup,
    delSwitchTreeInfo,
    editSwitchTreeGroup,
    editSwitchTreeInfo,
    getSwitchTreeData,
    setSwitchTreeLocation,
    updateSwitchTreeGroup
} from "@/modules-ampcon/apis/inventory_api";
import {treeModalAction} from "@/modules-ampcon/components/custom_modal";
import {smallModal} from "@/modules-otn/components/modal/custom_modal";
import {addDCP920Info} from "@/modules-ampcon/apis/dcp920";
import {addFMTInfo} from "@/modules-ampcon/apis/fmt";
import {delOTNDevice, editOTNInfo, getOTNDeviceList, setOTNTreeGroup} from "@/modules-ampcon/apis/otn";
import {
    isOtnNode,
    isRootNode,
    isSwitchNode,
    getNeOrGroupDetailById,
    splitOtnOrSwitchGroupKey,
    formatGroupAndNeInfo,
    isGroupNode,
    isCommonViewNode,
    isNeNode,
    isOtnGroup,
    isSwitchGroup,
    isOtnNe,
    isSwitchNe,
    getNeedUpdateGroupKeysInLeave
} from "./device_map_helper";
import styles from "./device_tree.module.scss";

const DeviceTree = () => {
    const {alarms, switchAlarms, upgrade} = useSelector(state => state.notification);
    const dispatch = useDispatch();
    const onlyOTN = import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-T";
    const defaultExpandedKeys = onlyOTN ? "otnRoot" : "commonViewRoot";
    const deferredAlarm = useDeferredValue(alarms);
    const [treeData, setTreeData] = useState([]);
    const deferredData = useDeferredValue(treeData);
    const [selectNode, setSelectNode] = useState();
    const [treeDataList, setTreeDataList] = useState();
    const userRight = useUserRight();
    const [expandedKeys, setExpandedKeys] = useState([defaultExpandedKeys]);
    const {selectedItem} = useSelector(state => state.map);
    const [selectedKeys, setSelectedKeys] = useState("");
    const [inputValue, setInputValue] = useState("");
    const [autoExpandParent, setAutoExpandParent] = useState(true);
    const [expandAll, setExpandAll] = useState(false);

    let isDBClick = false;

    const scrollToNode = selectedItem => {
        setTimeout(() => {
            const a = document.querySelector(`span[title="${selectedItem.value.ne_id ?? selectedItem.value.title}"]`);
            a?.scrollIntoView(false);
        }, 400);
    };

    useEffect(() => {
        try {
            if (selectedItem?.id) {
                if (selectedItem.value?.group || selectedItem?.value?.parentKey) {
                    const newKey = selectedItem.value.group || selectedItem.value.parentKey;
                    const _epKeys = structuredClone(expandedKeys);
                    if (!_epKeys.includes(newKey)) {
                        _epKeys.push(newKey);
                    }
                    setExpandedKeys(_epKeys);
                    setAutoExpandParent(true);
                }
                setSelectedKeys(selectedItem.id);
                if (selectedItem?.action !== 0) {
                    scrollToNode(selectedItem);
                }
            }
        } catch (e) {
            // console.log(e);
        }
    }, [selectedItem]);

    const getAllNE = (data, neList) => {
        const list = convertToArray(data);
        list.forEach(item => {
            if (item?.nodeType === "otn") {
                neList.push(item.value.value.ne_id);
            }
            if (item.children) {
                getAllNE(item.children, neList);
            }
        });
    };

    const onSelect = (selectedKeys, info) => {
        try {
            isDBClick = false;
            setTimeout(() => {
                if (!isDBClick) {
                    const select = info.selected ? info.selectedNodes : null;
                    setSelectNode(select);
                    dispatch(setSelectedItem({...info.selectedNodes?.[0]?.value, action: 0}));
                    dispatch(setOnSelectItem({...info.selectedNodes?.[0]?.value}));
                    if (!select || ["otnRoot", "switchRoot"].includes(select?.[0]?.key)) {
                        setSelectedKeys("");
                        if (["otnRoot", "switchRoot"].includes(select?.[0]?.key)) {
                            dispatch(setTableFilter({type: "ROOT"}));
                        } else {
                            dispatch(setTableFilter({type: "UN_SELECTED"}));
                        }
                        return;
                    }
                    if (select?.[0]?.value?.id?.includes("config:ne")) {
                        dispatch(
                            setTableFilter({
                                type: "NODE_NE",
                                id: select[0].value.value.ne_id
                            })
                        );
                    } else if (select?.[0]?.value?.id?.includes("nms:group")) {
                        const neList = [];
                        getAllNE(select, neList);
                        dispatch(setTableFilter({type: "NODE_GROUP", idList: neList}));
                    }
                }
            }, 300);
        } catch (e) {
            // console.log(e);
        }
    };
    const tree = useRef(null);

    const getTitle = (title, tips) => {
        try {
            const matched = inputValue !== "" ? title.match(new RegExp(`(.*?)(${inputValue})(.*)`, "i")) : null;
            return matched ? (
                <span title={tips}>
                    {matched[1]}
                    <span style={{color: "red"}}>{matched[2]}</span>
                    {matched[3]}
                </span>
            ) : (
                <span title={tips}>{title}</span>
            );
        } catch (e) {
            return <span title={title}>{title}</span>;
        }
    };

    const getAlarmInfo = (deviceType, ne_id, name) => {
        if (deviceType === "switch_ne") {
            return {
                size:
                    switchAlarms?.filter?.(alarm => alarm?.ne_id === name && alarm?.severity === "MAJOR")?.length ?? 0,
                level: "MAJOR"
            };
        }
        return {
            size: alarms?.filter?.(alarm => alarm?.ne_id === ne_id && alarm?.severity === "CRITICAL")?.length ?? 0,
            level: "CRITICAL"
        };
    };

    const rootNodes = [
        {
            id: "otnRoot",
            value: {
                name: "OTN",
                nodeType: "otn_group"
            }
        },
        {
            id: "switchRoot",
            value: {
                name: "Switch",
                nodeType: "switch_group"
            }
        }
    ];

    const groupIconMap = {
        commonViewRoot: commonViewIcon,
        otnRoot: otnGroupIcon,
        switchRoot: switchGroupIcon
    };

    const deviceIconMap = {
        otn_ne: {
            1: otnDeviceIcon, // 1: online 0:offLine
            0: otnDeviceDisabledIcon
        },
        switch_ne: {
            1: switchDeviceIcon,
            0: switchDeviceDisabledIcon
        }
    };

    /**
     *
     * @param groupList
     * @param devices
     */
    const createDeviceData = (groupList, devices) => {
        try {
            const devicesList = Object.fromEntries(
                devices
                    .map(({id, value}) => {
                        const {level, size} = getAlarmInfo(value.nodeType, value.ne_id, value.name);
                        // eslint-disable-next-line no-nested-ternary
                        const offsetValue = [size > 9 ? (size > 99 ? 23 : 18) : 10, 6];
                        return [
                            id,
                            {
                                title: (
                                    <Badge
                                        count={size}
                                        size="small"
                                        offset={offsetValue}
                                        style={{backgroundColor: ALARM_COLOR[level]}}
                                    >
                                        {getTitle(value.name, value.ne_id)}
                                    </Badge>
                                ),
                                sortValue: value.name,
                                key: id,
                                nodeType: value.nodeType,
                                value: {id, value},
                                icon: deviceIconMap[value.nodeType][value.runState ?? 0]
                            }
                        ];
                    })
                    .sort((a, b) => a[1].sortValue.localeCompare(b[1].sortValue))
            );
            Object.entries(devicesList).forEach(ne => {
                groupList[ne[1].value.value.group].children.push(ne[1]);
            });
        } catch (e) {
            // console.log(e);
        }
    };

    const createGroupData = groups => {
        const groupList = Object.fromEntries(
            groups
                .map(({id, value}) => [
                    id,
                    {
                        children: [],
                        icon: groupIconMap[id] ? <Icon component={groupIconMap[id]} /> : null,
                        key: id,
                        nodeType: value.nodeType,
                        sortValue: value.name,
                        title: getTitle(value.name),
                        value: {id, value}
                    }
                ])
                .sort((a, b) => a[1].sortValue.localeCompare(b[1].sortValue))
        );
        Object.entries(groupList).forEach(group => {
            groupList[group[1].value.value.parentKey]?.children?.push(group[1]);
        });
        return groupList;
    };

    const createTreeData = (groups, devices) => {
        try {
            const newGroups = [...rootNodes, ...groups];

            const groupList = createGroupData(newGroups);
            createDeviceData(groupList, devices);
            let treeDatas;
            if (onlyOTN) {
                treeDatas = groupList.otnRoot;
            } else {
                treeDatas = createGroupData([
                    {
                        id: "commonViewRoot",
                        value: {
                            name: "Common View",
                            nodeType: "common_group"
                        }
                    }
                ]).commonViewRoot;
                treeDatas.children = [groupList.switchRoot, groupList.otnRoot];
            }
            setTreeData([treeDatas]);
            setExpandedKeys([...(expandedKeys || [defaultExpandedKeys])]);
            setAutoExpandParent(true);
        } catch (e) {
            // console.log(e);
        }
    };

    const loadData = isInit => {
        const otnGroupRequest = objectGet("nms:group", {});
        const otnNesRequest = objectGet("config:ne");

        const switchRequest = getSwitchTreeData();

        const ampconOTNRequest = getOTNDeviceList();

        Promise.all([otnGroupRequest, otnNesRequest, switchRequest, ampconOTNRequest])
            .then(rs => {
                const [otnGroupResponse, otnNesResponse, switchResponse, ampconOTNResponse] = rs;

                const {formatGroups: formatAmpconOTNGroups, formatNes: formatAmpconOTNNes} = formatGroupAndNeInfo(
                    {groupInfo: ampconOTNResponse.groupInfo, neInfo: ampconOTNResponse.neInfo},
                    "otn"
                );

                const {formatGroups: formatOtnGroups, formatNes: formatOtnNes} = formatGroupAndNeInfo(
                    {groupInfo: otnGroupResponse.documents, neInfo: otnNesResponse.documents},
                    "otn"
                );
                const {formatGroups: formatSwitchGroups, formatNes: formatSwitchs} = formatGroupAndNeInfo(
                    {groupInfo: switchResponse.groupInfo, neInfo: switchResponse.neInfo},
                    "switch"
                );

                const groups = [...formatOtnGroups, ...formatSwitchGroups, ...formatAmpconOTNGroups];
                const nes = [...formatOtnNes, ...formatSwitchs, ...formatAmpconOTNNes];
                setTreeDataList({groups, nes});
                createTreeData(groups, nes);
                if (isInit) {
                    setExpandedKeys([
                        ...expandedKeys,
                        ...groups.filter(i => !expandedKeys.includes(i.id)).map(i => i.id)
                    ]);
                }
            })
            .catch(() => {
                // console.log("e =", e);
            });
    };

    useEffect(() => {
        loadData(true);
    }, []);

    useEffect(() => {
        if (Object.keys(upgrade).length > 0) {
            loadData();
        }
    }, [upgrade]);

    useEffect(() => {
        if (treeDataList) {
            createTreeData(treeDataList.groups, treeDataList.nes);
        }
    }, [inputValue, deferredAlarm]);

    // useEffect(() => {
    //     const firstLeafLine = document.getElementsByClassName("ant-tree-switcher-leaf-line")?.[0];
    //     if (firstLeafLine && firstLeafLine.style.display !== "none") {
    //         firstLeafLine.style.display = "none";
    //     }
    // });

    const clearSelect = () => {
        setSelectedKeys("");
        setSelectNode(null);
        setTimeout(() => {
            dispatch(setSelectedItem({})); // action:0 树的单击事件
        }, 200);
        dispatch(setTableFilter({type: "UN_SELECTED"}));
    };

    const delNode = () => {
        const onOk = async () => {
            if (selectNode) {
                selectNode?.forEach(async item => {
                    if (item?.children?.length > 0) {
                        message.warning(gLabelList.delete_node_confirm);
                        return;
                    }

                    if (isOtnNe(item.nodeType)) {
                        // delete NE, if the NE has services, need toast a warning
                        if (
                            (
                                await objectGet("ne:5:connection", {
                                    ne_id: item.value.value.ne_id
                                })
                            ).documents.length > 0
                        ) {
                            const modal2 = rootModal.confirm({
                                title: gLabelList.warning,
                                // eslint-disable-next-line no-unused-vars
                                onOk: _ => {
                                    modal2.destroy();
                                    delNE(item);
                                },
                                closable: true,
                                centered: true,
                                content: gLabelList.del_device_with_service
                            });
                            return;
                        }
                        delNE(item);
                    } else if (isSwitchNe(item.nodeType)) {
                        await delSwitch(item);
                    } else if (isOtnGroup(item.nodeType)) {
                        await objectDel({
                            key: item.key,
                            success: () => {
                                setSelectNode(null);
                                loadData();
                                dispatch(setTreeItemChangedTag(true));
                            }
                        }).then();
                    } else if (isSwitchGroup(item.nodeType)) {
                        await delSwitchTreeGroup(item.key).then(rs => {
                            if (rs.apiResult === "fail") {
                                message.error(rs.apiMessage);
                            } else {
                                setSelectNode(null);
                                loadData();
                                dispatch(setTreeItemChangedTag(true));
                            }
                        });
                    }
                });
            }
        };

        const confirmModal = smallModal({
            content: gLabelList.delete_confirm_msg,
            // eslint-disable-next-line no-unused-vars
            onOk: _ => {
                onOk().finally(() => {
                    confirmModal.destroy();
                });
            }
        });
    };

    const [form] = Form.useForm();

    const addNE = () => {
        let initData = {};
        if (selectNode) {
            initData = {
                group: {
                    value: selectNode[0].value.id,
                    options: [
                        {
                            value: selectNode[0].value.id,
                            label: selectNode[0].value.value.name
                        }
                    ]
                }
            };
        }
        const typeOptions = {options: []};
        Object.keys(NE_TYPE_CONFIG).map(type => {
            if (SUPPORT_NE_TYPES.includes(type.toString())) {
                typeOptions.options.push({
                    value: type.toString(),
                    label: NE_TYPE_CONFIG[type]
                });
            }
        });
        if (SUPPORT_NE_TYPES.length === 2) {
            typeOptions.value = typeOptions.options[1].value;
        }
        initData.type = typeOptions;

        if (isSwitchGroup(selectNode?.[0]?.nodeType)) {
            treeModalAction(form, "switch", "Switch SN", "SELECT", "", value => {
                addSwitchTreeInfo(value.switch, selectNode[0].value.id).then(rs => {
                    if (rs.apiResult === "fail") {
                        message.error(rs.apiMessage);
                    } else {
                        dispatch(setTreeItemChangedTag(true));
                        clearSelect();
                        message.success(rs.apiMessage);
                        setTimeout(() => {
                            setSelectNode(null);
                            loadData();
                            window.location.reload();
                        }, 500);
                    }
                });
            });
        } else if (isOtnGroup(selectNode?.[0]?.nodeType)) {
            openDBModalCreate({
                type: "config:ne",
                initData,
                submit: async (values, defaultValue, diffValue, cancel, fail, toContinue) => {
                    if (values.type === "6") {
                        addDCP920Info(selectNode[0].value.id, values.name, "DCP920", values.host).then(ret => {
                            if (ret.apiResult === "complete") {
                                // 添加设备和修改设备name， 需要更新设备的name的redux里的数据，删除可以不处理。
                                // 这里是更新了一个设备的ip对应的name
                                // 也可以直接读整个设备表，把整个表的name全更新一遍。
                                const mappingData = [
                                    {
                                        value: {ne_id: values.host, name: values.name}
                                    }
                                ];
                                dispatch(setNeNameMap(mappingData));
                                dispatch(setTreeItemChangedTag(true));
                                clearSelect();
                                message.success("Add device success");
                                cancel(true);
                                loadData();
                            } else {
                                message.error(ret.apiMessage);
                                fail(false);
                            }
                        });
                    } else if (values.type === "7") {
                        addFMTInfo(selectNode[0].value.id, values.name, "FMT", values.host).then(ret => {
                            if (ret.apiResult === "complete") {
                                // 添加设备和修改设备name， 需要更新设备的name的redux里的数据，删除可以不处理。
                                // 这里是更新了一个设备的ip对应的name
                                // 也可以直接读整个设备表，把整个表的name全更新一遍。
                                const mappingData = [
                                    {
                                        value: {ne_id: values.host, name: values.name}
                                    }
                                ];
                                dispatch(setNeNameMap(mappingData));
                                dispatch(setTreeItemChangedTag(true));
                                clearSelect();
                                message.success("Add device success");
                                cancel(true);
                                loadData();
                            } else {
                                message.error(ret.apiMessage);
                                fail(false);
                            }
                        });
                    } else {
                        toContinue(true);
                    }
                },
                success: rs => {
                    loadData();

                    objectGet("config:ne", {}).then(r => {
                        dispatch(setNeNameMap(r.documents));
                        dispatch(setTreeItemChangedTag(true));
                        // 创建成功时，取消已选中的树节点，防止数据错乱
                        clearSelect();

                        if (rs?.formValue?.group) {
                            setTimeout(() => {
                                setExpandedKeys([rs.formValue.group]);
                            }, 500);
                        }
                    });
                }
            });
        }
    };

    const delNE = item => {
        if (["6", "7"].includes(item.value.value.type)) {
            delOTNDevice(item.value.value.host).then(res => {
                if (res.errorCode === 0) {
                    message.success(gLabelList.delete_success);
                }
            });
            setTimeout(() => {
                setSelectNode(null);
                loadData();
                window.location.reload();
            }, 500);
        } else {
            const neInfo = item.value.value.ne_id.split(":");
            apiDelNE({host: neInfo[0], port: neInfo[1]}).then(res => {
                const {apiResult, apiMessage} = res || {};
                if (apiResult === "complete") {
                    message.success(gLabelList.delete_success);
                    setTimeout(() => {
                        setSelectNode(null);
                        loadData();
                        window.location.reload();
                    }, 500);
                }
                if (apiResult === "fail") {
                    message.error(apiMessage).then();
                }
            });
        }
    };

    const delSwitch = async item => {
        await delSwitchTreeInfo(item.value.value.host).then(rs => {
            if (rs.apiResult === "fail") {
                message.error(rs.apiMessage);
            } else {
                message.success(rs.apiMessage);
                setTimeout(() => {
                    setSelectNode(null);
                    loadData();
                    window.location.reload();
                }, 500);
            }
        });
    };

    const editNE = () => {
        openDBModalEdit({
            type: "config:ne",
            keys: {DBKey: selectNode[0].value.id},
            success: () => {
                loadData();
                // if (selectNode[0].value.value.type === "6") {
                //     delDCP920Info(selectNode[0].value.value.host).then(rs => {
                //         if (rs.errorCode === 0) {
                //             const mappingData = [
                //                 {
                //                     value: {ne_id: selectNode[0].value.value.host, name: selectNode[0].value.value.name}
                //                 }
                //             ];
                //             dispatch(setNeNameMap(mappingData));
                //             dispatch(setTreeItemChangedTag(true));
                //         }
                //     });
                // } else {
                objectGet("config:ne", {}).then(r => {
                    // 修改设备名称必须更新redux里的ne_id与name的对应关系, 更新方法和添加设备一样。
                    dispatch(setNeNameMap(r.documents));
                    dispatch(setTreeItemChangedTag(true));
                });
                // }
            }
        });
    };

    const editSwitch = async () => {
        await treeModalAction(form, "switch", "Switch SN", "SELECT", selectNode[0].value.value.host, value => {
            editSwitchTreeInfo(selectNode[0].value.value.host, value.switch).then(rs => {
                if (rs.apiResult === "fail") {
                    message.error(rs.apiMessage);
                } else {
                    // dispatch(setNeNameMap(rs));
                    message.success("update switch device success");
                    loadData();
                    setSelectNode(null);
                    dispatch(setTreeItemChangedTag(true));
                }
            });
        });
    };

    const editAmpconOTN = async () => {
        await treeModalAction(
            form,
            "AmpconOTN",
            "NE",
            "EDIT_AmpconOTN",
            [selectNode[0].value.value.name, selectNode[0].value.value.host, selectNode[0].value.value.type],
            value => {
                editOTNInfo(value.ip, value.name).then(rs => {
                    if (rs.errorCode !== 0) {
                        message.error(rs.apiMessage);
                    } else {
                        // dispatch(setNeNameMap(rs));
                        message.success("update OTN device success");
                        loadData();
                        setSelectNode(null);
                        dispatch(setTreeItemChangedTag(true));
                    }
                });
            }
        );
    };

    const onExpand = (newExpandedKeys, event) => {
        try {
            if (!event.expanded && newExpandedKeys.includes(event.node.key)) {
                const n = [...newExpandedKeys];
                delete n[event.node.key];
                setExpandedKeys(n);
            } else {
                setExpandedKeys(newExpandedKeys);
            }
            setAutoExpandParent(false);
        } catch (e) {
            // console.log(e);
        }
    };

    const searchTree = e => {
        const {value} = e.target;
        if (value.trim() !== "") {
            const newExpandedItems = treeDataList.groups.concat(treeDataList.nes).reduce((pre, item) => {
                const keyStr = item.value?.title || item.value?.name;
                return keyStr.toString().toUpperCase().indexOf(value.toString().toUpperCase()) > -1
                    ? [...pre, item]
                    : pre;
            }, []);

            setExpandedKeys(newExpandedItems.map(item => item.id));
            if (newExpandedItems.length > 0) {
                scrollToNode(newExpandedItems[0]);
            }
            setAutoExpandParent(true);
        }
        setInputValue(value);
    };

    const onDragStart = event => {
        if (userRight.disabled) {
            // eslint-disable-next-line no-alert
            alert(gLabelList.no_permission);
        }
        if (isRootNode(event.node.key)) {
            // eslint-disable-next-line no-alert
            alert(gLabelList.cannot_move);
        }
    };

    const onDrop = async event => {
        if (
            event.node.key === "commonViewRoot" ||
            (event.node.key === "otnRoot" && !isOtnNode(event.dragNode.nodeType)) ||
            (event.node.key === "switchRoot" && !isSwitchNode(event.dragNode.nodeType)) ||
            (isNeNode(event.node.nodeType) && isNeNode(event.dragNode.nodeType))
        ) {
            message.error(gLabelList.drop_type_not_match_error);
            return;
        }
        if (event.dragNode.nodeType?.endsWith("_group") && event.node.nodeType?.endsWith("_ne")) {
            message.error(gLabelList.move_group_to_ne);
            return;
        }
        if (
            event.dragNode.nodeType?.endsWith("_group") &&
            event.node.nodeType?.endsWith("_group") &&
            (event.dragNode.nodeType !== event.node.nodeType ||
                event.dragNode.value?.value?.parentKey === event.node.value?.id)
        ) {
            return;
        }

        if (
            event.dragNode.nodeType?.endsWith("_ne") &&
            event.node.nodeType?.endsWith("_group") &&
            event.dragNode.value?.value?.group === event.node.value?.id
        ) {
            return;
        }
        const update = {};
        if (isOtnNe(event.dragNode.nodeType)) {
            update.group = event.node.key;
        } else {
            update.parentKey = event.node.key;
        }

        const dragSourceID = event.dragNodesKeys[0];
        const dragTargetID = update.group ?? update.parentKey;

        const {nes, groups} = treeDataList;
        let newNeInfo = nes,
            newGroupInfo = groups,
            oldGroup;
        if (isNeNode(event.dragNode.nodeType)) {
            newNeInfo = nes.map(item => {
                if (item.id !== dragSourceID) return item;
                oldGroup = item.value.group;
                return {
                    id: item.id,
                    value: {
                        ...item.value,
                        group: dragTargetID
                    }
                };
            });
        } else {
            newGroupInfo = groups.map(item => {
                if (item.id !== dragSourceID) return item;
                oldGroup = item.value.parentKey;
                return {
                    id: item.id,
                    value: {
                        ...item.value,
                        parentKey: dragTargetID
                    }
                };
            });
        }
        const sourceDetail = getNeOrGroupDetailById(dragSourceID, newNeInfo, newGroupInfo);

        if (sourceDetail.value.lng && sourceDetail.value.lat) {
            const needUpdateLocationDataIn = getNeedUpdateGroupKeysInLeave({
                id: dragTargetID,
                neInfo: newNeInfo,
                groupInfo: newGroupInfo
            });
            const inRequests = [];
            Object.entries(needUpdateLocationDataIn).forEach(([key, value]) => {
                let [lng, lat] = key.split(",");
                lng = lng === "undefined" ? null : parseFloat(lng);
                lat = lat === "undefined" ? null : parseFloat(lat);
                const splitLeaveRequest = splitOtnOrSwitchGroupKey(value);
                if (splitLeaveRequest.otn.length) {
                    inRequests.push(
                        setLocationInfo({
                            neList: [],
                            groupList: splitLeaveRequest.otn,
                            lng,
                            lat
                        })
                    );
                }
                if (splitLeaveRequest.switch.length) {
                    inRequests.push(
                        setSwitchTreeLocation({
                            neList: [],
                            groupList: splitLeaveRequest.switch,
                            lng,
                            lat
                        })
                    );
                }
            });
            if (inRequests.length) {
                await Promise.all(inRequests);
            }

            const needUpdateLocationDataInLeave = getNeedUpdateGroupKeysInLeave({
                id: oldGroup,
                neInfo: newNeInfo,
                groupInfo: newGroupInfo
            });
            const leaveRequests = [];
            Object.entries(needUpdateLocationDataInLeave).forEach(([key, value]) => {
                let [lng, lat] = key.split(",");
                lng = lng === "undefined" ? null : parseFloat(lng);
                lat = lat === "undefined" ? null : parseFloat(lat);
                const splitLeaveRequest = splitOtnOrSwitchGroupKey(value);
                if (splitLeaveRequest.otn.length) {
                    leaveRequests.push(
                        setLocationInfo({
                            neList: [],
                            groupList: splitLeaveRequest.otn,
                            lng,
                            lat
                        })
                    );
                }
                if (splitLeaveRequest.switch.length) {
                    leaveRequests.push(
                        setSwitchTreeLocation({
                            neList: [],
                            groupList: splitLeaveRequest.switch,
                            lng,
                            lat
                        })
                    );
                }
            });
            if (leaveRequests.length) {
                await Promise.all(leaveRequests);
            }
        }

        if (isOtnNode(event.dragNode.nodeType)) {
            if (["6", "7"].includes(event.dragNode?.value?.value?.type)) {
                setOTNTreeGroup({
                    sourceId: dragSourceID,
                    targetId: dragTargetID
                }).then(rs => {
                    if (rs.apiResult === "fail") {
                        message.error(rs.apiMessage);
                    } else {
                        message.success(rs.apiMessage);
                        loadData();
                    }
                });
            }

            objectUpdate({
                key: event.dragNode.key,
                data: update,
                msg: true,
                success: () => loadData()
            }).then();
        } else if (isSwitchNode(event.dragNode.nodeType)) {
            updateSwitchTreeGroup({
                sourceId: dragSourceID,
                targetId: dragTargetID
            }).then(rs => {
                if (rs.apiResult === "fail") {
                    message.error(rs.apiMessage);
                } else {
                    message.success(rs.apiMessage);
                    loadData();
                }
            });
        }

        dispatch(setTreeItemChangedTag(true));
    };

    const onAddGroup = () => {
        openDBModalCreate({
            type: isOtnNode(selectNode[0].nodeType) ? "nms:group" : "switch:group",
            submit: async (values, defaultValue, diffValue, cancel, fail) => {
                const {name} = values;
                if (isOtnNode(selectNode[0].nodeType)) {
                    objectAdd({
                        entity: "nms:group",
                        data: {
                            name,
                            parentKey: selectNode[0].key
                        },
                        success: () => {
                            cancel?.({});
                            loadData();
                            clearSelect();
                            dispatch(setTreeItemChangedTag(true));
                            if (selectNode?.[0]?.key) {
                                setTimeout(() => {
                                    setExpandedKeys([selectNode[0].key]);
                                }, 500);
                            }
                        },
                        fail: rs => {
                            fail?.(rs);
                        }
                    }).then();
                }
                if (isSwitchNode(selectNode[0].nodeType)) {
                    addSwitchTreeGroup(name, selectNode[0].key).then(rs => {
                        if (rs.apiResult === "fail") {
                            message.error(rs.apiMessage);
                        } else {
                            cancel?.({});
                            setSelectNode(null);
                            loadData();
                            dispatch(setTreeItemChangedTag(true));
                            message.success(rs.apiMessage);
                            if (selectNode?.[0]?.key) {
                                setTimeout(() => {
                                    setExpandedKeys([selectNode[0].key]);
                                }, 500);
                            }
                        }
                    });
                }
            }
        });
    };

    const onEditBtn = async () => {
        if (isOtnNe(selectNode[0].nodeType)) {
            if (["6", "7"].includes(selectNode[0].value?.value?.type)) {
                await editAmpconOTN();
            } else {
                editNE(); // edit OTN ne
            }
        } else if (isSwitchNe(selectNode[0].nodeType)) {
            await editSwitch();
        } else if (isOtnGroup(selectNode[0].nodeType)) {
            openDBModalEdit({
                type: "nms:group",
                keys: {DBKey: selectNode[0].value.id},
                success: () => {
                    loadData();
                    objectGet("config:ne", {}).then(r => {
                        dispatch(setNeNameMap(r.documents));
                        dispatch(setTreeItemChangedTag(true));
                    });
                }
            });
        } else if (isSwitchGroup(selectNode[0].nodeType)) {
            await treeModalAction(form, "group", "Group Name", "INPUT", selectNode[0].value.value.host, value => {
                editSwitchTreeGroup(selectNode[0].key, value.group).then(rs => {
                    if (rs.apiResult === "fail") {
                        message.error(rs.apiMessage);
                    } else {
                        message.success(rs.apiMessage);
                        setSelectNode(null);
                        loadData();
                        dispatch(setTreeItemChangedTag(true));
                    }
                });
            });
        }
    };

    return (
        <div className={styles.container}>
            <div style={{paddingTop: 32, fontSize: 18, fontWeight: 700}}>NE List</div>
            <div style={{paddingTop: 24}}>
                <Input
                    placeholder="Search"
                    prefix={<SearchOutlined style={{color: "#b9b9b9"}} />}
                    value={inputValue}
                    style={{width: "280px"}}
                    onChange={event => {
                        searchTree(event);
                    }}
                />
            </div>
            <div style={{paddingTop: 24}}>
                <DebounceButton
                    containerType="ToolButton"
                    title={gLabelList.expand_btn}
                    enabledIcon={expandIcon}
                    onFocusIcon={expandFocusIcon}
                    onClick={() => {
                        if (expandAll) {
                            setExpandedKeys([]);
                        } else {
                            const keyList = [];
                            treeDataList.groups.forEach(item => {
                                // export group
                                keyList.push(item.id);
                            });
                            treeDataList.nes.forEach(item => {
                                // export NE
                                keyList.push(item.id);
                            });
                            setExpandedKeys(keyList);
                            setAutoExpandParent(true);
                        }
                        setExpandAll(!expandAll);
                    }}
                />
                <DebounceButton
                    containerType="ToolButton"
                    enabledIcon={addObjectIcon}
                    onFocusIcon={addObjectFocusIcon}
                    disabledIcon={addObjectDisabledIcon}
                    title={gLabelList.addGroup_btn}
                    disabled={
                        selectNode == null ||
                        !isGroupNode(selectNode?.[0]?.nodeType) ||
                        isCommonViewNode(selectNode?.[0]?.nodeType) ||
                        userRight.disabled
                    }
                    onClick={onAddGroup}
                    style={{marginLeft: 8}}
                />
                <DebounceButton
                    containerType="ToolButton"
                    title={gLabelList.del}
                    enabledIcon={deleteObjectIcon}
                    disabledIcon={deleteObjectDisabledIcon}
                    onFocusIcon={deleteObjectFocusIcon}
                    disabled={selectNode == null || isRootNode(selectNode?.[0]?.key) || userRight.disabled}
                    onClick={delNode}
                    style={{marginLeft: 8}}
                />
                <DebounceButton
                    containerType="ToolButton"
                    title={gLabelList.modify_group}
                    enabledIcon={editNodeIcon}
                    disabledIcon={editNodeDisabledIcon}
                    onFocusIcon={editNodeFocusIcon}
                    disabled={selectNode == null || isRootNode(selectNode?.[0]?.key) || userRight.disabled}
                    onClick={onEditBtn}
                    style={{marginLeft: 8}}
                />
                <DebounceButton
                    containerType="ToolButton"
                    title={gLabelList.add_ne}
                    enabledIcon={addNEIcon}
                    disabledIcon={addNEDisabledIcon}
                    onFocusIcon={addNEFocusIcon}
                    disabled={
                        !selectNode ||
                        isNeNode(selectNode?.[0]?.nodeType) ||
                        isCommonViewNode(selectNode?.[0]?.nodeType) ||
                        userRight.disabled
                    }
                    onClick={addNE}
                    style={{marginLeft: 8}}
                />
            </div>
            <div style={{paddingTop: 16, overflow: "auto", flex: 1, marginLeft: -5}}>
                {deferredData.length > 0 && (
                    <Tree
                        ref={tree}
                        showLine={{showLeafIcon: null}}
                        draggable={{icon: false}}
                        blockNode
                        showIcon
                        selectedKeys={[selectedKeys]}
                        onExpand={onExpand}
                        expandedKeys={expandedKeys}
                        autoExpandParent={autoExpandParent}
                        onDrop={onDrop}
                        onDragStart={onDragStart}
                        onSelect={onSelect}
                        treeData={deferredData}
                        defaultExpandedKeys={[defaultExpandedKeys]}
                        onDoubleClick={(e, node) => {
                            if (userRight.disabled) {
                                return;
                            }
                            isDBClick = true;
                            if (node.nodeType === "otn_ne" && ["2", "5"].includes(node?.value?.value?.type)) {
                                objectGet("config:ne", {DBKey: node.key, security: false}).then(rs => {
                                    const {host, username, password, type} = rs.documents[0].value;
                                    let url;
                                    if (type === "2") {
                                        url = `http://${host}`;
                                    } else if (type === "5") {
                                        url = `https://${host}/login?p1=${username}&p2=${password}&p3=${localStorage.language}`;
                                    }
                                    const isBlank = window.open(url, "_blank");
                                    if (!isBlank) {
                                        message.error(gLabelList.not_allowed_pop);
                                    }
                                });
                            }

                            if (node.nodeType === "otn_ne" && ["6"].includes(node?.value?.value?.type)) {
                                const host = node?.value?.value?.host;
                                const isBlank = window.open(`http://${host}`, "_blank");
                                if (!isBlank) {
                                    message.error(gLabelList.not_allowed_pop);
                                }
                            }
                        }}
                    />
                )}
            </div>
        </div>
    );
};

export default DeviceTree;
