import {Table} from "antd";
import {getValueByJPath, convertToArray} from "@/modules-otn/utils/util";
import styles from "./custom_table.module.scss";

export const CustomTable = props => {
    const {columns} = props;
    columns?.forEach(col => {
        if (col.onCell) {
            const func = col.onCell;
            col.onCell = record => {
                const rt = func(record);
                rt.className = styles.customTable_ctn_content_cell;
                return rt;
            };
        } else {
            col.onCell = record => ({
                record,
                ...col,
                className: styles.customTable_ctn_content_cell
            });
        }
        col.onHeaderCell = column => ({
            ...column,
            title: null,
            className: styles.customTable_ctn_content_headerCell
        });

        if (!col.sorter) {
            col.sorter = (a, b) => {
                const av = getValueByJPath(a, convertToArray(col.dataIndex));
                const bv = getValueByJPath(b, convertToArray(col.dataIndex));
                if (av == null && bv == null) return 1;
                if (av == null) return -1;
                if (bv == null) return 1;
                return av >= bv ? 1 : -1;
            };
        }
    });

    return (
        <Table
            bordered
            {...props}
            columns={columns}
            pagination={
                props.pagination ?? {
                    showSizeChanger: true,
                    showTotal(total, range) {
                        return `${range.join("-")} of ${total} items`;
                    },
                    defaultPageSize: 20,
                    showQuickJumper: true
                }
            }
            rowClassName={styles.customTable_ctn_content_row}
        />
    );
};

export const showTablePage = (title, props) => {
    return (
        <div>
            <div className={styles.customTable_ctn_header}>
                <div className={styles.customTable_ctn_header_title}>{title}</div>
            </div>
            <div className={styles.customTable_ctn_content}>{CustomTable(props)}</div>
        </div>
    );
};
