import {Form} from "antd";
import {max} from "lodash";
import {useState, forwardRef, useImperativeHandle, useLayoutEffect} from "react";

const MutilColumnForm = forwardRef((props, ref) => {
    const {fields, newRowItems = [], formProps = {}} = props;
    const [labelMaxWidth, setLabelMaxWidth] = useState(0);
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
        form
    }));

    useLayoutEffect(() => {
        const maxWidth =
            max(
                Array.from(document.querySelectorAll(".ant-form-item-label > label")).map(item => {
                    return parseInt(item.offsetWidth);
                })
            ) + 32;
        setLabelMaxWidth(maxWidth);
    }, [fields]);

    return (
        <Form
            form={form}
            labelAlign="left"
            {...formProps}
            style={{width: "100%", display: "flex", flexWrap: "wrap", columnGap: 80, marginTop: 8}}
        >
            {fields.map(item => {
                const {render, ...rest} = item;
                return (
                    <Form.Item
                        {...rest}
                        labelCol={{style: {...(labelMaxWidth ? {width: labelMaxWidth} : {})}}}
                        wrapperCol={{flex: "0 0 280px"}}
                        style={{width: "auto"}}
                    >
                        {render}
                    </Form.Item>
                );
            })}
        </Form>
    );
});

export default MutilColumnForm;
