.tca {
    padding: 5px;

    &_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 15px;
        border-bottom: 1px solid #f0f0f0;

        &_title {
            font-size: 16px;
            font-weight: 600;
        }

        &_select {
            font-weight: 500;
            display: inline-block;
            margin: 0 10px;
        }
    }

    &_content {
        display: flex;
        flex-direction: column;
        overflow: auto;
        margin: 5px 10px;
    }
}

.table_select span {
    //margin-left: -14px;
    font-size: 12px;
}

.table_select_unit {
  font-family: Lato, serif;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.default_btn:hover {
    background-color: #e7f9f8 !important;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
}
