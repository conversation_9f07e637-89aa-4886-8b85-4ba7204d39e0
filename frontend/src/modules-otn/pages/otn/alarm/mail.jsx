import {rootModal} from "@/modules-otn/utils/util";
import styles from "@/modules-otn/components/form/create_form.module.scss";
import {Form} from "antd";

const formConfig = [
    [
        {
            dataIndex: "sender"
        },
        {
            dataIndex: "smtp"
        }
    ],
    [
        {
            dataIndex: "username"
        },
        {
            dataIndex: "password",
            type: "password"
        }
    ],
    [
        {
            dataIndex: "level",
            type: "check"
        }
    ],
    [
        {
            dataIndex: "port"
        },
        {
            dataIndex: "ssl",
            type: "check"
        }
    ],
    [
        {
            type: "split"
        }
    ],
    [
        {
            dataIndex: "recipient"
        },
        {
            dataIndex: "subject"
        }
    ]
];

const MailForm = () => {
    const [form] = Form.useForm();
    const handleSubmit = async () => {};

    return (
        <>
            <div className={styles.create_form_header} />
            <div className={styles.create_form_content}>
                <Form
                    labelCol={{span: 6}}
                    wrapperCol={{span: 18}}
                    form={form}
                    onFinish={handleSubmit}
                    className={styles.create_form}
                    style={{overflow: "auto"}}
                    labelAlign="left"
                />
            </div>
        </>
    );
};

export const openMaiConfig = () => {
    let form;
    const handle = fun => {
        form = fun;
    };

    const afterFinish = success => {
        if (success) {
            modal.destroy();
        } else {
            modal.update({okButtonProps: {loading: false}});
        }
    };

    const processFail = () => {
        modal.update({okButtonProps: {loading: false}});
    };

    const modal = rootModal.confirm({
        title: "Configure",
        width: 600,
        closable: true,
        centered: true,
        icon: null,
        okText: "Save",
        onOk: _ => {
            form.validateFields().then(() => {
                modal.update({okButtonProps: {loading: true}});
                form.submit();
            });
        },
        content: <MailForm processFail={processFail} onCancel={afterFinish} setForm={handle} />
    });
};
