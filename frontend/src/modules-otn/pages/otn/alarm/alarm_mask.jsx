import {useEffect, useState} from "react";
import {useSelector} from "react-redux";
import {message} from "antd";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import Icon from "@ant-design/icons";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {openDBModalCreate} from "@/modules-otn/components/form/create_form_db";
import {objectGet, netconfChange} from "@/modules-otn/apis/api";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {bigModal} from "@/modules-otn/components/modal/custom_modal";
import {
    deleteCommonIcon,
    deleteCommonDisabledIcon,
    addCommonDisabledIcon,
    addCommonPrimaryIcon
} from "@/modules-otn/pages/otn/device/device_icons";

const AlarmMask = () => {
    const {labelList} = useSelector(state => state.languageOTN);
    const {neNameMap} = useSelector(state => state.neName);
    const [data, setData] = useState([]);
    const [commonData, setCommonData] = useState({});
    const userRight = useUserRight();

    const loadData = () => {
        try {
            objectGet("ne:5:alarm-mask", {}).then(rs => {
                setData(
                    rs.documents.map(item => {
                        return {
                            key: item.id,
                            name: neNameMap[item.value.ne_id],
                            ne_id: item.value.ne_id,
                            ...item.value.data.config
                        };
                    })
                );
            });
        } catch (e) {
            // console.log(e);
        }
    };

    useEffect(() => {
        loadData();
    }, [neNameMap]);

    return (
        <>
            {/* <div className={styles.create_form_header} style={{marginBottom: 20}} /> */}
            <CustomTable
                type="alarm_mask"
                initDataSource={data}
                refreshParent={() => {
                    loadData();
                }}
                commonData={commonData}
                setCommonData={setCommonData}
                scroll={false}
                buttons={[
                    {
                        label: labelList.create,
                        disabled: userRight.disabled,
                        type: "primary",
                        icon: <Icon component={userRight.disabled ? addCommonDisabledIcon : addCommonPrimaryIcon} />,
                        onClick: async () => {
                            openDBModalCreate({
                                type: "create_alarm_mask",
                                submit: async (values, defaultValue, diffValue, cancel, fail) => {
                                    const filter = {objectRange: values["object-range"]};
                                    if (values["object-name"]) {
                                        filter.objectName = values["object-name"];
                                    }
                                    if (
                                        (
                                            await objectGet("ne:5:alarm-mask", {
                                                ...filter,
                                                ne_id: values.ne_id
                                            })
                                        ).documents.length > 0
                                    ) {
                                        message.error(labelList.alarm_mask_exist);
                                        fail({});
                                        return;
                                    }

                                    await netconfChange({
                                        ne_id: values.ne_id,
                                        operation: "create",
                                        entity: "alarm-mask",
                                        keys: [values.index],
                                        values: {
                                            config: {
                                                "object-name": values["object-name"] ?? "",
                                                "object-range": values["object-range"]
                                            }
                                        },
                                        msg: true
                                    }).then(rs => {
                                        if (rs.apiResult === "fail") {
                                            fail?.(rs);
                                        } else {
                                            setTimeout(() => {
                                                loadData();
                                                cancel?.(rs);
                                            }, 500);
                                        }
                                    });
                                }
                            });
                        }
                    },
                    {
                        label: labelList.delete,
                        disabled: userRight.disabled || !commonData.alarm_mask,
                        icon: (
                            <Icon
                                component={
                                    userRight.disabled || !commonData.alarm_mask
                                        ? deleteCommonDisabledIcon
                                        : deleteCommonIcon
                                }
                            />
                        ),
                        confirm: {title: gLabelList.delete_confirm},
                        onClick: async () => {
                            if (commonData?.alarm_mask?.selectedRows?.length > 0) {
                                let success = true;
                                for (let i = 0; i < commonData.alarm_mask.selectedRows.length; i++) {
                                    const {ne_id, index} = commonData.alarm_mask.selectedRows[i];
                                    const rs = await netconfChange({
                                        ne_id,
                                        operation: "delete",
                                        entity: "alarm-mask",
                                        keys: [index],
                                        values: {
                                            config: {}
                                        },
                                        msg: false
                                    });
                                    if (rs.apiResult === "fail") {
                                        message.error(`${labelList.delete_failed}`);
                                        success = false;
                                        break;
                                    }
                                }
                                if (success) {
                                    setTimeout(() => {
                                        message.success(labelList.delete_success);
                                        loadData();
                                    }, 500);
                                }
                            }
                        }
                    }
                ]}
            />
        </>
    );
};

const openAlarmMaskDialog = () => {
    bigModal({
        title: gLabelList.alarm_mask,
        okText: gLabelList.ok,
        content: <AlarmMask />
    });
};

export {openAlarmMaskDialog};
