export const AlarmFaultHandlingCN = {
    MCU_SYNC_FAIL: {
        alarmName: "MCU_SYNC_FAIL",
        alarmLevel: "次要",
        alarmMeaning: "主备同步失败",
        reportObject: "主用主控",
        errorDescription: "主主控板和备主控板之间的软件版本不一致；",
        treatmentSuggestion:
            "系统升级操作完成之后，主主控和备主控的版本会一致。后更换的主控，如果版本和主主控不一致上报告警，需要重新升级主控，同步主备版本。"
    },
    SWVERSION_MISMATCH: {
        alarmName: "SWVERSION_MISMATCH",
        alarmLevel: "提示",
        alarmMeaning: "单板版本不匹配",
        reportObject: "单板",
        errorDescription: "单板版本和主控预期版本不一致",
        treatmentSuggestion: "升级单板"
    },
    EQPT_FILE_NOT_ACTIVE: {
        alarmName: "EQPT_FILE_NOT_ACTIVE",
        alarmLevel: "主要",
        alarmMeaning: "单板文件未激活",
        reportObject: "单板",
        errorDescription: "单板软件已下发，未下发激活命令",
        treatmentSuggestion: "激活单板"
    },
    FIRMWARE_NEED_UPDATE: {
        alarmName: "FIRMWARE_NEED_UPDATE",
        alarmLevel: "次要",
        alarmMeaning: "单板固件待升级",
        reportObject: "单板",
        errorDescription: "单板固件待升级",
        treatmentSuggestion: "等待单板固件升级完成"
    },
    FIRMWARE_UPDATING: {
        alarmName: "FIRMWARE_UPDATING",
        alarmLevel: "次要",
        alarmMeaning: "单板固件正在升级",
        reportObject: "单板",
        errorDescription: "单板固件正在升级",
        treatmentSuggestion: "等待单板固件升级完成"
    },
    FLASH_OVER: {
        alarmName: "FLASH_OVER",
        alarmLevel: "紧急",
        alarmMeaning: "Flash文件系统空间不足",
        reportObject: "主控及其他支持单板",
        errorDescription: "SD卡文件系统空间使用率超过85%上报告警",
        treatmentSuggestion: "重启主控；若告警仍不清除，联系供应商获得技术支持；"
    },
    MEM_OVER: {
        alarmName: "MEM_OVER",
        alarmLevel: "紧急",
        alarmMeaning: "内存文件系统空间不足",
        reportObject: "主控及其他支持单板",
        errorDescription: "内存文件系统空间使用率超过85%上报告警",
        treatmentSuggestion: "重启主控；若告警仍不清除，联系供应商获得技术支持；"
    },
    PASSWORD_NEED_CHANGE: {
        alarmName: "PASSWORD_NEED_CHANGE",
        alarmLevel: "主要",
        alarmMeaning: "默认用户的密码需要修改",
        reportObject: "网元",
        errorDescription: "默认用户的密码未修改过，有安全风险，需要修改",
        treatmentSuggestion: "联系供应商获得技术支持；"
    },
    REL_POWER_DIFF_OVER: {
        alarmName: "REL_POWER_DIFF_OVER",
        alarmLevel: "主要",
        alarmMeaning: "主备通道信号相对光功率越限",
        reportObject: "APS",
        errorDescription:
            "(1)主通道或备通道衰减过大，从而导致到接收时光功率差异过大，超出3dB越限门限；(2)上游站点主备通道发送光功率差异过大，接收端光功率差异超出3dB越限门限；(3)单板检测电路故障。",
        treatmentSuggestion: "检查主备通道接收光功率"
    },
    APS_POWER_OVER: {
        alarmName: "APS_POWER_OVER",
        alarmLevel: "主要",
        alarmMeaning: "主备通道信号绝对光功率越限",
        reportObject: "APS",
        errorDescription: "(1)主通道或备通道衰减过大，从而导致到接收时光功率过低，低于越限门限；",
        treatmentSuggestion: "检查主备通道接收光功率"
    },
    APS_STATUS_INDI: {
        alarmName: "APS_STATUS_INDI",
        alarmLevel: "主要",
        alarmMeaning: "APS倒换指示",
        reportObject: "APS",
        errorDescription: "保护组处于倒换状态",
        treatmentSuggestion: "检查主通道接收光功率"
    },
    APS_SF_INDI: {
        alarmName: "APS_SF_INDI",
        alarmLevel: "主要",
        alarmMeaning: "保护组信号失效状态指示告警",
        reportObject: "APS",
        errorDescription: "保护组存在SF状态",
        treatmentSuggestion: "检查主备通道接收光功率"
    },
    APS_INDI: {
        alarmName: "APS_INDI",
        alarmLevel: "次要",
        alarmMeaning: "保护协议状态指示告警",
        reportObject: "APS",
        errorDescription: "下发了外部倒换命令(包括人工倒换、强制倒换、练习倒换)",
        treatmentSuggestion: "释放外部倒换命令"
    },
    LOOP_ALM: {
        alarmName: "LOOP_ALM",
        alarmLevel: "次要",
        alarmMeaning: "板上通道处于环回状态",
        reportObject: "产生警示用户的告警.",
        errorDescription: "端口",
        treatmentSuggestion: "人工配置了线路板或接口板的环回。"
    },
    COMMUN_FAIL: {
        alarmName: "COMMUN_FAIL",
        alarmLevel: "主要",
        alarmMeaning: "单板间通信失效",
        reportObject: "单板",
        errorDescription: "(1)单板故障；(2)单板复位。",
        treatmentSuggestion:
            "单板复位完成之后，告警会自动清除；如果告警始终不清除，拔插单板；若告警仍不清除，重启主控；若告警仍不清除，联系供应商获得技术支持；"
    },
    LINK_DOWN: {
        alarmName: "LINK_DOWN",
        alarmLevel: "紧急",
        alarmMeaning: "数据链路错误",
        reportObject: "端口",
        errorDescription:
            "(1)收发两端的光口工作模式不一致，造成协商失败；(2)链路故障；(3)电缆、光纤连接或者对端设备故障；(4)对接的两个以太网端口，输入光功率过高或过低；(5)对接的两个以太网端口的光接口模式不一致，或光接口模式和光纤模式不一致；(6)本站单板故障。",
        treatmentSuggestion:
            "检查单板端口连接是否正常；若告警仍不清除，重启单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TELEMETRY_COMM_FAIL: {
        alarmName: "TELEMETRY_COMM_FAIL",
        alarmLevel: "主要",
        alarmMeaning: "网元和Telemetry server通讯失效",
        reportObject: "网元",
        errorDescription: "在TCP模式下，网元和Telemetry服务器连接中断或者和服务器之间会话异常则上报此告警。",
        treatmentSuggestion: "检查Telemetry服务器的访问连接是否正常"
    },
    TCA_OPT_INPUT_POWER_H_15MIN: {
        alarmName: "TCA_OPT_INPUT_POWER_H_15MIN",
        alarmLevel: "紧急",
        alarmMeaning: "输入光功率15分钟越高门限",
        reportObject: "端口、OCH、OP、OSC",
        errorDescription: "(1)对端站发送功率过高；(2)选择的光模块型号不合适；(3)本站单板故障；(4)没有加适量的衰减。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查衰减；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_INPUT_POWER_H_24H: {
        alarmName: "TCA_OPT_INPUT_POWER_H_24H",
        alarmLevel: "紧急",
        alarmMeaning: "输入光功率24小时越高门限",
        reportObject: "端口、OCH、OP、OSC",
        errorDescription: "(1)对端站发送功率过高；(2)选择的光模块型号不合适；(3)本站单板故障；(4)没有加适量的衰减。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查衰减；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_INPUT_POWER_L_15MIN: {
        alarmName: "TCA_OPT_INPUT_POWER_L_15MIN",
        alarmLevel: "紧急",
        alarmMeaning: "输入光功率15分钟越低门限",
        reportObject: "端口、OCH、OP、OSC",
        errorDescription:
            "(1)对端站发送功率过低；(2)光纤性能劣化；(3)选择的光模块型号不合适；(4)光纤连接器污损；(5)尾纤弯曲度过大、损坏或老化；(6)本站单板的接收光口加了过大的光衰减器；(7)光信号在传输过程中衰减过大，没有得到足够的光放大补偿；(8)对端站单板的发送光口添加了过大的光衰减器或者发送光模块故障，导致对端站单板发送光功率过低；(9)本站单板故障；(10)上游断纤，接收信号为噪声；(11)上游单板激光器关闭，接收信号为噪声。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查光纤连接是否正常，清洁光纤连接头；检查衰减；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_INPUT_POWER_L_24H: {
        alarmName: "TCA_OPT_INPUT_POWER_L_24H",
        alarmLevel: "紧急",
        alarmMeaning: "输入光功率24小时越低门限",
        reportObject: "端口、OCH、OP、OSC",
        errorDescription:
            "(1)对端站发送功率过低；(2)光纤性能劣化；(3)选择的光模块型号不合适；(4)光纤连接器污损；(5)尾纤弯曲度过大、损坏或老化；(6)本站单板的接收光口加了过大的光衰减器；(7)光信号在传输过程中衰减过大，没有得到足够的光放大补偿；(8)对端站单板的发送光口添加了过大的光衰减器或者发送光模块故障，导致对端站单板发送光功率过低；(9)本站单板故障；(10)上游断纤，接收信号为噪声；(11)上游单板激光器关闭，接收信号为噪声。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查光纤连接是否正常，清洁光纤连接头；检查衰减；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LANE_IN_PWR_H_15MIN: {
        alarmName: "TCA_LANE_IN_PWR_H_15MIN",
        alarmLevel: "紧急",
        alarmMeaning: "子通道激光器输入功率15分钟过高",
        reportObject: "端口",
        errorDescription: "子通道激光器输入功率过高",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LANE_IN_PWR_H_24H: {
        alarmName: "TCA_LANE_IN_PWR_H_24H",
        alarmLevel: "紧急",
        alarmMeaning: "子通道激光器输入功率24小时过高",
        reportObject: "端口",
        errorDescription: "子通道激光器输入功率过高",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LANE_IN_PWR_L_15MIN: {
        alarmName: "TCA_LANE_IN_PWR_L_15MIN",
        alarmLevel: "紧急",
        alarmMeaning: "子通道激光器输入功率15分钟过低",
        reportObject: "端口",
        errorDescription: "子通道激光器输入功率过低",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查光纤连接是否正常，清洁光纤连接头；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LANE_IN_PWR_L_24H: {
        alarmName: "TCA_LANE_IN_PWR_L_24H",
        alarmLevel: "紧急",
        alarmMeaning: "子通道激光器输入功率24小时过低",
        reportObject: "端口",
        errorDescription: "子通道激光器输入功率过低",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查光纤连接是否正常，清洁光纤连接头；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LANE_OUT_PWR_H_15MIN: {
        alarmName: "TCA_LANE_OUT_PWR_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "子通道激光器输出功率15分钟过高",
        reportObject: "端口",
        errorDescription: "子通道激光器输出功率过高",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LANE_OUT_PWR_H_24H: {
        alarmName: "TCA_LANE_OUT_PWR_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "子通道激光器输出功率24小时过高",
        reportObject: "端口",
        errorDescription: "子通道激光器输出功率过高",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LANE_OUT_PWR_L_15MIN: {
        alarmName: "TCA_LANE_OUT_PWR_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "子通道激光器输出功率15分钟过低",
        reportObject: "端口",
        errorDescription: "子通道激光器输出功率过低",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查光纤连接是否正常，清洁光纤连接头；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LANE_OUT_PWR_L_24H: {
        alarmName: "TCA_LANE_OUT_PWR_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "子通道激光器输出功率24小时过低",
        reportObject: "端口",
        errorDescription: "子通道激光器输出功率过低",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查光纤连接是否正常，清洁光纤连接头；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    OA_OUTPUT_LOS: {
        alarmName: "OA_OUTPUT_LOS",
        alarmLevel: "次要",
        alarmMeaning: "无输出功率",
        reportObject: "端口",
        errorDescription: "(1)EDFA模块老化；(2)测量放大电路故障；(3)无输入光。",
        treatmentSuggestion: "检查入光是否正常；检查OA是否有EDFA相关告警；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_OUTPUT_POWER_H_15MIN: {
        alarmName: "TCA_OPT_OUTPUT_POWER_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "输出光功率15分钟过高",
        reportObject: "端口、OCH、OP、OSC",
        errorDescription: "线路侧、EDFA模块故障。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；若告警仍不清除，拔插/更换模块或单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_OUTPUT_POWER_H_24H: {
        alarmName: "TCA_OPT_OUTPUT_POWER_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "输出光功率24小时过高",
        reportObject: "端口、OCH、OP、OSC",
        errorDescription: "线路侧、EDFA模块故障。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；若告警仍不清除，拔插/更换模块或单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_OUTPUT_POWER_L_15MIN: {
        alarmName: "TCA_OPT_OUTPUT_POWER_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "输出光功率15分钟过低",
        reportObject: "端口、OCH、OP、OSC",
        errorDescription: "(1)线路侧、EDFA模块故障；(2)输入光异常。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；若告警仍不清除，拔插/更换模块或单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_OUTPUT_POWER_L_24H: {
        alarmName: "TCA_OPT_OUTPUT_POWER_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "输出光功率24小时过低",
        reportObject: "端口、OCH、OP、OSC",
        errorDescription: "(1)线路侧、EDFA模块故障；(2)输入光异常。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；若告警仍不清除，拔插/更换模块或单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    RX_LOS: {
        alarmName: "RX_LOS",
        alarmLevel: "紧急",
        alarmMeaning: "接收信号丢失",
        reportObject: "端口、模块、EDFA",
        errorDescription:
            "(1)本站单板光接口处未连接尾纤；(2)对端站单板激光器关闭；(3)传输线路断纤；(4)传输线路衰耗过大；(5)对端站单板发送部分故障；(6)本站接收部分故障。",
        treatmentSuggestion:
            "检查光纤连接是否正确；检查上游单板激光器输出光功率：若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    OSC_LOS: {
        alarmName: "OSC_LOS",
        alarmLevel: "紧急",
        alarmMeaning: "OSC信号丢失",
        reportObject: "端口、OSC",
        errorDescription: "OSC单板输入光功率低于其LOS门限。",
        treatmentSuggestion:
            "检查光纤连接是否正确；检查上游单板激光器输出光功率：若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    OSC_LF: {
        alarmName: "OSC_LF",
        alarmLevel: "紧急",
        alarmMeaning: "OSC本地失效告警",
        reportObject: "端口、OSC",
        errorDescription: "OSC本地失效告警",
        treatmentSuggestion:
            "检查光纤连接是否正确；检查上游单板激光器输出光功率：若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    EQPT_MISMATCH: {
        alarmName: "EQPT_MISMATCH",
        alarmLevel: "主要",
        alarmMeaning: "所插板类型错误",
        reportObject: "单板",
        errorDescription: "所配置的逻辑板与设备实际的物理板类型不一致。",
        treatmentSuggestion: "拔出单板，删除板卡，插入单板，系统会自动配置物理插入的单板类型。"
    },
    NTP_SYNC_FAIL: {
        alarmName: "NTP_SYNC_FAIL",
        alarmLevel: "次要",
        alarmMeaning: "NTP时间同步失败",
        reportObject: "网元",
        errorDescription:
            "NTP时间同步失败告警指示网元目前处于时间未同步状态（1天未同步）。在NTP成功完成时间同步后，该告警结束。",
        treatmentSuggestion: "检查NTP服务器的访问连接是否正常"
    },
    TCA_TEMP_L_15MIN: {
        alarmName: "TCA_TEMP_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "温度15分钟越低门限",
        reportObject: "单板、模块、EDFA",
        errorDescription: "(1)单板或模块环境温度过低；(2)单板或模块故障。",
        treatmentSuggestion:
            "检查单板或模块环境温度；检查TCA门限是否设置合理；检查是否存在风扇告警；拔插/更换单板或模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_TEMP_L_24H: {
        alarmName: "TCA_TEMP_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "温度24小时越低门限",
        reportObject: "单板、模块、EDFA",
        errorDescription: "(1)单板或模块环境温度过低；(2)单板或模块故障。",
        treatmentSuggestion:
            "检查单板或模块环境温度；检查TCA门限是否设置合理；检查是否存在风扇告警；拔插/更换单板或模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_TEMP_H_15MIN: {
        alarmName: "TCA_TEMP_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "温度15分钟越高门限",
        reportObject: "单板、模块、EDFA",
        errorDescription: "(1)单板或模块环境温度过高；(2)单板或模块故障。",
        treatmentSuggestion:
            "检查单板或模块环境温度；检查TCA门限是否设置合理；检查是否存在风扇告警；拔插/更换单板或模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_TEMP_H_24H: {
        alarmName: "TCA_TEMP_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "温度24小时越高门限",
        reportObject: "单板、模块、EDFA",
        errorDescription: "(1)单板或模块环境温度过高；(2)单板或模块故障。",
        treatmentSuggestion:
            "检查单板或模块环境温度；检查TCA门限是否设置合理；检查是否存在风扇告警；拔插/更换单板或模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TEMP_HEAT: {
        alarmName: "TEMP_HEAT",
        alarmLevel: "紧急",
        alarmMeaning: "工作温度严重越限",
        reportObject: "单板、模块、子模块",
        errorDescription:
            "(1)风扇停止工作或防尘板积累灰尘太多；(2)制冷(热)设备故障，导致环境温度过高(低)；(3)单板故障 。",
        treatmentSuggestion: "检查是否存在风扇告警；拔插/更换单板或模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    OA_LOW_GAIN: {
        alarmName: "OA_LOW_GAIN",
        alarmLevel: "紧急",
        alarmMeaning: "光放增益降低告警",
        reportObject: "EDFA",
        errorDescription: "(1)PUMP老化使增益达不到要求；(2)输入光功率过高；(3)AD检测故障。",
        treatmentSuggestion: "检查上游输出光功率；拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    OSNR_DEG: {
        alarmName: "OSNR_DEG",
        alarmLevel: "次要",
        alarmMeaning: "OSNR劣化告警",
        reportObject: "端口",
        errorDescription: "OSNR劣化",
        treatmentSuggestion:
            "检查上游输出光信号质量；检查光模块是否存在告警；拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    EQPT_TRANSCEIVER_MISMATCH: {
        alarmName: "EQPT_TRANSCEIVER_MISMATCH",
        alarmLevel: "紧急",
        alarmMeaning: "光模块失配",
        reportObject: "模块",
        errorDescription: "单板光口上安装的光模块类型与单板期望的光模块类型不匹配",
        treatmentSuggestion: "按系统支持光模块列表，更换单板端口模块"
    },
    EQPT_FAILURE: {
        alarmName: "EQPT_FAILURE",
        alarmLevel: "紧急",
        alarmMeaning: "设备故障",
        reportObject: "单板、 模块、子模块",
        errorDescription: "单板、光模块、子模块故障，影响通信、管理或者业务",
        treatmentSuggestion: "拔插/更换单板；若告警始终不清除，重启主控；若告警仍不清除，联系供应商获得技术支持；"
    },
    EQPT_COM_FAIL: {
        alarmName: "EQPT_COM_FAIL",
        alarmLevel: "主要",
        alarmMeaning: "设备通讯异常告警",
        reportObject: "主控、单板、模块",
        errorDescription: "(1)模块硬件故障；(2)模块微程序损坏；(3)单板软件工作异常；(4)单板硬件故障。",
        treatmentSuggestion: "拔插/更换单板；若告警始终不清除，重启主控；若告警仍不清除，联系供应商获得技术支持；"
    },
    TRANSCEIVER_RATE_MISMATCH: {
        alarmName: "TRANSCEIVER_RATE_MISMATCH",
        alarmLevel: "主要",
        alarmMeaning: "模块速率不匹配告警",
        reportObject: "端口",
        errorDescription: "用户设置的单板速率模块不支持。",
        treatmentSuggestion: "检查光模块是否支持多速率"
    },
    EQPT_ABSENCE_WARNING: {
        alarmName: "EQPT_ABSENCE_WARNING",
        alarmLevel: "紧急",
        alarmMeaning: "设备不在位告警",
        reportObject: "单板、模块、子模块",
        errorDescription: "单板、模块、子模块不在位",
        treatmentSuggestion:
            "检查单板/模块是否合适地置位；拔插/更换单板或模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    ALS_ACTIVED: {
        alarmName: "ALS_ACTIVED",
        alarmLevel: "次要",
        alarmMeaning: "激光器自动关断",
        reportObject: "EDFA、模块",
        errorDescription: "触发了激光器自动关断功能",
        treatmentSuggestion:
            "对于EDFA APR或者电卡客户侧光模块ALS功能，触发激光器自动关断的条件消失之后，该告警会自动消失"
    },
    TCA_LASER_BIAS_CURRENT_L_15MIN: {
        alarmName: "TCA_LASER_BIAS_CURRENT_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "激光器偏置电流15分钟过低",
        reportObject: "EDFA、模块、OCH",
        errorDescription: "(1)激光器老化；(2)单板故障。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；若告警不清除，拔插/更换光模块；若告警始终不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LASER_BIAS_CURRENT_L_24H: {
        alarmName: "TCA_LASER_BIAS_CURRENT_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "激光器偏置电流24小时过低",
        reportObject: "EDFA、模块、OCH",
        errorDescription: "(1)激光器老化；(2)单板故障。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；若告警不清除，拔插/更换光模块；若告警始终不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LASER_BIAS_CURRENT_H_15MIN: {
        alarmName: "TCA_LASER_BIAS_CURRENT_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "激光器偏置电流15分钟过高",
        reportObject: "EDFA、模块、OCH",
        errorDescription: "(1)激光器老化；(2)单板故障。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；若告警不清除，拔插/更换光模块；若告警始终不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LASER_BIAS_CURRENT_H_24H: {
        alarmName: "TCA_LASER_BIAS_CURRENT_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "激光器偏置电流24小时过高",
        reportObject: "EDFA、模块、OCH",
        errorDescription: "(1)激光器老化；(2)单板故障。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；若告警不清除，拔插/更换光模块；若告警始终不清除，拔插单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    LASER_LIFE_WARNING: {
        alarmName: "LASER_LIFE_WARNING",
        alarmLevel: "紧急",
        alarmMeaning: "激光器寿命预告警",
        reportObject: "EDFA、模块、OCH、OSC",
        errorDescription: "(1)激光器老化；(2)单板的检测电路故障。",
        treatmentSuggestion:
            "拔插/更换光模块；若告警始终不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_BIAS_CURRENT_L_15MIN: {
        alarmName: "TCA_OA_PUMP_BIAS_CURRENT_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "泵浦激光器偏置电流15分钟过低",
        reportObject: "EDFA",
        errorDescription: "EDFA模块故障。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；若告警不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_BIAS_CURRENT_L_24H: {
        alarmName: "TCA_OA_PUMP_BIAS_CURRENT_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "泵浦激光器偏置电流24小时过低",
        reportObject: "EDFA",
        errorDescription: "EDFA模块故障。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；若告警不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_BIAS_CURRENT_H_15MIN: {
        alarmName: "TCA_OA_PUMP_BIAS_CURRENT_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "泵浦激光器偏置电流15分钟过高",
        reportObject: "EDFA",
        errorDescription: "EDFA模块故障。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；若告警不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_BIAS_CURRENT_H_24H: {
        alarmName: "TCA_OA_PUMP_BIAS_CURRENT_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "泵浦激光器偏置电流24小时过高",
        reportObject: "EDFA",
        errorDescription: "EDFA模块故障。",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；若告警不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_TEMP_L_15MIN: {
        alarmName: "TCA_OA_PUMP_TEMP_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "泵浦激光器工作温度15分钟过低",
        reportObject: "EDFA",
        errorDescription: "EDFA模块故障。",
        treatmentSuggestion:
            "检查单板或模块环境温度；检查TCA门限是否设置合理；检查是否存在风扇告警；拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_TEMP_L_24H: {
        alarmName: "TCA_OA_PUMP_TEMP_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "泵浦激光器工作温度24小时过低",
        reportObject: "EDFA",
        errorDescription: "EDFA模块故障。",
        treatmentSuggestion:
            "检查单板或模块环境温度；检查TCA门限是否设置合理；检查是否存在风扇告警；拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_TEMP_H_15MIN: {
        alarmName: "TCA_OA_PUMP_TEMP_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "泵浦激光器工作温度15分钟过高",
        reportObject: "EDFA",
        errorDescription: "EDFA模块故障。",
        treatmentSuggestion:
            "检查单板或模块环境温度；检查TCA门限是否设置合理；检查是否存在风扇告警；拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_TEMP_H_24H: {
        alarmName: "TCA_OA_PUMP_TEMP_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "泵浦激光器工作温度24小时过高",
        reportObject: "EDFA",
        errorDescription: "EDFA模块故障。",
        treatmentSuggestion:
            "检查单板或模块环境温度；检查TCA门限是否设置合理；检查是否存在风扇告警；拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_COOLING_CURRENT_L_15MIN: {
        alarmName: "TCA_OA_PUMP_COOLING_CURRENT_L_15MIN",
        alarmLevel: "紧急",
        alarmMeaning: "泵浦激光器制冷电流15分钟过低",
        reportObject: "EDFA",
        errorDescription: "(1)泵浦激光器内部温度过高(低)；(2)单板外部环境温度过高(低)；(3)单板内部电路故障。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_COOLING_CURRENT_L_24H: {
        alarmName: "TCA_OA_PUMP_COOLING_CURRENT_L_24H",
        alarmLevel: "紧急",
        alarmMeaning: "泵浦激光器制冷电流24小时过低",
        reportObject: "EDFA",
        errorDescription: "(1)泵浦激光器内部温度过高(低)；(2)单板外部环境温度过高(低)；(3)单板内部电路故障。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_COOLING_CURRENT_H_15MIN: {
        alarmName: "TCA_OA_PUMP_COOLING_CURRENT_H_15MIN",
        alarmLevel: "紧急",
        alarmMeaning: "泵浦激光器制冷电流15分钟过高",
        reportObject: "EDFA",
        errorDescription: "(1)泵浦激光器内部温度过高(低)；(2)单板外部环境温度过高(低)；(3)单板内部电路故障。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OA_PUMP_COOLING_CURRENT_H_24H: {
        alarmName: "TCA_OA_PUMP_COOLING_CURRENT_H_24H",
        alarmLevel: "紧急",
        alarmMeaning: "泵浦激光器制冷电流24小时过高",
        reportObject: "EDFA",
        errorDescription: "(1)泵浦激光器内部温度过高(低)；(2)单板外部环境温度过高(低)；(3)单板内部电路故障。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_HOUSING_TEMP_L_15MIN: {
        alarmName: "TCA_OPT_HOUSING_TEMP_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "模块外壳温度15分钟过限过低",
        reportObject: "模块",
        errorDescription: "模块外壳温度15分钟过限过低",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_HOUSING_TEMP_L_24H: {
        alarmName: "TCA_OPT_HOUSING_TEMP_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "模块外壳温度24小时过限过低",
        reportObject: "模块",
        errorDescription: "模块外壳温度24小时过限过低",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_HOUSING_TEMP_H_15MIN: {
        alarmName: "TCA_OPT_HOUSING_TEMP_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "模块外壳温度15分钟过限过高",
        reportObject: "模块",
        errorDescription: "模块外壳温度15分钟过限过高",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_HOUSING_TEMP_H_24H: {
        alarmName: "TCA_OPT_HOUSING_TEMP_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "模块外壳温度24小时过限过高",
        reportObject: "模块",
        errorDescription: "模块外壳温度24小时过限过高",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_SUPPLY_VOLTAGE_L_15MIN: {
        alarmName: "TCA_OPT_SUPPLY_VOLTAGE_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "供电电压15分钟过限过低",
        reportObject: "模块",
        errorDescription: "供电电压15分钟过限过低",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_SUPPLY_VOLTAGE_L_24H: {
        alarmName: "TCA_OPT_SUPPLY_VOLTAGE_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "供电电压24小时过限过低",
        reportObject: "模块",
        errorDescription: "供电电压24小时过限过低",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_SUPPLY_VOLTAGE_H_15MIN: {
        alarmName: "TCA_OPT_SUPPLY_VOLTAGE_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "供电电压15分钟过限过高",
        reportObject: "模块",
        errorDescription: "供电电压15分钟过限过高",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_SUPPLY_VOLTAGE_H_24H: {
        alarmName: "TCA_OPT_SUPPLY_VOLTAGE_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "供电电压24小时过限过高",
        reportObject: "模块",
        errorDescription: "供电电压24小时过限过高",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_TOTAL_OUTPUT_POWER_L_15MIN: {
        alarmName: "TCA_OPT_TOTAL_OUTPUT_POWER_L_15MIN",
        alarmLevel: "次要",
        alarmMeaning: "输出总光功率15分钟过限过低",
        reportObject: "模块",
        errorDescription: "输出总光功率15分钟过限过低",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查光纤连接是否正常，清洁光纤连接头；若告警仍不清除，拔插模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_TOTAL_OUTPUT_POWER_L_24H: {
        alarmName: "TCA_OPT_TOTAL_OUTPUT_POWER_L_24H",
        alarmLevel: "次要",
        alarmMeaning: "输出总光功率24小时过限过低",
        reportObject: "模块",
        errorDescription: "输出总光功率24小时过限过低",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查光纤连接是否正常，清洁光纤连接头；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_TOTAL_OUTPUT_POWER_H_15MIN: {
        alarmName: "TCA_OPT_TOTAL_OUTPUT_POWER_H_15MIN",
        alarmLevel: "次要",
        alarmMeaning: "输出总光功率15分钟过限过高",
        reportObject: "模块",
        errorDescription: "输出总光功率15分钟过限过高",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_TOTAL_OUTPUT_POWER_H_24H: {
        alarmName: "TCA_OPT_TOTAL_OUTPUT_POWER_H_24H",
        alarmLevel: "次要",
        alarmMeaning: "输出总光功率24小时过限过高",
        reportObject: "模块",
        errorDescription: "输出总光功率24小时过限过高",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；若告警仍不清除，拔插模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_TOTAL_INPUT_POWER_L_15MIN: {
        alarmName: "TCA_OPT_TOTAL_INPUT_POWER_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "输入总光功率15分钟过限过低",
        reportObject: "模块",
        errorDescription: "输入总光功率15分钟过限过低",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查光纤连接是否正常，清洁光纤连接头；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_TOTAL_INPUT_POWER_L_24H: {
        alarmName: "TCA_OPT_TOTAL_INPUT_POWER_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "输入总光功率24小时过限过低",
        reportObject: "模块",
        errorDescription: "输入总光功率24小时过限过低",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；检查光纤连接是否正常，清洁光纤连接头；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_TOTAL_INPUT_POWER_H_15MIN: {
        alarmName: "TCA_OPT_TOTAL_INPUT_POWER_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "输入总光功率15分钟过限过高",
        reportObject: "模块",
        errorDescription: "输入总光功率15分钟过限过高",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；若告警仍不清除，拔插模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_OPT_TOTAL_INPUT_POWER_H_24H: {
        alarmName: "TCA_OPT_TOTAL_INPUT_POWER_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "输入总光功率24小时过限过高",
        reportObject: "模块",
        errorDescription: "输入总光功率24小时过限过高",
        treatmentSuggestion:
            "在网管上点击“清除TCA”；检查TCA门限是否设置合理；检查对端发送功率和光模块是否合适；若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    LASER_DISABLED: {
        alarmName: "LASER_DISABLED",
        alarmLevel: "主要",
        alarmMeaning: "激光器关闭",
        reportObject: "EDFA、模块",
        errorDescription: "用户通过网管执行了关闭激光器操作。",
        treatmentSuggestion: "该告警用来提示用户执行了关闭激光器的操作，解除该操作后，告警消除"
    },
    EQPT_POWER_DOWN: {
        alarmName: "EQPT_POWER_DOWN",
        alarmLevel: "主要",
        alarmMeaning: "单板或子模块未上电",
        reportObject: "单板",
        errorDescription: "做下电配置后上报",
        treatmentSuggestion: "单板下电配置后会上报此告警，取消该配置后告警消除"
    },
    PWR_NO_INPUT: {
        alarmName: "PWR_NO_INPUT",
        alarmLevel: "紧急",
        alarmMeaning: "电源板无输入告警",
        reportObject: "电源板",
        errorDescription: "电源板无输入告警",
        treatmentSuggestion: "检查电源接入是否正常；拔插/更换电源；若告警仍不清除，联系供应商获得技术支持；"
    },
    PWR_NO_OUTPUT: {
        alarmName: "PWR_NO_OUTPUT",
        alarmLevel: "紧急",
        alarmMeaning: "电源板无输出告警",
        reportObject: "电源板",
        errorDescription: "电源板无输出告警",
        treatmentSuggestion: "检查电源接入是否正常；拔插/更换电源；若告警仍不清除，联系供应商获得技术支持；"
    },
    POWER_ABNORMAL: {
        alarmName: "POWER_ABNORMAL",
        alarmLevel: "主要",
        alarmMeaning: "电源失效",
        reportObject: "电源板",
        errorDescription: "(1)单板电源模块失效；(2)电源输入异常；(3)子架电源板故障。",
        treatmentSuggestion: "检查电源接入是否正常；拔插/更换电源；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_PWR_CURRENT_OUTPUT_H_15MIN: {
        alarmName: "TCA_PWR_CURRENT_OUTPUT_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "电源输出电流15分钟越高限",
        reportObject: "电源板",
        errorDescription: "电源输出电流越高限",
        treatmentSuggestion:
            "检查电源接入是否正常；检查TCA门限是否设置合理；拔插/更换电源；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_PWR_CURRENT_OUTPUT_H_24H: {
        alarmName: "TCA_PWR_CURRENT_OUTPUT_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "电源输出电流24小时越高限",
        reportObject: "电源板",
        errorDescription: "电源输出电流越高限",
        treatmentSuggestion:
            "检查电源接入是否正常；检查TCA门限是否设置合理；拔插/更换电源；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_PWR_CURRENT_OUTPUT_L_15MIN: {
        alarmName: "TCA_PWR_CURRENT_OUTPUT_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "电源输出电流15分钟越低限",
        reportObject: "电源板",
        errorDescription: "电源输出电流越低限",
        treatmentSuggestion:
            "检查电源接入是否正常；检查TCA门限是否设置合理；拔插/更换电源；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_PWR_CURRENT_OUTPUT_L_24H: {
        alarmName: "TCA_PWR_CURRENT_OUTPUT_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "电源输出电流24小时越低限",
        reportObject: "电源板",
        errorDescription: "电源输出电流越低限",
        treatmentSuggestion:
            "检查电源接入是否正常；检查TCA门限是否设置合理；拔插/更换电源；若告警仍不清除，联系供应商获得技术支持；"
    },
    FAN_SPEED_LOW: {
        alarmName: "FAN_SPEED_LOW",
        alarmLevel: "主要",
        alarmMeaning: "风扇实际转速与配置值偏离值越限",
        reportObject: "风扇板",
        errorDescription: "实际转速低于配置值20%(风扇全速的20%)以上上报",
        treatmentSuggestion: "检查是否存在风扇其它告警；拔插/更换风扇；若告警仍不清除，联系供应商获得技术支持；"
    },
    OCH_LOS_P: {
        alarmName: "OCH_LOS_P",
        alarmLevel: "紧急",
        alarmMeaning: "OCh 层净荷信号丢失",
        reportObject: "OCH",
        errorDescription: "单板OCH端口输入光功率低于其LOS门限。",
        treatmentSuggestion:
            "检查光纤连接是否正确；检查上游单板激光器输出光功率：若告警仍不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LASER_TEMP_L_15MIN: {
        alarmName: "TCA_LASER_TEMP_L_15MIN",
        alarmLevel: "紧急",
        alarmMeaning: "激光器温度15分钟过低",
        reportObject: "OCH",
        errorDescription: "模块激光器温度15分钟过限过低",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LASER_TEMP_L_24H: {
        alarmName: "TCA_LASER_TEMP_L_24H",
        alarmLevel: "紧急",
        alarmMeaning: "激光器温度24小时过低",
        reportObject: "OCH",
        errorDescription: "模块激光器温度24小时过限过低",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LASER_TEMP_H_15MIN: {
        alarmName: "TCA_LASER_TEMP_H_15MIN",
        alarmLevel: "紧急",
        alarmMeaning: "激光器温度15分钟过高",
        reportObject: "OCH",
        errorDescription: "模块激光器温度15分钟过限过高",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    TCA_LASER_TEMP_H_24H: {
        alarmName: "TCA_LASER_TEMP_H_24H",
        alarmLevel: "紧急",
        alarmMeaning: "激光器温度24小时过高",
        reportObject: "OCH",
        errorDescription: "模块激光器温度24小时过限过高",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；检查是否存在风扇告警；在网管上点击“清除TCA”；若告警不清除，拔插/更换模块；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODU2_PM_AIS: {
        alarmName: "ODU2_PM_AIS",
        alarmLevel: "提示",
        alarmMeaning: "ODU2 PM告警指示信号",
        reportObject: "ODU",
        errorDescription: "整个ODU2信号为全1(除FA OH、OTU1 OH和ODU2 FTFL)，检测STAT信息为111。",
        treatmentSuggestion:
            "检查相连接的设备端口或业务实体上是否有告警；检查光纤连接是否正常，清洁光纤连接头；热起单板；冷起单板；拔插/更换模块和单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODUFLEX_PM_AIS: {
        alarmName: "ODUFLEX_PM_AIS",
        alarmLevel: "提示",
        alarmMeaning: "ODUFLEX PM告警指示信号",
        reportObject: "ODU",
        errorDescription:
            "(1)客户侧原因：客户侧输入信号中包含ODUk_PM_AIS告警信号；(2)波分侧原因：对端站相应单板客户侧上报ODUFlex_PM_AIS、R_LOS、OTUk_LOF、OTUk_LOM、OTUk_TIM等告警，导致给本端下插AIS信号。",
        treatmentSuggestion:
            "检查相连接的设备端口或业务实体上是否有告警；检查光纤连接是否正常，清洁光纤连接头；热起单板；冷起单板；拔插/更换模块和单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODU4_PM_AIS: {
        alarmName: "ODU4_PM_AIS",
        alarmLevel: "提示",
        alarmMeaning: "ODU4 PM告警指示信号",
        reportObject: "ODU",
        errorDescription:
            "客户侧输入信号中包含ODUk_PM_AIS信号；对端相应单板发送了ODUk_PM_AIS信号；上游站点设置了环回或者交叉、FEC类型配置错误。",
        treatmentSuggestion:
            "检查相连接的设备端口或业务实体上是否有告警；检查光纤连接是否正常，清洁光纤连接头；热起单板；冷起单板；拔插/更换模块和单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODUCN_PM_AIS: {
        alarmName: "ODUCN_PM_AIS",
        alarmLevel: "提示",
        alarmMeaning: "ODUCn PM告警指示信号",
        reportObject: "ODU",
        errorDescription:
            "(1)客户侧输入信号中包含ODUCn_PM_AIS信号；(2)对端相应单板发送了ODUCn_PM_AIS信号；(3)上游站点设置了环回或者交叉、FEC类型配置错误。",
        treatmentSuggestion:
            "检查相连接的设备端口或业务实体上是否有告警；检查光纤连接是否正常，清洁光纤连接头；热起单板；冷起单板；拔插/更换模块和单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODU2_PM_BDI: {
        alarmName: "ODU2_PM_BDI",
        alarmLevel: "提示",
        alarmMeaning: "ODU2 PM后向缺陷指示",
        reportObject: "ODU",
        errorDescription: "PM开销域中BDI比特维持5帧为1则上报该告警。",
        treatmentSuggestion: "检查上游相关联ODU业务实体上是否有告警：LOS"
    },
    ODUFLEX_PM_BDI: {
        alarmName: "ODUFLEX_PM_BDI",
        alarmLevel: "提示",
        alarmMeaning: "ODUFLEX PM后向缺陷指示",
        reportObject: "ODU",
        errorDescription:
            "(1)客户侧原因：客户侧输入信号中包含ODUFlex_PM_BDI信号；(2)波分侧原因：对端站相应单板波分册产生ODUFlex_LOFLOM、ODUFlex_PM_AIS、ODUFlex_PM_LCK、ODUFlex_PM_OCI、ODUFlex_PM_TIM、ODUFlex_PM_SSF等告警，向本站回传ODUFlex_PM_BDI告警。",
        treatmentSuggestion: "检查上游相关联ODU业务实体上是否有告警：LOS"
    },
    ODU4_PM_BDI: {
        alarmName: "ODU4_PM_BDI",
        alarmLevel: "提示",
        alarmMeaning: "ODU4 PM后向缺陷指示",
        reportObject: "ODU",
        errorDescription: "客户侧输入信号中包含ODUk_PM_BDI信号；下游站对应OTU单板收到LOF，LOM，ODUk_PM_AIS告警。",
        treatmentSuggestion: "检查上游相关联ODU业务实体上是否有告警：LOS"
    },
    ODUCN_PM_BDI: {
        alarmName: "ODUCN_PM_BDI",
        alarmLevel: "提示",
        alarmMeaning: "ODUCn PM后向缺陷指示",
        reportObject: "ODU",
        errorDescription:
            "(1)客户侧输入信号中包含ODUCn_PM_BDI信号；(2)下游站对应OTU单板收到LOF，LOM，ODUCn_PM_AIS告警。",
        treatmentSuggestion: "检查上游相关联ODU业务实体上是否有告警：LOS"
    },
    ODU2_PM_DEG: {
        alarmName: "ODU2_PM_DEG",
        alarmLevel: "次要",
        alarmMeaning: "ODU2 PM信号劣化",
        reportObject: "ODU",
        errorDescription:
            "假定误码突发分布，故仅支持DEG，不支持EXC。如果检测到DEGM个连续的不良1s性能监视周期(1s周期内检测到的误码块数量或比例≥DEGTHR，则为不良1s周期)则上报该告警。",
        treatmentSuggestion:
            "检查接收光功率是否正常；检查光纤连接是否正常，清洁光纤连接头；热起/冷起单板；若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODUFLEX_PM_DEG: {
        alarmName: "ODUFLEX_PM_DEG",
        alarmLevel: "次要",
        alarmMeaning: "ODUFLEX PM信号劣化",
        reportObject: "ODU",
        errorDescription: "(1)单板输入光功率异常；(2)光纤传输线路异常；(3)上报此告警的单板或相应的单板故障。",
        treatmentSuggestion:
            "检查接收光功率是否正常；检查光纤连接是否正常，清洁光纤连接头；热起/冷起单板；若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODU4_PM_DEG: {
        alarmName: "ODU4_PM_DEG",
        alarmLevel: "次要",
        alarmMeaning: "ODU4 PM信号劣化",
        reportObject: "ODU",
        errorDescription: "本端光口故障；对端光口故障；光纤传输线路异常。",
        treatmentSuggestion:
            "检查接收光功率是否正常；检查光纤连接是否正常，清洁光纤连接头；热起/冷起单板；若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODUCN_PM_DEG: {
        alarmName: "ODUCN_PM_DEG",
        alarmLevel: "主要",
        alarmMeaning: "ODUCn PM段BIP8误码越限",
        reportObject: "ODU",
        errorDescription: "(1)接收信号衰减偏大，光纤传输线路异常；(2)对端站发送部分故障；(3)本站接收部分故障。",
        treatmentSuggestion:
            "检查接收光功率是否正常；检查光纤连接是否正常，清洁光纤连接头；热起/冷起单板；若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODU2_PM_SSF: {
        alarmName: "ODU2_PM_SSF",
        alarmLevel: "提示",
        alarmMeaning: "ODU2 PM服务层信号失效",
        reportObject: "ODU",
        errorDescription:
            "ODU2_PM_SSF告警由以下缺陷产生：(1)ODU2_PM_AIS；(2)在ODU2_TCMi宿使能且为操作模式下，有ODU2_TCMi_SSF、ODU2_TCMi_LCK、ODU2_TCMi_OCI、ODU2_TCMi_AIS、ODU2_TCMi_LTC且ODU2_TCMi_LTC后继行动使能、ODU2_TCMi_TIM且ODU2_TCMi_TIM后继使能；(3)OTU2_TIM，且OTU2_TIM后继行动使能；(4)OTU2_SSF。",
        treatmentSuggestion:
            "检查接收光功率是否正常；清洗光模块接头；热起/冷起单板；拔插/更换光模块；若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODUFLEX_PM_SSF: {
        alarmName: "ODUFLEX_PM_SSF",
        alarmLevel: "提示",
        alarmMeaning: "ODUFLEX PM服务层信号失效",
        reportObject: "ODU",
        errorDescription: "ODUFlex_PM_SSF告警由以下缺陷产生ODU0_PM_AIS。",
        treatmentSuggestion:
            "检查接收光功率是否正常；清洗光模块接头；热起/冷起单板；拔插/更换光模块；若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODU4_PM_SSF: {
        alarmName: "ODU4_PM_SSF",
        alarmLevel: "提示",
        alarmMeaning: "ODU4 PM服务层信号失效",
        reportObject: "ODU",
        errorDescription:
            "ODU4_PM_SSF告警由以下缺陷产生：1、ODU4_PM_AIS2、在ODU4_TCMi宿使能且为操作模式下，有ODU4_TCMi_SSF、ODU4_TCMi_LCK、ODU4_TCMi_OCI、ODU4_TCMi_AIS、ODU4_TCMi_LTC且ODU4_TCMi_LTC后继行动使能、ODU4_TCMi_TIM且ODU4_TCMi_TIM后继使能，3、OTU1_TIM，且OTU1_TIM后继行动使能4、OTU1_SSF",
        treatmentSuggestion:
            "检查接收光功率是否正常；清洗光模块接头；热起/冷起单板；拔插/更换光模块；若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODUCN_PM_SSF: {
        alarmName: "ODUCN_PM_SSF",
        alarmLevel: "提示",
        alarmMeaning: "ODUCn PM服务层信号失效",
        reportObject: "ODU",
        errorDescription:
            "ODUCn_PM_SSF告警由以下缺陷产生：(1)ODUCn_PM_AIS(2)OTUCn_TIM，且OTUCn_TIM后继行动使能(3)OTUCn_SSF",
        treatmentSuggestion:
            "检查接收光功率是否正常；清洗光模块接头；热起/冷起单板；拔插/更换光模块；若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODU2_LOFLOM: {
        alarmName: "ODU2_LOFLOM",
        alarmLevel: "紧急",
        alarmMeaning: "ODU2帧和复帧丢失",
        reportObject: "ODU",
        errorDescription: "如果帧定位处理过程持续帧失步(OOF)状态3ms，应发出LOFLOM告警。",
        treatmentSuggestion:
            "检查接收光功率是否正常；清洗光模块接头；热起/冷起单板；拔插/更换光模块；若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODUFLEX_LOFLOM: {
        alarmName: "ODUFLEX_LOFLOM",
        alarmLevel: "紧急",
        alarmMeaning: "ODUFLEX 帧和复帧丢失",
        reportObject: "ODU",
        errorDescription:
            "(1)对端对应单板发送的信号无帧结构； (2)输入光功率异常；(3)FEC类型设置错误；(4)本站OTU单板接收到的信号误码过多；(5)光纤传输线路异常。",
        treatmentSuggestion:
            "检查接收光功率是否正常；清洗光模块接头；热起/冷起单板；拔插/更换光模块；若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODU4_LOFLOM: {
        alarmName: "ODU4_LOFLOM",
        alarmLevel: "紧急",
        alarmMeaning: "ODU4帧和复帧丢失 ",
        reportObject: "ODU",
        errorDescription: "如果帧定位处理过程持续帧失步(OOF)状态3ms，应发出LOFLOM告警",
        treatmentSuggestion:
            "检查接收光功率是否正常；清洗光模块接头；热起/冷起单板；拔插/更换光模块；若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODU2_PM_LCK: {
        alarmName: "ODU2_PM_LCK",
        alarmLevel: "次要",
        alarmMeaning: "ODU2 PM信号锁定缺陷",
        reportObject: "ODU",
        errorDescription: "如果接受的STAT信息是101，应产生LCK。",
        treatmentSuggestion:
            "检查对端和本端是否admin down等锁定系统的配置常若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODUFLEX_PM_LCK: {
        alarmName: "ODUFLEX_PM_LCK",
        alarmLevel: "次要",
        alarmMeaning: "ODUFLEX PM信号锁定缺陷",
        reportObject: "ODU",
        errorDescription: "因测试当前的线路信号，而锁定信号。",
        treatmentSuggestion:
            "检查对端和本端是否admin down等锁定系统的配置常若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODU4_PM_LCK: {
        alarmName: "ODU4_PM_LCK",
        alarmLevel: "次要",
        alarmMeaning: "ODU4 PM信号锁定缺陷",
        reportObject: "ODU",
        errorDescription: "因测试当前的线路信号，而锁定信号。",
        treatmentSuggestion:
            "检查对端和本端是否admin down等锁定系统的配置常若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODUCN_PM_LCK: {
        alarmName: "ODUCN_PM_LCK",
        alarmLevel: "次要",
        alarmMeaning: "ODUCN PM信号锁定缺陷",
        reportObject: "ODU",
        errorDescription: "ODUCN PM信号锁定缺陷",
        treatmentSuggestion:
            "检查对端和本端是否admin down等锁定系统的配置常若告警仍不清除，拔插/更换单板；若告警仍不清除，联系供应商获得技术支持；"
    },
    ODU2_PM_OCI: {
        alarmName: "ODU2_PM_OCI",
        alarmLevel: "次要",
        alarmMeaning: "ODU2 PM断开连接指示",
        reportObject: "ODU",
        errorDescription:
            "ODUk PM段断开连接指示告警。ODUk PM段断开连接指示告警。当输出没有连接到输入，STAT字节的值是“110”，上报此告警。k表示级别号，其取值为1、2、5G。",
        treatmentSuggestion: "检查本端和对端的交叉配置拔插/更换板卡；联系供应商获得技术支持；"
    },
    ODUFLEX_PM_OCI: {
        alarmName: "ODUFLEX_PM_OCI",
        alarmLevel: "次要",
        alarmMeaning: "ODUFLEX PM断开连接指示",
        reportObject: "ODU",
        errorDescription:
            "(1)上游站点对应的单板存在ODUFlex_PM_OCI告警；(2)对端站对应单板设置了环回；(3)对端站对应单板没有配置交叉或配置不正确。",
        treatmentSuggestion: "检查本端和对端的交叉配置拔插/更换板卡；联系供应商获得技术支持；"
    },
    ODU4_PM_OCI: {
        alarmName: "ODU4_PM_OCI",
        alarmLevel: "次要",
        alarmMeaning: "ODU4 PM断开连接指示",
        reportObject: "ODU",
        errorDescription:
            "上游站点对应的单板存在ODUk_PM_OCI告警；对端站对应单板设置了环回；对端站对应单板没有配置交叉或配置不正确。",
        treatmentSuggestion: "检查本端和对端的交叉配置拔插/更换板卡；联系供应商获得技术支持；"
    },
    ODU2_PM_TIM: {
        alarmName: "ODU2_PM_TIM",
        alarmLevel: "次要",
        alarmMeaning: "ODU2 PM踪迹标识不匹配",
        reportObject: "ODU",
        errorDescription: "TTI的失配处理过程报告踪迹标识符失配告警。",
        treatmentSuggestion:
            "检查对端发送的TTI和本端期望的TTI是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    ODUFLEX_PM_TIM: {
        alarmName: "ODUFLEX_PM_TIM",
        alarmLevel: "次要",
        alarmMeaning: "ODUFLEX PM踪迹标识不匹配",
        reportObject: "ODU",
        errorDescription:
            "(1)组网结构和应配置的TIM检测模式不一致；(2)对端发送的TTI(Trail Trace Identifier)和本端应收的TTI不一致；(3)光纤连接错误；(4)交叉配置错误。",
        treatmentSuggestion:
            "检查对端发送的TTI和本端期望的TTI是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    ODU4_PM_TIM: {
        alarmName: "ODU4_PM_TIM",
        alarmLevel: "次要",
        alarmMeaning: "ODU4 PM踪迹标识不匹配",
        reportObject: "ODU",
        errorDescription:
            "组网结构和应配置的TIM检测模式不一致；对端发送的TTI(Trail Trace Identifier)和本端应收的TTI不一致；光纤连接错误；交叉配置错误。",
        treatmentSuggestion:
            "检查对端发送的TTI和本端期望的TTI是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    ODUCN_PM_TIM: {
        alarmName: "ODUCN_PM_TIM",
        alarmLevel: "次要",
        alarmMeaning: "ODUCn PM踪迹标识不匹配",
        reportObject: "ODU",
        errorDescription:
            "(1)组网结构和应配置的TIM检测模式不一致；(2)对端发送的TTI(Trail Trace Identifier)和本端应收的TTI不一致；(3)光纤连接错误；(4)交叉配置错误。",
        treatmentSuggestion:
            "检查对端发送的TTI和本端期望的TTI是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    OPU2_CSF: {
        alarmName: "OPU2_CSF",
        alarmLevel: "次要",
        alarmMeaning: "客户层信号失效",
        reportObject: "OPU",
        errorDescription: "远端客户层信号失效，系统通过OPU开销下插到本端上报告警，指示远端客户层信号失效。",
        treatmentSuggestion: "检查对端设备工作状态拔插/更换板卡；联系供应商获得技术支持；"
    },
    OPU4_CSF: {
        alarmName: "OPU4_CSF",
        alarmLevel: "次要",
        alarmMeaning: "客户层信号失效",
        reportObject: "OPU",
        errorDescription: "客户层信号失效",
        treatmentSuggestion: "检查对端设备工作状态拔插/更换板卡；联系供应商获得技术支持；"
    },
    OPUFLEX_CSF: {
        alarmName: "OPUFLEX_CSF",
        alarmLevel: "次要",
        alarmMeaning: "客户层信号失效",
        reportObject: "OPU",
        errorDescription: "远端客户层信号失效，系统通过OPU开销下插到本端上报告警，指示远端客户层信号失效。",
        treatmentSuggestion: "检查对端设备工作状态拔插/更换板卡；联系供应商获得技术支持；"
    },
    OPU2_MSIM: {
        alarmName: "OPU2_MSIM",
        alarmLevel: "次要",
        alarmMeaning: "OPU2复用结构标识符失配",
        reportObject: "OPU",
        errorDescription:
            "(1)支持并激活复用结构的自动配置时，如果特定适配功能不支持实收MSI的无效值时声明MSIM缺陷；(2)不支持或未激活复用结构的自动配置时，如果实收MSI不等于应收MSI时声明MSIM缺陷。",
        treatmentSuggestion:
            "检查对端和本端期望的业务配置类型是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    OPU4_MSIM: {
        alarmName: "OPU4_MSIM",
        alarmLevel: "次要",
        alarmMeaning: "OPU4复用结构标识符失配",
        reportObject: "OPU",
        errorDescription: "单板不支持源网元单板发送业务的复用结构。",
        treatmentSuggestion:
            "检查对端和本端期望的业务配置类型是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    OPUCN_MSIM: {
        alarmName: "OPUCN_MSIM",
        alarmLevel: "次要",
        alarmMeaning: "OPUCn复用结构标识符失配",
        reportObject: "OPU",
        errorDescription: "单板不支持源网元单板发送业务的复用结构。",
        treatmentSuggestion:
            "检查对端和本端期望的业务配置类型是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    OPU2_PT_MISMATCH: {
        alarmName: "OPU2_PT_MISMATCH",
        alarmLevel: "次要",
        alarmMeaning: "OPU2净荷类型PT失配",
        reportObject: "OPU",
        errorDescription: "如果接受的净荷类型不等于适配功能定义的净荷期望类型，应发出PLM告警。",
        treatmentSuggestion: "检查对端发送的净荷类型和本端期望的是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OPU4_PT_MISMATCH: {
        alarmName: "OPU4_PT_MISMATCH",
        alarmLevel: "次要",
        alarmMeaning: "OPU4净荷类型PT失配",
        reportObject: "OPU",
        errorDescription: "本网元OPU层应收PT字节设置与OPU层实收的PT字节不一致。",
        treatmentSuggestion: "检查对端发送的净荷类型和本端期望的是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OPUCN_PT_MISMATCH: {
        alarmName: "OPUCN_PT_MISMATCH",
        alarmLevel: "次要",
        alarmMeaning: "OPUCn净荷类型PT失配",
        reportObject: "OPU",
        errorDescription: "本网元OPU层应收PT字节设置与OPU层实收的PT字节不一致。",
        treatmentSuggestion: "检查对端发送的净荷类型和本端期望的是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OPUFLEX_PT_MISMATCH: {
        alarmName: "OPUFLEX_PT_MISMATCH",
        alarmLevel: "次要",
        alarmMeaning: "OPUFlex层净荷类型PT失配",
        reportObject: "OPU",
        errorDescription: "本网元OPU层应收PT字节设置与OPU层实收的PT字节不一致。",
        treatmentSuggestion: "检查对端发送的净荷类型和本端期望的是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    BIP_ERROR: {
        alarmName: "BIP_ERROR",
        alarmLevel: "次要",
        alarmMeaning: "PCS-BIP过高",
        reportObject: "ETH",
        errorDescription: "(1)接收信号衰减偏大，光纤传输线路异常；(2)对端站发送部分故障；(3)本站接收部分故障。",
        treatmentSuggestion:
            "检查接收光功率；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU2_AIS: {
        alarmName: "OTU2_AIS",
        alarmLevel: "提示",
        alarmMeaning: "OTU2告警指示信号",
        reportObject: "OTU",
        errorDescription:
            "客户侧输入信号中包含ODUk_PM_AIS、OTUk_AIS信号；上游站点设置了环回或者交叉、FEC类型配置错误。",
        treatmentSuggestion: "检查 FEC 类型配置拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU4_AIS: {
        alarmName: "OTU4_AIS",
        alarmLevel: "提示",
        alarmMeaning: "OTU4告警指示信号",
        reportObject: "OTU",
        errorDescription:
            "客户侧输入信号中包含ODUk_PM_AIS、OTUk_AIS信号；上游站点设置了环回或者交叉、FEC类型配置错误。",
        treatmentSuggestion: "检查 FEC 类型配置拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU2_BDI: {
        alarmName: "OTU2_BDI",
        alarmLevel: "提示",
        alarmMeaning: "OTU2后向缺陷指示",
        reportObject: "OTU",
        errorDescription:
            "客户侧输入信号中包含ODUk_PM_BDI、ODUk_TCMn_BDI告警；下游站对应OTU单板收到LOF，LOM，ODUk_PM_AIS、ODUk_TCMn_AIS告警。",
        treatmentSuggestion: "检查本端发送光功率检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU4_BDI: {
        alarmName: "OTU4_BDI",
        alarmLevel: "提示",
        alarmMeaning: "OTU4后向缺陷指示",
        reportObject: "OTU",
        errorDescription:
            "客户侧输入信号中包含ODUk_PM_BDI、ODUk_TCMn_BDI告警；下游站对应OTU单板收到LOF，LOM，ODUk_PM_AIS、ODUk_TCMn_AIS告警。",
        treatmentSuggestion: "检查本端发送光功率检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTUCN_BDI: {
        alarmName: "OTUCN_BDI",
        alarmLevel: "提示",
        alarmMeaning: "OTUCn后向缺陷指示",
        reportObject: "OTU",
        errorDescription: "客户侧输入信号中包含ODUCn_PM_BDI；下游站对应OTU单板收到LOF，LOM，OTUCn_TIM",
        treatmentSuggestion: "检查本端发送光功率检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU2_DEG: {
        alarmName: "OTU2_DEG",
        alarmLevel: "次要",
        alarmMeaning: "OTU2信号劣化",
        reportObject: "OTU",
        errorDescription: "本端光口故障；光纤传输线路异常。",
        treatmentSuggestion: "检查接收光功率清洗光模块接头拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU4_DEG: {
        alarmName: "OTU4_DEG",
        alarmLevel: "次要",
        alarmMeaning: "OTU4信号劣化",
        reportObject: "OTU",
        errorDescription: "本端光口故障；光纤传输线路异常。",
        treatmentSuggestion: "检查接收光功率清洗光模块接头拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTUCN_DEG: {
        alarmName: "OTUCN_DEG",
        alarmLevel: "次要",
        alarmMeaning: "OTUCn信号劣化",
        reportObject: "OTU",
        errorDescription: "本端光口故障；光纤传输线路异常。",
        treatmentSuggestion: "检查接收光功率清洗光模块接头拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU2_LOF: {
        alarmName: "OTU2_LOF",
        alarmLevel: "紧急",
        alarmMeaning: "OTU2帧丢失",
        reportObject: "OTU",
        errorDescription: "对端对应单板发送的信号无帧结构； 输入光功率异常； FEC类型设置错误； 光纤传输线路异常。",
        treatmentSuggestion: "检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU4_LOF: {
        alarmName: "OTU4_LOF",
        alarmLevel: "紧急",
        alarmMeaning: "OTU4帧丢失",
        reportObject: "OTU",
        errorDescription: "对端对应单板发送的信号无帧结构； 输入光功率异常； FEC类型设置错误； 光纤传输线路异常。",
        treatmentSuggestion: "检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTUCN_LOF: {
        alarmName: "OTUCN_LOF",
        alarmLevel: "紧急",
        alarmMeaning: "OTUCn帧丢失",
        reportObject: "OTU",
        errorDescription: "对端对应单板发送的信号无帧结构； 输入光功率异常； FEC类型设置错误； 光纤传输线路异常。",
        treatmentSuggestion: "检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU2_LOM: {
        alarmName: "OTU2_LOM",
        alarmLevel: "主要",
        alarmMeaning: "OTU2复帧丢失",
        reportObject: "OTU",
        errorDescription: "FEC类型设置错误； 本站OTU单板接收到的信号误码过多； 本站单板故障。",
        treatmentSuggestion: "检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU4_LOM: {
        alarmName: "OTU4_LOM",
        alarmLevel: "主要",
        alarmMeaning: "OTU4复帧丢失",
        reportObject: "OTU",
        errorDescription: "FEC类型设置错误； 本站OTU单板接收到的信号误码过多； 本站单板故障。",
        treatmentSuggestion: "检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTUCN_LOM: {
        alarmName: "OTUCN_LOM",
        alarmLevel: "主要",
        alarmMeaning: "OTUCn复帧丢失",
        reportObject: "OTU",
        errorDescription: "FEC类型设置错误； 本站OTU单板接收到的信号误码过多； 本站单板故障。",
        treatmentSuggestion: "检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU2_SSF: {
        alarmName: "OTU2_SSF",
        alarmLevel: "提示",
        alarmMeaning: "OTU2服务层信号失效",
        reportObject: "OTU",
        errorDescription: "如果存在LOS/OTU2_AIS/OTU2_LOF/OTU2_LOM告警，则产生OTU2_SSF告警",
        treatmentSuggestion: "检查接收光功率检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU4_SSF: {
        alarmName: "OTU4_SSF",
        alarmLevel: "提示",
        alarmMeaning: "OTU4服务层信号失效",
        reportObject: "OTU",
        errorDescription: "如果存在LOS/OTU4_AIS/OTU4_LOF/OTU4_LOM告警，则产生OTU4_SSF告警",
        treatmentSuggestion: "检查接收光功率检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTUCN_SSF: {
        alarmName: "OTUCN_SSF",
        alarmLevel: "提示",
        alarmMeaning: "OTUCn服务层信号失效",
        reportObject: "OTU",
        errorDescription: "如果存在LOS/OTUCn_LOF/OTUCn_LOM告警，则产生OTUCn_SSF告警",
        treatmentSuggestion: "检查接收光功率检查本端和对端配置是否一致拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU2_TIM: {
        alarmName: "OTU2_TIM",
        alarmLevel: "次要",
        alarmMeaning: "OTU2踪迹标识不匹配",
        reportObject: "OTU",
        errorDescription: "TTI的失配处理过程报告踪迹标识符失配告警。",
        treatmentSuggestion:
            "检查对端发送的TTI和本端期望的TTI是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTU4_TIM: {
        alarmName: "OTU4_TIM",
        alarmLevel: "次要",
        alarmMeaning: "OTU4踪迹标识不匹配",
        reportObject: "OTU",
        errorDescription:
            "组网结构和应配置的TIM检测模式不一致；对端发送的TTI(Trail Trace Identifier)和本端应收的TTI不一致；光纤连接错误；交叉配置错误。",
        treatmentSuggestion:
            "检查对端发送的TTI和本端期望的TTI是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    OTUCN_TIM: {
        alarmName: "OTUCN_TIM",
        alarmLevel: "次要",
        alarmMeaning: "OTUCn踪迹标识不匹配",
        reportObject: "OTU",
        errorDescription:
            "组网结构和应配置的TIM检测模式不一致；对端发送的TTI(Trail Trace Identifier)和本端应收的TTI不一致；光纤连接错误；交叉配置错误。",
        treatmentSuggestion:
            "检查对端发送的TTI和本端期望的TTI是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    B1_EXC: {
        alarmName: "B1_EXC",
        alarmLevel: "次要",
        alarmMeaning: "再生段(B1)误码越限",
        reportObject: "SDH",
        errorDescription:
            "(1)接收信号衰减偏大；(2)光纤头不清洁；(3)光纤连接器松动或未插好；(4)光衰减器的衰减值过大或过小；(5)对端站发送部分故障；(6)本站接收部分故障。",
        treatmentSuggestion:
            "检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_FEC_PRE_H_15MIN: {
        alarmName: "TCA_FEC_PRE_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "纠错前FEC误码率15分钟过限过高",
        reportObject: "OTU",
        errorDescription:
            "(1)单板输入光功率过高或者过低；(2)系统性能劣化，例如OSNR过低、色散、非线性等；(3)对端站单板故障；(4)本站单板故障；注意：操作者需立刻关注。虽然当前没有业务影响，但是业务长期稳定运行存在隐患。需要尽快进行业务调测。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_FEC_PRE_H_24H: {
        alarmName: "TCA_FEC_PRE_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "纠错前FEC误码率24小时过限过高",
        reportObject: "OTU",
        errorDescription:
            "(1)单板输入光功率过高或者过低；(2)系统性能劣化，例如OSNR过低、色散、非线性等；(3)对端站单板故障；(4)本站单板故障；注意：操作者需立刻关注。虽然当前没有业务影响，但是业务长期稳定运行存在隐患。需要尽快进行业务调测。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_FEC_POST_H_15MIN: {
        alarmName: "TCA_FEC_POST_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "纠错后FEC误码率15分钟过限过高",
        reportObject: "OTU",
        errorDescription:
            "(1)单板输入光功率过高或者过低；(2)系统性能劣化，例如OSNR过低、色散、非线性等；(3)对端站单板故障；(4)本站单板故障；",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_FEC_POST_H_24H: {
        alarmName: "TCA_FEC_POST_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "纠错后FEC误码率24小时过限过高",
        reportObject: "OTU",
        errorDescription:
            "(1)单板输入光功率过高或者过低；(2)系统性能劣化，例如OSNR过低、色散、非线性等；(3)对端站单板故障；(4)本站单板故障；",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_ETH_CRC_Tx_ERR_H_15MIN: {
        alarmName: "TCA_ETH_CRC_Tx_ERR_H_15MIN",
        alarmLevel: "次要",
        alarmMeaning: "发送的CRC错包15分钟越越限过高",
        reportObject: "ETH",
        errorDescription: "(1)远端站发送部分故障 (2)本站发送部分故障。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_ETH_CRC_Tx_ERR_H_24H: {
        alarmName: "TCA_ETH_CRC_Tx_ERR_H_24H",
        alarmLevel: "次要",
        alarmMeaning: "发送的CRC错包24小时越限过过高",
        reportObject: "ETH",
        errorDescription: "(1)远端站发送部分故障 (2)本站发送部分故障。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_ETH_CRC_Rx_ERR_H_15MIN: {
        alarmName: "TCA_ETH_CRC_Rx_ERR_H_15MIN",
        alarmLevel: "次要",
        alarmMeaning: "接受的CRC错包15分钟越越限过高",
        reportObject: "ETH",
        errorDescription: "(1)接收信号衰减偏大，光纤传输线路异常；(2)对端站发送部分故障；(3)本站接收部分故障。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_ETH_CRC_Rx_ERR_H_24H: {
        alarmName: "TCA_ETH_CRC_Rx_ERR_H_24H",
        alarmLevel: "次要",
        alarmMeaning: "接收的CRC错包24小时越限过过高",
        reportObject: "ETH",
        errorDescription: "(1)接收信号衰减偏大，光纤传输线路异常；(2)对端站发送部分故障；(3)本站接收部分故障。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    "TCA_ETH_PCS-BIP_Tx_ERR_H_15MIN": {
        alarmName: "TCA_ETH_PCS-BIP_Tx_ERR_H_15MIN",
        alarmLevel: "次要",
        alarmMeaning: "发送PCS-BIP错误15分钟过高",
        reportObject: "ETH",
        errorDescription: "(1)远端站发送部分故障 (2)本站发送部分故障。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    "TCA_ETH_PCS-BIP_Tx_ERR_H_24H": {
        alarmName: "TCA_ETH_PCS-BIP_Tx_ERR_H_24H",
        alarmLevel: "次要",
        alarmMeaning: "发送PCS-BIP错误24小时过高",
        reportObject: "ETH",
        errorDescription: "(1)远端站发送部分故障 (2)本站发送部分故障。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    "TCA_ETH_PCS-BIP_Rx_ERR_H_15MIN": {
        alarmName: "TCA_ETH_PCS-BIP_Rx_ERR_H_15MIN",
        alarmLevel: "次要",
        alarmMeaning: "接收PCS-BIP错误15分钟过高",
        reportObject: "ETH",
        errorDescription:
            "(1)接收信号衰减偏大，光纤传输线路异常；(2)对端站发送部分故障；(3)本站接收部分故障。40GE/100GE 误码超过设定门限时上报此告警。当误码类型为突发模式时，误码越限只产生ETH_BIP8_SD告警，不产生此告警",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    "TCA_ETH_PCS-BIP_Rx_ERR_H_24H": {
        alarmName: "TCA_ETH_PCS-BIP_Rx_ERR_H_24H",
        alarmLevel: "次要",
        alarmMeaning: "接收PCS-BIP错误24小时过高",
        reportObject: "ETH",
        errorDescription:
            "(1)接收信号衰减偏大，光纤传输线路异常；(2)对端站发送部分故障；(3)本站接收部分故障。40GE/100GE 误码超过设定门限时上报此告警。当误码类型为突发模式时，误码越限只产生ETH_BIP8_SD告警，不产生此告警",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；检查接收光功率若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    J0_MISMATCH: {
        alarmName: "J0_MISMATCH",
        alarmLevel: "次要",
        alarmMeaning: "J0失配",
        reportObject: "SDH",
        errorDescription: "(1)对端应发J0字节与本端应收J0字节不一致；(2)业务连接错误。",
        treatmentSuggestion:
            "检查对端发送的J0和本端期望的J0是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    ETH_LOS: {
        alarmName: "ETH_LOS",
        alarmLevel: "紧急",
        alarmMeaning: "以太网物理端口信号丢失告警",
        reportObject: "ETH",
        errorDescription: "(1)本端设备接收异常",
        treatmentSuggestion: "检查接收光功率清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    ETH_LOS_SYN: {
        alarmName: "ETH_LOS_SYN",
        alarmLevel: "紧急",
        alarmMeaning: "同步丢失告警",
        reportObject: "ETH",
        errorDescription: "(1)对端设备发送的信号类型或者速率与本站配置的不一致；(2)链路故障；(3)对端设备故障。",
        treatmentSuggestion:
            "检查对端和本端的业务配置是否一致检查对端设备工作状态拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    LINK_LF: {
        alarmName: "LINK_LF",
        alarmLevel: "次要",
        alarmMeaning: "连接本地失效告警",
        reportObject: "ETH",
        errorDescription: "以太网近端缺陷指示",
        treatmentSuggestion: "检查对端设备工作状态拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    MS_AIS: {
        alarmName: "MS_AIS",
        alarmLevel: "主要",
        alarmMeaning: "复用段告警指示",
        reportObject: "SDH",
        errorDescription:
            "(1)系统中存在更高级的告警，如R_LOS、R_LOF；(2)对端站主备交叉时钟板均不在位；(3)对端站交叉时钟板故障；(4)对端站发送部分故障；(5)本板接收部分故障。",
        treatmentSuggestion:
            "检查对端设备工作状态检查对端和本端业务配置是否一致检查接收光功率拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    MS_RDI: {
        alarmName: "MS_RDI",
        alarmLevel: "次要",
        alarmMeaning: "复用段远端接收失效指示",
        reportObject: "SDH",
        errorDescription:
            "(1)对端站接收到R_LOS、R_LOC、R_LOF、MS_AIS、B2_EXC和B2_SD信号；(2)对端站接收部分故障；(3)本站发送部分故障。",
        treatmentSuggestion:
            "检查对端和本端业务配置是否一致检查发送光功率拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    RS_LOF: {
        alarmName: "RS_LOF",
        alarmLevel: "紧急",
        alarmMeaning: "RS帧丢失告警",
        reportObject: "SDH",
        errorDescription:
            "(1)光纤错连；(2)接收光功率异常；(3)对端站发送信号无帧结构；(4)本站接收方向故障；(5)上游断纤，接收信号为噪声；(6)上游单板激光器关闭，接收信号为噪声。",
        treatmentSuggestion:
            "检查对端设备工作状态检查对端和本端业务配置是否一致拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    LINK_RF: {
        alarmName: "LINK_RF",
        alarmLevel: "次要",
        alarmMeaning: "连接远端失效告警",
        reportObject: "ETH",
        errorDescription: "以太网远端缺陷指示",
        treatmentSuggestion:
            "检查对端和本端业务配置是否一致检查发送光功率拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_Q_VALUE_L_15MIN: {
        alarmName: "TCA_Q_VALUE_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "Q值15分钟越限过低",
        reportObject: "OTU",
        errorDescription: "FEC纠前误码过限导致Q值越限。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，检查接收光功率清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_Q_VALUE_L_24H: {
        alarmName: "TCA_Q_VALUE_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "Q值24小时越限过低",
        reportObject: "OTU",
        errorDescription: "FEC纠前误码过限导致Q值越限。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，检查接收光功率清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_OSNR_VALUE_L_15MIN: {
        alarmName: "TCA_OSNR_VALUE_L_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "OSNR15分钟越限过低",
        reportObject: "OTU",
        errorDescription: "FEC纠前误码过限导致OSNR越限。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，检查接收光功率清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_OSNR_VALUE_L_24H: {
        alarmName: "TCA_OSNR_VALUE_L_24H",
        alarmLevel: "主要",
        alarmMeaning: "OSNR24小时越限过低",
        reportObject: "OTU",
        errorDescription: "FEC纠前误码过限导致OSNR越限。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，检查接收光功率清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_RS_H_15MIN: {
        alarmName: "TCA_RS_H_15MIN",
        alarmLevel: "次要",
        alarmMeaning: "再生段性能15分钟越限过高",
        reportObject: "SDH",
        errorDescription:
            "(1)对端站激光器性能劣化；(2)本站接收光功率过高或过低；(3)对端站时钟性能劣化；(4)光纤性能劣化。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，检查接收光功率清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_RS_H_24H: {
        alarmName: "TCA_RS_H_24H",
        alarmLevel: "次要",
        alarmMeaning: "再生段性能24小时越限过高",
        reportObject: "SDH",
        errorDescription:
            "(1)对端站激光器性能劣化；(2)本站接收光功率过高或过低；(3)对端站时钟性能劣化；(4)光纤性能劣化。",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，检查接收光功率清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    RS_DEG: {
        alarmName: "RS_DEG",
        alarmLevel: "主要",
        alarmMeaning: "再生段DEG告警",
        reportObject: "SDH",
        errorDescription:
            "(1)接收信号衰减偏大；(2)光纤头不清洁；(3)光纤连接器松动或未插好；(4)光衰减器的衰减值过大或过小；(5)对端站发送部分故障；(6)本站接收部分故障。",
        treatmentSuggestion: "检查接收光功率清洗光纤连接头拔插/更换模块拔插/更换板卡联系供应商获得技术支持"
    },
    TCA_ETH_IN_PKT_ERR_H_15MIN: {
        alarmName: "TCA_ETH_IN_PKT_ERR_H_15MIN",
        alarmLevel: "次要",
        alarmMeaning: "接收坏包越限15分钟过高告警",
        reportObject: "ETH",
        errorDescription: "接收坏包越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_ETH_IN_PKT_ERR_H_24H: {
        alarmName: "TCA_ETH_IN_PKT_ERR_H_24H",
        alarmLevel: "次要",
        alarmMeaning: "接收坏包越限24小时过高告警",
        reportObject: "ETH",
        errorDescription: "接收坏包越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_BBE_H_15MIN: {
        alarmName: "TCA_BBE_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "背景块误码15分钟越限告警",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "背景块误码越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_BBE_H_24H: {
        alarmName: "TCA_BBE_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "背景块误码24小时越限告警",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "背景块误码越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_ES_H_15MIN: {
        alarmName: "TCA_ES_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "误码秒15分钟越限告警",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "误码秒越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_ES_H_24H: {
        alarmName: "TCA_ES_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "误码秒24小时越限告警",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "误码秒越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_SES_H_15MIN: {
        alarmName: "TCA_SES_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "严重误码秒15分钟越限告警",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "严重误码秒越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_SES_H_24H: {
        alarmName: "TCA_SES_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "严重误码秒24小时越限告警",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "严重误码秒越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_SESR_H_15MIN: {
        alarmName: "TCA_SESR_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "严重误码秒比15分钟越限告警",
        reportObject: "ODU、OTU",
        errorDescription: "严重误码秒比越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_SESR_H_24H: {
        alarmName: "TCA_SESR_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "严重误码秒比24小时越限告警",
        reportObject: "ODU、OTU",
        errorDescription: "严重误码秒比越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_UAS_H_15MIN: {
        alarmName: "TCA_UAS_H_15MIN",
        alarmLevel: "主要",
        alarmMeaning: "不可用秒15分钟越限告警",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "不可用秒越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    },
    TCA_UAS_H_24H: {
        alarmName: "TCA_UAS_H_24H",
        alarmLevel: "主要",
        alarmMeaning: "不可用秒24小时越限告警",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "不可用秒越限",
        treatmentSuggestion:
            "检查TCA门限是否设置合理；在网管上点击“清除TCA”；若告警未清除，清洗光纤连接头；拔插/更换模块；拔插/更换板卡；联系供应商获得技术支持；"
    }
};

export const AlarmFaultHandlingEN = {
    MCU_SYNC_FAIL: {
        alarmName: "MCU_SYNC_FAIL",
        alarmLevel: "secondary",
        alarmMeaning: "Primary and secondary synchronization failed",
        reportObject: "Main master",
        errorDescription:
            "The software version between the main main control board and the backup main control board is inconsistent;",
        treatmentSuggestion:
            "After the system upgrade operation is completed, the versions of the active and standby masters will be consistent. If the main control is replaced later, if the version is inconsistent with the main main control, an alarm will be reported. You need to upgrade the main control again and synchronize the main and backup versions."
    },
    SWVERSION_MISMATCH: {
        alarmName: "SWVERSION_MISMATCH",
        alarmLevel: "hint",
        alarmMeaning: "Board version mismatch",
        reportObject: "veneer",
        errorDescription: "The board version is inconsistent with the expected version of the main control",
        treatmentSuggestion: "Upgrade boards"
    },
    EQPT_FILE_NOT_ACTIVE: {
        alarmName: "EQPT_FILE_NOT_ACTIVE",
        alarmLevel: "main",
        alarmMeaning: "Board file is not activated",
        reportObject: "veneer",
        errorDescription: "The board software has been delivered, but the activation command has not been delivered.",
        treatmentSuggestion: "Activate board"
    },
    FIRMWARE_NEED_UPDATE: {
        alarmName: "FIRMWARE_NEED_UPDATE",
        alarmLevel: "secondary",
        alarmMeaning: "Board firmware needs to be upgraded",
        reportObject: "veneer",
        errorDescription: "Board firmware needs to be upgraded",
        treatmentSuggestion: "Wait for the board firmware upgrade to be completed"
    },
    FIRMWARE_UPDATING: {
        alarmName: "FIRMWARE_UPDATING",
        alarmLevel: "secondary",
        alarmMeaning: "The board firmware is being upgraded",
        reportObject: "veneer",
        errorDescription: "The board firmware is being upgraded",
        treatmentSuggestion: "Wait for the board firmware upgrade to be completed"
    },
    FLASH_OVER: {
        alarmName: "FLASH_OVER",
        alarmLevel: "urgent",
        alarmMeaning: "Insufficient space in Flash file system",
        reportObject: "Main control and other supporting boards",
        errorDescription: "Report an alarm when the SD card file system space usage exceeds 85%",
        treatmentSuggestion:
            "Restart the main control; if the alarm still does not clear, contact the supplier for technical support;"
    },
    MEM_OVER: {
        alarmName: "MEM_OVER",
        alarmLevel: "urgent",
        alarmMeaning: "Insufficient memory file system space",
        reportObject: "Main control and other supporting boards",
        errorDescription: "Report an alarm when the memory file system space usage exceeds 85%",
        treatmentSuggestion:
            "Restart the main control; if the alarm still does not clear, contact the supplier for technical support;"
    },
    PASSWORD_NEED_CHANGE: {
        alarmName: "PASSWORD_NEED_CHANGE",
        alarmLevel: "main",
        alarmMeaning: "The default user password needs to be changed",
        reportObject: "Network element",
        errorDescription:
            "The password of the default user has not been changed, which poses a security risk and needs to be changed.",
        treatmentSuggestion: "Contact the supplier for technical support;"
    },
    REL_POWER_DIFF_OVER: {
        alarmName: "REL_POWER_DIFF_OVER",
        alarmLevel: "main",
        alarmMeaning: "The relative optical power of the main and backup channel signals exceeds the limit.",
        reportObject: "APS",
        errorDescription:
            "(1) The attenuation of the main channel or the backup channel is too large, resulting in an excessive difference in optical power at the receiving end, exceeding the 3dB threshold; (2) The difference in transmit optical power between the main and backup channels at the upstream site is too large, and the optical power difference at the receiving end exceeds 3dB. The threshold is exceeded; (3) The single board detection circuit is faulty.",
        treatmentSuggestion: "Check the received optical power of the main and backup channels"
    },
    APS_POWER_OVER: {
        alarmName: "APS_POWER_OVER",
        alarmLevel: "main",
        alarmMeaning: "The absolute optical power of the main and backup channel signals exceeds the limit.",
        reportObject: "APS",
        errorDescription:
            "(1) The attenuation of the main channel or the backup channel is too large, resulting in the received light power being too low, lower than the over-limit threshold;",
        treatmentSuggestion: "Check the received optical power of the main and backup channels"
    },
    APS_STATUS_INDI: {
        alarmName: "APS_STATUS_INDI",
        alarmLevel: "main",
        alarmMeaning: "APS switching instructions",
        reportObject: "APS",
        errorDescription: "The protection group is in switching state",
        treatmentSuggestion: "Check the main channel received optical power"
    },
    APS_SF_INDI: {
        alarmName: "APS_SF_INDI",
        alarmLevel: "main",
        alarmMeaning: "Protection group signal failure status indication alarm",
        reportObject: "APS",
        errorDescription: "The protection group exists in SF status",
        treatmentSuggestion: "Check the received optical power of the main and backup channels"
    },
    APS_INDI: {
        alarmName: "APS_INDI",
        alarmLevel: "secondary",
        alarmMeaning: "Protection protocol status indication alarm",
        reportObject: "APS",
        errorDescription:
            "Issued external switching commands (including manual switching, forced switching, and practice switching)",
        treatmentSuggestion: "Release external switching command"
    },
    LOOP_ALM: {
        alarmName: "LOOP_ALM",
        alarmLevel: "secondary",
        alarmMeaning: "The channel on the board is in loopback state",
        reportObject: "Generate an alert to alert the user.",
        errorDescription: "port",
        treatmentSuggestion: "Loopback of the line board or interface board is manually configured."
    },
    COMMUN_FAIL: {
        alarmName: "COMMUN_FAIL",
        alarmLevel: "main",
        alarmMeaning: "Inter-board communication failure",
        reportObject: "veneer",
        errorDescription: "(1) Single board failure; (2) Single board reset.",
        treatmentSuggestion:
            "After the board is reset, the alarm will be automatically cleared; if the alarm is still not cleared, unplug and insert the board; if the alarm is still not cleared, restart the main control; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    LINK_DOWN: {
        alarmName: "LINK_DOWN",
        alarmLevel: "urgent",
        alarmMeaning: "data link error",
        reportObject: "port",
        errorDescription:
            "(1) The working modes of the optical ports at the sending and receiving ends are inconsistent, causing negotiation failure; (2) Link failure; (3) Cable, optical fiber connection or peer equipment failure; (4) The input optical power of the two connected Ethernet ports Too high or too low; (5) The optical interface modes of the two connected Ethernet ports are inconsistent, or the optical interface mode and fiber optic mode are inconsistent; (6) The single board of the station is faulty.",
        treatmentSuggestion:
            "Check whether the board port connection is normal; if the alarm still does not clear, restart the board; if the alarm still does not clear, contact the supplier for technical support;"
    },
    TELEMETRY_COMM_FAIL: {
        alarmName: "TELEMETRY_COMM_FAIL",
        alarmLevel: "main",
        alarmMeaning: "Communication failure between network element and Telemetry server",
        reportObject: "Network element",
        errorDescription:
            "In TCP mode, this alarm is reported if the connection between the network element and the Telemetry server is interrupted or the session with the server is abnormal.",
        treatmentSuggestion: "Check whether the access connection of the Telemetry server is normal"
    },
    TCA_OPT_INPUT_POWER_H_15MIN: {
        alarmName: "TCA_OPT_INPUT_POWER_H_15MIN",
        alarmLevel: "urgent",
        alarmMeaning: "The input optical power reaches the higher threshold after 15 minutes.",
        reportObject: "Port, OCH, OP, OSC",
        errorDescription:
            "(1) The transmit power of the opposite end station is too high; (2) The selected optical module model is inappropriate; (3) The single board of this station is faulty; (4) The appropriate amount of attenuation is not added.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check the attenuation; if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier Get technical support;'
    },
    TCA_OPT_INPUT_POWER_H_24H: {
        alarmName: "TCA_OPT_INPUT_POWER_H_24H",
        alarmLevel: "urgent",
        alarmMeaning: "The input optical power is higher than the threshold for 24 hours.",
        reportObject: "Port, OCH, OP, OSC",
        errorDescription:
            "(1) The transmit power of the opposite end station is too high; (2) The selected optical module model is inappropriate; (3) The single board of this station is faulty; (4) The appropriate amount of attenuation is not added.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check the attenuation; if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier Get technical support;'
    },
    TCA_OPT_INPUT_POWER_L_15MIN: {
        alarmName: "TCA_OPT_INPUT_POWER_L_15MIN",
        alarmLevel: "urgent",
        alarmMeaning: "The input optical power reaches the lower threshold after 15 minutes.",
        reportObject: "Port, OCH, OP, OSC",
        errorDescription:
            "(1) The transmission power of the opposite end station is too low; (2) The optical fiber performance is degraded; (3) The selected optical module model is inappropriate; (4) The optical fiber connector is dirty; (5) The pigtail is too bent, damaged or Aging; (6) An oversized optical attenuator is added to the receiving optical port of the single board of this station; (7) The optical signal is attenuated too much during the transmission process, and insufficient optical amplification compensation is obtained; (8) The opposite station single board An excessively large optical attenuator is added to the transmit optical port of the board or the transmit optical module fails, causing the transmit optical power of the opposite station board to be too low; (9) The local board fails; (10) The upstream fiber is broken, and the received signal is Noise; (11) The upstream single-board laser is turned off, and the received signal is noise.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check whether the optical fiber connection is normal and clean the optical fiber connector; check the attenuation; if the alarm is still not cleared, unplug/replace it module; if the alarm still does not clear, contact the supplier for technical support;'
    },
    TCA_OPT_INPUT_POWER_L_24H: {
        alarmName: "TCA_OPT_INPUT_POWER_L_24H",
        alarmLevel: "urgent",
        alarmMeaning: "The input optical power is lower than the threshold for 24 hours",
        reportObject: "Port, OCH, OP, OSC",
        errorDescription:
            "(1) The transmission power of the opposite end station is too low; (2) The optical fiber performance is degraded; (3) The selected optical module model is inappropriate; (4) The optical fiber connector is dirty; (5) The pigtail is too bent, damaged or Aging; (6) An oversized optical attenuator is added to the receiving optical port of the single board of this station; (7) The optical signal is attenuated too much during the transmission process, and insufficient optical amplification compensation is obtained; (8) The opposite station single board An excessively large optical attenuator is added to the transmit optical port of the board or the transmit optical module fails, causing the transmit optical power of the opposite station board to be too low; (9) The local board fails; (10) The upstream fiber is broken, and the received signal is Noise; (11) The upstream single-board laser is turned off, and the received signal is noise.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check whether the optical fiber connection is normal and clean the optical fiber connector; check the attenuation; if the alarm is still not cleared, unplug/replace it module; if the alarm still does not clear, contact the supplier for technical support;'
    },
    TCA_LANE_IN_PWR_H_15MIN: {
        alarmName: "TCA_LANE_IN_PWR_H_15MIN",
        alarmLevel: "urgent",
        alarmMeaning: "Sub-channel laser input power is too high for 15 minutes",
        reportObject: "port",
        errorDescription: "Sub-channel laser input power is too high",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support ;'
    },
    TCA_LANE_IN_PWR_H_24H: {
        alarmName: "TCA_LANE_IN_PWR_H_24H",
        alarmLevel: "urgent",
        alarmMeaning: "Sub-channel laser input power is too high for 24 hours",
        reportObject: "port",
        errorDescription: "Sub-channel laser input power is too high",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support ;'
    },
    TCA_LANE_IN_PWR_L_15MIN: {
        alarmName: "TCA_LANE_IN_PWR_L_15MIN",
        alarmLevel: "urgent",
        alarmMeaning: "Sub-channel laser input power is too low for 15 minutes",
        reportObject: "port",
        errorDescription: "Sub-channel laser input power is too low",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check whether the optical fiber connection is normal and clean the optical fiber connector; if the alarm is still not cleared, unplug/replace the module; if If the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_LANE_IN_PWR_L_24H: {
        alarmName: "TCA_LANE_IN_PWR_L_24H",
        alarmLevel: "urgent",
        alarmMeaning: "Sub-channel laser input power is too low for 24 hours",
        reportObject: "port",
        errorDescription: "Sub-channel laser input power is too low",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check whether the optical fiber connection is normal and clean the optical fiber connector; if the alarm is still not cleared, unplug/replace the module; if If the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_LANE_OUT_PWR_H_15MIN: {
        alarmName: "TCA_LANE_OUT_PWR_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Sub-channel laser output power is too high for 15 minutes",
        reportObject: "port",
        errorDescription: "Sub-channel laser output power is too high",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support ;'
    },
    TCA_LANE_OUT_PWR_H_24H: {
        alarmName: "TCA_LANE_OUT_PWR_H_24H",
        alarmLevel: "main",
        alarmMeaning: "Sub-channel laser output power is too high for 24 hours",
        reportObject: "port",
        errorDescription: "Sub-channel laser output power is too high",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support ;'
    },
    TCA_LANE_OUT_PWR_L_15MIN: {
        alarmName: "TCA_LANE_OUT_PWR_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Sub-channel laser output power is too low for 15 minutes",
        reportObject: "port",
        errorDescription: "Sub-channel laser output power is too low",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check whether the optical fiber connection is normal and clean the optical fiber connector; if the alarm is still not cleared, unplug/replace the module; if If the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_LANE_OUT_PWR_L_24H: {
        alarmName: "TCA_LANE_OUT_PWR_L_24H",
        alarmLevel: "main",
        alarmMeaning: "Sub-channel laser output power is too low for 24 hours",
        reportObject: "port",
        errorDescription: "Sub-channel laser output power is too low",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check whether the optical fiber connection is normal and clean the optical fiber connector; if the alarm is still not cleared, unplug/replace the module; if If the alarm is still not cleared, contact the supplier for technical support;'
    },
    OA_OUTPUT_LOS: {
        alarmName: "OA_OUTPUT_LOS",
        alarmLevel: "secondary",
        alarmMeaning: "No output power",
        reportObject: "port",
        errorDescription: "(1) EDFA module aging; (2) Measurement amplification circuit failure; (3) No input light.",
        treatmentSuggestion:
            "Check whether the incoming light is normal; check whether there are EDFA-related alarms in the OA; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_OPT_OUTPUT_POWER_H_15MIN: {
        alarmName: "TCA_OPT_OUTPUT_POWER_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Output optical power is too high for 15 minutes",
        reportObject: "Port, OCH, OP, OSC",
        errorDescription: "The line side and EDFA module are faulty.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; if the alarm is still not cleared, unplug/replace the module or board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_OUTPUT_POWER_H_24H: {
        alarmName: "TCA_OPT_OUTPUT_POWER_H_24H",
        alarmLevel: "main",
        alarmMeaning: "The output optical power is too high for 24 hours",
        reportObject: "Port, OCH, OP, OSC",
        errorDescription: "The line side and EDFA module are faulty.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; if the alarm is still not cleared, unplug/replace the module or board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_OUTPUT_POWER_L_15MIN: {
        alarmName: "TCA_OPT_OUTPUT_POWER_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Output optical power is too low for 15 minutes",
        reportObject: "Port, OCH, OP, OSC",
        errorDescription: "(1) The line side and EDFA module are faulty; (2) The input light is abnormal.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; if the alarm is still not cleared, unplug/replace the module or board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_OUTPUT_POWER_L_24H: {
        alarmName: "TCA_OPT_OUTPUT_POWER_L_24H",
        alarmLevel: "main",
        alarmMeaning: "The output optical power is too low for 24 hours",
        reportObject: "Port, OCH, OP, OSC",
        errorDescription: "(1) The line side and EDFA module are faulty; (2) The input light is abnormal.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; if the alarm is still not cleared, unplug/replace the module or board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    RX_LOS: {
        alarmName: "RX_LOS",
        alarmLevel: "urgent",
        alarmMeaning: "Receive signal lost",
        reportObject: "Port, module, EDFA",
        errorDescription:
            "(1) The pigtail is not connected to the optical interface of the single board of this station; (2) The laser of the single board of the opposite end station is turned off; (3) The transmission line fiber is broken; (4) The attenuation of the transmission line is too large; (5) The opposite end station The sending part of the single board is faulty; (6) The receiving part of the station is faulty.",
        treatmentSuggestion:
            "Check whether the optical fiber connection is correct; check the output optical power of the upstream single-board laser: if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    OSC_LOS: {
        alarmName: "OSC_LOS",
        alarmLevel: "urgent",
        alarmMeaning: "OSC signal lost",
        reportObject: "Port, OSC",
        errorDescription: "The input optical power of the OSC board is lower than its LOS threshold.",
        treatmentSuggestion:
            "Check whether the optical fiber connection is correct; check the output optical power of the upstream single-board laser: if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    OSC_LF: {
        alarmName: "OSC_LF",
        alarmLevel: "urgent",
        alarmMeaning: "OSC local failure alarm",
        reportObject: "Port, OSC",
        errorDescription: "OSC local failure alarm",
        treatmentSuggestion:
            "Check whether the optical fiber connection is correct; check the output optical power of the upstream single-board laser: if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    EQPT_MISMATCH: {
        alarmName: "EQPT_MISMATCH",
        alarmLevel: "main",
        alarmMeaning: "Wrong type of board inserted",
        reportObject: "veneer",
        errorDescription:
            "The configured logical board is inconsistent with the actual physical board type of the device.",
        treatmentSuggestion:
            "Unplug the board, delete the board, and insert the board. The system will automatically configure the physically inserted board type."
    },
    NTP_SYNC_FAIL: {
        alarmName: "NTP_SYNC_FAIL",
        alarmLevel: "secondary",
        alarmMeaning: "NTP time synchronization failed",
        reportObject: "Network element",
        errorDescription:
            "The NTP time synchronization failure alarm indicates that the network element is currently out of synchronization (not synchronized for 1 day). This alarm ends after NTP successfully completes time synchronization.",
        treatmentSuggestion: "Check whether the access connection of the NTP server is normal"
    },
    TCA_TEMP_L_15MIN: {
        alarmName: "TCA_TEMP_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The temperature lowers the threshold after 15 minutes",
        reportObject: "Single board, module, EDFA",
        errorDescription:
            "(1) The ambient temperature of the board or module is too low; (2) The board or module is faulty.",
        treatmentSuggestion:
            "Check the ambient temperature of the board or module; check whether the TCA threshold is set appropriately; check whether there is a fan alarm; unplug/replace the board or module; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_TEMP_L_24H: {
        alarmName: "TCA_TEMP_L_24H",
        alarmLevel: "main",
        alarmMeaning: "The temperature lowers the threshold for 24 hours",
        reportObject: "Single board, module, EDFA",
        errorDescription:
            "(1) The ambient temperature of the board or module is too low; (2) The board or module is faulty.",
        treatmentSuggestion:
            "Check the ambient temperature of the board or module; check whether the TCA threshold is set appropriately; check whether there is a fan alarm; unplug/replace the board or module; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_TEMP_H_15MIN: {
        alarmName: "TCA_TEMP_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The temperature reaches the higher threshold after 15 minutes",
        reportObject: "Single board, module, EDFA",
        errorDescription:
            "(1) The ambient temperature of the board or module is too high; (2) The board or module is faulty.",
        treatmentSuggestion:
            "Check the ambient temperature of the board or module; check whether the TCA threshold is set appropriately; check whether there is a fan alarm; unplug/replace the board or module; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_TEMP_H_24H: {
        alarmName: "TCA_TEMP_H_24H",
        alarmLevel: "main",
        alarmMeaning: "The temperature is higher than the threshold for 24 hours",
        reportObject: "Single board, module, EDFA",
        errorDescription:
            "(1) The ambient temperature of the board or module is too high; (2) The board or module is faulty.",
        treatmentSuggestion:
            "Check the ambient temperature of the board or module; check whether the TCA threshold is set appropriately; check whether there is a fan alarm; unplug/replace the board or module; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TEMP_HEAT: {
        alarmName: "TEMP_HEAT",
        alarmLevel: "urgent",
        alarmMeaning: "The operating temperature seriously exceeds the limit",
        reportObject: "Board, module, sub-module",
        errorDescription:
            "(1) The fan stops working or the dustproof board accumulates too much dust; (2) Refrigeration (heat) equipment fails, causing the ambient temperature to be too high (low); (3) Single board failure.",
        treatmentSuggestion:
            "Check whether there is a fan alarm; unplug/replace boards or modules; if the alarm still does not clear, contact the supplier for technical support;"
    },
    OA_LOW_GAIN: {
        alarmName: "OA_LOW_GAIN",
        alarmLevel: "urgent",
        alarmMeaning: "Optical amplifier gain reduction alarm",
        reportObject: "EDFA",
        errorDescription:
            "(1) PUMP aging causes the gain to fail to meet requirements; (2) input optical power is too high; (3) AD detection failure.",
        treatmentSuggestion:
            "Check the upstream output optical power; unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    OSNR_DEG: {
        alarmName: "OSNR_DEG",
        alarmLevel: "secondary",
        alarmMeaning: "OSNR degradation alarm",
        reportObject: "port",
        errorDescription: "OSNR degradation",
        treatmentSuggestion:
            "Check the quality of the upstream output optical signal; check whether there are alarms in the optical module; unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    EQPT_TRANSCEIVER_MISMATCH: {
        alarmName: "EQPT_TRANSCEIVER_MISMATCH",
        alarmLevel: "urgent",
        alarmMeaning: "Optical module mismatch",
        reportObject: "module",
        errorDescription:
            "The type of optical module installed on the optical port of the board does not match the type of optical module expected by the board.",
        treatmentSuggestion:
            "According to the system supported optical module list, replace the single board port module"
    },
    EQPT_FAILURE: {
        alarmName: "EQPT_FAILURE",
        alarmLevel: "urgent",
        alarmMeaning: "Equipment failure",
        reportObject: "Single board, module, sub-module",
        errorDescription:
            "Single board, optical module, or sub-module failure affects communication, management, or business",
        treatmentSuggestion:
            "Unplug/replace the board; if the alarm still does not clear, restart the main control; if the alarm still does not clear, contact the supplier for technical support;"
    },
    EQPT_COM_FAIL: {
        alarmName: "EQPT_COM_FAIL",
        alarmLevel: "main",
        alarmMeaning: "Equipment communication abnormality alarm",
        reportObject: "Main control, single board, module",
        errorDescription:
            "(1) Module hardware failure; (2) Module microprogram damage; (3) Single board software working abnormally; (4) Single board hardware failure.",
        treatmentSuggestion:
            "Unplug/replace the board; if the alarm still does not clear, restart the main control; if the alarm still does not clear, contact the supplier for technical support;"
    },
    TRANSCEIVER_RATE_MISMATCH: {
        alarmName: "TRANSCEIVER_RATE_MISMATCH",
        alarmLevel: "main",
        alarmMeaning: "Module rate mismatch alarm",
        reportObject: "port",
        errorDescription: "The board speed module set by the user is not supported.",
        treatmentSuggestion: "Check whether the optical module supports multi-rate"
    },
    EQPT_ABSENCE_WARNING: {
        alarmName: "EQPT_ABSENCE_WARNING",
        alarmLevel: "urgent",
        alarmMeaning: "Equipment not in place alarm",
        reportObject: "Board, module, sub-module",
        errorDescription: "The board, module, or sub-module is not in place",
        treatmentSuggestion:
            "Check whether the board/module is properly positioned; unplug/replace the board or module; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    ALS_ACTIVED: {
        alarmName: "ALS_ACTIVED",
        alarmLevel: "secondary",
        alarmMeaning: "Laser automatically shuts down",
        reportObject: "EDFA, module",
        errorDescription: "Laser automatic shut-off function triggered",
        treatmentSuggestion:
            "For the EDFA APR or electrical card client-side optical module ALS function, the alarm will automatically disappear after the conditions that trigger the laser to automatically turn off disappear."
    },
    TCA_LASER_BIAS_CURRENT_L_15MIN: {
        alarmName: "TCA_LASER_BIAS_CURRENT_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Laser bias current is too low for 15 minutes",
        reportObject: "EDFA, module, OCH",
        errorDescription: "(1) Laser aging; (2) Single board failure.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the optical module; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_LASER_BIAS_CURRENT_L_24H: {
        alarmName: "TCA_LASER_BIAS_CURRENT_L_24H",
        alarmLevel: "main",
        alarmMeaning: "Laser bias current is too low for 24 hours",
        reportObject: "EDFA, module, OCH",
        errorDescription: "(1) Laser aging; (2) Single board failure.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the optical module; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_LASER_BIAS_CURRENT_H_15MIN: {
        alarmName: "TCA_LASER_BIAS_CURRENT_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Laser bias current is too high for 15 minutes",
        reportObject: "EDFA, module, OCH",
        errorDescription: "(1) Laser aging; (2) Single board failure.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the optical module; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_LASER_BIAS_CURRENT_H_24H: {
        alarmName: "TCA_LASER_BIAS_CURRENT_H_24H",
        alarmLevel: "main",
        alarmMeaning: "Laser bias current is too high for 24 hours",
        reportObject: "EDFA, module, OCH",
        errorDescription: "(1) Laser aging; (2) Single board failure.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the optical module; if the alarm is still not cleared, unplug and insert the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    LASER_LIFE_WARNING: {
        alarmName: "LASER_LIFE_WARNING",
        alarmLevel: "urgent",
        alarmMeaning: "Laser life warning",
        reportObject: "EDFA, module, OCH, OSC",
        errorDescription: "(1) Laser aging; (2) Single board detection circuit failure.",
        treatmentSuggestion:
            "Unplug/replace the optical module; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_OA_PUMP_BIAS_CURRENT_L_15MIN: {
        alarmName: "TCA_OA_PUMP_BIAS_CURRENT_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Pump laser bias current is too low for 15 minutes",
        reportObject: "EDFA",
        errorDescription: "The EDFA module is faulty.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management system; if the alarm is not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OA_PUMP_BIAS_CURRENT_L_24H: {
        alarmName: "TCA_OA_PUMP_BIAS_CURRENT_L_24H",
        alarmLevel: "main",
        alarmMeaning: "Pump laser bias current is too low for 24 hours",
        reportObject: "EDFA",
        errorDescription: "The EDFA module is faulty.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management system; if the alarm is not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OA_PUMP_BIAS_CURRENT_H_15MIN: {
        alarmName: "TCA_OA_PUMP_BIAS_CURRENT_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Pump laser bias current is too high for 15 minutes",
        reportObject: "EDFA",
        errorDescription: "The EDFA module is faulty.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management system; if the alarm is not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OA_PUMP_BIAS_CURRENT_H_24H: {
        alarmName: "TCA_OA_PUMP_BIAS_CURRENT_H_24H",
        alarmLevel: "main",
        alarmMeaning: "Pump laser bias current is too high for 24 hours",
        reportObject: "EDFA",
        errorDescription: "The EDFA module is faulty.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management system; if the alarm is not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OA_PUMP_TEMP_L_15MIN: {
        alarmName: "TCA_OA_PUMP_TEMP_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The operating temperature of the pump laser is too low for 15 minutes",
        reportObject: "EDFA",
        errorDescription: "The EDFA module is faulty.",
        treatmentSuggestion:
            "Check the ambient temperature of the board or module; check whether the TCA threshold is set appropriately; check whether there is a fan alarm; unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_OA_PUMP_TEMP_L_24H: {
        alarmName: "TCA_OA_PUMP_TEMP_L_24H",
        alarmLevel: "main",
        alarmMeaning: "The operating temperature of the pump laser is too low for 24 hours",
        reportObject: "EDFA",
        errorDescription: "The EDFA module is faulty.",
        treatmentSuggestion:
            "Check the ambient temperature of the board or module; check whether the TCA threshold is set appropriately; check whether there is a fan alarm; unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_OA_PUMP_TEMP_H_15MIN: {
        alarmName: "TCA_OA_PUMP_TEMP_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The operating temperature of the pump laser is too high for 15 minutes",
        reportObject: "EDFA",
        errorDescription: "The EDFA module is faulty.",
        treatmentSuggestion:
            "Check the ambient temperature of the board or module; check whether the TCA threshold is set appropriately; check whether there is a fan alarm; unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_OA_PUMP_TEMP_H_24H: {
        alarmName: "TCA_OA_PUMP_TEMP_H_24H",
        alarmLevel: "main",
        alarmMeaning: "The operating temperature of the pump laser is too high for 24 hours",
        reportObject: "EDFA",
        errorDescription: "The EDFA module is faulty.",
        treatmentSuggestion:
            "Check the ambient temperature of the board or module; check whether the TCA threshold is set appropriately; check whether there is a fan alarm; unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_OA_PUMP_COOLING_CURRENT_L_15MIN: {
        alarmName: "TCA_OA_PUMP_COOLING_CURRENT_L_15MIN",
        alarmLevel: "urgent",
        alarmMeaning: "Pump laser cooling current is too low for 15 minutes",
        reportObject: "EDFA",
        errorDescription:
            "(1) The internal temperature of the pump laser is too high (low); (2) The external ambient temperature of the board is too high (low); (3) The internal circuit of the board is faulty.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OA_PUMP_COOLING_CURRENT_L_24H: {
        alarmName: "TCA_OA_PUMP_COOLING_CURRENT_L_24H",
        alarmLevel: "urgent",
        alarmMeaning: "Pump laser cooling current is too low for 24 hours",
        reportObject: "EDFA",
        errorDescription:
            "(1) The internal temperature of the pump laser is too high (low); (2) The external ambient temperature of the board is too high (low); (3) The internal circuit of the board is faulty.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OA_PUMP_COOLING_CURRENT_H_15MIN: {
        alarmName: "TCA_OA_PUMP_COOLING_CURRENT_H_15MIN",
        alarmLevel: "urgent",
        alarmMeaning: "Pump laser cooling current is too high for 15 minutes",
        reportObject: "EDFA",
        errorDescription:
            "(1) The internal temperature of the pump laser is too high (low); (2) The external ambient temperature of the board is too high (low); (3) The internal circuit of the board is faulty.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug and insert the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OA_PUMP_COOLING_CURRENT_H_24H: {
        alarmName: "TCA_OA_PUMP_COOLING_CURRENT_H_24H",
        alarmLevel: "urgent",
        alarmMeaning: "Pump laser cooling current is too high for 24 hours",
        reportObject: "EDFA",
        errorDescription:
            "(1) The internal temperature of the pump laser is too high (low); (2) The external ambient temperature of the board is too high (low); (3) The internal circuit of the board is faulty.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_HOUSING_TEMP_L_15MIN: {
        alarmName: "TCA_OPT_HOUSING_TEMP_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The module shell temperature exceeds the limit and is too low after 15 minutes.",
        reportObject: "module",
        errorDescription: "The module shell temperature exceeds the limit and is too low after 15 minutes.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_HOUSING_TEMP_L_24H: {
        alarmName: "TCA_OPT_HOUSING_TEMP_L_24H",
        alarmLevel: "main",
        alarmMeaning: "The module shell temperature exceeds the limit and is too low within 24 hours.",
        reportObject: "module",
        errorDescription: "The module shell temperature exceeds the limit and is too low within 24 hours.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug and insert the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_HOUSING_TEMP_H_15MIN: {
        alarmName: "TCA_OPT_HOUSING_TEMP_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The module shell temperature has exceeded the limit for 15 minutes and is too high.",
        reportObject: "module",
        errorDescription: "The module shell temperature has exceeded the limit for 15 minutes and is too high.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_HOUSING_TEMP_H_24H: {
        alarmName: "TCA_OPT_HOUSING_TEMP_H_24H",
        alarmLevel: "main",
        alarmMeaning: "The module shell temperature exceeds the limit for 24 hours and is too high.",
        reportObject: "module",
        errorDescription: "The module shell temperature exceeds the limit for 24 hours and is too high.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_SUPPLY_VOLTAGE_L_15MIN: {
        alarmName: "TCA_OPT_SUPPLY_VOLTAGE_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The power supply voltage exceeds the limit in 15 minutes and is too low.",
        reportObject: "module",
        errorDescription: "The power supply voltage exceeds the limit in 15 minutes and is too low.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_SUPPLY_VOLTAGE_L_24H: {
        alarmName: "TCA_OPT_SUPPLY_VOLTAGE_L_24H",
        alarmLevel: "main",
        alarmMeaning: "The power supply voltage exceeds the 24-hour limit and is too low.",
        reportObject: "module",
        errorDescription: "The power supply voltage exceeds the 24-hour limit and is too low.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_SUPPLY_VOLTAGE_H_15MIN: {
        alarmName: "TCA_OPT_SUPPLY_VOLTAGE_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The power supply voltage exceeds the limit in 15 minutes and is too high",
        reportObject: "module",
        errorDescription: "The power supply voltage exceeds the limit in 15 minutes and is too high",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_SUPPLY_VOLTAGE_H_24H: {
        alarmName: "TCA_OPT_SUPPLY_VOLTAGE_H_24H",
        alarmLevel: "main",
        alarmMeaning: "The power supply voltage exceeds the limit for 24 hours and is too high",
        reportObject: "module",
        errorDescription: "The power supply voltage exceeds the limit for 24 hours and is too high",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_TOTAL_OUTPUT_POWER_L_15MIN: {
        alarmName: "TCA_OPT_TOTAL_OUTPUT_POWER_L_15MIN",
        alarmLevel: "secondary",
        alarmMeaning: "The total output optical power exceeds the limit and is too low after 15 minutes.",
        reportObject: "module",
        errorDescription: "The total output optical power exceeds the limit and is too low after 15 minutes.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check whether the optical fiber connection is normal and clean the optical fiber connector; if the alarm is still not cleared, unplug and insert the module; if the alarm still persists If it is not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_TOTAL_OUTPUT_POWER_L_24H: {
        alarmName: "TCA_OPT_TOTAL_OUTPUT_POWER_L_24H",
        alarmLevel: "secondary",
        alarmMeaning: "The total output optical power exceeds the limit and is too low within 24 hours.",
        reportObject: "module",
        errorDescription: "The total output optical power exceeds the limit and is too low within 24 hours.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check whether the optical fiber connection is normal and clean the optical fiber connector; if the alarm is still not cleared, unplug/replace the module; if If the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_TOTAL_OUTPUT_POWER_H_15MIN: {
        alarmName: "TCA_OPT_TOTAL_OUTPUT_POWER_H_15MIN",
        alarmLevel: "secondary",
        alarmMeaning: "The total output optical power exceeds the limit for 15 minutes and is too high.",
        reportObject: "module",
        errorDescription: "The total output optical power exceeds the limit for 15 minutes and is too high.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support ;'
    },
    TCA_OPT_TOTAL_OUTPUT_POWER_H_24H: {
        alarmName: "TCA_OPT_TOTAL_OUTPUT_POWER_H_24H",
        alarmLevel: "secondary",
        alarmMeaning: "The total output optical power exceeds the limit for 24 hours and is too high.",
        reportObject: "module",
        errorDescription: "The total output optical power exceeds the limit for 24 hours and is too high.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; if the alarm is still not cleared, unplug and insert the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_TOTAL_INPUT_POWER_L_15MIN: {
        alarmName: "TCA_OPT_TOTAL_INPUT_POWER_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The total input optical power exceeds the limit for 15 minutes and is too low.",
        reportObject: "module",
        errorDescription: "The total input optical power exceeds the limit for 15 minutes and is too low.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check whether the optical fiber connection is normal and clean the optical fiber connector; if the alarm is still not cleared, unplug/replace the module; if If the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_TOTAL_INPUT_POWER_L_24H: {
        alarmName: "TCA_OPT_TOTAL_INPUT_POWER_L_24H",
        alarmLevel: "main",
        alarmMeaning: "The total input optical power exceeds the limit for 24 hours and is too low.",
        reportObject: "module",
        errorDescription: "The total input optical power exceeds the limit for 24 hours and is too low.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; check whether the optical fiber connection is normal and clean the optical fiber connector; if the alarm is still not cleared, unplug/replace the module; if If the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_TOTAL_INPUT_POWER_H_15MIN: {
        alarmName: "TCA_OPT_TOTAL_INPUT_POWER_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The total input optical power exceeds the limit for 15 minutes and is too high.",
        reportObject: "module",
        errorDescription: "The total input optical power exceeds the limit for 15 minutes and is too high.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; if the alarm is still not cleared, unplug and insert the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_OPT_TOTAL_INPUT_POWER_H_24H: {
        alarmName: "TCA_OPT_TOTAL_INPUT_POWER_H_24H",
        alarmLevel: "main",
        alarmMeaning: "The total input optical power exceeds the limit for 24 hours and is too high.",
        reportObject: "module",
        errorDescription: "The total input optical power exceeds the limit for 24 hours and is too high.",
        treatmentSuggestion:
            'Click "Clear TCA" on the network management; check whether the TCA threshold is set appropriately; check whether the peer\'s transmit power and optical module are appropriate; if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support ;'
    },
    LASER_DISABLED: {
        alarmName: "LASER_DISABLED",
        alarmLevel: "main",
        alarmMeaning: "Laser off",
        reportObject: "EDFA, module",
        errorDescription: "The user shut down the laser through the network management system.",
        treatmentSuggestion:
            "This alarm is used to prompt the user to perform the operation of turning off the laser. After the operation is released, the alarm is cleared."
    },
    EQPT_POWER_DOWN: {
        alarmName: "EQPT_POWER_DOWN",
        alarmLevel: "main",
        alarmMeaning: "The board or submodule is not powered on",
        reportObject: "veneer",
        errorDescription: "Report after powering off and configuring",
        treatmentSuggestion:
            "This alarm will be reported after the board is powered off and configured. The alarm will disappear after the configuration is cancelled."
    },
    PWR_NO_INPUT: {
        alarmName: "PWR_NO_INPUT",
        alarmLevel: "urgent",
        alarmMeaning: "No input alarm on power board",
        reportObject: "Power Board",
        errorDescription: "No input alarm on power board",
        treatmentSuggestion:
            "Check whether the power supply is connected normally; unplug/replace the power supply; if the alarm still does not clear, contact the supplier for technical support;"
    },
    PWR_NO_OUTPUT: {
        alarmName: "PWR_NO_OUTPUT",
        alarmLevel: "urgent",
        alarmMeaning: "No output alarm from power board",
        reportObject: "Power Board",
        errorDescription: "No output alarm from power board",
        treatmentSuggestion:
            "Check whether the power supply is connected normally; unplug/replace the power supply; if the alarm still does not clear, contact the supplier for technical support;"
    },
    POWER_ABNORMAL: {
        alarmName: "POWER_ABNORMAL",
        alarmLevel: "main",
        alarmMeaning: "power failure",
        reportObject: "Power Board",
        errorDescription:
            "(1) The single-board power module fails; (2) The power input is abnormal; (3) The subrack power board fails.",
        treatmentSuggestion:
            "Check whether the power supply is connected normally; unplug/replace the power supply; if the alarm still does not clear, contact the supplier for technical support;"
    },
    TCA_PWR_CURRENT_OUTPUT_H_15MIN: {
        alarmName: "TCA_PWR_CURRENT_OUTPUT_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The power supply output current reaches the upper limit after 15 minutes.",
        reportObject: "Power Board",
        errorDescription: "The higher the power supply output current limit",
        treatmentSuggestion:
            "Check whether the power access is normal; check whether the TCA threshold is set appropriately; unplug/replace the power supply; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_PWR_CURRENT_OUTPUT_H_24H: {
        alarmName: "TCA_PWR_CURRENT_OUTPUT_H_24H",
        alarmLevel: "main",
        alarmMeaning: "The power supply output current exceeds the upper limit for 24 hours",
        reportObject: "Power Board",
        errorDescription: "The higher the power supply output current limit",
        treatmentSuggestion:
            "Check whether the power access is normal; check whether the TCA threshold is set appropriately; unplug/replace the power supply; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_PWR_CURRENT_OUTPUT_L_15MIN: {
        alarmName: "TCA_PWR_CURRENT_OUTPUT_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "The power supply output current reaches the lower limit after 15 minutes.",
        reportObject: "Power Board",
        errorDescription: "The lower the power supply output current limit",
        treatmentSuggestion:
            "Check whether the power access is normal; check whether the TCA threshold is set appropriately; unplug/replace the power supply; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_PWR_CURRENT_OUTPUT_L_24H: {
        alarmName: "TCA_PWR_CURRENT_OUTPUT_L_24H",
        alarmLevel: "main",
        alarmMeaning: "The power supply output current reaches the lower limit in 24 hours",
        reportObject: "Power Board",
        errorDescription: "The lower the power supply output current limit",
        treatmentSuggestion:
            "Check whether the power access is normal; check whether the TCA threshold is set appropriately; unplug/replace the power supply; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    FAN_SPEED_LOW: {
        alarmName: "FAN_SPEED_LOW",
        alarmLevel: "main",
        alarmMeaning: "The deviation between the actual fan speed and the configured value exceeds the limit.",
        reportObject: "fan board",
        errorDescription:
            "Report if the actual speed is more than 20% lower than the configured value (20% of the fan’s full speed)",
        treatmentSuggestion:
            "Check whether there are other fan alarms; unplug/replace the fan; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    OCH_LOS_P: {
        alarmName: "OCH_LOS_P",
        alarmLevel: "urgent",
        alarmMeaning: "OCh layer payload signal loss",
        reportObject: "OCH",
        errorDescription:
            "The input optical power of the OCH port of the single board is lower than its LOS threshold.",
        treatmentSuggestion:
            "Check whether the optical fiber connection is correct; check the output optical power of the upstream single-board laser: if the alarm is still not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;"
    },
    TCA_LASER_TEMP_L_15MIN: {
        alarmName: "TCA_LASER_TEMP_L_15MIN",
        alarmLevel: "urgent",
        alarmMeaning: "Laser temperature is too low for 15 minutes",
        reportObject: "OCH",
        errorDescription: "The module laser temperature has exceeded the limit and is too low after 15 minutes.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_LASER_TEMP_L_24H: {
        alarmName: "TCA_LASER_TEMP_L_24H",
        alarmLevel: "urgent",
        alarmMeaning: "The laser temperature is too low for 24 hours",
        reportObject: "OCH",
        errorDescription: "Module laser temperature exceeds the limit and is too low within 24 hours",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_LASER_TEMP_H_15MIN: {
        alarmName: "TCA_LASER_TEMP_H_15MIN",
        alarmLevel: "urgent",
        alarmMeaning: "Laser temperature is too high for 15 minutes",
        reportObject: "OCH",
        errorDescription: "The module laser temperature has exceeded the limit for 15 minutes and is too high.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    TCA_LASER_TEMP_H_24H: {
        alarmName: "TCA_LASER_TEMP_H_24H",
        alarmLevel: "urgent",
        alarmMeaning: "Laser temperature is too high for 24 hours",
        reportObject: "OCH",
        errorDescription: "The module laser temperature exceeds the limit and is too high within 24 hours.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; check whether there is a fan alarm; click "Clear TCA" on the network management; if the alarm is not cleared, unplug/replace the module; if the alarm is still not cleared, contact the supplier for technical support;'
    },
    ODU2_PM_AIS: {
        alarmName: "ODU2_PM_AIS",
        alarmLevel: "hint",
        alarmMeaning: "ODU2 PM alarm indication signal",
        reportObject: "ODU",
        errorDescription:
            "The entire ODU2 signal is all 1 (except FA OH, OTU1 OH and ODU2 FTFL), and the detected STAT information is 111.",
        treatmentSuggestion:
            "Check whether there are alarms on the connected device ports or business entities; check whether the optical fiber connection is normal and clean the optical fiber connector; warm up the board; cold up the board; unplug/replace modules and boards; if the alarm is still not cleared, Contact the supplier for technical support;"
    },
    ODUFLEX_PM_AIS: {
        alarmName: "ODUFLEX_PM_AIS",
        alarmLevel: "hint",
        alarmMeaning: "ODUFLEX PM alarm indication signal",
        reportObject: "ODU",
        errorDescription:
            "(1) Client side reason: The client side input signal contains the ODUk_PM_AIS alarm signal; (2) Wavelength division side reason: The corresponding single board of the opposite end station reports ODUFlex_PM_AIS, R_LOS, OTUk_LOF, OTUk_LOM, OTUk_TIM and other alarms on the client side, causing alarms to the local end Plug in the AIS signal.",
        treatmentSuggestion:
            "Check whether there are alarms on the connected device ports or business entities; check whether the optical fiber connection is normal and clean the optical fiber connector; warm up the board; cold up the board; unplug/replace modules and boards; if the alarm is still not cleared, Contact the supplier for technical support;"
    },
    ODU4_PM_AIS: {
        alarmName: "ODU4_PM_AIS",
        alarmLevel: "hint",
        alarmMeaning: "ODU4 PM alarm indication signal",
        reportObject: "ODU",
        errorDescription:
            "The client-side input signal contains the ODUk_PM_AIS signal; the corresponding board at the opposite end sends the ODUk_PM_AIS signal; the upstream site has loopback or cross-connection, and the FEC type configuration is incorrect.",
        treatmentSuggestion:
            "Check whether there are alarms on the connected device ports or business entities; check whether the optical fiber connection is normal and clean the optical fiber connector; warm up the board; cold up the board; unplug/replace modules and boards; if the alarm is still not cleared, Contact the supplier for technical support;"
    },
    ODUCN_PM_AIS: {
        alarmName: "ODUCN_PM_AIS",
        alarmLevel: "hint",
        alarmMeaning: "ODUCn PM alarm indication signal",
        reportObject: "ODU",
        errorDescription:
            "(1) The client-side input signal contains the ODUCn_PM_AIS signal; (2) The corresponding board at the opposite end sends the ODUCn_PM_AIS signal; (3) The upstream site has loopback or cross-connection, or the FEC type configuration is incorrect.",
        treatmentSuggestion:
            "Check whether there are alarms on the connected device ports or business entities; check whether the optical fiber connection is normal and clean the optical fiber connector; warm up the board; cold up the board; unplug/replace modules and boards; if the alarm is still not cleared, Contact the supplier for technical support;"
    },
    ODU2_PM_BDI: {
        alarmName: "ODU2_PM_BDI",
        alarmLevel: "hint",
        alarmMeaning: "ODU2 PM backward defect indication",
        reportObject: "ODU",
        errorDescription:
            "If the BDI bit in the PM overhead field remains 1 for 5 frames, this alarm will be reported.",
        treatmentSuggestion: "Check whether there is an alarm on the upstream associated ODU business entity: LOS"
    },
    ODUFLEX_PM_BDI: {
        alarmName: "ODUFLEX_PM_BDI",
        alarmLevel: "hint",
        alarmMeaning: "ODUFLEX PM backward defect indication",
        reportObject: "ODU",
        errorDescription:
            "(1) Client-side reasons: The client-side input signal contains the ODUFlex_PM_BDI signal; (2) WDM-side reasons: The corresponding single board WDM volume of the opposite end station generates ODUFlex_LOFLOM, ODUFlex_PM_AIS, ODUFlex_PM_LCK, ODUFlex_PM_OCI, ODUFlex_PM_TIM, ODUFlex_PM_SSF and other alarms, and reports to this site Return the ODUFlex_PM_BDI alarm.",
        treatmentSuggestion: "Check whether there is an alarm on the upstream associated ODU business entity: LOS"
    },
    ODU4_PM_BDI: {
        alarmName: "ODU4_PM_BDI",
        alarmLevel: "hint",
        alarmMeaning: "ODU4 PM backward defect indication",
        reportObject: "ODU",
        errorDescription:
            "The client-side input signal contains the ODUk_PM_BDI signal; the corresponding OTU board of the downstream station receives LOF, LOM, and ODUk_PM_AIS alarms.",
        treatmentSuggestion: "Check whether there is an alarm on the upstream associated ODU business entity: LOS"
    },
    ODUCN_PM_BDI: {
        alarmName: "ODUCN_PM_BDI",
        alarmLevel: "hint",
        alarmMeaning: "ODUCn PM backward defect indication",
        reportObject: "ODU",
        errorDescription:
            "(1) The client-side input signal contains the ODUCn_PM_BDI signal; (2) The corresponding OTU board of the downstream station receives LOF, LOM, and ODUCn_PM_AIS alarms.",
        treatmentSuggestion: "Check whether there is an alarm on the upstream associated ODU business entity: LOS"
    },
    ODU2_PM_DEG: {
        alarmName: "ODU2_PM_DEG",
        alarmLevel: "secondary",
        alarmMeaning: "ODU2 PM signal degradation",
        reportObject: "ODU",
        errorDescription:
            "Assuming error burst distribution, only DEG is supported and EXC is not supported. If DEGM consecutive bad 1s performance monitoring cycles are detected (the number or proportion of error blocks detected in a 1s cycle ≥ DEGTHR, it is a bad 1s cycle), this alarm will be reported.",
        treatmentSuggestion:
            "Check whether the received optical power is normal; check whether the optical fiber connection is normal, clean the optical fiber connector; warm up/cold up the board; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support support;"
    },
    ODUFLEX_PM_DEG: {
        alarmName: "ODUFLEX_PM_DEG",
        alarmLevel: "secondary",
        alarmMeaning: "ODUFLEX PM signal degradation",
        reportObject: "ODU",
        errorDescription:
            "(1) The input optical power of the board is abnormal; (2) The optical fiber transmission line is abnormal; (3) The board that reported this alarm or the corresponding board is faulty.",
        treatmentSuggestion:
            "Check whether the received optical power is normal; check whether the optical fiber connection is normal, clean the optical fiber connector; warm up/cold up the board; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support support;"
    },
    ODU4_PM_DEG: {
        alarmName: "ODU4_PM_DEG",
        alarmLevel: "secondary",
        alarmMeaning: "ODU4 PM signal degradation",
        reportObject: "ODU",
        errorDescription:
            "The local optical port is faulty; the opposite optical port is faulty; the optical fiber transmission line is abnormal.",
        treatmentSuggestion:
            "Check whether the received optical power is normal; check whether the optical fiber connection is normal, clean the optical fiber connector; warm up/cold up the board; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support support;"
    },
    ODUCN_PM_DEG: {
        alarmName: "ODUCN_PM_DEG",
        alarmLevel: "main",
        alarmMeaning: "ODUCn PM segment BIP8 bit error exceeds the limit",
        reportObject: "ODU",
        errorDescription:
            "(1) The attenuation of the received signal is too large and the optical fiber transmission line is abnormal; (2) The sending part of the opposite end station is faulty; (3) The receiving part of this station is faulty.",
        treatmentSuggestion:
            "Check whether the received optical power is normal; check whether the optical fiber connection is normal, clean the optical fiber connector; warm up/cold up the board; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for technical support support;"
    },
    ODU2_PM_SSF: {
        alarmName: "ODU2_PM_SSF",
        alarmLevel: "hint",
        alarmMeaning: "ODU2 PM service layer signal failure",
        reportObject: "ODU",
        errorDescription:
            "The ODU2_PM_SSF alarm is generated by the following defects: (1) ODU2_PM_AIS; (2) When the ODU2_TCMi sink is enabled and in operation mode, there are ODU2_TCMi_SSF, ODU2_TCMi_LCK, ODU2_TCMi_OCI, ODU2_TCMi_AIS, ODU2_TCMi_LTC and ODU2_TCMi_LTC subsequent actions are enabled, ODU2_TCMi_TIM and ODU2 _TCMi_TIM subsequent enable; (3) OTU2_TIM, and OTU2_TIM subsequent actions are enabled; (4) OTU2_SSF.",
        treatmentSuggestion:
            "Check whether the received optical power is normal; clean the optical module connector; hot start/cold start the board; unplug/replace the optical module; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for more information. Technical Support;"
    },
    ODUFLEX_PM_SSF: {
        alarmName: "ODUFLEX_PM_SSF",
        alarmLevel: "hint",
        alarmMeaning: "ODUFLEX PM service layer signal failure",
        reportObject: "ODU",
        errorDescription: "The ODUFlex_PM_SSF alarm is generated by the following defect ODU0_PM_AIS.",
        treatmentSuggestion:
            "Check whether the received optical power is normal; clean the optical module connector; hot start/cold start the board; unplug/replace the optical module; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for more information. Technical Support;"
    },
    ODU4_PM_SSF: {
        alarmName: "ODU4_PM_SSF",
        alarmLevel: "hint",
        alarmMeaning: "ODU4 PM service layer signal failure",
        reportObject: "ODU",
        errorDescription:
            "The ODU4_PM_SSF alarm is generated by the following defects: 1. ODU4_PM_AIS2, when the ODU4_TCMi sink is enabled and in operation mode, there are ODU4_TCMi_SSF, ODU4_TCMi_LCK, ODU4_TCMi_OCI, ODU4_TCMi_AIS, ODU4_TCMi_LTC and the ODU4_TCMi_LTC follow-up action is enabled, ODU4_TCMi_TIM and ODU4_TC Mi_TIM subsequent enablement, 3. OTU1_TIM, And OTU1_TIM follow-up action is enabled 4, OTU1_SSF",
        treatmentSuggestion:
            "Check whether the received optical power is normal; clean the optical module connector; hot start/cold start the board; unplug/replace the optical module; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for more information. Technical Support;"
    },
    ODUCN_PM_SSF: {
        alarmName: "ODUCN_PM_SSF",
        alarmLevel: "hint",
        alarmMeaning: "ODUCn PM service layer signal failure",
        reportObject: "ODU",
        errorDescription:
            "The ODUCn_PM_SSF alarm is generated by the following defects: (1) ODUCn_PM_AIS (2) OTUCn_TIM, and the OTUCn_TIM follow-up action is enabled (3) OTUCn_SSF",
        treatmentSuggestion:
            "Check whether the received optical power is normal; clean the optical module connector; hot start/cold start the board; unplug/replace the optical module; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for more information. Technical Support;"
    },
    ODU2_LOFLOM: {
        alarmName: "ODU2_LOFLOM",
        alarmLevel: "urgent",
        alarmMeaning: "ODU2 frame and multiframe loss",
        reportObject: "ODU",
        errorDescription:
            "If the frame positioning process continues to be out of frame (OOF) for 3ms, a LOFLOM alarm should be issued.",
        treatmentSuggestion:
            "Check whether the received optical power is normal; clean the optical module connector; hot start/cold start the board; unplug/replace the optical module; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for more information. Technical Support;"
    },
    ODUFLEX_LOFLOM: {
        alarmName: "ODUFLEX_LOFLOM",
        alarmLevel: "urgent",
        alarmMeaning: "ODUFLEX frame and multiframe loss",
        reportObject: "ODU",
        errorDescription:
            "(1) The signal sent by the corresponding board at the opposite end has no frame structure; (2) The input optical power is abnormal; (3) The FEC type setting is wrong; (4) The signal received by the OTU board of the local station has too many bit errors; (5 ) The optical fiber transmission line is abnormal.",
        treatmentSuggestion:
            "Check whether the received optical power is normal; clean the optical module connector; hot start/cold start the board; unplug/replace the optical module; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for more information. Technical Support;"
    },
    ODU4_LOFLOM: {
        alarmName: "ODU4_LOFLOM",
        alarmLevel: "urgent",
        alarmMeaning: "ODU4 frame and multiframe loss",
        reportObject: "ODU",
        errorDescription:
            "If the frame positioning process continues to be out of frame (OOF) for 3ms, a LOFLOM alarm should be issued.",
        treatmentSuggestion:
            "Check whether the received optical power is normal; clean the optical module connector; hot start/cold start the board; unplug/replace the optical module; if the alarm is still not cleared, unplug/replace the board; if the alarm is still not cleared, contact the supplier for more information. Technical Support;"
    },
    ODU2_PM_LCK: {
        alarmName: "ODU2_PM_LCK",
        alarmLevel: "secondary",
        alarmMeaning: "ODU2 PM signal locking defect",
        reportObject: "ODU",
        errorDescription: "If the received STAT message is 101, LCK should be generated.",
        treatmentSuggestion:
            "Check whether the remote end and the local end are admin down and other locked system configurations. If the alarm still does not clear, unplug/replace the board; if the alarm still does not clear, contact the supplier for technical support;"
    },
    ODUFLEX_PM_LCK: {
        alarmName: "ODUFLEX_PM_LCK",
        alarmLevel: "secondary",
        alarmMeaning: "ODUFLEX PM signal locking defect",
        reportObject: "ODU",
        errorDescription: "The signal is locked due to testing the current line signal.",
        treatmentSuggestion:
            "Check whether the remote end and the local end are admin down and other locked system configurations. If the alarm still does not clear, unplug/replace the board; if the alarm still does not clear, contact the supplier for technical support;"
    },
    ODU4_PM_LCK: {
        alarmName: "ODU4_PM_LCK",
        alarmLevel: "secondary",
        alarmMeaning: "ODU4 PM signal locking defect",
        reportObject: "ODU",
        errorDescription: "The signal is locked due to testing the current line signal.",
        treatmentSuggestion:
            "Check whether the remote end and the local end are admin down and other locked system configurations. If the alarm still does not clear, unplug/replace the board; if the alarm still does not clear, contact the supplier for technical support;"
    },
    ODUCN_PM_LCK: {
        alarmName: "ODUCN_PM_LCK",
        alarmLevel: "secondary",
        alarmMeaning: "ODUCN PM signal locking defect",
        reportObject: "ODU",
        errorDescription: "ODUCN PM signal locking defect",
        treatmentSuggestion:
            "Check whether the remote end and the local end are admin down and other locked system configurations. If the alarm still does not clear, unplug/replace the board; if the alarm still does not clear, contact the supplier for technical support;"
    },
    ODU2_PM_OCI: {
        alarmName: "ODU2_PM_OCI",
        alarmLevel: "secondary",
        alarmMeaning: "ODU2 PM disconnect indication",
        reportObject: "ODU",
        errorDescription:
            'ODUk PM segment disconnection indication alarm. ODUk PM segment disconnection indication alarm. When the output is not connected to the input and the value of the STAT byte is "110", this alarm is reported. k represents the level number, and its values ​​are 1, 2, and 5G.',
        treatmentSuggestion:
            "Check the cross-configuration of the local and opposite ends, unplug/replace boards; contact the supplier for technical support;"
    },
    ODUFLEX_PM_OCI: {
        alarmName: "ODUFLEX_PM_OCI",
        alarmLevel: "secondary",
        alarmMeaning: "ODUFLEX PM disconnect indication",
        reportObject: "ODU",
        errorDescription:
            "(1) There is an ODUFlex_PM_OCI alarm on the board corresponding to the upstream site; (2) Loopback is set on the board corresponding to the opposite end station; (3) The corresponding board of the opposite end station is not configured with cross-connection or is configured incorrectly.",
        treatmentSuggestion:
            "Check the cross-configuration of the local and opposite ends, unplug/replace boards; contact the supplier for technical support;"
    },
    ODU4_PM_OCI: {
        alarmName: "ODU4_PM_OCI",
        alarmLevel: "secondary",
        alarmMeaning: "ODU4 PM disconnect indication",
        reportObject: "ODU",
        errorDescription:
            "There is an ODUk_PM_OCI alarm on the board corresponding to the upstream site; loopback is set on the board corresponding to the opposite site; cross-connection is not configured or the configuration is incorrect on the board corresponding to the opposite site.",
        treatmentSuggestion:
            "Check the cross-configuration of the local and opposite ends, unplug/replace boards; contact the supplier for technical support;"
    },
    ODU2_PM_TIM: {
        alarmName: "ODU2_PM_TIM",
        alarmLevel: "secondary",
        alarmMeaning: "ODU2 PM trace identifier mismatch",
        reportObject: "ODU",
        errorDescription: "TTI's mismatch handling process reports trace identifier mismatch alarms.",
        treatmentSuggestion:
            "Check whether the TTI sent by the peer end is consistent with the TTI expected by the local end. Unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    ODUFLEX_PM_TIM: {
        alarmName: "ODUFLEX_PM_TIM",
        alarmLevel: "secondary",
        alarmMeaning: "ODUFLEX PM trace identifier mismatch",
        reportObject: "ODU",
        errorDescription:
            "(1) The networking structure is inconsistent with the TIM detection mode that should be configured; (2) The TTI (Trail Trace Identifier) ​​sent by the peer end is inconsistent with the TTI received by the local end; (3) Fiber connection error; (4) Cross-connection configuration error .",
        treatmentSuggestion:
            "Check whether the TTI sent by the peer end is consistent with the TTI expected by the local end. Unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    ODU4_PM_TIM: {
        alarmName: "ODU4_PM_TIM",
        alarmLevel: "secondary",
        alarmMeaning: "ODU4 PM trace identifier mismatch",
        reportObject: "ODU",
        errorDescription:
            "The networking structure is inconsistent with the TIM detection mode that should be configured; the TTI (Trail Trace Identifier) ​​sent by the peer end is inconsistent with the TTI received by the local end; the fiber connection is wrong; the cross-connection configuration is wrong.",
        treatmentSuggestion:
            "Check whether the TTI sent by the peer end is consistent with the TTI expected by the local end. Unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    ODUCN_PM_TIM: {
        alarmName: "ODUCN_PM_TIM",
        alarmLevel: "secondary",
        alarmMeaning: "ODUCn PM trace identifier mismatch",
        reportObject: "ODU",
        errorDescription:
            "(1) The networking structure is inconsistent with the TIM detection mode that should be configured; (2) The TTI (Trail Trace Identifier) ​​sent by the peer end is inconsistent with the TTI received by the local end; (3) Fiber connection error; (4) Cross-connection configuration error .",
        treatmentSuggestion:
            "Check whether the TTI sent by the peer end is consistent with the TTI expected by the local end. Unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    OPU2_CSF: {
        alarmName: "OPU2_CSF",
        alarmLevel: "secondary",
        alarmMeaning: "Client layer signal failure",
        reportObject: "OPU",
        errorDescription:
            "When the remote client layer signal fails, the system inserts the OPU overhead into the local end and reports an alarm, indicating that the remote client layer signal fails.",
        treatmentSuggestion:
            "Check the working status of the peer device, unplug/replace boards; contact the supplier for technical support;"
    },
    OPU4_CSF: {
        alarmName: "OPU4_CSF",
        alarmLevel: "secondary",
        alarmMeaning: "Client layer signal failure",
        reportObject: "OPU",
        errorDescription: "Client layer signal failure",
        treatmentSuggestion:
            "Check the working status of the peer device, unplug/replace boards; contact the supplier for technical support;"
    },
    OPUFLEX_CSF: {
        alarmName: "OPUFLEX_CSF",
        alarmLevel: "secondary",
        alarmMeaning: "Client layer signal failure",
        reportObject: "OPU",
        errorDescription:
            "When the remote client layer signal fails, the system inserts the OPU overhead into the local end and reports an alarm, indicating that the remote client layer signal fails.",
        treatmentSuggestion:
            "Check the working status of the peer device, unplug/replace boards; contact the supplier for technical support;"
    },
    OPU2_MSIM: {
        alarmName: "OPU2_MSIM",
        alarmLevel: "secondary",
        alarmMeaning: "OPU2 multiplex structure identifier mismatch",
        reportObject: "OPU",
        errorDescription:
            "(1) When the automatic configuration of the multiplexing structure is supported and activated, an MSIM defect is declared if the specific adaptation function does not support the actual receipt of an invalid value of the MSI; (2) When the automatic configuration of the multiplexing structure is not supported or activated, if the actual MSIM defects are declared when receiving MSI does not equal MSI receivable.",
        treatmentSuggestion:
            "Check whether the service configuration types expected by the peer end and the local end are consistent. Unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    OPU4_MSIM: {
        alarmName: "OPU4_MSIM",
        alarmLevel: "secondary",
        alarmMeaning: "OPU4 multiplex structure identifier mismatch",
        reportObject: "OPU",
        errorDescription:
            "The board does not support the multiplexing structure of services sent by the source network element board.",
        treatmentSuggestion:
            "Check whether the service configuration types expected by the peer end and the local end are consistent. Unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    OPUCN_MSIM: {
        alarmName: "OPUCN_MSIM",
        alarmLevel: "secondary",
        alarmMeaning: "OPUCn multiplex structure identifier mismatch",
        reportObject: "OPU",
        errorDescription:
            "The board does not support the multiplexing structure of services sent by the source network element board.",
        treatmentSuggestion:
            "Check whether the service configuration types expected by the peer end and the local end are consistent. Unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    OPU2_PT_MISMATCH: {
        alarmName: "OPU2_PT_MISMATCH",
        alarmLevel: "secondary",
        alarmMeaning: "OPU2 payload type PT mismatch",
        reportObject: "OPU",
        errorDescription:
            "If the accepted payload type is not equal to the expected payload type defined by the adaptation function, a PLM alarm shall be issued.",
        treatmentSuggestion:
            "Check whether the payload type sent by the peer end is consistent with what the local end expects. Unplug/replace the board; contact the supplier for technical support;"
    },
    OPU4_PT_MISMATCH: {
        alarmName: "OPU4_PT_MISMATCH",
        alarmLevel: "secondary",
        alarmMeaning: "OPU4 payload type PT mismatch",
        reportObject: "OPU",
        errorDescription:
            "The setting of PT bytes receivable by the OPU layer of this network element is inconsistent with the actual PT bytes received by the OPU layer.",
        treatmentSuggestion:
            "Check whether the payload type sent by the peer end is consistent with what the local end expects. Unplug/replace the board; contact the supplier for technical support;"
    },
    OPUCN_PT_MISMATCH: {
        alarmName: "OPUCN_PT_MISMATCH",
        alarmLevel: "secondary",
        alarmMeaning: "OPUCn payload type PT mismatch",
        reportObject: "OPU",
        errorDescription:
            "The setting of PT bytes receivable by the OPU layer of this network element is inconsistent with the actual PT bytes received by the OPU layer.",
        treatmentSuggestion:
            "Check whether the payload type sent by the peer end is consistent with what the local end expects. Unplug/replace the board; contact the supplier for technical support;"
    },
    OPUFLEX_PT_MISMATCH: {
        alarmName: "OPUFLEX_PT_MISMATCH",
        alarmLevel: "secondary",
        alarmMeaning: "OPUFlex layer payload type PT mismatch",
        reportObject: "OPU",
        errorDescription:
            "The setting of PT bytes receivable by the OPU layer of this network element is inconsistent with the actual PT bytes received by the OPU layer.",
        treatmentSuggestion:
            "Check whether the payload type sent by the peer end is consistent with what the local end expects. Unplug/replace the board; contact the supplier for technical support;"
    },
    BIP_ERROR: {
        alarmName: "BIP_ERROR",
        alarmLevel: "secondary",
        alarmMeaning: "PCS-BIP too high",
        reportObject: "ETH",
        errorDescription:
            "(1) The attenuation of the received signal is too large and the optical fiber transmission line is abnormal; (2) The sending part of the opposite end station is faulty; (3) The receiving part of this station is faulty.",
        treatmentSuggestion:
            "Check the received optical power; if the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;"
    },
    OTU2_AIS: {
        alarmName: "OTU2_AIS",
        alarmLevel: "hint",
        alarmMeaning: "OTU2 alarm indication signal",
        reportObject: "OTU",
        errorDescription:
            "The client-side input signal includes ODUk_PM_AIS and OTUk_AIS signals; the upstream site has loopback or cross-connection, and the FEC type configuration is incorrect.",
        treatmentSuggestion:
            "Check the FEC type configuration, unplug/replace the board; contact the supplier for technical support;"
    },
    OTU4_AIS: {
        alarmName: "OTU4_AIS",
        alarmLevel: "hint",
        alarmMeaning: "OTU4 alarm indication signal",
        reportObject: "OTU",
        errorDescription:
            "The client-side input signal includes ODUk_PM_AIS and OTUk_AIS signals; the upstream site has loopback or cross-connection, and the FEC type configuration is incorrect.",
        treatmentSuggestion:
            "Check the FEC type configuration, unplug/replace the board; contact the supplier for technical support;"
    },
    OTU2_BDI: {
        alarmName: "OTU2_BDI",
        alarmLevel: "hint",
        alarmMeaning: "OTU2 backward defect indication",
        reportObject: "OTU",
        errorDescription:
            "The client-side input signal contains ODUk_PM_BDI and ODUk_TCMn_BDI alarms; the corresponding OTU board of the downstream station receives LOF, LOM, ODUk_PM_AIS, and ODUk_TCMn_AIS alarms.",
        treatmentSuggestion:
            "Check the transmit optical power of the local end. Check whether the configurations of the local end and the opposite end are consistent. Unplug/replace the board; contact the supplier for technical support;"
    },
    OTU4_BDI: {
        alarmName: "OTU4_BDI",
        alarmLevel: "hint",
        alarmMeaning: "OTU4 backward defect indication",
        reportObject: "OTU",
        errorDescription:
            "The client-side input signal contains ODUk_PM_BDI and ODUk_TCMn_BDI alarms; the corresponding OTU board of the downstream station receives LOF, LOM, ODUk_PM_AIS, and ODUk_TCMn_AIS alarms.",
        treatmentSuggestion:
            "Check the transmit optical power of the local end. Check whether the configurations of the local end and the opposite end are consistent. Unplug/replace the board; contact the supplier for technical support;"
    },
    OTUCN_BDI: {
        alarmName: "OTUCN_BDI",
        alarmLevel: "hint",
        alarmMeaning: "OTUCn backward defect indication",
        reportObject: "OTU",
        errorDescription:
            "The client-side input signal contains ODUCn_PM_BDI; the corresponding OTU board of the downstream station receives LOF, LOM, OTUCn_TIM",
        treatmentSuggestion:
            "Check the transmit optical power of the local end. Check whether the configurations of the local end and the opposite end are consistent. Unplug/replace the board; contact the supplier for technical support;"
    },
    OTU2_DEG: {
        alarmName: "OTU2_DEG",
        alarmLevel: "secondary",
        alarmMeaning: "OTU2 signal degradation",
        reportObject: "OTU",
        errorDescription: "The local optical port is faulty; the optical fiber transmission line is abnormal.",
        treatmentSuggestion:
            "Check the received optical power, clean the optical module connector, unplug/replace the board; contact the supplier for technical support;"
    },
    OTU4_DEG: {
        alarmName: "OTU4_DEG",
        alarmLevel: "secondary",
        alarmMeaning: "OTU4 signal degradation",
        reportObject: "OTU",
        errorDescription: "The local optical port is faulty; the optical fiber transmission line is abnormal.",
        treatmentSuggestion:
            "Check the received optical power, clean the optical module connector, unplug/replace the board; contact the supplier for technical support;"
    },
    OTUCN_DEG: {
        alarmName: "OTUCN_DEG",
        alarmLevel: "secondary",
        alarmMeaning: "OTUCn signal degradation",
        reportObject: "OTU",
        errorDescription: "The local optical port is faulty; the optical fiber transmission line is abnormal.",
        treatmentSuggestion:
            "Check the received optical power, clean the optical module connector, unplug/replace the board; contact the supplier for technical support;"
    },
    OTU2_LOF: {
        alarmName: "OTU2_LOF",
        alarmLevel: "urgent",
        alarmMeaning: "OTU2 frame lost",
        reportObject: "OTU",
        errorDescription:
            "The signal sent by the corresponding board at the opposite end has no frame structure; the input optical power is abnormal; the FEC type setting is incorrect; the optical fiber transmission line is abnormal.",
        treatmentSuggestion:
            "Check whether the local and peer configurations are consistent. Unplug/replace boards; contact the supplier for technical support;"
    },
    OTU4_LOF: {
        alarmName: "OTU4_LOF",
        alarmLevel: "urgent",
        alarmMeaning: "OTU4 frame lost",
        reportObject: "OTU",
        errorDescription:
            "The signal sent by the corresponding board at the opposite end has no frame structure; the input optical power is abnormal; the FEC type setting is incorrect; the optical fiber transmission line is abnormal.",
        treatmentSuggestion:
            "Check whether the local and peer configurations are consistent. Unplug/replace boards; contact the supplier for technical support;"
    },
    OTUCN_LOF: {
        alarmName: "OTUCN_LOF",
        alarmLevel: "urgent",
        alarmMeaning: "OTUCn frame loss",
        reportObject: "OTU",
        errorDescription:
            "The signal sent by the corresponding board at the opposite end has no frame structure; the input optical power is abnormal; the FEC type setting is incorrect; the optical fiber transmission line is abnormal.",
        treatmentSuggestion:
            "Check whether the local and peer configurations are consistent. Unplug/replace boards; contact the supplier for technical support;"
    },
    OTU2_LOM: {
        alarmName: "OTU2_LOM",
        alarmLevel: "main",
        alarmMeaning: "OTU2 multiframe loss",
        reportObject: "OTU",
        errorDescription:
            "The FEC type setting is incorrect; the signal received by the OTU board of the local station has too many bit errors; the single board of the local station is faulty.",
        treatmentSuggestion:
            "Check whether the local and peer configurations are consistent. Unplug/replace boards; contact the supplier for technical support;"
    },
    OTU4_LOM: {
        alarmName: "OTU4_LOM",
        alarmLevel: "main",
        alarmMeaning: "OTU4 multiframe loss",
        reportObject: "OTU",
        errorDescription:
            "The FEC type setting is incorrect; the signal received by the OTU board of the local station has too many bit errors; the single board of the local station is faulty.",
        treatmentSuggestion:
            "Check whether the local and peer configurations are consistent. Unplug/replace boards; contact the supplier for technical support;"
    },
    OTUCN_LOM: {
        alarmName: "OTUCN_LOM",
        alarmLevel: "main",
        alarmMeaning: "OTUCn multiframe loss",
        reportObject: "OTU",
        errorDescription:
            "The FEC type setting is incorrect; the signal received by the OTU board of the local station has too many bit errors; the single board of the local station is faulty.",
        treatmentSuggestion:
            "Check whether the local and peer configurations are consistent. Unplug/replace boards; contact the supplier for technical support;"
    },
    OTU2_SSF: {
        alarmName: "OTU2_SSF",
        alarmLevel: "hint",
        alarmMeaning: "OTU2 service layer signal failure",
        reportObject: "OTU",
        errorDescription: "If the LOS/OTU2_AIS/OTU2_LOF/OTU2_LOM alarm exists, the OTU2_SSF alarm is generated.",
        treatmentSuggestion:
            "Check the received optical power, check whether the local and opposite end configurations are consistent, unplug/replace the board; contact the supplier for technical support;"
    },
    OTU4_SSF: {
        alarmName: "OTU4_SSF",
        alarmLevel: "hint",
        alarmMeaning: "OTU4 service layer signal failure",
        reportObject: "OTU",
        errorDescription: "If the LOS/OTU4_AIS/OTU4_LOF/OTU4_LOM alarm exists, the OTU4_SSF alarm is generated.",
        treatmentSuggestion:
            "Check the received optical power, check whether the local and opposite end configurations are consistent, unplug/replace the board; contact the supplier for technical support;"
    },
    OTUCN_SSF: {
        alarmName: "OTUCN_SSF",
        alarmLevel: "hint",
        alarmMeaning: "OTUCn service layer signal fails",
        reportObject: "OTU",
        errorDescription: "If the LOS/OTUCn_LOF/OTUCn_LOM alarm exists, the OTUCn_SSF alarm is generated.",
        treatmentSuggestion:
            "Check the received optical power, check whether the local and opposite end configurations are consistent, unplug/replace the board; contact the supplier for technical support;"
    },
    OTU2_TIM: {
        alarmName: "OTU2_TIM",
        alarmLevel: "secondary",
        alarmMeaning: "OTU2 trace identifier mismatch",
        reportObject: "OTU",
        errorDescription: "TTI's mismatch handling process reports trace identifier mismatch alarms.",
        treatmentSuggestion:
            "Check whether the TTI sent by the peer end is consistent with the TTI expected by the local end. Unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    OTU4_TIM: {
        alarmName: "OTU4_TIM",
        alarmLevel: "secondary",
        alarmMeaning: "OTU4 trace identifier mismatch",
        reportObject: "OTU",
        errorDescription:
            "The networking structure is inconsistent with the TIM detection mode that should be configured; the TTI (Trail Trace Identifier) ​​sent by the peer end is inconsistent with the TTI received by the local end; the fiber connection is wrong; the cross-connection configuration is wrong.",
        treatmentSuggestion:
            "Check whether the TTI sent by the peer end is consistent with the TTI expected by the local end. Unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    OTUCN_TIM: {
        alarmName: "OTUCN_TIM",
        alarmLevel: "secondary",
        alarmMeaning: "OTUCn trace identifier mismatch",
        reportObject: "OTU",
        errorDescription:
            "The networking structure is inconsistent with the TIM detection mode that should be configured; the TTI (Trail Trace Identifier) ​​sent by the peer end is inconsistent with the TTI received by the local end; the fiber connection is wrong; the cross-connection configuration is wrong.",
        treatmentSuggestion:
            "Check whether the TTI sent by the peer end is consistent with the TTI expected by the local end. Unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    B1_EXC: {
        alarmName: "B1_EXC",
        alarmLevel: "secondary",
        alarmMeaning: "Regeneration section (B1) bit error exceeds limit",
        reportObject: "SDH",
        errorDescription:
            "(1) The attenuation of the received signal is too large; (2) The optical fiber head is not clean; (3) The optical fiber connector is loose or not plugged in properly; (4) The attenuation value of the optical attenuator is too large or too small; (5) The opposite end station Partial failure in sending; (6) Partial failure in receiving at this station.",
        treatmentSuggestion:
            "Check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;"
    },
    TCA_FEC_PRE_H_15MIN: {
        alarmName: "TCA_FEC_PRE_H_15MIN",
        alarmLevel: "main",
        alarmMeaning:
            "The FEC bit error rate before error correction exceeds the limit for 15 minutes and is too high.",
        reportObject: "OTU",
        errorDescription:
            "(1) The input optical power of the single board is too high or too low; (2) System performance is degraded, such as low OSNR, dispersion, nonlinearity, etc.; (3) Single board failure of the opposite station; (4) Single board failure of the local station ; Note: The operator needs to pay attention immediately. Although there is currently no business impact, there are hidden dangers to the long-term stable operation of the business. Business commissioning needs to be carried out as soon as possible.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_FEC_PRE_H_24H: {
        alarmName: "TCA_FEC_PRE_H_24H",
        alarmLevel: "main",
        alarmMeaning: "The FEC bit error rate exceeds the limit for 24 hours before error correction.",
        reportObject: "OTU",
        errorDescription:
            "(1) The input optical power of the single board is too high or too low; (2) System performance is degraded, such as low OSNR, dispersion, nonlinearity, etc.; (3) Single board failure of the opposite station; (4) Single board failure of the local station ; Note: The operator needs to pay attention immediately. Although there is currently no business impact, there are hidden dangers to the long-term stable operation of the business. Business commissioning needs to be carried out as soon as possible.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_FEC_POST_H_15MIN: {
        alarmName: "TCA_FEC_POST_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "After error correction, the FEC bit error rate exceeds the limit in 15 minutes.",
        reportObject: "OTU",
        errorDescription:
            "(1) The input optical power of the single board is too high or too low; (2) System performance is degraded, such as low OSNR, dispersion, nonlinearity, etc.; (3) Single board failure of the opposite station; (4) Single board failure of the local station ;",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_FEC_POST_H_24H: {
        alarmName: "TCA_FEC_POST_H_24H",
        alarmLevel: "main",
        alarmMeaning: "After error correction, the FEC bit error rate exceeds the limit in 24 hours.",
        reportObject: "OTU",
        errorDescription:
            "(1) The input optical power of the single board is too high or too low; (2) System performance is degraded, such as low OSNR, dispersion, nonlinearity, etc.; (3) Single board failure of the opposite station; (4) Single board failure of the local station ;",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_ETH_CRC_Tx_ERR_H_15MIN: {
        alarmName: "TCA_ETH_CRC_Tx_ERR_H_15MIN",
        alarmLevel: "secondary",
        alarmMeaning: "The CRC error packet sent exceeds the limit within 15 minutes and is too high.",
        reportObject: "ETH",
        errorDescription:
            "(1) Partial failure of the remote station’s transmission (2) Partial failure of the local station’s transmission.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_ETH_CRC_Tx_ERR_H_24H: {
        alarmName: "TCA_ETH_CRC_Tx_ERR_H_24H",
        alarmLevel: "secondary",
        alarmMeaning: "The CRC error packet sent exceeds the 24-hour limit and is too high.",
        reportObject: "ETH",
        errorDescription:
            "(1) Partial failure of the remote station’s transmission (2) Partial failure of the local station’s transmission.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_ETH_CRC_Rx_ERR_H_15MIN: {
        alarmName: "TCA_ETH_CRC_Rx_ERR_H_15MIN",
        alarmLevel: "secondary",
        alarmMeaning: "The number of CRC error packets received within 15 minutes exceeds the limit and is too high.",
        reportObject: "ETH",
        errorDescription:
            "(1) The attenuation of the received signal is too large and the optical fiber transmission line is abnormal; (2) The sending part of the opposite end station is faulty; (3) The receiving part of this station is faulty.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_ETH_CRC_Rx_ERR_H_24H: {
        alarmName: "TCA_ETH_CRC_Rx_ERR_H_24H",
        alarmLevel: "secondary",
        alarmMeaning: "The received CRC error packet exceeds the 24-hour limit and is too high.",
        reportObject: "ETH",
        errorDescription:
            "(1) The attenuation of the received signal is too large and the optical fiber transmission line is abnormal; (2) The sending part of the opposite end station is faulty; (3) The receiving part of this station is faulty.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    "TCA_ETH_PCS-BIP_Tx_ERR_H_15MIN": {
        alarmName: "TCA_ETH_PCS-BIP_Tx_ERR_H_15MIN",
        alarmLevel: "secondary",
        alarmMeaning: "Send PCS-BIP error 15 minutes too high",
        reportObject: "ETH",
        errorDescription:
            "(1) Partial failure of the remote station’s transmission (2) Partial failure of the local station’s transmission.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    "TCA_ETH_PCS-BIP_Tx_ERR_H_24H": {
        alarmName: "TCA_ETH_PCS-BIP_Tx_ERR_H_24H",
        alarmLevel: "secondary",
        alarmMeaning: "Send PCS-BIP error 24 hours too high",
        reportObject: "ETH",
        errorDescription:
            "(1) Partial failure of the remote station’s transmission (2) Partial failure of the local station’s transmission.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    "TCA_ETH_PCS-BIP_Rx_ERR_H_15MIN": {
        alarmName: "TCA_ETH_PCS-BIP_Rx_ERR_H_15MIN",
        alarmLevel: "secondary",
        alarmMeaning: "Receive PCS-BIP error 15 minutes too high",
        reportObject: "ETH",
        errorDescription:
            "(1) The attenuation of the received signal is too large and the optical fiber transmission line is abnormal; (2) The sending part of the opposite end station is faulty; (3) The receiving part of this station is faulty. This alarm is reported when the 40GE/100GE bit error exceeds the set threshold. When the error type is burst mode, only the ETH_BIP8_SD alarm is generated when the error exceeds the limit, and this alarm is not generated.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    "TCA_ETH_PCS-BIP_Rx_ERR_H_24H": {
        alarmName: "TCA_ETH_PCS-BIP_Rx_ERR_H_24H",
        alarmLevel: "secondary",
        alarmMeaning: "Receive PCS-BIP error 24 hours too high",
        reportObject: "ETH",
        errorDescription:
            "(1) The attenuation of the received signal is too large and the optical fiber transmission line is abnormal; (2) The sending part of the opposite end station is faulty; (3) The receiving part of this station is faulty. This alarm is reported when the 40GE/100GE bit error exceeds the set threshold. When the error type is burst mode, only the ETH_BIP8_SD alarm is generated when the error exceeds the limit, and this alarm is not generated.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; check the received optical power. If the alarm is not cleared, clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    J0_MISMATCH: {
        alarmName: "J0_MISMATCH",
        alarmLevel: "secondary",
        alarmMeaning: "J0 mismatch",
        reportObject: "SDH",
        errorDescription:
            "(1) The J0 byte that the opposite end should send is inconsistent with the J0 byte that the local end should receive; (2) The business connection error.",
        treatmentSuggestion:
            "Check whether the J0 sent by the peer end is consistent with the J0 expected by the local end. Unplug/replace the module; unplug/replace the board; contact the supplier for technical support;"
    },
    ETH_LOS: {
        alarmName: "ETH_LOS",
        alarmLevel: "urgent",
        alarmMeaning: "Ethernet physical port signal loss alarm",
        reportObject: "ETH",
        errorDescription: "(1) The local device receives abnormally",
        treatmentSuggestion:
            "Check the received optical power and clean the optical fiber connector; unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    ETH_LOS_SYN: {
        alarmName: "ETH_LOS_SYN",
        alarmLevel: "urgent",
        alarmMeaning: "Synchronization loss alarm",
        reportObject: "ETH",
        errorDescription:
            "(1) The signal type or rate sent by the peer device is inconsistent with the configuration of the station; (2) Link failure; (3) The peer device fails.",
        treatmentSuggestion:
            "Check whether the business configurations of the opposite end and the local end are consistent; check the working status of the opposite end equipment; unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    LINK_LF: {
        alarmName: "LINK_LF",
        alarmLevel: "secondary",
        alarmMeaning: "Connection local failure alarm",
        reportObject: "ETH",
        errorDescription: "Ethernet near-end defect indication",
        treatmentSuggestion:
            "Check the working status of the peer device, unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    MS_AIS: {
        alarmName: "MS_AIS",
        alarmLevel: "main",
        alarmMeaning: "Multiplex section alarm indication",
        reportObject: "SDH",
        errorDescription:
            "(1) There are higher-level alarms in the system, such as R_LOS and R_LOF; (2) The opposite station’s primary and backup cross-connect clock boards are not in place; (3) The opposite station’s cross-connect clock board is faulty; (4) The opposite station’s sending part Fault; (5) This board receives part of the fault.",
        treatmentSuggestion:
            "Check the working status of the peer device. Check whether the peer and local service configurations are consistent. Check the received optical power. Unplug/replace the module; unplug/replace the board; contact the supplier for technical support;"
    },
    MS_RDI: {
        alarmName: "MS_RDI",
        alarmLevel: "secondary",
        alarmMeaning: "Multiplex section remote reception failure indication",
        reportObject: "SDH",
        errorDescription:
            "(1) The opposite end station receives R_LOS, R_LOC, R_LOF, MS_AIS, B2_EXC and B2_SD signals; (2) The opposite end station receives partial failure; (3) The local station transmits partial failure.",
        treatmentSuggestion:
            "Check whether the peer and local service configurations are consistent. Check the transmit optical power. Unplug/replace the module; unplug/replace the board; contact the supplier for technical support;"
    },
    RS_LOF: {
        alarmName: "RS_LOF",
        alarmLevel: "urgent",
        alarmMeaning: "RS frame loss alarm",
        reportObject: "SDH",
        errorDescription:
            "(1) The optical fiber is misconnected; (2) The received optical power is abnormal; (3) The signal sent by the opposite end station has no frame structure; (4) The receiving direction of the station is faulty; (5) The upstream fiber is broken and the received signal is noise; (6) ) The upstream single-board laser is turned off, and the received signal is noise.",
        treatmentSuggestion:
            "Check the working status of the peer device; check whether the business configurations of the peer and local devices are consistent; unplug/replace modules; unplug/replace boards; contact the supplier for technical support;"
    },
    LINK_RF: {
        alarmName: "LINK_RF",
        alarmLevel: "secondary",
        alarmMeaning: "Remote connection failure alarm",
        reportObject: "ETH",
        errorDescription: "Ethernet remote defect indication",
        treatmentSuggestion:
            "Check whether the peer and local service configurations are consistent. Check the transmit optical power. Unplug/replace the module; unplug/replace the board; contact the supplier for technical support;"
    },
    TCA_Q_VALUE_L_15MIN: {
        alarmName: "TCA_Q_VALUE_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Q value exceeds the limit in 15 minutes and is too low",
        reportObject: "OTU",
        errorDescription:
            "The FEC pre-correction error code exceeds the limit, causing the Q value to exceed the limit.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, check the received optical power and clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_Q_VALUE_L_24H: {
        alarmName: "TCA_Q_VALUE_L_24H",
        alarmLevel: "main",
        alarmMeaning: "The Q value exceeds the 24-hour limit and is too low",
        reportObject: "OTU",
        errorDescription:
            "The FEC pre-correction error code exceeds the limit, causing the Q value to exceed the limit.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, check the received optical power and clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_OSNR_VALUE_L_15MIN: {
        alarmName: "TCA_OSNR_VALUE_L_15MIN",
        alarmLevel: "main",
        alarmMeaning: "OSNR exceeds the limit in 15 minutes and is too low",
        reportObject: "OTU",
        errorDescription: "The FEC pre-correction bit error exceeds the limit, causing the OSNR to exceed the limit.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, check the received optical power and clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_OSNR_VALUE_L_24H: {
        alarmName: "TCA_OSNR_VALUE_L_24H",
        alarmLevel: "main",
        alarmMeaning: "OSNR 24 hours crosses the limit and is too low",
        reportObject: "OTU",
        errorDescription: "The FEC pre-correction bit error exceeds the limit, causing the OSNR to exceed the limit.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, check the received optical power and clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_RS_H_15MIN: {
        alarmName: "TCA_RS_H_15MIN",
        alarmLevel: "secondary",
        alarmMeaning: "The regeneration section performance exceeded the limit for 15 minutes and was too high.",
        reportObject: "SDH",
        errorDescription:
            "(1) The performance of the laser of the opposite end station is degraded; (2) The optical power received by this station is too high or too low; (3) The clock performance of the opposite end station is degraded; (4) The performance of the optical fiber is degraded.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, check the received optical power and clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_RS_H_24H: {
        alarmName: "TCA_RS_H_24H",
        alarmLevel: "secondary",
        alarmMeaning: "Regeneration section performance exceeds the limit for 24 hours and is too high",
        reportObject: "SDH",
        errorDescription:
            "(1) The performance of the laser of the opposite end station is degraded; (2) The optical power received by this station is too high or too low; (3) The clock performance of the opposite end station is degraded; (4) The performance of the optical fiber is degraded.",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, check the received optical power and clean the optical fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    RS_DEG: {
        alarmName: "RS_DEG",
        alarmLevel: "main",
        alarmMeaning: "Regeneration section DEG alarm",
        reportObject: "SDH",
        errorDescription:
            "(1) The attenuation of the received signal is too large; (2) The optical fiber head is not clean; (3) The optical fiber connector is loose or not plugged in properly; (4) The attenuation value of the optical attenuator is too large or too small; (5) The opposite end station Partial failure in sending; (6) Partial failure in receiving at this station.",
        treatmentSuggestion:
            "Check the received optical power. Clean the optical fiber connector. Unplug/replace the module. Unplug/replace the board. Contact the supplier for technical support."
    },
    TCA_ETH_IN_PKT_ERR_H_15MIN: {
        alarmName: "TCA_ETH_IN_PKT_ERR_H_15MIN",
        alarmLevel: "secondary",
        alarmMeaning: "Alarm for receiving bad packets exceeding the limit for 15 minutes",
        reportObject: "ETH",
        errorDescription: "Receiving bad packets exceeds the limit",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_ETH_IN_PKT_ERR_H_24H: {
        alarmName: "TCA_ETH_IN_PKT_ERR_H_24H",
        alarmLevel: "secondary",
        alarmMeaning: "Alarm for receiving bad packets exceeding the limit for 24 hours",
        reportObject: "ETH",
        errorDescription: "Receiving bad packets exceeds the limit",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_BBE_H_15MIN: {
        alarmName: "TCA_BBE_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Background block bit error exceeds the limit alarm for 15 minutes",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "Background block error limit exceeded",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_BBE_H_24H: {
        alarmName: "TCA_BBE_H_24H",
        alarmLevel: "main",
        alarmMeaning: "Background block error 24-hour limit alarm",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "Background block error limit exceeded",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_ES_H_15MIN: {
        alarmName: "TCA_ES_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Errored seconds exceeding the limit of 15 minutes alarm",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "Errored second limit exceeded",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_ES_H_24H: {
        alarmName: "TCA_ES_H_24H",
        alarmLevel: "main",
        alarmMeaning: "Errored second 24-hour limit alarm",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "Errored second limit exceeded",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_SES_H_15MIN: {
        alarmName: "TCA_SES_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Seriously errored second 15 minutes exceeded limit alarm",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "Seriously errored seconds exceeded",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_SES_H_24H: {
        alarmName: "TCA_SES_H_24H",
        alarmLevel: "main",
        alarmMeaning: "Seriously errored second 24-hour limit alarm",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "Seriously errored seconds exceeded",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_SESR_H_15MIN: {
        alarmName: "TCA_SESR_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Seriously errored second ratio 15 minutes exceeds the limit alarm",
        reportObject: "ODU、OTU",
        errorDescription: "Seriously errored second ratio exceeds the limit",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_SESR_H_24H: {
        alarmName: "TCA_SESR_H_24H",
        alarmLevel: "main",
        alarmMeaning: "Seriously errored seconds ratio 24 hours exceeds the limit alarm",
        reportObject: "ODU、OTU",
        errorDescription: "Seriously errored second ratio exceeds the limit",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_UAS_H_15MIN: {
        alarmName: "TCA_UAS_H_15MIN",
        alarmLevel: "main",
        alarmMeaning: "Unavailable second 15 minutes exceeds the limit alarm",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "Unavailable second limit exceeded",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    },
    TCA_UAS_H_24H: {
        alarmName: "TCA_UAS_H_24H",
        alarmLevel: "main",
        alarmMeaning: "Unavailable second 24-hour limit violation alarm",
        reportObject: "ODU、OTU、OSC",
        errorDescription: "Unavailable second limit exceeded",
        treatmentSuggestion:
            'Check whether the TCA threshold is set appropriately; click "Clear TCA" on the network management; if the alarm is not cleared, clean the fiber connector; unplug/replace the module; unplug/replace the board; contact the supplier for technical support;'
    }
};
