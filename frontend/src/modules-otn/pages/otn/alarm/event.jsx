import {useSelector} from "react-redux";
import {useDeferredValue, useEffect, useState} from "react";
import dayjs from "dayjs";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {filterEvents, sortArr} from "@/modules-otn/utils/util";
import {objectGet} from "@/modules-otn/apis/api";
import {Col, Form, Row} from "antd";
import SelectLoading from "@/modules-otn/components/common/select_loading";

const Events = ({serviceType, head}) => {
    const {events} = useSelector(state => state.notification);
    const {neNameMap} = useSelector(state => state.neName);
    const {neTypeMap} = useSelector(state => state.neName);
    const {labelList} = useSelector(state => state.language);
    const [data, setData] = useState([]);
    const deferredData = useDeferredValue(data, {timeoutMs: 1000});
    const {tableFilter} = useSelector(state => state.map);
    const [showState, setShowState] = useState(false);
    const [loading, setLoading] = useState(false);

    const [form] = Form.useForm();

    const [selectGroup, setSelectGroup] = useState();
    const [cardResource, setCardResource] = useState([]);
    const [filterData, setFilterData] = useState([]);
    const [filterCardData, setFilterCardData] = useState([]);
    const [filterPortData, setFilterPortData] = useState([]);

    const filterResource = (data, ne) => {
        const resource = data.reduce((prev, v) => {
            const [, chassis, slot] = v.resource.split("-");
            const card = slot ? `SLOT-${chassis}-${slot}` : null;
            if (card && v.name === ne && !prev.includes(card)) {
                prev.push(card);
            }
            return prev;
        }, []);
        return resource;
    };
    const filterCard = (data, card) =>
        data.filter(alarm => {
            const [, chassis, slot] = alarm.resource.split("-");
            const [, cardChassis, cardSlot] = card.split("-");
            return (!slot && alarm.resource === card) || (chassis === cardChassis && slot === cardSlot);
        });

    useEffect(() => {
        const {ne, card, port} = form.getFieldsValue();
        if (ne && card && port) {
            const resource = filterResource(deferredData, ne);
            const _cardData = [];
            const portData = deferredData.reduce((prev, alarm) => {
                if (alarm.name === ne) {
                    _cardData.push(alarm);
                    const [, , R2, R3] = alarm.resource.split("-");
                    const [, , P2, P3] = port.split("-");
                    if (R3 === P3 && R2 === P2) prev.push(alarm);
                }
                return prev;
            }, []);
            const cardData = filterCard(_cardData, card);
            setCardResource(resource);
            setFilterCardData(_cardData);
            setFilterPortData(cardData);
            setFilterData(portData);
        } else if (ne && card) {
            const resource = filterResource(deferredData, ne);
            const _cardData = deferredData.reduce((prev, alarm) => {
                if (alarm.name === ne) {
                    prev.push(alarm);
                }
                return prev;
            }, []);
            const cardData = filterCard(_cardData, card);
            setCardResource(resource);
            setFilterCardData(_cardData);
            setFilterData(cardData);
        } else if (ne) {
            const resource = filterResource(deferredData, ne);
            const cardData = deferredData.reduce((prev, alarm) => {
                if (alarm.name === ne) {
                    prev.push(alarm);
                }
                return prev;
            }, []);
            setFilterCardData(cardData);
            setFilterData(cardData);
            setCardResource(resource);
        } else {
            setFilterData(deferredData);
        }
    }, [deferredData]);

    const updateData = newEvents => {
        try {
            const _data = [];
            newEvents.forEach(item => {
                if (neTypeMap[item.ne_id] === "5") {
                    _data.push({
                        ...item,
                        key: item.id,
                        name: neNameMap[item.ne_id],
                        "time-created": dayjs(item["time-created"] / 1000000).format("YYYY/MM/DD HH:mm:ss")
                    });
                }
            });
            setData(_data);
        } catch (e) {
            setData([]);
        }
    };

    const loadData = () => {
        try {
            if (showState) {
                setLoading(true);
                objectGet("nms:event", {}).then(res => {
                    updateData(
                        res.documents.map(item => {
                            return {
                                ...item.value.data,
                                ne_id: item.value.ne_id
                            };
                        })
                    );
                    setTimeout(() => {
                        setLoading(false);
                    }, 1000);
                });
                return;
            }
            let newEvents = events;
            if (head === false) {
                if (tableFilter?.type === "NODE_NE") {
                    newEvents = filterEvents(newEvents, tableFilter);
                } else if (tableFilter?.type === "NODE_GROUP" && tableFilter.idList.length > 0) {
                    newEvents = newEvents.filter(item => tableFilter.idList.includes(item.ne_id));
                } else if (
                    tableFilter?.type &&
                    ["ots", "oms", "och", "client"].includes(tableFilter?.type) &&
                    tableFilter.servicePortList
                ) {
                    const filterKeys = [];
                    const fiterFun = (ne_id, keyStr, eventList) => {
                        const relateInfo = keyStr.replace(/\w+-/, "-");
                        if (!filterKeys.includes(`${ne_id}_${relateInfo}`)) {
                            filterKeys.push(`${ne_id}_${relateInfo}`);
                            const regExp = new RegExp(`^[A-z]+${relateInfo}$`);
                            newEvents.map(i => {
                                if (i.ne_id === ne_id && regExp.test(i.resource)) {
                                    eventList.push(i);
                                }
                            });
                        }
                    };
                    const _events = [];
                    tableFilter.servicePortList.map(item => {
                        fiterFun(item.ne_id, "CHASSIS-1", _events); // ne
                        fiterFun(item.ne_id, item.card, _events); // card
                        if (item.ports) {
                            item.ports.map(port => {
                                let _port = port;
                                if (typeof port !== "string") {
                                    [_port] = Object.values(port) ?? [];
                                }
                                fiterFun(item.ne_id, _port, _events); // port
                                if (_port.endsWith("OUT") || _port.endsWith("IN")) {
                                    fiterFun(item.ne_id, _port.replace(/OUT|IN/, ""), _events); // port
                                }
                            });
                        }
                    });
                    newEvents = _events;
                } else if (serviceType) {
                    newEvents = newEvents.filter(item => neTypeMap[item.ne_id] === "5");
                }
            }
            updateData(newEvents);
        } catch (e) {
            updateData([]);
        }
    };

    useEffect(() => {
        loadData();
    }, [events, neNameMap, tableFilter, showState]);

    return (
        <div style={{width: "100%", height: "100%"}}>
            {head && (
                <>
                    <div
                        style={{
                            fontSize: "16px",
                            fontWeight: 600,
                            height: "20px",
                            marginTop: "8px",
                            paddingLeft: "15px"
                        }}
                    >
                        {showState ? labelList.history_event : labelList.current_event}
                    </div>
                    <div style={{display: "flex", padding: 8, height: "40px"}}>
                        <Form form={form}>
                            <Row>
                                <Col>
                                    <Form.Item name="group" label={labelList.group}>
                                        <SelectLoading
                                            style={{width: 240}}
                                            placeholder={labelList.select_group}
                                            allowClear
                                            fetchData={() => {
                                                return objectGet("nms:group", {}).then(rs => {
                                                    const {apiResult, documents, apiMessage} = rs;
                                                    if (apiResult === "fail") {
                                                        throw new Error(apiMessage);
                                                    }
                                                    const options = sortArr(documents, ["value", "name"]).map(item => ({
                                                        label: item.value.name,
                                                        value: item.id
                                                    }));
                                                    return options;
                                                });
                                            }}
                                            onChange={id => {
                                                if (form.getFieldsValue()?.ne) {
                                                    form.setFieldsValue({
                                                        ...form.getFieldsValue(),
                                                        ne: undefined,
                                                        card: undefined
                                                    });
                                                    setCardResource([]);
                                                    setFilterCardData(data);
                                                    setFilterData(data);
                                                }
                                                setSelectGroup(id);
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col style={{marginLeft: 10}}>
                                    <Form.Item name="ne" label={labelList.ne}>
                                        <SelectLoading
                                            style={{width: 240}}
                                            allowClear
                                            placeholder={labelList.select_ne}
                                            dependence={selectGroup}
                                            fetchData={() => {
                                                return objectGet(
                                                    "config:ne",
                                                    selectGroup ? {group: selectGroup} : {}
                                                ).then(rs => {
                                                    const {apiResult, documents, apiMessage} = rs;
                                                    if (apiResult === "fail") {
                                                        throw new Error(apiMessage);
                                                    }
                                                    const options = sortArr(documents, ["value", "name"]).map(item => ({
                                                        label: item.value.name,
                                                        value: item.value.name,
                                                        key: item.value.ne_id
                                                    }));
                                                    return options;
                                                });
                                            }}
                                            onChange={(ne_id, o) => {
                                                if (form.getFieldsValue()?.card) {
                                                    form.setFieldsValue({
                                                        ...form.getFieldsValue(),
                                                        card: undefined,
                                                        port: undefined
                                                    });
                                                }
                                                if (ne_id) {
                                                    const resource = filterResource(deferredData, o.label);
                                                    const cardData = deferredData.reduce((prev, alarm) => {
                                                        if (alarm.name === o.label) {
                                                            prev.push(alarm);
                                                        }
                                                        return prev;
                                                    }, []);
                                                    setFilterCardData(cardData);
                                                    setFilterData(cardData);
                                                    setCardResource(resource);
                                                } else {
                                                    setCardResource([]);
                                                    setFilterPortData([]);
                                                    setFilterCardData(data);
                                                    setFilterData(data);
                                                }
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col style={{marginLeft: 10}}>
                                    <Form.Item name="card" label={labelList.slot}>
                                        <SelectLoading
                                            style={{width: 240}}
                                            allowClear
                                            placeholder={labelList.please_select}
                                            dependence={filterCardData}
                                            fetchData={() => {
                                                const options = cardResource
                                                    .sort((a, b) => a.localeCompare(b, "ZH-CN", {numeric: true}))
                                                    .map(item => ({
                                                        label: item,
                                                        value: item
                                                    }));
                                                return options;
                                            }}
                                            onChange={card => {
                                                if (form.getFieldsValue()?.port) {
                                                    form.setFieldsValue({...form.getFieldsValue(), port: undefined});
                                                }
                                                if (card) {
                                                    const cardData = filterCard(filterCardData, card);
                                                    setFilterData(cardData);
                                                    setFilterPortData(cardData);
                                                } else {
                                                    setFilterData(filterCardData);
                                                    setFilterPortData([]);
                                                }
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col style={{marginLeft: 10}}>
                                    <Form.Item name="port" label={labelList.port}>
                                        <SelectLoading
                                            style={{width: 240}}
                                            allowClear
                                            placeholder={labelList.please_select}
                                            dependence={filterPortData}
                                            fetchData={() => {
                                                const options = filterPortData
                                                    .reduce((pre, item) => {
                                                        const ports = item.resource?.split("-");
                                                        ports[0] = "PORT";
                                                        const port = ports.join("-");
                                                        if (
                                                            ports?.[3] &&
                                                            !ports?.[4] &&
                                                            !pre.find(f => f.value === port)
                                                        ) {
                                                            pre.push({label: port, value: port});
                                                        }
                                                        return pre;
                                                    }, [])
                                                    .sort((a, b) =>
                                                        a.label.localeCompare(b.label, "ZH-CN", {numeric: true})
                                                    );
                                                return options;
                                            }}
                                            onChange={port => {
                                                if (port) {
                                                    const portData = filterPortData.filter(
                                                        alarm => alarm.resource?.split("-")?.[3] === port.split("-")[3]
                                                    );
                                                    setFilterData(portData);
                                                } else {
                                                    setFilterData(filterPortData);
                                                }
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Form>
                    </div>
                </>
            )}
            <div style={{width: "100%", height: head ? "calc(100% - 70px)" : "100%"}}>
                <CustomTable
                    type="allEvent"
                    initTitle={false}
                    initHead={head}
                    refreshParent={loadData}
                    initDataSource={head ? filterData : deferredData}
                    loading={loading}
                    buttons={[
                        {
                            label: showState ? "current" : "history",
                            onClick() {
                                setShowState(!showState);
                            }
                        }
                    ]}
                    columnFormat={{
                        resource: r => r
                    }}
                />
            </div>
        </div>
    );
};

export default Events;
