import React, {useEffect, useState} from "react";
import {Card} from "antd";
import {getDashboardData} from "@/modules-otn/apis/api";
import {useSelector} from "react-redux";
import Icon from "@ant-design/icons";
import {criticalIcon, majorIcon, minorIcon, warningIcon} from "@/modules-otn/utils/iconSvg";
import {fetchStaticHistory} from "@/modules-ampcon/apis/dashboard_api";
import {getDCP920List} from "@/modules-ampcon/apis/dcp920";
import {Doughnutchart, PlusDoughnutchart, BarEcharts, MultiLineChart} from "./echarts_common";
import styles from "./new_otn_view.module.scss";
import Alarm from "../otn/alarm/alarm";

const OtnView = () => {
    const [statisticsData, setStatisticsData] = useState({
        serverCpuData: [],
        serverMemData: [],
        serverDiskData: [],
        loading: true
    });
    const {alarms} = useSelector(state => state.notification);
    const [otnViewData, setOtnViewData] = useState();
    const [cardSeriesData, setCardSeriesData] = useState();
    const [neSeriesData, setNeSeriesData] = useState();
    const alarmRightImg = [criticalIcon, majorIcon, minorIcon, warningIcon];
    const alarmRightText = ["Critical", "Major", "Minor", "Warning"];
    const [alarmRightData, setAlarmRightData] = useState();
    useEffect(() => {
        const fetchHistoryData = async () => {
            await fetchStaticHistory().then(res => {
                if (res.status === 200) {
                    const cpu_count = parseFloat(parseFloat(res.data[0].cpu).toFixed(2));
                    const cpu_count_free = parseFloat(parseFloat(100 - cpu_count).toFixed(2));
                    const mem_count = parseFloat(parseFloat(res.data[0].mem).toFixed(2));
                    const mem_count_free = parseFloat(parseFloat(100 - mem_count).toFixed(2));
                    const disk_count = parseFloat(parseFloat(res.data[0].disk).toFixed(2));
                    const disk_count_free = parseFloat(parseFloat(100 - disk_count).toFixed(2));

                    setStatisticsData({
                        serverCpuData: [
                            {value: cpu_count, name: `Usage ${cpu_count}`},
                            {value: cpu_count_free, name: `Free ${cpu_count_free}`}
                        ],
                        serverMemData: [
                            {value: mem_count, name: `Usage ${mem_count}`},
                            {value: mem_count_free, name: `Free ${mem_count_free}`}
                        ],
                        serverDiskData: [
                            {value: disk_count, name: `Usage ${disk_count}`},
                            {value: disk_count_free, name: `Free ${disk_count_free}`}
                        ],
                        loading: false
                    });
                }
            });
        };
        fetchHistoryData();
    }, []);
    useEffect(() => {
        const countProperty = (obj, property) => {
            return obj.reduce((count, item) => (item.severity === property ? count + 1 : count), 0);
        };
        const result = ["CRITICAL", "MAJOR", "MINOR", "WARNING"].map(property => countProperty(alarms, property));
        setAlarmRightData(result);
    }, [alarms]);
    useEffect(() => {
        setCardSeriesData(
            otnViewData?.card
                ? Object.entries(otnViewData?.card).map(item => {
                      return {
                          name: item[0],
                          value: item[1]
                      };
                  })
                : []
        );
        const seriesData = otnViewData?.ne
            ? Object.entries(otnViewData?.ne).map(item => {
                  if (item && item[0] !== "offLineStatistic") {
                      return {
                          name: item[0].charAt(0).toUpperCase() + item[0].slice(1).toLowerCase(),
                          value:
                              item[0] === "onLine"
                                  ? {
                                        value: item[1],
                                        itemStyle: {
                                            color: "#78D047"
                                        }
                                    }
                                  : {
                                        value: item[1],
                                        itemStyle: {
                                            color: "#C5CACD"
                                        }
                                    }
                      };
                  }
              })
            : [];
        setNeSeriesData(seriesData.filter(item => item !== undefined));
    }, [otnViewData]);
    useEffect(() => {
        const mergeStatus = (otnData, dcpData) => {
            let onLineCount = otnData?.ne.onLine;
            let offLineCount = otnData?.ne.offLine;

            dcpData?.neInfo?.forEach(item => {
                const runState = item?.value?.runState;
                if (runState === 1) {
                    onLineCount += 1;
                } else {
                    offLineCount += 1;
                }
            });

            return {
                ...otnData,
                ne: {
                    ...otnData?.ne,
                    onLine: onLineCount,
                    offLine: offLineCount
                }
            };
        };
        const fetchData = async () => {
            const [dashboardData, dcpData] = await Promise.all([getDashboardData(), getDCP920List()]);
            const mergedData = mergeStatus(dashboardData, dcpData);
            setOtnViewData(mergedData);
        };
        fetchData();
        const intervalId = setInterval(fetchData, 10000);
        return () => {
            clearInterval(intervalId);
        };
    }, []);
    return (
        <div className={styles.otnView}>
            <Card
                title={<div className={styles.otnView_custom_title}>Alarms</div>}
                bordered={false}
                style={{height: "100%"}}
            >
                <div className={styles.otnView_header_cardBody}>
                    {alarmRightData?.map((item, index) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <div key={index} className={styles.otnView_alarms_value}>
                            <Icon component={alarmRightImg[index]} />
                            <div className={styles.otnView_alarmText}>
                                <span className={styles.otnView_alarms_number}>{item}</span>
                                <div className={styles.otnView_header_title}>{alarmRightText[index]}</div>
                            </div>
                        </div>
                    ))}
                </div>
            </Card>
            <Card
                title={<div className={styles.otnView_custom_title}>CPU</div>}
                style={{height: "100%"}}
                loading={statisticsData.loading}
            >
                <Doughnutchart chartData={statisticsData.serverCpuData} Echartsname="CPU" color="#14C9BB" />
            </Card>

            <Card
                title={<div className={styles.otnView_custom_title}>MEM</div>}
                style={{height: "100%"}}
                loading={statisticsData.loading}
            >
                <Doughnutchart chartData={statisticsData.serverMemData} Echartsname="Mem" color="#FFBB00" />
            </Card>

            <Card
                title={<div className={styles.otnView_custom_title}>DISK</div>}
                style={{
                    width: "100%",
                    height: "100%"
                }}
                loading={statisticsData.loading}
            >
                <Doughnutchart chartData={statisticsData.serverDiskData} Echartsname="Disk" color="#14C9BB" />
            </Card>
            <Card
                title={<div className={styles.otnView_custom_title}>Card</div>}
                bordered={false}
                style={{
                    height: "100%",
                    width: "100%"
                }}
            >
                <BarEcharts colorList={["#708DFD"]} width="24px" seriesData={cardSeriesData} seriesName="Card" />
            </Card>
            <Card
                title={<div className={styles.otnView_custom_title}>Alarm Statistics For The Past 24H</div>}
                bordered={false}
                style={{
                    height: "100%",
                    width: "100%"
                }}
                // loading={statisticsHistoryData.loading}
            >
                <MultiLineChart />
            </Card>
            <Card
                title={<div className={styles.otnView_custom_title}>NE Statistics</div>}
                style={{
                    height: "100%",
                    width: "100%"
                }}
                loading={statisticsData.loading}
            >
                <div>
                    <PlusDoughnutchart seriesData={neSeriesData} Echartsname="NE" color="#14C9BB" />
                    <div className={styles.otnView_ne}>
                        <div className={styles.otnView_item}>
                            <span className={styles.otnView_sizeText}>{otnViewData?.ne?.onLine}</span>
                            <div className={styles.otnView_nebottom}>
                                <div className={styles.otnView_neOnlineCricle} />
                                <span className={styles.otnView_onlineText}>Online</span>
                            </div>
                        </div>
                        <div className={styles.otnView_item}>
                            <span className={styles.otnView_sizeText}>{otnViewData?.ne?.offLine}</span>
                            <div className={styles.otnView_nebottom}>
                                <div className={styles.otnView_neOfflineCricle} />
                                <span className={styles.otnView_onlineText}>Offline</span>
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
            <Card title={<div className={styles.otnView_custom_title}>Alarm</div>} bordered={false}>
                <div className={styles.otnView_alarmCard_alarmBody}>
                    <Alarm alarmType="currentAlarmStatistics" />
                </div>
            </Card>
        </div>
    );
};

export default OtnView;
