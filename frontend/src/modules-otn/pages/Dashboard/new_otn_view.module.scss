.otnView {
    display: grid;
    height: 100%;
    width: 100%;
    gap: 18px 24px;
    grid-template-columns: repeat(15, 1fr);
    > div {
  
        &:nth-child(1) {
          grid-column: span 6;
          grid-row: 1;
        }
        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4) {
            grid-column: span 3;
            grid-row: 1;
        }
  
        &:nth-child(5),
        &:nth-child(6) {
            grid-column: span 6;
            grid-row: 2;
        }
        &:nth-child(7){
          grid-column: span 3;
          grid-row: 2;
        }
        &:nth-child(8) {
            grid-column: 1 / -1;
            grid-row: 3;
        }
    }

    &_header {

      &_cardBody {
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: space-around;
        align-items: center;
        overflow: hidden;
      }
  
      &_value {
        font-weight: 700;
        font-size: 20px;
        text-align: center;
  
      }
  
      &_title {
        color: #929A9E;
        font-size: 16px;
        text-align: left;
        font-weight: 400;
        margin-left: 8px;
      }
    }
  
    &_alarms_value {
      display: flex;
      font-weight: 700;
      font-size: 20px;
      align-items: center;
    }
    &_alarmText {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }
  
    &_alarms_number {
      margin-left: 8px;
    }
  
    &_alarmCard {
      flex: 2.4;
      overflow: hidden;
      margin-bottom: 18px;

      &_alarmBody {
        flex: 1;
        display: flex;
        padding: 14px 0;
        height: calc(100% - 10px);
        vertical-align: center;
        flex-direction: column;
        justify-content: flex-start;

        &_head {
          display: flex;
          height: 40px;
          background-color: #F2F4F5;
        }

        &_body {
          display: flex;
          flex-direction: row;
          height: 49px;
          border-bottom: 1px solid #E7E7E7
        }
      }
    }
    &_title {
      display: flex; 
      align-items: center;
      margin-top: 24px;
      margin-bottom: 10px;

      &_textContainer {
        font-size: 14px;
        font-weight: normal;
      }
      &_accountText {
        font-size: 14px;
      }
    }
    &_ne {
      display: flex;
      flex-direction: row; 
      justify-content: center;
      align-items: center;
      gap: 50px; 
    }
    &_item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    &_sizeText {
      font-weight: 700;
      font-size: 20px;
      color: #212519;
    }
    &_nebottom { 
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 5px; 
    }

    &_neOnlineCricle {
        width: 10px;
        height: 10px;
        background: #7EDCD0;
        border-radius: 50%;
    }

    &_neOfflineCricle {
        width: 10px;
        height: 10px;
        background: #C5CACD;
        border-radius: 50%;
    }

    &_onlineText {
        font-size: 14px;
        font-weight: 400;
        color: #929A9E;
    }
    &_table {
      &_content {
        display: flex;
        flex-direction: column;
        height: 100%;
        row-gap: 12px !important;
        justify-content: space-between;
        div {
          width: 100% !important;
          ul {
            text-align: right;
          }
        }
      }
    }
}