import axios from "axios";
import {message} from "antd";

import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {addNS} from "@/modules-otn/utils/util";

export const initAxios = () => {
    axios.defaults.baseURL = "/otn/api";
    axios.defaults.validateStatus = status => {
        if ([401, 500, 504].includes(status)) {
            // dispatch(setUsername(""));
        }
        return true;
    };
    axios.defaults.timeout = 40000;
};

export const requestAxios = async (request, parameter, timeout) => {
    // const {success, fail, msg = true} = parameter;
    try {
        if (timeout) {
            return await axios.post(request, parameter ?? {}, {timeout});
        }
        return await axios.post(request, parameter ?? {});
    } catch (er) {
        let message = `${er.name}: ${er.message}`;
        if (er.name === "AxiosError" && er.message.startsWith("timeout of ")) {
            message = gLabelList.assess_timeout;
        }
        return {data: {apiResult: "fail", apiMessage: message, message}};
    }
};

export const requestSyncTimer = async ({ne_id, msg, timeout, success, fail, sync}) => {
    const rs = (await requestAxios("request_sync_timer", {ne_id, sync}, timeout)).data;
    if (rs.apiResult === "fail") {
        if (msg) message.error(gLabelList.save_failed);
        if (fail) {
            fail(rs);
        }
    } else {
        if (msg) message.success(gLabelList.save_success);
        if (success) {
            success(rs);
        }
    }
    return rs;
};

// type : get-config or null
export const netconfGetByXML = async ({ne_id, xml, msg, type, timeout}) => {
    const rs = (await requestAxios("ne/get", {ne_id, xml, type}, timeout)).data;
    if (rs.apiResult === "fail") {
        if (msg) message.error(gLabelList.get_data_fail);
        return rs;
    }
    return rs.data;
};

export const NEGet = async ({ne_id, parameter, msg = true, timeout}) => {
    const rs = (await requestAxios("ne/get", {ne_id, xml: parameter, type: "get"}, timeout)).data;
    if (rs.apiResult === "fail") {
        if (msg) message.error(gLabelList.get_data_fail);
        return rs;
    }
    return rs.data;
};

export const NESet = async ({ne_id, parameter, msg = true, fail, success, timeout}) => {
    const rs = (await requestAxios("ne/change_xml", {ne_id, xml: parameter}, timeout)).data;
    if (rs.message === "fail") {
        if (msg) message.error(gLabelList.save_failed);
        if (fail) {
            fail(rs);
        }
    } else {
        if (msg) message.success(gLabelList.save_success);
        if (success) {
            success(rs);
        }
    }
    return rs;
};

export const netconfGetByXMLWithXPath = async ({ne_id, xml, type, msg}) => {
    const Yang = await apiGetYang(type);
    const rs = (await requestAxios("ne/get", {ne_id, xml: addNS(xml, Yang)})).data;
    if (rs.apiResult === "fail") {
        if (msg) message.error(gLabelList.get_data_fail);
        return rs;
    }
    return rs.data;
};

export const createFiber = async params => (await requestAxios("ne/createFiber", params)).data;

export const apiRpc = async (params, timeout = 40000) => (await requestAxios("ne/rpc", params, timeout)).data;

export const getStateData = async (params, timeout = 40000) =>
    (await requestAxios("ne/getStateData", params, timeout)).data;

export const netconfChange = async ({ne_id, operation, entity, keys, values, msg, success, fail, sync}) => {
    const rs = (await requestAxios("ne/change", {ne_id, operation, entity, keys, values, sync})).data;
    if (!operation) {
        operation = "get";
    }

    if (rs.apiResult === "fail") {
        if (msg) message.error(gLabelList.save_failed);
        if (fail) {
            fail(rs);
        }
    } else {
        if (msg) message.success(gLabelList.save_success);
        if (success) {
            success(rs);
        }
    }
    return rs;
};

export const apiEditRpc = async ({ne_id, params, success, fail, sync, msg = true}) => {
    const {type} = (await objectGet("config:ne", {ne_id})).documents[0].value;
    const Yang = await apiGetYang(type);
    const rs = (await requestAxios("ne/change_xml", {ne_id, xml: addNS(params, Yang), sync}, 60000)).data;

    if (rs.apiResult === "fail") {
        if (msg) message.error(gLabelList.save_failed);
        fail?.(rs);
    } else {
        if (msg) message.success(gLabelList.save_success);
        success?.(rs);
    }
    return rs;
};

export const netconfByXML = async ({ne_id, xml, msg, timeout, success, fail, sync, action = "create"}) => {
    const messageType = {
        create: {
            success: gLabelList.save_success,
            fail: gLabelList.save_failed
        },
        edit: {success: gLabelList.save_success, fail: gLabelList.save_failed},
        delete: {success: gLabelList.delete_success, fail: gLabelList.delete_fail}
    };
    const rs = (await requestAxios("ne/change_xml", {ne_id, xml, sync}, timeout)).data;
    if (rs.apiResult === "fail") {
        if (msg) message.error(messageType[action].fail);
        if (fail) {
            fail(rs);
        }
    } else {
        if (msg) message.success(messageType[action].success);
        if (success) {
            success(rs);
        }
    }
    return rs;
};

// data
export const apiGetNEData = async (entity, filter) => (await requestAxios("data/get", {entity, filter})).data;

export const objectGet = async (entity, filter, sort, msg) => {
    let rs;
    if (!filter) {
        filter = {};
    }
    if (filter?.DBKey) {
        const value = (await requestAxios("data/get_key", filter)).data;
        if (value.apiResult === "fail") {
            rs = value;
        } else {
            rs = {
                total: 1,
                documents: [{id: filter.DBKey, value}]
            };
        }
    } else {
        rs = (await requestAxios(`data/get?${entity}`, {entity, filter})).data;
    }
    if (rs.apiResult === "fail" && (msg === undefined || msg === null || msg === true)) {
        message.error(gLabelList.get_data_fail);
    } else if (sort) {
        rs.documents = rs.documents.sort((a, b) => {
            (a?.id ?? "")
                .toString()
                .toLowerCase()
                .localeCompare((b?.id ?? "").toString().toLowerCase());
        });
    }
    return rs;
};

let YANG;

export const apiGetYang = async type => {
    if (!YANG) {
        YANG = (await requestAxios("data/get_yang", {})).data;
    }
    return YANG[type];
};

export const getYangRpc = async (name, type) => {
    if (!YANG) {
        YANG = (await requestAxios("data/get_yang", {})).data;
    }
    return YANG[type].rpc[name];
};

let CATEGORY;

export const apiGetCategory = async (categoryName, type) => {
    if (!categoryName) {
        return null;
    }
    if (!CATEGORY) {
        CATEGORY = (await requestAxios("data/get_category", {})).data;
    }
    return CATEGORY[type][categoryName];
};

export const apiConnectionByGroup = async params => (await requestAxios("data/connectionbygroup", params)).data;

export const objectAdd = async ({DBKey, entity, data, msg = true, success, fail}) => {
    const rs = (await requestAxios("data/add", {DBKey, entity, data})).data;
    if (rs.apiResult === "fail") {
        if (msg) message.error(gLabelList.save_failed);
        if (fail) {
            fail(rs);
        }
    } else {
        if (msg) message.success(gLabelList.save_success);
        if (success) {
            success(rs);
        }
    }
    return rs;
};

export const arrayAdd = async ({entity, key, path, data, msg = true, success, fail}) => {
    const rs = (await requestAxios("data/arr_add", {entity, key, path, data})).data;
    if (rs.apiResult === "fail") {
        if (msg) message.error(gLabelList.save_failed);
        if (fail) {
            fail(rs);
        }
    } else {
        if (msg) message.success(gLabelList.save_success);
        if (success) {
            success(rs);
        }
    }
    return rs;
};

export const objectDel = async ({key, msg, success, fail}) => {
    const rs = (await requestAxios("data/del", {key})).data;
    if (rs.apiResult === -1 || rs.apiResult === "fail") {
        if (msg === undefined || msg === true || msg === "error") message.error(gLabelList.delete_failed);
        if (fail) {
            fail(rs);
        }
    } else {
        if (msg === undefined || msg === true || msg === "success") message.success(gLabelList.delete_success);
        if (success) {
            success();
        }
    }
    return rs;
};

export const objectEdit = async ({key, data, msg = true, success, fail}) => {
    const rs = (await requestAxios("data/edit", {key, data})).data;
    if (rs.apiResult === "fail") {
        if (msg) message.error(gLabelList.save_failed);
        if (fail) {
            fail(rs);
        }
    } else {
        if (msg) message.success(gLabelList.save_success);
        if (success) {
            success(rs);
        }
    }
    return rs;
};

export const objectUpdate = async ({key, data, msg = true, success, fail}) => {
    const rs = (await requestAxios("data/update", {key, data})).data;
    if (rs.apiResult === "fail") {
        if (msg) message.error(gLabelList.save_failed);
        if (fail) {
            fail(rs);
        }
    } else {
        if (msg) message.success(gLabelList.save_success);
        if (success) {
            success(rs);
        }
    }
    return rs;
};

export const apiGetNESummary = async () => (await requestAxios("nelist/get_nesummary")).data;

export const apiGetFileList = async params => (await requestAxios("file/get", params)).data;

export const apiDelFile = async params => (await requestAxios("file/del", params)).data;

export const apiNEUpgrade = async (neList, fileName, type) =>
    (
        await requestAxios("upgrade/run", {
            neList,
            fileName,
            type
        })
    ).data;
export const apiGetProvision = async params => (await requestAxios("service_5/get_provision", params)).data;

export const apiGetOpticalpower = async params => (await requestAxios("service_5/get_opticalpower", params)).data;

export const apiAddNE = async params => (await requestAxios("nelist/add", params)).data;

export const apiDelNE = async params => (await requestAxios("nelist/del", params)).data;

export const neUpgradeDel = async params => (await requestAxios("upgrade/del_log", params)).data;

export const uploadFile = async params => (await requestAxios("file/ne_upload", params, 180000)).data;

export const apiConfigMapToken = async params => (await requestAxios("map/set_token", params)).data;
export const apiGetMapToken = async params => (await requestAxios("map/get_token", params)).data;
export const getOfflineMapData = async () => (await requestAxios("map/get_offline_map")).data;

export const syncNE = async ({ne_id, msg = true, success, fail}) => {
    const rs = (await requestAxios("sync", {ne_id}, 120000)).data;
    if (rs.apiResult === "complete") {
        if (msg) message.success(gLabelList.sync_suc.format(ne_id));
        if (success) {
            success(rs);
        }
    } else {
        if (msg) message.error(gLabelList.sync_fail.format(ne_id)).then();
        fail?.(rs);
    }
};

export const clearTelemetryData = async params => (await requestAxios("telemetry/clear_telemetry_data", params)).data;

export const objectGetByTime = async (entity, filter, timeFilter, msg) => {
    if (!filter) {
        filter = {};
    }
    const rs = (await requestAxios(`ne/getByTime?${entity}`, {entity, filter, timeFilter})).data;
    if (rs.apiResult === "fail" && (msg === undefined || msg === null || msg === true)) {
        message.error(gLabelList.get_data_fail);
    }
    return rs;
};

export const apiOtdrGet = async params => (await requestAxios("otdr/get", params, 60000)).data;

export const getPortUsed = async params => (await requestAxios("service_5/port_used", params)).data;

export const setLocationInfo = async params => (await requestAxios("map/set_location", params)).data;

// pm.js
export const getPmInfo = async params => (await requestAxios("pm/get", params)).data;
export const getPmFilterInfo = async params => (await requestAxios("pm/get_filter", params)).data;

export const getTelemetryComponents = async params =>
    (await requestAxios("telemetry/get_telemetry_components", params)).data;

export const getTelemetry = async params => (await requestAxios("telemetry/get_telemetry", params)).data;
export const createDestinationGroup = async ({ne_id, group_id, msg, success, fail}) => {
    const rs = (await requestAxios("telemetry/create_destination_group", {ne_id, group_id})).data;
    if (rs.apiResult === "fail") {
        if (msg) message.error(gLabelList.save_success);
        if (fail) {
            fail(rs);
        }
    } else {
        if (msg) message.success(gLabelList.save_success);
        if (success) {
            success(rs);
        }
    }
    return rs;
};

export const getServiceData = async params => (await requestAxios("service_5/get_device_resource", params)).data;

export const apiGetSafeNeList = async params => (await requestAxios("nelist/get_safe_nelist", params)).data;

export const clearHistoryAlarm = async params => (await requestAxios("monitoring/clear_history_alarm", params)).data;

export const getDashboardData = async params => (await requestAxios("nelist/get_dashboard_data", params)).data;
