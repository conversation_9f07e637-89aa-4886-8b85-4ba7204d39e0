import {getNeType, NE_PROTOCOL_TYPE, NE_TYPE_CONFIG} from "@/modules-otn/utils/util";
import {apiAddNE, apiDelFile, apiGetFileList, apiGetNESummary, objectAdd} from "@/modules-otn/apis/api";
import {config as tableConfig5} from "./table_config_5";

/**
 * columns : list
 * refresh : boolean
 * del : boolean
 * pagination : boolean
 *
 * columns :
 *      dataIndex: mapping with db
 *      title
 *      required
 *      pattern
 *      inputType : default is 'text'
 *      uniq : create/edit唯一性校验
 */
const config = {
    "config:ne": {
        title: "ne",
        columns: [
            {
                dataIndex: "type",
                required: true,
                inputType: "select",
                edit: {
                    disabled: true,
                    options: Object.entries(NE_TYPE_CONFIG).map(([k, v]) => ({
                        value: k.toString(),
                        label: v
                    }))
                },
                covertDefault: async data => {
                    if (NE_PROTOCOL_TYPE?.[getNeType(data.type)] === "snmp") {
                        return {port: "161"};
                    }
                    if (NE_PROTOCOL_TYPE?.[getNeType(data.type)] === "netConf") {
                        return {port: ""};
                    }
                    return {};
                }
            },
            {
                dataIndex: "name",
                required: true,
                uniq: true,
                pattern: /^[\w+.:|\u4e00-\u9fa5-]{1,20}$/,
                message: "name_patter"
            },
            {
                dataIndex: "group",
                inputType: "select",
                edit: {
                    show: false
                },
                data: {
                    type: "nms:group",
                    inputKey: "dbKey",
                    titleKeys: ["name"],
                    valueKey: "name",
                    effect: ["ne_id"]
                }
            },

            {
                dataIndex: "host",
                required: true,
                edit: {
                    disabled: true
                },
                pattern: /((2(5[0-5]|[0-4]\d))|1?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|1?\d{1,2})){3}/g,
                message: "ip_patter"
            },
            {
                dataIndex: "port",
                required: true,
                edit: {
                    disabled: true
                },
                pattern: /((^[1-5]?\d{1,4}$)|(^6([0-4]\d{3}|(5([0-4]\d{2}|(5([0-2]\d|3[0-5])))))$))/g,
                message: "number_patter",
                when: data => {
                    return ["snmp", "netConf"].includes(NE_PROTOCOL_TYPE?.[getNeType(data.type)]);
                }
            },
            {
                dataIndex: "username",
                required: true,
                edit: {
                    required: false
                },
                when: data => {
                    return !data.type || NE_PROTOCOL_TYPE[getNeType(data.type)] === "netConf";
                }
            },
            {
                dataIndex: "read_community",
                title: "read-community",
                required: true,
                edit: {
                    required: false
                },
                when: data => {
                    return NE_PROTOCOL_TYPE[getNeType(data.type)] === "snmp";
                }
            },
            {
                dataIndex: "password",
                required: true,
                inputType: "password",
                edit: {
                    required: false
                },
                when: data => {
                    return !data.type || NE_PROTOCOL_TYPE[getNeType(data.type)] === "netConf";
                }
            },
            {
                dataIndex: "write_community",
                title: "write-community",
                required: true,
                edit: {
                    required: false
                },
                when: data => {
                    return NE_PROTOCOL_TYPE[getNeType(data.type)] === "snmp";
                }
            },
            {
                dataIndex: "trap_port",
                title: "trap-port",
                required: true,
                defaultValue: "162",
                when: data => {
                    return NE_PROTOCOL_TYPE[getNeType(data.type)] === "snmp";
                }
            }
        ],
        addAPI: {
            API: apiAddNE
        }
    },
    neList: {
        title: "ne",
        columns: [
            {dataIndex: "name", title: "ne_name", width: 120, fixed: "left"},
            {dataIndex: "ne_id", title: "ip"},
            {dataIndex: "software", filter: true},
            {dataIndex: "group", filter: true, width: 140, fixed: "right"}
        ]
    },
    upgrade: {
        title: "ne",
        operateColumnWidth: 120,
        columns: [
            {dataIndex: "name", title: "ne_name", fixed: "left", width: "calc(100%/5)"},
            {dataIndex: "ne_id", title: "ip", width: "calc(100%/5)"},
            {
                dataIndex: "type",
                filter: true,
                width: "calc(100%/5)",
                filters: [
                    {text: NE_TYPE_CONFIG[5], value: "5"},
                    {text: NE_TYPE_CONFIG[7], value: "7"}
                ],
                onFilter: (value, record) => record.type === value
            },
            {dataIndex: "software", filter: true, width: "calc(100%/5)"},
            {
                dataIndex: "state",
                filter: true,
                fixed: "right",
                filters: [
                    {text: "error", value: "error"},
                    {text: "lost", value: 0},
                    {text: "connected", value: 1},
                    {text: "download_sw", value: 2},
                    {text: "activate_sw", value: 3},
                    {text: "activate_suc", value: 4},
                    {text: "reboot", value: 5},
                    {text: "cli_login_success", value: 6},
                    {text: "start_upgrade", value: 7},
                    {text: "download_success", value: 8},
                    {text: "upgrade_completed", value: 9}
                ],
                onFilter: (value, record) => record.state === value
            }
        ],
        selectionType: "check",
        dataAPI: {
            APIName: () => {
                return apiGetNESummary({version: true});
            },
            key: "ne_id",
            sortKey: "name"
        }
    },
    images: {
        title: "sw_version",
        columns: [
            {dataIndex: "name", width: "calc(100%/6)"},
            {dataIndex: "software", width: "calc(100%/6)"},
            {
                dataIndex: "type",
                filter: true,
                width: "calc(100%/6)",
                filters: [
                    {text: NE_TYPE_CONFIG[5], value: "5"},
                    {text: NE_TYPE_CONFIG[7], value: "7"}
                ],
                onFilter: (value, record) => record.type === value
            },
            {dataIndex: "size", unit: "KiB", width: "calc(100%/6)"},
            {dataIndex: "time-created", defaultSortOrder: "descend", width: "calc(100%/6)"}
        ],
        selectionType: "radio",
        operateColumnWidth: 120,
        delAPI: {
            APIName: apiDelFile,
            APIParameter: data => {
                return {
                    file: data.name,
                    type: "upgrade"
                };
            }
        },
        dataAPI: {
            APIName: apiGetFileList,
            APIParameter: () => {
                return {
                    type: "upgrade"
                };
            }
        }
    },
    currentAlarm: {
        title: "alarm_title",
        operateColumnWidth: 100,
        columns: [
            // {dataIndex: "id"},
            {dataIndex: "name", title: "ne_name", width: 120, fixed: "left"},
            {dataIndex: "resource", width: 200},
            {
                dataIndex: "severity",
                filter: true,
                width: 120,
                sortFun: () => {
                    return (optionA, optionB) => {
                        return (optionB.severity ?? "")
                            .toString()
                            .toLowerCase()
                            .localeCompare((optionA.severity ?? "").toString().toLowerCase());
                    };
                }
            },
            {dataIndex: "alarm-abbreviate", width: 220, filter: true},
            {dataIndex: "time-created", width: 200, defaultSortOrder: "descend"},
            {dataIndex: "text", width: 300},
            {dataIndex: "operator-text", width: 150},
            {dataIndex: "operator-name", width: 150},
            {dataIndex: "time-acknowledged", width: 200}
        ]
    },
    historyAlarm: {
        title: "history_alarm",
        operateColumnWidth: 100,
        columns: [
            {dataIndex: "name", title: "ne_name", width: 120, fixed: "left"},
            {dataIndex: "resource", width: 200},
            {dataIndex: "alarm-abbreviate", width: 220, filter: true},
            {dataIndex: "time-created", width: 160, defaultSortOrder: "descend"},
            {dataIndex: "time-cleared", width: 160},
            {dataIndex: "text", width: 200},
            {dataIndex: "operator-text", width: 150},
            {dataIndex: "operator-name", width: 150},
            {dataIndex: "time-acknowledged", width: 200},
            {
                dataIndex: "severity",
                filter: true,
                width: 120,
                fixed: "right",
                sortFun: () => {
                    return (optionA, optionB) => {
                        return (optionB.severity ?? "")
                            .toString()
                            .toLowerCase()
                            .localeCompare((optionA.severity ?? "").toString().toLowerCase());
                    };
                }
            }
        ]
    },
    currentAlarmStatistics: {
        title: "alarm_title",
        columns: [
            {dataIndex: "name", filter: true, fixed: "left"},
            {dataIndex: "resource", filter: true},
            {
                dataIndex: "severity",
                filter: true,
                sortFun: () => {
                    return (optionA, optionB) => {
                        return (optionB.severity ?? "")
                            .toString()
                            .toLowerCase()
                            .localeCompare((optionA.severity ?? "").toString().toLowerCase());
                    };
                }
            },
            {dataIndex: "alarm-abbreviate", filter: true},
            {dataIndex: "type-id", filter: true},
            {dataIndex: "text"},
            {dataIndex: "time-created", defaultSortOrder: "descend", fixed: "right"}
        ]
    },
    tnmsAlarm: {
        title: "alarm_title",
        columns: [
            // {dataIndex: "id"},
            {dataIndex: "name", filter: true, title: "ne_name"},
            {dataIndex: "resource", filter: true},
            {
                dataIndex: "severity",
                filter: true,
                sortFun: () => {
                    return (optionA, optionB) => {
                        return (optionB.severity ?? "")
                            .toString()
                            .toLowerCase()
                            .localeCompare((optionA.severity ?? "").toString().toLowerCase());
                    };
                }
            },
            {dataIndex: "alarm-abbreviate", filter: true},
            {dataIndex: "time-created", defaultSortOrder: "descend"},
            {dataIndex: "type-id", filter: true},
            {dataIndex: "text"},
            {dataIndex: "description"}
        ]
    },
    "nms:group": {
        title: "group",
        columns: [
            {
                dataIndex: "name",
                required: true,
                uniq: true,
                pattern: /^[\w+./:|\u4e00-\u9fa5-]{1,20}$/,
                message: "name_patter"
            }
        ]
    },
    "switch:group": {
        title: "group",
        columns: [
            {
                dataIndex: "name",
                required: true,
                pattern: /^[\w+./:|\u4e00-\u9fa5-]{1,20}$/,
                message: "name_patter"
            }
        ]
    },
    tnmsHistoryAlarm: {
        columns: [
            // {dataIndex: "id"},
            {dataIndex: "name", filter: true, title: "ne_name"},
            {dataIndex: "resource", filter: true},
            {
                dataIndex: "severity",
                filter: true,
                sortFun: () => {
                    return (optionA, optionB) => {
                        return (optionB.severity ?? "")
                            .toString()
                            .toLowerCase()
                            .localeCompare((optionA.severity ?? "").toString().toLowerCase());
                    };
                }
            },
            {dataIndex: "alarm-abbreviate", filter: true},
            {dataIndex: "time-created", defaultSortOrder: "descend"},
            {dataIndex: "time-cleared"},
            {dataIndex: "type-id", filter: true},
            {dataIndex: "text"},
            {dataIndex: "description"}
        ]
    },
    allEvent: {
        columns: [
            {dataIndex: "name", filter: true, title: "ne_name"},
            {dataIndex: "resource", filter: true},
            {dataIndex: "text", width: 450},
            {dataIndex: "time-created", defaultSortOrder: "descend"},
            // {dataIndex: "hostname", filter: true},
            // {dataIndex: "vendor-type"},
            {dataIndex: "event-abbreviate", filter: true}
        ]
    },
    relate_port: {
        title: "Port",
        columns: [
            {dataIndex: "ne_name", filter: true, width: 150, fixed: "left"},
            {dataIndex: "name", filter: true, width: 150},
            {dataIndex: "location", filter: true, width: 150},
            {dataIndex: "actual-vendor-type", title: "vendor_info", filter: true, width: 150},
            {dataIndex: "admin-state", filter: true, width: 150},
            {dataIndex: "oper-status", filter: true, width: 150},

            // {dataIndex: "type", filter: true},
            {dataIndex: "serial-no", width: 150, filter: true},
            // {dataIndex: "part-no", filter: true},
            {dataIndex: "description", width: 150}
        ]
    },
    tca: {
        head: false
    },
    reboot: {
        title: "reboot",
        columns: [
            {
                dataIndex: "reboot-type",
                defaultValue: "cold",
                inputType: "select",
                data: {
                    options: ["cold", "warm"]
                }
            }
        ]
    },
    time_manage: {
        title: "time_manage",
        selectionType: "check"
    },
    ntp_template: {
        title: "ntp_template",
        operateColumnWidth: 180,
        columns: [
            {
                title: "Name",
                dataIndex: "name"
            },
            {
                title: "Address",
                dataIndex: "address"
            },
            {
                title: "Port",
                dataIndex: "port"
            },
            {
                title: "Version",
                dataIndex: "version"
            }
        ]
    },
    pm: {
        title: "pm",
        columns: [
            {title: "pm-point", dataIndex: "pm-point", filter: true, fixed: "left"},
            {title: "pm-point-type", dataIndex: "pm-point-type", filter: true},
            {title: "pm-parameter", dataIndex: "pm-parameter", filter: true},
            {title: "pm-granularity", dataIndex: "pm-granularity", filter: true, width: 180},
            {title: "max-value", dataIndex: "max-value"},
            {title: "min-value", dataIndex: "min-value"},
            {title: "average-value", dataIndex: "average-value"},
            {title: "current-value", dataIndex: "current-value"},
            {
                title: "monitoring-date-time",
                dataIndex: "monitoring-date-time",
                defaultSortOrder: "descend",
                filter: true,
                fixed: "right",
                width: 220
            }
        ]
    },
    user_manage: {
        title: "user_management",
        columns: [
            {dataIndex: "username", filter: true, width: 180, defaultSortOrder: "ascend"},
            {dataIndex: "type", filter: true, width: 180}
        ]
    },
    global_clock: {
        title: "global_clock",
        columns: [{dataIndex: "ssm-enable"}, {dataIndex: "wtr", title: "wtrt"}]
    },
    system_clock: {
        title: "system_clock",
        columns: [{dataIndex: "name"}, {dataIndex: "current-ql"}, {dataIndex: "pll-state"}]
    },
    "nms:mail": {
        title: "alarm_notice_settings",
        addAPI: {
            API: async values => {
                return await objectAdd({
                    DBKey: "nms:mail:1",
                    entity: "nms:mail",
                    data: values,
                    msg: false
                });
            }
        },
        columns: [
            {
                dataIndex: "enable",
                required: true,
                inputType: "switch"
            },
            {
                dataIndex: "host",
                title: "mail-service-host",
                pattern: /\w+.\w+/g,
                message: "mail_service_patter",
                required: (v, formValues) => {
                    return formValues?.enable !== false;
                },
                disabled: (v, formValues) => {
                    return formValues?.enable === false;
                }
            },
            {
                dataIndex: "port",
                title: "mail-service-port",
                pattern: /((^[1-5]?\d{1,4}$)|(^6([0-4]\d{3}|(5([0-4]\d{2}|(5([0-2]\d|3[0-5])))))$))/g,
                message: "number_patter",
                required: (v, formValues) => {
                    return formValues?.enable !== false;
                },
                disabled: (v, formValues) => {
                    return formValues?.enable === false;
                }
            },
            {
                dataIndex: "username",
                title: "user-name",
                required: (v, formValues) => {
                    return formValues?.enable !== false;
                },
                disabled: (v, formValues) => {
                    return formValues?.enable === false;
                }
            },
            {
                dataIndex: "password",
                inputType: "password",
                init: false,
                required: (v, formValues) => {
                    return formValues?.enable !== false;
                },
                disabled: (v, formValues) => {
                    return formValues?.enable === false;
                }
            },
            {
                dataIndex: "subject",
                required: (v, formValues) => {
                    return formValues?.enable !== false;
                },
                disabled: (v, formValues) => {
                    return formValues?.enable === false;
                }
            },
            {
                dataIndex: "to",
                title: "send-to",
                inputType: "textarea",
                placeholder: "separated_semicolons",
                required: (v, formValues) => {
                    return formValues?.enable !== false;
                },
                pattern: /\w+@\w+.\w/g,
                message: "send_to_patter",
                disabled: (v, formValues) => {
                    return formValues?.enable === false;
                }
            }
        ]
    },
    register: {
        title: "Register",
        columns: [
            {
                dataIndex: "key",
                title: "secret-key",
                required: true
            },
            {
                dataIndex: "code",
                inputType: "textarea",
                rows: 8,
                required: true
            }
        ]
    },
    license: {
        // title: "License",
        pagination: false,
        columns: [
            {
                dataIndex: "company"
            },
            {
                dataIndex: "type"
            },
            {
                dataIndex: "state"
            },
            {
                dataIndex: "remainder",
                title: "Remaining Days"
            },
            {
                dataIndex: "support-version",
                title: "Support Version"
            },
            {
                dataIndex: "register-time",
                title: "Registration Time"
            }
        ]
    }
};

const mergeTableConfig = (...args) => {
    return args.reduce((result, _config) => {
        return Object.assign(result, _config);
    }, {});
};

export const TableConfig = mergeTableConfig(config, tableConfig5);
