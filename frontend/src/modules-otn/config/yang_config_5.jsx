import {objectGet} from "@/modules-otn/apis/api";
import {convertToArray} from "@/modules-otn/utils/util";

export const YANG_CONFIG = {
    "alarm-ack": {
        id: {
            when: () => false
        },
        "time-acknowledged": {
            when: () => false
        },
        "operator-text": {
            required: true,
            pattern: /^((?!&|<|>|"|').){0,32}$/,
            message: "alarm_ack_pattern"
        },
        "operator-name": {
            when: () => false
        }
    },
    "frequency-channel": {
        "ad-port": {
            type: "select",
            data: async (v, parameter) => {
                const rs = (await objectGet("", {DBKey: `ne:5:component:${parameter.ne_id}:${parameter.keys[0]}`}))
                    .documents;
                if (rs.length > 0) {
                    return convertToArray(rs[0].value.data?.subcomponents?.subcomponent)
                        .filter(i => i.name.indexOf("-AD") > -1)
                        .map(i => i.name);
                }
                return [];
            }
        },
        "line-port": {
            type: "select",
            defaultValue: "",
            data: async (v, parameter) => {
                const rs = (await objectGet("", {DBKey: `ne:5:component:${parameter.ne_id}:${parameter.keys[0]}`}))
                    .documents;
                if (rs.length > 0) {
                    return convertToArray(rs[0].value.data?.subcomponents?.subcomponent)
                        .filter(i => i.name.indexOf("-MUX") > -1)
                        .map(i => i.name);
                }
                return [];
            }
        }
    },
    amplifier: {
        "amp-mode": {
            type: "select",
            data: async () => {
                return ["CONSTANT_GAIN"];
            }
        }
    },
    "physical-channel": {
        "target-output-power": {
            tableHidden: true
        }
    }
};
