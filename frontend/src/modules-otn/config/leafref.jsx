import {apiGetNEData, objectGet} from "@/modules-otn/apis/api";
import {convertToArray, getValueByJPath, getAttrValue, extractNameFromNamespace} from "@/modules-otn/utils/util";

const category = {};

const CARD_TYPES = ["LINECARD", "TFF", "WSS", "OA", "OLP", "OLA", "OTDR_CARD", "POWER_SUPPLY", "FAN", "MCU"];

export const LeafRefs = {
    "alarms-mask": {
        "alarm-mask": {
            config: {
                "object-name": {
                    getList: async param => {
                        const response = await apiGetNEData(
                            {
                                components: {
                                    component: {
                                        name: "",
                                        state: {
                                            type: ""
                                        }
                                    }
                                }
                            },
                            "get-alarm-mask-object-names"
                        );
                        if (response.result && response.data) {
                            const data = [""];
                            const objectType =
                                param?.form?.getFieldValue("object-range") ??
                                param?.form?.getFieldValue([
                                    ...category["alarm-mask"].rootPath,
                                    "config",
                                    "object-range"
                                ]);
                            getValueByJPath(response.data, ["components", "component"]).forEach(component => {
                                const type = extractNameFromNamespace(getAttrValue(component.state, "type"));
                                if (!objectType) {
                                    if (CARD_TYPES.includes(type) || type === "PORT" || type === "CHASSIS") {
                                        data.push(component.name);
                                    }
                                } else if (objectType === "CARD") {
                                    if (CARD_TYPES.includes(type)) {
                                        data.push(component.name);
                                    }
                                } else if (objectType === "PORT") {
                                    if (type === "PORT") {
                                        data.push(component.name);
                                    }
                                } else if (type === "CHASSIS") {
                                    data.push(component.name);
                                }
                            });
                            return data;
                        }
                        return [];
                    }
                }
            }
        }
    },
    components: {
        component: {
            port: {
                config: {
                    "layer-protocol-name": {
                        getList: param => {
                            const v = param.form.getFieldValue([
                                "components",
                                "component",
                                "port",
                                "state",
                                "supported-layer-protocol-names",
                                "supported-layer-protocol-name"
                            ]);
                            v.sort();
                            return v;
                        }
                    }
                }
            },
            transceiver: {
                config: {
                    "used-service-port-type-preconf": {
                        getList: param => {
                            const v = convertToArray(
                                param.form.getFieldValue([
                                    "components",
                                    "component",
                                    "transceiver",
                                    "state",
                                    "supported-service-port-types",
                                    "supported-service-port-type"
                                ])
                            );
                            v?.sort();
                            return v.map(i => ({
                                label: i.split("_")[1],
                                value: i.split("_")[1]
                            }));
                        }
                    }
                }
            },
            wss: {
                config: {
                    "frequency-channel": {
                        "ad-port": {
                            getList: async param => {
                                // eslint-disable-next-line no-unsafe-optional-chaining
                                const wssName = param?.keys?.[param?.keys?.length - 1];
                                const response = await apiGetNEData(
                                    {
                                        components: {
                                            component: {
                                                state: {
                                                    parent: wssName,
                                                    type: "oc-platform-types:PORT"
                                                }
                                            }
                                        }
                                    },
                                    "get-ports-frequency-channel"
                                );
                                if (response.result && response.data) {
                                    const data = [""];
                                    getValueByJPath(response.data, ["components", "component", "state"]).forEach(
                                        item => {
                                            if (getAttrValue(item, "name")?.includes("AD")) {
                                                data.push(item.name);
                                            }
                                        }
                                    );
                                    return data;
                                }
                                return [];
                            }
                        },
                        "line-port": {
                            getList: async param => {
                                // eslint-disable-next-line no-unsafe-optional-chaining
                                const wssName = param?.keys?.[param?.keys?.length - 1];
                                const response = await apiGetNEData(
                                    {
                                        components: {
                                            component: {
                                                state: {
                                                    parent: wssName,
                                                    type: "oc-platform-types:PORT"
                                                }
                                            }
                                        }
                                    },
                                    "get-ports-frequency-channel"
                                );
                                if (response.result && response.data) {
                                    const data = [""];
                                    getValueByJPath(response.data, ["components", "component", "state"]).forEach(
                                        item => {
                                            if (getAttrValue(item, "name").endsWith("MUX")) {
                                                data.push(item.name);
                                            }
                                        }
                                    );
                                    return data;
                                }
                                return [];
                            }
                        }
                    }
                }
            },
            config: {
                "vendor-type-preconf": {
                    empty: form =>
                        getValueByJPath(form.getFieldsValue(), ["components", "component", "state", "type"]) !== "SLOT",
                    getList: param =>
                        param.form
                            .getFieldValue([
                                "components",
                                "component",
                                "state",
                                "supported-vendor-types",
                                "supported-vendor-type"
                            ])
                            .sort()
                }
            }
        }
    },
    "telemetry-system": {
        subscriptions: {
            "persistent-subscriptions": {
                "persistent-subscription": {
                    "sensor-profiles": {
                        "sensor-profile": {
                            config: {
                                "sensor-group": {
                                    getList: async () => {
                                        const response = await apiGetNEData(
                                            {
                                                "telemetry-system": {
                                                    "sensor-groups": {
                                                        "sensor-group": {}
                                                    }
                                                }
                                            },
                                            "get-sensor-group"
                                        );
                                        if (response.result && response.data) {
                                            const data = [""];
                                            convertToArray(
                                                getValueByJPath(response.data, [
                                                    "telemetry-system",
                                                    "sensor-groups",
                                                    "sensor-group"
                                                ])
                                            )?.forEach(sensor => {
                                                data.push(sensor["sensor-group-id"]);
                                            });
                                            return data;
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "destination-groups": {
                        "destination-group": {
                            config: {
                                "group-id": {
                                    getList: async () => {
                                        const response = await apiGetNEData(
                                            {
                                                "telemetry-system": {
                                                    "destination-groups": {
                                                        "destination-group": {}
                                                    }
                                                }
                                            },
                                            "get-sensor-group"
                                        );
                                        if (response.result && response.data) {
                                            const data = [""];
                                            convertToArray(
                                                getValueByJPath(response.data, [
                                                    "telemetry-system",
                                                    "destination-groups",
                                                    "destination-group"
                                                ])
                                            )?.forEach(sensor => {
                                                data.push(sensor["group-id"]);
                                            });
                                            return data;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    otdrs: {
        otdr: {
            config: {
                "active-local-port": {
                    getList: async param => {
                        try {
                            const {ne_id, keys} = param ?? {};
                            const cardAndPort = keys?.at(0)?.match(/-(\d+-\d+)*/);
                            const rawData = await objectGet("ne:5:component", {ne_id}).then(rs =>
                                rs?.documents?.map(item => item?.value?.data)
                            );
                            const OTRDReg = new RegExp(`^PORT-${cardAndPort[1]}.*OTDR.*$`);
                            return rawData?.reduce(
                                (res, item) =>
                                    OTRDReg.test(item?.name) && !res.includes(item?.name) ? [...res, item.name] : res,
                                []
                            );
                        } catch (e) {
                            return [];
                        }
                    }
                }
            }
        }
    },
    "channel-monitors": {
        "channel-monitor": {
            config: {
                "active-local-port": {
                    getList: async param => {
                        try {
                            const {ne_id, keys} = param ?? {};
                            const cardAndPort = keys?.at(0)?.match(/-(\d+-\d+)*/);
                            const rawData = await objectGet("ne:5:component", {ne_id}).then(rs =>
                                rs?.documents?.map(item => item?.value?.data)
                            );
                            const OTRDReg = new RegExp(`^PORT-${cardAndPort[1]}.*OCM.*$`);
                            return rawData.reduce(
                                (res, item) =>
                                    OTRDReg.test(item?.name) && !res.includes(item?.name) ? [...res, item.name] : res,
                                []
                            );
                        } catch (e) {
                            return [];
                        }
                    }
                }
            }
        }
    }
};
