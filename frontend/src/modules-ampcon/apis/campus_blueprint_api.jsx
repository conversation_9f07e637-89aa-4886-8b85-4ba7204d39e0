import {request} from "@/utils/common/request";

const baseUrl = "/ampcon/campus_blueprint";

export function saveConfig(data) {
    return request({
        url: `${baseUrl}/site_config/save`,
        method: "POST",
        data: {data}
    });
}
export function getSiteTopoList(page, pageSize, filterFields, sortFields, searchFields) {
    if (searchFields && searchFields.value && searchFields.value.toLowerCase().includes("ip clos")) {
        searchFields.value = searchFields.value.toLowerCase().replace("ip clos", "ip-clos");
    }
    return request({
        url: `${baseUrl}/site_topo/list`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function getAllSiteTopoInfo() {
    return request({
        url: `${baseUrl}/site_topo_info/all`,
        method: "POST"
    });
}

export function viewSiteTopo(site_config_id) {
    return request({
        url: `${baseUrl}/site_topo/preview`,
        method: "POST",
        data: {site_config_id}
    });
}
export function deleteSiteTopo(topology_config_id) {
    return request({
        url: `${baseUrl}/site_topo/delete`,
        method: "POST",
        data: {topology_config_id}
    });
}

export function getDeploymentStatus(siteConfigId, page, pageSize, filterFields, sortFields, searchFields) {
    const defaultSortFields = sortFields.length === 0 ? [{field: "create_time", order: "desc"}] : sortFields;

    return request({
        url: `${baseUrl}/campus_fabric/deployment_status`,
        method: "POST",
        data: {
            siteConfigId,
            page,
            pageSize,
            filterFields,
            sortFields: defaultSortFields,
            searchFields
        }
    });
}
export function getSwitchTemplateList(page, pageSize, filterFields, sortFields, searchFields) {
    sortFields =
        sortFields.length === 0
            ? [
                  {
                      field: "create_time",
                      order: "desc"
                  }
              ]
            : sortFields;
    return request({
        url: `${baseUrl}/switch_templates/get_list`,
        method: "POST",
        data: {page, pageSize, filterFields, sortFields, searchFields}
    });
}

export function getSwitchStatus(snList) {
    return request({
        url: `${baseUrl}/get_switch_status`,
        method: "POST",
        data: {snList}
    });
}

export function getSwitchList(switchList, page, pageSize, filterFields, sortFields, searchFields) {
    return request({
        url: `${baseUrl}/switch_templates/get_switch_list`,
        method: "POST",
        data: {switchList, page, pageSize, filterFields, sortFields, searchFields}
    });
}

export function copyTemplate(id, name, timestamp) {
    return request({
        url: `${baseUrl}/switch_templates/copy`,
        method: "POST",
        data: {id, name, timestamp}
    });
}

export function getStatusList(id, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    sortFields =
        sortFields.length === 0
            ? [
                  {
                      field: "create_time",
                      order: "desc"
                  }
              ]
            : sortFields;
    return request({
        url: `${baseUrl}/switch_templates/get_deploy_status_list`,
        method: "POST",
        data: {
            id,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function getStatusLog(log_id) {
    return request({
        url: `${baseUrl}/switch_templates/get_deploy_status_log`,
        method: "GET",
        params: {log_id}
    });
}

export function configDeploy(data) {
    return request({
        url: `${baseUrl}/switch_templates/config_delploy`,
        method: "POST",
        data
    });
}

export function deleteTemplate(id) {
    return request({
        url: `${baseUrl}/switch_templates/delete_template`,
        method: "DELETE",
        params: {
            id
        }
    });
}

export function viewTemplate(id) {
    return request({
        url: `${baseUrl}/switch_templates/get_view`,
        method: "GET",
        params: {id}
    });
}

export function downloadTemplate(id) {
    return request({
        url: `${baseUrl}/switch_templates/download_template`,
        method: "GET",
        params: {id},
        responseType: "blob"
    });
}

export function importTemplate(formData) {
    return request({
        url: `${baseUrl}/switch_templates/import_template`,
        method: "POST",
        data: formData
    });
}

export function getAddedSwitch(switchList, page, pageSize, searchFields = {}) {
    return request({
        url: `${baseUrl}/switch_templates/get_added_switch_list`,
        method: "POST",
        data: {
            switchList,
            searchFields,
            page,
            pageSize
        }
    });
}

export function getOverlayData(search_value) {
    return request({
        url: `${baseUrl}/overlay/get_dropdown_box_data`,
        method: "POST",
        data: {
            search_value
        }
    });
}

export function checkDuplicateName(name, id) {
    return request({
        url: `${baseUrl}/switch_templates/check_duplicate_name`,
        method: "POST",
        data: {
            name,
            id
        }
    });
}
