import {request} from "@/utils/common/request";
import {useQuery} from "@tanstack/react-query";

const baseUrl = "/ampcon/upgrade";

export function queryImages(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/images`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function deleteImage(imageId) {
    return request({
        url: `${baseUrl}/image/${imageId}`,
        method: "DELETE"
    });
}

export function uploadAPImage(imageSource, version, revision, model) {
    const isFile = imageSource instanceof File;

    if (isFile) {
        const formData = new FormData();
        formData.append("upload_type", "1");
        formData.append("file", imageSource);
        formData.append("version", version);
        formData.append("revision", revision);
        formData.append("model", model);

        return request({
            url: `${baseUrl}/image/action/upload`,
            method: "POST",
            data: formData,
            headers: {
                "Content-Type": "multipart/form-data"
            }
        });
    }
    if (typeof imageSource === "string") {
        const data = {
            imageLink: imageSource,
            version,
            revision,
            model,
            upload_type: "2"
        };

        return request({
            url: `${baseUrl}/image/action/upload`,
            method: "POST",
            data,
            headers: {
                "Content-Type": "application/json"
            }
        });
    }
    return Promise.reject(new Error("Invalid image source type."));
}

export const useGetDeviceTypes = () => {
    return useQuery(
        ["get-device-types"],
        () => {
            return request({
                url: `${baseUrl}/models`,
                method: "GET"
            })
                .then(res => {
                    if (res.status === 200) {
                        return res.info.model_list;
                    }
                    throw new Error("Failed to get device types");
                })
                .catch(() => []);
        },
        {
            staleTime: Infinity
        }
    );
};

export function batchUpgradeAP(sn_list, image_id, upgrade_type, upgrade_time) {
    return request({
        url: `${baseUrl}/task/submit`,
        method: "POST",
        data: {
            sn_list,
            image_id,
            upgrade_type,
            upgrade_time
        }
    });
}

export function getUpgradeStatus(snList) {
    return request({
        url: `${baseUrl}/status?sn=${snList}`,
        method: "GET"
    });
}

export function getAPUpgradeLogs(sn) {
    return request({
        url: `${baseUrl}/${sn}/logs`,
        method: "GET"
    });
}

export function cancelScheduleTask(sn) {
    return request({
        url: `${baseUrl}/schedule_task`,
        method: "DELETE",
        data: {sn}
    });
}
