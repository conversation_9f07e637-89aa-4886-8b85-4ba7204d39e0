import {request} from "@/utils/common/request";

const baseURL = "/ampcon";
const dcp920Url = `${baseURL}/dcp920`;

// bypass database in backend
export function queryDCP920Info(ip) {
    return request({
        url: `${dcp920Url}/info/query`,
        method: "GET",
        params: {
            ip
        }
    });
}

// query database in backend
export function getDCP920Info(ip) {
    return request({
        url: `${dcp920Url}/info/get`,
        method: "GET",
        params: {
            ip
        }
    });
}

export function modifyDCP920Config(ip, config) {
    return request({
        url: `${dcp920Url}/config/modify`,
        method: "PUT",
        params: {
            ip,
            ...config
        }
    });
}

export function queryDCP920Config(ip, configType) {
    return request({
        url: `${dcp920Url}/config/query`,
        method: "GET",
        params: {
            ip,
            config_type: configType
        }
    });
}

export function queryDCP920Test(ip) {
    return request({
        url: `${dcp920Url}/info/test_result/get`,
        method: "GET",
        params: {
            ip
        }
    });
}
