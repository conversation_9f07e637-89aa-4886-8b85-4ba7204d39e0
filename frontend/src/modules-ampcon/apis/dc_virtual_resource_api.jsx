import {request} from "@/utils/common/request";

const baseUrl = "/ampcon/dc_virtual_resource";
// AZ
export function fetchVirtualResourcePoolAZInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/virtual_resource_pool_az/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function addVirtualResourcePoolAZ(data) {
    return request({
        url: `${baseUrl}/virtual_resource_pool_az/save`,
        method: "POST",
        data
    });
}

export function editVirtualResourcePoolAZ(data) {
    return request({
        url: `${baseUrl}/virtual_resource_pool_az/save`,
        method: "POST",
        data
    });
}

export function deleteVirtualResourcePoolAZ(data) {
    console.log(data);
    return request({
        url: `${baseUrl}/virtual_resource_pool_az/delete`,
        method: "POST",
        data
    });
}

// VPC
export function fetchVirtualResourceVPCInfo(
    az_id,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/virtual_resource_vpc/list`,
        method: "POST",
        data: {
            az_id,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

// VM
export function fetchVirtualResourceVMInfo(
    az_id,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/virtual_resource_vm/list`,
        method: "POST",
        data: {
            az_id,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

// Network
export function fetchVirtualResourceNetworkInfo(
    az_id,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/virtual_resource_network/list`,
        method: "POST",
        data: {
            az_id,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function fetchNetworkIAccessInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/network_access/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function addNetworkIAccess(id, vl2Name, azId, fabricId, vlanId) {
    return request({
        url: `${baseUrl}/network_access/save`,
        method: "POST",
        data: {
            id,
            vl2Name,
            azId,
            fabricId,
            vlanId
        }
    });
}

export function fetchNetworkIAccessDeviceInfo(vl2Id) {
    return request({
        url: `${baseUrl}/network_access/device_list`,
        method: "POST",
        data: {
            vl2Id
        }
    });
}

export function fetchNetworkIAccessAvailableDeviceDetailInfo(nodeGroupId) {
    return request({
        url: `${baseUrl}/network_access/available_device_list`,
        method: "POST",
        data: {
            nodeGroupId
        }
    });
}

export function fetchGroupList(azId) {
    return request({
        url: `${baseUrl}/network_access/node_group_list`,
        method: "POST",
        data: {
            azId
        }
    });
}

export function addAssociateDevice(vl2Id, nicPortgroupIdList) {
    return request({
        url: `${baseUrl}/network_access/associate`,
        method: "POST",
        data: {
            vl2Id,
            nicPortgroupIdList
        }
    });
}

export function dissociateAssociate(vl2Id, nodegroupId, nicPortgroupId) {
    return request({
        url: `${baseUrl}/network_access/dissociate`,
        method: "POST",
        data: {
            vl2Id,
            nodegroupId,
            nicPortgroupId
        }
    });
}

export function deleteNetworkAccess(id) {
    return request({
        url: `${baseUrl}/network_access/delete`,
        method: "POST",
        data: {
            id
        }
    });
}
