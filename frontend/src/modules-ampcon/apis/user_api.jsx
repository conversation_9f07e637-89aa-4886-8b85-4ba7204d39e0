import {request} from "@/utils/common/request";

const baseUrl = "/ampcon/user";

export function fetchUserInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/management`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function addUserInfo(data) {
    return request({
        url: `${baseUrl}/add`,
        method: "POST",
        data
    });
}

export function checkPwdInfo(data) {
    return request({
        url: `${baseUrl}/check_pwd`,
        method: "POST",
        data
    });
}

export function editUserInfo(data) {
    return request({
        url: `${baseUrl}/edit`,
        method: "POST",
        data
    });
}

export function updateUserInfo(data) {
    return request({
        url: `${baseUrl}/update_current_user`,
        method: "POST",
        data
    });
}

export function delUserInfo(username) {
    return request({
        url: `${baseUrl}/del_user/${username}`,
        method: "GET"
    });
}

export function unLockUserInfo(username) {
    return request({
        url: `${baseUrl}/unlock_user/${username}`,
        method: "GET"
    });
}

export function lockUserInfo(username) {
    return request({
        url: `${baseUrl}/lock_user/${username}`,
        method: "GET"
    });
}

export function getTACACSSettings() {
    return request({
        url: `${baseUrl}/tacacs_settings`,
        method: "GET"
    });
}

export function setTACACSSettings(data) {
    return request({
        url: `${baseUrl}/tacacs_settings`,
        method: "POST",
        data
    });
}

export function fetchUserGroupInfo(groupName, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    if (groupName !== "All" && groupName) {
        return request({
            url: `${baseUrl}/group/${groupName}`,
            method: "POST",
            data: {
                filterFields,
                sortFields,
                searchFields,
                page,
                pageSize
            }
        });
    }
    return fetchUserInfo(page, pageSize, filterFields, sortFields, searchFields);
}
