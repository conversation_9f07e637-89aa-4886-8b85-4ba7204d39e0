import {Input, Select} from "antd";
import React, {useEffect, useState} from "react";

import {searchSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";

const {Option, OptGroup} = Select;

const SwitchModelSelector = ({placeholder, handleChange, data, selectedModel, style}) => {
    const [searchValue, setSearchValue] = useState("");
    const [filteredOptions, setFilteredOptions] = useState(data);

    const handleSearch = value => {
        setSearchValue(value);
        const filtered = Object.fromEntries(
            Object.entries(data)
                .filter(([, options]) => options.some(options => options.toLowerCase().includes(value.toLowerCase())))
                .map(([groupLabel, options]) => [
                    groupLabel,
                    options.filter(options => options.toLowerCase().includes(value.toLowerCase()))
                ])
                .filter(([, options]) => options.length > 0)
        );
        setFilteredOptions(filtered);
    };

    const dropDownRender = menu => {
        return (
            <div>
                <div>
                    <Input
                        value={searchValue}
                        onChange={e => handleSearch(e.target.value)}
                        placeholder="Search"
                        prefix={<Icon component={searchSvg} />}
                        allowClear
                        style={{width: "100%", height: "32px", marginBottom: "3px"}}
                    />
                </div>
                {menu}
            </div>
        );
    };

    useEffect(() => {
        setFilteredOptions(data);
    }, [data]);

    return (
        <Select
            value={selectedModel}
            style={style}
            onChange={handleChange}
            placeholder={placeholder}
            // showSearch
            optionFilterProp="children"
            dropdownRender={menu => dropDownRender(menu)}
        >
            {Object.entries(filteredOptions).map(([groupLabel, options]) => (
                <OptGroup key={groupLabel} label={groupLabel}>
                    {options.map(option => (
                        <Option key={option} value={option}>
                            {option}
                        </Option>
                    ))}
                </OptGroup>
            ))}
        </Select>
    );
};

export default SwitchModelSelector;
