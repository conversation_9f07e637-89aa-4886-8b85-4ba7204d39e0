import React, {useState} from "react";
import {Form, Input, Button, Card, Select, Flex} from "antd";
import {formValidateRules} from "@/modules-ampcon/utils/util";
import {DeleteOutlined, DownOutlined, PlusOutlined} from "@ant-design/icons";
import dynamicConfigGenStyle from "@/modules-ampcon/components/dynamic_config_gen.module.scss";
import {TIMEZONE_OPTIONS} from "../utils/timezone_options";

const {TextArea} = Input;

const formItemLayout = {
    labelCol: {span: 5},
    wrapperCol: {span: 19},
    style: {
        marginBottom: "10px"
    }
};

const childFormItemLayoutChild = {
    labelCol: {span: 6},
    wrapperCol: {span: 18},
    style: {
        marginBottom: "10px"
    }
};

const elementStyle = {
    width: "280px",
    display: "inline-flex"
};

const DynamicConfigDetail = ({title, formList, name, field, isChild, physicPorts}) => {
    const getDeviceInterfaces = type => {
        return physicPorts
            .filter(port => port.startsWith(type))
            .map(interfaceStr => ({
                label: interfaceStr,
                value: interfaceStr
            }));
    };

    const [expand, setExpand] = useState(true);

    const renderFormItem = (item, itemTitle) => {
        let formDetail = null;
        let initialValue = null;
        if (item.param_default !== undefined && item.param_default !== "") {
            initialValue = item.param_default;
        }
        const rules = [];
        switch (true) {
            case item.type === "int":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                rules.push(formValidateRules.int());
                const intRangeRegex = /\[(\d+)\.\.(\d+)]/;
                const matches = item.param_check.match(intRangeRegex);
                if (matches && matches.length >= 2) {
                    const startValue = Math.min(matches[1], matches[2]);
                    const endValue = Math.max(matches[1], matches[2]);
                    rules.push({
                        validator: async (_, value) => {
                            if (value === "" || value === undefined || /^-?\d+$/g.test(value) === false) {
                                return Promise.resolve();
                            }
                            if (parseInt(value) < parseInt(startValue) || parseInt(value) > parseInt(endValue)) {
                                return Promise.reject(
                                    new Error(`The value must be between ${startValue} and ${endValue}`)
                                );
                            }
                            return Promise.resolve();
                        }
                    });
                }
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                break;
            case item.type === "str" || item.type === "text":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                break;
            case item.type === "textarea":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                formDetail = (
                    <TextArea autoSize={{minRows: 4}} style={elementStyle} placeholder={item.description} allowClear />
                );
                break;
            case item.type === "uintrange":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                rules.push(formValidateRules.uintrange());
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                break;
            case item.type === "boolean":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                const booleanOptions = [
                    {value: true, label: "true"},
                    {value: false, label: "false"}
                ];
                initialValue = initialValue || booleanOptions[0].value;
                formDetail = <Select style={elementStyle} options={booleanOptions} defaultValue={booleanOptions[0]} />;
                break;
            case item.type === "timezone":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                const timeZoneOptions = TIMEZONE_OPTIONS.map(item => ({value: item, label: item}));
                initialValue = initialValue || timeZoneOptions[0].value;
                formDetail = (
                    <Select
                        style={elementStyle}
                        showSearch
                        options={timeZoneOptions}
                        defaultValue={timeZoneOptions[0]}
                        placeholder={item.description}
                        allowClear
                    />
                );
                break;
            case item.type === "aex":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                const aexOptions = Array.from({length: 55}, (_, index) => index + 1).map(item => ({
                    value: `ae${item}`,
                    label: `ae${item}`
                }));
                initialValue = initialValue || aexOptions[0].value;
                formDetail = (
                    <Select
                        style={elementStyle}
                        showSearch
                        options={aexOptions}
                        defaultValue={aexOptions[0]}
                        placeholder={item.description}
                        allowClear
                    />
                );
                break;
            case item.type === "macaddr":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                rules.push(formValidateRules.macaddr());
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                break;
            case item.type === "IPv4":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                rules.push(formValidateRules.ipv4());
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                break;
            case item.type === "IPv4Net":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                rules.push(formValidateRules.ipv4net());
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                break;
            case item.type === "ipv4range":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                rules.push(formValidateRules.ipv4range());
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                break;
            case item.type === "IPv6":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                rules.push(formValidateRules.ipv6());
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                break;
            case item.type === "IPv6Net":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                rules.push(formValidateRules.ipv6net());
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                break;
            case item.type === "ipv6range":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                rules.push(formValidateRules.ipv6range());
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                break;
            case item.type === "IPv4/IPv6":
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                rules.push(formValidateRules.ipv4ipv6());
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                break;
            case item.type.includes("enum"):
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                const enumValues = item.type.replace("enum[", "").replace("]", "").replace(", ", ",").split(",");
                const enumOptions = enumValues.map(item => ({value: item, label: item}));
                initialValue = initialValue || enumOptions[0].value;
                formDetail = (
                    <Select
                        style={elementStyle}
                        defaultValue={enumOptions[0]}
                        showSearch
                        options={enumOptions}
                        allowClear
                    />
                );
                break;
            case item.type.includes("ge-") || item.type.includes("te-") || item.type.includes("xe-"):
                if (item.required === "required" || item.required === undefined) {
                    rules.push({required: true, message: "This value is required."});
                }
                const itemType = item.type.match(/^(te-|xe-|ge-)/);
                if (!itemType) {
                    return null;
                }
                const interfacesOptions = getDeviceInterfaces(itemType[0]);
                if (!Array.isArray(interfacesOptions) || interfacesOptions.length === 0) {
                    formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
                } else {
                    [initialValue] = interfacesOptions;
                    formDetail = (
                        <Select style={elementStyle} options={interfacesOptions} defaultValue={initialValue} />
                    );
                }
                break;
            default:
                formDetail = <Input style={elementStyle} placeholder={item.description} allowClear />;
        }

        return (
            <div>
                {(() => {
                    if (item.isMapList === true) {
                        return (
                            <Form.List key={item.key} name={[...name, title, itemTitle, item.key]} initialValue={[""]}>
                                {(fields, {add, remove}) => (
                                    <div style={{display: "flex", rowGap: 16, flexDirection: "column"}}>
                                        {fields.map((field, index) => (
                                            <div style={{marginBottom: 8, display: "flex", flexDirection: "row"}}>
                                                <DynamicConfigDetail
                                                    title={item.name}
                                                    formList={JSON.parse(JSON.stringify(item.mapListChildren))}
                                                    name={[...name, title, item.key]}
                                                    field={field}
                                                    isChild
                                                    physicPorts={physicPorts}
                                                />
                                                {fields.length > 1 ? (
                                                    <>
                                                        <DeleteOutlined
                                                            style={{marginLeft: "20px"}}
                                                            onClick={() => {
                                                                remove(field.name);
                                                            }}
                                                        />
                                                        <PlusOutlined
                                                            style={{marginLeft: "5px"}}
                                                            onClick={() => {
                                                                add("", index + 1);
                                                            }}
                                                        />
                                                    </>
                                                ) : (
                                                    <>
                                                        <DeleteOutlined
                                                            style={{
                                                                marginLeft: "20px",
                                                                visibility: "hidden"
                                                            }}
                                                        />
                                                        <PlusOutlined
                                                            style={{marginLeft: "5px"}}
                                                            onClick={() => {
                                                                add("", index + 1);
                                                            }}
                                                        />
                                                    </>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </Form.List>
                        );
                    }
                    if (item.isList === true) {
                        return (
                            <Form.List key={item.key} name={[...name, title, itemTitle, item.key]} initialValue={[""]}>
                                {(fields, {add, remove}) => (
                                    <>
                                        {fields.map((field, index) => (
                                            <Form.Item
                                                {...formItemLayout}
                                                label={item.type !== "checkbox" ? item.name.replace(/_/g, " ") : ""}
                                                valuePropName={item.type === "checkbox" ? "checked" : "value"}
                                            >
                                                <Form.Item
                                                    {...field}
                                                    style={{height: "100%"}}
                                                    validateTrigger={["onChange", "onBlur"]}
                                                    noStyle
                                                    label={item.type !== "checkbox" ? item.name.replace(/_/g, " ") : ""}
                                                    valuePropName={item.type === "checkbox" ? "checked" : "value"}
                                                    rules={rules}
                                                    initialValue={initialValue === null ? undefined : initialValue}
                                                >
                                                    {formDetail}
                                                </Form.Item>
                                                {fields.length > 1 ? (
                                                    <>
                                                        <DeleteOutlined
                                                            style={{marginLeft: "20px"}}
                                                            onClick={() => {
                                                                remove(field.name);
                                                            }}
                                                        />
                                                        <PlusOutlined
                                                            style={{marginLeft: "5px"}}
                                                            onClick={() => {
                                                                add("", index + 1);
                                                            }}
                                                        />
                                                    </>
                                                ) : (
                                                    <>
                                                        <DeleteOutlined
                                                            style={{
                                                                marginLeft: "20px",
                                                                visibility: "hidden"
                                                            }}
                                                        />
                                                        <PlusOutlined
                                                            style={{marginLeft: "5px"}}
                                                            onClick={() => {
                                                                add("", index + 1);
                                                            }}
                                                        />
                                                    </>
                                                )}
                                            </Form.Item>
                                        ))}
                                    </>
                                )}
                            </Form.List>
                        );
                    }
                    return isChild ? (
                        <Form.Item
                            {...childFormItemLayoutChild}
                            name={[field.name, item.key]}
                            label={item.type !== "checkbox" ? item.name.replace(/_/g, " ") : ""}
                            valuePropName={item.type === "checkbox" ? "checked" : "value"}
                            validateTrigger={["onChange", "onBlur"]}
                            rules={rules}
                            initialValue={initialValue === null ? undefined : initialValue}
                        >
                            {formDetail}
                        </Form.Item>
                    ) : (
                        <Form.Item
                            {...formItemLayout}
                            key={item.key}
                            name={[title, itemTitle, item.key]}
                            label={item.type !== "checkbox" ? item.name.replace(/_/g, " ") : ""}
                            valuePropName={item.type === "checkbox" ? "checked" : "value"}
                            validateTrigger={["onChange", "onBlur"]}
                            rules={rules}
                            initialValue={initialValue === null ? undefined : initialValue}
                        >
                            {formDetail}
                        </Form.Item>
                    );
                })()}
            </div>
        );
    };

    return formList.map(item =>
        isChild ? (
            <Card
                title={
                    <Flex justify="space-between">
                        <div>{title}</div>
                        <a
                            style={{
                                fontSize: 12
                            }}
                            onClick={() => {
                                setExpand(!expand);
                            }}
                        >
                            <Button icon={<DownOutlined rotate={expand ? 0 : 270} />} />
                        </a>
                    </Flex>
                }
                style={{width: "88%", marginBottom: "5px"}}
                className={`${expand ? "expand" : "collapse"} ${dynamicConfigGenStyle.tileFormCardChildren}`}
            >
                <div style={{display: expand ? "block" : "none"}}>
                    {item.formData.map(i => {
                        if (isChild && (i.isList === true || i.isMapList === true)) {
                            return null;
                        }
                        return <>{renderFormItem(i, item.title)}</>;
                    })}
                </div>
            </Card>
        ) : (
            <Card title={item.title} style={{marginBottom: 18}} className={dynamicConfigGenStyle.tileFormCard}>
                {item.formData.map(i => (
                    <>{renderFormItem(i, item.title)}</>
                ))}
            </Card>
        )
    );
};

const DynamicConfigGen = ({title, physicPorts = "", form, formList, setFormList}) => {
    return (
        <Form
            name={title}
            form={form}
            layout="horizontal"
            labelAlign="left"
            autoComplete="off"
            labelCol={{span: 5}}
            wrapperCol={{span: 19}}
            labelWrap
            className="label-wrap"
        >
            <DynamicConfigDetail
                title={title}
                formList={formList}
                name={[]}
                setFormList={setFormList}
                isChild={false}
                physicPorts={physicPorts}
            />
        </Form>
    );
};

export default DynamicConfigGen;
