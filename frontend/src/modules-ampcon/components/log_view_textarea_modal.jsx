import {But<PERSON>, <PERSON><PERSON><PERSON>, Flex, Input, Modal} from "antd";
import {forwardRef, useImperativeHandle, useState} from "react";
import {ReloadOutlined} from "@ant-design/icons";
import {queryLog} from "@/modules-ampcon/apis/dashboard_api";
import {getAPUpgradeLogs} from "@/modules-ampcon/apis/upgrade_api";

const LogViewTextareaModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showLogViewTextareaModal: (sn, type) => {
            setSourceType(type);
            if (type === "ap") {
                getAPUpgradeLogs(sn)
                    .then(response => {
                        const logString = (response.info || []).join("\n");
                        setLogContent(logString);
                    })
                    .catch(error => {
                        console.error("Failed to load AP logs:", error);
                    });
            } else {
                queryLog(sn)
                    .then(response => {
                        setLogContent(response);
                    })
                    .catch(error => {
                        console.error("Failed to load logs:", error);
                    });
            }
            setSelectedSN(sn);
            setIsShowModal(true);
        },
        hideLogViewTextareaModal: () => {
            resetModal();
        }
    }));

    const readonlyStyle = {
        minHeight: "350px",
        height: "35vh",
        resize: "vertical",
        background: "#F8FAFB",
        border: "1px solid #FFFFFF",
        backgroundColor: "#F8FAFB",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        maxHeight: "calc(100vh - 500px)",
        marginBottom: -20
    };

    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedSN, setSelectedSN] = useState("");
    const [logContent, setLogContent] = useState("");
    const [sourceType, setSourceType] = useState(null);

    const resetModal = () => {
        setIsShowModal(false);
        setSelectedSN("");
        setLogContent("");
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <>
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        {`${selectedSN} logs`}
                        <Button
                            type="text"
                            className="ant-modal-close"
                            style={{marginRight: "30px", marginTop: "2px"}}
                            icon={<ReloadOutlined />}
                            onClick={() => {
                                const reloadLog = () => {
                                    queryLog(selectedSN).then(response => {
                                        setLogContent(response);
                                    });
                                };
                                const reloadAPLogs = () => {
                                    getAPUpgradeLogs(selectedSN).then(response => {
                                        const logString = (response.info || []).join("\n");
                                        setLogContent(logString);
                                    });
                                };

                                if (sourceType === "ap") {
                                    reloadAPLogs();
                                } else {
                                    reloadLog();
                                }
                            }}
                        />
                    </div>
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </>
            }
            open={isShowModal}
            onCancel={() => {
                resetModal();
            }}
            footer={null}
        >
            <Flex vertical style={{flex: 1}}>
                <Input.TextArea style={readonlyStyle} value={logContent} rows={19} readOnly />
            </Flex>
        </Modal>
    );
});

export default LogViewTextareaModal;
