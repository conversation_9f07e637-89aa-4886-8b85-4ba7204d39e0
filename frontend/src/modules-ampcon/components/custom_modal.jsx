import {Modal, Button, Input, Divider, Form, Select, Space, message, Checkbox} from "antd";
import Icon, {ReloadOutlined} from "@ant-design/icons";
import React, {useEffect, useState} from "react";
import modal from "antd/es/modal";
import {buttonProps} from "@/modules-ampcon/utils/util";
import {confirmSvg, closeModal} from "@/utils/common/iconSvg";
import {getAvaliableSwitch} from "@/modules-ampcon/apis/inventory_api";

const {TextArea} = Input;

export const LogModal = ({title, isModalOpen, onCancel, fetchLogAPI, fetchAPIParams, modalClass = ""}) => {
    const readonlyStyle = {
        minHeight: "330px",
        height: "58vh",
        resize: "vertical",
        background: "#F8FAFB",
        border: "1px solid #FFFFFF",
        backgroundColor: "#F8FAFB",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        maxHeight: "calc(100vh - 500px)"
    };

    const [content, setContent] = useState("");

    const fetchLog = async () => {
        if (fetchAPIParams.every(item => item === null || item === undefined)) {
            return;
        }
        const config = await fetchLogAPI(...fetchAPIParams);
        setContent(config);
    };

    const handleRefresh = async () => {
        fetchLog().then(() => {});
    };

    useEffect(() => {
        fetchLog().then(() => {});
    }, [title, fetchAPIParams]);

    return (
        <Modal
            className={modalClass || ""}
            title={
                <>
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        {title}{" "}
                        <Button
                            type="text"
                            className="ant-modal-close"
                            style={{marginRight: "30px"}}
                            icon={
                                <ReloadOutlined
                                    className="anticon anticon-close ant-modal-close-icon"
                                    style={{marginTop: "3px"}}
                                />
                            }
                            onClick={handleRefresh}
                        />
                    </div>
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </>
            }
            open={isModalOpen}
            onCancel={onCancel}
            footer={null}
        >
            <TextArea style={readonlyStyle} rows="19" readOnly value={content} />
        </Modal>
    );
};

export const ViewExportModal = ({title, isModalOpen, onCancel, fetchDataAPI, fetchAPIParams, modalClass = ""}) => {
    const [content, setContent] = useState("");

    const fetchReport = async () => {
        // console.log("fetchAPIParams", fetchAPIParams);
        if (fetchAPIParams.every(item => item === null || item === undefined)) {
            return;
        }
        const config = await fetchDataAPI(...fetchAPIParams);
        setContent(config);
    };

    const handleExport = () => {
        const fileBlob = new Blob([content], {type: "text/plain"});
        const downloadLink = document.createElement("a");
        downloadLink.href = URL.createObjectURL(fileBlob);
        downloadLink.download = fetchAPIParams[0].trim().replace(/[\s:<>]/g, "_");
        downloadLink.click();
    };

    useEffect(() => {
        fetchReport().then(() => {});
    }, [title, fetchAPIParams]);

    return (
        <Modal
            className={modalClass || ""}
            title={
                <div>
                    {title || "View Report"}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onCancel={onCancel}
            footer={[
                <Divider style={{marginTop: 0, marginBottom: 20}} />,
                <Button key="ok" type="primary" onClick={handleExport}>
                    Export Report
                </Button>
            ]}
        >
            <TextArea
                rows="18"
                style={{
                    resize: "none",
                    height: "268.23px",
                    minHeight: "268.23px",
                    background: "#F8FAFB",
                    border: "1px solid #FFFFFF",
                    fontSize: "16px",
                    borderRadius: "4px"
                }}
                readOnly
                value={content}
            />
        </Modal>
    );
};

let isModalOpen = false;

export const confirmModalAction = (content, onOk, onCancel = () => {}, isAsync = false) => {
    if (isModalOpen) {
        return;
    }
    isModalOpen = true;
    const handleClose = () => {
        onCancel();
        isModalOpen = false;
    };
    let modalInstance;

    const wrappedOnOk = () => {
        if (!onOk) {
            handleClose();
            return;
        }

        if (isAsync) {
            return onOk()
                .then(() => {
                    isModalOpen = false;
                    modalInstance?.destroy();
                })
                .catch(err => {
                    message.error("Async confirm failed:", err);
                });
        }
        onOk();
        isModalOpen = false;
        modalInstance?.destroy();
    };
    modalInstance = modal.confirm({
        title: (
            <div
                style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    padding: "0px 0px 24px 0px",
                    borderBottom: "1px solid #E7E7E7"
                }}
            >
                <span style={{fontSize: "20px", color: "#212519"}}>Note</span>
                <div>
                    <span
                        className="ant-modal-close"
                        style={{border: 0, color: "#A2ACB2"}}
                        onClick={() => {
                            modalInstance?.destroy();
                        }}
                    >
                        <span className="ant-modal-close-x">
                            <span role="img" aria-label="close" className="anticon anticon-close ant-modal-close-icon">
                                <Icon component={closeModal} />
                            </span>
                        </span>
                    </span>
                </div>
            </div>
        ),
        content: (
            <Space
                style={{
                    marginTop: "16px",
                    height: "auto",
                    minHeight: "55px",
                    width: "100%"
                }}
            >
                <Icon component={confirmSvg} style={{paddingRight: "5px"}} />
                <div
                    style={{
                        wordBreak: "break-word",
                        overflowWrap: "break-word",
                        whiteSpace: "normal",
                        flex: 1
                    }}
                >
                    {content}
                </div>
            </Space>
        ),
        okText: "Yes",
        cancelText: "No",
        ...buttonProps,
        icon: null,
        onOk: wrappedOnOk,
        onCancel: handleClose,
        className: "ampcon-mini-modal",
        maskClosable: true,
        afterClose: handleClose,
        autoFocusButton: null
    });
};

export const confirmModalActionWithCheckbox = (content, checkboxLabel, onOk, onCancel = () => {}, isAsync = false) => {
    if (isModalOpen) {
        return;
    }
    isModalOpen = true;
    const handleClose = () => {
        onCancel();
        isModalOpen = false;
    };
    let modalInstance;

    const ModalBody = () => {
        const [checked, setChecked] = useState(false);

        useEffect(() => {
            modalInstance?.update({
                okButtonProps: {
                    disabled: !checked
                }
            });
        }, [checked]);

        return (
            <div>
                <Space style={{marginTop: "16px", height: "auto", minHeight: "55px"}}>
                    <Icon component={confirmSvg} style={{paddingRight: "5px"}} />
                    {content}
                </Space>
                <Checkbox style={{marginTop: "16px"}} onChange={e => setChecked(e.target.checked)}>
                    <span>{checkboxLabel}</span>
                </Checkbox>
            </div>
        );
    };

    const wrappedOnOk = () => {
        if (!onOk) {
            handleClose();
            return;
        }

        if (isAsync) {
            return onOk()
                .then(() => {
                    isModalOpen = false;
                    modalInstance?.destroy();
                })
                .catch(err => {
                    message.error("Async confirm failed:", err);
                });
        }
        onOk();
        isModalOpen = false;
        modalInstance?.destroy();
    };
    modalInstance = modal.confirm({
        title: (
            <div
                style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    padding: "0px 0px 24px 0px",
                    borderBottom: "1px solid #E7E7E7"
                }}
            >
                <span style={{fontSize: "20px", color: "#212519"}}>Note</span>
                <div>
                    <span
                        className="ant-modal-close"
                        style={{border: 0, color: "#A2ACB2"}}
                        onClick={() => {
                            modalInstance?.destroy();
                        }}
                    >
                        <span className="ant-modal-close-x">
                            <span role="img" aria-label="close" className="anticon anticon-close ant-modal-close-icon">
                                <Icon component={closeModal} />
                            </span>
                        </span>
                    </span>
                </div>
            </div>
        ),
        content: <ModalBody />,
        okText: "Yes",
        cancelText: "No",
        ...buttonProps,
        icon: null,
        onOk: wrappedOnOk,
        onCancel: handleClose,
        className: "ampcon-mini-modal",
        maskClosable: true,
        afterClose: handleClose,
        autoFocusButton: null,
        okButtonProps: {
            disabled: true
        }
    });
};

export const treeModalAction = async (form, title, label, tag, initialVal, onOk) => {
    let options = [];

    const available_switch_options = async () => {
        const rs = await getAvaliableSwitch();
        return rs.data.map(item => ({label: item, value: item}));
    };

    if (tag === "SELECT") {
        options = await available_switch_options();
    }

    if (isModalOpen) {
        return;
    }
    isModalOpen = true;
    const handleClose = () => {
        isModalOpen = false;
        form.resetFields();
        modalInstance?.destroy();
    };

    const capitalizeFirstLetter = label => {
        const newLabel = label.split(" ")[0];
        return newLabel.charAt(0).toUpperCase() + newLabel.slice(1);
    };

    const capitalizedLabel = capitalizeFirstLetter(label);

    form.setFieldsValue({
        [title]: initialVal,
        ...(tag === "EDIT_AmpconOTN" && {name: initialVal[0], ip: initialVal[1], type: initialVal[2]})
    });

    const modalInstance = modal.confirm({
        title: (
            <div className="tree-modal-action">
                <span style={{fontSize: "20px", color: "#212519"}}>{capitalizedLabel}</span>
                <div>
                    <span
                        className="ant-modal-close"
                        style={{border: 0, color: "#A2ACB2"}}
                        onClick={() => {
                            modalInstance?.destroy();
                        }}
                    >
                        <span className="ant-modal-close-x">
                            <span role="img" aria-label="close" className="anticon anticon-close ant-modal-close-icon">
                                <Icon component={closeModal} />
                            </span>
                        </span>
                    </span>
                </div>
            </div>
        ),
        content: (
            <div style={{marginBottom: "20px", marginTop: "20px", minHeight: "100px", display: "flex"}}>
                <Form
                    form={form}
                    onFinish={values => {
                        onOk(values);
                    }}
                    labelCol={{span: 14}}
                    wrapperCol={{span: 10}}
                    labelAlign="left"
                >
                    {tag === "INPUT" && (
                        <Form.Item label={label} name={title} rules={[{required: true}]} initialValue={initialVal}>
                            <Input style={{width: "280px", borderRadius: 2}} />
                        </Form.Item>
                    )}
                    {tag === "SELECT" && options && (
                        <Form.Item label={label} name={title} rules={[{required: true}]} initialValue={initialVal}>
                            <Select style={{width: "280px", borderRadius: 2}} options={options} />
                        </Form.Item>
                    )}
                    {tag === "EDIT_AmpconOTN" && (
                        <>
                            <Form.Item label="Name" name="name" rules={[{required: true}]} initialValue={initialVal[0]}>
                                <Input style={{width: "280px", borderRadius: 2}} />
                            </Form.Item>
                            <Form.Item label="Type" name="type" rules={[{required: true}]} initialValue={initialVal[2]}>
                                <Select
                                    style={{width: "280px", borderRadius: 2}}
                                    options={[
                                        {value: "6", label: "DCP920"},
                                        {value: "7", label: "FMT"},
                                        {value: "8", label: "DCS"}
                                    ]}
                                    disabled
                                />
                            </Form.Item>
                            <Form.Item label="IP" name="ip" rules={[{required: true}]} initialValue={initialVal[1]}>
                                <Input style={{width: "280px", borderRadius: 2}} disabled />
                            </Form.Item>
                        </>
                    )}
                </Form>
            </div>
        ),
        ...buttonProps,
        onOk: () => {
            form.submit();
        },
        icon: null,
        okText: "Apply",
        onCancel: handleClose,
        cancelText: "Cancel",
        className: "tree-modal-action-style",
        maskClosable: true,
        afterClose: handleClose
    });
};

export const tipModalAction = (content, onOk) => {
    if (isModalOpen) {
        return;
    }
    isModalOpen = true;
    const handleClose = () => {
        isModalOpen = false;
    };

    const modalInstance = modal.info({
        title: (
            <div
                style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    padding: "0px 0px 24px 0px",
                    borderBottom: "1px solid #E7E7E7"
                }}
            >
                <span style={{fontSize: "20px", color: "#212519"}}>Note</span>
                <div>
                    <span
                        className="ant-modal-close"
                        style={{border: 0, color: "#A2ACB2"}}
                        onClick={() => {
                            modalInstance?.destroy();
                        }}
                    >
                        <span className="ant-modal-close-x">
                            <span role="img" aria-label="close" className="anticon anticon-close ant-modal-close-icon">
                                <Icon component={closeModal} />
                            </span>
                        </span>
                    </span>
                </div>
            </div>
        ),
        content: (
            <Space style={{marginTop: "16px", height: "auto", minHeight: "55px"}}>
                <Icon component={confirmSvg} style={{paddingRight: "5px"}} />
                {content}
            </Space>
        ),
        okText: "Yes",
        okButtonProps: buttonProps.okButtonProps,
        cancelButtonProps: null,
        icon: null,
        onOk: () => {
            handleClose();
            if (onOk) {
                onOk();
            }
        },
        className: "ampcon-mini-modal",
        maskClosable: true,
        afterClose: handleClose,
        autoFocusButton: null
    });
};
