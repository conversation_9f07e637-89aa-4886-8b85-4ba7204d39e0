import {Button, Flex, Input, Tooltip} from "antd";
import {useState} from "react";

const ConfigContentTextarea = ({
    title,
    content,
    saveCallback,
    handleFileContentChange,
    saveConfigButtonToolTip,
    editConfigButtonTooltip,
    cancelEditConfigButtonTooltip
}) => {
    const readonlyStyle = {
        height: "85%",
        resize: "none",
        border: "none",
        backgroundColor: "#F8FAFB",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        flex: 10
        // marginBottom: "30px"
    };
    const editStyle = {
        height: "85%",
        resize: "none",
        border: "1px solid rgb(20, 201, 187)",
        backgroundColor: "#F8FAFB",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        flex: 10
        // marginBottom: "30px"
    };
    const [isEdit, setIsEdit] = useState(false);
    const saveButtonCallback = () => {
        saveCallback();
        setIsEdit(false);
    };

    const editCallback = () => {
        setIsEdit(true);
    };

    const cancelEditCallBack = () => {
        setIsEdit(false);
    };

    return isEdit ? (
        <Flex vertical style={{flex: 1}}>
            <Flex layout="horizontal" justify="space-between" style={{flex: 1, marginBottom: "20px"}}>
                <h1
                    style={{
                        margin: 0,
                        height: 29,
                        fontSize: 21
                    }}
                >
                    {title}
                </h1>
                {saveCallback === undefined ? null : (
                    <Flex layout="horizontal" align="center">
                        <Tooltip title={cancelEditConfigButtonTooltip} placement="topRight">
                            <Button style={{marginRight: "20px", minWidth: "80px"}} onClick={cancelEditCallBack}>
                                Cancel Edit
                            </Button>
                        </Tooltip>
                        <Tooltip title={saveConfigButtonToolTip} placement="topRight">
                            <Button type="primary" style={{minWidth: "80px"}} onClick={saveButtonCallback}>
                                Save
                            </Button>
                        </Tooltip>
                    </Flex>
                )}
            </Flex>
            <Input.TextArea style={editStyle} value={content} onChange={handleFileContentChange} />
        </Flex>
    ) : (
        <Flex vertical style={{flex: 1}}>
            <Flex layout="horizontal" justify="space-between" style={{flex: 1, marginBottom: "20px"}}>
                <h1
                    style={{
                        margin: 0,
                        height: 29,
                        fontSize: 21
                    }}
                >
                    {title}
                </h1>
                {saveCallback === undefined ? null : (
                    <Flex layout="horizontal" align="center">
                        <Tooltip title={editConfigButtonTooltip} placement="topRight">
                            <Button style={{marginRight: "20px", minWidth: "80px"}} onClick={editCallback}>
                                Edit
                            </Button>
                        </Tooltip>

                        <Tooltip title={saveConfigButtonToolTip} placement="topRight">
                            <Button type="primary" style={{minWidth: "80px"}} onClick={saveButtonCallback}>
                                Save
                            </Button>
                        </Tooltip>
                    </Flex>
                )}
            </Flex>
            <Input.TextArea style={readonlyStyle} value={content} readonly />
        </Flex>
    );
};

export default ConfigContentTextarea;
