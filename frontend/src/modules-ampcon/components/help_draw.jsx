import {But<PERSON>, Drawer} from "antd";
import {useState} from "react";
import {ExclamationCircleFilled} from "@ant-design/icons";

export const HelpDraw = ({title, content}) => {
    const [isOpen, setIsOpen] = useState(false);
    return (
        <>
            <Button
                type="primary"
                icon={<ExclamationCircleFilled />}
                onClick={() => {
                    setIsOpen(true);
                }}
            >
                Help
            </Button>
            <Drawer
                title={title}
                onClose={() => {
                    setIsOpen(false);
                }}
                open={isOpen}
            >
                {content}
            </Drawer>
        </>
    );
};
