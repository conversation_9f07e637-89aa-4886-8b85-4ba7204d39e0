import {Flex, Input} from "antd";

const ConfigFilePreview = ({title, content, handleFileContentChange}) => {
    const readonlyStyle = {
        height: "85%",
        resize: "none",
        border: "none",
        backgroundColor: "#F8FAFB",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        flex: 10,
        marginBottom: "30px"
    };

    return (
        <Flex vertical style={{flex: 1}}>
            <Flex layout="horizontal" justify="space-between" style={{flex: 1}}>
                <h1
                    style={{
                        margin: 0,
                        height: 29,
                        fontSize: 21
                    }}
                >
                    {title}
                </h1>
            </Flex>
            <Input.TextArea style={readonlyStyle} value={content} onChange={handleFileContentChange} />
        </Flex>
    );
};

export default ConfigFilePreview;
