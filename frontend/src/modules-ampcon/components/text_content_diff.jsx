import React, {useEffect, useRef} from "react";
import * as monaco from "monaco-editor";

const TextContentDiffComponent = ({text1, text2}) => {
    const editorRef = useRef(null);
    const sanitizedText1 = text1.replace(/&#34;/g, '"');
    const sanitizedText2 = text2.replace(/&#34;/g, '"');
    const originalModel = monaco.editor.createModel(/* set from `originalModel`: */ sanitizedText1, "text/plain");
    const modifiedModel = monaco.editor.createModel(/* set from `modifiedModel`: */ sanitizedText2, "text/plain");
    let diffEditor;
    useEffect(() => {
        diffEditor = monaco.editor.createDiffEditor(editorRef.current, {
            readOnly: true,
            originalEditable: false,
            automaticLayout: true
        });

        diffEditor.setModel({
            original: originalModel,
            modified: modifiedModel
        });

        return () => {
            if (diffEditor) {
                diffEditor.dispose();
            }
        };
    }, [text1, text2]);

    return <div ref={editorRef} style={{height: "70vh", width: "100%", border: "1px solid #ccc"}} />;
};
export default TextContentDiffComponent;
