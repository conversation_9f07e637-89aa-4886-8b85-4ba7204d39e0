import dayjs from "dayjs";
import {DatePicker} from "antd";

export const TelemetryDateRangePicker = ({timeRange, setTimeRange, placeholder = ["Start Date", "End Date"]}) => {
    const {RangePicker} = DatePicker;

    return (
        <RangePicker
            // 使得点击日期的时候跳转到utc-0时间而非本地时间上
            showTime={{
                format: "HH:mm",
                defaultValue: [dayjs().utc(), dayjs().utc()]
            }}
            format="YYYY-MM-DD HH:mm"
            style={{height: "32px", marginLeft: "32px"}}
            value={timeRange[0] && timeRange[1] ? [dayjs.utc(timeRange[0]), dayjs.utc(timeRange[1])] : []}
            onChange={(_, dates) => {
                setTimeRange(dates);
            }}
            placeholder={placeholder}
            disabledDate={current => {
                if (!current) return false;

                const now = dayjs(); // 注意这里用本地时间
                const todayEnd = now.endOf("day");
                const thirtyDaysAgo = now.subtract(30, "day").startOf("day");

                if (!timeRange[0] || (timeRange[0] && timeRange[1])) {
                    return current.isAfter(todayEnd) || current.isBefore(thirtyDaysAgo);
                }

                if (timeRange[0] && !timeRange[1]) {
                    const startDate = timeRange[0]; // 仍然是 UTC 时间
                    return (
                        current.isAfter(todayEnd) ||
                        current.isBefore(thirtyDaysAgo) ||
                        current.isBefore(startDate, "day")
                    );
                }
                return false;
            }}
            disabledTime={(current, type) => {
                const now = dayjs.utc();
                const currentHour = now.hour();
                const currentMinute = now.minute();

                if (type === "start" && timeRange[1]) {
                    const endDate = dayjs.utc(timeRange[1], "YYYY-MM-DD HH:mm");
                    if (current.isSame(endDate, "day")) {
                        const endHour = endDate.hour();
                        const endMinute = endDate.minute();
                        return {
                            disabledHours: () => Array.from({length: 24 - endHour - 1}, (_, i) => endHour + i + 1),
                            disabledMinutes: selectedHour => {
                                return selectedHour === -1 || selectedHour >= endHour
                                    ? Array.from({length: 60 - endMinute - 1}, (_, i) => endMinute + i + 1)
                                    : [];
                            },
                            disabledSeconds: () => []
                        };
                    }
                }
                if (type === "end" && timeRange[0]) {
                    const startDate = dayjs.utc(timeRange[0], "YYYY-MM-DD HH:mm");
                    if (current.isSame(startDate, "day")) {
                        const startHour = startDate.hour();
                        const startMinute = startDate.minute();

                        return {
                            disabledHours: () => {
                                const disabled_before_hours = Array.from({length: startHour}, (_, i) => i);
                                const disabled_after_hours = Array.from(
                                    {length: 24 - currentHour - 1},
                                    (_, i) => currentHour + i + 1
                                );
                                if (current.isSame(now, "day"))
                                    return [...disabled_before_hours, ...disabled_after_hours];
                                return [...disabled_before_hours];
                            },
                            disabledMinutes: selectedHour => {
                                const disabled_before_minutes = Array.from({length: startMinute}, (_, i) => i);
                                const disabled_after_minutes = Array.from(
                                    {length: 60 - currentMinute - 1},
                                    (_, i) => currentMinute + i + 1
                                );
                                if (current.isSame(now, "day")) {
                                    if (selectedHour === startHour && selectedHour === currentHour) {
                                        return [...disabled_before_minutes, ...disabled_after_minutes];
                                    }
                                    if (selectedHour === currentHour) {
                                        return disabled_after_minutes;
                                    }
                                } else if (selectedHour === startHour) {
                                    return disabled_before_minutes;
                                }
                                return [];
                            },
                            disabledSeconds: () => []
                        };
                    }
                }

                // 用户还没选日期时，默认用UTC时间禁用时分
                if (!current || current.isSame(now, "day")) {
                    return {
                        disabledHours: () => Array.from({length: 24 - currentHour - 1}, (_, i) => currentHour + i + 1),
                        disabledMinutes: selectedHour => {
                            return selectedHour === -1 || selectedHour >= currentHour
                                ? Array.from({length: 60 - currentMinute - 1}, (_, i) => currentMinute + i + 1)
                                : [];
                        },
                        disabledSeconds: () => []
                    };
                }

                if (current.isAfter(now, "day")) {
                    return {
                        disabledHours: () => [...Array(24).keys()],
                        disabledMinutes: () => [...Array(60).keys()],
                        disabledSeconds: () => [...Array(60).keys()]
                    };
                }

                return {};
            }}
        />
    );
};
