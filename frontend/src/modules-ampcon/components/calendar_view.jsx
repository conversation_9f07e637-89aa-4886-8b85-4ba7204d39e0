import React from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import interactionPlugin from "@fullcalendar/interaction";

const colorMap = schedule_type => {
    switch (schedule_type) {
        case "DIRECT":
            return "#009688";
        case "ONCE":
            return "#2c3e50";
        case "SCHEDULED":
            return "#f04109";
        default:
            return "#009688";
    }
};

export default function Calendar({eventItems, itemClickHandler}) {
    const calEventItems = eventItems.map(item => {
        const itemDate = new Date(item.execute_time);
        return {
            title: item.playbook_name,
            // startStr: new Date(item.execute_time),
            date: itemDate.toISOString().slice(0, 10),
            color: colorMap(item.schedule_type),
            extendedProps: item
        };
    });

    const handleItemClick = info => {
        itemClickHandler(info.event.extendedProps.job_name);
    };
    const calendarContent = eventInfo => {
        return (
            <div className="custom-event">
                <p className="event-title" style={{marginTop: 0, marginBottom: 0}}>
                    {eventInfo.event.title}
                </p>
                <p className="event-description" style={{marginTop: 0, marginBottom: 0}}>
                    {eventInfo.event.extendedProps.job_name}
                </p>
            </div>
        );
    };

    return (
        <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin]}
            initialView="dayGridMonth"
            headerToolbar={{
                left: "prev,next today",
                center: "title",
                right: "dayGridMonth,timeGridWeek,timeGridDay,listDay"
            }}
            selectable
            selectMirror
            dayMaxEvents
            weekends
            buttonText={{
                today: "TODAY",
                month: "MONTH",
                week: "WEEK",
                day: "DAY",
                list: "LIST"
            }}
            themeSystem="custom"
            events={calEventItems}
            eventClick={handleItemClick}
            eventContent={calendarContent}
        />
    );
}
