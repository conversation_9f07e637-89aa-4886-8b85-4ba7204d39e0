import React, {useRef} from "react";
import Icon from "@ant-design/icons";
import {Button, Tag, Row, Col} from "antd";

import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {refreshSvg} from "@/utils/common/iconSvg";

import styles from "@/modules-ampcon/pages/Service Provision/Logical Routers/logical_router.module.scss";

const PrevFormTable = ({title, prevForm, columns, fetchApi, fetchAPIParams, searchFieldsList, matchFieldsList}) => {
    const tableRef = useRef(null);

    const checkStatus = obj => {
        if (obj.status && Array.isArray(obj.status)) {
            if (obj.status.includes("Normal")) {
                return styles.successTag;
            }
            if (obj.status.includes("Error")) {
                return styles.failedTag;
            }
            return "";
        }
    };

    const capitalizeFirstLetter = str => {
        return (
            str
                .split(" ")
                // .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(" ")
        );
    };
    const capitalizeArrayElements = arr => {
        return arr.map(element => (typeof element === "string" ? capitalizeFirstLetter(element) : element));
    };

    const convertObject = obj => {
        const newObj = {};
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                const newKey = key
                    .replace(/_/g, " ")
                    .split(" ")
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(" ");
                let value = obj[key];

                if (typeof value === "string") {
                    value = capitalizeFirstLetter(value);
                } else if (Array.isArray(value)) {
                    value = capitalizeArrayElements(value);
                }

                newObj[newKey] = value;
            }
        }
        return newObj;
    };
    const convertedForm = convertObject(prevForm);

    return (
        <>
            <div>
                <Row style={{marginTop: "8px"}} gutter={[24, 24]}>
                    {Object.keys(convertedForm).map(key => {
                        if (key === "Status") {
                            return (
                                <Col
                                    style={{
                                        background: "rgb(221, 221, 221, 0.2)",
                                        padding: "6px 12px",
                                        marginLeft: 12,
                                        minWidth: 280
                                    }}
                                >
                                    <span style={{marginRight: 28}}>{key}</span>
                                    {Array.isArray(convertedForm[key]) ? (
                                        convertedForm[key].map(item => (
                                            <Tag className={checkStatus(prevForm)}>{item}</Tag>
                                        ))
                                    ) : (
                                        <Tag className={checkStatus(prevForm)}>{convertedForm[key]}</Tag>
                                    )}
                                </Col>
                            );
                        }
                        return (
                            <Col
                                style={{
                                    background: "rgb(221, 221, 221, 0.2)",
                                    padding: "6px 12px",
                                    marginLeft: 12,
                                    minWidth: 280
                                }}
                            >
                                <span style={{marginRight: 28}}>{key}</span>
                                {Array.isArray(convertedForm[key]) ? (
                                    convertedForm[key].map(item => <span>{item}</span>)
                                ) : (
                                    <span>{convertedForm[key]}</span>
                                )}
                            </Col>
                        );
                    })}
                </Row>
            </div>
            <h2 style={{fontSize: "18px", marginTop: "24px"}}>{title}</h2>
            <AmpConCustomTable
                // dataSource={fakedata}
                columns={columns}
                fetchAPIInfo={fetchApi}
                fetchAPIParams={fetchAPIParams}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <Button
                        style={{width: 104, height: 32}}
                        onClick={() => {
                            tableRef.current.refreshTable();
                        }}
                    >
                        <Icon component={refreshSvg} />
                        Refresh
                    </Button>
                }
                ref={tableRef}
            />
        </>
    );
};

export default PrevFormTable;
