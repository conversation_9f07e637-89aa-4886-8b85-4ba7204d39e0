import {useEffect, useRef} from "react";
import {Graph} from "@antv/x6";
import {calculateSwitchViewTopoVertices} from "@/utils/topo_layout_utils";

Graph.registerNode(
    "init-node",
    {
        inherit: "rect",
        width: 160,
        height: 52,
        attrs: {
            body: {
                stroke: "#26A5E1",
                strokeWidth: 1,
                fill: "rgba(38,165,225,0.1)",
                rx: 4,
                ry: 4
            },
            label: {
                fill: "#26A5E1"
            }
        },
        ports: {
            groups: {
                left: {
                    position: {name: "left", args: {dx: -10}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                right: {
                    position: {name: "right", args: {dx: 10}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                verticesLeft: {
                    position: {name: "left", args: {dx: -20}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                verticesRight: {
                    position: {name: "right", args: {dx: 20}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                }
            }
        }
    },
    true
);

Graph.registerNode(
    "failed-node",
    {
        inherit: "rect",
        width: 160,
        height: 52,
        attrs: {
            body: {
                stroke: "#FF7B43",
                strokeWidth: 1,
                fill: "rgba(255,123,67,0.1)",
                rx: 4,
                ry: 4
            },
            label: {
                fill: "#FF7B43"
            }
        },
        ports: {
            groups: {
                left: {
                    position: {name: "left", args: {dx: -10}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                right: {
                    position: {name: "right", args: {dx: 10}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                verticesLeft: {
                    position: {name: "left", args: {dx: -20}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                verticesRight: {
                    position: {name: "right", args: {dx: 20}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                }
            }
        }
    },
    true
);

Graph.registerNode(
    "success-node",
    {
        inherit: "rect",
        width: 160,
        height: 52,
        attrs: {
            body: {
                stroke: "#2BC174",
                strokeWidth: 1,
                fill: "rgba(43,193,116,0.1)",
                rx: 4,
                ry: 4
            },
            label: {
                fill: "#2BC174"
            }
        },
        ports: {
            groups: {
                left: {
                    position: {name: "left", args: {dx: -10}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                right: {
                    position: {name: "right", args: {dx: 10}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                verticesLeft: {
                    position: {name: "left", args: {dx: -20}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                verticesRight: {
                    position: {name: "right", args: {dx: 20}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                }
            }
        }
    },
    true
);

Graph.registerNode(
    "decom-node",
    {
        inherit: "rect",
        width: 160,
        height: 52,
        attrs: {
            body: {
                stroke: "#AFB4BB",
                strokeWidth: 1,
                fill: "rgba(175,180,187,0.1)",
                rx: 4,
                ry: 4
            },
            label: {
                fill: "#AFB4BB"
            }
        },
        ports: {
            groups: {
                left: {
                    position: {name: "left", args: {dx: -10}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                right: {
                    position: {name: "right", args: {dx: 10}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                verticesLeft: {
                    position: {name: "left", args: {dx: -20}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                },
                verticesRight: {
                    position: {name: "right", args: {dx: 20}},
                    attrs: {
                        circle: {
                            magnet: false,
                            stroke: "#8f8f8f",
                            r: 0
                        }
                    }
                }
            }
        }
    },
    true
);

let deploymentDataLocal = null;

const FlowChart = ({deploymentData}) => {
    const containerRef = useRef(null);
    const graphRef = useRef(null);
    deploymentDataLocal = deploymentData;

    useEffect(() => {
        renderSwitchFlowChart();
    }, []);

    useEffect(() => {
        renderSwitchFlowChart();
    }, [deploymentDataLocal]);

    useEffect(() => {
        window.addEventListener("resize", renderSwitchFlowChart);
        return () => window.removeEventListener("resize", renderSwitchFlowChart);
    }, []);

    const renderSwitchFlowChart = () => {
        if (!deploymentDataLocal || Object.keys(deploymentDataLocal).length === 0) {
            return;
        }
        if (graphRef.current) {
            graphRef.current.dispose();
        }

        graphRef.current = new Graph({
            container: containerRef.current,
            width: containerRef.current?.clientWidth,
            height: containerRef.current?.clientHeight,
            grid: false,
            rotating: {
                enabled: false
            },
            connector: "smooth",
            mousewheel: {
                enabled: false,
                modifiers: "ctrl"
            },
            interacting: {
                nodeMovable: false,
                edgeMovable: false,
                nodeSelectable: false,
                edgeSelectable: false,
                edgeLabelMovable: false, // Disable edge label movement
                edgeLabelSelectable: false, // Disable edge label selection
                edgeLabelConnectable: false // Disable edge label connection
            }
        });
        // adjust the position of the graph
        if (containerRef.current) {
            if (containerRef.current.clientWidth <= 1100) {
                graphRef.current.zoomTo(containerRef.current.clientWidth / 1060);
            }
            const newX = containerRef.current.clientWidth <= 1100 ? 0 : containerRef.current.clientWidth / 2 - 530;
            const newY = containerRef.current.clientWidth <= 1100 ? containerRef.current.clientHeight / 2 - 90 : 0;
            graphRef.current.translate(newX, newY);
        }
        refreshAllCells();
    };

    const refreshAllCells = () => {
        // clear all cells
        graphRef.current.clearCells();

        graphRef.current.addNode({
            id: "parking",
            shape: "init-node",
            x: 0,
            y: 90,
            label: `Parking (${deploymentDataLocal.parking})`,
            ports: {
                items: [
                    {
                        id: "right_port",
                        group: "right"
                    }
                ]
            }
        });

        graphRef.current.addNode({
            id: "configured",
            shape: "init-node",
            x: 225,
            y: 90,
            label: `Configured (${deploymentDataLocal.configured})`,
            ports: {
                items: [
                    {
                        id: "right_port",
                        group: "right"
                    },
                    {
                        id: "left_port",
                        group: "left"
                    }
                ]
            }
        });

        graphRef.current.addNode({
            id: "staged",
            shape: "init-node",
            x: 450,
            y: 90,
            label: `Staged (${deploymentDataLocal.staged})`,
            ports: {
                items: [
                    {
                        id: "right_port",
                        group: "right"
                    },
                    {
                        id: "left_port",
                        group: "left"
                    },
                    {
                        id: "vertices_right",
                        group: "verticesRight"
                    },
                    {
                        id: "vertices_left",
                        group: "verticesLeft"
                    }
                ]
            }
        });

        graphRef.current.addNode({
            id: "deployed",
            shape: "success-node",
            x: 675,
            y: 90,
            label: `Deployed (${deploymentDataLocal.deployed})`,
            ports: {
                items: [
                    {
                        id: "right_port",
                        group: "right"
                    },
                    {
                        id: "left_port",
                        group: "left"
                    },
                    {
                        id: "vertices_right",
                        group: "verticesRight"
                    },
                    {
                        id: "vertices_left",
                        group: "verticesLeft"
                    }
                ]
            }
        });

        graphRef.current.addNode({
            id: "deploy_failed",
            shape: "failed-node",
            x: 675,
            y: 10,
            label: `Deploy Failed (${deploymentDataLocal.deployfailed})`,
            ports: {
                items: [
                    {
                        id: "right_port",
                        group: "right"
                    },
                    {
                        id: "left_port",
                        group: "left"
                    },
                    {
                        id: "vertices_right",
                        group: "verticesRight"
                    },
                    {
                        id: "vertices_left",
                        group: "verticesLeft"
                    }
                ]
            }
        });

        graphRef.current.addNode({
            id: "imported",
            shape: "success-node",
            x: 675,
            y: 170,
            label: `Imported (${deploymentDataLocal.imported})`,
            ports: {
                items: [
                    {
                        id: "right_port",
                        group: "right"
                    },
                    {
                        id: "left_port",
                        group: "left"
                    },
                    {
                        id: "vertices_right",
                        group: "verticesRight"
                    },
                    {
                        id: "vertices_left",
                        group: "verticesLeft"
                    }
                ]
            }
        });

        graphRef.current.addNode({
            id: "decom",
            shape: "decom-node",
            x: 900,
            y: 90,
            label: `Decom (${deploymentDataLocal.decom})`,
            ports: {
                items: [
                    {
                        id: "right_port",
                        group: "right"
                    },
                    {
                        id: "left_port",
                        group: "left"
                    },
                    {
                        id: "vertices_right",
                        group: "verticesRight"
                    },
                    {
                        id: "vertices_left",
                        group: "verticesLeft"
                    }
                ]
            }
        });

        graphRef.current.addEdge({
            source: {cell: "parking", port: "right_port"},
            target: {cell: "configured", port: "left_port"},
            attrs: {
                line: {
                    stroke: "#26A5E1",
                    strokeWidth: 1.5,
                    targetMarker: {
                        name: "block",
                        size: 8,
                        height: 12,
                        fill: "none",
                        stroke: " #26A5E1",
                        open: true
                    }
                }
            }
        });
        graphRef.current.addEdge({
            source: {cell: "configured", port: "right_port"},
            target: {cell: "staged", port: "left_port"},
            attrs: {
                line: {
                    stroke: "#26A5E1",
                    strokeWidth: 1.5,
                    targetMarker: {
                        name: "block",
                        size: 8,
                        height: 12,
                        fill: "none",
                        stroke: " #26A5E1",
                        open: true
                    }
                }
            }
        });
        graphRef.current.addEdge({
            source: {cell: "staged", port: "right_port"},
            target: {cell: "deployed", port: "left_port"},
            attrs: {
                line: {
                    stroke: "#26A5E1",
                    strokeWidth: 1.5,
                    targetMarker: {
                        name: "block",
                        size: 8,
                        height: 12,
                        fill: "none",
                        stroke: " #26A5E1",
                        open: true
                    }
                }
            }
        });
        const stagedNodePosition = graphRef.current.getCellById("staged").getPosition();
        const stagedNodeRightPortPosition = {x: stagedNodePosition.x + 170, y: stagedNodePosition.y + 24};
        const deployFailedNodePosition = graphRef.current.getCellById("deploy_failed").getPosition();
        const deployFailedNodeLeftPortPosition = {
            x: deployFailedNodePosition.x - 10,
            y: deployFailedNodePosition.y + 24
        };
        graphRef.current.addEdge({
            source: {cell: "staged", port: "right_port"},
            target: {cell: "staged", port: "vertices_right"},
            attrs: {
                line: {
                    stroke: "#FF7B43",
                    strokeWidth: 1.5,
                    targetMarker: null
                }
            }
        });
        graphRef.current.addEdge({
            source: {cell: "staged", port: "vertices_right"},
            target: {cell: "deploy_failed", port: "vertices_left"},
            attrs: {
                line: {
                    stroke: "#FF7B43",
                    strokeWidth: 1.5,
                    targetMarker: null
                }
            },
            vertices: calculateSwitchViewTopoVertices(stagedNodeRightPortPosition, deployFailedNodeLeftPortPosition),
            connector: {name: "smooth"}
        });
        graphRef.current.addEdge({
            source: {cell: "deploy_failed", port: "vertices_left"},
            target: {cell: "deploy_failed", port: "left_port"},
            attrs: {
                line: {
                    stroke: "#FF7B43",
                    strokeWidth: 1.5,
                    targetMarker: {
                        name: "block",
                        size: 8,
                        height: 12,
                        fill: "none",
                        stroke: " #FF7B43",
                        open: true
                    }
                }
            }
        });
        graphRef.current.addEdge({
            source: {cell: "deployed", port: "right_port"},
            target: {cell: "decom", port: "left_port"},
            attrs: {
                line: {
                    stroke: "#8f8f8f",
                    strokeWidth: 1.5,
                    targetMarker: {
                        name: "block",
                        size: 8,
                        height: 12,
                        fill: "none",
                        stroke: " #8f8f8f",
                        open: true
                    }
                }
            }
        });
        const importedNodePosition = graphRef.current.getCellById("imported").getPosition();
        const importedNodeRightPortPosition = {x: importedNodePosition.x + 170, y: importedNodePosition.y + 24};
        const decomNodePosition = graphRef.current.getCellById("decom").getPosition();
        const decomNodeLeftPortPosition = {x: decomNodePosition.x - 10, y: decomNodePosition.y + 24};
        graphRef.current.addEdge({
            source: {cell: "imported", port: "right_port"},
            target: {cell: "imported", port: "vertices_right"},
            attrs: {
                line: {
                    stroke: "#8f8f8f",
                    strokeWidth: 1.5,
                    targetMarker: null
                }
            }
        });
        graphRef.current.addEdge({
            source: {cell: "imported", port: "vertices_right"},
            target: {cell: "decom", port: "vertices_left"},
            attrs: {
                line: {
                    stroke: "#8f8f8f",
                    strokeWidth: 1.5,
                    targetMarker: null
                }
            },
            vertices: calculateSwitchViewTopoVertices(importedNodeRightPortPosition, decomNodeLeftPortPosition),
            connector: {name: "smooth"}
        });
        graphRef.current.addEdge({
            source: {cell: "decom", port: "vertices_left"},
            target: {cell: "decom", port: "left_port"},
            attrs: {
                line: {
                    stroke: "#8f8f8f",
                    strokeWidth: 1.5,
                    targetMarker: {
                        name: "block",
                        size: 8,
                        height: 12,
                        fill: "none",
                        stroke: " #8f8f8f",
                        open: true
                    }
                }
            }
        });
    };

    return (
        <div style={{position: "relative", width: "100%", height: "241px"}}>
            <div
                ref={containerRef}
                className="switch-view-flow-chart-container"
                style={{
                    backgroundColor: "#FFFFFF",
                    minWidth: "100%",
                    minHeight: "100%",
                    width: "100%",
                    height: "100%"
                }}
            />
        </div>
    );
};

export default FlowChart;
