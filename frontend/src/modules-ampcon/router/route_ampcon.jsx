import AnsibleJobsList from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/AnsibleJobsList/ansible_jobs_list";
import OtherDevices from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/OtherDevices/other_devices";
import Playbook from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/Playbooks/playbook";
import Schedule from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/Schedules/schedule";
import UserManagement from "@/modules-ampcon/pages/System/user_management";
import SwitchView from "@/modules-ampcon/pages/Dashboard/SwitchView/switch_view";
import ConfigTemplateIndex from "@/modules-ampcon/pages/Service/Switch/ConfigTemplate/config_template_index";
import RoCESwitchManager from "../pages/Monitor/RoCE_Counts/Switch/Switch_manager";
import EventManager from "../pages/Monitor/Event/Event_manager";
import RoceConfigManager from "../pages/Service/Nics/RoceConfig/RoCE_Config_Manager";
import SwitchManager from "@/modules-ampcon/pages/Resource/AuthorityManagement/authority_manager";
import GroupManagement from "@/modules-ampcon/pages/Resource/AuthorityManagement/GroupManagement/group_management";
import SiteManagement from "@/modules-ampcon/pages/Resource/AuthorityManagement/SiteManagement/site_management";
import CLIConfiguration from "@/modules-ampcon/pages/Maintain/CliConfig/cli_config";
import DCSwitch from "@/modules-ampcon/pages/Service/Switch/switch";
import SwitchesIndex from "@/modules-ampcon/pages/Service/Switch/CampusSwitches/switches_index";
import GlobalConfiguration from "@/modules-ampcon/pages/Service/Switch/GlobalConfiguration/global_configuration";
import SwitchConfiguration from "@/modules-ampcon/pages/Service/Switch/SwitchConfiguration/switch_configuration";
import ConfigFileView from "@/modules-ampcon/pages/Service/Switch/ConfigFileView/configfile_view";
import SwitchModel from "@/modules-ampcon/pages/Service/Switch/SwitchModel/switch_model";
import AmpConSystemConfig from "@/modules-ampcon/pages/Service/Switch/SystemConfig/system_config";
import SystemBackup from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/SystemBackup/system_backup";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import Alarms from "@/modules-ampcon/pages/Monitor/Alarm/Alarms/alarms";
import AlarmNotificationRules from "@/modules-ampcon/pages/Monitor/Alarm/AlarmNotificationRules/alarm_notification_rules";
import HistoricalAlarmEmailLogs from "@/modules-ampcon/pages/Monitor/Alarm/HistoricalAlarmEmailLogs/historical_alarm_email_logs";
import WiredClients from "@/modules-ampcon/pages/Monitor/WiredClients/wired_clients";
import LicenseView from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseView/license_view";
import LicenseManagement from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/license_management";
import LicenseLog from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseLog/license_log";
import TelemetryView from "@/modules-ampcon/pages/Dashboard/Telemetry/telemetry_view";
import WirelessView from "@/modules-ampcon/pages/Dashboard/WirelessView/wireless_view";
import TopoEntrance from "@/modules-ampcon/pages/Topo/Topology/topo_entrance";
import TopoUnit from "@/modules-ampcon/pages/PhysicalNetwork/Design/Unit/units";
import TopoUnitDetail from "@/modules-ampcon/pages/PhysicalNetwork/Design/Unit/unit_detail";
import DCTemplates from "@/modules-ampcon/pages/PhysicalNetwork/Design/DCTemplate/dc_templates";
import DCTemplatesDetail from "@/modules-ampcon/pages/PhysicalNetwork/Design/DCTemplate/dc_templates_detail";
import FabricDetail from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_detail";
import CampusFabric from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric_entrance";
import SwitchTelemetry from "@/modules-ampcon/pages/Service/Switch/switch_telemetry";
import FSOSSwitchTelemetry from "@/modules-ampcon/pages/Service/Switch/fsos_switch_telemetry";
import IpPool from "@/modules-ampcon/pages/Resource/Pool/IpPool/ip_pool";
import AsnPool from "@/modules-ampcon/pages/Resource/Pool/AsnPool/asn_pool";
import VniPool from "@/modules-ampcon/pages/Resource/Pool/VniPool/vni_pool";
import NICsInventory from "../pages/Service/Nics/Inventory/nic_inventory";
import NICsMonitoring from "../pages/Service/Nics/Monitoring/nic_monitoring";
import CampusFabricMLAGRecordView from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric_mlag_record_view";
import CampusFabricIPCLOSRecordView from "../pages/Topo/CampusFabric/campus_fabric_ipclos_record_view";
import EmailSetting from "@/modules-ampcon/pages/System/EmailSetting/email_setting";
import HostIndex from "@/modules-ampcon/pages/Service/Hosts/host_index";
import NicsIndex from "../pages/Service/Nics/nics_index";
import DeviceProfileIndex from "../pages/Service/Switch/device_profile_index";
import OpticalModulesIndex from "@/modules-ampcon/pages/Monitor/Telemetry/OpticalModules/optical_modules_index";
import PerformanceStatisticsIndex from "@/modules-ampcon/pages/Monitor/Telemetry/Performancestatistics/performance_statistics_index";
import FabricManagement from "../pages/PhysicalNetwork/Design/FabricManagement/fabric_management";
import RoCEEasydeployManager from "../pages/PhysicalNetwork/RoCE/RoCEEasydeploy/roce_easydeploy_manager";
import AZDetail from "@/pages/Resource/AZ/az_detail";
import UpgradeManagement from "@/pages/Resource/UpgradeManagement/upgrade_management";
import OpticalModules from "@/modules-ampcon/pages/Telemetry/Switch/optical_modules";
import LogicalNetworks from "@/modules-ampcon/pages/Service Provision/Logical Networks/logical_networks";
import LogicalNetworkDetails from "@/modules-ampcon/pages/Service Provision/Logical Networks/logical_network_details";
import LogicalSwitches from "@/modules-ampcon/pages/Service Provision/Logical Switches/logical_switches";
import LogicalSwitchDetail from "@/modules-ampcon/pages/Service Provision/Logical Switches/logical_switches_detail";
import LogicalRouters from "@/modules-ampcon/pages/Service Provision/Logical Routers/logical_routers";
import LogicalRouterDetail from "@/modules-ampcon/pages/Service Provision/Logical Routers/logical_router_detail";
import NetworkAccessTableView from "@/modules-ampcon/pages/Service Provision/Network Access/network_access";
import AZManager from "../pages/Resource/ResourceInterconnection/ResourceInterconnection_manager";
import VLANDomainDetails from "../pages/Resource/ResourceInterconnection/VlanDomain/vlanDomainDetails";
import CreateBareMetalNodeAddition from "../pages/Resource/ResourceInterconnection/NodeAddition/createBareMetalNodeAddition";
import CreateCloudNodeAddition from "../pages/Resource/ResourceInterconnection/NodeAddition/createCloudNodeAddition";
import LoadBalancingManager from "../pages/PhysicalNetwork/RoCE/LoadBalancing/LoadBalancing_manager";
import RoCEPoliciesManager from "../pages/PhysicalNetwork/RoCE/RoCEPolicies/RoCEPolicies_manager";
import CreateAssociateDevice from "@/modules-ampcon/pages/Service Provision/Network Access/create_associate_device";
import SwitchTemplatesView from "../pages/Topo/SwitchTemplates/switch_templates_view";
import SwitchTemplatesCreate from "../pages/Topo/SwitchTemplates/switch_templates_create";
import TemplateDeploymentStatus from "../pages/Topo/SwitchTemplates/template_deployment_status";
import SwitchTemplatesTbleView from "../pages/Topo/SwitchTemplates/switch_templates_table_view";
import QuickActivate from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/quick_activate";
import TrialConvertStandard from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/trial_convert_standard";
import CampusFabricCreateAndEditView from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric_create_and_edit_view";
import CampusFabricTableView from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric_table_view";
import CampusFabricSwitchStatus from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric_deployment_status_table";
import OverlayView from "@/modules-ampcon/pages/Topo/OverlayServiceActivation/overlay_view";
import {viewSiteTopo} from "@/modules-ampcon/apis/campus_blueprint_api";
import SnmpVisualizationView from "@/modules-ampcon/pages/Monitor/SNMPManagement/SNMPVisualization/snmp_visualizartion_view";
import SnmpDataFilteringPage from "@/modules-ampcon/pages/Monitor/SNMPManagement/SNMPDataFiltering/snmp_data_filtering_table_view";
import {useParams} from "react-router-dom";

export const ampConRoute = [
    {
        path: "/dashboard/switch_view",
        element: <ProtectedRoute component={SwitchView} />
    },
    {
        path: "/maintain/network_config/automation/playbooks",
        element: <ProtectedRoute component={Playbook} />
    },
    {
        path: "/maintain/network_config/automation/other_devices",
        element: <ProtectedRoute component={OtherDevices} />
    },
    {
        path: "/maintain/network_config/automation/ansible_jobs_list/:type",
        element: <AnsibleJobsList />
    },
    {
        path: "/maintain/network_config/automation/schedule",
        element: <ProtectedRoute component={Schedule} />
    },
    {
        path: "/maintain/network_config/system_backup",
        element: <ProtectedRoute component={SystemBackup} />
    },
    {
        path: "/maintain/cli_configuration",
        element: <ProtectedRoute component={CLIConfiguration} />
    },
    {
        path: "/system/user_management",
        element: <ProtectedRoute component={UserManagement} />
    },
    {
        path: "/system/email_settings",
        element: <ProtectedRoute component={EmailSetting} />
    },
    {
        path: "/resource/auth_management/device_license_management/:type",
        element: <SwitchManager />
    },
    {
        path: "/resource/auth_management/group_management",
        element: <ProtectedRoute component={GroupManagement} />
    },
    {
        path: "/service/switch/config_template/:type",
        element: <ConfigTemplateIndex />
    },
    {
        path: "/service/switch/switch",
        element: <ProtectedRoute component={DCSwitch} />
    },
    {
        path: "/service/switch/global_configuration",
        element: <ProtectedRoute component={GlobalConfiguration} />
    },
    {
        path: "/service/switch/switch_configuration",
        element: <ProtectedRoute component={SwitchConfiguration} />
    },
    {
        path: "/service/switch/config_file_view",
        element: <ProtectedRoute component={ConfigFileView} />
    },
    {
        path: "/service/switch/switch_model",
        element: <ProtectedRoute component={SwitchModel} />
    },
    {
        path: "/service/switch/system_management",
        element: <ProtectedRoute component={AmpConSystemConfig} />
    },
    {
        path: "/dashboard/telemetry_dashboard",
        element: <ProtectedRoute component={TelemetryView} />
    },
    {
        path: "/service/switch/:sn",
        element: <ProtectedRoute component={SwitchTelemetry} />
    },
    {
        path: "/topo/topology",
        element: <ProtectedRoute component={TopoEntrance} />
    },
    {
        path: "/topo/CampusFabric",
        element: <ProtectedRoute component={CampusFabric} />
    }
];

export const ampConDCRoute = [
    {
        path: "/dashboard/switch_overview",
        element: <ProtectedRoute component={SwitchView} />
    },
    {
        path: "/dashboard/telemetry_dashboard",
        element: <ProtectedRoute component={TelemetryView} />
    },
    {
        path: "/maintain/automation/playbooks",
        element: <ProtectedRoute component={Playbook} />
    },
    {
        path: "/maintain/automation/other_devices",
        element: <ProtectedRoute component={OtherDevices} />
    },
    {
        path: "/maintain/automation/ansible_jobs/:type",
        element: <ProtectedRoute component={AnsibleJobsList} />
    },
    {
        path: "/maintain/automation/scheduler",
        element: <ProtectedRoute component={Schedule} />
    },
    {
        path: "/maintain/system_backup",
        element: <ProtectedRoute component={SystemBackup} />
    },
    {
        path: "/maintain/web_access",
        element: <ProtectedRoute component={CLIConfiguration} />
    },
    {
        path: "/system/user_management",
        element: <ProtectedRoute component={UserManagement} />
    },
    {
        path: "/system/email_settings",
        element: <ProtectedRoute component={EmailSetting} />
    },
    {
        path: "/system/software_licenses/license_view",
        element: <ProtectedRoute component={LicenseView} />
    },
    {
        path: "/system/software_licenses/license_management",
        element: <ProtectedRoute component={LicenseManagement} />
    },
    {
        path: "/system/software_licenses/license_management/quick_activate",
        element: <ProtectedRoute component={QuickActivate} />
    },
    {
        path: "/system/software_licenses/license_management/convert_trial_license",
        element: <ProtectedRoute component={TrialConvertStandard} />
    },
    {
        path: "/system/software_licenses/license_log",
        element: <ProtectedRoute component={LicenseLog} />
    },
    {
        path: "/resource/auth_management/device_license_management/:type",
        element: <SwitchManager />
    },
    {
        path: "/resource/auth_management/group_management",
        element: <ProtectedRoute component={GroupManagement} />
    },
    {
        path: "/resource/auth_management/site_management",
        element: <ProtectedRoute component={SiteManagement} />
    },
    {
        path: "/resource/device_licenses/:type",
        element: <ProtectedRoute component={SwitchManager} />
    },
    {
        path: "/resource/group_management",
        element: <ProtectedRoute component={GroupManagement} />
    },
    {
        path: "/resource/pools/ip_pools",
        element: <ProtectedRoute component={IpPool} />
    },
    {
        path: "/resource/pools/asn_pools",
        element: <ProtectedRoute component={AsnPool} />
    },
    {
        path: "/resource/pools/vni_pools",
        element: <ProtectedRoute component={VniPool} />
    },
    {
        path: "/resource/resource_interconnection/:type",
        element: <ProtectedRoute component={AZManager} />
    },
    {
        path: "/resource/resource_interconnection/vlan_domain/:vlanDomainName",
        element: <ProtectedRoute component={VLANDomainDetails} />
    },
    {
        path: "/resource/resource_interconnection/PoD/:az_name",
        element: <ProtectedRoute component={AZDetail} />
    },
    {
        path: "/resource/resource_interconnection/node_addition/bare_metal/:fabric_name",
        element: <ProtectedRoute component={CreateBareMetalNodeAddition} />
    },
    {
        path: "/resource/resource_interconnection/node_addition/cloud/:fabric_name",
        element: <ProtectedRoute component={CreateCloudNodeAddition} />
    },

    // {
    //     path: "/service/config_template/:type",
    //     element: <ConfigTemplateIndex />
    // },
    // {
    //     path: "/service/switch",
    //     element: <ProtectedRoute component={Switch} />
    // },
    // {
    //     path: "/service/global_configuration",
    //     element: <ProtectedRoute component={GlobalConfiguration} />
    // },
    // {
    //     path: "/service/switch_configuration",
    //     element: <ProtectedRoute component={SwitchConfiguration} />
    // },
    // {
    //     path: "/service/config_file_view",
    //     element: <ProtectedRoute component={ConfigFileView} />
    // },
    // {
    //     path: "/service/switch_model",
    //     element: <ProtectedRoute component={SwitchModel} />
    // },
    // {
    //     path: "/service/system_configuration",
    //     element: <ProtectedRoute component={AmpConSystemConfig} />
    // },
    {
        path: "/device/switches",
        element: <ProtectedRoute component={DCSwitch} />
    },
    {
        path: "/device/hosts/:type",
        element: <HostIndex />
    },
    {
        path: "/device/switches/:sn",
        element: <ProtectedRoute component={SwitchTelemetry} />
    },
    {
        path: "/device/NICs/:type",
        element: <NicsIndex />
    },
    {
        path: "/device/device_profiles/:type",
        element: <DeviceProfileIndex />
    },
    {
        path: "/device/config_templates/:type",
        element: <ConfigTemplateIndex />
    },
    {
        path: "/service_provision/logical_networks",
        element: <ProtectedRoute component={LogicalNetworks} />
    },
    {
        path: "/service_provision/logical_networks/:logical_network_name",
        element: <ProtectedRoute component={LogicalNetworkDetails} />
    },
    {
        path: "/service_provision/logical_switches",
        element: <ProtectedRoute component={LogicalSwitches} />
    },
    {
        path: "/service_provision/logical_switches/:logical_switches_name",
        element: <ProtectedRoute component={LogicalSwitchDetail} />
    },
    {
        path: "/service_provision/logical_routers",
        element: <ProtectedRoute component={LogicalRouters} />
    },
    {
        path: "/service_provision/logical_routers/:logical_routers_name",
        element: <ProtectedRoute component={LogicalRouterDetail} />
    },
    {
        path: "/service_provision/network_access",
        element: <ProtectedRoute component={NetworkAccessTableView} />
        // handle: {
        //     breadcrumb: () => "Logical Networks"
        // }
    },
    {
        path: "/service_provision/network_access/:network_access_name",
        element: <ProtectedRoute component={CreateAssociateDevice} />
    },
    {
        path: "/monitor/alerts/alert_list",
        element: <ProtectedRoute component={Alarms} />
    },
    {
        path: "/monitor/alerts/notification_rules",
        element: <ProtectedRoute component={AlarmNotificationRules} />
    },
    {
        path: "/monitor/alerts/notification_history",
        element: <ProtectedRoute component={HistoricalAlarmEmailLogs} />
    },
    // {
    //     path: "/monitor/network/DLB",
    //     element: <ProtectedRoute component={DLB} />
    // },
    // {
    //     path: "/monitor/RoCE_counters/switch/:type",
    //     element: <RoCESwitchManager />
    // },
    // {
    //     path: "/monitor/RoCE_counters/NICs",
    //     element: <ProtectedRoute component={RoceNicsMonitor} />
    // },
    {
        path: "/monitor/telemetry/performance_statistics/:type",
        element: <PerformanceStatisticsIndex />,
        handle: {
            breadcrumb: title => {
                switch (title) {
                    case "nic":
                        return "NIC";
                    default:
                        return undefined;
                }
            }
        }
    },
    {
        path: "/monitor/telemetry/optical_modules/:type",
        element: <OpticalModulesIndex />
    },
    {
        path: "/monitor/telemetry/RoCE_counters/:type",
        element: <RoCESwitchManager />,
        handle: {
            breadcrumb: title => {
                switch (title) {
                    case "pfc_ecn":
                        return "PFC & ECN";
                    default:
                        return undefined;
                }
            }
        }
    },
    {
        path: "/monitor/event_log/:type",
        element: <EventManager />
    },
    // {
    //     path: "/service/switch/:sn",
    //     element: <ProtectedRoute component={SwitchTelemetry} />
    // },
    // {
    //     path: "/service/NICs/inventory",
    //     element: <ProtectedRoute component={NICsInventory} />
    // },
    // {
    //     path: "/service/NICs/monitoring",
    //     element: <ProtectedRoute component={NICsMonitoring} />
    // },
    // {
    //     path: "/service/hosts/inventory",
    //     element: <ProtectedRoute component={HostInventory} />
    // },
    // {
    //     path: "/service/hosts/device_discovery",
    //     element: <ProtectedRoute component={HostDevices} />
    // },
    // {
    //     path: "/service/NICs/modules_overview",
    //     element: <ProtectedRoute component={NICsModulesOverview} />
    // },
    // {
    //     path: "/service/NICs/RoCE_configuration/:type",
    //     element: <RoceConfigManager />
    // },
    {
        path: "/physical_network/topologies",
        element: <ProtectedRoute component={TopoEntrance} />
    },
    {
        path: "/physical_network/design/units",
        element: <ProtectedRoute component={TopoUnit} />
    },
    {
        path: "/physical_network/design/units/:unit_name",
        element: <ProtectedRoute component={TopoUnitDetail} />
    },
    {
        path: "/physical_network/design/dc_templates",
        element: <ProtectedRoute component={DCTemplates} />
    },
    {
        path: "/physical_network/design/dc_templates/:template_name",
        element: <ProtectedRoute component={DCTemplatesDetail} />
    },
    {
        path: "/physical_network/fabrics",
        element: <ProtectedRoute component={FabricManagement} />
    },
    {
        path: "/physical_network/fabrics/:fabric_name",
        element: <ProtectedRoute component={FabricDetail} />
    },
    {
        path: "/physical_network/RoCE/RoCE_easydeploy/:type",
        element: <ProtectedRoute component={RoCEEasydeployManager} />
    },
    {
        path: "/physical_network/RoCE/RoCE_policies/:type",
        element: <ProtectedRoute component={RoCEPoliciesManager} />
    },
    {
        path: "/physical_network/RoCE/load_balancing/:type",
        element: <ProtectedRoute component={LoadBalancingManager} />
    },
    {
        path: "/physical_network/RoCE/NIC_configurations/:type",
        element: <RoceConfigManager />
    },
    {
        path: "/telemetry/switch/optical_modules",
        element: <ProtectedRoute component={OpticalModules} />
    }
    // {
    //     path: "/telemetry/switch/optical_modules_pre",
    //     element: <ProtectedRoute component={OpticalModules} />
    // }
];

export const ampConCampusRoute = [
    // {
    //     path: "/dashboard/wireless_view",
    //     element: <ProtectedRoute component={WirelessView} />
    // },
    {
        path: "/dashboard/switch_overview",
        element: <ProtectedRoute component={SwitchView} />
    },
    {
        path: "/maintain/automation/playbooks",
        element: <ProtectedRoute component={Playbook} />
    },
    {
        path: "/maintain/automation/other_devices",
        element: <ProtectedRoute component={OtherDevices} />
    },
    {
        path: "/maintain/automation/ansible_jobs/:type",
        element: <ProtectedRoute component={AnsibleJobsList} />
    },
    {
        path: "/maintain/automation/scheduler",
        element: <ProtectedRoute component={Schedule} />
    },
    {
        path: "/maintain/system_backup",
        element: <ProtectedRoute component={SystemBackup} />
    },
    {
        path: "/maintain/web_access",
        element: <ProtectedRoute component={CLIConfiguration} />
    },
    {
        path: "/system/user_management",
        element: <ProtectedRoute component={UserManagement} />
    },
    {
        path: "/system/email_settings",
        element: <ProtectedRoute component={EmailSetting} />
    },
    {
        path: "/system/software_licenses/license_view",
        element: <ProtectedRoute component={LicenseView} />
    },
    {
        path: "/system/software_licenses/license_management",
        element: <ProtectedRoute component={LicenseManagement} />
    },
    {
        path: "/system/software_licenses/license_management/quick_activate",
        element: <ProtectedRoute component={QuickActivate} />
    },
    {
        path: "/system/software_licenses/license_management/convert_trial_license",
        element: <ProtectedRoute component={TrialConvertStandard} />
    },
    {
        path: "/system/software_licenses/license_log",
        element: <ProtectedRoute component={LicenseLog} />
    },
    {
        path: "/resource/upgrade_management/:type",
        element: <ProtectedRoute component={UpgradeManagement} />
    },
    {
        path: "/resource/device_licenses/:type",
        element: <ProtectedRoute component={SwitchManager} />
    },
    {
        path: "/resource/group_management",
        element: <ProtectedRoute component={GroupManagement} />
    },
    {
        path: "/resource/auth_management/fabric_management",
        element: <ProtectedRoute component={FabricManagement} />
    },
    {
        path: "/resource/site_management",
        element: <ProtectedRoute component={SiteManagement} />
    },
    {
        path: "/device/switches/:type",
        element: <SwitchesIndex />,
        handle: {
            breadcrumb: title => {
                const titleMap = {
                    picos: "PicOS",
                    fsos: "FSOS"
                };
                return titleMap[title];
            }
        }
    },
    {
        path: "/device/switches/:type/:sn",
        element: (
            <ProtectedRoute
                component={() => {
                    const {type} = useParams();
                    return type === "picos" ? <SwitchTelemetry /> : <FSOSSwitchTelemetry />;
                }}
            />
        )
    },
    {
        path: "/device/config_templates/:type",
        element: <ConfigTemplateIndex />
    },
    {
        path: "/device/device_profiles/:type",
        element: <DeviceProfileIndex />
    },
    {
        path: "/monitor/alerts/alert_list",
        element: <ProtectedRoute component={Alarms} />
    },
    {
        path: "/monitor/alerts/notification_rules",
        element: <ProtectedRoute component={AlarmNotificationRules} />
    },
    {
        path: "/monitor/alerts/notification_history",
        element: <ProtectedRoute component={HistoricalAlarmEmailLogs} />
    },
    {
        path: "/monitor/telemetry",
        element: <ProtectedRoute component={TelemetryView} />
    },
    {
        path: "/monitor/snmp_management/snmp_data_filtering",
        element: <ProtectedRoute component={SnmpDataFilteringPage} />
    },
    {
        path: "/monitor/snmp_management/snmp_visualization",
        element: <ProtectedRoute component={SnmpVisualizationView} />
    },
    {
        path: "/monitor/wired_clients",
        element: <ProtectedRoute component={WiredClients} />
    },
    {
        path: "/service/switch/:sn",
        element: <ProtectedRoute component={SwitchTelemetry} />
    },
    {
        path: "/service/nics/inventory",
        element: <ProtectedRoute component={NICsInventory} />
    },
    {
        path: "/service/nics/monitoring",
        element: <ProtectedRoute component={NICsMonitoring} />
    },
    {
        path: "/network_design/topologies",
        element: <ProtectedRoute component={TopoEntrance} />
    },
    {
        path: "/network_design/campus_fabric",
        element: <ProtectedRoute component={CampusFabricTableView} />
    },
    {
        path: "/network_design/campus_fabric/create",
        element: <ProtectedRoute component={CampusFabricCreateAndEditView} />
    },
    {
        path: "/network_design/campus_fabric/mlag_view/:id",
        element: <ProtectedRoute component={CampusFabricMLAGRecordView} />,
        handle: {
            breadcrumb: async id => {
                const res = await viewSiteTopo(id);
                return res.topology_name || id;
            }
        }
    },
    {
        path: "/network_design/campus_fabric/mlag_view/:id/edit",
        element: <ProtectedRoute component={CampusFabricCreateAndEditView} />
    },
    {
        path: "/network_design/campus_fabric/mlag_view/:id/status",
        element: <ProtectedRoute component={CampusFabricSwitchStatus} />
    },
    {
        path: "/network_design/campus_fabric/ipclos_view/:id",
        element: <ProtectedRoute component={CampusFabricIPCLOSRecordView} />,
        handle: {
            breadcrumb: async id => {
                const res = await viewSiteTopo(id);
                return res.topology_name || id;
            }
        }
    },
    {
        path: "/network_design/campus_fabric/ipclos_view/:id/edit",
        element: <ProtectedRoute component={CampusFabricCreateAndEditView} />
    },
    {
        path: "/network_design/campus_fabric/ipclos_view/:id/status",
        element: <ProtectedRoute component={CampusFabricSwitchStatus} />
    },
    {
        path: "/network_design/overlay_service_activation",
        element: <ProtectedRoute component={OverlayView} />
    },
    {
        path: "/network_design/switch_templates",
        element: <ProtectedRoute component={SwitchTemplatesTbleView} />
    },
    {
        path: "/network_design/switch_templates/view",
        element: <ProtectedRoute component={SwitchTemplatesView} />
    },
    {
        path: "/network_design/switch_templates/create",
        element: <ProtectedRoute component={SwitchTemplatesCreate} />
    },
    {
        path: "/network_design/switch_templates/template_deployment_status",
        element: <ProtectedRoute component={TemplateDeploymentStatus} />
    }
];
