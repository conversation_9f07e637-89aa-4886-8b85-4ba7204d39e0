import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {fetchJobInfoBySwitch, fetchTaskOutputInfo} from "@/modules-ampcon/apis/automation_api";
import {message, Space} from "antd";
import {JobResultView} from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/job_result";
import {useState} from "react";
import {exclamationSvg, offlineSvg, onlineSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";

const SwitchView = () => {
    const matchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "version", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["sn", "mgt_ip", "platform_model", "version", "status"];

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isModalOpenItems, setIsModalOpenItems] = useState(false);
    const [curSn, setCurSn] = useState("");
    const [textAreaValue, setTextAreaValue] = useState("");

    const taskResults = record => {
        setIsModalOpen(true);
        setCurSn(record.sn);
        fetchTaskOutputInfo({sn: record.sn}).then(res => {
            if (res.status === 200) {
                setTextAreaValue(res.info);
            } else {
                message.error("Failed to fetch task output info");
            }
        });
    };

    const columns = [
        createColumnConfig("SN", "sn", TableFilterDropdown),
        {
            ...createColumnConfig("VPN IP", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            }
        },
        createColumnConfig("Model", "platform_model", TableFilterDropdown),
        createColumnConfig("Version", "version", TableFilterDropdown),
        createColumnConfig("Status", "status", TableFilterDropdown),
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a onClick={() => taskResults(record)}>Task Results</a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    return (
        <div style={{marginTop: "-10px"}}>
            <JobResultView
                extraParams={curSn}
                textAreaValue={textAreaValue}
                isModalOpen={isModalOpen}
                setIsModalOpen={setIsModalOpen}
                isModalOpenItems={isModalOpenItems}
                setIsModalOpenItems={setIsModalOpenItems}
            />
            <AmpConCustomTable
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                fetchAPIInfo={fetchJobInfoBySwitch}
            />
        </div>
    );
};

export default SwitchView;
