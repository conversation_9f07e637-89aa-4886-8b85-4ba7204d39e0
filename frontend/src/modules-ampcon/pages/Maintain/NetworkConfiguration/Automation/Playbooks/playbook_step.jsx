import {<PERSON><PERSON>, <PERSON>, DatePicker, Flex, Form, Input, message, Radio, Select, Space, Steps, Tabs, Tree} from "antd";
import React, {useState} from "react";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {fetchDeviceInfo, fetchPlaybookGroupInfo, runPlaybook} from "@/modules-ampcon/apis/automation_api";
import {fetchSwitchInfo} from "@/modules-ampcon/apis/template_api";
import TextArea from "antd/es/input/TextArea";
import dayjs from "dayjs";
import {useForm} from "antd/es/form/Form";
import {exclamationSvg, offlineSvg, onlineSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";

const {DirectoryTree} = Tree;
const {RangePicker} = DatePicker;

const {Option} = Select;

const switchColumns = [
    createColumnConfig("SN", "sn", TableFilterDropdown),
    createColumnConfig("Host Name", "host_name", TableFilterDropdown),
    {
        ...createColumnConfig("Mgt IP", "mgt_ip", TableFilterDropdown),
        render: (_, record) => {
            let iconComponent;
            if (record.reachable_status === 0) {
                iconComponent = onlineSvg;
            } else if (record.reachable_status === 1) {
                iconComponent = offlineSvg;
            } else {
                iconComponent = exclamationSvg;
            }

            return (
                <Space>
                    <Icon component={iconComponent} />
                    {record.mgt_ip}
                </Space>
            );
        }
    },
    createColumnConfig("Model", "platform_model", TableFilterDropdown)
];
const switchSearchFieldsList = ["sn", "host_name", "mgt_ip", "platform_model"];
const switchMatchFieldsList = [
    {name: "sn", matchMode: "fuzzy"},
    {name: "host_name", matchMode: "fuzzy"},
    {name: "mgt_ip", matchMode: "fuzzy"},
    {name: "platform_model", matchMode: "fuzzy"}
];

const groupColumns = [
    createColumnConfig("Group Name", "group_name", TableFilterDropdown),
    createColumnConfig("Group Description", "description", TableFilterDropdown)
];
const groupSearchFieldsList = ["group_name", "description"];
const groupMatchFieldsList = [
    {name: "group_name", matchMode: "exact"},
    {name: "description", matchMode: "fuzzy"}
];

const deviceColumns = [
    createColumnConfig("Device Name", "device_name", TableFilterDropdown),
    createColumnConfig("Device IP", "ip", TableFilterDropdown),
    createColumnConfig("Device User", "device_user", TableFilterDropdown),
    createColumnConfig("Device Port", "device_port", TableFilterDropdown)
];
const deviceSearchFieldsList = ["device_name", "ip", "device_user"];
const deviceMatchFieldsList = [
    {name: "device_name", matchMode: "fuzzy"},
    {name: "ip", matchMode: "fuzzy"},
    {name: "device_user", matchMode: "fuzzy"},
    {name: "device_port", matchMode: "fuzzy"}
];

const CustomStep = ({treeData, playbookName, playbookDesc, setIsModalOpenRun}) => {
    const [current, setCurrent] = useState(0);
    const [isShowSchedule, setIsShowSchedule] = useState(false);
    const [isShowOnceDate, setIsShowOnceDate] = useState(false);
    const [playbookDir, setPlaybookDir] = useState("");
    const [selectedSwitchRowKeys, setSelectedSwitchRowKeys] = useState([]);
    const [selectedGropusRowKeys, setSelectedGropusRowKeys] = useState([]);
    const [selectedDevicesRowKeys, setSelectedDevicesRowKeys] = useState([]);
    const [currentTab, setCurrentTab] = useState("switch");
    const [paramArea, setParamArea] = useState("{}");
    const [extraForm, setExtraForm] = useState({});
    const [extraSwitchForm, setExtraSwitchForm] = useState([]);
    const [extraGropusForm, setExtraGropusForm] = useState([]);
    const [extraDevicesForm, setExtraDevicesForm] = useState([]);

    const [runForm] = useForm();

    const onSelect = async (_, {node}) => {
        setPlaybookDir(node.key);
    };

    const handleFormData = values => {
        switch (values.runType) {
            case "ONCE":
                values.scheduled = {
                    type: "ONCE",
                    params: {
                        start_time: dayjs(values.timeList[0]).format("YYYY-MM-DD HH:mm:ss"),
                        end_time: dayjs(values.timeList[1]).format("YYYY-MM-DD HH:mm:ss")
                    }
                };
                delete values.runType;
                delete values.timeList;
                break;
            case "DIRECT":
                values.scheduled = {type: "DIRECT"};
                delete values.runType;
                break;
            case "SCHEDULED":
                let runTime = values.time < 10 ? `0${values.time}` : values.time;
                if (runTime === "000") {
                    runTime = "00";
                }

                values.scheduled = {
                    type: "SCHEDULED",
                    params: {crontab_expression: `0 ${runTime} */${values.days} * * `}
                };
                delete values.runType;
                delete values.time;
                delete values.days;
                break;
            default:
                break;
        }

        values.vars = JSON.parse(paramArea);
        values.playbookDir = playbookDir;

        const newFormData = {...extraForm, ...values};

        runPlaybook(newFormData).then(res => {
            if (res.status === 200) {
                message.success(res.info);
                setIsModalOpenRun(false);
            } else {
                message.error(res.info);
            }
        });
    };

    const switchRowSelection = {
        selectedRowKeys: selectedSwitchRowKeys,
        selectedRows: extraSwitchForm,
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedSwitchRowKeys(selectedRowKeys);
            setExtraSwitchForm(selectedRows);
        }
    };
    const groupsRowSelection = {
        selectedRowKeys: selectedGropusRowKeys,
        selectedRows: extraGropusForm,
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedGropusRowKeys(selectedRowKeys);
            setExtraGropusForm(selectedRows);
        }
    };
    const devicesRowSelection = {
        selectedRowKeys: selectedDevicesRowKeys,
        selectedRows: extraDevicesForm,
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedDevicesRowKeys(selectedRowKeys);
            setExtraDevicesForm(selectedRows);
        }
    };

    const items = [
        {
            key: "switch",
            label: "Choose Switches",
            children: (
                <AmpConCustomTable
                    rowSelection={switchRowSelection}
                    columns={switchColumns}
                    searchFieldsList={switchSearchFieldsList}
                    matchFieldsList={switchMatchFieldsList}
                    fetchAPIInfo={fetchSwitchInfo}
                />
            )
        },
        {
            key: "groups",
            label: "Choose Groups",
            children: (
                <AmpConCustomTable
                    rowSelection={groupsRowSelection}
                    columns={groupColumns}
                    searchFieldsList={groupSearchFieldsList}
                    matchFieldsList={groupMatchFieldsList}
                    fetchAPIInfo={fetchPlaybookGroupInfo}
                    fetchAPIParams={["switch"]}
                />
            )
        },
        {
            key: "devices",
            label: "Choose Other Devices",
            children: (
                <AmpConCustomTable
                    rowSelection={devicesRowSelection}
                    columns={deviceColumns}
                    searchFieldsList={deviceSearchFieldsList}
                    matchFieldsList={deviceMatchFieldsList}
                    fetchAPIInfo={fetchDeviceInfo}
                />
            )
        }
    ];

    const steps = [
        {
            title: "Select Playbook",
            content: (
                <div style={{marginTop: "20px", marginBottom: "20px", minHeight: "300px", flex: 1}}>
                    <DirectoryTree
                        defaultExpandAll
                        onSelect={onSelect}
                        treeData={treeData}
                        defaultSelectedKeys={[playbookDir]}
                    />
                </div>
            )
        },
        {
            title: "Select Switch",
            content: (
                <Card style={{marginTop: "20px", marginBottom: "20px", paddingTop: "20px"}}>
                    <Tabs
                        items={items}
                        activeKey={currentTab}
                        onChange={val => {
                            setCurrentTab(val);
                        }}
                        className="radioGroupTabs"
                    />
                </Card>
            )
        },
        {
            title: "Set Extra Variables",
            content: (
                <div
                    style={{
                        minHeight: "auto",
                        maxHeight: "fit-content",
                        overflow: "auto",
                        paddingTop: "20px",
                        paddingBottom: "20px"
                    }}
                >
                    <TextArea
                        autoSize={{minRows: 13, maxRows: 13}}
                        value={paramArea}
                        onChange={e => setParamArea(e.target.value)}
                    />
                </div>
            )
        },
        {
            title: "Run Playbook",
            content: (
                <Card style={{marginTop: "20px", marginBottom: "20px", minHeight: "300px"}}>
                    <Form
                        layout="horizontal"
                        form={runForm}
                        onFinish={handleFormData}
                        validateTrigger="onBlur"
                        labelAlign="left"
                        labelCol={{span: 8}}
                        wrapperCol={{span: 16}}
                    >
                        <Form.Item name="playbookName" label="Playbook Name" initialValue={playbookName}>
                            <Input disabled style={{color: "#212529"}} />
                        </Form.Item>
                        <Form.Item name="playbookDesc" label="Playbook Description" initialValue={playbookDesc}>
                            <Input disabled style={{color: "#212529"}} />
                        </Form.Item>
                        <Form.Item
                            name="runType"
                            label="Schedule Type"
                            rules={[{required: true, message: "Please select run playbook type!"}]}
                        >
                            <Radio.Group
                                onChange={e => {
                                    if (e.target.value === "SCHEDULED") {
                                        setIsShowSchedule(true);
                                        setIsShowOnceDate(false);
                                    } else if (e.target.value === "ONCE") {
                                        setIsShowOnceDate(true);
                                        setIsShowSchedule(false);
                                    } else {
                                        setIsShowSchedule(false);
                                        setIsShowOnceDate(false);
                                    }
                                }}
                            >
                                <Radio value="DIRECT">Run now</Radio>
                                <Radio value="ONCE">One Time</Radio>
                                <Radio value="SCHEDULED">Scheduled</Radio>
                            </Radio.Group>
                        </Form.Item>
                        {isShowSchedule && (
                            <Card>
                                <Form.Item label="Schedule" labelCol={{span: 5, offset: 8}} wrapperCol={{span: 5}}>
                                    <Form.Item label="days" name="days" initialValue={6}>
                                        <Select>
                                            {Array.from({length: 31}, (_, index) => (
                                                <Option key={index + 1} value={index + 1}>
                                                    {index + 1}
                                                </Option>
                                            ))}
                                        </Select>
                                    </Form.Item>

                                    <Form.Item label="time" name="time" initialValue="00">
                                        <Select>
                                            {Array.from({length: 24}, (_, index) => (
                                                <Option key={index} value={index}>
                                                    {index < 10 ? `0${index}` : index}
                                                </Option>
                                            ))}
                                        </Select>
                                    </Form.Item>
                                </Form.Item>
                            </Card>
                        )}
                        {isShowOnceDate && (
                            <Card>
                                <Form.Item
                                    label="StartTime ~ EndTime"
                                    labelCol={{span: 5, offset: 3}}
                                    wrapperCol={{span: 19}}
                                    name="timeList"
                                >
                                    <RangePicker showTime onChange={() => {}} />
                                </Form.Item>
                            </Card>
                        )}
                    </Form>
                </Card>
            )
        }
    ];
    const next = () => {
        const allKeys = [...selectedSwitchRowKeys, ...selectedGropusRowKeys, ...selectedDevicesRowKeys];
        const switchForm = {switches: extraSwitchForm.map(item => item.sn)};
        const groupForm = {groupList: extraGropusForm.map(item => item.group_name)};
        const deviceForm = {
            other_device: extraDevicesForm.map(item => ({
                device_ip: item.ip,
                device_name: item.device_name,
                device_user: item.device_user,
                device_pwd: item.device_pwd
            }))
        };
        const allForms = {...switchForm, ...groupForm, ...deviceForm};
        setExtraForm(allForms);
        if (current === 0 && !playbookDir) {
            message.error("Please selected playbook tree");
            return;
        }
        if (current === 1 && allKeys.length === 0) {
            message.error("Please selected at least one switch or group or device");
            return;
        }
        if (current === 2) {
            try {
                if (paramArea.trim()) {
                    JSON.parse(paramArea);
                }
            } catch (e) {
                message.error("The JSON format is invalid");
                return;
            }
        }
        setCurrent(current + 1);
    };
    const prev = () => {
        setCurrent(current - 1);
    };
    const items2 = steps.map(item => ({
        key: item.title,
        title: item.title
    }));
    return (
        <div>
            <Steps current={current} items={items2} />
            <div className="steps-content" style={{minHeight: "275px"}}>
                {steps[current].content}
            </div>
            <Flex justify="end" className="steps-action">
                {current > 0 && (
                    <Button
                        style={{
                            marginRight: "16px"
                        }}
                        onClick={() => prev()}
                    >
                        Previous
                    </Button>
                )}
                {current < steps.length - 1 && (
                    <Button type="primary" onClick={() => next()}>
                        Next
                    </Button>
                )}
                {current === steps.length - 1 && (
                    <Button type="primary" onClick={() => runForm.submit()}>
                        Run Playbook
                    </Button>
                )}
            </Flex>
        </div>
    );
};
export default CustomStep;
