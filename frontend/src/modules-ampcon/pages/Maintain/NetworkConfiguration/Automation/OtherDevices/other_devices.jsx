import {
    AmpConCustomModalForm,
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import Icon, {
    CheckCircleOutlined,
    CloseCircleOutlined,
    EyeInvisibleOutlined,
    EyeOutlined,
    LoadingOutlined
} from "@ant-design/icons";
import {Button, Card, Form, Input, message, Radio, Select, Space, Spin} from "antd";
import React, {useEffect, useRef, useState} from "react";
import {useForm} from "antd/es/form/Form";
import {
    addDeviceInfo,
    checkPingDevicesAPI,
    delDeviceInfo,
    enableDeviceMonitor,
    fetchDeviceInfo
} from "@/modules-ampcon/apis/automation_api";
import {formValidateRules} from "@/modules-ampcon/utils/util";
import {addSvg, pingSvg} from "@/utils/common/iconSvg";
import TextArea from "antd/es/input/TextArea";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const OtherDeviceButton = ({setIsModalOpen, onPing}) => {
    return (
        <>
            <Button
                type="primary"
                style={{width: 100, height: 32}}
                onClick={() => {
                    setIsModalOpen(true);
                }}
            >
                <Icon component={addSvg} />
                Device
            </Button>
            <Button onClick={onPing} style={{width: 88, height: 32}}>
                <Icon component={pingSvg} />
                Ping
            </Button>
        </>
    );
};

const DevicesFormItems = () => {
    const [formContent, setFormContent] = useState(null);
    const [monitorEnabled, setMonitorEnabled] = useState(false);
    const [connectType, setConnectType] = useState("");
    const handleTypeChange = value => {
        if (value === "password") {
            setFormContent(
                <>
                    <Form.Item
                        name="deviceUser"
                        label="User"
                        rules={[{required: true, message: "Please input your username!"}]}
                    >
                        <Input placeholder="Username" style={{width: "280px"}} />
                    </Form.Item>
                    <Form.Item
                        name="devicePassword"
                        label="Password"
                        rules={[{required: true, message: "Please input your password!"}]}
                    >
                        <Input.Password
                            iconRender={visible => {
                                return visible ? (
                                    <EyeOutlined style={{color: "#c5c5c5"}} />
                                ) : (
                                    <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                );
                            }}
                            placeholder="Password"
                            style={{width: "280px"}}
                        />
                    </Form.Item>
                </>
            );
        } else if (value === "pkey") {
            setFormContent(
                <>
                    <Form.Item
                        name="deviceUser"
                        label="User"
                        rules={[{required: true, message: "Please input your username!"}]}
                    >
                        <Input placeholder="Username" style={{width: "280px"}} />
                    </Form.Item>
                    <Form.Item
                        name="devicePkey"
                        label="Pkey"
                        rules={[{required: true, message: "Please input your pkey!"}]}
                    >
                        <TextArea placeholder="Pkey" style={{width: "280px"}} />
                    </Form.Item>
                    {monitorEnabled ? (
                        <Form.Item
                            name="deviceSudoPass"
                            label="SudoPassword"
                            rules={[{required: true, message: "Please input your sudo password!"}]}
                        >
                            <Input.Password
                                iconRender={visible => {
                                    return visible ? (
                                        <EyeOutlined style={{color: "#c5c5c5"}} />
                                    ) : (
                                        <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                    );
                                }}
                                placeholder="SudoPassword"
                                style={{width: "280px"}}
                            />
                        </Form.Item>
                    ) : null}
                </>
            );
        }

        // return formContent;
    };

    useEffect(() => {
        if (monitorEnabled && formContent) {
            handleTypeChange(connectType);
        }
        if (connectType) {
            handleTypeChange(connectType);
        }
    }, [monitorEnabled, connectType]);

    return (
        <>
            <Form.Item
                name="deviceName"
                label="Name"
                rules={[{required: true, message: "Please input your device name!"}]}
            >
                <Input placeholder="Device Name" style={{width: "280px"}} />
            </Form.Item>
            <Form.Item
                name="deviceIp"
                label="IP"
                rules={[{required: true, message: "Please input your ip!"}, formValidateRules.ipv4()]}
            >
                <Input placeholder="Device IP" style={{width: "280px"}} />
            </Form.Item>
            <Form.Item
                name="devicePort"
                label="Port"
                initialValue="22"
                rules={[
                    {
                        required: true,
                        validator(_, value) {
                            if (
                                !isNaN(value) &&
                                Number.isInteger(Number(value)) &&
                                !value.toString().includes(".") &&
                                Number(value) >= 1 &&
                                Number(value) <= 65535
                            ) {
                                return Promise.resolve();
                            }
                            if (value === undefined || value === null || value === "") {
                                return Promise.reject(new Error("Please input your device port!"));
                            }
                            return Promise.reject(new Error("Please input a vaild port number!"));
                        }
                    }
                ]}
            >
                <Input placeholder="Device Port" style={{width: "280px"}} />
            </Form.Item>

            {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC" ? (
                <Form.Item
                    label="Monitor"
                    name="enableMonitor"
                    initialValue={false}
                    rules={[{required: true, message: "Please choose whether to enable monitor!"}]}
                >
                    <Radio.Group
                        onChange={e => setMonitorEnabled(e.target.value)}
                        options={[
                            {label: "Enable", value: true},
                            {label: "Disable", value: false}
                        ]}
                    />
                </Form.Item>
            ) : (
                <Form.Item
                    label="Monitor"
                    hidden
                    name="enableMonitor"
                    initialValue={false}
                    rules={[{required: true, message: "Please choose whether to enable monitor!"}]}
                >
                    <Radio.Group
                        disabled
                        onChange={e => setMonitorEnabled(e.target.value)}
                        options={[
                            {label: "Enable", value: true},
                            {label: "Disable", value: false}
                        ]}
                    />
                </Form.Item>
            )}

            <Form.Item
                name="connectType"
                label="Type"
                rules={[{required: true, message: "Please input your connect type !"}]}
            >
                <Select placeholder="Select a connect type" onChange={setConnectType} style={{width: "280px"}}>
                    <Select.Option value="password" key="password">
                        Password
                    </Select.Option>
                    <Select.Option value="pkey" key="pkey">
                        Pkey
                    </Select.Option>
                </Select>
            </Form.Item>
            {formContent}
        </>
    );
};

const OtherDevices = () => {
    const [form] = useForm();
    const [isModalOpen, setIsModalOpen] = useState();
    const tableRef = useRef(null);

    const [pingData, setPingData] = useState([]);
    const [loading, setLoading] = useState(false);

    const [mointorform] = useForm();
    const [isSudoPassModalOpen, setIsSudoPassModalOpen] = useState(false);
    const [selectedRecord, setSelectRecord] = useState({});

    const handlePingData = async () => {
        const pingRes = tableRef.current.getTableData();
        setLoading(true);
        try {
            const pingData = await checkPingDevicesAPI(pingRes);
            setPingData(pingData.data);
        } catch (error) {
            // console.log("Error fetching data:", error);
            setPingData([]);
        } finally {
            setLoading(false);
        }
    };

    const onSubmit = async values => {
        const ret = await addDeviceInfo(values);
        if (ret.status === 200) {
            form.resetFields();
            setIsModalOpen(false);
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
        tableRef.current.refreshTable();
    };

    const onEnableMonitor = async values => {
        enableMonitor(selectedRecord, values.deviceSudoPassword);
        mointorform.resetFields();
        setIsSudoPassModalOpen(false);
    };

    const matchFieldsList = [
        {name: "device_name", matchMode: "fuzzy"},
        {name: "ip", matchMode: "fuzzy"},
        {name: "device_user", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"},
        {name: "modified_time", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["device_name", "ip", "device_user"];

    const delDevice = async device_name => {
        const ret = await delDeviceInfo(device_name);
        if (ret.status === 200) {
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
        tableRef.current.refreshTable();
    };

    const enableMonitor = async (device, deviceSudoPass) => {
        const ret = await enableDeviceMonitor(device.device_name, deviceSudoPass);
        if (ret.status === 200) {
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
        tableRef.current.refreshTable();
    };

    const columns = [
        createColumnConfig("Device Name", "device_name", TableFilterDropdown),
        createColumnConfig("Device IP", "ip", TableFilterDropdown),
        createColumnConfig("Device User", "device_user", TableFilterDropdown),
        createColumnConfig("CreateTime", "create_time", TableFilterDropdown),
        createColumnConfig("UpdateTime", "modified_time", TableFilterDropdown, "", "", "descend"),
        {
            title: "Status",
            render: (_, record) => {
                if (loading) {
                    return (
                        <div className="custom-spin">
                            <Spin indicator={<LoadingOutlined spin />} size="small" />
                        </div>
                    );
                }
                const pingEntry = pingData.find(p => p.id === record.id);
                if (pingEntry) {
                    return pingEntry.status === 200 ? (
                        <CheckCircleOutlined style={{color: "green"}} />
                    ) : (
                        <CloseCircleOutlined style={{color: "red"}} />
                    );
                }
                return <span>---</span>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete?", () =>
                                        delDevice(record.device_name)
                                    )
                                }
                            >
                                Delete
                            </a>
                            {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC" && (
                                <a
                                    onClick={() =>
                                        confirmModalAction("Are you sure want to enable monitor?", () => {
                                            if (record.type === "pwd") {
                                                enableMonitor(record, "");
                                            } else {
                                                setIsSudoPassModalOpen(true);
                                                setSelectRecord(record);
                                            }
                                        })
                                    }
                                >
                                    Enable Monitor
                                </a>
                            )}
                        </Space>
                    </div>
                );
            }
        }
    ];

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{margin: "8px 0 20px"}}>Other Devices</h2>
            <AmpConCustomModalForm
                modalClass="ampcon-middle-modal"
                title="Create Device"
                isModalOpen={isModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 5
                    }
                }}
                CustomFormItems={DevicesFormItems}
                onCancel={() => {
                    form.resetFields();
                    setIsModalOpen(false);
                }}
                onSubmit={onSubmit}
            />
            <AmpConCustomTable
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={<OtherDeviceButton setIsModalOpen={setIsModalOpen} onPing={handlePingData} />}
                fetchAPIInfo={fetchDeviceInfo}
                ref={tableRef}
            />
            <AmpConCustomModalForm
                modalClass="ampcon-min-modal"
                title="Enable Monitor"
                isModalOpen={isSudoPassModalOpen}
                formInstance={mointorform}
                layoutProps={{
                    labelCol: {
                        span: 6
                    }
                }}
                CustomFormItems={
                    <Form.Item
                        name="deviceSudoPassword"
                        label="SudoPassword"
                        rules={[{required: true, message: "Please input your sudo password!"}]}
                    >
                        <Input.Password
                            iconRender={visible => {
                                return visible ? (
                                    <EyeOutlined style={{color: "#c5c5c5"}} />
                                ) : (
                                    <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                );
                            }}
                            placeholder="Sudo Password"
                            style={{width: "280px"}}
                        />
                    </Form.Item>
                }
                onCancel={() => {
                    mointorform.resetFields();
                    setIsSudoPassModalOpen(false);
                }}
                onSubmit={onEnableMonitor}
            />
        </Card>
    );
};

export default OtherDevices;
