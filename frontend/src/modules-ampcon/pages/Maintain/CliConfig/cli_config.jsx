import {But<PERSON>, Col, Flex, Form, Input, Row, Select, Switch, Radio, Upload} from "antd";
import {useState} from "react";
import {EyeOutlined, EyeInvisibleOutlined} from "@ant-design/icons";
import PrivkeyFileUploader from "@/modules-ampcon/pages/Maintain/CliConfig/priv_key_file_upload";

const CLIConfiguration = () => {
    const [webSSHURL, setWebSSHURL] = useState("");
    const [refreshKey, setRefreshKey] = useState(0);
    const isCampusModule = import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS";
    const [authType, setAuthType] = useState(isCampusModule ? "password" : null);
    const [cliForm] = Form.useForm();

    const onConnect = ({host, port, username, password, privkey, passphrase, new_tab}) => {
        const encodeString = btoa(
            `${host};${port};${username};${password || ""};${privkey || ""};${passphrase || ""};`
        );
        if (new_tab) {
            window.open(`/ssh/?${encodeString}`, "_blank", "noopener,noreferrer");
        } else {
            setWebSSHURL(`/ssh/?${encodeString}`);
            setRefreshKey(prevKey => prevKey + 1);
        }
    };

    const handleKeyFileRead = (privateKey, fileName) => {
        cliForm.setFieldsValue({privkey: privateKey});
        cliForm.setFieldsValue({filename: fileName});
    };

    return (
        <Flex
            vertical
            style={{
                flex: 1,
                background: "#FFFFFF",
                padding: "13px 20px 20px 20px",
                borderRadius: "5px",
                minWidth: "fit-content"
            }}
        >
            <h2 style={{margin: "8px 0 20px"}}>Web Access</h2>

            {isCampusModule && (
                <Form.Item label="Select Authentication" style={{marginBottom: 20}}>
                    <Radio.Group value={authType} onChange={e => setAuthType(e.target.value)}>
                        <Radio value="password">Password</Radio>
                        <Radio value="key">SSH Key</Radio>
                    </Radio.Group>
                </Form.Item>
            )}

            <Form
                form={cliForm}
                style={{marginBottom: 18, flexDirection: "column"}}
                layout="inline"
                onFinish={onConnect}
                className="web-access-form"
            >
                <Row style={{marginBottom: "26px", minWidth: "calc(60vw + 314px)", width: "100%"}} gutter={[16, 26]}>
                    <Col style={{marginRight: "5vw"}}>
                        <Form.Item
                            name="host"
                            className="host-input"
                            rules={[
                                {required: true, message: "This field is required."},
                                {
                                    validator: (_, value) => {
                                        const ipv4Pattern =
                                            /^(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/;
                                        const ipv6Pattern =
                                            /^((([\dA-Fa-f]{1,4}:){7}([\dA-Fa-f]{1,4}|:))|(([\dA-Fa-f]{1,4}:){6}(:[\dA-Fa-f]{1,4}|((\d{1,3}\.){3}\d{1,3})|:))|(([\dA-Fa-f]{1,4}:){5}(((:[\dA-Fa-f]{1,4}){1,2})|:((\d{1,3}\.){3}\d{1,3})|:))|(([\dA-Fa-f]{1,4}:){4}(((:[\dA-Fa-f]{1,4}){1,3})|((:[\dA-Fa-f]{1,4})?:((\d{1,3}\.){3}\d{1,3}))|:))|(([\dA-Fa-f]{1,4}:){3}(((:[\dA-Fa-f]{1,4}){1,4})|((:[\dA-Fa-f]{1,4}){0,2}:((\d{1,3}\.){3}\d{1,3}))|:))|(([\dA-Fa-f]{1,4}:){2}(((:[\dA-Fa-f]{1,4}){1,5})|((:[\dA-Fa-f]{1,4}){0,3}:((\d{1,3}\.){3}\d{1,3}))|:))|(([\dA-Fa-f]{1,4}:)(((:[\dA-Fa-f]{1,4}){1,6})|((:[\dA-Fa-f]{1,4}){0,4}:((\d{1,3}\.){3}\d{1,3}))|:))|((:(((:[\dA-Fa-f]{1,4}){1,7})|((:[\dA-Fa-f]{1,4}){0,5}:((\d{1,3}\.){3}\d{1,3})))|:)))(%.+)?$/;
                                        if (ipv4Pattern.test(value) || ipv6Pattern.test(value)) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject(new Error("Invalid IP address."));
                                    }
                                }
                            ]}
                            label="Host"
                        >
                            <Input
                                placeholder="Host"
                                style={{
                                    width: "15vw",
                                    marginLeft: "30px"
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col style={{marginRight: "5vw"}}>
                        <Form.Item
                            name="username"
                            label="User Name"
                            rules={[
                                {required: true, message: "This field is required."},
                                {max: 32, message: "Username cannot be longer than 32 characters!"},
                                { pattern: /^[^\u4e00-\u9fa5]*$/, message: "Username is not in a valid format." }
                            ]}
                        >
                            <Input
                                placeholder="Username"
                                style={{
                                    width: "15vw"
                                }}
                            />
                        </Form.Item>
                    </Col>

                    {(!isCampusModule || authType !== "key") && (
                        <Col>
                            <Form.Item
                                label="Password"
                                name="password"
                                style={{margin: 0}}
                                rules={[{required: true, message: "This field is required."}]}
                            >
                                <Input.Password
                                    iconRender={visible => {
                                        return visible ? (
                                            <EyeOutlined style={{color: "#c5c5c5"}} />
                                        ) : (
                                            <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                        );
                                    }}
                                    placeholder="Password"
                                    style={{
                                        width: "15vw"
                                    }}
                                />
                            </Form.Item>
                        </Col>
                    )}
                    {isCampusModule && authType === "key" && (
                        <Col style={{display: "flex", gap: "20px"}}>
                            <Form.Item name="privkey" style={{display: "none"}}>
                                <Input type="hidden" />
                            </Form.Item>
                            <Form.Item
                                label="Key"
                                name="filename"
                                style={{margin: 0}}
                                className="privkey-input"
                                rules={[{required: true, message: "This field is required."}]}
                            >
                                <Input
                                    placeholder="SSH Key"
                                    style={{width: "15vw", marginLeft: "36px", color: "#1F1F1F"}}
                                    // onClick={() => importPrivKeyModalRef.current?.showModal(handleImportSuccess)}
                                    disabled
                                />
                            </Form.Item>
                            <PrivkeyFileUploader onFileRead={handleKeyFileRead} />
                        </Col>
                    )}
                </Row>

                {/* Second Row */}
                <Row style={{minWidth: "calc(60vw + 448px)"}} gutter={[16, 26]}>
                    {isCampusModule && authType === "key" && (
                        <Col style={{marginRight: "5vw"}}>
                            <Form.Item label="Passphrase" name="passphrase">
                                <Input.Password
                                    iconRender={visible => {
                                        return visible ? (
                                            <EyeOutlined style={{color: "#c5c5c5"}} />
                                        ) : (
                                            <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                        );
                                    }}
                                    placeholder="Passphrase"
                                    style={{
                                        width: "15vw"
                                    }}
                                />
                            </Form.Item>
                        </Col>
                    )}
                    <Col style={{marginRight: "5vw"}}>
                        <Form.Item
                            label="Port"
                            name="port"
                            className={`port-input-${authType}`}
                            rules={[
                                {required: true, message: "This field is required."},
                                {
                                    validator: async (_, value) => {
                                        if (!/^\d+$/.test(value)) {
                                            return Promise.reject(new Error("Only digits are allowed!"));
                                        }
                                        if (value < 1 || value > 65535) {
                                            return Promise.reject(new Error("Port must be between 1 and 65535."));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <Input
                                placeholder="Port"
                                style={{
                                    width: "15vw",
                                    marginLeft: (() => {
                                        if (!isCampusModule) {
                                            return "32px";
                                        }
                                        return authType === "password" ? "33px" : "40px";
                                    })()
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col
                        style={{
                            marginRight: "5.6vw"
                        }}
                    >
                        <Form.Item
                            label="Session"
                            name="session"
                            style={{margin: 0}}
                            className={`session-input-${authType}`}
                            rules={[{required: true, message: "This field is required."}]}
                        >
                            <Select
                                options={[{value: "SSH", label: "SSH"}]}
                                style={{
                                    width: "15vw",
                                    marginLeft: (() => {
                                        if (!isCampusModule) {
                                            return "22px";
                                        }
                                        return authType === "password" ? "23px" : "16px";
                                    })()
                                }}
                            />
                        </Form.Item>
                    </Col>
                    {(isCampusModule && authType === "password") || !isCampusModule ? (
                        <>
                            <Col style={{marginLeft: "-12px"}}>
                                <Form.Item name="new_tab" label="New Tab" labelCol={{span: 15}} wrapperCol={{span: 9}}>
                                    <Switch style={{marginLeft: "14px"}} />
                                </Form.Item>
                            </Col>
                            <Col>
                                <Form.Item style={{margin: 0}}>
                                    <Button type="primary" htmlType="submit">
                                        Submit
                                    </Button>
                                </Form.Item>
                            </Col>
                        </>
                    ) : null}
                </Row>

                {isCampusModule && authType === "key" && (
                    <Row gutter={[16, 26]} style={{marginTop: 26, minWidth: "calc(40vw + 448px)"}}>
                        <Col style={{marginLeft: "-12px"}}>
                            <Form.Item name="new_tab" label="New Tab" labelCol={{span: 15}} wrapperCol={{span: 9}}>
                                <Switch style={{marginLeft: "14px"}} />
                            </Form.Item>
                        </Col>
                        <Col>
                            <Form.Item style={{margin: 0}}>
                                <Button type="primary" htmlType="submit">
                                    Submit
                                </Button>
                            </Form.Item>
                        </Col>
                    </Row>
                )}
            </Form>

            <Flex
                style={{
                    height: "100%",
                    backgroundColor: "#F8FAFB",
                    borderRadius: "4px",
                    boxSizing: "border-box",
                    padding: "1px"
                }}
            >
                {(() => {
                    if (refreshKey) {
                        return (
                            <iframe
                                src={webSSHURL}
                                title="Dynamic Iframe"
                                width="100%"
                                style={{flexGrow: 1, border: 0}}
                                key={refreshKey}
                            />
                        );
                    }
                })()}
            </Flex>
        </Flex>
    );
};
export default CLIConfiguration;
