import {Button, Form, message, Upload} from "antd";
import {useState, useImperativeHandle, forwardRef, useRef} from "react";
import {UploadOutlined} from "@ant-design/icons";

const PrivkeyFileUploader = ({onFileRead}) => {
    const [fileName, setFileName] = useState("");
    const [error, setError] = useState(null);
    const [isLoading, setIsLoading] = useState(false);

    const beforeUpload = file => {
        setIsLoading(true);
        setFileName(file.name);
        setError(null);

        const reader = new FileReader();
        reader.onload = e => {
            setIsLoading(false);
            const keyContent = e.target.result;

            if (!isValidSshPrivateKey(keyContent)) {
                message.error("The file does not appear to be a valid SSH private key");
                setFileName("");
                return;
            }

            onFileRead && onFileRead(keyContent, file.name);
            message.success("SSH private key file upload successfully");
        };

        reader.onerror = () => {
            setIsLoading(false);
            message.error("Failed to read file content");
            setFileName("");
        };

        reader.readAsText(file);
        return false;
    };

    const isValidSshPrivateKey = content => {
        const trimmed = content.trim();

        const patterns = [
            /^-{5}BEGIN OPENS{2}H PRIVATE KEY-{5}[\S\s]+-{5}END OPENS{2}H PRIVATE KEY-{5}$/,
            /^-{5}BEGIN RSA PRIVATE KEY-{5}[\S\s]+-{5}END RSA PRIVATE KEY-{5}$/,
            /^-{5}BEGIN EC PRIVATE KEY-{5}[\S\s]+-{5}END EC PRIVATE KEY-{5}$/,
            /^-{5}BEGIN DSA PRIVATE KEY-{5}[\S\s]+-{5}END DSA PRIVATE KEY-{5}$/,
            /^-{5}BEGIN PRIVATE KEY-{5}[\S\s]+-{5}END PRIVATE KEY-{5}$/
        ];

        const getKeyType = content => {
            if (content.includes("BEGIN OPENSSH PRIVATE KEY")) return "openssh";
            if (content.includes("BEGIN RSA PRIVATE KEY")) return "rsa";
            if (content.includes("BEGIN EC PRIVATE KEY")) return "ec";
            if (content.includes("BEGIN DSA PRIVATE KEY")) return "dsa";
            if (content.includes("BEGIN PRIVATE KEY")) return "pkcs8";
            if (content.startsWith("ssh-")) return "public";
            return "unknown";
        };

        // const isValidLengthByType = (type, length) => {
        //     switch (type) {
        //         case "openssh":
        //             return length >= 1500 && length <= 2000;
        //         case "rsa":
        //             return length >= 1600 && length <= 3500;
        //         case "ec":
        //             return length >= 300 && length <= 700;
        //         case "dsa":
        //             return length >= 800 && length <= 1500;
        //         case "pkcs8":
        //             return length >= 800 && length <= 2000;
        //         default:
        //             return false;
        //     }
        // };

        const isFormatValid = patterns.some(re => re.test(trimmed));
        const keyType = getKeyType(trimmed);
        // const isLengthValid = isValidLengthByType(keyType, trimmed.length);

        return isFormatValid;
    };

    const handleRemove = () => {
        setFileName("");
        setError(null);
        onFileRead && onFileRead("", "");
    };

    return (
        <div>
            <Upload
                beforeUpload={beforeUpload}
                showUploadList={false}
                maxCount={1}
                accept=".pem,.key,text/plain"
                onRemove={handleRemove}
            >
                <Button icon={<UploadOutlined />} loading={isLoading}>
                    Upload
                </Button>
            </Upload>
        </div>
    );
};
export default PrivkeyFileUploader;
