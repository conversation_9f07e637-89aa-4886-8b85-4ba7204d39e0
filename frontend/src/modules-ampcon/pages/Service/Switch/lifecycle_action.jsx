import {Dropdown, message, Spin, Form, Input, Select, Switch} from "antd";
import {DownOutlined, UpOutlined} from "@ant-design/icons";
import {AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {delSwitch} from "@/modules-ampcon/apis/dashboard_api";
import {getAllSystemConfigBrief} from "@/modules-ampcon/apis/config_api";
import {doRMA, doDecom} from "@/modules-ampcon/apis/rma_api";
import {useState, useEffect} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const {Option} = Select;

const LifecycleAction = ({record, tableRef}) => {
    const [isRMAModalOpen, setIsRMAModalOpen] = useState(false);
    const [spinning, setSpinning] = useState(false);
    const [hoverStatus, setHoverStatus] = useState(false);
    const dropdownMenu = [
        record.status !== "DECOM" &&
        record.status !== "DECOM-Init" &&
        record.status !== "DECOM-Pending" &&
        record.status !== "DECOM-Manual"
            ? {
                  label: "RMA",
                  onClick: () => {
                      setIsRMAModalOpen(true);
                  }
              }
            : null,

        record.status !== "DECOM" &&
        record.status !== "DECOM-Init" &&
        record.status !== "DECOM-Pending" &&
        record.status !== "DECOM-Manual" &&
        (record.import_type === 0 || record.import_type === 2 || record.upgrade_status === 1)
            ? {
                  label: "DECOM",
                  danger: true,
                  onClick: () => {
                      confirmModalAction(`Please confirm you want to DECOM for switch ${record.sn}`, async () => {
                          handleDecom();
                      });
                  }
              }
            : null,
        record.status !== "Provisioning Success"
            ? {
                  label: "Remove",
                  danger: true,
                  onClick: () => {
                      confirmModalAction(`Please confirm you want to remove switch ${record.sn}`, async () => {
                          const response = await delSwitch(record.sn);
                          if (response.status !== 200) {
                              message.error(response.info);
                          } else {
                              message.success(response.info);
                              tableRef.current.refreshTable();
                          }
                      });
                  }
              }
            : null
    ].filter(Boolean);

    const handleRMA = async values => {
        setSpinning(true);
        doRMA(record.sn, record.mgt_ip, values.sn, values.staged, values.systemConfig)
            .then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                    tableRef.current.refreshTable();
                }
                setSpinning(false);
            })
            .catch(() => {
                message.error("RMA Switch failed");
                setSpinning(false);
            });
        tableRef.current.refreshTable();
    };

    const handleDecom = async () => {
        setSpinning(true);
        doDecom(record.sn)
            .then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                }
                tableRef.current.refreshTable();
                setSpinning(false);
            })
            .catch(() => {
                message.error("DECOM Switch failed");
                setSpinning(false);
            });
        tableRef.current.refreshTable();
    };

    return (
        <div>
            <Dropdown menu={{items: dropdownMenu}} trigger={["hover"]} onOpenChange={val => setHoverStatus(val)}>
                <a onClick={e => e.preventDefault()}>
                    <div>Lifecycle Actions {hoverStatus ? <UpOutlined /> : <DownOutlined />}</div>
                </a>
            </Dropdown>
            <RMAModal
                isModalOpen={isRMAModalOpen}
                onCancel={() => {
                    setIsRMAModalOpen(false);
                }}
                onSubmit={handleRMA}
            />
            <Spin spinning={spinning} tip="Loading..." size="large" fullscreen />
        </div>
    );
};

const RMAModal = ({isModalOpen, onCancel, onSubmit}) => {
    const [form] = Form.useForm();
    const [configOptions, setConfigOption] = useState([]);
    const formItems = () => {
        return (
            <>
                <Form.Item name="staged" label="Staged" initialValue={false}>
                    <Switch checkedChildren="ON" unCheckedChildren="OFF" defaultChecked={false} />
                </Form.Item>
                <Form.Item name="sn" label="SN" rules={[{required: true, message: "Please input SN!"}]}>
                    <Input placeholder="SN" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="systemConfig"
                    label="System Configuration"
                    rules={[{required: true, message: "Please select System Configuration!"}]}
                >
                    <Select placeholder="Select a config" style={{width: "280px"}}>
                        {configOptions.map(item => (
                            <Option key={item.system_config_id} value={item.system_config_name}>
                                {item.system_config_name}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
            </>
        );
    };

    const handleCancel = () => {
        onCancel();
        form.resetFields();
    };

    const handleSubmit = () => {
        onSubmit(form.getFieldValue());
        handleCancel();
    };

    useEffect(() => {
        if (isModalOpen) {
            getAllSystemConfigBrief().then(response => {
                if (response.status === 200) {
                    setConfigOption(response.data);
                }
            });
        }
    }, [isModalOpen]);

    return (
        <div>
            <AmpConCustomModalForm
                title="Switch RMA"
                isModalOpen={isModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 8
                    }
                }}
                CustomFormItems={formItems}
                onCancel={handleCancel}
                onSubmit={handleSubmit}
                modalClass="ampcon-middle-modal"
            />
        </div>
    );
};
export default LifecycleAction;
