import React, {forwardRef} from "react";
import PortNode from "./port_node";
import {Flex} from "antd";

const ModulePanel = forwardRef(({ddmData, tableWidth, onlinePorts, portData}, ref) => {
    const getPortStatus = portName => {
        const portEvents = ddmData.filter(item => item.interface === portName);
        if (onlinePorts?.includes(portName)) {
            if (portEvents.length === 0) {
                return "info";
            }
            // 有事件且全部恢复
            if (portEvents.every(event => event.resolved)) {
                return "info";
            }
        }

        // 只会有 warning 和 error，取最高级别
        if (portEvents.length > 0) {
            const hasError = portEvents.some(event => !event.resolved && event.alert_level?.toLowerCase() === "error");
            if (hasError) {
                return "error";
            }
            const hasWarn = portEvents.some(event => !event.resolved && event.alert_level?.toLowerCase() === "warning");
            if (hasWarn) {
                return "warning";
            }
        }

        return "common";
    };

    return (
        <div ref={ref}>
            <Flex
                wrap
                gap="small"
                style={{
                    paddingLeft: "50px",
                    paddingBottom: "12px",
                    background: `linear-gradient(180deg, #EEF1F6 0%, #E0E6EF 100%)`,
                    minWidth: tableWidth - 48
                }}
            >
                <Flex horizontal style={{marginTop: "12px"}}>
                    {portData.map((ports, index) => {
                        return (
                            <Flex vertical gap="8px" key={index}>
                                {ports.map(port => {
                                    if (port === null) {
                                        return <div style={{height: "39.71px"}}> </div>;
                                    }
                                    return (
                                        <Flex gap={port.isGap ? "32px" : "0px"}>
                                            <PortNode
                                                index={port.label}
                                                portName={port.portName}
                                                portType={port.portType}
                                                isSvgReverse={port.isReverse}
                                                status={getPortStatus(port.portName)}
                                            />
                                            <div style={{width: "8px"}} />
                                        </Flex>
                                    );
                                })}
                            </Flex>
                        );
                    })}
                </Flex>
            </Flex>
        </div>
    );
});

export default ModulePanel;
