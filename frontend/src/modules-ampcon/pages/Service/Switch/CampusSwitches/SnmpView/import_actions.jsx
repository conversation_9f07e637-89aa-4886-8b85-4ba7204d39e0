import {Button, Dropdown, Spin, Form, Input, Select, message, TreeSelect, Divider, Radio, Upload} from "antd";
// import <PERSON>agger from "antd/es/upload/Dragger";
import Icon, {InboxOutlined, DownOutlined, UpOutlined, PlusOutlined, MinusOutlined} from "@ant-design/icons";
import React, {useState, useEffect} from "react";
import {formValidateRules} from "@/modules-ampcon/utils/util";
import {AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {useSelector} from "react-redux";
import {batchUpdateSnmpViewData, updateSnmpViewData} from "@/modules-ampcon/apis/dashboard_api";
import uploadFileSvg from "@/modules-ampcon/pages/Service/Switch/CampusSwitches/resource/upload_file.svg?react";
import downloadSVG from "@/modules-ampcon/pages/Service/Switch/CampusSwitches/resource/download_template.svg?react";
import {tipModalAction} from "@/modules-ampcon/components/custom_modal";

const {Option} = Select;
const {Dragger} = Upload;

const ImportActions = ({tableRef}) => {
    const [isSnmpModalOpen, setisSnmpModalOpen] = useState(false);
    const [hoverStatus, setHoverStatus] = useState(false);
    const [isBatchImport, setIsBatchImport] = useState(false);

    const dropdownMenu = [
        {
            label: "Single Import",
            onClick: () => {
                setIsBatchImport(false);
                setisSnmpModalOpen(true);
            }
        },
        {
            label: "Batch Import",
            onClick: () => {
                setIsBatchImport(true);
                setisSnmpModalOpen(true);
            }
        }
    ];

    return (
        <div>
            <Dropdown menu={{items: dropdownMenu}} trigger={["hover"]} onOpenChange={val => setHoverStatus(val)}>
                <Button type="primary" style={{width: "150px"}}>
                    <>
                        Import Actions
                        {hoverStatus ? (
                            <UpOutlined style={{fontSize: "12px", marginLeft: "8px"}} />
                        ) : (
                            <DownOutlined style={{fontSize: "12px", marginLeft: "8px"}} />
                        )}
                    </>
                </Button>
            </Dropdown>
            <SnmpModal
                isModalOpen={isSnmpModalOpen}
                isBatchImport={isBatchImport}
                onCancel={() => setisSnmpModalOpen(false)}
                onSubmitSuccess={() => tableRef.current.refreshTable()}
            />
        </div>
    );
};

const SnmpModal = ({isModalOpen, onCancel, onSubmitSuccess, isBatchImport}) => {
    const [form] = Form.useForm();
    const currentUser = useSelector(state => state.user.userInfo);

    const [snmpVersion, setSnmpVersion] = useState("v2c");
    const [securityLevel, setSecurityLevel] = useState("authPriv");
    const [spinning, setSpinning] = useState(false);

    const [fileList, setFileList] = useState([]); // 上传文件列表
    // const [uploadProgress, setUploadProgress] = useState(0); // 上传进度

    const snmpVersionOptions = [
        {value: "v1", label: "SNMP v1"},
        {value: "v2c", label: "SNMP v2c"},
        {value: "v3", label: "SNMP v3"}
    ];

    const securityLevelOptions = [
        {value: "noAuthNoPriv", label: "noAuthNoPriv"},
        {value: "authNoPriv", label: "authNoPriv"},
        {value: "authPriv", label: "authPriv"}
    ];

    const authProtocolOptions = [
        {value: "MD5", label: "MD5"},
        {value: "SHA", label: "SHA"},
        {value: "SHA-256", label: "SHA-256"}
    ];

    const privProtocolOptions = [
        {value: "DES", label: "DES"},
        {value: "AES-128", label: "AES-128"},
        {value: "AES-256", label: "AES-256"}
    ];

    const handleDownload = () => {
        const templateExamples = [
            {
                description: "Example for SNMP v1 - all fields are required",
                ip: "***********",
                snmpVersion: "v1",
                community: "Public"
            },
            {
                description: "Example for SNMP v2c - all fields are required",
                ip: "***********",
                snmpVersion: "v2c",
                community: "Public"
            },
            {
                description:
                    "Example for SNMP v3 with securityLevel 'noAuthNoPriv' - contextName is optional. If the device does not require a specific SNMP context, use an empty string (do not use spaces).",
                ip: "***********",
                snmpVersion: "v3",
                contextName: "",
                securityUser: "user1",
                securityLevel: "noAuthNoPriv"
            },
            {
                description:
                    "Example for SNMP v3 with securityLevel 'authNoPriv' - contextName is optional. If the device does not require a specific SNMP context, use an empty string (do not use spaces).",
                ip: "***********",
                snmpVersion: "v3",
                contextName: "",
                securityUser: "user2",
                securityLevel: "authNoPriv",
                authProtocol: "MD5",
                authPassword: "AuthPass123"
            },
            {
                description:
                    "Example for SNMP v3 with securityLevel 'authPriv' - contextName is optional. If the device does not require a specific SNMP context, use an empty string (do not use spaces).",
                ip: "***********",
                snmpVersion: "v3",
                contextName: "",
                securityUser: "user3",
                securityLevel: "authPriv",
                authProtocol: "SHA",
                authPassword: "AuthPass456",
                privProtocol: "AES-128",
                privPassword: "PrivPass789"
            }
        ];

        // 转换为标准 JSON 字符串
        const jsonString = JSON.stringify(templateExamples, null, 2);
        const blob = new Blob([jsonString], {type: "application/json"});
        const url = URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.download = "snmp_templates.json";
        link.click();

        // 清理资源
        URL.revokeObjectURL(url);
    };

    const handleRemoveFile = file => {
        // console.log(fileList);
        setFileList(prevFiles => prevFiles.filter(f => f.uid !== file.uid));
    };

    const handleBatchImport = async () => {
        try {
            setSpinning(true);

            if (fileList.length === 0) {
                throw new Error("No files selected");
            }

            const formData = new FormData();
            formData.append("file", fileList[0]);

            const response = await batchUpdateSnmpViewData(formData);
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
                onSubmitSuccess();
            }
        } catch (error) {
            message.error(error.message || "Batch import failed, please check input");
        } finally {
            onCancel();
            setSpinning(false);
            setFileList([]);
        }
    };

    // 动态表单
    const getFormItems = () => {
        return (
            <>
                {isBatchImport && (
                    <>
                        <Form.Item name="downloadTemplate" label="Download Template" className="actionLink">
                            <a onClick={handleDownload}>
                                <Icon component={downloadSVG} style={{marginRight: 4}} />
                                JSON Template
                            </a>
                        </Form.Item>
                        <Form.Item name="uploadFile" label="Upload File" wrapperCol={{span: 12}}>
                            <div id="uploadWidth">
                                <Dragger
                                    name="file"
                                    style={{marginTop: 10}}
                                    beforeUpload={file => {
                                        // 处理文件格式验证
                                        const isJson = file.name.endsWith(".json");
                                        if (isJson) {
                                            if (fileList.length < 1) {
                                                setFileList(prev => [...prev, file]);
                                            } else {
                                                message.error("Only one file can be uploaded at a time");
                                            }
                                        } else {
                                            message.error(`${file.name} is not a json file`);
                                        }
                                        return false;
                                    }}
                                    accept=".json"
                                    fileList={fileList}
                                    onRemove={file => handleRemoveFile(file)}
                                >
                                    <p className="ant-upload-drag-icon" style={{marginBottom: 2, marginTop: 25}}>
                                        <Icon component={uploadFileSvg} style={{width: "20px", height: "20px"}} />
                                    </p>
                                    <p
                                        className="ant-upload-hint"
                                        style={{fontSize: "12px", marginTop: 2, marginBottom: 25}}
                                    >
                                        Drag and drop a file here or <span style={{color: "#14C9BB"}}>Choose File</span>
                                        <br />
                                        (.json File)
                                    </p>
                                </Dragger>
                            </div>
                        </Form.Item>
                    </>
                )}

                {!isBatchImport && (
                    <>
                        <Form.Item
                            name="ip"
                            label="IP Address"
                            rules={[{required: true, message: "Please input IP address!"}, formValidateRules.ipv4()]}
                        >
                            <Input style={{width: "280px"}} placeholder="Example：***********" />
                        </Form.Item>

                        <Form.Item
                            name="snmpVersion"
                            label="SNMP Version"
                            initialValue="v2c"
                            rules={[{required: true, message: "Please select SNMP version!"}]}
                        >
                            <Select
                                style={{width: "280px"}}
                                value={snmpVersion}
                                onChange={value => setSnmpVersion(value)}
                            >
                                {snmpVersionOptions.map(option => (
                                    <Option key={option.value} value={option.value}>
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>

                        {(snmpVersion === "v1" || snmpVersion === "v2c") && (
                            <Form.Item
                                name="community"
                                label="SNMP Community"
                                rules={[{required: true, message: "Please input SNMP community!"}]}
                            >
                                <Input style={{width: "280px"}} />
                            </Form.Item>
                        )}

                        {snmpVersion === "v3" && (
                            <>
                                <Form.Item name="contextName" label="Context Name">
                                    <Input style={{width: "280px"}} />
                                </Form.Item>

                                <Form.Item
                                    name="securityUser"
                                    label="Security User"
                                    rules={[{required: true, message: "Please input security user!"}]}
                                >
                                    <Input style={{width: "280px"}} />
                                </Form.Item>

                                <Form.Item
                                    name="securityLevel"
                                    label="Security Level"
                                    initialValue="authPriv"
                                    rules={[{required: true, message: "Please select security level!"}]}
                                >
                                    <Select
                                        style={{width: "280px"}}
                                        value={securityLevel}
                                        onChange={value => setSecurityLevel(value)}
                                    >
                                        {securityLevelOptions.map(option => (
                                            <Option key={option.value} value={option.value}>
                                                {option.label}
                                            </Option>
                                        ))}
                                    </Select>
                                </Form.Item>

                                {securityLevel !== "noAuthNoPriv" && (
                                    <>
                                        <Form.Item
                                            name="authProtocol"
                                            label="Authentication Protocol"
                                            initialValue="MD5"
                                            rules={[
                                                {required: true, message: "Please select authentication protocol!"}
                                            ]}
                                        >
                                            <Select
                                                placeholder="Select authentication protocol"
                                                style={{width: "280px"}}
                                            >
                                                {authProtocolOptions.map(option => (
                                                    <Option key={option.value} value={option.value}>
                                                        {option.label}
                                                    </Option>
                                                ))}
                                            </Select>
                                        </Form.Item>

                                        <Form.Item
                                            name="authPassword"
                                            label="Authentication Password"
                                            rules={[{required: true, message: "Please input authentication password!"}]}
                                        >
                                            <Input.Password style={{width: "280px"}} />
                                        </Form.Item>
                                    </>
                                )}

                                {securityLevel === "authPriv" && (
                                    <>
                                        <Form.Item
                                            name="privProtocol"
                                            label="Privacy Protocol"
                                            initialValue="DES"
                                            rules={[{required: true, message: "Please select privacy protocol!"}]}
                                        >
                                            <Select placeholder="Select privacy protocol" style={{width: "280px"}}>
                                                {privProtocolOptions.map(option => (
                                                    <Option key={option.value} value={option.value}>
                                                        {option.label}
                                                    </Option>
                                                ))}
                                            </Select>
                                        </Form.Item>

                                        <Form.Item
                                            name="privPassword"
                                            label="Privacy Password"
                                            rules={[{required: true, message: "Please input privacy password!"}]}
                                        >
                                            <Input.Password style={{width: "280px"}} />
                                        </Form.Item>
                                    </>
                                )}
                            </>
                        )}
                    </>
                )}
            </>
        );
    };

    const onSubmit = async () => {
        try {
            setSpinning(true);
            const values = await form.getFieldValue();

            const response = await updateSnmpViewData(values);

            if (response.status !== 200) {
                // tipModalAction(response.info, () => {});
                message.error(response.info);
                form.resetFields();
            } else {
                message.success(response.info);
                onSubmitSuccess();
                form.resetFields();
            }
        } catch (error) {
            // console.error("Form validation error:", error);
            message.error("Please check form inputs");
        } finally {
            onCancel();
            setSpinning(false);
        }
    };

    const onCancelFunc = () => {
        setSnmpVersion("v2c");
        setSecurityLevel("authPriv");
        form.resetFields();
        onCancel();
    };

    return (
        <div>
            <AmpConCustomModalForm
                title={isBatchImport ? "Batch Import" : "Single Import"}
                isModalOpen={isModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 8
                    }
                }}
                CustomFormItems={getFormItems}
                onCancel={onCancelFunc}
                onSubmit={onSubmit}
                modalClass="ampcon-middle-modal"
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={onCancelFunc}>
                        Cancel
                    </Button>,
                    <Button
                        key="apply"
                        type="primary"
                        onClick={() => (isBatchImport ? handleBatchImport() : form.submit())}
                    >
                        Apply
                    </Button>
                ]}
            />
            <Spin spinning={spinning} tip="Importing..." size="large" fullscreen />
        </div>
    );
};

export default ImportActions;
