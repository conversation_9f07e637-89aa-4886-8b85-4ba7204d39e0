import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {<PERSON>ert, Button, Divider, message, Modal, Select, Space, Tag} from "antd";
import {getSnmpImportDetails, getSnmpViewData, removeSnmpViewData} from "@/modules-ampcon/apis/dashboard_api";
import React, {useEffect, useRef, useState} from "react";
import {useNavigate} from "react-router-dom";
import SSHAction from "@/modules-ampcon/pages/Service/Switch/ssh_action";
import {exclamationSvg, InfoSvg, offlineSvg, onlineSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import ImportActions from "@/modules-ampcon/pages/Service/Switch/CampusSwitches/SnmpView/import_actions";
import dayjs from "dayjs";
import styles from "./snmp_view.module.scss";
import {useDispatch} from "react-redux";
import {updateAlarmSearch, updateAlarmSearchStatus} from "@/store/modules/common/alarm_slice";
import {SearchOutlined} from "@ant-design/icons";

const SnmpView = () => {
    // const currentUser = useSelector(state => state.user.userInfo);
    // const [isForbidden, setIsForbidden] = useState(false);
    const navigate = useNavigate();
    const fetchIntervalRef = useRef();
    const snmpRef = useRef(null);
    const [viewType] = useState("snmpView");

    useEffect(() => {
        fetchIntervalRef.current = setInterval(() => {
            if (snmpRef.current) {
                snmpRef.current.refreshTable();
            }
        }, 60000);
        return () => {
            if (fetchIntervalRef.current) {
                clearInterval(fetchIntervalRef.current);
            }
        };
    }, []);
    const snmpColumns = [
        {...createColumnConfig("Sysname", "sysname", TableFilterDropdown, "", "16.6%")},
        {
            ...createColumnConfig("SN/Service Tag", "sn", TableFilterDropdown, "", "16.7%"),
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                navigate(`${record.sn}`);
                            }}
                        >
                            {record.sn}
                        </a>
                    </Space>
                );
            }
        },
        createColumnConfig("Model", "model", TableFilterDropdown, "", "16.7%"),
        createColumnConfig("Version", "version", TableFilterDropdown, "", "16.7%"),
        {
            ...createColumnConfig("Mgmt IP", "mgt_ip", TableFilterDropdown, "", "16.7%"),
            render: (_, record) => {
                if (!record.mgt_ip) {
                    return null;
                }

                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <SSHAction viewType={viewType} record={record} tableRef={snmpRef} type="fsos" />
                            <a
                                onClick={() => {
                                    clickRemoveSnmpViewDataCallback(record);
                                }}
                            >
                                Remove
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ].filter(Boolean);

    const snmpSearchFieldsList = ["sn", "sysname", "mgt_ip", "model"];
    const snmpMatchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "sysname", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "model", matchMode: "fuzzy"}
    ];

    const clickRemoveSnmpViewDataCallback = record => {
        confirmModalAction("Are you sure want to remove the SNMP device ?", () => {
            removeSnmpViewData({snmpId: record.id}).then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                    snmpRef.current.refreshTable();
                }
            });
        });
    };

    return (
        <div>
            <AmpConCustomTable
                columns={snmpColumns}
                searchFieldsList={snmpSearchFieldsList}
                matchFieldsList={snmpMatchFieldsList}
                extraButton={<ExtraButton tableRef={snmpRef} />}
                fetchAPIInfo={getSnmpViewData}
                ref={snmpRef}
            />
        </div>
    );
};

const ExtraButton = ({tableRef}) => {
    return (
        <Space size="middle">
            <ImportActions tableRef={tableRef} />
            <ViewImportDetailButton />
        </Space>
    );
};

const ViewImportDetailButtonTableSelectDropdown = ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
    defaultValue,
    searchCount
}) => {
    const dispatch = useDispatch();

    useEffect(() => {
        if (defaultValue) {
            setSelectedKeys([defaultValue]);
            handleSearch([defaultValue], confirm);
        } else {
            clearFilters();
            handleSearch(selectedKeys, confirm);
        }
    }, [searchCount + defaultValue]);

    const handleSearch = (keys, confirmFunc) => {
        confirmFunc();
        dispatch(updateAlarmSearch(keys[0]));
    };

    return (
        <div style={{padding: 8}}>
            <Select
                placeholder="Select Type"
                value={selectedKeys}
                onChange={value => {
                    setSelectedKeys(value ? [value] : []);
                }}
                allowClear
                style={{width: 188, marginBottom: 8, display: "block"}}
            >
                <Select.Option value="pending">
                    <Tag className={styles.pendingStyle}>Pending</Tag>
                </Select.Option>
                <Select.Option value="running">
                    <Tag className={styles.runningStyle}>Running</Tag>
                </Select.Option>
                <Select.Option value="success">
                    <Tag className={styles.successStyle}>Successful</Tag>
                </Select.Option>
                <Select.Option value="failed">
                    <Tag className={styles.failedStyle}>Failed</Tag>
                </Select.Option>
            </Select>
            <Button
                type="primary"
                onClick={() => {
                    dispatch(updateAlarmSearchStatus(false));
                    handleSearch(selectedKeys, confirm);
                }}
                icon={<SearchOutlined />}
                size="small"
                style={{width: 90, marginRight: 8}}
            >
                Search
            </Button>
            <Button
                onClick={() => {
                    clearFilters();
                    dispatch(updateAlarmSearch(""));
                    handleSearch("", confirm);
                }}
                size="small"
                style={{width: 90}}
            >
                Reset
            </Button>
        </div>
    );
};

const ViewImportDetailButton = () => {
    const tableRef = useRef(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    const columns = [
        {
            ...createColumnConfig("Date", "create_time", null),
            render: (_, record) => {
                return <div>{dayjs(record.create_time).utc().format("YYYY-MM-DD HH:mm:ss")}</div>;
            }
        },
        createColumnConfig("Mgmt IP", "mgt_ip", TableFilterDropdown),
        {
            ...createColumnConfig("Import Status", "status", ViewImportDetailButtonTableSelectDropdown),
            render: (_, record) => {
                const statusText = {
                    pending: "Pending",
                    running: "Running",
                    success: "Successful",
                    failed: "Failed"
                };
                return (
                    <span className={`${styles.deviceStatus} ${styles[record.status]}`}>
                        {statusText[record.status]}
                    </span>
                );
            }
        }
    ];
    return (
        <>
            <Modal
                className="ampcon-max-modal"
                onCancel={() => {
                    setIsModalOpen(false);
                }}
                open={isModalOpen}
                title={
                    <>
                        <div>View Import Details</div>
                        <Divider style={{marginBottom: 0}} />
                    </>
                }
                footer={null}
            >
                <Alert
                    message={
                        <Space style={{paddingLeft: "8px"}} direction="vertical" size={1}>
                            <div style={{fontWeight: 700}}>Import Status Notes</div>
                            <div>
                                <span style={{fontWeight: 700}}>Successful: </span>The FSOS switch has been imported.
                            </div>
                            <div>
                                <span style={{fontWeight: 700}}>Failed: </span>The FSOS switch fails to be imported.
                                Possible causes:
                            </div>
                            <ul
                                style={{
                                    margin: 0,
                                    paddingLeft: "16px",
                                    listStyleType: "disc",
                                    fontWeight: 400
                                }}
                            >
                                <li>The switch to be imported is not an FSOS switch.</li>
                                <li>
                                    The switch to be imported is an FSOS switch but is not supported by the SNMP feature
                                    of AmpCon-Campus. See{" "}
                                    <a
                                        href="https://pica8-fs.atlassian.net/wiki/spaces/ampconcampus23/pages/508067989/Supported+FSOS+Switches"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        style={{textDecoration: "underline", color: "#14C9BB"}}
                                    >
                                        Supported FSOS Switches
                                    </a>
                                    .
                                </li>
                                <li>
                                    The network times out due to reasons like unreachable IP, disabled SNMP, firewall
                                    blockage, or ACL restrictions.
                                </li>
                                <li>The authentication fails because the username or password is incorrect.</li>
                                <li>
                                    The OIDs (Sysname, Serial Number, Model, and Version) are blocked on the switch
                                    side. These OIDs must be accessible for the SNMP View feature.
                                </li>
                            </ul>
                        </Space>
                    }
                    type="info"
                    showIcon
                    icon={
                        <div style={{marginRight: 0, marginTop: "2px"}}>
                            <InfoSvg />
                        </div>
                    }
                    closable
                    style={{
                        marginBottom: 16,
                        backgroundColor: "#F3F8FF",
                        border: "1px solid #F3F8FF",
                        color: "#367EFF",
                        width: 1304,
                        height: 200,
                        borderRadius: 2,
                        alignItems: "start"
                    }}
                />
                <AmpConCustomTable
                    ref={tableRef}
                    columns={columns}
                    matchFieldsList={[{name: "mgt_ip", matchMode: "fuzzy"}]}
                    searchFieldsList={["mgt_ip"]}
                    fetchAPIInfo={getSnmpImportDetails}
                />
            </Modal>

            <Button
                onClick={() => {
                    setIsModalOpen(true);
                    tableRef.current?.refreshTable();
                }}
            >
                View Import Details
            </Button>
        </>
    );
};

export default SnmpView;
