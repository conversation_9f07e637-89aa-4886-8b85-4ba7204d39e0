import {Flex, Tag} from "antd";
import {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import {
    portGECommonSvg,
    portTECommonSvg,
    portXECommonSvg
} from "@/modules-ampcon/pages/Telemetry/Switch/switch_port_panel/port_svg";
import Icon from "@ant-design/icons";
import {usePopper} from "react-popper";

const NodeInfoPanel = ({portName, status, referenceElement}) => {
    const [popperElement, setPopperElement] = useState(null);
    const {styles, attributes} = usePopper(referenceElement, popperElement, {
        placement: "right-start",
        modifiers: [
            {
                name: "offset",
                options: {
                    offset: [10, 5]
                }
            }
        ]
    });
    const headerStyle = {
        fontSize: "14px",
        color: "#929A9E",
        lineHeight: "17px",
        textAlign: "left",
        fontStyle: "normal",
        fontWeight: 400
    };

    const contentStyle = {
        fontSize: "14px",
        color: "#212519",
        lineHeight: "17px",
        textAlign: "left",
        fontStyle: "normal",
        fontWeight: 400
    };

    const alert_level_map = {
        warning: "Warning",
        error: "Alarm",
        info: "Info",
        common: "Disable"
    };

    const tag_classname_map = {
        warning: "warnTag",
        error: "failedTag",
        info: "successTag",
        common: "disableTag"
    };

    return (
        <div
            ref={setPopperElement}
            style={{
                ...styles.popper,
                background: "#ffffff",
                border: "1px solid #ccc",
                padding: "16px",
                borderRadius: "4px",
                boxShadow: "0 1px 12px 1px #E6E8EA",
                zIndex: 1000
            }}
            {...attributes.popper}
        >
            <Flex horizontal gap="16px">
                <Flex vertical gap="19px" style={{}}>
                    <div style={headerStyle}>Port</div>
                    <div style={headerStyle}>Level</div>
                </Flex>
                <Flex vertical gap="16px" style={{}}>
                    <div style={contentStyle}>{portName}</div>
                    <Tag className={tag_classname_map[status]}>{alert_level_map[status]}</Tag>
                </Flex>
            </Flex>
        </div>
    );
};

const PortNode = forwardRef(({index, portName, portType, status, isSvgReverse}, ref) => {
    const iconRef = useRef(null);
    const [displayIndex, setDisplayIndex] = useState(index);
    const [isHovered, setIsHovered] = useState(false);

    useImperativeHandle(ref, () => ({}));

    const getPortSvg = () => {
        if (portType.toUpperCase() === "GE") {
            return portGECommonSvg;
        }
        if (portType.toUpperCase() === "TE") {
            return portTECommonSvg;
        }
        if (portType.toUpperCase() === "XE") {
            return portXECommonSvg;
        }
    };

    const getClassname = () => {
        if (status === "warning") {
            return "warningnode";
        }
        if (status === "error") {
            return "alarmnode";
        }
        if (status === "info") {
            return "infonode";
        }
        return "commonnode";
    };

    return (
        <div
            onMouseEnter={() => {
                if (status !== "common") {
                    setIsHovered(true);
                }
            }}
            onMouseLeave={() => setIsHovered(false)}
        >
            {isSvgReverse ? (
                <Flex vertical>
                    <Flex style={{justifyContent: "center"}}>
                        <div className={getClassname()}>
                            <Icon
                                ref={iconRef}
                                component={getPortSvg()}
                                style={portType.toUpperCase() === "TE" ? {} : {transform: "rotate(180deg)"}}
                            />
                        </div>
                    </Flex>
                    <Flex style={{justifyContent: "center", fontSize: "10px"}}>{displayIndex}</Flex>
                </Flex>
            ) : (
                <Flex vertical>
                    <Flex style={{justifyContent: "center", fontSize: "10px"}}>{displayIndex}</Flex>
                    <Flex style={{justifyContent: "center"}}>
                        <div className={getClassname()}>
                            <Icon
                                ref={iconRef}
                                component={getPortSvg()}
                                style={portType.toUpperCase() === "TE" ? {transform: "rotate(180deg)"} : {}}
                            />
                        </div>
                    </Flex>
                </Flex>
            )}
            {isHovered && <NodeInfoPanel portName={portName} status={status} referenceElement={iconRef.current} />}
        </div>
    );
});

export default PortNode;
