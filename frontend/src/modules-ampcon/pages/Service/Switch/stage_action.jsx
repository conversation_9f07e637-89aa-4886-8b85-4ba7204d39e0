import {message} from "antd";
import {updateSwitchStageStatus} from "@/modules-ampcon/apis/dashboard_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const StageAction = ({record, tableRef}) => {
    return (
        <a
            onClick={
                (!record.enable || record.status === "Staged") && record.status !== "Imported"
                    ? () =>
                          confirmModalAction(
                              `Are you sure want to update switch to ${record.enable ? "UnStage" : "Stage"}?`,
                              () => {
                                  updateSwitchStageStatus(record.sn, record.enable ? "unStage" : "stage").then(res => {
                                      if (res.status === 200) {
                                          message.success(res.info);
                                          tableRef.current.refreshTable();
                                      } else {
                                          message.error(res.info);
                                      }
                                  });
                              }
                          )
                    : null
            }
            style={{
                color:
                    (!record.enable || record.status === "Staged") && record.status !== "Imported"
                        ? "#14C9BB"
                        : "#999999",
                cursor:
                    (!record.enable || record.status === "Staged") && record.status !== "Imported"
                        ? "pointer"
                        : "not-allowed"
            }}
        >
            {record.enable ? "UnStage" : "Stage"}
        </a>
    );
};
export default StageAction;
