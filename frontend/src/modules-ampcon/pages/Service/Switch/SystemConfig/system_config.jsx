import {
    Flex,
    Form,
    InputNumber,
    Input,
    Tooltip,
    Space,
    Select,
    Switch,
    Divider,
    Button,
    message,
    Modal,
    Spin
} from "antd";
import {
    EyeInvisibleOutlined,
    ApiFilled,
    EyeOutlined,
    LeftOutlined,
    PlusOutlined,
    QuestionCircleOutlined
} from "@ant-design/icons";

import styles from "@/modules-ampcon/pages/Service/Switch/SystemConfig/system_config.module.scss";
import {useEffect, useRef, useState} from "react";
import {
    addGroupSystemConfig,
    getFileContent,
    getLicenseConnectState,
    getSystemConfigInfo,
    removeSystemConfigByName,
    saveGlobalSystemConfig,
    saveGroupSystemConfig,
    updateEncryptKey
} from "@/modules-ampcon/apis/config_api";
import {setDebugState} from "@/modules-ampcon/apis/dashboard_api";
import ConfigContentTextarea from "@/modules-ampcon/components/config_content_textarea";
import CustomPasswordInput from "@/modules-ampcon/components/custom_input_password";
import SyslogConfigModal from "@/modules-ampcon/pages/Service/Switch/SystemConfig/syslog_config_modal";
import ViewAssociatedSwitchModal from "@/modules-ampcon/pages/Service/Switch/SystemConfig/view_associated_switch_modal";
import ManageSwitchModal from "@/modules-ampcon/pages/Service/Switch/SystemConfig/manage_switch_modal";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const {Option} = Select;

const AmpConSystemConfig = () => {
    const systemConfigPageTitle = "System Configuration";
    const systemConfigPageTooltip =
        "This is the dialog to specify Deployment Config (System Config) application parameters, and security data.";
    const configurationNameLabel = "Configuration Name";
    const deviceDefaultLoginUserLabel = "Device Default Login User";
    const deviceDefaultLoginUserTooltip =
        "credentials to login to the switch. These credentials are used with AAA (e.g. TACACS) login";
    const deviceDefaultPasswordLabel = "Device Default Password";
    const licensePortalURLLabel = "License Portal URL";
    const licensePortalUserLabel = "License Portal User";
    const licensePortalPasswordLabel = "License Portal Password";
    const configBackupNumberLabel = "Config Backup Number";
    const configBackupNumberTooltip = "The maximum number of Config backups to be retained for each switch";
    const dbBackupNumberLabel = "DB Backup Number";
    const dbBackupNumberTooltip = "The maximum number of backups to be retained for ampcon";
    const securityConfigFileLabel = "Security Config File";
    const parkingSecurityConfigFileLabel = "Parking Security Config File";
    const allowSwitchSourceIPLabel = "Allow Switch Source IP";
    const allowSwitchSourceIPTooltip =
        "Optionally allows specifying subnets from which switches can access AmpCon, as a security measure";
    const enableDebuggingLabel = "DEBUG";
    const enableDebuggingTooltip = "Enables debug log for backend operations";
    const filePreviewTitle = "File Check";
    const modifyEncryptKeyModalTitle = "Modify Encrypt Key";
    const modifyEncryptKeyModalOriginKeyLabel = "Original Key";
    const modifyEncryptKeyModalNewKeyLabel = "New Key";
    const modifyEncryptKeyModalConfirmKeyLabel = "Confirm Key";

    const timerRef = useRef(null);

    // spin
    const [isShowSpin, setIsShowSpin] = useState(false);

    // dataLoaded
    const [isDataLoaded, setIsDataLoaded] = useState(false);

    // add group system Config state
    const [isAddGroupSystemConfig, setIsAddGroupSystemConfig] = useState(false);

    // is able to add group system configuration
    const [isAbleToAddGroupSystemConfig, setIsAbleToAddGroupSystemConfig] = useState(false);

    // form
    const [configurationNameForm] = Form.useForm();
    const [systemConfigForm] = Form.useForm();
    const [modifyEncryptKeyForm] = Form.useForm();

    const [selectedSystemConfig, setSelectedSystemConfig] = useState("Global");
    const [systemConfigList, setSystemConfigList] = useState([]);
    const [deviceDefaultLoginUser, setDeviceDefaultLoginUser] = useState("");
    const [deviceDefaultPassword, setDeviceDefaultPassword] = useState("");
    const [licensePortalURL, setLicensePortalURL] = useState("");
    const [licensePortalUser, setLicensePortalUser] = useState("");
    const [licensePortalPassword, setLicensePortalPassword] = useState("");
    const [configBackupNumber, setConfigBackupNumber] = useState("");
    const [dbBackupNumber, setDBBackupNumber] = useState("");
    const [dbBackupNumberRange, setDBBackupNumberRange] = useState([]);
    const [securityConfigFileDom, setSecurityConfigFileDom] = useState({
        inputType: "file",
        value: "",
        readOnly: false
    });
    const [securityConfigFile, setSecurityConfigFile] = useState(null);
    const [parkingSecurityConfigFileDom, setParkingSecurityConfigFileDom] = useState({
        inputType: "file",
        value: "",
        readOnly: false
    });
    const [parkingSecurityConfigFile, setParkingSecurityConfigFile] = useState(null);
    const [allowSwitchSourceIP, setAllowSwitchSourceIP] = useState("");
    const [isEnableDebug, setIsEnableDebug] = useState(false);

    // file check
    const [isShowFilePreview, setIsShowFilePreview] = useState(false);
    const [fileContent, setFileContent] = useState("");

    // save action
    const [saveAction, setSaveAction] = useState("update");

    // Modal Modify Encrypt Key
    const [isShowModifyEncryptKeyModal, setIsShowModifyEncryptKeyModal] = useState(false);
    const [isNewKeyAndConfirmKeyEqual, setIsNewKeyAndConfirmKeyEqual] = useState(true);
    const [isModifyEncryptKeySaveButtonBeenPressed, setIsModifyEncryptKeySaveButtonBeenPressed] = useState(false);
    const [isOriginKeyAndNewKeyEqual, setIsOriginKeyAndNewKeyEqual] = useState(false);

    // Modal Syslog Config
    const syslogConfigModalRef = useRef(null);

    // View Associated Switch
    const viewAssociatedSwitchModalRef = useRef(null);

    // Manage Switch Modal
    const manageSwitchModalRef = useRef(null);

    useEffect(() => {
        fetchData();
    }, []);

    const setFormInitialData = response => {
        setSystemConfigList(response.data.allSystemConfigName);
        setDeviceDefaultLoginUser(response.data.systemConfig.switch_op_user);
        setDeviceDefaultPassword(response.data.systemConfig.switch_op_password);
        setLicensePortalURL(response.data.systemConfig.license_portal_url);
        setLicensePortalUser(response.data.systemConfig.license_portal_user);
        setLicensePortalPassword(response.data.systemConfig.license_portal_password);
        setConfigBackupNumber(response.data.systemConfig.retrieve_config_num);
        setDBBackupNumber(response.data.dbBackupConfigInfo[0]);
        setDBBackupNumberRange(response.data.dbBackupConfigInfo[1]);
        setSecurityConfigFileDom({
            inputType: "input",
            value: response.data.systemConfig.security_config,
            readOnly: true
        });
        if (response.data.systemConfig.parking_security_config !== "") {
            setParkingSecurityConfigFileDom({
                inputType: "input",
                value: response.data.systemConfig.parking_security_config,
                readOnly: true
            });
        }
        setAllowSwitchSourceIP(response.data.systemConfig.allowed_source_ip_policy);
        setIsEnableDebug(response.data.enableDebug);
        setSaveAction("update");
    };

    const fetchData = async selectedSystemConfigName => {
        const response = await getSystemConfigInfo(selectedSystemConfigName || selectedSystemConfig);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            if (response.data.systemConfig !== null) {
                setFormInitialData(response);
                setIsAbleToAddGroupSystemConfig(true);
            } else {
                setSystemConfigList(["Global"]);
                setSaveAction("update");
            }
            setIsDataLoaded(true);
        }
    };

    const refreshData = async selectedSystemConfigName => {
        const selectValue = selectedSystemConfigName || selectedSystemConfig;
        const response = await getSystemConfigInfo(selectValue);
        setSystemConfigList(response.data.allSystemConfigName);
        setSelectedSystemConfig(selectValue);
        setFormInitialData(response);
        if (response.status !== 200) {
            message.error(response.info);
        } else if (selectValue === "Global") {
            configurationNameForm.setFieldsValue({
                configurationName: "Global"
            });
            setSecurityConfigFileDom({
                inputType: "input",
                value: response.data.systemConfig.security_config,
                readOnly: true
            });
            if (response.data.systemConfig.parking_security_config !== "") {
                setParkingSecurityConfigFileDom({
                    inputType: "input",
                    value: response.data.systemConfig.parking_security_config,
                    readOnly: true
                });
            }
            systemConfigForm.resetFields();
            systemConfigForm.setFieldsValue({
                deviceDefaultLoginUser: response.data.systemConfig.switch_op_user,
                deviceDefaultPassword: response.data.systemConfig.switch_op_password,
                licensePortalURL: response.data.systemConfig.license_portal_url,
                licensePortalUser: response.data.systemConfig.license_portal_user,
                licensePortalPassword: response.data.systemConfig.license_portal_password,
                configBackupNumber: response.data.systemConfig.retrieve_config_num,
                dbBackupNumber: response.data.dbBackupConfigInfo[0],
                securityConfigFile: response.data.systemConfig.security_config,
                parkingSecurityConfigFile: response.data.systemConfig.parking_security_config,
                allowSwitchSourceIP: response.data.systemConfig.allowed_source_ip_policy
            });
        } else {
            configurationNameForm.setFieldsValue({
                configurationName: selectValue
            });
            setSecurityConfigFileDom({
                inputType: "input",
                value: response.data.systemConfig.security_config,
                readOnly: true
            });
            systemConfigForm.resetFields();
            systemConfigForm.setFieldsValue({
                deviceDefaultLoginUser: response.data.systemConfig.switch_op_user,
                deviceDefaultPassword: response.data.systemConfig.switch_op_password,
                licensePortalURL: response.data.systemConfig.license_portal_url,
                licensePortalUser: response.data.systemConfig.license_portal_user,
                licensePortalPassword: response.data.systemConfig.license_portal_password,
                securityConfigFile: response.data.systemConfig.security_config
            });
        }
        setSelectedSystemConfig(selectValue);
        setIsShowFilePreview(false);
    };

    const handleTestLicenseStateButtonCallback = async () => {
        const formTemp = systemConfigForm;
        try {
            await formTemp.validateFields(["licensePortalURL"]);
        } catch (errorInfo) {
            return;
        }
        setIsShowSpin(true);
        const licensePortalURL = isAddGroupSystemConfig
            ? formTemp.getFieldValue().licensePortalURLAdd
            : formTemp.getFieldValue().licensePortalURL;
        const licensePortalUser = isAddGroupSystemConfig
            ? formTemp.getFieldValue().licensePortalUserAdd
            : formTemp.getFieldValue().licensePortalUser;
        const licensePortalPassword = isAddGroupSystemConfig
            ? formTemp.getFieldValue().licensePortalPasswordAdd
            : formTemp.getFieldValue().licensePortalPassword;
        await getLicenseConnectState(
            licensePortalURL,
            licensePortalUser,
            licensePortalPassword,
            selectedSystemConfig
        ).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
            }
        });
        setIsShowSpin(false);
    };

    const handleSecurityGlobalFileChange = e => {
        if (e.target.files.length === 0) {
            setSecurityConfigFile(null);
            return;
        }
        setSecurityConfigFile(e.target.files[0]);
    };

    const securityConfigPlusButtonCallback = () => {
        systemConfigForm.setFieldValue("securityConfigFile", "");
        setSecurityConfigFile(null);
        setSecurityConfigFileDom({
            inputType: "file",
            value: "",
            readOnly: false
        });
    };

    const securityConfigPreviewButtonCallback = () => {
        getFileContent(`${securityConfigFileDom.value}.pica8`).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                setFileContent(response.data);
                setIsShowFilePreview(true);
            }
        });
    };

    const handleParkingSecurityGlobalFileChange = e => {
        if (e.target.files.length === 0) {
            parkingSecurityConfigFile(null);
            return;
        }
        setParkingSecurityConfigFile(e.target.files[0]);
    };

    const handleDebugSwitchChange = value => {
        setDebugState(value ? "1" : "0").then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
            }
        });
    };

    const parkingSecurityConfigPlusButtonCallback = () => {
        systemConfigForm.setFieldValue("parkingSecurityConfigFile", "");
        setParkingSecurityConfigFile(null);
        setParkingSecurityConfigFileDom({
            inputType: "file",
            value: "",
            readOnly: false
        });
    };

    const parkingSecurityConfigPreviewButtonCallback = () => {
        getFileContent(`${parkingSecurityConfigFileDom.value}.pica8`).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                setFileContent(response.data);
                setIsShowFilePreview(true);
            }
        });
    };

    const saveSystemConfigCallback = async () => {
        try {
            // If no errors, proceed with saving the form
            const formTemp = systemConfigForm;
            await formTemp.validateFields();
            if (selectedSystemConfig === "Global") {
                setIsShowSpin(true);
                await saveGlobalSystemConfig(
                    saveAction,
                    isAddGroupSystemConfig ? formTemp.getFieldValue().configurationName : selectedSystemConfig,
                    formTemp.getFieldValue().deviceDefaultLoginUser,
                    formTemp.getFieldValue().deviceDefaultPassword,
                    formTemp.getFieldValue().licensePortalURL,
                    formTemp.getFieldValue().licensePortalUser,
                    formTemp.getFieldValue().licensePortalPassword,
                    formTemp.getFieldValue().configBackupNumber || 0,
                    formTemp.getFieldValue().dbBackupNumber || 0,
                    securityConfigFile || securityConfigFileDom.value,
                    parkingSecurityConfigFile || parkingSecurityConfigFileDom.value || "",
                    formTemp.getFieldValue().allowSwitchSourceIP || ""
                ).then(response => {
                    if (response.status !== 200) {
                        message.error(response.info);
                    } else {
                        refreshData(selectedSystemConfig).then(() => {
                            message.success(response.info);
                            setIsAbleToAddGroupSystemConfig(true);
                        });
                    }
                });
                setIsShowSpin(false);
                setSecurityConfigFile(null);
                setParkingSecurityConfigFile(null);
            } else {
                await saveGroupSystemConfig(
                    saveAction,
                    isAddGroupSystemConfig ? formTemp.getFieldValue().configurationName : selectedSystemConfig,
                    formTemp.getFieldValue().deviceDefaultLoginUser,
                    formTemp.getFieldValue().deviceDefaultPassword,
                    formTemp.getFieldValue().licensePortalURL,
                    formTemp.getFieldValue().licensePortalUser,
                    formTemp.getFieldValue().licensePortalPassword,
                    securityConfigFile || securityConfigFileDom.value
                ).then(response => {
                    if (response.status !== 200) {
                        message.error(response.info);
                    } else {
                        refreshData(selectedSystemConfig).then(() => {
                            message.success(response.info);
                        });
                    }
                });
                setSecurityConfigFile(null);
                setParkingSecurityConfigFile(null);
            }
        } catch (errorInfo) {
            setIsShowSpin(false);
            message.error("There are some errors in the form, please check and submit again!");
        }
    };

    const addSystemConfigCallback = async () => {
        try {
            // If no errors, proceed with saving the form
            const formTemp = systemConfigForm;
            await formTemp.validateFields();
            setIsShowSpin(true);
            setSelectedSystemConfig(formTemp.getFieldValue().configurationNameAdd);
            const selectedSystemConfigName = formTemp.getFieldValue().configurationNameAdd;
            await addGroupSystemConfig(
                selectedSystemConfigName,
                formTemp.getFieldValue().deviceDefaultLoginUserAdd,
                formTemp.getFieldValue().deviceDefaultPasswordAdd,
                formTemp.getFieldValue().licensePortalURLAdd,
                formTemp.getFieldValue().licensePortalUserAdd,
                formTemp.getFieldValue().licensePortalPasswordAdd,
                securityConfigFile
            ).then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    setSelectedSystemConfig(selectedSystemConfigName);
                    setIsAddGroupSystemConfig(false);
                    refreshData(selectedSystemConfigName).then(() => {
                        message.success(response.info);
                    });
                    setSecurityConfigFile(null);
                    setParkingSecurityConfigFile(null);
                }
            });
            setIsShowSpin(false);
        } catch (errorInfo) {
            setIsShowSpin(false);
            message.error("There are some errors in the form, please check and submit again!");
        }
    };

    const handleAddNewGroupSystemConfigButtonCallback = () => {
        if (!isAbleToAddGroupSystemConfig) {
            message.error("Please save the Global Configuration first.");
            return;
        }
        setIsAddGroupSystemConfig(true);
        setSaveAction("add");
        setIsShowFilePreview(false);
    };

    const modifyEncryptKeySaveButtonCallback = async () => {
        await modifyEncryptKeyForm.validateFields();
        const formValue = modifyEncryptKeyForm.getFieldsValue();
        setIsModifyEncryptKeySaveButtonBeenPressed(true);
        if (formValue.newKey !== formValue.confirmKey) {
            setIsNewKeyAndConfirmKeyEqual(false);
            return;
        }
        if (formValue.originalKey === formValue.newKey) {
            setIsOriginKeyAndNewKeyEqual(true);
            return;
        }
        setIsNewKeyAndConfirmKeyEqual(true);
        setIsShowModifyEncryptKeyModal(false);
        setIsShowSpin(true);
        await updateEncryptKey(formValue.originalKey, formValue.newKey).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
            }
        });
        setIsShowSpin(false);
    };

    const handleEncryptKeyChangeCallback = () => {
        const formValue = modifyEncryptKeyForm.getFieldsValue();
        if (isModifyEncryptKeySaveButtonBeenPressed) {
            if (formValue.newKey !== formValue.confirmKey) {
                setIsNewKeyAndConfirmKeyEqual(false);
            } else {
                setIsNewKeyAndConfirmKeyEqual(true);
            }
        }
    };

    const getEncryptKeyFormValidateStatus = () => {
        if (isNewKeyAndConfirmKeyEqual) {
            if (isOriginKeyAndNewKeyEqual) {
                return "error";
            }
            return "success";
        }
        return "error";
    };

    const getEncryptKeyFormValidateHelp = () => {
        if (isNewKeyAndConfirmKeyEqual) {
            if (isOriginKeyAndNewKeyEqual) {
                return "Confirm that the new key must be inconsistent with the original key.";
            }
            return "";
        }
        return "Confirm that the keys must be consistent.";
    };
    const validateIP = (_, value, callback) => {
        if (!value) {
            callback();
            return;
        }
        const ipList = value.split(",").map(ip => ip.trim());
        const ipv4Pattern =
            /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        const ipv6Pattern = /^(?:[\dA-Fa-f]{1,4}:){7}[\dA-Fa-f]{1,4}$/;

        for (const ip of ipList) {
            const ipPart = ip.split("/")[0];
            try {
                const ipMask = ip.split("/")[1];
                if (isNaN(ipMask) || ipMask <= 0 || ipMask >= 32 || ipMask === "") {
                    callback("Please enter a valid IP subnet, multiple addresses separated by commas");
                    return;
                }
            } catch (error) {
                callback("Please enter a valid IP subnet, multiple addresses separated by commas");
                return;
            }

            if (!ipv4Pattern.test(ipPart)) {
                callback("Please enter a valid IP address, multiple addresses separated by commas");
                return;
            }
        }

        callback();
    };

    const isUrlValidate = value => {
        try {
            // eslint-disable-next-line no-new
            new URL(value);
            return true;
        } catch {
            return false;
        }
    };

    const debounceURLValidator = (_, value) => {
        return new Promise((resolve, reject) => {
            clearTimeout(timerRef.current);
            timerRef.current = setTimeout(() => {
                if (!value) {
                    reject(new Error("Please enter a valid License Portal URL"));
                    return;
                }
                if (!isUrlValidate(value)) {
                    reject(new Error("Please enter a valid License Portal URL"));
                    return;
                }
                resolve();
            }, 100);
        });
    };

    return (
        <Flex
            layout="horizontal"
            className={styles.tile}
            style={{minWidth: "fit-content", flex: 1, margin: "-12px 0px 20px 0px"}}
        >
            <div>
                <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
                <Modal
                    className="ampcon-middle-modal"
                    title={
                        <div>
                            {modifyEncryptKeyModalTitle}
                            <Divider style={{marginTop: 8, marginBottom: 0}} />
                        </div>
                    }
                    open={isShowModifyEncryptKeyModal}
                    onCancel={() => {
                        setIsShowModifyEncryptKeyModal(false);
                        setIsModifyEncryptKeySaveButtonBeenPressed(false);
                        setIsNewKeyAndConfirmKeyEqual(true);
                        setIsOriginKeyAndNewKeyEqual(false);
                        modifyEncryptKeyForm.resetFields();
                    }}
                    footer={
                        <Flex vertical>
                            <Divider style={{marginTop: 0, marginBottom: 20}} />
                            <Flex justify="flex-end">
                                <Button
                                    onClick={() => {
                                        setIsShowModifyEncryptKeyModal(false);
                                        setIsModifyEncryptKeySaveButtonBeenPressed(false);
                                        setIsNewKeyAndConfirmKeyEqual(true);
                                        setIsOriginKeyAndNewKeyEqual(false);
                                        modifyEncryptKeyForm.resetFields();
                                    }}
                                    className={styles.buttonStyle2}
                                >
                                    Cancle
                                </Button>
                                <Button
                                    type="primary"
                                    onClick={modifyEncryptKeySaveButtonCallback}
                                    className={styles.buttonStyle2}
                                >
                                    Apply
                                </Button>
                            </Flex>
                        </Flex>
                    }
                >
                    <Form
                        layout="horizontal"
                        labelAlign="left"
                        labelCol={{style: {width: 128}}}
                        wrapperCol={{span: 14}}
                        labelWrap
                        className="label-wrap"
                        form={modifyEncryptKeyForm}
                        style={{minHeight: "268px"}}
                    >
                        <Form.Item
                            name="originalKey"
                            label={modifyEncryptKeyModalOriginKeyLabel}
                            rules={[
                                {
                                    required: true
                                }
                            ]}
                            initialValue=""
                        >
                            <Input.Password
                                iconRender={visible => {
                                    return visible ? (
                                        <EyeOutlined style={{color: "#c5c5c5"}} />
                                    ) : (
                                        <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                    );
                                }}
                                style={{width: 280}}
                            />
                        </Form.Item>
                        <Form.Item
                            name="newKey"
                            label={modifyEncryptKeyModalNewKeyLabel}
                            rules={[
                                {
                                    required: true
                                }
                            ]}
                            initialValue=""
                            validateStatus={getEncryptKeyFormValidateStatus()}
                            help={getEncryptKeyFormValidateHelp()}
                        >
                            <Input.Password
                                iconRender={visible => {
                                    return visible ? (
                                        <EyeOutlined style={{color: "#c5c5c5"}} />
                                    ) : (
                                        <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                    );
                                }}
                                onChange={handleEncryptKeyChangeCallback}
                                style={{width: 280}}
                            />
                        </Form.Item>
                        <Form.Item
                            name="confirmKey"
                            label={modifyEncryptKeyModalConfirmKeyLabel}
                            rules={[
                                {
                                    required: true
                                }
                            ]}
                            initialValue=""
                            validateStatus={getEncryptKeyFormValidateStatus()}
                            help={getEncryptKeyFormValidateHelp()}
                        >
                            <Input.Password
                                iconRender={visible => {
                                    return visible ? (
                                        <EyeOutlined style={{color: "#c5c5c5"}} />
                                    ) : (
                                        <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                    );
                                }}
                                onChange={handleEncryptKeyChangeCallback}
                                style={{width: 280}}
                            />
                        </Form.Item>
                    </Form>
                </Modal>
                <SyslogConfigModal ref={syslogConfigModalRef} />
                <ViewAssociatedSwitchModal ref={viewAssociatedSwitchModalRef} />
                <ManageSwitchModal ref={manageSwitchModalRef} />
            </div>
            {isDataLoaded ? (
                <Flex layout="horizontal" style={{minHeight: "712px", minWidth: "600px", height: "100%", flex: 1}}>
                    <Flex
                        vertical
                        // justify="space-between"
                        style={{
                            minWidth: "600px",
                            maxWidth: "600px",
                            width: "calc(40% - 30px)"
                        }}
                        className={styles.tile1}
                    >
                        <Flex vertical>
                            <div className={styles.systemTitle}>
                                <Flex justify="space-between">
                                    <Tooltip title={systemConfigPageTooltip} placement="right">
                                        <div style={{display: "flex", alignItems: "center"}}>
                                            <Space>
                                                <h2 style={{margin: 0}}>{systemConfigPageTitle}</h2>
                                                <QuestionCircleOutlined className="questioncircle-color" />
                                            </Space>
                                        </div>
                                    </Tooltip>
                                    <div style={{display: "flex", alignItems: "center"}}>
                                        {isAddGroupSystemConfig ? (
                                            <a
                                                onClick={() => {
                                                    setSelectedSystemConfig("Global");
                                                    refreshData("Global").then(() => {
                                                        setIsAddGroupSystemConfig(false);
                                                        setSaveAction("update");
                                                    });
                                                }}
                                            >
                                                <Button
                                                    type="link"
                                                    icon={<LeftOutlined />}
                                                    style={{
                                                        border: "none",
                                                        fontSize: "14px",
                                                        color: "#14C9BB"
                                                    }}
                                                >
                                                    Back
                                                </Button>
                                            </a>
                                        ) : (
                                            <Button
                                                type="primary"
                                                icon={<PlusOutlined />}
                                                style={{
                                                    height: "32px",
                                                    width: "32px"
                                                }}
                                                onClick={handleAddNewGroupSystemConfigButtonCallback}
                                            />
                                        )}
                                    </div>
                                </Flex>
                            </div>
                            <Form
                                layout="horizontal"
                                labelAlign="left"
                                labelCol={{flex: "0 0 258px"}}
                                wrapperCol={{span: 14}}
                                labelWrap
                                className="label-wrap"
                                form={configurationNameForm}
                            >
                                {isAddGroupSystemConfig ? null : (
                                    <Form.Item
                                        name="configurationName"
                                        label={configurationNameLabel}
                                        rules={[
                                            {
                                                required: true,
                                                message: "Please input your system configuration name!"
                                            },
                                            {pattern: /^[\s\w:-]+$/, message: "System configuration name is invalid."}
                                        ]}
                                        initialValue={selectedSystemConfig}
                                    >
                                        <Select
                                            onChange={value => {
                                                refreshData(value);
                                            }}
                                            style={{width: "280px"}}
                                        >
                                            {systemConfigList.map(systemConfig => {
                                                return (
                                                    <Option key={systemConfig} value={systemConfig}>
                                                        {systemConfig}
                                                    </Option>
                                                );
                                            })}
                                        </Select>
                                    </Form.Item>
                                )}
                            </Form>
                            {(() => {
                                if (isAddGroupSystemConfig) {
                                    return (
                                        <Form
                                            layout="horizontal"
                                            labelAlign="left"
                                            labelCol={{flex: "0 0 258px"}}
                                            wrapperCol={{span: 14}}
                                            form={systemConfigForm}
                                            labelWrap
                                            className="label-wrap"
                                        >
                                            <Form.Item
                                                name="configurationNameAdd"
                                                label={configurationNameLabel}
                                                rules={[
                                                    {
                                                        validator: (_, value) => {
                                                            if (!value) {
                                                                return Promise.reject(
                                                                    new Error("Configuration name cannot be empty")
                                                                );
                                                            }
                                                            if (value && /\s/.test(value)) {
                                                                return Promise.reject(
                                                                    new Error(
                                                                        "Configuration name cannot contain spaces"
                                                                    )
                                                                );
                                                            }
                                                            if (!/^[\s\w:-]+$/.test(value)) {
                                                                return Promise.reject(
                                                                    new Error("System configuration name is invalid.")
                                                                );
                                                            }
                                                            return Promise.resolve();
                                                        }
                                                    }
                                                ]}
                                                initialValue=""
                                            >
                                                <Input style={{width: "280px"}} />
                                            </Form.Item>
                                            <Form.Item
                                                name="deviceDefaultLoginUserAdd"
                                                label={deviceDefaultLoginUserLabel}
                                                tooltip={deviceDefaultLoginUserTooltip}
                                                rules={[
                                                    {
                                                        required: true
                                                    }
                                                ]}
                                                initialValue=""
                                            >
                                                <Input style={{width: "280px"}} />
                                            </Form.Item>
                                            <Form.Item
                                                name="deviceDefaultPasswordAdd"
                                                label={deviceDefaultPasswordLabel}
                                                rules={[
                                                    {
                                                        required: true
                                                    }
                                                ]}
                                                initialValue=""
                                            >
                                                <CustomPasswordInput style={{width: "280px"}} />
                                            </Form.Item>
                                            <Form.Item
                                                name="licensePortalURLAdd"
                                                label={licensePortalURLLabel}
                                                rules={[{required: true, validator: debounceURLValidator}]}
                                                initialValue=""
                                            >
                                                <Input
                                                    suffix={
                                                        <ApiFilled
                                                            style={{color: "rgba(0,0,0,.45)"}}
                                                            onClick={handleTestLicenseStateButtonCallback}
                                                        />
                                                    }
                                                    style={{width: "280px"}}
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                name="licensePortalUserAdd"
                                                label={licensePortalUserLabel}
                                                rules={[
                                                    {
                                                        required: true
                                                    }
                                                ]}
                                                initialValue=""
                                            >
                                                <Input style={{width: "280px"}} />
                                            </Form.Item>
                                            <Form.Item
                                                name="licensePortalPasswordAdd"
                                                label={licensePortalPasswordLabel}
                                                rules={[
                                                    {
                                                        required: true
                                                    }
                                                ]}
                                                initialValue=""
                                            >
                                                <CustomPasswordInput style={{width: "280px"}} />
                                            </Form.Item>
                                            <Form.Item
                                                name="securityConfigFileAdd"
                                                label={securityConfigFileLabel}
                                                rules={[
                                                    {
                                                        required: true
                                                    }
                                                ]}
                                                onChange={handleSecurityGlobalFileChange}
                                            >
                                                <Input type="file" value="" style={{width: "280px"}} />
                                            </Form.Item>
                                        </Form>
                                    );
                                }
                                if (selectedSystemConfig === "Global") {
                                    return (
                                        <Form
                                            layout="horizontal"
                                            labelAlign="left"
                                            labelCol={{flex: "0 0 258px"}}
                                            wrapperCol={{span: 14}}
                                            form={systemConfigForm}
                                            labelWrap
                                            className="label-wrap"
                                        >
                                            <Form.Item
                                                name="deviceDefaultLoginUser"
                                                label={deviceDefaultLoginUserLabel}
                                                tooltip={deviceDefaultLoginUserTooltip}
                                                rules={[
                                                    {
                                                        required: true
                                                    }
                                                ]}
                                                initialValue={deviceDefaultLoginUser}
                                            >
                                                <Input
                                                    onChange={e => setDeviceDefaultLoginUser(e.target.value)}
                                                    style={{width: "280px"}}
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                name="deviceDefaultPassword"
                                                label={deviceDefaultPasswordLabel}
                                                rules={[
                                                    {
                                                        required: true
                                                    }
                                                ]}
                                                initialValue={deviceDefaultPassword}
                                            >
                                                <CustomPasswordInput style={{width: "280px"}} />
                                            </Form.Item>
                                            <Form.Item
                                                name="licensePortalURL"
                                                label={licensePortalURLLabel}
                                                rules={[{required: true, validator: debounceURLValidator}]}
                                                initialValue={licensePortalURL}
                                            >
                                                <Input
                                                    suffix={
                                                        <ApiFilled
                                                            style={{color: "rgba(0,0,0,.45)"}}
                                                            onClick={handleTestLicenseStateButtonCallback}
                                                        />
                                                    }
                                                    style={{width: "280px"}}
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                name="licensePortalUser"
                                                label={licensePortalUserLabel}
                                                rules={[
                                                    {
                                                        required: true
                                                    }
                                                ]}
                                                initialValue={licensePortalUser}
                                            >
                                                <Input style={{width: "280px"}} />
                                            </Form.Item>
                                            <Form.Item
                                                name="licensePortalPassword"
                                                label={licensePortalPasswordLabel}
                                                rules={[
                                                    {
                                                        required: true
                                                    }
                                                ]}
                                                initialValue={licensePortalPassword}
                                            >
                                                <CustomPasswordInput style={{width: "280px"}} />
                                            </Form.Item>
                                            <Form.Item
                                                name="configBackupNumber"
                                                label={configBackupNumberLabel}
                                                tooltip={configBackupNumberTooltip}
                                                rules={[
                                                    {
                                                        required: true
                                                    },
                                                    {
                                                        type: "number",
                                                        min: 1,
                                                        max: 100,
                                                        message: "Please enter a value less than or equal to 100."
                                                    }
                                                ]}
                                                initialValue={configBackupNumber}
                                            >
                                                <InputNumber style={{width: "280px"}} />
                                            </Form.Item>
                                            <Form.Item
                                                name="dbBackupNumber"
                                                label={dbBackupNumberLabel}
                                                tooltip={dbBackupNumberTooltip}
                                                rules={[
                                                    {
                                                        required: true
                                                    }
                                                ]}
                                                initialValue={dbBackupNumber}
                                            >
                                                <InputNumber
                                                    style={{width: "280px"}}
                                                    min={dbBackupNumberRange[0]}
                                                    max={dbBackupNumberRange[1]}
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                name="securityConfigFile"
                                                label={securityConfigFileLabel}
                                                rules={[
                                                    {
                                                        required: true
                                                    }
                                                ]}
                                                initialValue={securityConfigFileDom.value}
                                                onChange={handleSecurityGlobalFileChange}
                                            >
                                                {securityConfigFileDom.inputType === "file" ? (
                                                    <Input
                                                        type={securityConfigFileDom.inputType}
                                                        style={{width: "280px"}}
                                                    />
                                                ) : (
                                                    <Input
                                                        type={securityConfigFileDom.inputType}
                                                        readOnly={securityConfigFileDom.readOnly}
                                                        suffix={
                                                            <>
                                                                <PlusOutlined
                                                                    onClick={securityConfigPlusButtonCallback}
                                                                />
                                                                <EyeOutlined
                                                                    onClick={securityConfigPreviewButtonCallback}
                                                                />
                                                            </>
                                                        }
                                                        style={{backgroundColor: "#f0f0f0", width: "280px"}}
                                                        className={styles.inputFileFilled}
                                                    />
                                                )}
                                            </Form.Item>
                                            <Form.Item
                                                name="parkingSecurityConfigFile"
                                                label={parkingSecurityConfigFileLabel}
                                                initialValue={parkingSecurityConfigFileDom.value}
                                                onChange={handleParkingSecurityGlobalFileChange}
                                            >
                                                {parkingSecurityConfigFileDom.inputType === "file" ? (
                                                    <Input
                                                        type={parkingSecurityConfigFileDom.inputType}
                                                        style={{width: "280px"}}
                                                    />
                                                ) : (
                                                    <Input
                                                        type={parkingSecurityConfigFileDom.inputType}
                                                        readOnly={parkingSecurityConfigFileDom.readOnly}
                                                        suffix={
                                                            <>
                                                                <PlusOutlined
                                                                    onClick={parkingSecurityConfigPlusButtonCallback}
                                                                />
                                                                <EyeOutlined
                                                                    onClick={parkingSecurityConfigPreviewButtonCallback}
                                                                />
                                                            </>
                                                        }
                                                        style={{backgroundColor: "#f0f0f0", width: "280px"}}
                                                        className={styles.inputFileFilled}
                                                    />
                                                )}
                                            </Form.Item>
                                            <Form.Item
                                                name="allowSwitchSourceIP"
                                                label={allowSwitchSourceIPLabel}
                                                tooltip={allowSwitchSourceIPTooltip}
                                                rules={[{validator: validateIP}]}
                                                initialValue={allowSwitchSourceIP}
                                            >
                                                <Input
                                                    rules={[
                                                        {
                                                            required: true
                                                        }
                                                    ]}
                                                    placeholder="Eg: 10.0.0.0/8 , ***********/24"
                                                    style={{width: "280px"}}
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                name="enableDebugging"
                                                valuePropName="checked"
                                                label={enableDebuggingLabel}
                                                tooltip={enableDebuggingTooltip}
                                            >
                                                <Switch
                                                    onChange={handleDebugSwitchChange}
                                                    defaultChecked={isEnableDebug}
                                                />
                                            </Form.Item>
                                        </Form>
                                    );
                                }

                                return (
                                    <Form
                                        layout="horizontal"
                                        labelAlign="left"
                                        // labelCol={{span: 10}}
                                        // wrapperCol={{span: 18}}
                                        labelCol={{flex: "0 0 258px"}}
                                        wrapperCol={{span: 14}}
                                        form={systemConfigForm}
                                        labelWrap
                                        className="label-wrap"
                                    >
                                        <Form.Item
                                            name="deviceDefaultLoginUser"
                                            label={deviceDefaultLoginUserLabel}
                                            tooltip={deviceDefaultLoginUserTooltip}
                                            rules={[
                                                {
                                                    required: true
                                                }
                                            ]}
                                            initialValue={deviceDefaultLoginUser}
                                        >
                                            <Input
                                                onChange={e => setDeviceDefaultLoginUser(e.target.value)}
                                                style={{width: "280px"}}
                                            />
                                        </Form.Item>
                                        <Form.Item
                                            name="deviceDefaultPassword"
                                            label={deviceDefaultPasswordLabel}
                                            rules={[
                                                {
                                                    required: true
                                                }
                                            ]}
                                            initialValue={deviceDefaultPassword}
                                        >
                                            <CustomPasswordInput style={{width: "280px"}} />
                                        </Form.Item>
                                        <Form.Item
                                            name="licensePortalURL"
                                            label={licensePortalURLLabel}
                                            rules={[{required: true, validator: debounceURLValidator}]}
                                            initialValue={licensePortalURL}
                                        >
                                            <Input
                                                suffix={
                                                    <ApiFilled
                                                        style={{color: "rgba(0,0,0,.45)"}}
                                                        onClick={handleTestLicenseStateButtonCallback}
                                                    />
                                                }
                                                style={{width: "280px"}}
                                            />
                                        </Form.Item>
                                        <Form.Item
                                            name="licensePortalUser"
                                            label={licensePortalUserLabel}
                                            rules={[
                                                {
                                                    required: true
                                                }
                                            ]}
                                            initialValue={licensePortalUser}
                                        >
                                            <Input style={{width: "280px"}} />
                                        </Form.Item>
                                        <Form.Item
                                            name="licensePortalPassword"
                                            label={licensePortalPasswordLabel}
                                            rules={[
                                                {
                                                    required: true
                                                }
                                            ]}
                                            initialValue={licensePortalPassword}
                                        >
                                            <CustomPasswordInput style={{width: "280px"}} />
                                        </Form.Item>
                                        <Form.Item
                                            name="securityConfigFile"
                                            label={securityConfigFileLabel}
                                            rules={[
                                                {
                                                    required: true
                                                }
                                            ]}
                                            initialValue={securityConfigFileDom.value}
                                            onChange={handleSecurityGlobalFileChange}
                                        >
                                            {securityConfigFileDom.inputType === "file" ? (
                                                <Input
                                                    type={securityConfigFileDom.inputType}
                                                    style={{width: "280px"}}
                                                />
                                            ) : (
                                                <Input
                                                    type={securityConfigFileDom.inputType}
                                                    readOnly={securityConfigFileDom.readOnly}
                                                    suffix={
                                                        <>
                                                            <PlusOutlined onClick={securityConfigPlusButtonCallback} />
                                                            <EyeOutlined
                                                                onClick={securityConfigPreviewButtonCallback}
                                                            />
                                                        </>
                                                    }
                                                    style={{backgroundColor: "#f0f0f0", width: "280px"}}
                                                    className={styles.inputFileFilled}
                                                />
                                            )}
                                        </Form.Item>
                                    </Form>
                                );
                            })()}
                        </Flex>
                        <Flex vertical style={{paddingTop: 16, width: "538px"}}>
                            {/* <Divider className={styles.dividerSetting} /> */}
                            {(() => {
                                if (isAddGroupSystemConfig) {
                                    return (
                                        <Form
                                            layout="horizontal"
                                            labelAlign="left"
                                            labelCol={{flex: "0 0 258px"}}
                                            wrapperCol={{span: 20}}
                                            labelWrap
                                            className="label-wrap"
                                        >
                                            <Form.Item label=" ">
                                                <Flex>
                                                    <Button
                                                        type="primary"
                                                        onClick={addSystemConfigCallback}
                                                        className={styles.buttonStyle}
                                                    >
                                                        Add
                                                    </Button>
                                                    <Button
                                                        htmlType="button"
                                                        className={styles.buttonStyle}
                                                        onClick={() => {
                                                            refreshData().then(() => {});
                                                        }}
                                                    >
                                                        Reset
                                                    </Button>
                                                </Flex>
                                            </Form.Item>
                                        </Form>
                                    );
                                }
                                if (selectedSystemConfig === "Global") {
                                    return (
                                        <Flex justify="space-between">
                                            <Flex>
                                                <Button
                                                    type="primary"
                                                    onClick={saveSystemConfigCallback}
                                                    className={styles.buttonStyle}
                                                >
                                                    Save
                                                </Button>
                                                <Button
                                                    htmlType="button"
                                                    className={styles.buttonStyle}
                                                    onClick={() => {
                                                        refreshData().then(() => {});
                                                    }}
                                                >
                                                    Reset
                                                </Button>
                                                {/* <Button type="primary" icon={<CopyFilled />} style={buttonStyle}> */}
                                                {/*    Backup */}
                                                {/* </Button> */}
                                                {/* <Button type="primary" icon={<UndoOutlined />} style={buttonStyle}> */}
                                                {/*    Recover */}
                                                {/* </Button> */}
                                            </Flex>
                                            <Flex>
                                                <Button
                                                    type="primary"
                                                    className={styles.buttonStyle}
                                                    onClick={() => {
                                                        setIsShowModifyEncryptKeyModal(true);
                                                    }}
                                                >
                                                    Update Encrypt Key
                                                </Button>
                                                <Button
                                                    type="primary"
                                                    onClick={syslogConfigModalRef.current.showSyslogConfigModal}
                                                >
                                                    Syslog Config
                                                </Button>
                                            </Flex>
                                        </Flex>
                                    );
                                }
                                return (
                                    <Flex justify="space-between" style={{marginBottom: "30px"}}>
                                        <Flex>
                                            <Button
                                                type="primary"
                                                onClick={saveSystemConfigCallback}
                                                className={styles.buttonStyle}
                                            >
                                                Save
                                            </Button>
                                            <Button
                                                htmlType="button"
                                                className={styles.buttonStyle}
                                                onClick={() => {
                                                    refreshData().then(() => {});
                                                }}
                                            >
                                                Reset
                                            </Button>
                                            <Button
                                                htmlType="button"
                                                className={styles.buttonStyle}
                                                onClick={() => {
                                                    confirmModalAction(
                                                        `Do you want to remove system config ${selectedSystemConfig}?`,
                                                        () => {
                                                            removeSystemConfigByName(selectedSystemConfig).then(
                                                                response => {
                                                                    if (response.status !== 200) {
                                                                        message.error(response.info);
                                                                    } else {
                                                                        message.success(response.info);
                                                                        configurationNameForm.setFieldsValue({
                                                                            configurationName: "Global"
                                                                        });
                                                                        setSelectedSystemConfig("Global");
                                                                        refreshData("Global").then(() => {});
                                                                    }
                                                                }
                                                            );
                                                        }
                                                    );
                                                }}
                                            >
                                                Remove
                                            </Button>
                                        </Flex>
                                        <Flex>
                                            <Button
                                                type="primary"
                                                className={styles.buttonStyle}
                                                onClick={() => {
                                                    viewAssociatedSwitchModalRef.current.showViewAssociatedSwitchModal(
                                                        selectedSystemConfig
                                                    );
                                                }}
                                            >
                                                View Associated Switch
                                            </Button>
                                            <Button
                                                type="primary"
                                                onClick={() => {
                                                    manageSwitchModalRef.current.showManageSwitchModal(
                                                        selectedSystemConfig
                                                    );
                                                }}
                                            >
                                                Manage Switch
                                            </Button>
                                        </Flex>
                                    </Flex>
                                );
                            })()}
                        </Flex>
                    </Flex>
                    {isShowFilePreview ? (
                        <Flex className={styles.tile2} style={{flex: 1, minWidth: "580px"}}>
                            <ConfigContentTextarea title={filePreviewTitle} content={fileContent} />
                        </Flex>
                    ) : null}
                </Flex>
            ) : null}
        </Flex>
    );
};

export default AmpConSystemConfig;
