import {
    <PERSON><PERSON>,
    Card,
    Col,
    Divider,
    Flex,
    Form,
    List,
    message,
    Row,
    Select,
    Tabs,
    Tag,
    Empty,
    Checkbox,
    Space
} from "antd";
import {useEffect, useMemo, useRef, useState, forwardRef, useImperativeHandle} from "react";
import {useLocation, useParams} from "react-router-dom";
import {useForm} from "antd/es/form/Form";
import Icon from "@ant-design/icons/lib/components/Icon";
import {FilterSvg} from "@/utils/common/iconSvg";
import {
    fetchFsosSwtichInterface,
    fetchModulesTopK,
    fetchFsosSwtichUsage,
    fetchFsosSwtichAllPorts,
    fetchFsosSwitchDeviceMac,
    getFsosDeviceTemperature,
    getFsosDeviceFanSpeed,
    getFsosDeviceSupplyStatus,
    getFsosDevicePortOverview,
    getFsosDeviceFanStatus
} from "@/modules-ampcon/apis/monitor_api";
import {getSnmpViewData} from "@/modules-ampcon/apis/dashboard_api";
import {AmpConCustomModalForm, AmpConCustomTelemteryTable} from "@/modules-ampcon/components/custom_table";
import EmptyPic from "@/assets/images/App/empty.png";
import {
    CustomTelemetryCard,
    CustomTelemetryLimitCard,
    CustomTelemetryLinkCard,
    CustomLineChart
} from "@/modules-ampcon/components/echarts_common";
import settingGreySvg from "../../Topo/Topology/resource/site_grey.svg?react";
import settingGreenSvg from "../../Topo/Topology/resource/site_green.svg?react";
import styles from "./fsos_switch_telemetry.module.scss";
import HealthStatus from "./health_status";
import * as echarts from "echarts";
import {TelemetryDateRangePicker} from "@/modules-ampcon/components/telemetry_date_range_picker";

const importAllImages = () => {
    const images = {};
    const modules = import.meta.glob("/src/assets/switchs/*.png", {eager: true});

    // eslint-disable-next-line guard-for-in
    for (const path in modules) {
        const key = path.split("/").pop().replace(".png", "");
        images[key] = modules[path];
    }

    return images;
};

const switchImages = importAllImages();

const usageOptions = {
    // both: "Usage (%)",
    fan: "Fan Speed (RPM)"
};

const temperatureOptions = {
    temperature: "Device Temperature (°C)"
};

const cpuMemoryOptions = {
    memory: "Memory (%)",
    cpu: "CPU (%)"
};
const flashUsageOptions = {
    "flash-usages": "Flash Usages"
};

const interfaceCheckboxOptions = {
    "out-packets-discarded": "Out Packets Discarded",
    "out-packets-with-errors": "Out Packets with Errors",
    "in-packets-discarded": "In Packets Discarded",
    "in-packets-with-errors": "In Packets with Errors",
    "bits-sent": "Bits Sent (bytes)",
    "bits-received": "Bits Received (bytes)",
    "poe-power": "PoE Power (W)",
    "rx-power": "Rx Power (dBm)",
    "tx-power": "Tx Power (dBm)",
    "bias-current": "Bias Current (mA)",
    "bias-voltage": "Bias Voltage (V)",
    "optical-module-temperature": "Optical Module Temperature (°C)"
};

const extractParts = str => {
    const match = str.match(/^([A-Za-z-]+)-(\d+)\/(\d+)\/(\d+)$/);
    if (!match) {
        return {prefix: str, numbers: []};
    }
    const prefix = match[1];
    const numbers = match.slice(2).map(num => parseInt(num, 10));
    return {prefix, numbers};
};

const FSOSSwitchTelemetry = () => {
    const location = useLocation();
    const [switchInfo, setSwitchInfo] = useState([]);
    const [record, setRecord] = useState({});
    const [activeKey, setActiveKey] = useState("switchOverview");
    const {sn} = useParams();
    const tabRef = useRef(null);
    const [tableWidth, setTableWidth] = useState(0);

    const handleResize = () => {
        if (tabRef.current) {
            setTableWidth(tabRef.current.offsetWidth - 50);
        }
    };

    const makeSwitchInfo = async sn => {
        const response = await getSnmpViewData(
            1,
            10,
            [{field: "sn", filters: [{value: sn, matchMode: "exact"}]}],
            [],
            {}
        );
        const macResponse = await fetchFsosSwitchDeviceMac(sn);
        if (response.status === 200) {
            const record = response.data[0];
            const data = [
                {
                    title: "SN",
                    value: record.sn
                },
                {
                    title: "Sysname",
                    value: record.sysname
                },
                {
                    title: "MAC Address",
                    value: macResponse?.data?.mac_address ?? ""
                },
                {
                    title: "IP Address",
                    value: record.mgt_ip
                },
                {
                    title: "Version",
                    value: record.version
                }
            ];
            setSwitchInfo(data);
            setRecord(record);
        }
    };

    const items = [
        {
            key: "switchOverview",
            label: "Switch Overview",
            children: (
                <SwitchOverview
                    active={activeKey === "switchOverview"}
                    sn={sn}
                    showAI={record.gnmi_ai && import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC"}
                />
            )
        },
        {
            key: "deviceOverview",
            label: "Device Overview",
            children: <DeviceOverview active={activeKey === "deviceOverview"} sn={sn} tableWidth={tableWidth} />
        },
        {
            key: "portOverview",
            label: "Port Overview",
            children: <PortOverview active={activeKey === "portOverview"} sn={sn} tableWidth={tableWidth} />
        }
    ];

    useEffect(() => {
        const pathParts = location.pathname.split("/");
        const sn = pathParts[pathParts.length - 1];
        makeSwitchInfo(sn);
        if (location.state && location.state.showModuleStatus) {
            setActiveKey("modulesOverview");
        }
    }, [location.pathname]);

    useEffect(() => {
        handleResize();
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    return (
        <div style={{display: "flex", flexDirection: "column", height: "100%"}}>
            <Card title="Device Information">
                <div
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        justifyContent: "flex-start",
                        alignItems: "center",
                        marginTop: "12px"
                    }}
                >
                    <div style={{marginLeft: "36px"}}>
                        <img
                            src={switchImages[record.platform_model]?.default || switchImages.default_switch.default}
                            alt="Model"
                        />
                        <div style={{display: "flex"}}>
                            <div style={{color: "#929A9E", marginLeft: "8px", marginRight: "8px"}}>Model:</div>
                            <div style={{whiteSpace: "nowrap"}}>{record.model}</div>
                        </div>
                    </div>
                    <Divider type="vertical" style={{height: "100px", marginLeft: "60px", marginRight: "60px"}} />
                    <List
                        dataSource={switchInfo}
                        split={false}
                        grid={{gutter: 32, column: 3}}
                        renderItem={item => (
                            <List.Item key={item.title}>
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center"
                                    }}
                                >
                                    <span
                                        style={{
                                            color: "#929A9E",
                                            width: "100px",
                                            display: "inline-block"
                                        }}
                                    >
                                        {item.title}:
                                    </span>
                                    <span style={{whiteSpace: "nowrap"}}>{item.value}</span>
                                </div>
                            </List.Item>
                        )}
                    />
                </div>
            </Card>
            <Flex ref={tabRef}>
                <Tabs
                    onChange={setActiveKey}
                    activeKey={activeKey}
                    items={items}
                    style={{marginTop: "24px", maxWidth: "100%"}}
                />
            </Flex>
        </div>
    );
};

const getSorter = (dataIndex, isString) => {
    return isString ? (a, b) => a[dataIndex].localeCompare(b[dataIndex]) : (a, b) => a[dataIndex] - b[dataIndex];
};

export const SnmpCard = forwardRef(({showFilter, name, type, timeRange, cardstyle, target, filterItems = []}, ref) => {
    const [chartData, setChartData] = useState([]);
    const [xAxisData, setXAxisData] = useState([]);
    const [topK, setTopK] = useState(5);
    const [xAxisInterval, setXAxisInterval] = useState(1);
    const [itemPortSelectedItems, setItemPortSelectedItems] = useState([]);
    const finalFilterItems =
        filterItems.length > 0
            ? [
                  {value: "all", label: "All Ports"},
                  ...filterItems.map(item => (typeof item === "string" ? {value: item, label: item} : item))
              ]
            : [];
    const fetchData = async () => {
        let response;
        if (type === "cpu") {
            response = await fetchFsosSwtichUsage(name, target, timeRange[0], timeRange[1]);
        } else if (type === "interface") {
            response = await fetchFsosSwtichInterface(itemPortSelectedItems, name, target, timeRange[0], timeRange[1]);
        } else if (type === "modules") {
            response = await fetchModulesTopK(name, topK, target, timeRange[0], timeRange[1]);
        } else if (type === "temperature") {
            response = await getFsosDeviceTemperature(target, timeRange[0], timeRange[1]);
        } else if (type === "fan") {
            response = await getFsosDeviceFanSpeed(target, timeRange[0], timeRange[1]);
        }
        if (response.status !== 200) {
            message.error(response.info);
            setChartData([]);
            setXAxisData([]);
            return;
        }
        let rawData = [];
        if (type === "fan") {
            rawData = response.data.fans || [];
        } else {
            rawData = response.data || [];
        }
        if (rawData.length > 0) {
            const series = rawData.map((item, index) => {
                let seriesName = "unknown";
                if (type === "cpu" || type === "temperature") {
                    seriesName = item.target;
                } else if (type === "interface") {
                    seriesName = item.interface_name;
                } else if (type === "modules") {
                    seriesName = item.sn || item.target || "modules";
                } else if (type === "fan") {
                    seriesName = item.fan_name || `Fan ${item.fan_index}`;
                }
                const safeValues = Array.isArray(item.values) ? item.values : [];
                return {
                    name: seriesName,
                    data: safeValues.map(v => {
                        let x = null;
                        let y = null;

                        if (v && typeof v === "object" && !Array.isArray(v)) {
                            x = v.time ?? null;
                            y = v.used_percentage ?? null;
                        } else if (Array.isArray(v)) {
                            x = v[0] ?? null;
                            y = v[1] ?? null;
                        }

                        return {
                            value: [x, y],
                            index_value: item.index_value
                        };
                    }),
                    connectNulls: false
                };
            });
            setChartData(series);
            const xAxisData = Array.from(
                new Set(
                    rawData.flatMap(item => {
                        if (!Array.isArray(item.values)) return [];
                        return item.values.map(v => (Array.isArray(v) ? v[0] : v.time));
                    })
                )
            ).sort();
            if (timeRange[0] && timeRange[1]) {
                const totalPoints = xAxisData.length;
                const interval = Math.floor(totalPoints / 5);
                setXAxisInterval(interval);
            } else {
                setXAxisInterval(1);
            }
            setXAxisData(xAxisData);
        } else {
            setChartData([]);
            setXAxisData([]);
        }
    };
    const getNiceAxis = (rawMax, rawMin, targetTicks = 5) => {
        if (!isFinite(rawMax) || !isFinite(rawMin)) return {max: 1, min: 0, interval: 0.2};
        let min, max;
        const isAllPositive = rawMin >= 0;
        const isAllNegative = rawMax <= 0;
        if (rawMax === 0 && rawMin === 0) {
            return {max: 1, min: 0, interval: 0.2};
        }
        if (isAllPositive) {
            min = 0;
            max = rawMax;
        } else if (isAllNegative) {
            // 全负数特殊处理：从最小值到0
            min = rawMin;
            max = 0;
        } else {
            min = rawMin;
            max = rawMax;
        }
        // 计算初始间隔
        const calculateInterval = span => {
            const rough = span / targetTicks;
            const mag = 10 ** Math.floor(Math.log10(Math.abs(rough)));
            const r = rough / mag;
            if (r <= 1) return 1 * mag;
            if (r <= 2) return 2 * mag;
            if (r <= 2.5) return 2.5 * mag;
            if (r <= 5) return 5 * mag;
            return 10 * mag;
        };
        let interval = calculateInterval(max - min);
        // 对全负数情况的特殊处理
        if (isAllNegative) {
            // 计算绝对值范围的nice间隔
            const absInterval = calculateInterval(Math.abs(rawMin));
            // 确保间隔为正值，并向下取整
            interval = Math.abs(absInterval);
            min = Math.floor(rawMin / interval) * interval;
            // 确保0总是包含在内
            max = 0;
        }
        // 对全正数情况的特殊处理
        if (isAllPositive) {
            const targetRatio = 4 / 5;
            const candidateMax1 = Math.ceil(rawMax / interval) * interval;
            const candidateMax2 = Math.ceil(rawMax / targetRatio / interval) * interval;

            max = Math.min(candidateMax2, rawMax * 2);
            interval = calculateInterval(max);
        }
        // 最终调整
        if (isAllPositive) {
            max = Math.ceil(max / interval) * interval;
        } else if (isAllNegative) {
            // 保持min的调整，max固定为0
        } else {
            max = Math.ceil(max / interval) * interval;
            min = Math.floor(min / interval) * interval;
        }
        return {max, min, interval};
    };
    const allValues = chartData.flatMap(item => item.data.map(point => point.value[1]));
    const dataMax = allValues.length > 0 ? allValues.reduce((max, v) => Math.max(max, v), -Infinity) : 0;
    const dataMin = allValues.length > 0 ? allValues.reduce((min, v) => Math.min(min, v), Infinity) : 0;
    const {max: yMax, interval: yInterval, min: yMin} = getNiceAxis(dataMax, dataMin, 5);
    const option = {
        tooltip: {
            trigger: "axis",
            formatter: params => {
                const sortedParams = params.sort((a, b) => {
                    if (b.value[1] !== a.value[1]) {
                        return b.value[1] - a.value[1];
                    }
                    return (a.data.index_value ?? 0) - (b.data.index_value ?? 0);
                });
                let content = `
                <div style="width: 100%; margin: 0; padding: 0;">
                    <div style="background-color: #F8FAFB; width: calc(100% + 22px); padding: 5px;padding-left:14px; margin: -11px -12px 10px -11px;border-bottom: 1px solid #F2F2F2;">
                        <div style="font-size: 16px;front-weight: 600 ; color: #212519">${params[0].name}</div>
                    </div>
            `;
                sortedParams.forEach(item => {
                    content += `
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div style="display: flex; align-items: center;">
                            <span style="display:inline-block;margin-right:5px;border-radius:1px;width:12px;height:12px;background-color:${item.color};"></span>
                            <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">${item.seriesName}</span>
                          </div>
                          <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${item.value[1]}</span>
                        </div>
                    `;
                });
                content += `</div>`;

                return content;
            },
            position(pos, params, el, elRect, size) {
                const obj = {};
                const [x, y] = pos;
                const tooltipWidth = el.getBoundingClientRect().width;
                const parentRect = el.parentElement.getBoundingClientRect();
                const rightSpace = parentRect.width - x;
                if (y > window.innerHeight / 2) {
                    obj.bottom = "30px";
                    delete obj.top;
                }
                if (rightSpace < x - 10 - tooltipWidth) {
                    obj.left = `${x - tooltipWidth - 10}px`;
                } else {
                    obj.left = `${x + 10}px`;
                }

                return obj;
            }
        },
        legend: {
            data: chartData
                .slice()
                .sort((a, b) => {
                    const a_index_value = a.data[0]?.index_value ?? null;
                    const b_index_value = b.data[0]?.index_value ?? null;
                    if (a_index_value != null && b_index_value != null) {
                        return parseInt(a_index_value) - parseInt(b_index_value);
                    }
                    if (a_index_value != null) return -1;
                    if (b_index_value != null) return 1;
                    return 0;
                })
                .map(item => item.name),
            orient: "horizontal", // 设置图例的方向为水平
            top: "88%", // 设置图例的垂直位置
            left: "center", // 设置图例的水平位置
            right: "5%",
            textStyle: {
                // 图例文字样式
                fontSize: 15
            },
            itemWidth: 10, // 图例图形的宽度
            itemHeight: 10, // 图例图形的高度
            type: "scroll",
            pageIconColor: "#A2ACB2", // 默认可点击色值
            pageIconInactiveColor: "#E3E5EB", // 不可点击色值
            width: "95%",
            icon: "rect"
        },
        grid: {
            left: "3%",
            right: "3%",
            top: "5%",
            bottom: "10%",
            containLabel: true,
            width: "95%",
            height: "75%"
        },
        xAxis: {
            type: "category",
            data: xAxisData,
            axisLabel: {
                interval: xAxisInterval,
                formatter(value) {
                    const date = new Date(value);
                    const startDate = new Date(timeRange[0] || Date.now() - 5 * 60 * 1000);
                    const endDate = new Date(timeRange[1] || Date.now());
                    const hour = date.getHours().toString().padStart(2, "0");
                    const minute = date.getMinutes().toString().padStart(2, "0");
                    const second = date.getSeconds().toString().padStart(2, "0");
                    if (startDate.getMonth() !== endDate.getMonth() || startDate.getDate() !== endDate.getDate()) {
                        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                    }
                    return `${hour}:${minute}:${second}`;
                }
            },
            splitLine: {
                show: true
            }
        },
        yAxis: {
            type: "value",
            max: yMax,
            min: yMin,
            interval: yInterval,
            axisLabel: {
                formatter(value) {
                    if (value > 1e9) {
                        return `${value.toExponential(2)}`;
                    }
                    if (value >= 1000000) {
                        return `${value / 1000000}M`;
                    }
                    if (value >= 1000) {
                        return `${value / 1000}k`;
                    }
                    return value;
                }
            }
        },
        series: chartData.map(item => ({
            name: item.name,
            type: "line",
            data: item.data,
            symbol: "none"
        })),
        width: "100%",
        height: "180px"
    };

    useImperativeHandle(ref, () => ({
        refreshTelemetry: () => {
            fetchData();
        }
    }));

    useEffect(() => {
        fetchData();
    }, [name, timeRange, itemPortSelectedItems]);
    let label;
    if (type === "temperature") {
        label = <span>{temperatureOptions[name]}</span>;
    } else if (type === "cpu") {
        label = <span>{cpuMemoryOptions[name]}</span>;
    } else if (type === "interface") {
        label = <span>{interfaceCheckboxOptions[name]}</span>;
    } else if (type === "fan") {
        label = <span>{usageOptions[name]}</span>;
    } else if (type === "flash") {
        label = <span>{flashUsageOptions[name]}</span>;
    }
    const itemPortOptionRender = oriOption => {
        const isAll = oriOption.value === "all";
        const allCount = finalFilterItems.length - 1;
        const selectedCount = itemPortSelectedItems.length;
        const checked = isAll ? selectedCount === allCount : itemPortSelectedItems.includes(oriOption.value);
        const indeterminate = isAll && selectedCount > 0 && selectedCount < allCount;
        return (
            <Space>
                <Checkbox checked={checked} indeterminate={indeterminate} />
                <div className={styles.optionLabel}>{oriOption.label}</div>
            </Space>
        );
    };
    const handleItemNameChange = (value, option) => {
        const allClicked = option.find(opt => opt.value === "all");
        if (allClicked) {
            if (itemPortSelectedItems.length === finalFilterItems.length - 1) {
                setItemPortSelectedItems([]);
            } else {
                const allValues = finalFilterItems.filter(opt => opt.value !== "all").map(opt => opt.value);
                setItemPortSelectedItems(allValues);
            }
        } else {
            setItemPortSelectedItems(value);
        }
    };
    return (
        <Card
            title={
                <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                    {label}
                    {showFilter ? (
                        <Select
                            mode="multiple"
                            style={{minWidth: 120}}
                            options={finalFilterItems}
                            value={itemPortSelectedItems}
                            optionRender={itemPortOptionRender}
                            onChange={handleItemNameChange}
                            maxTagCount={2}
                            placeholder={
                                <span style={{display: "flex", alignItems: "center", gap: 4}}>
                                    <FilterSvg style={{width: 14, height: 14, color: "#999"}} />
                                    Filter
                                </span>
                            }
                        />
                    ) : null}
                </div>
            }
            bordered={false}
            style={{
                height: "350px",
                width: "100%",
                ...(cardstyle ?? {})
            }}
            className="linechart"
        >
            {option.series.length === 0 ? (
                <div style={{display: "flex", justifyContent: "center", alignItems: "center"}}>
                    <Empty image={EmptyPic} description="No Data" imageStyle={{display: "block", margin: 0}} />
                </div>
            ) : (
                <CustomLineChart chartOption={option} />
            )}
        </Card>
    );
});

const FSOSTelemetrySetting = ({form, isModalOpen, onCancel, onChange}) => {
    const formItems = () => {
        const allOptions = {
            ...flashUsageOptions,
            ...cpuMemoryOptions,
            ...interfaceCheckboxOptions
        };

        return (
            <Form.Item name="selectedCounters" label="">
                <Checkbox.Group>
                    <Row gutter={[16, 16]}>
                        {Object.entries(allOptions).map(([value, title]) => (
                            <Col span={12} key={value}>
                                <Checkbox value={value}>{title}</Checkbox>
                            </Col>
                        ))}
                    </Row>
                </Checkbox.Group>
            </Form.Item>
        );
    };

    return (
        <AmpConCustomModalForm
            title="All Counters"
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 5
                }
            }}
            CustomFormItems={formItems}
            onCancel={onCancel}
            onSubmit={onChange}
            modalClass="ampcon-middle-modal"
        />
    );
};

const SwitchOverview = ({active, sn, showAI}) => {
    const [form] = useForm();
    const [timeRange, setTimeRange] = useState(["", ""]);
    const [counters, setCounters] = useState({
        cpu: Object.keys(cpuMemoryOptions),
        flash: Object.keys(flashUsageOptions),
        interface: Object.keys(interfaceCheckboxOptions)
    });
    const [isSelectCountersModalOpen, setSelectCountersModalOpen] = useState(false);
    const cardRefs = useRef({});
    const [isHovered, setIsHovered] = useState(false);
    const [statisticsData, setStatisticsData] = useState({
        serverFlashData: [],
        loading: true
    });
    const [filterItems, setFilterItems] = useState([]);
    const addToRefs = (el, name) => {
        if (el) {
            cardRefs.current[name] = el;
        }
    };

    const onChangeCounters = () => {
        const selected = form.getFieldValue("selectedCounters") || [];
        setCounters({
            cpu: selected.filter(i => cpuMemoryOptions[i]),
            flash: selected.filter(i => flashUsageOptions[i]),
            interface: selected.filter(i => interfaceCheckboxOptions[i])
        });
        setSelectCountersModalOpen(false);
    };

    useEffect(() => {
        if (active) {
            Object.keys(cardRefs.current).forEach(key => {
                const ref = cardRefs.current[key];
                if (ref && ref.refreshTelemetry) {
                    ref.refreshTelemetry();
                }
            });
        }
    }, [active]);

    useEffect(() => {
        const allSelected = [...counters.cpu, ...counters.flash, ...counters.interface];
        form.setFieldsValue({selectedCounters: allSelected});
        fetchFlashUsage();
        loadFsosSwitchAllPorts(sn);
    }, []);

    const loadFsosSwitchAllPorts = async sn => {
        try {
            const res = await fetchFsosSwtichAllPorts(sn);
            setFilterItems(res);
        } catch (e) {
            console.error(e);
        }
    };

    const fetchFlashUsage = async () => {
        try {
            const res = await fetchFsosSwtichUsage("flash-usages", sn);
            if (res.status !== 200 || !res.data.length) {
                setStatisticsData({
                    serverFlashData: [],
                    loading: false
                });
                return;
            }
            const parseSizeWithUnit = sizeStr => {
                if (sizeStr == null || sizeStr === "") {
                    return {value: 0, raw: "0", unit: "", exists: false};
                }
                const match = sizeStr.match(/^([\d.]+)\s*([A-Za-z]+)$/);
                if (match) {
                    return {value: parseFloat(match[1]), raw: match[1], unit: match[2], exists: true};
                }
                return {value: parseFloat(sizeStr) || 0, raw: String(sizeStr), unit: "", exists: true};
            };
            const usageParsed = parseSizeWithUnit(res.data[0].values[0].usage_size);
            const freeParsed = parseSizeWithUnit(res.data[0].values[0].free_size);
            const totalParsed = parseSizeWithUnit(res.data[0].values[0].total_size);
            const seriesData = [];
            if (usageParsed.exists) {
                seriesData.push({
                    name: `Usage ${usageParsed.raw}${usageParsed.unit}`,
                    value: usageParsed.value
                });
            }
            if (freeParsed.exists) {
                seriesData.push({
                    name: `Free ${freeParsed.raw}${freeParsed.unit}`,
                    value: freeParsed.value
                });
            }
            setStatisticsData({
                serverFlashData: seriesData,
                totalFlash: totalParsed.exists ? `${totalParsed.raw}${totalParsed.unit}` : undefined,
                loading: false
            });
        } catch (e) {
            console.error(e);
            setStatisticsData(prev => ({...prev, loading: false}));
        }
    };

    const allCards = useMemo(
        () => [
            ...counters.flash.map(item => {
                return (
                    <Card
                        title="Flash Usage"
                        style={{height: "100%", width: "100%"}}
                        loading={statisticsData.loading}
                        className="chart-center"
                    >
                        <BasePieEcharts
                            name="Flash"
                            seriesData={statisticsData.serverFlashData}
                            totalValue={statisticsData.totalFlash}
                            chartType="ring"
                            colorList={["#FFBB00", "#14C9BB"]}
                            height="22vh"
                            maxWidth="300px"
                            showPercent={false}
                            cardstyle={{
                                borderColor: "#E7E7E7",
                                borderWidth: "1px",
                                borderStyle: "solid",
                                boxShadow: "none"
                            }}
                        />
                    </Card>
                );
            }),
            ...counters.cpu.map(item => (
                <SnmpCard
                    showFilter={false}
                    key={item}
                    name={item}
                    type="cpu"
                    label={cpuMemoryOptions[item]}
                    timeRange={timeRange}
                    target={sn}
                    cardstyle={{
                        borderColor: "#E7E7E7",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        boxShadow: "none"
                    }}
                />
            )),
            ...counters.interface.map(item => (
                <SnmpCard
                    showFilter
                    key={item}
                    name={item}
                    target={sn}
                    type="interface"
                    filterItems={filterItems}
                    label={interfaceCheckboxOptions[item]}
                    timeRange={timeRange}
                    cardstyle={{
                        borderColor: "#E7E7E7",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        boxShadow: "none"
                    }}
                />
            ))
        ],
        [counters, timeRange, filterItems, statisticsData]
    );

    return (
        <>
            <div style={{display: "flex", justifyContent: "flex-end"}}>
                <div style={{display: "flex", alignItems: "center", justifyContent: "center"}}>Time (UTC-0)</div>
                <TelemetryDateRangePicker
                    timeRange={timeRange}
                    setTimeRange={setTimeRange}
                    placeholder={["Start Time", "End Time"]}
                />
                <Divider type="vertical" style={{height: "30px", marginLeft: "16px", marginRight: "16px"}} />
                <Button
                    style={{borderColor: isHovered ? "#34DCCF" : "#d9d9d9"}}
                    icon={<Icon component={isHovered ? settingGreenSvg : settingGreySvg} />}
                    onClick={() => setSelectCountersModalOpen(true)}
                    onMouseEnter={() => {
                        setIsHovered(true);
                    }}
                    onMouseLeave={() => {
                        setIsHovered(false);
                    }}
                />
            </div>
            {sn ? (
                <div style={{height: "100%", width: "100%", marginTop: "18px", marginBottom: "18px"}}>
                    <Row gutter={[24, 24]}>
                        {allCards.flat().map((card, index) => (
                            <Col key={index} span={24} xxl={12} style={{display: "flex", justifyContent: "center"}}>
                                {card}
                            </Col>
                        ))}
                    </Row>
                </div>
            ) : null}
            <FSOSTelemetrySetting
                form={form}
                isModalOpen={isSelectCountersModalOpen}
                onCancel={() => {
                    form.setFieldsValue(counters);
                    setSelectCountersModalOpen(false);
                }}
                onChange={onChangeCounters}
            />
        </>
    );
};

const DeviceOverview = ({active, sn, tableWidth}) => {
    const [fanFormattedData, setFanFormattedData] = useState([]);
    const [rpsuFormattedData, setRpsuFormattedData] = useState([]);
    const [timeRange, setTimeRange] = useState(["", ""]);
    const [counters, setCounters] = useState({
        temperature: Object.keys(temperatureOptions),
        usage: Object.keys(usageOptions)
    });

    const chartCards = useMemo(
        () => [
            ...counters.temperature.map(item => (
                <SnmpCard
                    key={item}
                    name={item}
                    type="temperature"
                    label={temperatureOptions[item]}
                    timeRange={timeRange}
                    target={sn}
                    cardstyle={{
                        borderColor: "#E7E7E7",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        boxShadow: "none"
                    }}
                    showFilter={false}
                />
            )),
            ...counters.usage.map(item => (
                <SnmpCard
                    key={item}
                    name={item}
                    type="fan"
                    label={usageOptions[item]}
                    target={sn}
                    timeRange={timeRange}
                    cardstyle={{
                        borderColor: "#E7E7E7",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        boxShadow: "none"
                    }}
                    showFilter={false}
                />
            ))
        ],
        [fanFormattedData, rpsuFormattedData]
    );

    const fanColumnsConfig = [
        {title: "Fan", dataIndex: "fan_name", isString: true},
        {title: "Status", dataIndex: "status", isString: true},
        {title: "Speed", dataIndex: "speed", isString: false}
    ];

    const fanColumns = fanColumnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const rpsuColumnsConfig = [
        {title: "Supply Index", dataIndex: "index"},
        {title: "Status", dataIndex: "value", isString: true}
    ];

    const rpsuColumns = rpsuColumnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchDataSupplyStatus = async () => {
        try {
            const res = await getFsosDeviceSupplyStatus(sn);
            if (res.status === 200) {
                const raw = res.data?.values || [];
                const formatted = raw.map(item => ({
                    index: item.index,
                    value: item.status
                }));
                setRpsuFormattedData(formatted);
            } else {
                message.error(res.info || "Failed to fetch supply status");
                setRpsuFormattedData([]);
            }
        } catch (e) {
            console.error(e);
            message.error("Failed to fetch supply status");
            setRpsuFormattedData([]);
        }
    };
    const fetchDataFanStatus = async () => {
        try {
            const res = await getFsosDeviceFanStatus(sn);
            if (res.status === 200) {
                const raw = res.data?.values || [];
                const formatted = raw.map(item => ({
                    fan_name: item.fan_name,
                    speed: item.speed,
                    status: item.status
                }));
                setFanFormattedData(formatted);
            } else {
                message.error(res.info || "Failed to fetch supply status");
                setFanFormattedData([]);
            }
        } catch (e) {
            console.error(e);
            message.error("Failed to fetch supply status");
            setFanFormattedData([]);
        }
    };

    useEffect(() => {
        if (active) {
            fetchDataSupplyStatus();
            fetchDataFanStatus();
        }
    }, [active, sn]);

    return (
        <div style={{height: "100%", width: "98%", marginTop: "18px", marginBottom: "18px", marginLeft: "0px"}}>
            <Row gutter={[24, 24]} style={{marginBottom: 24}}>
                {chartCards.map((card, index) => (
                    <Col key={index} span={24} xxl={12} style={{display: "flex", justifyContent: "center"}}>
                        {card}
                    </Col>
                ))}
            </Row>

            <Row gutter={[24, 24]} style={{marginBottom: 24}}>
                <Col span={12}>
                    <Card>
                        <h2 style={{marginTop: "-6px", fontSize: "18px"}}>Supply Status</h2>
                        <AmpConCustomTelemteryTable
                            columnsConfig={rpsuColumns}
                            data={rpsuFormattedData}
                            tableWidth={tableWidth}
                            showSetting={false}
                        />
                    </Card>
                </Col>

                <Col span={12}>
                    <Card>
                        <h2 style={{marginTop: "-6px", fontSize: "18px"}}>Fan Status</h2>
                        <AmpConCustomTelemteryTable
                            columnsConfig={fanColumns}
                            data={fanFormattedData}
                            tableWidth={tableWidth}
                            showSetting={false}
                        />
                    </Card>
                </Col>
            </Row>
        </div>
    );
};

const PortOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);
    // All Columns里可供选择部分
    const columnsConfig = [
        {title: "Port Name", dataIndex: "name", fixed: "left", isString: true},
        {
            title: "Port Status",
            dataIndex: "port_status",
            isString: true,
            render: (_, record) => {
                const status = record.port_status || "unknown";
                const display = status.charAt(0).toUpperCase() + status.slice(1);
                return <Tag className={status === "up" ? "up-tag" : "down-tag"}>{display}</Tag>;
            }
        },
        {title: "MTU", dataIndex: "mtu", width: 100},
        {title: "Port Speed", dataIndex: "port_speed", isString: false},
        {title: "MAC Address", dataIndex: "address", isString: true, width: 140},
        {title: "In Packets Discarded", dataIndex: "in_packets_discarded", isString: false, width: 200},
        {title: "In Packets with Errors", dataIndex: "in_packets_with_errors", isString: false, width: 200},
        {title: "Out Packets Discarded", dataIndex: "out_packets_discarded", isString: false, width: 200},
        {title: "Out Packets with Errors", dataIndex: "out_packets_with_errors", isString: false, width: 200},
        {title: "Bits Sent", dataIndex: "bits_sent", isString: false, width: 100},
        {title: "Bits Received", dataIndex: "bits_received", isString: false, width: 100}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        const response = await getFsosDevicePortOverview(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const tableData = Object.values(response.data);
            setFormattedData(tableData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return (
        <AmpConCustomTelemteryTable
            showSetting={false}
            showSettingColumn
            columnsConfig={columns}
            data={formattedData}
            tableWidth={tableWidth}
        />
    );
};

export default FSOSSwitchTelemetry;
const BasePieEcharts = ({
    seriesData,
    name,
    chartType = "pie",
    colorList = [],
    height = "28vh",
    maxWidth = "430px",
    onClicked = null,
    showPercent = true,
    totalValue = ""
}) => {
    const dataForChart = [...seriesData];
    if (dataForChart.length === 0 && totalValue) {
        dataForChart.push({
            name: `Total ${totalValue}`,
            value: 1 // 给一个占位值即可
        });
    }
    let chartRadius;
    switch (chartType) {
        case "pie":
            chartRadius = ["0%", "69%"];
            break;
        case "ring":
            chartRadius = ["45%", "65%"];
            break;
        default:
            chartRadius = ["0%", "70%"];
    }
    const getMaxDecimalDigits = arr => {
        return arr.reduce((max, item) => {
            const str = String(item.value);
            const decimals = str.includes(".") ? str.split(".")[1].length : 0;
            return Math.max(max, decimals);
        }, 0);
    };
    const precision = getMaxDecimalDigits(dataForChart);
    const total = dataForChart.reduce((sum, item) => sum + parseFloat(item.value), 0).toFixed(precision);
    const chartRef = useRef();
    useEffect(() => {
        const container = chartRef.current;
        if (!container) {
            return;
        }
        const myChart = echarts.init(chartRef.current);
        const option = {
            tooltip: {
                trigger: "item",
                backgroundColor: "rgba(0, 0, 0, 0.6)", // 设置提示框背景颜色为黑色，透明度为0.6
                textStyle: {
                    color: "#fff" // 设置提示框中的字体颜色为白色
                },
                formatter(params) {
                    const parts = params.name.split(" ");
                    if (parts.length > 1) {
                        parts[parts.length - 1] = parts[parts.length - 1].replace(/\d+(\.\d+)?/g, "");
                    }
                    const bWithoutNumbers = parts.join(" ");
                    return showPercent
                        ? `${params.seriesName} <br/>${bWithoutNumbers}: ${params.value}%`
                        : `${params.seriesName} <br/>${bWithoutNumbers}: ${params.value}`;
                }
            },
            legend: {
                type: "scroll",
                orient: "vertical",
                top: "center",
                left: maxWidth === "450px" ? "10px" : "0px",
                itemGap: 15,
                itemWidth: 12, // 设置图例 item 的宽度
                itemHeight: 12, // 设置图例 item 的高度
                formatter(name) {
                    const nameSplit = name.split(/\s+/);
                    if (
                        nameSplit[0] === "Usage" ||
                        nameSplit[0] === "Free" ||
                        nameSplit[0] === "Used" ||
                        nameSplit[0] === "Unused" ||
                        nameSplit[0] === "Normal" ||
                        nameSplit[0] === "Abnormal" ||
                        nameSplit[0] === "Expired" ||
                        nameSplit[0] === "Total"
                    ) {
                        return showPercent
                            ? `{name|${nameSplit[0]}}{count|${nameSplit[1]}%}`
                            : `{name|${nameSplit[0]}}{count|${nameSplit[1]}}`;
                    }
                    return name;
                }
            },
            textStyle: {
                rich: {
                    name: {
                        color: "#929A9E",
                        lineHeight: 20,
                        textAlign: "center",
                        display: "inline-block",
                        width: maxWidth === "450px" ? 65 : 40
                    },
                    count: {
                        fontSize: "14px",
                        fontWeight: 700,
                        lineHeight: 20,
                        textAlign: "center",
                        display: "inline-block",
                        width: 10
                    }
                }
            },
            series: [
                {
                    name,
                    type: "pie",
                    center: ["70%", "50%"],
                    radius: chartRadius,
                    label: {
                        show: chartType === "ring",
                        position: "center",
                        formatter: () => {
                            if (showPercent) {
                                return "";
                            }
                            if (!totalValue) {
                                return "";
                            }
                            if (dataForChart.length === 1 && /^Total\b/.test(dataForChart[0].name)) {
                                return "";
                            }
                            return `{value|${totalValue}}\n{total|Total}`;
                        },
                        rich: {
                            value: {
                                color: "#333",
                                fontSize: maxWidth === "450px" ? 20 : "16px",
                                fontWeight: "bold",
                                lineHeight: 30
                            },
                            total: {
                                color: "#333",
                                fontSize: maxWidth === "450px" ? 16 : "14px",
                                lineHeight: 20
                            }
                        }
                    },
                    data: dataForChart,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: "rgba(0, 0, 0, 0.5)"
                        }
                    }
                }
            ]
        };
        if (colorList.length !== 0) {
            option.color = colorList;
        }
        myChart.setOption(option);
        myChart.on("click", params => {
            if (onClicked) {
                onClicked(params);
            }
        });

        const handleResize = () => {
            myChart.resize();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
            myChart.dispose();
        };
    }, [dataForChart, totalValue]);
    return (
        <Flex justify="center" align="center">
            {seriesData.length === 0 && !totalValue ? (
                <div style={{display: "flex", justifyContent: "center", alignItems: "center"}}>
                    <Empty image={EmptyPic} description="No Data" imageStyle={{display: "block", margin: 0}} />
                </div>
            ) : (
                <div style={{height, width: "100%", maxWidth}} ref={chartRef} />
            )}
        </Flex>
    );
};
