import {Space, Modal, Button, Checkbox, Input, Tooltip, message, Spin, Tag, Form, Divider, Flex} from "antd";
import React, {useEffect, useState, useRef} from "react";
import {AmpConCustomTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";
import {getSnapshotList} from "@/modules-ampcon/apis/config_backup_api";
import {rollbackConfig} from "@/modules-ampcon/apis/rma_api";
import {convertConfigToStr} from "@/modules-ampcon/utils/util";
import {
    fetchSnapshotConfigAPI,
    setGoldenSnapshot,
    updateSnapshotDescription,
    delSnapshot,
    updateTag
} from "@/modules-ampcon/apis/template_api";

import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import ConfigBackupStyle from "./config_backup.module.scss";

const {TextArea} = Input;

const SnapshotListModal = ({sn, isModalOpen, onCancel}) => {
    const [selectedRow, setSelectedRow] = useState("");
    const [isSnapshotModalOpen, setIsSnapshotModalOpen] = useState(false);
    const [isRollbackModalOpen, setIsRollbackModalOpen] = useState(false);
    const [isTagModalOpen, setIsTagModalOpen] = useState(false);
    const tableRef = useRef(null);

    const searchFieldsList = ["snapshot_time", "config_type", "tag", "tag_content"];
    const matchFieldsList = [
        {name: "snapshot_time", matchMode: "fuzzy"},
        {name: "config_type", matchMode: "fuzzy"},
        {name: "tag", matchMode: "fuzzy"}
    ];

    const columns = [
        createColumnConfig("Snapshot Time", "snapshot_time", null, "18%"),
        createColumnConfig("Type", "config_type", null, "18%"),
        {
            ...createColumnConfig("Tags", "tag", null, "30%"),
            render: (tag, record) => {
                const tagList = composeTagList(tag, record.tag_content);
                return (
                    <Flex style={{flexWrap: "wrap", rowGap: "5px"}}>
                        {tagList.map(item => (
                            <Tag
                                key={item}
                                // color={item === "GOLDEN_CONFIG" ? "gold" : "grey"}
                                style={{
                                    // color: "#14C9BB",
                                    color: item === "GOLDEN_CONFIG" ? "gold" : "#14C9BB",
                                    backgroundColor: item === "GOLDEN_CONFIG" ? "#FFFBE7" : "rgba(20, 201, 187, 0.1)",
                                    // backgroundColor: "rgba(20, 201, 187, 0.1)",
                                    border: item === "GOLDEN_CONFIG" ? "1px solid gold" : "1px solid #14C9BB"
                                    // border: "1px solid #14C9BB",
                                    // margin: "4px"
                                }}
                            >
                                {item}
                            </Tag>
                        ))}
                    </Flex>
                );
            }
        },
        {
            title: "Operation",
            key: "operation",
            render: record => (
                <Space size="large" className={ConfigBackupStyle.actionLink}>
                    <a onClick={() => showModal("snapshot", record)}>Config</a>
                    <a onClick={() => handleSetGoldenConfig(record)}>Set Golden Config</a>
                    <a onClick={() => showModal("rollback", record)}>Rollback Config</a>
                    <a
                        onClick={() => {
                            confirmModalAction("Are you sure want to delete?", () => del_snapshot(record));
                        }}
                    >
                        Delete
                    </a>
                    <a onClick={() => showModal("tag", record)}>Tag Management</a>
                </Space>
            )
        }
    ];

    const showModal = (modal, record) => {
        handleModalOpen(modal);
        setSelectedRow(record);
    };

    const handleModalOpen = modalName => {
        switch (modalName) {
            case "snapshot":
                setIsSnapshotModalOpen(true);
                break;
            case "rollback":
                setIsRollbackModalOpen(true);
                break;
            case "tag":
                setIsTagModalOpen(true);
                break;
            default:
        }
    };

    const handleModalCancel = modalName => {
        switch (modalName) {
            case "snapshot":
                setIsSnapshotModalOpen(false);
                break;
            case "rollback":
                setIsRollbackModalOpen(false);
                break;
            case "tag":
                setIsTagModalOpen(false);
                break;
            default:
        }
        tableRef.current.refreshTable();
    };

    const handleSetGoldenConfig = async record => {
        await setGoldenSnapshot(sn, record.snapshot_time);
        tableRef.current.refreshTable();
    };

    const del_snapshot = async record => {
        await delSnapshot(sn, record.snapshot_time);
        tableRef.current.refreshTable();
    };

    return (
        <div style={{marginTop: "20px"}}>
            <Modal
                className="ampcon-max-modal"
                title={
                    <div>
                        Archive Configuration
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={isModalOpen}
                onCancel={onCancel}
                destroyOnClose
                footer={null}
            >
                <AmpConCustomTable
                    extraButton={[]}
                    columns={columns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    fetchAPIInfo={getSnapshotList}
                    fetchAPIParams={[sn]}
                    ref={tableRef}
                />
                <SnapshotModal
                    sn={sn}
                    snapshotTime={selectedRow === "" ? "" : selectedRow.snapshot_time}
                    tag={selectedRow === "" ? "None" : selectedRow.tag}
                    isModalOpen={isSnapshotModalOpen}
                    onCancel={() => handleModalCancel("snapshot")}
                />
                <RollbackModal
                    sn={sn}
                    snapshotId={selectedRow.id}
                    isModalOpen={isRollbackModalOpen}
                    onCancel={() => handleModalCancel("rollback")}
                />
                <TagManagementModal
                    snapshotId={selectedRow.id}
                    snapshotTime={selectedRow.snapshot_time}
                    snapshotType={selectedRow.config_type}
                    tags={composeTagList(selectedRow.tag, selectedRow.tag_content)}
                    isModalOpen={isTagModalOpen}
                    onCancel={() => handleModalCancel("tag")}
                />
            </Modal>
        </div>
    );
};

const SnapshotModal = ({sn, snapshotTime, tag, isModalOpen, onCancel}) => {
    const tipTitle = "Select to view config in SET command format (default is JSON format)";
    const [checked, setChecked] = useState(false);
    const [content, setContent] = useState("");
    const [form] = Form.useForm();

    const fetchSnapshotConfig = async () => {
        const config = await fetchSnapshotConfigAPI(sn, snapshotTime);
        setContent(config.snapshotConfig);
        const values = {
            description: config.description,
            tag: config.tag
        };
        form.setFieldsValue(values);
    };

    useEffect(() => {
        setChecked(false);
        if (snapshotTime && sn) {
            fetchSnapshotConfig().then(() => {});
        }
    }, [sn, snapshotTime, tag]);

    const handleCheckboxChange = async e => {
        const isChecked = e.target.checked;
        setChecked(isChecked);
        if (isChecked) {
            const newcontent = convertConfigToStr(content);
            setContent(newcontent);
        } else {
            fetchSnapshotConfig();
        }
    };

    const handleUpdateSnapshot = async values => {
        // eslint-disable-next-line no-console
        // console.log("Form values:", values);
        await updateSnapshotDescription(sn, snapshotTime, values.description, values.tag)
            .then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                }
            })
            .catch(() => {
                message.error("Update Config failed");
            });
    };

    return (
        <Modal
            title={
                <div>
                    {`${sn} Config ${snapshotTime}`}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            className="ampcon-middle-modal"
            open={isModalOpen}
            onCancel={onCancel}
            footer={null}
        >
            <Form form={form} layout="inline" onFinish={handleUpdateSnapshot}>
                <Form.Item label="Description:" name="description" rules={[{required: true}]}>
                    <Input style={{width: "160px"}} />
                </Form.Item>
                <Form.Item label="Tags:" name="tag" rules={[{required: true}]}>
                    <Input style={{width: "160px"}} disabled />
                </Form.Item>
                <Form.Item>
                    <Button
                        type="primary"
                        onClick={() => {
                            form.submit();
                            onCancel();
                        }}
                    >
                        Save
                    </Button>
                </Form.Item>
            </Form>
            <div style={{marginTop: 8}}>
                <Tooltip title={tipTitle} placement="topRight">
                    <Checkbox tooltip={tipTitle} checked={checked} onChange={handleCheckboxChange}>
                        Show SET-format
                    </Checkbox>
                </Tooltip>
                <TextArea
                    rows="18"
                    style={{
                        resize: "none",
                        border: "none",
                        background: "#F8FAFB",
                        borderRadius: "4px",
                        marginTop: "8px"
                    }}
                    readOnly
                    value={content}
                />
            </div>
        </Modal>
    );
};

const RollbackModal = ({sn, snapshotId, isModalOpen, onCancel}) => {
    const tipTitle =
        "This operation commits the backup configuration to the configuration file and starts two timers: commit confirm timer and commit wait timer to make sure all the configurations are correctly loaded. You have to wait for a period of “commit wait timer” when a commit command can be entered";
    const tipWaitTime =
        "The default timer value is 10 seconds, but a custom value can be entered from 10 to 240 seconds";
    const [form] = Form.useForm();
    const [spinning, setSpinning] = useState(false);

    const validateWaitTimeRange = (_, value) => {
        if (value >= 10 && value <= 240) {
            return Promise.resolve();
        }
        return Promise.reject(new Error("a custom value must be entered from 10 to 240 seconds"));
    };

    useEffect(() => {
        const values = {
            commit_wait_time: 10
        };
        form.setFieldsValue(values);
    }, []);

    const handleRollback = async values => {
        // eslint-disable-next-line no-console
        console.log("Form values:", values);
        setSpinning(true);
        await rollbackConfig(sn, snapshotId, values.commit_wait_time)
            .then(response => {
                setSpinning(false);
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                }
            })
            .catch(() => {
                setSpinning(false);
                message.error("Update Config failed");
            });
    };

    return (
        <>
            <Modal
                title={
                    <div>
                        {titleWithTip("Rollback Config", tipTitle)}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                className="ampcon-middle-modal"
                open={isModalOpen}
                // onOk={form.submit}
                // okText="Rollback"
                onCancel={onCancel}
                footer={
                    <Flex vertical>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Flex justify="flex-end">
                            <Button
                                type="primary"
                                onClick={() => {
                                    onCancel();
                                    form.submit();
                                }}
                            >
                                Apply
                            </Button>
                        </Flex>
                    </Flex>
                }
            >
                <Form
                    form={form}
                    layout="horizontal"
                    onFinish={handleRollback}
                    labelCol={{span: 8}}
                    labelAlign="left"
                    style={{minHeight: "268px"}}
                >
                    <Form.Item
                        label="Commit Wait Time:"
                        name="commit_wait_time"
                        tooltip={tipWaitTime}
                        rules={[{required: true}, {validator: validateWaitTimeRange}]}
                    >
                        <Input style={{width: "280px"}} />
                    </Form.Item>
                </Form>
            </Modal>
            <Spin spinning={spinning} tip="Loading..." size="large" fullscreen />
        </>
    );
};

const TagManagementModal = ({snapshotTime, snapshotId, snapshotType, tags, isModalOpen, onCancel}) => {
    const [form] = Form.useForm();
    const [taglist, setTaglist] = useState([]);

    const handleUpdateTag = async () => {
        const tags = form.getFieldValue("tags");
        const tag_content = tags.filter(item => item !== "GOLDEN_CONFIG").join(",");
        await updateTag(snapshotId, "snapshot", tag_content)
            .then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                }
            })
            .catch(() => {
                message.error("Update Config tag failed");
            });
    };

    const handleTagClose = tag => {
        const tags = form.getFieldValue("tags");
        const updatedTags = tags.filter(t => t !== tag);
        form.setFieldsValue({tags: updatedTags});
        setTaglist(updatedTags);
    };

    const handleAddTag = () => {
        form.validateFields().then(values => {
            const {tagName} = values;
            if (tagName === "GOLDEN_CONFIG") {
                form.setFields([
                    {
                        name: "tagName",
                        errors: ['Tag name cannot be "GOLDEN_CONFIG".']
                    }
                ]);
            } else if (taglist.includes(tagName)) {
                form.setFields([
                    {
                        name: "tagName",
                        errors: ["Tag name already exists."]
                    }
                ]);
            } else if (tagName === undefined || tagName.trim().length === 0) {
                form.setFields([
                    {
                        name: "tagName",
                        errors: ["Please input a tag."]
                    }
                ]);
            } else if (tagName.trim().length > 24) {
                form.setFields([
                    {
                        name: "tagName",
                        errors: ["Tag Name over 24 character limit"]
                    }
                ]);
            } else {
                const updatedTaglist = [...taglist, tagName];
                setTaglist(updatedTaglist);
                form.setFieldsValue({
                    tags: updatedTaglist
                });
                // form.resetFields();
                form.resetFields(["tagName"]);
            }
        });
    };

    useEffect(() => {
        const values = {
            snapshotTime,
            snapshotType,
            tags
        };
        form.setFieldsValue(values);
        setTaglist(tags);
    }, [snapshotId, tags]);

    return (
        <Modal
            title={
                <div>
                    Tag Management
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            className="ampcon-middle-modal"
            open={isModalOpen}
            // onOk={form.submit}
            // okText="Save"
            onCancel={onCancel}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            type="primary"
                            onClick={async () => {
                                await handleUpdateTag();
                                onCancel();
                            }}
                        >
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form
                form={form}
                layout="horizontal"
                labelCol={{span: 5}}
                wrapperCol={{span: 16}}
                labelAlign="left"
                style={{minHeight: "268px"}}
            >
                <Form.Item label="Snapshot Time:" name="snapshotTime">
                    <Input disabled style={{color: "#212529", width: "280px"}} />
                </Form.Item>
                <Form.Item label="Snapshot Type:" name="snapshotType">
                    <Input disabled style={{color: "#212529", width: "280px"}} />
                </Form.Item>
                <Form.Item label="Template Tag:" name="tags">
                    <div style={{border: "1px solid #ccc", minHeight: "50px", width: "280px"}}>
                        {taglist && taglist.length > 0 && (
                            <>
                                {taglist.map(tag => (
                                    <Tag
                                        key={tag}
                                        color={tag === "GOLDEN_CONFIG" ? "gold" : "#14c9bb1A"}
                                        closable={tag !== "GOLDEN_CONFIG"}
                                        onClose={tag !== "GOLDEN_CONFIG" ? () => handleTagClose(tag) : undefined}
                                        style={{
                                            // color: "#14C9BB",
                                            color: tag === "GOLDEN_CONFIG" ? "gold" : "#14C9BB",
                                            backgroundColor:
                                                tag === "GOLDEN_CONFIG" ? "#FFFBE7" : "rgba(20, 201, 187, 0.1)",
                                            // backgroundColor: "rgba(20, 201, 187, 0.1)",
                                            border: tag === "GOLDEN_CONFIG" ? "1px solid gold" : "1px solid #14C9BB",
                                            // border: "1px solid #14C9BB",
                                            margin: "4px"
                                        }}
                                    >
                                        {tag}
                                    </Tag>
                                ))}
                            </>
                        )}
                    </div>
                </Form.Item>
                <Form.Item label="Tag Name:" name="tagName">
                    <Flex>
                        <Input style={{width: "280px", marginRight: "16px"}} />
                        <Button htmlType="button" onClick={handleAddTag}>
                            Add
                        </Button>
                    </Flex>
                </Form.Item>
            </Form>
        </Modal>
    );
};

const titleWithTip = (title, tips) => {
    return (
        <div>
            <Tooltip title={tips} placement="topRight">
                <span>{title}</span>
                {/* <QuestionCircleOutlined style={{ marginLeft: 5, color: '#1890ff' }} /> */}
            </Tooltip>
        </div>
    );
};

function composeTagList(tag, tagContent) {
    const tagList = [];
    if (tag !== "None") {
        tagList.push(tag);
    }
    if (tagContent) {
        const newTagList = tagContent.split(",").map(item => item.trim());
        tagList.push(...newTagList);
    }
    return tagList;
}

export {SnapshotListModal};
