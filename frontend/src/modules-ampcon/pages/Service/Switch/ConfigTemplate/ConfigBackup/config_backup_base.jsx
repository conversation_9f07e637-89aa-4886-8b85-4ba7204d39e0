import {Space, Modal, Flex, Checkbox, Input, Tooltip, message, Spin, Upload, Divider, Form, Select, Button} from "antd";
import React, {useRef, useEffect, useState} from "react";
import LogViewTextareaModal from "@/modules-ampcon/components/log_view_textarea_modal";

import {createColumnConfig, TableFilterDropdown, AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {ViewExportModal} from "@/modules-ampcon/components/custom_modal";
import {backupSNConfig, backupGroupConfig, collectBackupConfig} from "@/modules-ampcon/apis/config_backup_api";
import {queryConfig, uploadConfig} from "@/modules-ampcon/apis/rma_api";
import {SnapshotListModal} from "@/modules-ampcon/pages/Service/Switch/ConfigTemplate/ConfigBackup/config_snapshot_list";
import {viewReport, loadGroup} from "@/modules-ampcon/apis/lifecycle_api";
import {LoadGroupForm, ViewReportForm} from "@/modules-ampcon/components/custom_form";
import {convertConfigToStr} from "@/modules-ampcon/utils/util";
import {onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import ConfigBackupStyle from "./config_backup.module.scss";

const {TextArea} = Input;
const {Option} = Select;

const BackupConfigButton = ({groups, reportTime, onSelectGroup, onChangeBackupInterval}) => {
    const [selectedReportTime, setSelectedReportTime] = useState();
    const [isViewReportModalOpen, setIsViewReportModalOpen] = useState(false);
    const [selectedTime, setSelectedTime] = useState(0);
    const hours = Array.from({length: 24}, (_, index) => index.toString().padStart(2, "0"));

    const handleBackupConfig = values => {
        backupGroupConfig(values.groupName).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
            }
        });
    };

    const handleViewReport = values => {
        setIsViewReportModalOpen(true);
        setSelectedReportTime(values.reportTime);
    };

    const handleCollectBackupConfig = async values => {
        // eslint-disable-next-line no-console
        collectBackupConfig(values.days, values.time).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                onChangeBackupInterval({
                    day: values.days,
                    hour: values.time
                });
                message.success(response.info);
            }
        });
    };

    return (
        <div>
            <Flex gap="large" style={{justifyContent: "flex-start"}}>
                <Form
                    layout="inline"
                    onFinish={handleCollectBackupConfig}
                    style={{
                        display: "flex",
                        justifyContent: "flex-start",
                        // marginTop: 24,
                        alignItems: "flex-start",
                        flexWrap: "nowrap"
                    }}
                >
                    <Form.Item
                        label="Interval Days"
                        name="days"
                        wrapperCol={{style: {width: 180}}}
                        rules={[
                            {required: true, message: "Please enter interval day!"},
                            {
                                validator: (_, value) => {
                                    if (value && /^-?\d+$/.test(value) && value !== "0") {
                                        return Promise.resolve();
                                    }
                                    return Promise.reject(new Error("Interval days must be a non-zero integer"));
                                }
                            }
                        ]}
                    >
                        <Input placeholder="Input day" style={{height: "32px"}} />
                    </Form.Item>
                    <Form.Item
                        label="Hours"
                        name="time"
                        rules={[{required: true, message: "Please select interval time!"}]}
                        wrapperCol={{style: {width: 180}}}
                        style={{marginRight: "6px"}}
                    >
                        <Select
                            value={selectedTime}
                            style={{height: "32px"}}
                            onChange={value => {
                                setSelectedTime(value);
                            }}
                            placeholder="Select time"
                            showSearch
                            optionFilterProp="children"
                        >
                            {hours.map(num => (
                                <Option key={num} value={num}>
                                    {num}
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item>
                        <Button type="primary" htmlType="submit" style={{width: 80}}>
                            Save
                        </Button>
                    </Form.Item>
                </Form>
                <LoadGroupForm
                    groups={groups}
                    buttonText="Backup Config"
                    onSelectChange={onSelectGroup}
                    onFinish={handleBackupConfig}
                />
                <ViewReportForm reportTime={reportTime} onFinish={handleViewReport} />
                <ViewExportModal
                    isModalOpen={isViewReportModalOpen}
                    onCancel={() => setIsViewReportModalOpen(false)}
                    fetchDataAPI={viewReport}
                    fetchAPIParams={[selectedReportTime]}
                    modalClass="ampcon-middle-modal"
                />
            </Flex>
        </div>
    );
};

const ConfigBackupTable = ({groups, reportTime, onChangeBackupInterval}) => {
    const [selectedRow, setSelectedRow] = useState("");
    const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
    const [isSnapshotModalOpen, setIsSnapshotModalOpen] = useState(false);
    const [spinning, setSpinning] = useState(false);
    const [selectedGroup, setSelectedGroup] = useState("");
    const LogViewTextareaModalRef = useRef(null);

    const matchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "host_name", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "version", matchMode: "fuzzy"},
        {name: "flag", matchMode: "fuzzy"},
        {name: "backup_time", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["sn", "host_name", "mgt_ip", "version", "flag", "backup_time"];

    const columns = [
        createColumnConfig("Host Name", "host_name", TableFilterDropdown),
        {
            ...createColumnConfig("IP address", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            }
        },
        createColumnConfig("Switch SN", "sn", TableFilterDropdown),
        createColumnConfig("Version", "version"),
        createColumnConfig("Flag", "flag"),
        createColumnConfig("Last Backup Time", "backup_time", TableFilterDropdown, "", "", "descend"),
        {
            title: "Operation",
            key: "operation",
            render: record => (
                <Space size="large" className={ConfigBackupStyle.actionLink}>
                    <a onClick={() => showModal("config", record)}>Config</a>
                    <a onClick={() => backupConfig(record)}>Backup Config</a>
                    <ConfigUpload record={record} />
                    <a onClick={() => showModal("snapshot", record)}>Archive List</a>
                    <a
                        onClick={() => {
                            LogViewTextareaModalRef.current.showLogViewTextareaModal(record.sn);
                        }}
                    >
                        Device Log
                    </a>
                </Space>
            )
        }
    ];

    const showModal = (modal, record) => {
        handleModalOpen(modal);
        setSelectedRow(record);
    };

    const backupConfig = async record => {
        setSpinning(true);
        backupSNConfig(record.sn, record.mgt_ip)
            .then(response => {
                setSpinning(false);
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                }
            })
            .catch(() => {
                setSpinning(false);
                message.error("Backup failed");
            });
    };

    const handleModalOpen = modalName => {
        switch (modalName) {
            case "config":
                setIsConfigModalOpen(true);
                break;
            case "snapshot":
                setIsSnapshotModalOpen(true);
                break;
            default:
        }
    };

    const handleModalCancel = modalName => {
        switch (modalName) {
            case "config":
                setIsConfigModalOpen(false);
                break;
            case "snapshot":
                setIsSnapshotModalOpen(false);
                break;
            default:
        }
    };

    const handleSelectedGroupChange = value => {
        setSelectedGroup(value);
    };

    return (
        <div style={{flex: 1, marginTop: -5}}>
            <AmpConCustomTable
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <BackupConfigButton
                        groups={groups}
                        reportTime={reportTime}
                        onSelectGroup={handleSelectedGroupChange}
                        onChangeBackupInterval={value => {
                            onChangeBackupInterval(value);
                        }}
                    />
                }
                fetchAPIInfo={loadGroup}
                fetchAPIParams={[selectedGroup]}
            />
            <Spin spinning={spinning} tip="Loading..." size="large" fullscreen />
            <ConfigModal
                ip={selectedRow.mgt_ip}
                isModalOpen={isConfigModalOpen}
                onCancel={() => handleModalCancel("config")}
            />
            <LogViewTextareaModal ref={LogViewTextareaModalRef} />
            <SnapshotListModal
                sn={selectedRow.sn}
                isModalOpen={isSnapshotModalOpen}
                onCancel={() => handleModalCancel("snapshot")}
            />
        </div>
    );
};

const ConfigModal = ({ip, isModalOpen, onCancel}) => {
    const tipTitle = "Select to view config in SET command format (default is JSON format)";
    const [checked, setChecked] = useState(false);
    const [content, setContent] = useState("");

    const fetchConfig = async () => {
        if (ip === undefined || ip === null) return;
        const config = await queryConfig(ip);
        setContent(config);
    };

    useEffect(() => {
        setChecked(false);
        fetchConfig().then(() => {});
    }, [ip]);

    const handleCheckboxChange = async e => {
        const isChecked = e.target.checked;
        setChecked(isChecked);
        if (isChecked) {
            const newcontent = convertConfigToStr(content);
            setContent(newcontent);
        } else {
            fetchConfig();
        }
    };

    return (
        <Modal
            title={
                <div>
                    {`${ip} Configuration`}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            className="ampcon-middle-modal"
            open={isModalOpen}
            onCancel={onCancel}
            footer={null}
        >
            <Tooltip title={tipTitle} placement="topRight">
                <Checkbox
                    tooltip={tipTitle}
                    checked={checked}
                    onChange={handleCheckboxChange}
                    style={{marginBottom: "16px"}}
                >
                    Show SET-format
                </Checkbox>
            </Tooltip>
            <TextArea
                rows="15"
                style={{resize: "none", border: "none", background: "#F8FAFB", borderRadius: "4px"}}
                readOnly
                value={content}
            />
        </Modal>
    );
};

const ConfigUpload = ({record}) => {
    const handleFileChange = async file => {
        await uploadConfig(file, record.sn, record.mgt_ip)
            .then(response => {
                if (response.status === 200) {
                    message.success(response.info);
                } else {
                    message.error(response.info);
                }
            })
            .catch(error => {
                message.error(error);
            });
    };

    const customRequest = async ({file}) => {
        await handleFileChange(file);
    };

    return (
        <Upload customRequest={customRequest} showUploadList={false}>
            <a href="#">Upload Config</a>
        </Upload>
    );
};

export {ConfigBackupTable};
