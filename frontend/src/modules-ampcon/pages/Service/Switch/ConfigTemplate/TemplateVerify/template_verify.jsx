import TextContentDiffComponent from "@/modules-ampcon/components/text_content_diff";
import {Col, Row, Form, Input, Select, Button, Space, Tooltip, message, Spin} from "antd";
import {useRef, useState} from "react";
import {
    AmpConCustomModalTable,
    createColumnConfig,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import GenerateConfigWithGlobalConfigAndTemplateModal from "@/modules-ampcon/pages/Service/Switch/ConfigTemplate/TemplateVerify/generate_config_with_global_config_and_template_modal";

import {
    fetchSnapshotListAPI,
    fetchRunningConfigAPI,
    fetchSwitchInfo,
    generateConfigTemplateVerify
} from "@/modules-ampcon/apis/template_api";
import styles from "@/modules-ampcon/pages/System/user_management.module.scss";
import {QuestionCircleOutlined} from "@ant-design/icons";
import {onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";

const TemplateVerify = () => {
    const searchFieldsList = ["sn", "host_name", "mgt_ip", "platform_model"];

    const matchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "host_name", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"}
    ];

    const [originalContent, setOriginalContent] = useState("");
    const [modifiedContent, setModifiedContent] = useState("");

    const [selectModalOpen, setSelectModalOpen] = useState(false);
    const [title, setTitle] = useState("");
    const [leftSn, setLeftSn] = useState();
    const [rightTemplateName, setRightTemplateName] = useState();
    const [leftConfigName, setLeftConfigName] = useState();

    const [isShowSpin, setIsShowSpin] = useState(false);

    const generateConfigWithGlobalConfigAndTemplateModalRef = useRef(null);

    const onCancel = () => {
        setSelectModalOpen(false);
    };

    const selectChangeOriginal = async value => {
        try {
            if (value === "running_config") {
                setIsShowSpin(true);
                await fetchRunningConfigAPI(leftSn, "set").then(response => {
                    if (response.status !== 200) {
                        message.error(response.info);
                    } else {
                        setModifiedContent(response.data);
                    }
                });
                setIsShowSpin(false);
            } else {
                setIsShowSpin(true);
                await fetchRunningConfigAPI(leftSn, "all_set").then(response => {
                    if (response.status !== 200) {
                        message.error(response.info);
                    } else {
                        setModifiedContent(response.data);
                    }
                });
                setIsShowSpin(false);
            }
        } catch (e) {
            message.error("An error occurred during the process of obtaining config");
        } finally {
            setIsShowSpin(false);
        }
    };

    const generateCallback = (globalConfigName, siteTemplateName, content) => {
        setRightTemplateName(siteTemplateName);
        generateConfigTemplateVerify(leftSn, globalConfigName, siteTemplateName, content).then(data => {
            if (data.status !== 200) {
                message.error(data.info);
            } else {
                setOriginalContent(data.data);
            }
        });
    };

    const columns = [
        createColumnConfig("Switch SN", "sn", TableFilterDropdown),
        createColumnConfig("Sysname", "host_name", TableFilterDropdown),
        {
            ...createColumnConfig("Mgt IP", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            }
        },
        createColumnConfig("Model", "platform_model", TableFilterDropdown)
        // {
        //     title: "Operation",
        //     render: (_, record) => (
        //         <Space size="middle" className={styles.actionLink}>
        //             <a
        //                 onClick={() => {
        //                     fetchSnapshotListAPI(record.sn).then(() => {
        //                         setLeftSn(record.sn);
        //                         setLeftConfigName([
        //                             {
        //                                 label: <span>Running Config</span>,
        //                                 options: [
        //                                     {label: <span>Running Config(set format)</span>, value: "running_config"},
        //                                     {
        //                                         label: <span>Running Config(all set format)</span>,
        //                                         value: "all_running_config"
        //                                     }
        //                                 ]
        //                             }
        //                         ]);
        //                     });
        //                     onCancel();
        //                 }}
        //             >
        //                 {leftSn === record.sn && title === "Select the switch A" ? "Selected" : "Select"}
        //             </a>
        //         </Space>
        //     )
        // }
    ];

    return (
        <div>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            <AmpConCustomModalTable
                title={title}
                rowSelection={{
                    type: "radio",
                    selectedRowKeys: [leftSn], // 控制高亮行
                    onChange: async (_, selectedRows) => {
                        const record = selectedRows[0];
                        if (!record) return;

                        await fetchSnapshotListAPI(record.sn);
                        setLeftSn(record.sn);
                        setLeftConfigName([
                            {
                                label: <span>Running Config</span>,
                                options: [
                                    {label: <span>Running Config(set format)</span>, value: "running_config"},
                                    {label: <span>Running Config(all set format)</span>, value: "all_running_config"}
                                ]
                            }
                        ]);
                        onCancel();
                    }
                }}
                selectModalOpen={selectModalOpen}
                onCancel={onCancel}
                columns={columns}
                matchFieldsList={matchFieldsList}
                searchFieldsList={searchFieldsList}
                buttonProps={[]}
                fetchAPIInfo={fetchSwitchInfo}
                modalClass="ampcon-max-modal"
            />
            <GenerateConfigWithGlobalConfigAndTemplateModal
                generateCallback={generateCallback}
                ref={generateConfigWithGlobalConfigAndTemplateModalRef}
            />
            <Row>
                <Col span={8} style={{flex: "none", marginRight: "32px"}}>
                    <Form style={{display: "flex", flexWrap: "nowrap"}}>
                        <Form.Item
                            label={
                                <div>
                                    SN
                                    <Tooltip
                                        title="Select the Switch for which sample config is to be generated"
                                        placement="right"
                                    >
                                        <QuestionCircleOutlined
                                            className="questioncircle-color"
                                            style={{paddingLeft: 4, marginRight: 22}}
                                        />
                                    </Tooltip>
                                </div>
                            }
                        >
                            <Input
                                disabled
                                value={leftSn}
                                style={{borderTopRightRadius: 0, borderBottomRightRadius: 0, color: "#212529"}}
                            />
                        </Form.Item>
                        <Form.Item>
                            <Button
                                style={{borderTopLeftRadius: 0, borderBottomLeftRadius: 0}}
                                // type="primary"
                                onClick={() => {
                                    setTitle("Select the switch A");
                                    setSelectModalOpen(true);
                                }}
                            >
                                Select
                            </Button>
                        </Form.Item>
                    </Form>
                </Col>
                <Col span={8} style={{flex: "none", marginRight: "32px"}}>
                    <Form style={{display: "flex", flexWrap: "nowrap"}}>
                        <Form.Item
                            label={
                                <div>
                                    Template
                                    <Tooltip title="Select the template which is being verified" placement="right">
                                        <QuestionCircleOutlined
                                            className="questioncircle-color"
                                            style={{paddingLeft: 4, marginRight: 22}}
                                        />
                                    </Tooltip>
                                </div>
                            }
                        >
                            <Input
                                disabled
                                value={rightTemplateName}
                                style={{borderTopRightRadius: 0, borderBottomRightRadius: 0, color: "#212529"}}
                            />
                        </Form.Item>
                        <Form.Item>
                            <Button
                                style={{borderTopLeftRadius: 0, borderBottomLeftRadius: 0}}
                                // type="primary"
                                onClick={() => {
                                    if (leftSn === undefined) {
                                        message.error("Please select the switch first");
                                        return;
                                    }
                                    generateConfigWithGlobalConfigAndTemplateModalRef.current.showGenerateConfigWithGlobalConfigAndTemplateModal();
                                }}
                            >
                                Select
                            </Button>
                        </Form.Item>
                    </Form>
                </Col>
                <Col span={9} style={{flex: "none"}}>
                    <Form>
                        <Form.Item
                            label={
                                <div>
                                    Running Config
                                    <Tooltip
                                        title="Select the running config to compare with the config generated by the template"
                                        placement="right"
                                    >
                                        <QuestionCircleOutlined
                                            className="questioncircle-color"
                                            style={{paddingLeft: 4, marginRight: 22}}
                                        />
                                    </Tooltip>
                                </div>
                            }
                        >
                            <Space size={8}>
                                <Select options={leftConfigName} style={{width: 260}} onChange={selectChangeOriginal} />
                            </Space>
                        </Form.Item>
                    </Form>
                </Col>
            </Row>
            <Row style={{minWidth: "1180px"}}>
                <Col span={24}>
                    <TextContentDiffComponent text1={originalContent} text2={modifiedContent} />
                </Col>
            </Row>
        </div>
    );
};
export default TemplateVerify;
