import {Button, Dropdown, Spin, Form, Input, Select, message} from "antd";
import {DownOutlined, UpOutlined, PlusOutlined, MinusOutlined} from "@ant-design/icons";
import React, {useState, useEffect} from "react";
import {formValidateRules} from "@/modules-ampcon/utils/util";
import {AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {getSystemConfigInfo} from "@/modules-ampcon/apis/config_api";
import {adoptSwitch, importVPNSwitch} from "@/modules-ampcon/apis/rma_api";
import {useSelector} from "react-redux";
import {getUserGroup} from "@/modules-ampcon/apis/inventory_api";
import {getFabric, getSite} from "@/modules-ampcon/apis/lifecycle_api";

const {Option} = Select;

const ImportDropdownAction = ({tableRef}) => {
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const [isAdoptModalOpen, setIsAdoptModalOpen] = useState(false);
    const [hoverStatus, setHoverStatus] = useState(false);
    const currentUser = useSelector(state => state.user.userInfo);
    const [groupList, setGroupList] = useState([]);

    const dropdownMenu = [
        {
            label: "Import",
            onClick: () => {
                setIsImportModalOpen(true);
            }
        },
        {
            label: "Adopt",
            onClick: () => {
                setIsAdoptModalOpen(true);
            }
        },
        {
            label: "Download VPN Script",
            onClick: () => {
                handleDownload();
            }
        }
    ];

    const handleDownload = () => {
        const url = "/ampcon/rma/file/agent/enable_switch_vpn.sh";
        const link = document.createElement("a");
        link.href = url;
        link.download = "enable_switch_vpn.sh";
        link.click();
    };

    useEffect(() => {
        if (isGroupUser()) {
            getUserGroup().then(response => {
                setGroupList(response.data.filter(group => group.group_type === "switch"));
            });
        }
    }, [currentUser]);

    const isGroupUser = () => {
        return currentUser.userType === "group";
    };

    return (
        <div>
            <Dropdown menu={{items: dropdownMenu}} trigger={["hover"]} onOpenChange={val => setHoverStatus(val)}>
                <Button type="primary" style={{width: "150px"}}>
                    <>
                        Import Actions
                        {hoverStatus ? (
                            <UpOutlined style={{fontSize: "12px", marginLeft: "8px"}} />
                        ) : (
                            <DownOutlined style={{fontSize: "12px", marginLeft: "8px"}} />
                        )}
                    </>
                </Button>
            </Dropdown>
            <ImportModal
                isModalOpen={isImportModalOpen}
                onCancel={() => setIsImportModalOpen(false)}
                onSubmitSuccess={() => tableRef.current.refreshTable()}
                groupList={groupList}
            />
            <AdoptModal
                isModalOpen={isAdoptModalOpen}
                onCancel={() => setIsAdoptModalOpen(false)}
                onSubmitSuccess={() => tableRef.current.refreshTable()}
                groupList={groupList}
            />
        </div>
    );
};

const ImportModal = ({isModalOpen, onCancel, onSubmitSuccess, groupList}) => {
    const fabricSelectTooltip =
        "Select the fabric configuration for the switch. This determines the network topology and connectivity.";
    const siteSelectTooltip =
        "Select the site where the switch will be deployed. This helps in organizing and managing the network devices.";

    const [form] = Form.useForm();
    const [configOptions, setConfigOption] = useState([]);
    const [spinning, setSpinning] = useState(false);
    const currentUser = useSelector(state => state.user.userInfo);

    const [additionalGroupObject, setAdditionalGroupObject] = useState({});
    const [groupListIndex, setGroupListIndex] = useState(0);
    const [rootGroupObject, setRootGroupObject] = useState({
        valid: true
    });

    const [fabricList, setFabricList] = useState([]);
    const [siteList, setSiteList] = useState([]);
    const [selectedFabric, setSelectedFabric] = useState("default");
    const [selectedSite, setSelectedSite] = useState("default");

    const handleGroupChangeCallback = (selectedItem, siteGroupKey) => {
        const formValues = form.getFieldsValue();
        formValues[siteGroupKey] = selectedItem.children;
        form.setFieldsValue(formValues);
        const newAdditionalGroupObject = JSON.parse(JSON.stringify(additionalGroupObject));
        groupValueCheck(formValues, newAdditionalGroupObject);
    };

    const handleRemoveGroupCallback = (selectedItem, groupKey) => {
        const newAdditionalGroupObject = JSON.parse(JSON.stringify(additionalGroupObject));
        delete newAdditionalGroupObject[groupKey];
        const formValues = form.getFieldsValue();
        delete formValues[groupKey];
        form.setFieldsValue(formValues);
        groupValueCheck(formValues, newAdditionalGroupObject);
    };

    const groupValueCheck = (formValues, newAdditionalGroupObject) => {
        const keysWithValuesMoreThanTwice = getGroupKeysWithValuesMoreThanTwice(formValues);
        // set all keys to valid
        setRootGroupObject({valid: true});
        Object.keys(newAdditionalGroupObject).forEach(key => {
            newAdditionalGroupObject[key].valid = true;
        });
        keysWithValuesMoreThanTwice.forEach(key => {
            if (!key.startsWith("group")) {
                return;
            }
            if (key === "group0") {
                setRootGroupObject({valid: false});
            } else {
                newAdditionalGroupObject[key].valid = false;
            }
        });
        setAdditionalGroupObject(newAdditionalGroupObject);
    };

    const getGroupKeysWithValuesMoreThanTwice = formValues => {
        const filteredFormValues = Object.keys(formValues)
            .filter(key => key.startsWith("group"))
            .reduce((result, key) => {
                result[key] = formValues[key];
                return result;
            }, {});
        const valueCounts = Object.values(filteredFormValues).reduce((counts, value) => {
            counts[value] = (counts[value] || 0) + 1;
            return counts;
        }, {});
        const valuesMoreThanTwice = Object.keys(valueCounts).filter(value => valueCounts[value] >= 2);
        return Object.keys(formValues).filter(key => valuesMoreThanTwice.includes(formValues[key]));
    };

    const formItems = () => {
        return (
            <>
                <Form.Item
                    name="ip"
                    label="IP"
                    rules={[{required: true, message: "Please input your IP!"}, formValidateRules.ipv4()]}
                >
                    <Input placeholder="IP" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="systemConfig"
                    label="System Config"
                    rules={[{required: true, message: "Please select your config!"}]}
                >
                    <Select placeholder="Select a config" style={{width: "280px"}}>
                        {configOptions.map(item => (
                            <Option key={item} value={item}>
                                {item}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC" && (
                    <Form.Item
                        name="fabric"
                        label="Fabric"
                        rules={[
                            {
                                validator: () => {
                                    return Promise.resolve(); // 始终通过
                                }
                            }
                        ]}
                        required
                    >
                        <Select
                            style={{width: "280px"}}
                            placeholder="Please select a fabirc"
                            defaultValue={selectedFabric}
                            onChange={value => {
                                setSelectedFabric(value);
                            }}
                        >
                            {fabricList.map(fabric => {
                                return (
                                    <Option key={fabric} value={fabric}>
                                        {fabric}
                                    </Option>
                                );
                            })}
                        </Select>
                    </Form.Item>
                )}
                {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS" && (
                    <Form.Item
                        name="site"
                        label="Site"
                        rules={[
                            {
                                validator: () => {
                                    return Promise.resolve(); // 始终通过
                                }
                            }
                        ]}
                        required
                    >
                        <Select
                            style={{width: "280px"}}
                            placeholder="Please select a site"
                            defaultValue={selectedSite}
                            onChange={value => {
                                setSelectedSite(value);
                            }}
                        >
                            {siteList.map(site => {
                                return (
                                    <Option key={site} value={site}>
                                        {site}
                                    </Option>
                                );
                            })}
                        </Select>
                    </Form.Item>
                )}

                {currentUser.userType === "group" ? (
                    <>
                        {groupList.length !== 0 ? (
                            <Form.Item
                                name="group0"
                                label="Select Group"
                                rules={[{required: true, message: "Please select a group!"}]}
                                validateStatus={rootGroupObject.valid ? "success" : "error"}
                                help={rootGroupObject.valid ? "" : "The selected group cannot be selected."}
                                validateTrigger={["onChange", "onBlur"]}
                            >
                                <Select
                                    style={{width: "280px"}}
                                    placeholder="Please select group"
                                    onChange={(value, selectedItem) => {
                                        handleGroupChangeCallback(selectedItem, "group0");
                                    }}
                                >
                                    {groupList.map(group => {
                                        return (
                                            <Option key={group.id} value={group.name}>
                                                {group.name}
                                            </Option>
                                        );
                                    })}
                                </Select>
                                <Button
                                    style={{
                                        backgroundColor: "transparent",
                                        color: "#BFBFBF"
                                    }}
                                    type="link"
                                    onClick={() => {
                                        const newAdditionalGroupObject = JSON.parse(
                                            JSON.stringify(additionalGroupObject)
                                        );
                                        newAdditionalGroupObject[`group${groupListIndex + 1}`] = {
                                            valid: true
                                        };
                                        setGroupListIndex(groupListIndex + 1);
                                        setAdditionalGroupObject(newAdditionalGroupObject);
                                    }}
                                    icon={<PlusOutlined />}
                                />
                            </Form.Item>
                        ) : (
                            <Form.Item
                                name="group0"
                                label="Select Group"
                                rules={[{required: true, message: "Please select a group!"}]}
                            >
                                <Select style={{width: "280px"}} placeholder="Please select group" />
                            </Form.Item>
                        )}
                        {Object.entries(additionalGroupObject).map(([groupKey, groupValue]) => {
                            return (
                                <Form.Item
                                    name={groupKey}
                                    label="Select Group"
                                    validateStatus={groupValue.valid ? "success" : "error"}
                                    help={groupValue.valid ? "" : "The selected group cannot be selected."}
                                    validateTrigger={["onChange", "onBlur"]}
                                >
                                    <Select
                                        style={{width: "280px"}}
                                        placeholder="Please select a group"
                                        onChange={(value, selectedItem) => {
                                            handleGroupChangeCallback(selectedItem, groupKey);
                                        }}
                                    >
                                        {groupList.map(group => {
                                            return (
                                                <Option key={group.id} value={group.id}>
                                                    {group.name}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                    <Button
                                        style={{
                                            backgroundColor: "transparent",
                                            color: "black"
                                        }}
                                        type="minus"
                                        onClick={(value, selectedItem) => {
                                            handleRemoveGroupCallback(selectedItem, groupKey);
                                        }}
                                        icon={<MinusOutlined />}
                                    />
                                </Form.Item>
                            );
                        })}
                    </>
                ) : null}
            </>
        );
    };

    const onSubmit = async () => {
        const values = form.getFieldValue();
        const groupList = Object.values(values).filter((_, index) => {
            const key = Object.keys(values)[index];
            return key.startsWith("group");
        });
        setSpinning(true);
        onCancel();
        form.resetFields();
        setSelectedSite("default");
        setSelectedFabric("default");
        importVPNSwitch(values.ip, values.systemConfig, groupList, selectedFabric, selectedSite)
            .then(response => {
                setSpinning(false);
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                    onSubmitSuccess();
                }
            })
            .catch(() => {
                setSpinning(false);
                message.error("Import Switch failed");
            });
    };

    useEffect(() => {
        fetchFabricOrSiteSelectData().then(() => {});

        if (currentUser.type !== "readonly") {
            getSystemConfigInfo("Global").then(response => {
                if (response.status === 200) {
                    setConfigOption(response.data.allSystemConfigName);
                }
            });
        }
        setAdditionalGroupObject({});
        setGroupListIndex(0);
    }, []);

    const onCancelFunc = () => {
        setAdditionalGroupObject({});
        setRootGroupObject({valid: true});
        setGroupListIndex(0);
        onCancel();
        form.resetFields();
        setSelectedSite("default");
        setSelectedFabric("default");
    };

    const fetchFabricOrSiteSelectData = async () => {
        if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS") {
            await getSite().then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    setFabricList(["default"]);
                    setSiteList(response.data);
                }
            });
        } else if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC") {
            await getFabric().then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    setFabricList(response.data);
                    setSiteList(["default"]);
                }
            });
        }
    };

    return (
        <div>
            <AmpConCustomModalForm
                title="Import switch and build VPN connection"
                isModalOpen={isModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 6
                    }
                }}
                CustomFormItems={formItems}
                onCancel={onCancelFunc}
                onSubmit={onSubmit}
                modalClass="ampcon-middle-modal"
            />
            <Spin spinning={spinning} tip="Loading..." size="large" fullscreen />
        </div>
    );
};

const AdoptModal = ({isModalOpen, onCancel, onSubmitSuccess, groupList}) => {
    const [form] = Form.useForm();
    const currentUser = useSelector(state => state.user.userInfo);

    const [additionalGroupObject, setAdditionalGroupObject] = useState({});
    const [groupListIndex, setGroupListIndex] = useState(0);
    const [rootGroupObject, setRootGroupObject] = useState({
        valid: true
    });

    const handleGroupChangeCallback = (selectedItem, siteGroupKey) => {
        const formValues = form.getFieldsValue();
        formValues[siteGroupKey] = selectedItem.children;
        form.setFieldsValue(formValues);
        const newAdditionalGroupObject = JSON.parse(JSON.stringify(additionalGroupObject));
        groupValueCheck(formValues, newAdditionalGroupObject);
    };

    const handleRemoveGroupCallback = (selectedItem, groupKey) => {
        const newAdditionalGroupObject = JSON.parse(JSON.stringify(additionalGroupObject));
        delete newAdditionalGroupObject[groupKey];
        const formValues = form.getFieldsValue();
        delete formValues[groupKey];
        form.setFieldsValue(formValues);
        groupValueCheck(formValues, newAdditionalGroupObject);
    };

    const groupValueCheck = (formValues, newAdditionalGroupObject) => {
        const keysWithValuesMoreThanTwice = getGroupKeysWithValuesMoreThanTwice(formValues);
        // set all keys to valid
        setRootGroupObject({valid: true});
        Object.keys(newAdditionalGroupObject).forEach(key => {
            newAdditionalGroupObject[key].valid = true;
        });
        keysWithValuesMoreThanTwice.forEach(key => {
            if (!key.startsWith("group")) {
                return;
            }
            if (key === "group0") {
                setRootGroupObject({valid: false});
            } else {
                newAdditionalGroupObject[key].valid = false;
            }
        });
        setAdditionalGroupObject(newAdditionalGroupObject);
    };

    const getGroupKeysWithValuesMoreThanTwice = formValues => {
        const filteredFormValues = Object.keys(formValues)
            .filter(key => key.startsWith("group"))
            .reduce((result, key) => {
                result[key] = formValues[key];
                return result;
            }, {});
        const valueCounts = Object.values(filteredFormValues).reduce((counts, value) => {
            counts[value] = (counts[value] || 0) + 1;
            return counts;
        }, {});
        const valuesMoreThanTwice = Object.keys(valueCounts).filter(value => valueCounts[value] >= 2);
        return Object.keys(formValues).filter(key => valuesMoreThanTwice.includes(formValues[key]));
    };

    const formItems = () => {
        return (
            <>
                <Form.Item name="sn" label="SN" rules={[{required: true, message: "Please input SN!"}]}>
                    <Input placeholder="SN" style={{width: "280px"}} />
                </Form.Item>
                {currentUser.userType === "group" ? (
                    <>
                        {groupList.length !== 0 ? (
                            <Form.Item
                                name="group0"
                                label="Select Group"
                                rules={[{required: true, message: "Please select a group!"}]}
                                validateStatus={rootGroupObject.valid ? "success" : "error"}
                                help={rootGroupObject.valid ? "" : "The selected group cannot be selected."}
                                validateTrigger={["onChange", "onBlur"]}
                            >
                                <Select
                                    style={{width: "280px"}}
                                    placeholder="Please select group"
                                    onChange={(value, selectedItem) => {
                                        handleGroupChangeCallback(selectedItem, "group0");
                                    }}
                                >
                                    {groupList.map(group => {
                                        return (
                                            <Option key={group.id} value={group.name}>
                                                {group.name}
                                            </Option>
                                        );
                                    })}
                                </Select>
                                <Button
                                    style={{
                                        backgroundColor: "transparent",
                                        color: "#BFBFBF"
                                    }}
                                    type="link"
                                    onClick={() => {
                                        const newAdditionalGroupObject = JSON.parse(
                                            JSON.stringify(additionalGroupObject)
                                        );
                                        newAdditionalGroupObject[`group${groupListIndex + 1}`] = {
                                            valid: true
                                        };
                                        setGroupListIndex(groupListIndex + 1);
                                        setAdditionalGroupObject(newAdditionalGroupObject);
                                    }}
                                    icon={<PlusOutlined />}
                                />
                            </Form.Item>
                        ) : (
                            <Form.Item
                                name="group0"
                                label="Select Group"
                                rules={[{required: true, message: "Please select a group!"}]}
                            >
                                <Select style={{width: "280px"}} placeholder="Please select group" />
                            </Form.Item>
                        )}
                        {Object.entries(additionalGroupObject).map(([groupKey, groupValue]) => {
                            return (
                                <Form.Item
                                    name={groupKey}
                                    label="Select Group"
                                    validateStatus={groupValue.valid ? "success" : "error"}
                                    help={groupValue.valid ? "" : "The selected group cannot be selected."}
                                    validateTrigger={["onChange", "onBlur"]}
                                >
                                    <Select
                                        style={{width: "280px"}}
                                        placeholder="Please select a group"
                                        onChange={(value, selectedItem) => {
                                            handleGroupChangeCallback(selectedItem, groupKey);
                                        }}
                                    >
                                        {groupList.map(group => {
                                            return (
                                                <Option key={group.id} value={group.id}>
                                                    {group.name}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                    <Button
                                        style={{
                                            backgroundColor: "transparent",
                                            color: "black"
                                        }}
                                        type="minus"
                                        onClick={(value, selectedItem) => {
                                            handleRemoveGroupCallback(selectedItem, groupKey);
                                        }}
                                        icon={<MinusOutlined />}
                                    />
                                </Form.Item>
                            );
                        })}
                    </>
                ) : null}
            </>
        );
    };

    const onSubmit = async () => {
        const values = form.getFieldValue();
        const groupList = Object.values(values).filter((_, index) => {
            const key = Object.keys(values)[index];
            return key.startsWith("group");
        });
        adoptSwitch(values.sn, groupList)
            .then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                    onSubmitSuccess();
                }
            })
            .catch(() => {
                message.error("Import Switch failed");
            });
        onCancel();
    };

    const onCancelFunc = () => {
        setAdditionalGroupObject({});
        setGroupListIndex(0);
        onCancel();
    };

    return (
        <AmpConCustomModalForm
            title="Build VPN profile for Switch"
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 4
                }
            }}
            CustomFormItems={formItems}
            onCancel={onCancelFunc}
            onSubmit={onSubmit}
            modalClass="ampcon-middle-modal"
        />
    );
};

export default ImportDropdownAction;
