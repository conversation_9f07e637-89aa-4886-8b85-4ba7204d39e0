import {Tag} from "antd";

import {AmpConCustomTable, AmpConCustomModal} from "@/modules-ampcon/components/custom_table";

const alarm_map = {
    warning: "Warning",
    error: "Alarm"
};

const RecoveryEventsModal = ({interfaceDDMData, setInterfaceDDMData, showRecoverEvents, setshowRecoverEvents}) => {
    const columns = [
        {
            title: "Recovery Time",
            dataIndex: "resolved_time",
            sorter: (a, b) => a.resolved_time.localeCompare(b.resolved_time)
        },
        {
            title: "Level",
            dataIndex: "alert_level",
            render: (text, record) => {
                return <Tag className={text === "warning" ? "warnTag" : "failedTag"}>{alarm_map[text]}</Tag>;
            },
            sorter: (a, b) => a.alert_level.localeCompare(b.alert_level)
        },
        {
            title: "Repetition Count",
            dataIndex: "count",
            sorter: (a, b) => a.count - b.count
        },
        {
            title: "Sysname",
            dataIndex: "sysname",
            sorter: (a, b) => a.sysname.localeCompare(b.sysname)
        },
        {
            title: "Port Name",
            dataIndex: "interface",
            sorter: (a, b) => a.interface.localeCompare(b.interface)
        },
        {
            title: "Recovery Event",
            dataIndex: "alert_type",
            sorter: (a, b) => a.alert_type.localeCompare(b.alert_type)
        },
        {
            title: "Channel",
            dataIndex: "channel",
            sorter: (a, b) => a.channel.localeCompare(b.channel)
        },
        {
            title: "Description",
            dataIndex: "alert_msg"
        }
    ];
    const childItems = () => {
        return (
            <div>
                <AmpConCustomTable columns={columns} dataSource={interfaceDDMData} />
            </div>
        );
    };

    return (
        <AmpConCustomModal
            title="Recovery Events"
            childItems={childItems()}
            isModalOpen={showRecoverEvents}
            onCancel={() => {
                setshowRecoverEvents(false);
                setInterfaceDDMData([]);
            }}
            modalClass="ampcon-max-modal"
        />
    );
};

export default RecoveryEventsModal;
