.activeTab {
    padding: 0 4px;
    min-width: 98px;
    height: 34px;
    background: #e7f9f8;
    border: 1px solid #14c9bb;
    line-height: 34px;
    display: inline-block;
    text-align: center;
    font-weight: 400;
    font-size: 14px;
    color: #14c9bb;
}

.inactiveTab {
    padding: 0 4px;
    min-width: 98px;
    height: 34px;
    background: #ffffff;
    border: 1px solid #c5d0d6;
    line-height: 34px;
    display: inline-block;
    text-align: center;
    font-weight: 400;
    font-size: 14px;
    color: #929a9e;
}

.activeKey {
    :global {
        .ant-tabs-nav {
            .ant-tabs-tab-active {
                border-bottom: 3px solid #14c9bb !important;
            }

            .ant-tabs-tab {
                border: none;
                background: #fff;
                padding: 4px 0 !important;
                margin-right: 16px !important;
            }

            .ant-tabs-ink-bar {
                top: unset;
            }

            .ant-tabs-nav-wrap {
                background: #ffffff;
            }
        }
    }
}

.formItem {
    :global {
        .ant-form-item-label {
            white-space: unset;
        }
    }
}

.optionLabel {
    color: #000000 !important;
}
