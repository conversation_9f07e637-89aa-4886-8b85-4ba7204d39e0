import {message, Tag, Select, Space, Flex} from "antd";
import {useState, useEffect} from "react";
import {fetchDeviceDDMInfo, fetchInterfaceDDMInfo, fetchModuleTableAPI} from "@/modules-ampcon/apis/monitor_api";
import ModulePanel from "./module_panel";
import {AmpConCustomTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";
import RecoveryEventsModal from "./recovery_events_modal";
import {getAllModelPhysicPortInfo} from "@/modules-ampcon/apis/config_api";
import SwitchPortPhysicData from "@/modules-ampcon/pages/Telemetry/Switch/switch_port_panel/switch_port_physic_data";
import {toLower, toUpper} from "lodash";
import {TelemetryDateRangePicker} from "@/modules-ampcon/components/telemetry_date_range_picker";

const alert_level_map = {
    error: "Alarm",
    warning: "Warning"
};

export const TimeSelector = ({timeRange, setTimeRange, label = false, disableRange = false}) => {
    return (
        <div style={{display: "flex", alignItems: "center"}}>
            {label || <div style={{fontSize: "14px"}}>Time (UTC-0)</div>}
            <TelemetryDateRangePicker timeRange={timeRange} setTimeRange={setTimeRange} />
        </div>
    );
};

const LevelSelector = ({setAlarmLevel, alarmLevel}) => {
    return (
        <div style={{display: "flex", alignItems: "center"}}>
            <div style={{fontSize: "14px", marginRight: "32px"}}>Level</div>
            <Select
                style={{height: "32px", width: "280px"}}
                options={[
                    {value: "", label: "All"},
                    {value: "error", label: "Alarm"},
                    {value: "warning", label: "Warning"}
                ]}
                value={alarmLevel}
                onChange={value => {
                    setAlarmLevel(value);
                }}
                allowClear
            />
        </div>
    );
};

const LevelAndTimeSelector = ({timeRange, setTimeRange, setAlarmLevel, alarmLevel}) => {
    return (
        <div style={{display: "inline-flex", gap: "32px"}}>
            <LevelSelector setAlarmLevel={setAlarmLevel} alarmLevel={alarmLevel} />
            <TimeSelector timeRange={timeRange} setTimeRange={setTimeRange} />
        </div>
    );
};

const ChartSelector = ({chartType, setChartType}) => {
    return (
        <div style={{display: "inline-flex", visibility: "hidden"}}>
            <div
                onClick={() => setChartType("statistics")}
                style={{
                    fontSize: "16px",
                    marginRight: "32px",
                    color: chartType === "statistics" ? "#34DCCF" : "#212519",
                    cursor: "pointer",
                    borderBottom: chartType === "statistics" ? "4px solid #34DCCF" : "none",
                    boxSizing: "border-box"
                }}
            >
                Statistics
            </div>
            <div
                onClick={() => setChartType("visualization")}
                style={{
                    fontSize: "16px",
                    color: chartType === "visualization" ? "#34DCCF" : "#212519",
                    cursor: "pointer",
                    borderBottom: chartType === "visualization" ? "4px solid #34DCCF" : "none",
                    boxSizing: "border-box"
                }}
            >
                Virtualization
            </div>
        </div>
    );
};

const HealthStatus = ({
    model,
    sn,
    tableWidth,
    sysname,
    ip_address,
    active,
    activeKey,
    activeTab,
    setSelectedPort,
    setActiveKey
}) => {
    const [timeRange, setTimeRange] = useState(["", ""]);
    const [chartType, setChartType] = useState("statistics");
    const [alarmLevel, setAlarmLevel] = useState(null);

    return (
        <div className="subtest" style={{}}>
            <Flex justify="space-between" gap={32} style={{marginBottom: "16px", width: tableWidth}}>
                <ChartSelector chartType={chartType} setChartType={setChartType} />
                <LevelAndTimeSelector
                    timeRange={timeRange}
                    setTimeRange={setTimeRange}
                    alarmLevel={alarmLevel}
                    setAlarmLevel={setAlarmLevel}
                />
            </Flex>
            <HealthStatistics
                model={model}
                sn={sn}
                tableWidth={tableWidth}
                sysname={sysname}
                ip_address={ip_address}
                alarmLevel={alarmLevel}
                setAlarmLevel={setAlarmLevel}
                setTimeRange={setTimeRange}
                timeRange={timeRange}
                active={active}
                activeKey={activeKey}
                activeTab={activeTab}
                setActiveKey={setActiveKey}
                setSelectedPort={setSelectedPort}
            />
        </div>
    );
};

const HealthVisualization = () => {
    return <h1>health vir</h1>;
};

const HealthStatistics = ({
    model,
    sn,
    tableWidth,
    sysname,
    ip_address,
    alarmLevel,
    setAlarmLevel,
    setTimeRange,
    timeRange,
    active,
    activeKey,
    setActiveKey,
    activeTab,
    setSelectedPort
}) => {
    const [showRecoverEvents, setshowRecoverEvents] = useState(false);
    const [currentDDMData, setCurrentDdmData] = useState([]);
    const [interfaceDDMData, setInterfaceDDMData] = useState([]);
    const [onlinePorts, setOnlinePorts] = useState([]);
    const [portData, setPortData] = useState([]);
    const testsn = "testsn";

    function buildLogicPortData(modelData) {
        const temp = [];
        const res = [];
        ["ge", "te", "qe", "xe"].map(type =>
            modelData?.[type]?.map(port => {
                temp.push([port, type]);
            })
        );
        for (let i = 0; i < temp.length; i += 2) {
            if (temp[i + 1] !== undefined) {
                res.push([
                    {
                        label: i + 1,
                        portName: temp[i][0],
                        portType: temp[i][1]
                    },
                    {
                        label: i + 2,
                        portName: temp[i + 1][0],
                        portType: temp[i + 1][1]
                    }
                ]);
            } else {
                res.push([
                    {
                        label: i + 1,
                        portName: temp[i][0],
                        portType: temp[i][1]
                    }
                ]);
            }
        }
        return res;
    }

    const fetchPortData = async () => {
        const isVirtualPanel = !Object.keys(SwitchPortPhysicData).includes(model);
        if (isVirtualPanel) {
            const response = await getAllModelPhysicPortInfo();
            if (response.status === 200) {
                const portInfo = response.data[toLower(model)] || response.data[toUpper(model)];
                const res = buildLogicPortData(portInfo);
                setPortData(res);
                return;
            }
        }
        setPortData(SwitchPortPhysicData[model].portData);
    };
    const fetchDeviceDDMData = async () => {
        if (!sn || !model) return;

        const res = await fetchDeviceDDMInfo([sn]);
        if (res.status !== 200) {
            message.error(res.info);
            return;
        }

        const availablePorts = res.ddm_status?.[sn];
        if (!availablePorts) {
            setCurrentDdmData([]);
            return;
        }

        setOnlinePorts(availablePorts);

        const mergedData = {};
        res.data
            .filter(item => availablePorts.includes(item.interface))
            .forEach(item => {
                const {interface: iface, alert_level} = item;
                // 处理te-1/1/1.1 te-1/1/1.2的情况
                if (iface.includes(".")) {
                    const base = iface.split(".")[0];
                    if (!mergedData[base]) {
                        mergedData[base] = {...item, interface: base};
                    } else if (mergedData[base].alert_level === "warning" && alert_level === "error") {
                        mergedData[base].alert_level = "error";
                    }
                } else {
                    mergedData[iface] = mergedData[iface] || [];
                    mergedData[iface].push(item);
                }
            });

        const mergedList = Object.values(mergedData).flatMap(val => (Array.isArray(val) ? val : [val]));
        setCurrentDdmData(mergedList);
    };

    const fetchInterfaceDDMData = async (sn, interfaceName) => {
        const response = await fetchInterfaceDDMInfo(sn, interfaceName);
        if (response.status !== 200) {
            message.error(response.info);
        }
        setInterfaceDDMData(response.data);
    };

    useEffect(() => {
        if (active && activeTab === "health_status" && activeKey === "Statistics" && sn) {
            fetchDeviceDDMData();
        }
    }, [alarmLevel, timeRange, activeKey, activeTab, active, sn]);

    useEffect(() => {
        fetchPortData();
    }, [sn]);

    const columns = [
        createColumnConfig("Anomaly Time", "last_alert_time"),
        {
            ...createColumnConfig("Level", "alert_level"),
            render: text => <Tag className={text === "warning" ? "warnTag" : "failedTag"}>{alert_level_map[text]}</Tag>
        },
        createColumnConfig("Repetition Count", "count"),
        createColumnConfig("Module Type", "module_type"),
        createColumnConfig("Port Name", "interface"),
        {
            ...createColumnConfig("Anomaly Event", "alert_type"),
            render: text => {
                if (text === "InputPower") return "Rx Power";
                if (text === "OutputPower") return "Tx Power";
                if (text === "LaserTemperature") return "Temperature";
                if (text === "SupplyVoltage") return "Voltage";
                if (text === "LaserBiasCurrent") return "Bias";
                return text;
            }
        },
        {
            ...createColumnConfig("Channel", "channel"),
            render: text => (text === -1 ? "--" : text)
        },
        createColumnConfig("Description", "alert_msg"),
        {
            ...createColumnConfig("Recovered", "resolved"),
            render: text => {
                if (text) {
                    return <span>Yes</span>;
                }
                return <span>No</span>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    fetchInterfaceDDMData(sn, record.interface);
                                    setshowRecoverEvents(true);
                                }}
                            >
                                View Recovery Events
                            </a>
                            <a
                                onClick={() => {
                                    setActiveKey("Visualization");
                                    setSelectedPort(record.interface);
                                }}
                            >
                                Visualization
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const testDDMData = [
        {
            anomaly_time: "2023-10-01 12:00:00",
            alert_level: "warn",
            count: 3,
            interface: "xe-1/1/1",
            alert_type: "High Temperature",
            channel: "Channel 1",
            alert_msg: "Temperature exceeded threshold",
            resolved: false
        },
        {
            anomaly_time: "2024-10-01 12:00:00",
            alert_level: "error",
            count: 32,
            interface: "xe-1/1/2",
            alert_type: "High Temperature",
            channel: "Channel 1",
            alert_msg: "Temperature exceeded threshold",
            resolved: false
        },
        {
            anomaly_time: "2025-10-01 12:00:00",
            alert_level: "warn",
            count: 322,
            interface: "xe-1/1/3",
            alert_type: "High Temperature",
            channel: "Channel 1",
            alert_msg: "Temperature exceeded threshold",
            resolved: true
        },
        {
            anomaly_time: "2026-10-01 12:00:00",
            alert_level: "error",
            count: 3222,
            interface: "xe-1/1/4",
            alert_type: "High Temperature",
            channel: "Channel 1",
            alert_msg: "Temperature exceeded threshold",
            resolved: true
        }
    ];

    const testInterfaceDDMData = [
        {
            resolved_time: "2023-10-01 12:00:00",
            alert_level: "warn",
            count: 3,
            sysname: "sysname",
            interface: "Port 1",
            alert_type: "High Temperature",
            channel: "Channel 1",
            alert_msg: "Temperature exceeded threshold"
        },
        {
            resolved_time: "2024-10-01 12:00:00",
            alert_level: "error",
            count: 34,
            sysname: "sysname",
            interface: "Port 2",
            alert_type: "Low Temperature",
            channel: "Channel 12",
            alert_msg: "Temperature exceeded threshold"
        },
        {
            resolved_time: "2025-10-01 12:00:00",
            alert_level: "warn",
            count: 322,
            sysname: "sysname",
            interface: "Port 3",
            alert_type: "High Temperature",
            channel: "Channel 1",
            alert_msg: "Temperature exceeded threshold"
        },
        {
            resolved_time: "2026-10-01 12:00:00",
            alert_level: "error",
            count: 3222,
            sysname: "sysname",
            interface: "Port 4",
            alert_type: "High Temperature",
            channel: "Channel 1",
            alert_msg: "Temperature exceeded threshold"
        }
    ];

    return (
        <div style={{}}>
            <RecoveryEventsModal
                showRecoverEvents={showRecoverEvents}
                setshowRecoverEvents={setshowRecoverEvents}
                interfaceDDMData={interfaceDDMData}
                setInterfaceDDMData={setInterfaceDDMData}
            />
            <div
                style={{
                    gap: "32px",
                    marginBottom: "-4px",
                    backgroundColor: "#F7F7F7",
                    padding: "24px 16px",
                    width: tableWidth
                }}
            >
                <div
                    style={{
                        display: "inline-flex",
                        paddingLeft: "16px",
                        paddingBottom: "12px",
                        justifyContent: "space-between",
                        width: tableWidth - 50
                    }}
                >
                    <div style={{display: "inline-flex", gap: "106px"}}>
                        <div
                            style={{
                                fontWeight: 400,
                                fontSize: 14,
                                color: "#929A9E",
                                lineHeight: "17px",
                                textTransform: "none"
                            }}
                        >
                            Sysname
                            <span
                                style={{
                                    marginLeft: "8px",
                                    fontWeight: 500,
                                    color: "#212529",
                                    lineHeight: "17px",
                                    textTransform: "none"
                                }}
                            >
                                {sysname}
                            </span>
                        </div>
                        <div
                            style={{
                                fontWeight: 400,
                                fontSize: 14,
                                color: "#929A9E",
                                lineHeight: "17px",
                                textTransform: "none"
                            }}
                        >
                            Device IP
                            <span
                                style={{
                                    marginLeft: "8px",
                                    fontWeight: 500,
                                    color: "#212529",
                                    lineHeight: "17px",
                                    textTransform: "none"
                                }}
                            >
                                {ip_address}
                            </span>
                        </div>
                    </div>
                    <ModuleLegend />
                </div>
                <ModulePanel
                    ddmData={currentDDMData}
                    tableWidth={tableWidth}
                    onlinePorts={onlinePorts}
                    portData={portData}
                />
            </div>
            <AmpConCustomTable
                columns={columns}
                fetchAPIInfo={fetchModuleTableAPI}
                fetchAPIParams={[sn, alarmLevel, timeRange[0], timeRange[1]]}
                isShowPagination
                style={{width: tableWidth}}
            />
        </div>
    );
};
const ModuleLegend = () => {
    const legendItems = [
        {label: "Info", color: "#14C9BB"},
        {label: "Warning", color: "#fbc634"},
        {label: "Alarm", color: "#f53f3f"}
    ];

    return (
        <div style={{display: "flex", gap: "16px", alignItems: "center"}}>
            {legendItems.map(item => (
                <div key={item.label} style={{display: "flex", alignItems: "center", gap: "8px"}}>
                    <div
                        style={{
                            width: "10px",
                            height: "10px",
                            backgroundColor: item.color,
                            borderRadius: "1px"
                        }}
                    />
                    <span>{item.label}</span>
                </div>
            ))}
        </div>
    );
};

export default HealthStatus;
