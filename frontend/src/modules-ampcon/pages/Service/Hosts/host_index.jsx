import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import HostDevices from "./Device/devices";
import HostInventory from "./Inventory/inventory";
import ProtectedRoute, {isRouteForbidden} from "@/modules-ampcon/utils/util";
import {useSelector} from "react-redux";
import ForbiddenPage from "@/modules-ampcon/pages/ForbiddenPage";

const HostIndex = () => {
    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;
    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    const allItems = [
        {
            key: "device_discovery",
            label: "Device Discovery",
            children: <ProtectedRoute component={HostDevices} />
        },
        {
            key: "inventory",
            label: "Inventory",
            children: <ProtectedRoute component={HostInventory} />
        }
    ];

    let pathReg;
    let items = [];

    if (userType === "readonly") {
        items = allItems.filter(item => item.key === "inventory");
        pathReg = /(inventory)$/;
    } else {
        items = allItems;
        pathReg = /(device_discovery|inventory)$/;
    }

    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const match = currentPath.match(pathReg);
            setCurrentActiveKey(match[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    if (isRouteForbidden(location.pathname, userType)) {
        return <ForbiddenPage />;
    }

    return (
        <div className="scrollable-container">
            <Tabs activeKey={currentActiveKey} items={items} onChange={onChange} destroyInactiveTabPane />
        </div>
    );
};

export default HostIndex;
