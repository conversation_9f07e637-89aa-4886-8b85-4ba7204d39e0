import React, {useState, useEffect, useImperativeHandle, forwardRef, useRef, memo} from "react";
import {Card, Select, TreeSelect, Button, message, Tooltip, Form} from "antd";
import Icon, {QuestionCircleOutlined} from "@ant-design/icons";
import {fetchTemplatePreview, fetchServerConfig} from "@/modules-ampcon/apis/roce_api";
import {fetchRoceNicPort} from "@/modules-ampcon/apis/monitor_api";
import styles from "./server.module.scss";
import {useForm} from "antd/es/form/Form";
import filterSvg from "../Monitoring/resource/filter.svg?react";
import shrinkSvg from "../Monitoring/resource/shrink.svg?react";
import unfoldSvg from "../Monitoring/resource/unfold.svg?react";
import shrinkHoverSvg from "../Monitoring/resource/shrink_hover.svg?react";
import unfoldHoverSvg from "../Monitoring/resource/unfold_hover.svg?react";

// const {Tab} = Tabs;

const ShScriptGenerator = memo(
    forwardRef(({serverType}, ref) => {
        const [scriptContent, setScriptContent] = useState(""); // 后端返回的脚本模板
        const [formData, setFormData] = useState({}); // 用户输入的数据
        const [validationRules, setValidationRules] = useState({}); // 校验规则
        const [initFormData, setInitFormData] = useState({}); // 初始表单数据
        const portInfoRef = useRef(null);

        useEffect(() => {
            const fetchScript = async () => {
                try {
                    const response = await fetchTemplatePreview(serverType);
                    const {template, default_param, validation_rules} = response.data;
                    setScriptContent(template);
                    setFormData(default_param);
                    setInitFormData(default_param);
                    setValidationRules(validation_rules);
                } catch (error) {
                    message.error({
                        content: `${error.message}`,
                        duration: 2
                    });
                }
            };
            if (serverType) {
                fetchScript();
            }
        }, [serverType]);
        // 监听scriptContent 变化后再解析模板
        useEffect(() => {
            if (scriptContent) {
                parseTemplate(scriptContent, formData);
            }
        }, [scriptContent]);
        // 解析模板，提取出需要填写的字段并填充默认值
        const parseTemplate = (template, defaults) => {
            const regex = /{{ (.*?) }}/g;
            const placeholders = [];
            let match;

            // eslint-disable-next-line no-cond-assign
            while ((match = regex.exec(template)) !== null) {
                placeholders.push(match[1].trim());
            }

            const initialFormData = {};
            placeholders.forEach(placeholder => {
                initialFormData[placeholder] = defaults[placeholder] || "";
            });

            setFormData(initialFormData);
        };

        const handleInputChange = (e, field) => {
            const {value} = e.target;
            setFormData({...formData, [field]: value});
        };

        const updatePortInfo = newPortInfo => {
            portInfoRef.current = newPortInfo;
        };

        const handleApply = async () => {
            try {
                let isValid = true;
                Object.keys(formData).forEach(key => {
                    const value = formData[key];
                    const validationRule = validationRules[key];

                    // 先判空
                    if (!value || value.trim() === "") {
                        isValid = false;
                        message.error({
                            content: `The value of ${key} cannot be empty!`,
                            duration: 5
                        });
                    } else if (validationRule) {
                        if ("min" in validationRule && "max" in validationRule) {
                            const numValue = Number(value);
                            if (numValue < validationRule.min || numValue > validationRule.max) {
                                isValid = false;
                                message.error({
                                    content: `The value of ${key} must be between ${validationRule.min} and ${validationRule.max}`,
                                    duration: 5
                                });
                            }
                        }
                        if ("pattern" in validationRule) {
                            const regex = new RegExp(validationRule.pattern);
                            if (!regex.test(value)) {
                                isValid = false;
                                message.error({
                                    content: `The value of ${key} does not match the required pattern`,
                                    duration: 5
                                });
                            }
                        }
                    }
                    if (isValid) {
                        // 检查前导零（不等于 "0" 且以 "0" 开头的数字）
                        if (/^0\d/.test(value)) {
                            isValid = false;
                            message.error({
                                content: `The value of ${key} should not have leading zeros`,
                                duration: 5
                            });
                        }
                        // 检查是否包含小数点
                        if (/\./.test(value)) {
                            isValid = false;
                            message.error({
                                content: `The value of ${key} should be an integer without decimal points`,
                                duration: 5
                            });
                        }
                    }
                });

                if (!isValid) {
                    return;
                }

                // console.log("portInfoRef.current:", portInfoRef.current); //打印portInfo值
                if (!portInfoRef.current || Object.keys(portInfoRef.current).length === 0) {
                    message.error({
                        content: "Port info is null. Please check the configuration.",
                        duration: 5
                    });
                    return;
                }

                const params = {...formData};
                const response = await fetchServerConfig(portInfoRef.current, serverType, params);
                if (response.status === 200) {
                    message.success(response.info || "Configuration submission successful!");
                } else {
                    message.error(response.info || "Request failed, please check the configuration");
                }
            } catch (error) {
                message.error(`Request Failure: ${error.message}`);
            }
        };

        const handleCancel = () => {
            message.info("User cancels configuration");
            parseTemplate(scriptContent, initFormData);
        };

        useImperativeHandle(ref, () => ({
            handleShCancel: () => {
                handleCancel();
            },
            handleShApply: () => {
                handleApply();
            },
            updateShPortInfo: newPortInfo => {
                updatePortInfo(newPortInfo);
            }
        }));

        const broadcomTipText = [
            "Option            Description",
            "-r [0-7]          RoCE packet priority",
            "-s VALUE          RoCE packet DSCP value",
            "-c [0-7]          Roce CNP packet priority",
            "-p VALUE          RoCE CNP packet DSCP value",
            "-m [1-3]          1 — PFC only.2 — CC only.3 — PFC + CC mode",
            "-u [2-3]          Utility to configure QoS settings.2 — Use IIdptool3 — Use niccli utility"
        ];
        // Rendering templates, processing the wrapped and annotated parts of {{}} in the template
        const renderTemplate = () => {
            const parts = scriptContent.split(/({{.*?}}|#[^\n]*)/);

            let lastBracketIndex = -1;
            // 定位最最后一个】的位置
            parts.forEach((part, index) => {
                if (part.startsWith("{{") && part.endsWith("}}")) {
                    lastBracketIndex = index;
                }
            });
            return parts.filter(Boolean).map((part, index) => {
                if (part.startsWith("{{") && part.endsWith("}}")) {
                    const key = part.slice(2, -2).trim();
                    const defaultValue = formData[key] || "";
                    const inputWidth = `${Math.max(defaultValue.length, 1) * 8.5}px`;
                    const validationRule = validationRules[key];

                    let isValid = true;
                    if (validationRule) {
                        if ("min" in validationRule && "max" in validationRule) {
                            const numValue = Number(defaultValue);
                            isValid = numValue >= validationRule.min && numValue <= validationRule.max;
                        }
                        if ("pattern" in validationRule) {
                            const regex = new RegExp(validationRule.pattern);
                            isValid = regex.test(defaultValue);
                        }
                        if (isValid) {
                            // 判断是否有前导零，且不等于 '0'）
                            if (/^0\d/.test(defaultValue)) {
                                isValid = false;
                            }
                            // 判断是否包含小数点，任何包含小数点的数字都是无效的
                            if (/\./.test(defaultValue)) {
                                isValid = false;
                            }
                        }
                    }

                    const textColor = isValid ? "#14c9bb" : "red";

                    // const isLastBracket = serverType === "broadcom" && array.slice(index + 1).every(item => !item.includes("}}"));

                    return (
                        <span
                            // eslint-disable-next-line react/no-array-index-key
                            key={index}
                            style={{
                                display: "inline-flex",
                                alignItems: "center",
                                position: "relative"
                            }}
                        >
                            <span className={styles.bracketStyles}>【</span>
                            <input
                                type="text"
                                className={styles.customInput}
                                style={{"--input-width": inputWidth, color: textColor}} // Dynamically set width
                                value={defaultValue}
                                onChange={e => handleInputChange(e, key)}
                            />
                            <span className={styles.bracketStyles}>】</span>
                        </span>
                    );
                }

                if (part.startsWith("#")) {
                    return (
                        <span
                            // eslint-disable-next-line react/no-array-index-key
                            key={index}
                            style={{color: "gray", fontSize: "14px", fontWeight: "normal", fontFamily: "monospace"}}
                        >
                            {part} {/* Keep # and display comments together */}
                        </span>
                    );
                }

                if (part.includes("\n") && serverType === "broadcom" && index === lastBracketIndex + 1) {
                    const firstNewLineIndex = part.indexOf("\n");
                    const beforeNewLine = part.slice(0, firstNewLineIndex); // `\n` 之前的部分
                    const afterNewLine = part.slice(firstNewLineIndex); // `\n` 及其后的部分

                    return (
                        // eslint-disable-next-line react/no-array-index-key
                        <span key={index}>
                            {beforeNewLine}
                            <Tooltip
                                title={
                                    <pre
                                        style={{
                                            margin: 0,
                                            whiteSpace: "pre-wrap",
                                            fontFamily: "monospace",
                                            fontSize: "16px"
                                        }}
                                    >
                                        {broadcomTipText.map(line => (
                                            <div key={line} style={{padding: "2px 0"}}>
                                                {line.replace(/\s{11,}/g, "            ")}
                                            </div>
                                        ))}
                                    </pre>
                                }
                                overlayStyle={{maxWidth: "1000px", fontWeight: "500"}}
                            >
                                <QuestionCircleOutlined style={{color: "rgba(0, 0, 0, 0.45)", marginLeft: 5}} />
                            </Tooltip>
                            {afterNewLine} {/* 换行后的内容 */}
                        </span>
                    );
                }
                // Other content remains unchanged
                // eslint-disable-next-line react/no-array-index-key
                return <span key={index}>{part}</span>;
            });
        };

        return (
            <div>
                <div>
                    <div style={{whiteSpace: "pre-wrap", fontFamily: "monospace", marginTop: "0px"}}>
                        {renderTemplate()}
                    </div>
                </div>
            </div>
        );
    })
);

const ServerPage = () => {
    const [form] = useForm();
    // const [activeTab, setActiveTab] = useState("1");
    const [nicVendor, setNicVendor] = useState();
    const [nicPort, setNicPort] = useState([]);
    const [treeData, setTreeData] = useState();

    const [hoveredIcons, setHoveredIcons] = useState({});

    const shScriptGeneratorRef = useRef(null);

    const nicPortsTooltip =
        "Note: If a new NIC device is added to the network, check it again to avoid incomplete device identification";
    const deploymentTip =
        "Configuration Script Deployment Guidelines\nEditable Parameters:\nMarked in blue text (within validated operational ranges)\nCritical Alerts:\nFlagged in red text (require immediate correction)\nValue Constraints:\nDefined in annotation headers and parameter tooltips";
    const handleNicVendorChange = value => {
        setNicVendor(value);
        setNicPort([]);
        form.setFieldsValue({nicports: []});
    };

    const handleNicPortChange = value => {
        setNicPort(value);
    };

    const handleCancel = () => {
        if (shScriptGeneratorRef.current) {
            shScriptGeneratorRef.current.handleShCancel();
        }
    };

    const handleApply = () => {
        if (shScriptGeneratorRef.current) {
            shScriptGeneratorRef.current.handleShApply();
        }
    };

    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };

    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);

        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px", marginRight: "8px", marginLeft: "8px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    useEffect(() => {
        if (treeData) {
            const transformPortInfo = (selectedValues, treeData) => {
                return treeData.reduce((acc, node) => {
                    const selectedForDevice = node.children
                        .filter(child => selectedValues.includes(child.value))
                        .map(child => child.value.split("-")[1]); // Only retrieve the value of the port (remove device ID prefix)
                    if (selectedForDevice.length > 0) {
                        acc[node.value.toString()] = selectedForDevice;
                    }
                    return acc;
                }, {});
            };
            const newPortInfo = transformPortInfo(nicPort, treeData);
            if (shScriptGeneratorRef.current) {
                shScriptGeneratorRef.current.updateShPortInfo(newPortInfo);
            }
        }
    }, [nicPort]);

    useEffect(() => {
        const fectchPort = async () => {
            try {
                const response = await fetchRoceNicPort(nicVendor);
                const bankendData = response.data;
                const transformTreeData = data => {
                    return data.map(node => ({
                        title: node.nodename,
                        value: node.device_id,
                        children:
                            node.children?.map(port => ({
                                title: port.port_title,
                                value: `${node.device_id}-${port.port_value}` // Combine the child nodes with the device_id and port_value to ensure uniqueness
                            })) || []
                    }));
                };
                const frontData = transformTreeData(bankendData);
                setTreeData(frontData);
            } catch (error) {
                message.error({
                    content: `${error.message}`,
                    duration: 2
                });
            }
        };
        if (nicVendor) {
            fectchPort();
        }
    }, [nicVendor]);

    return (
        <>
            <div style={{display: "flex", flexDirection: "column", height: "100%", border: "none"}}>
                <div className={styles.mainDiv}>
                    {/* Left card */}
                    <Card
                        className={styles.leftCard}
                        title={
                            <span>
                                Deployment
                                <Tooltip
                                    title={<div style={{whiteSpace: "pre-line"}}>{deploymentTip}</div>}
                                    placement="right"
                                    overlayStyle={{
                                        maxWidth: "500px",
                                        fontWeight: "500",
                                        backgroundColor: "gray"
                                    }}
                                >
                                    <QuestionCircleOutlined style={{color: "rgba(0, 0, 0, 0.45)", marginLeft: 5}} />
                                </Tooltip>
                            </span>
                        }
                        headStyle={{padding: "20px 20px"}}
                    >
                        <Form
                            form={form}
                            layout="horizontal"
                            labelAlign="left"
                            labelCol={{flex: "120px"}}
                            // wrapperCol={{flex: "auto"}}
                            style={{width: "100%"}}
                        >
                            <Form.Item
                                label="NIC Vendors"
                                name="nicvendors"
                                required="false"
                                style={{maxWidth: "100%"}}
                            >
                                <Select
                                    className={styles.selectStyle}
                                    value={nicVendor}
                                    onChange={handleNicVendorChange}
                                >
                                    <Select.Option value="nvidia">Nvidia</Select.Option>
                                    <Select.Option value="broadcom">Broadcom</Select.Option>
                                </Select>
                            </Form.Item>
                            <Form.Item
                                label="NIC Ports"
                                name="nicports"
                                required="true"
                                tooltip={nicPortsTooltip}
                                style={{maxWidth: "100%"}}
                            >
                                <TreeSelect
                                    className={styles.treeSelectStyle}
                                    treeData={treeData}
                                    value={nicPort}
                                    onChange={handleNicPortChange}
                                    dropdownStyle={{minWidth: "180px"}}
                                    treeCheckable
                                    showSearch={false}
                                    allowClear
                                    switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                                    suffixIcon={<Icon component={filterSvg} />}
                                    virtual={false}
                                />
                            </Form.Item>
                        </Form>
                    </Card>

                    {/* Right side script generator card */}
                    <Card className={styles.rightCard}>
                        <ShScriptGenerator ref={shScriptGeneratorRef} serverType={nicVendor} />
                    </Card>
                </div>
            </div>
            {/* Bottom button card */}
            <Card className={styles.buttomCard}>
                <Button type="default" onClick={handleCancel} style={{width: "80px"}}>
                    Cancel
                </Button>
                <Button type="primary" onClick={handleApply} style={{width: "80px"}}>
                    Apply
                </Button>
            </Card>
        </>
    );
};

export default ServerPage;
