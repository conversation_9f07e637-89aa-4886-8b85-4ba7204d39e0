import {useRef, useState, useEffect} from "react";
import {Card, Tag, Button, message} from "antd";
import Icon from "@ant-design/icons";
import {refreshSvg, whiteThickExportSvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable, createColumnConfigMultipleParams} from "@/modules-ampcon/components/custom_table";
import {fetchNICsInfo} from "@/modules-ampcon/apis/monitor_api";
import dayjs from "dayjs";
import styles from "@/modules-ampcon/pages/Service/Nics/Inventory/nic_inventory.module.scss";

const NICsInventory = () => {
    const tableRef = useRef(null);
    const tableSearchFieldsList = ["nodename"];
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setselectedRows] = useState([]);

    const columns = [
        createColumnConfigMultipleParams({
            title: "Sysname",
            dataIndex: "name",
            key: "name",
            enableFilter: false,
            enableSorter: false,
            width: "15%"
        }),
        createColumnConfigMultipleParams({
            title: "State",
            dataIndex: "operstate",
            key: "operstate",
            enableFilter: false,
            enableSorter: false,
            render: (_, record) =>
                record.operstate ? (
                    <Tag className={record.operstate === "up" ? "up-tag" : "down-tag"}>
                        {record.operstate.charAt(0).toUpperCase() + record.operstate.slice(1)}
                    </Tag>
                ) : null
        }),
        createColumnConfigMultipleParams({
            title: "Interface",
            dataIndex: "device",
            key: "device",
            enableFilter: false,
            enableSorter: false
        }),
        createColumnConfigMultipleParams({
            title: "Chip Number",
            dataIndex: "chip_number",
            key: "chip_number",
            enableFilter: false,
            enableSorter: false
        }),
        createColumnConfigMultipleParams({
            title: "Mac Address",
            dataIndex: "address",
            key: "address",
            enableFilter: false,
            enableSorter: false
        }),
        createColumnConfigMultipleParams({
            title: "Host Port",
            dataIndex: "host_port",
            key: "host_port",
            enableFilter: false,
            enableSorter: false
        }),
        createColumnConfigMultipleParams({
            title: "Firmware Version",
            dataIndex: "firmware_version",
            key: "firmware_version",
            enableFilter: false,
            enableSorter: false
        }),
        createColumnConfigMultipleParams({
            title: "Type",
            dataIndex: "type",
            key: "type",
            enableFilter: false,
            enableSorter: false
        })
    ];

    const rowSelection = {
        selectedRowKeys,
        selectedRows,
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRowKeys);
            setselectedRows(selectedRows);
        }
    };

    const exportToExecl = (columns, dataSource) => {
        const headerRow = columns.map(col => col.title);

        const convertDataRows = (data, columns) => {
            const rows = [];
            const convertRecord = record => {
                const row = columns.map(col => {
                    const value = record[col.dataIndex];
                    if (typeof value === "string" && value.includes(",")) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                });
                rows.push(row);
                if (record.children) {
                    record.children.forEach(child => {
                        convertRecord(child);
                    });
                }
            };
            data.forEach(record => {
                convertRecord(record);
            });

            return rows;
        };

        const dataRows = convertDataRows(dataSource, columns);
        const csvData = [headerRow, ...dataRows].map(row => row.join(",")).join("\n");
        const currentDateTime = dayjs().format("YYYY_MM_DD_HH_mm_ss");
        const filename = `NIC_Inventory_${currentDateTime}.csv`;

        const blob = new Blob([csvData], {type: "text/csv"});
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <div className={styles.nicInventoryTable}>
            {/* <Card style={{display: "flex", flex: 1, minHeight: "100%"}}> */}
            {/* <h2 style={{marginTop: "8px", marginBottom: "20px"}}>Inventory</h2> */}
            <AmpConCustomTable
                columns={columns}
                searchFieldsList={tableSearchFieldsList}
                extraButton={
                    <>
                        <Button
                            type="primary"
                            onClick={() => {
                                if (selectedRows.length === 0) {
                                    message.error("Please select a NIC first");
                                    return;
                                }
                                exportToExecl(columns, selectedRows);
                            }}
                            style={{display: "flex", alignItems: "center"}}
                        >
                            <Icon component={whiteThickExportSvg} />
                            Export
                        </Button>
                        <Button
                            onClick={() => {
                                tableRef.current.refreshTable();
                                message.success("NIC Inventory table refresh success.");
                            }}
                            style={{display: "flex", alignItems: "center"}}
                        >
                            <Icon component={refreshSvg} />
                            Refresh
                        </Button>
                    </>
                }
                fetchAPIInfo={fetchNICsInfo}
                ref={tableRef}
                rowSelection={{
                    ...rowSelection,
                    checkStrictly: false
                }}
                // bordered={false}
            />
            {/* </Card> */}
        </div>
    );
};

export default NICsInventory;
