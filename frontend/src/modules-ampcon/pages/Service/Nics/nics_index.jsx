import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import NICsInventory from "./Inventory/nic_inventory";
import NICsModulesOverview from "./ModulesOverview/nic_modules_overview";
import ProtectedRoute from "@/modules-ampcon/utils/util";

const items = [
    {
        key: "inventory",
        label: "Inventory",
        children: <ProtectedRoute component={NICsInventory} />
    },
    {
        key: "module_overview",
        label: "Module Overview",
        children: <ProtectedRoute component={NICsModulesOverview} />
    }
];

const NicsIndex = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    useEffect(() => {
        const currentPath = location.pathname;
        if (/(inventory|module_overview)$/.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(/(inventory|module_overview)$/)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (/(inventory|module_overview)$/.test(currentPath)) {
            const matchLength = currentPath.match(/(inventory|module_overview)$/)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <div className="scrollable-container">
            <Tabs activeKey={currentActiveKey} items={items} onChange={onChange} destroyInactiveTabPane />
        </div>
    );
};

export default NicsIndex;
