.modulesOverviewTable {
    min-height: 100%;
    :global {
        .ant-table-row-expand-icon {
            border: 1px solid #ddd;
            color: #ddd !important;
            transform: none;
            &:hover {
                color: #14c9bb;
            }
        }

        .ant-table-row-expand-icon.ant-table-row-expand-icon-expanded {
            border-radius: 2px;
        }

        .ant-input-affix-wrapper .ant-input-clear-icon:hover {
            color: #14c9bb;
        }

        // .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected > .ant-table-cell {
        //     background-color: #fff;
        // }

        .ant-table-wrapper .ant-table-row-expand-icon {
            color: #ccc !important;
            &:focus,
            &:focus-visible,
            &:focus-within {
                color: #ccc!important;
                border-color: #ccc !important;
                box-shadow: none !important;
                text-shadow: none !important;
                font-weight: normal !important;
            }
            &:hover {
                color: #14c9bb !important;
                border-color: #14c9bb !important; 
            }
        }
          .ant-table-wrapper .ant-table-row-expand-icon-collapsed {
            color: #ccc !important;
            &:focus,
            &:focus-visible,
            &:focus-within {
                color: #ccc!important;
                border-color: #ccc !important;
                box-shadow: none !important;
                text-shadow: none !important;
                font-weight: normal !important;
            }
            &:hover {
                color: #14c9bb !important;
                border-color: #14c9bb !important; 
            }
        }
    }
}

