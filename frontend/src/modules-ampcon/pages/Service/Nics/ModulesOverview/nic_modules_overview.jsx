import React, {useEffect, useRef, useState} from "react";
import {useForm} from "antd/es/form/Form";
import {Button, message, Card, Tag, Checkbox, Col, Row, TreeSelect, Form, Tooltip} from "antd";
import Icon from "@ant-design/icons";
import styles from "./nic_modules_overview.module.scss";
import {
    createColumnConfigMultipleParams,
    AmpConCustomTable,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {whiteExportSvg} from "@/utils/common/iconSvg";
import {getRoceModulesInfo, getRoceNicPort} from "@/modules-ampcon/apis/monitor_api";
import settingGreenSvg from "../../../Topo/Topology/resource/site_green.svg?react";
import settingGreenHoverSvg from "../../../Topo/Topology/resource/site_green_hover.svg?react";
import shrinkSvg from "../Monitoring/resource/shrink.svg?react";
import unfoldSvg from "../Monitoring/resource/unfold.svg?react";
import shrinkHoverSvg from "../Monitoring/resource/shrink_hover.svg?react";
import unfoldHoverSvg from "../Monitoring/resource/unfold_hover.svg?react";
import filterSvg from "../Monitoring/resource/filter.svg?react";
import dayjs from "dayjs";

const SettingModal = (settingForm, columnShow, treeData, setColumnShow, selectedPort, setSelectedPort) => {
    const attributeOptions = [
        {label: "Sysname", value: "Sysname"},
        {label: "Port", value: "Port"},
        {label: "Status", value: "Status"},
        {label: "PN", value: "PN"},
        {label: "SN", value: "SN"},
        {label: "Vendor", value: "Vendor"},
        {label: "Length", value: "Length"},
        {label: "Wavelength", value: "Wavelength"},
        {label: "Power Class", value: "PowerClass"},
        {label: "Temperature", value: "Temperature"},
        {label: "Voltage", value: "Voltage"},
        {label: "TX BIAS", value: "TXBIAS"},
        {label: "TX", value: "TX"},
        {label: "RX", value: "RX"}
    ];

    const selectTip = (
        <div style={{whiteSpace: "pre-line"}}>
            {
                "Select target device. Supported NICs: Nvidia/Broadcom.\nOther brands may not be detected.\nIf your selected port isn't listed:\n1. No inserted module\n2. NIC/module incompatibility"
            }
        </div>
    );

    const [checkSetting, setCheckSetting] = useState([]);
    const attributeOnChange = checkedValues => {
        setCheckSetting(checkedValues);
    };

    useEffect(() => {
        // 提取 columnShow 中值为 true 的键到数组
        const visibleKeys = Object.entries(columnShow)
            .filter(([key, value]) => value)
            .map(([key]) => key);
        setCheckSetting(visibleKeys);
        settingForm.setFieldsValue({Parameters: visibleKeys});
    }, [columnShow]);

    const portOnChange = value => {
        setSelectedPort(value);
    };

    const [hoveredIcons, setHoveredIcons] = useState({});
    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };
    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);

        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px", marginLeft: "8px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    return (
        <div>
            <div style={{fontSize: "18px", fontWeight: "bold", marginBottom: "10px"}}>Parameters</div>
            <Form.Item name="Parameters">
                <Checkbox.Group style={{width: "100%"}} defaultValue={checkSetting} onChange={attributeOnChange}>
                    <Row gutter={[0, 16]}>
                        {attributeOptions.map((option, index) => (
                            <Col key={index} span={8}>
                                <Checkbox value={option.value} disabled={index === 0 || index === 1}>
                                    {option.label}
                                </Checkbox>
                            </Col>
                        ))}
                    </Row>
                </Checkbox.Group>
            </Form.Item>
            <div style={{fontSize: "18px", fontWeight: "bold", marginBottom: "10px", marginTop: "20px"}}>Device</div>
            <Form.Item
                name="Device"
                label="Select Device"
                tooltip={{
                    title: selectTip,
                    overlayStyle: {maxWidth: "600px"}
                }}
                rules={[{required: false, message: "Please select configuration!"}]}
            >
                <TreeSelect
                    value={selectedPort}
                    maxTagCount={2}
                    maxTagTextLength={6}
                    treeDefaultExpandAll={false}
                    treeData={treeData}
                    onChange={portOnChange}
                    switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                    multiple
                    placeholder="Filter"
                    style={{width: 280}}
                    allowClear
                    showSearch={false}
                    virtual={false}
                    treeCheckable // 添加复选框
                    suffixIcon={<Icon component={filterSvg} />}
                />
            </Form.Item>
        </div>
    );
};

const NICsModulesOverview = () => {
    const [isSettingModalOpen, setIsSettingModalOpen] = useState();
    const tableRef = useRef(null);
    const [settingForm] = useForm();
    const [selectedPort, setSelectedPort] = useState();
    const [filterResult, setFilterResult] = useState([undefined]);
    const [isHovered, setIsHovered] = useState(false);

    const handleMouseEnter = () => setIsHovered(true);
    const handleMouseLeave = () => setIsHovered(false);

    const [columnShow, setColumnShow] = useState({
        Sysname: true,
        Port: true,
        Status: true,
        PN: true,
        SN: true,
        Vendor: true,
        Length: false,
        Wavelength: false,
        PowerClass: false,
        Temperature: false,
        Voltage: false,
        TXBIAS: false,
        TX: false,
        RX: false
    });

    const columns = [
        columnShow.Sysname &&
            createColumnConfigMultipleParams({
                title: "Sysname",
                dataIndex: "name",
                key: "name",
                enableFilter: false,
                enableSorter: true,
                render: (_, record) => {
                    return <div style={{paddingRight: "45px"}}>{record.name}</div>;
                },
                // width: "25%",
                tableLayout: "auto"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.Port &&
            createColumnConfigMultipleParams({
                title: "Port",
                dataIndex: "device",
                key: "device",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.Status && {
            ...createColumnConfigMultipleParams({
                title: "Status",
                dataIndex: "roce_sfp_module_state",
                key: "roce_sfp_module_state",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
            render: (_, record) =>
                !record.children ? (
                    <Tag className={record.roce_sfp_module_state === "3" ? "up-tag" : "down-tag"}>
                        {record.roce_sfp_module_state === "3" ? "Active" : "Inactive"}
                    </Tag>
                ) : null
        },
        columnShow.PN &&
            createColumnConfigMultipleParams({
                title: "PN",
                dataIndex: "part_number",
                key: "part_number",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.SN &&
            createColumnConfigMultipleParams({
                title: "SN",
                dataIndex: "serial",
                key: "serial",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.Vendor &&
            createColumnConfigMultipleParams({
                title: "Vendor",
                dataIndex: "vendor",
                key: "vendor",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.Length &&
            createColumnConfigMultipleParams({
                title: "Length",
                dataIndex: "roce_sfp_smf_length_meters",
                key: "roce_sfp_smf_length_meters",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.Wavelength &&
            createColumnConfigMultipleParams({
                title: "Wavelength",
                dataIndex: "roce_sfp_wavelength_nm",
                key: "roce_sfp_wavelength_nm",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.PowerClass &&
            createColumnConfigMultipleParams({
                title: "Power Class",
                dataIndex: "roce_sfp_power_class_watts",
                key: "roce_sfp_power_class_watts",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.Temperature &&
            createColumnConfigMultipleParams({
                title: "Temperature",
                dataIndex: "roce_sfp_temperature_celsius",
                key: "roce_sfp_temperature_celsius",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.Voltage &&
            createColumnConfigMultipleParams({
                title: "Voltage",
                dataIndex: "roce_sfp_voltage_mv",
                key: "roce_sfp_voltage_mv",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.TXBIAS &&
            createColumnConfigMultipleParams({
                title: "TX BIAS",
                dataIndex: "roce_sfp_tx_bias_current_ma",
                key: "roce_sfp_tx_bias_current_ma",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.TX &&
            createColumnConfigMultipleParams({
                title: "TX",
                dataIndex: "roce_sfp_tx_power_dbm",
                key: "roce_sfp_tx_power_dbm",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        columnShow.RX &&
            createColumnConfigMultipleParams({
                title: "RX",
                dataIndex: "roce_sfp_rx_power_dbm",
                key: "roce_sfp_rx_power_dbm",
                enableFilter: false,
                enableSorter: true,
                width: "15%"
                // filterDropdownComponent: TableFilterDropdown
            }),
        {
            title: (
                <div style={{textAlign: "center"}}>
                    <Icon
                        component={isHovered ? settingGreenSvg : settingGreenHoverSvg}
                        onClick={() => {
                            setIsSettingModalOpen(true);
                        }}
                        className="icon-button"
                        style={{fontSize: 20, transition: "all 0.3s ease"}}
                        onMouseEnter={handleMouseEnter}
                        onMouseLeave={handleMouseLeave}
                    />
                </div>
            ),
            width: "40px"
        }
    ].filter(Boolean); // 过滤掉所有为 false 的项

    const tableSearchFieldsList = ["name", "device", "roce_sfp_module_state"];

    const matchFieldsList = [
        {name: "name", matchMode: "fuzzy"},
        {name: "device", matchMode: "fuzzy"},
        {name: "roce_sfp_module_state", matchMode: "fuzzy"},
        {name: "part_number", matchMode: "fuzzy"},
        {name: "serial", matchMode: "fuzzy"}
    ];

    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);

    const rowSelection = {
        selectedRowKeys,
        selectedRows,
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRowKeys);
            setSelectedRows(selectedRows);
        }
    };

    const exportToExecl = (columns, dataSource) => {
        const headerRow = columns.map(col => col.title);
        headerRow.pop(); // 移除最后一个设置按钮
        const convertDataRows = (data, columns) => {
            const rows = [];
            const convertRecord = record => {
                const row = columns.map(col => {
                    const value = record[col.dataIndex];
                    if (typeof value === "string" && value.includes(",")) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                });
                rows.push(row);
                if (record.children) {
                    record.children.forEach(child => {
                        convertRecord(child);
                    });
                }
            };
            data.forEach(record => {
                convertRecord(record);
            });

            return rows;
        };

        const dataRows = convertDataRows(dataSource, columns);
        const csvData = [headerRow, ...dataRows].map(row => row.join(",")).join("\n");
        const currentDateTime = dayjs().format("YYYY_MM_DD_HH_mm_ss");
        const filename = `NIC_Module_Overview_${currentDateTime}.csv`;

        const blob = new Blob([csvData], {type: "text/csv"});
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const settingOnSubmit = async () => {
        const Parameters = settingForm.getFieldValue("Parameters");

        if (Parameters.length !== 0) {
            setColumnShow(prevColumnShow => {
                return Object.keys(prevColumnShow).reduce((acc, key) => {
                    acc[key] = prevColumnShow[key] ? false : prevColumnShow[key];
                    return acc;
                }, {});
            });
            for (let i = 0; i < Parameters.length; i++) {
                const str = Parameters[i];
                setColumnShow(prevState => ({
                    ...prevState, // 保留其他属性的值
                    [str]: true // 更新 Name 的值
                }));
            }
        } else {
            settingForm.setFieldsValue({Parameters: ["Sysname", "Port", "Status", "PN", "SN", "Vendor"]});
            message.error("At least one parameter needs to be selected!");
        }

        if (selectedPort) {
            const result = selectedPort.map(item => {
                const [ip, port] = item.split(":"); // 使用split方法拆分字符串
                return {[ip]: port}; // 创建对象并返回
            });
            setFilterResult([result]);
        }
        setIsSettingModalOpen(false);
    };

    const [treeData, setTreeData] = useState([]);
    const getPort = async () => {
        const response = await getRoceNicPort();
        if (response.status === 200) {
            const formattedData = response.data.map(node => {
                const parentLabel = `${node.nodename} (${node.instance})`;
                return {
                    value: node.instance,
                    title: (
                        <span
                            style={{
                                display: "block",
                                maxWidth: "200px",
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis"
                            }}
                            title={parentLabel}
                        >
                            {parentLabel}
                        </span>
                    ),
                    key: node.instance,
                    children: node.children.map(child => ({
                        value: `${node.nodename}:${child.port_value}`,
                        title: child.port_title.length > 30 ? `${child.port_title.slice(0, 20)}...` : child.port_title,
                        key: `${node.nodename}:${child.port_value}`
                    }))
                };
            });
            setTreeData(formattedData);
        }
    };

    useEffect(() => {
        getPort();
    }, []);

    return (
        <div className={styles.modulesOverviewTable}>
            {/* <Card style={{display: "flex", flex: 1, minHeight: "100%"}}> */}
            {/* <h2 style={{margin: "8px 0 20px"}}>Module Overview</h2> */}
            <AmpConCustomTable
                columns={columns}
                searchFieldsList={tableSearchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <Button
                        type="primary"
                        onClick={() => {
                            if (selectedRows.length === 0) {
                                message.error("Please select a NIC first");
                                return;
                            }
                            exportToExecl(columns, selectedRows);
                        }}
                    >
                        <Icon component={whiteExportSvg} />
                        Export
                    </Button>
                }
                fetchAPIInfo={getRoceModulesInfo}
                fetchAPIParams={filterResult}
                ref={tableRef}
                rowSelection={{
                    ...rowSelection,
                    checkStrictly: false
                }}
            />
            <AmpConCustomModalForm
                modalClass="ampcon-middle-modal"
                title="All Counters"
                isModalOpen={isSettingModalOpen}
                formInstance={settingForm}
                layoutProps={{
                    labelCol: {
                        span: 5
                    }
                }}
                CustomFormItems={SettingModal(
                    settingForm,
                    columnShow,
                    treeData,
                    setColumnShow,
                    selectedPort,
                    setSelectedPort
                )}
                onCancel={() => {
                    setIsSettingModalOpen(false);
                }}
                onSubmit={settingOnSubmit}
            />
            {/* </Card> */}
        </div>
    );
};

export default NICsModulesOverview;
