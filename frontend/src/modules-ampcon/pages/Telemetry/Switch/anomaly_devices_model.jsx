import {Mo<PERSON>, <PERSON>lex, <PERSON><PERSON><PERSON>, Spin} from "antd";
import {forwardRef, useImperativeHandle, useState, useMemo, useEffect} from "react";
import SwitchPortPhysicData from "@/modules-ampcon/pages/Telemetry/Switch/switch_port_panel/switch_port_physic_data";
import PortNode from "@/modules-ampcon/pages/Telemetry/Switch/switch_port_panel/port_node";
import {getAllModelPhysicPortInfo} from "@/modules-ampcon/apis/config_api";

const AnomalyDevicesModal = forwardRef((props, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [devices, setDevices] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [modelPortDataMap, setModelPortDataMap] = useState({});
    const [loadingPorts, setLoadingPorts] = useState(false);
    const [isReady, setIsReady] = useState(false);

    const pageSize = 4;

    useImperativeHandle(ref, () => ({
        showAddDeviceModal: (name, portSData) => {
            const formattedDevices = portSData.map(item => ({
                sn: item.sn,
                model: item.platform_model,
                ip: item.mgt_ip,
                ports: item.ports,
                name: item.host_name || "PICOS"
            }));

            const sortedDevices = [
                ...formattedDevices.filter(d => d.sn === name),
                ...formattedDevices.filter(d => d.sn !== name)
            ];

            setDevices(sortedDevices);
            setCurrentPage(1);
            setIsShowModal(true);
        }
    }));

    function buildLogicPortData(modelData) {
        const temp = [];
        const res = [];
        ["ge", "te", "qe", "xe"].map(type =>
            modelData?.[type]?.map(port => {
                temp.push([port, type]);
            })
        );
        for (let i = 0; i < temp.length; i += 2) {
            if (temp[i + 1] !== undefined) {
                res.push([
                    {
                        label: i + 1,
                        portName: temp[i][0],
                        portType: temp[i][1]
                    },
                    {
                        label: i + 2,
                        portName: temp[i + 1][0],
                        portType: temp[i + 1][1]
                    }
                ]);
            } else {
                res.push([
                    {
                        label: i + 1,
                        portName: temp[i][0],
                        portType: temp[i][1]
                    }
                ]);
            }
        }
        return res;
    }
    useEffect(() => {
        const loadAllModels = async () => {
            setIsReady(false); // 暂时关闭渲染
            setLoadingPorts(true);

            const models = [...new Set(devices.map(d => d.model.toLowerCase()))];

            const needFetch = models.some(model => !modelPortDataMap[model]);

            if (needFetch) {
                let responseData = {};
                const response = await getAllModelPhysicPortInfo();
                if (response.status === 200) {
                    responseData = response.data || {};
                }
                const normalizedResponseData = {};
                for (const key in responseData) {
                    if (Object.prototype.hasOwnProperty.call(responseData, key)) {
                        normalizedResponseData[key.toLowerCase()] = responseData[key];
                    }
                }

                const newModelPortDataMap = {...modelPortDataMap};

                for (const model of models) {
                    if (!newModelPortDataMap[model]) {
                        if (SwitchPortPhysicData[model]) {
                            newModelPortDataMap[model] = SwitchPortPhysicData[model].portData;
                        } else {
                            const modelData = normalizedResponseData[model]; // 这里 model 已经是小写了

                            if (modelData) {
                                newModelPortDataMap[model] = buildLogicPortData(modelData);
                            } else {
                                newModelPortDataMap[model] = [];
                            }
                        }
                    }
                }

                setModelPortDataMap(newModelPortDataMap);
            }

            setLoadingPorts(false);
            // 延时 200ms 再显示，避免空闪
            setTimeout(() => {
                setIsReady(true);
            }, 200);
        };

        if (devices.length > 0) {
            loadAllModels();
        }
    }, [devices]);

    const getPortStatus = (devicePorts, portName) => {
        const found = devicePorts.find(p => p.interface === portName);
        if (found?.alert_level) {
            const level = found.alert_level.toLowerCase();
            return level === "info" ? "info" : level;
        }
        return undefined;
    };

    const currentDevices = useMemo(() => {
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = currentPage * pageSize;
        return devices.slice(startIndex, endIndex);
    }, [currentPage, devices]);

    const handlePageChange = page => {
        setCurrentPage(page);
    };

    return (
        <Modal
            className="ampcon-port-modal"
            style={{
                minWidth: "calc(80vw - 240px)",
                maxWidth: "900px",
                minHeight: "420px",
                maxHeight: "calc(80vh - 120px)"
            }}
            open={isShowModal}
            title="Anomaly Devices"
            onCancel={() => setIsShowModal(false)}
            footer={null}
        >
            <div
                style={{
                    maxWidth: "calc(80vw - 240px)",
                    minWidth: "max-content",
                    height: "100%"
                }}
            >
                <div
                    style={{
                        height: "1px",
                        background: "#E7E7E7",
                        borderRadius: "0px",
                        marginTop: "12px",
                        marginLeft: "-32px",
                        marginRight: "-32px"
                    }}
                />
                <div style={{display: "flex", flexDirection: "column", marginTop: "24px"}}>
                    <Spin spinning={!isReady || loadingPorts} tip="Loading ports..." delay={200}>
                        {currentDevices.map((device, deviceIndex) => (
                            <div key={device.sn} style={{marginBottom: "24px"}}>
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        paddingBottom: "12px"
                                    }}
                                >
                                    <div style={{display: "flex", gap: "8px", alignItems: "center"}}>
                                        <span style={{color: "#929A9E", fontSize: "14px"}}>Sysname</span>
                                        <span
                                            style={{
                                                color: "#212529",
                                                fontSize: "14px",
                                                fontWeight: 500,
                                                width: "240px"
                                            }}
                                        >
                                            {device.name} ({device.sn})
                                        </span>
                                    </div>
                                    <div style={{display: "flex", gap: "8px", alignItems: "center"}}>
                                        <span style={{color: "#929A9E", fontSize: "14px", paddingLeft: "100px"}}>
                                            Device IP
                                        </span>
                                        <span style={{color: "#212529", fontSize: "14px", fontWeight: 500}}>
                                            {device.ip}
                                        </span>
                                    </div>
                                    <div style={{display: "flex", alignItems: "center", marginLeft: "auto"}}>
                                        <div
                                            style={{
                                                width: "10px",
                                                height: "10px",
                                                backgroundColor: "#14C9BB",
                                                borderRadius: "1px",
                                                marginRight: "8px"
                                            }}
                                        />
                                        <span style={{fontSize: "14px", color: "#212529", marginRight: "24px"}}>
                                            Info
                                        </span>
                                        <div
                                            style={{
                                                width: "10px",
                                                height: "10px",
                                                backgroundColor: "#FFBB00",
                                                borderRadius: "1px",
                                                marginRight: "8px"
                                            }}
                                        />
                                        <span style={{fontSize: "14px", color: "#212529", marginRight: "24px"}}>
                                            Warning
                                        </span>
                                        <div
                                            style={{
                                                width: "10px",
                                                height: "10px",
                                                backgroundColor: "#F53F3FCC",
                                                borderRadius: "1px",
                                                marginRight: "8px"
                                            }}
                                        />
                                        <span style={{fontSize: "14px", color: "#212529"}}>Alarm</span>
                                    </div>
                                </div>

                                <Flex
                                    wrap
                                    gap="small"
                                    style={{
                                        paddingLeft: "50px",
                                        paddingRight: "50px",
                                        paddingBottom: "12px",
                                        paddingTop: "12px",
                                        background: "linear-gradient(180deg, #EEF1F6 0%, #E0E6EF 100%)"
                                    }}
                                >
                                    {modelPortDataMap[device.model.toLowerCase()]?.map((ports, i) => (
                                        <Flex vertical gap="8px" key={i}>
                                            {ports.map((port, j) => {
                                                if (port === null) {
                                                    return <div key={j} style={{height: "39.71px"}} />;
                                                }
                                                return (
                                                    <Flex key={j} gap={port.isGap ? "32px" : "0px"}>
                                                        <PortNode
                                                            index={port.label}
                                                            portName={port.portName}
                                                            portType={port.portType}
                                                            isSvgReverse={port.isReverse}
                                                            status={getPortStatus(device.ports, port.portName)}
                                                            sn={device.sn}
                                                        />
                                                        <div style={{width: "8px"}} />
                                                    </Flex>
                                                );
                                            })}
                                        </Flex>
                                    ))}
                                </Flex>

                                {deviceIndex !== currentDevices.length - 1 && (
                                    <div
                                        style={{
                                            minWidth: "calc(80vw - 240px)",
                                            maxWidth: "1350px",
                                            height: "1px",
                                            background: "#F2F2F2",
                                            borderRadius: "0px",
                                            marginTop: "24px",
                                            marginLeft: "-32px",
                                            marginRight: "-32px"
                                        }}
                                    />
                                )}
                            </div>
                        ))}
                    </Spin>
                </div>

                <div style={{textAlign: "right"}}>
                    <Pagination
                        current={currentPage}
                        pageSize={pageSize}
                        total={devices.length}
                        onChange={handlePageChange}
                        showSizeChanger={false}
                    />
                </div>
            </div>
        </Modal>
    );
});

export default AnomalyDevicesModal;
