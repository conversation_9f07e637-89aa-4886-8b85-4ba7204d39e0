.opticalModules {
    display: grid;
    height: 100%;
    width: 100%;
    gap: 24px;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: 500px 500px;

  
    > div {
  
      &:nth-child(1) {
        grid-column: 1 / -1;
      }
    }

    &_custom_title {
        font-family: Lato, Lato;
        font-weight: 700;
        font-size: 18px;
        color: #212519;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}

@media (max-height: 1000px) {
    .opticalModules {
      grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 350px);
    }
}

@media (max-height: 800px) {
    .opticalModules {
      grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 290px);
    }
}

@media (min-height: 1500px) {
    .opticalModules {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: 40vh 40vh;
    }
}