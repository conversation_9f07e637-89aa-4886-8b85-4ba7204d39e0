export const portGECommonSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="nono" version="1.1" width="28" height="24" viewBox="0 0 28 24">
            <g>
                <g>
                    <rect x="0" y="0" width="28" height="24" rx="2" fill="currentColor" fillOpacity="1" />
                </g>
                <g transform="matrix(-1,1.2246468525851679e-16,1.2246468525851679e-16,1,48,-2.6645352591003757e-15)">
                    <path
                        d="M37.75,4L37.75,5.33333L40.25,5.33333L40.25,8L44,8L44,19.1111C44,19.602,43.6269,20,43.1667,20L24.833333,20C24.373096,20,24,19.602,24,19.1111L24,8L27.75,8L27.75,5.33333L30.25,5.33333L30.25,4L37.75,4ZM31.25,5L36.75,5L36.75,6.33333L39.25,6.33333L39.25,9L43,9L43,19L25,19L25,9L28.75,9L28.75,6.33333L31.25,6.33333L31.25,5Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

export const portGEHoverSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="28" height="24" viewBox="0 0 28 24">
            <g>
                <g>
                    <rect x="0" y="0" width="28" height="24" rx="2" fill="#838E9E" fillOpacity="1" />
                </g>
                <g transform="matrix(-1,1.2246468525851679e-16,1.2246468525851679e-16,1,48,-2.6645352591003757e-15)">
                    <path
                        d="M37.75,4L37.75,5.33333L40.25,5.33333L40.25,8L44,8L44,19.1111C44,19.602,43.6269,20,43.1667,20L24.833333,20C24.373096,20,24,19.602,24,19.1111L24,8L27.75,8L27.75,5.33333L30.25,5.33333L30.25,4L37.75,4ZM31.25,5L36.75,5L36.75,6.33333L39.25,6.33333L39.25,9L43,9L43,19L25,19L25,9L28.75,9L28.75,6.33333L31.25,6.33333L31.25,5Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

export const portGESelectedToCoreSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="nne" version="1.1" width="28" height="24" viewBox="0 0 28 24">
            <g>
                <g>
                    <rect x="0" y="0" width="28" height="24" rx="2" fill="#26A5E1" fillOpacity="0.800000011920929" />
                </g>
                <g transform="matrix(-1,1.2246468525851679e-16,1.2246468525851679e-16,1,48,-2.6645352591003757e-15)">
                    <path
                        d="M37.75,4L37.75,5.33333L40.25,5.33333L40.25,8L44,8L44,19.1111C44,19.602,43.6269,20,43.1667,20L24.833333,20C24.373096,20,24,19.602,24,19.1111L24,8L27.75,8L27.75,5.33333L30.25,5.33333L30.25,4L37.75,4ZM31.25,5L36.75,5L36.75,6.33333L39.25,6.33333L39.25,9L43,9L43,19L25,19L25,9L28.75,9L28.75,6.33333L31.25,6.33333L31.25,5Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

export const portTECommonSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="28" height="24" viewBox="0 0 28 24">
            <g transform="matrix(1,0,0,-1,0,48)">
                <g>
                    <rect x="0" y="24" width="28" height="24" rx="2" fill="currentColor" fillOpacity="1" />
                </g>
                <g>
                    <path
                        d="M18.230800000000002,43L9.76923,43L9.76923,44L4,44L4,28.68396C4,28.306274,4.235566,28,4.526152,28L23.4738,28C23.7644,28,24,28.306274,24,28.68396L24,44L18.230800000000002,44L18.230800000000002,43ZM8.76923,42L8.76923,43L5,43L5,29L23,29L23,43L19.230800000000002,43L19.230800000000002,42L8.76923,42Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

export const portTEHoverSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="28" height="24" viewBox="0 0 28 24">
            <g transform="matrix(1,0,0,-1,0,48)">
                <g>
                    <rect x="0" y="24" width="28" height="24" rx="2" fill="#838E9E" fillOpacity="1" />
                </g>
                <g>
                    <path
                        d="M18.230800000000002,43L9.76923,43L9.76923,44L4,44L4,28.68396C4,28.306274,4.235566,28,4.526152,28L23.4738,28C23.7644,28,24,28.306274,24,28.68396L24,44L18.230800000000002,44L18.230800000000002,43ZM8.76923,42L8.76923,43L5,43L5,29L23,29L23,43L19.230800000000002,43L19.230800000000002,42L8.76923,42Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

export const portXECommonSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="28" height="24" viewBox="0 0 28 24">
            <g>
                <g>
                    <rect x="0" y="0" width="28" height="24" rx="2" fill="currentColor" fillOpacity="1" />
                </g>
                <g>
                    <path
                        d="M4,20L24,20L24,4.68396C24,4.306274,23.7644,4,23.4738,4L4.526152,4C4.235566,4,4,4.306274,4,4.68396L4,20ZM23,19L5,19L5,5L23,5L23,19Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

export const portXEHoverSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="28" height="24" viewBox="0 0 28 24">
            <g>
                <g>
                    <rect x="0" y="0" width="28" height="24" rx="2" fill="#838E9E" fillOpacity="1" />
                </g>
                <g>
                    <path
                        d="M4,20L24,20L24,4.68396C24,4.306274,23.7644,4,23.4738,4L4.526152,4C4.235566,4,4,4.306274,4,4.68396L4,20ZM23,19L5,19L5,5L23,5L23,19Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

export const portXESelectedToCoreSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="28" height="24" viewBox="0 0 28 24">
            <g>
                <g>
                    <rect x="0" y="0" width="28" height="24" rx="2" fill="#26A5E1" fillOpacity="0.800000011920929" />
                </g>
                <g>
                    <path
                        d="M4,20L24,20L24,4.68396C24,4.306274,23.7644,4,23.4738,4L4.526152,4C4.235566,4,4,4.306274,4,4.68396L4,20ZM23,19L5,19L5,5L23,5L23,19Z"
                        fillRule="evenodd"
                        fill="#FFFFFF"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};
