import React, {useEffect, useState} from "react";
import {Space, Table, Button, Form, message, Dropdown, Card} from "antd";
import Icon, {DownOutlined, LockOutlined, UnlockOutlined, UsergroupAddOutlined} from "@ant-design/icons";
import {
    fetchUserInfo,
    addUserInfo,
    unLockUserInfo,
    lockUserInfo,
    delUserInfo,
    editUserInfo,
    getTACACSSettings,
    setTACACSSettings
} from "@/modules-ampcon/apis/user_api";
import {
    TableFilterDropdown,
    handleTableChange,
    createMatchMode,
    createColumnConfig,
    GlobalSearchInput,
    createFilterFields
} from "@/modules-ampcon/components/custom_table";
import {layoutColumnProps} from "@/modules-ampcon/utils/util";
import {UserModal, TACACSUserModal} from "@/modules-ampcon/pages/System/user_modal";
import styles from "@/modules-ampcon/pages/System/user_management.module.scss";
import {useTableInitialElement} from "@/modules-ampcon/hooks/useModalTable";
import {addSvg, showListSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const UserManagement = () => {
    const [
        isModalOpen,
        setIsModalOpen,
        searchFields,
        setSearchFields,
        data,
        setData,
        loading,
        setLoading,
        pagination,
        setPagination,
        userForm
    ] = useTableInitialElement([], true);

    const [TACACSUserForm] = Form.useForm();
    const [isModalOpenTacacs, setIsModalOpenTacacs] = useState(false);
    const [groups, setGroups] = useState([]);
    const [oldPwdFormItemTag, setOldPwdFormItemTag] = useState(false);
    const [groupSelects, setGroupSelects] = useState([]);

    const [showDeviceCheckBox, setShowDeviceCheckBox] = useState(false);
    const [deviceType, setDeviceType] = useState([]);
    const [hostGroupSelects, setHostGroupSelects] = useState([]);

    const checkSortedColumn = columns => {
        for (const columnKey in columns) {
            if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                const columnConfig = columns[columnKey];
                if (columnConfig.defaultSortOrder !== null) {
                    return [columnConfig.dataIndex, columnConfig.defaultSortOrder];
                }
            }
        }
        return [undefined, undefined];
    };
    const [sorter, setSorter] = useState({});
    const [filters, setFilters] = useState({});

    const fetchData = async () => {
        setLoading(true);

        const filterFields = filters ? createFilterFields(filters, matchModes) : [];
        const sortFields = [];
        if (sorter.field && sorter.order) {
            sortFields.push({
                field: sorter.field,
                order: sorter.order === "ascend" ? "asc" : "desc"
            });
        }

        try {
            const response = await fetchUserInfo(
                pagination.current,
                pagination.pageSize,
                filterFields,
                sortFields,
                searchFields
            );
            setGroups(response.allGroups);
            setData(response.data);
            setPagination(prev => ({
                ...prev,
                total: response.total,
                current: response.page,
                pageSize: response.pageSize
            }));
        } catch (error) {
            // error
        } finally {
            setLoading(false);
        }
    };

    const update_user = async (username, lockStatus) => {
        const ret = lockStatus ? await lockUserInfo(username) : await unLockUserInfo(username);
        if (ret.status === 200) {
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
        await fetchData();
    };

    const del_user = async username => {
        const ret = await delUserInfo(username);
        if (ret.status === 200) {
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
        await fetchData();
    };

    useEffect(() => {
        fetchData().then(() => {
            const [sortedColumn, sortedOrder] = checkSortedColumn(userColumns);
            if (sortedColumn) {
                sorter.field = sortedColumn;
                sorter.order = sortedOrder;
                tableChange("", "", sorter);
            }
        });
    }, []);

    useEffect(() => {
        fetchData().then();
    }, [searchFields]);

    const edit_user = record => {
        const groupIds = record.groupId ? record.groupId.split(",") : [];
        const switchGroupList = groups
            .filter(group => group.group_type === "switch" && groupIds.includes(group.id.toString()))
            .map(group => group.id.toString());
        const hostGroupList = groups
            .filter(group => group.group_type === "host" && groupIds.includes(group.id.toString()))
            .map(group => group.id.toString());

        const deviceTypes = [];
        if (switchGroupList.length > 0) deviceTypes.push("switch");
        if (hostGroupList.length > 0) deviceTypes.push("host");
        setDeviceType(deviceTypes);
        setShowDeviceCheckBox(record.userType === "group");

        userForm.setFieldsValue({
            username: record.name,
            userRole: record.type,
            userType: record.userType,
            email: record.email,
            group: record.groupId
        });
        setOldPwdFormItemTag(true);
        setIsModalOpen(true);
    };

    const handleSearchChange = e => {
        setSearchFields({
            fields: ["name", "email"],
            value: e.target.value
        });
    };

    const findGroupNameById = (array, id) => {
        const {name} = array.find(item => item.id === id);
        return name;
    };

    const renderGroup = record => {
        if (record.userType === "global") {
            return (
                // <Button type="primary" className={styles.groupButton}> <UsergroupAddOutlined />
                <>All Group</>
                // </Button>
            );
        }
        if (record.groupId && record.groupId.split(",").length > 0) {
            const items = record.groupId.split(",").map((groupId, index) => ({
                label: findGroupNameById(groups, parseInt(groupId)),
                key: `${index}`,
                icon: <UsergroupAddOutlined />
            }));

            return (
                <Dropdown menu={{items}}>
                    <a
                        style={{
                            color: "#14C9BB",
                            ":hover": {color: "#34DCCF"}
                        }}
                    >
                        Show All <DownOutlined className="rotate-icon" />
                    </a>
                </Dropdown>
            );
        }
        return <>No Group</>;
    };

    const roleMapping = {
        readonly: "Readonly",
        admin: "Operator",
        superadmin: "Admin",
        superuser: "SuperAdmin"
    };

    const userColumns = [
        {
            ...createColumnConfig("User Name", "name", TableFilterDropdown),
            render: (text, record) => (
                <Space>
                    {text}
                    {record.isLocked ? (
                        <LockOutlined style={{color: "red"}} />
                    ) : (
                        <UnlockOutlined style={{color: "#14c9bb"}} />
                    )}
                </Space>
            )
        },
        createColumnConfig("Created Time", "ctime", TableFilterDropdown, "", "", "descend"),
        {
            ...createColumnConfig("Role", "type"),
            render: (text, record) => <Space>{roleMapping[record.type]}</Space>
        },
        createColumnConfig("Email", "email", TableFilterDropdown),
        {
            title: "Group",
            dataIndex: "groupId",
            render: (text, record) => <Space>{renderGroup(record)}</Space>
        },
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="middle" className={styles.actionLink}>
                    {record.name !== "admin" && <a onClick={() => edit_user(record)}>Edit</a>}
                    <a onClick={() => confirmModalAction("Are you sure want to delete?", () => del_user(record.name))}>
                        Delete
                    </a>
                    {record.name !== "admin" &&
                        (record.isLocked ? (
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to unLock?", () =>
                                        update_user(record.name, false)
                                    )
                                }
                            >
                                Unlock
                            </a>
                        ) : (
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to lock?", () =>
                                        update_user(record.name, true)
                                    )
                                }
                            >
                                Lock
                            </a>
                        ))}
                </Space>
            )
        }
    ];

    const matchModes = createMatchMode([
        {name: "name", matchMode: "exact"},
        {name: "email", matchMode: "fuzzy"},
        {name: "ctime", matchMode: "fuzzy"}
    ]);

    const tableChange = async (pagination, filters, sorter) => {
        setSorter(sorter);
        setFilters(filters);
        await handleTableChange(
            pagination,
            filters,
            sorter,
            setPagination,
            searchFields,
            fetchUserInfo,
            "",
            setData,
            matchModes,
            setLoading
        );
    };

    const handleModal = async (formInstance, isOpen) => {
        formInstance.resetFields();
        if (formInstance === userForm) {
            setIsModalOpen(isOpen);
        } else if (formInstance === TACACSUserForm) {
            setIsModalOpenTacacs(isOpen);
            const record = await getTACACSSettings();
            const fieldValues = {
                enable: record.enable,
                authProtocol: record.authProtocol ? record.authProtocol : "",
                serverHost: record.serverHost || "",
                serverHostII: record.serverHostII || "",
                serverSecret: record.serverSecret || "",
                sessionTimeout: record.sessionTimeout || 5
            };
            if (!record.userMapping || !Object.keys(record.userMapping).length) {
                fieldValues.readonlyMin = 1;
                fieldValues.readonlyMax = 1;
                fieldValues.adminMin = 5;
                fieldValues.adminMax = 5;
                fieldValues.superadminMin = 10;
                fieldValues.superadminMax = 10;
                fieldValues.superuserMin = 15;
                fieldValues.superuserMax = 15;
            } else {
                Object.keys(record.userMapping).forEach(key => {
                    const [min, max] = record.userMapping[key];
                    fieldValues[`${key}Min`] = min;
                    fieldValues[`${key}Max`] = max;
                });
            }
            TACACSUserForm.setFieldsValue(fieldValues);
        }
    };

    const handleFormSubmit = async values => {
        const groupIdList = Object.keys(values)
            .filter(key => key.startsWith("groupSelect") && values[key] !== undefined)
            .map(key => values[key]);

        const filteredValues = Object.fromEntries(
            Object.entries(values).filter(([key]) => !key.startsWith("groupSelect"))
        );

        const updatedValues = {...filteredValues, groupIdList};

        let ret;
        if (oldPwdFormItemTag) {
            ret = await editUserInfo(updatedValues);
        } else {
            ret = await addUserInfo(updatedValues);
        }

        if (ret.status === 200) {
            userForm.resetFields();
            setIsModalOpen(false);
            setDeviceType([]);
            setShowDeviceCheckBox(false);
            setOldPwdFormItemTag(false);
            setGroupSelects([]);
            setHostGroupSelects([]);
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
        await fetchData();
    };

    const handleTacacsFormSubmit = async values => {
        const userMapping = {
            readonly: [values.readonlyMin, values.readonlyMax],
            admin: [values.adminMin, values.adminMax],
            superadmin: [values.superadminMin, values.superadminMax],
            superuser: [values.superuserMin, values.superuserMax]
        };
        // {readonly: [1, 1], admin: [5, 5], superadmin: [10, 10], superuser: [15, 15]}
        const fieldsToDelete = [
            "readonlyMin",
            "readonlyMax",
            "adminMin",
            "adminMax",
            "superadminMin",
            "superadminMax",
            "superuserMin",
            "superuserMax"
        ];
        fieldsToDelete.forEach(field => delete values[field]);
        values.userMapping = userMapping;
        setIsModalOpenTacacs(false);
        const ret = await setTACACSSettings(values);
        if (ret.status === 200) {
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
    };

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{margin: "8px 0 20px"}}>User Management</h2>
            <UserModal
                title={oldPwdFormItemTag ? "Edit User" : "Create User"}
                onText="Apply"
                isModalOpen={isModalOpen}
                onCancel={() => {
                    setDeviceType([]);
                    setShowDeviceCheckBox(false);
                    handleModal(userForm, false);
                }}
                onSubmit={handleFormSubmit}
                formInstance={userForm}
                layoutProps={layoutColumnProps}
                oldPwdFormItemTag={oldPwdFormItemTag}
                setOldPwdFormItemTag={setOldPwdFormItemTag}
                groupsData={groups}
                groupSelects={groupSelects}
                setGroupSelects={setGroupSelects}
                showDeviceCheckBox={showDeviceCheckBox}
                setShowDeviceCheckBox={setShowDeviceCheckBox}
                deviceType={deviceType}
                setDeviceType={setDeviceType}
                hostGroupSelects={hostGroupSelects}
                setHostGroupSelects={setHostGroupSelects}
                modalClass="ampcon-middle-modal"
            />
            <TACACSUserModal
                title="TACACS+ Settings"
                onText="Save"
                isModalOpen={isModalOpenTacacs}
                onCancel={() => handleModal(TACACSUserForm, false)}
                onSubmit={handleTacacsFormSubmit}
                formInstance={TACACSUserForm}
                layoutProps={layoutColumnProps}
                modalClass="ampcon-custom-modal-style"
            />
            <Space size={16} style={{marginBottom: "30px"}}>
                <Button type="primary" block onClick={() => handleModal(userForm, true)}>
                    <Icon component={addSvg} />
                    User
                </Button>
                <Button htmlType="button" block onClick={() => handleModal(TACACSUserForm, true)}>
                    <Icon component={showListSvg} />
                    TACACS+ Settings
                </Button>
            </Space>
            <GlobalSearchInput onChange={handleSearchChange} />
            <div>
                <Table
                    columns={userColumns}
                    bordered
                    rowKey={record => record.id}
                    loading={loading}
                    dataSource={data}
                    pagination={pagination}
                    onChange={tableChange}
                />
            </div>
        </Card>
    );
};

export default UserManagement;
