import {F<PERSON>, <PERSON><PERSON>, Card, message, Space, Tag} from "antd";
import Icon from "@ant-design/icons";
import {useEffect, useRef, useState} from "react";
import {refreshSvg, whiteThickExportSvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable, createColumnConfig, AmpConCustomStaticTable} from "@/modules-ampcon/components/custom_table";
import {getLicenseLog, getLicenseInfo} from "@/modules-ampcon/apis/dashboard_api";
import dayjs from "dayjs";
import styles from "@/modules-ampcon/pages/Monitor/Event/Event.module.scss";

const LicenseLog = () => {
    const licenseLogRef = useRef(null);
    const licenseAlarmRef = useRef(null);
    const [logSelected, setLogSelected] = useState([]);
    const [alarmSelected, setAlarmSelected] = useState([]);
    // const [isBackUpDisabled, setIsBackUpDisabled] = useState(true);
    const [alarmTableData, setAlarmTableData] = useState({});

    const getLicenseInfoApi = async () => {
        const transformedData = [];
        const response = await getLicenseInfo();
        if (response.status !== 200) {
            message.error("Get License Info Failed");
        } else {
            for (const item of response.data) {
                const {license_id, license_info, create_time, license_type} = item;

                for (const [hardwareId, licenseDetails] of Object.entries(license_info)) {
                    if (licenseDetails.status.toLowerCase() !== "valid") {
                        transformedData.push({
                            id: license_id + hardwareId,
                            license_file: license_id,
                            hardware_id: hardwareId,
                            license_file_status: licenseDetails.status.toLowerCase(),
                            valid_date: create_time,
                            expiration_date: licenseDetails.expire_time,
                            status_description: licenseDetails.description
                                ? licenseDetails.description
                                : licenseDetails.status.toLowerCase(),
                            platform_model: licenseDetails.platform_model,
                            license_type
                        });
                    }
                }
            }
        }

        const data = {
            data: transformedData,
            total: transformedData.length
        };
        setAlarmTableData(data);
        licenseAlarmRef.current.clearSelectedRow();
    };

    useEffect(() => {
        // if (alarmSelected.length > 0) {
        //     setIsBackUpDisabled(false);
        // }
        getLicenseInfoApi().then();
    }, []);

    const logTableColumns = [
        {
            ...createColumnConfig("Name", "params"),
            render: text => text || "--"
        },
        createColumnConfig("Create Time", "create_time", null, "", "", "descend"),
        createColumnConfig("Operation", "method"),
        {
            title: "Status",
            dataIndex: "status",
            // filterDropdown: props => TableSelectFilterDropdown({...props, filterOptions}),
            onFilter: () => true,
            sorter: (a, b) => a.status.localeCompare(b.status),
            render: (_, record) => {
                const status = String(record.status || "").toLowerCase();
                return (
                    <div>
                        <Space size="small">
                            {status === "success" && <Tag className={styles.successTag}>Success</Tag>}
                            {status === "error" && <Tag className={styles.failedTag}>Error</Tag>}
                            {status === "warning" && <Tag className={styles.runningTag}>Warning</Tag>}
                            {!["success", "error", "warning"].includes(status) && <Tag>--</Tag>}
                        </Space>
                    </div>
                );
            }
        }
    ];

    const alarmTableColumns = [
        {
            title: "License ID",
            dataIndex: "license_file",
            sorter: (a, b) => a.license_file.localeCompare(b.license_file)
        },
        {title: "Hardware ID", dataIndex: "hardware_id", sorter: (a, b) => a.hardware_id.localeCompare(b.hardware_id)},
        {
            title: "Model",
            dataIndex: "platform_model",
            sorter: (a, b) => a.platform_model.localeCompare(b.platform_model)
        },
        {
            title: "License Type",
            dataIndex: "license_type",
            sorter: (a, b) => a.license_type.localeCompare(b.license_type)
        },
        {title: "Valid Date", dataIndex: "valid_date", sorter: (a, b) => a.valid_date.localeCompare(b.valid_date)},
        {
            title: "Expiration Date",
            dataIndex: "expiration_date",
            sorter: (a, b) => a.expiration_date.localeCompare(b.expiration_date)
        },
        {
            title: "Status Description",
            dataIndex: "status_description",
            sorter: (a, b) => a.status_description.localeCompare(b.status_description)
        }
    ];
    const licenseLogTableMatchFieldsList = [
        {name: "params", matchMode: "fuzzy"},
        {name: "method", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"}
    ];
    const licenseLogTableSearchFieldsList = ["params_original", "method", "create_time", "status"];

    const exportToExecl = (columns, dataSource) => {
        const headerRow = columns.map(col => col.title);
        const dataRows = dataSource.map(record =>
            columns.map(col => {
                const value = record[col.dataIndex];
                if (typeof value === "string" && value.includes(",")) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return value;
            })
        );
        const csvData = [headerRow, ...dataRows].map(row => row.join(",")).join("\n");
        const currentDateTime = dayjs().format("YYYY_MM_DD_HH_mm_ss");
        const filename = `license_${currentDateTime}.csv`;

        const blob = new Blob([csvData], {type: "text/csv"});
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <Card style={{display: "flex", flex: 1}}>
            <Flex vertical>
                <span
                    style={{
                        fontSize: "18px",
                        paddingBottom: "18px",
                        borderBottom: "1px solid rgb(242, 242, 242)",
                        marginBottom: "24px",
                        fontWeight: 700
                    }}
                >
                    License Log
                </span>
                <AmpConCustomTable
                    ref={licenseLogRef}
                    rowSelection={{
                        selectedRowKeys: [],
                        selectedRows: [],
                        onChange: (_, selectedRows) => {
                            setLogSelected(selectedRows);
                        }
                    }}
                    extraButton={
                        <>
                            <Button
                                type="primary"
                                onClick={() => {
                                    if (logSelected.length === 0) {
                                        message.error("Please select a log first");
                                        return;
                                    }
                                    // alert("调用导出函数");
                                    exportToExecl(logTableColumns, logSelected);
                                }}
                                style={{display: "flex", alignItems: "center"}}
                            >
                                <Icon component={whiteThickExportSvg} />
                                Export
                            </Button>
                            <Button
                                onClick={() => {
                                    licenseLogRef.current.refreshTable();
                                    message.success("Log table refresh success.");
                                }}
                                style={{display: "flex", alignItems: "center"}}
                            >
                                <Icon component={refreshSvg} />
                                Refresh
                            </Button>
                        </>
                    }
                    columns={logTableColumns}
                    matchFieldsList={licenseLogTableMatchFieldsList}
                    searchFieldsList={licenseLogTableSearchFieldsList}
                    buttonProps={[]}
                    fetchAPIInfo={getLicenseLog}
                />
            </Flex>
            <Flex vertical>
                <span
                    style={{
                        fontSize: "18px",
                        paddingBottom: "18px",
                        borderBottom: "1px solid rgb(242, 242, 242)",
                        marginBottom: "24px",
                        fontWeight: 700
                    }}
                >
                    License Alert
                </span>
                <AmpConCustomStaticTable
                    ref={licenseAlarmRef}
                    rowSelection={{
                        selectedRowKeys: [],
                        selectedRows: [],
                        onChange: (_, selectedRows) => {
                            setAlarmSelected(selectedRows);
                        }
                    }}
                    extraButton={
                        <>
                            {/* <Button disabled={isBackUpDisabled} style={{display: "flex", alignItems: "center"}}>
                                <Icon component={isBackUpDisabled ? backUpGraySvg : backupSvg} />
                                Backup
                            </Button> */}
                            <Button
                                type="primary"
                                onClick={() => {
                                    if (alarmSelected.length === 0) {
                                        message.error("Please select a log first");
                                        return;
                                    }
                                    // alert("调用导出函数");
                                    exportToExecl(alarmTableColumns, alarmSelected);
                                    licenseAlarmRef.current.clearSelectedRow();
                                }}
                                style={{display: "flex", alignItems: "center"}}
                            >
                                <Icon component={whiteThickExportSvg} />
                                Export
                            </Button>
                            <Button
                                htmlType="button"
                                onClick={() => {
                                    getLicenseInfoApi().then();
                                    message.success("License Alert table refresh success.");
                                }}
                                style={{display: "flex", alignItems: "center"}}
                            >
                                <Icon component={refreshSvg} />
                                Refresh
                            </Button>
                        </>
                    }
                    columns={alarmTableColumns}
                    data={alarmTableData}
                />
            </Flex>
        </Card>
    );
};
export default LicenseLog;
