// eslint-disable-next-line no-unused-vars
import {
    importModalSvg,
    quickActivateSvg,
    refreshSvg,
    searchSvg,
    trialConvertStandardColorSvg
} from "@/utils/common/iconSvg";
import styles from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/license_management.module.scss";
import {Button, Card, Divider, Flex, Form, Input, message, Radio, Row, Space, Spin, Tag, Tree} from "antd";
import Icon from "@ant-design/icons/lib/components/Icon";
import {useEffect, useRef, useState} from "react";
import {AmpConCustomModal, AmpConCustomStaticTable} from "@/modules-ampcon/components/custom_table";
import {
    delSwitchWithCheckLic,
    getLicenseInfo,
    importLicense,
    importLicenseFile,
    invalidateLicense
} from "@/modules-ampcon/apis/dashboard_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {checkCurrentLicenseType} from "@/modules-ampcon/apis/config_api";
import {useNavigate} from "react-router-dom";

const {DirectoryTree} = Tree;

const InvalidItems = ({setIsModalOpenExpired, invalidCode}) => {
    const exportInvalidCode = () => {
        const element = document.createElement("a");
        element.setAttribute("download", "RevokeCode");
        const blob = new Blob([invalidCode], {type: "text/plain"});
        const fileUrl = URL.createObjectURL(blob);

        element.setAttribute("href", fileUrl);
        element.style.display = "none";
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    };

    return (
        <Flex vertical>
            <div style={{textAlign: "center", marginTop: "12px"}}>
                <Divider
                    style={{
                        width: "calc(100% + 48px)",
                        margin: "-20.8px 0px 20px -24px",
                        color: "#E2E8F0"
                        // border: "0.5px solid #E2E8F0"
                    }}
                />
                <Space>
                    <Input.TextArea
                        rows={12}
                        style={{width: "460px", background: "#F8FAFB", border: "none", borderRadius: "4px"}}
                        value={invalidCode}
                        readOnly
                        // style={{}}
                    />
                </Space>
            </div>
            <Divider style={{width: "calc(100% + 48px)", margin: "20px 0px 20px -24px"}} />
            <Row justify="end">
                <Space>
                    <Button
                        htmlType="cancel"
                        className={styles.buttonWidth}
                        style={{marginRight: "8px"}}
                        onClick={() => setIsModalOpenExpired(false)}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            exportInvalidCode();
                        }}
                        className={styles.buttonWidth}
                    >
                        Export
                    </Button>
                </Space>
            </Row>
        </Flex>
    );
};

const ChildComponent = ({selectedKeys}) => {
    // 在这里你可以使用 selectedKeys 来显示被选中的值
    return (
        <div className={styles.titleSize2}>
            {selectedKeys.length > 0 ? <p>{selectedKeys.join(", ")}</p> : <p>License Key 1</p>}
        </div>
    );
};

export const ImportLicenseModal = ({isModalOpen, onCancelFunc, handleCheckCurrentLicenseType}) => {
    const [formInstance] = Form.useForm();
    const [isShowCopy, setIsShowCopy] = useState(true);
    const [isShowAdd, setIsShowAdd] = useState(false);
    const [isSwitchModalOpen, setIsSwitchModalOpen] = useState(false);
    const [tableData, setTableData] = useState([]);
    const [isShowSpin, setIsShowSpin] = useState(false);

    const switchColumns = [
        {title: "Host Name", dataIndex: "host_name", sorter: (a, b) => a.host_name.localeCompare(b.host_name)},
        {title: "SN/Service Tag", dataIndex: "sn", sorter: (a, b) => a.sn.localeCompare(b.sn)},
        {
            title: "Model",
            dataIndex: "platform_model",
            sorter: (a, b) => a.platform_model.localeCompare(b.platform_model)
        },
        {title: "HardwareID", dataIndex: "hwid", sorter: (a, b) => a.hwid.localeCompare(b.hwid)},
        {title: "Status", dataIndex: "status", sorter: (a, b) => a.status.localeCompare(b.status)},
        {title: "Virtual Mgmt IP", dataIndex: "mgt_ip", sorter: (a, b) => a.mgt_ip.localeCompare(b.mgt_ip)},
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    confirmModalAction(
                                        `Please confirm you want to remove switch ${record.hwid}`,
                                        async () => {
                                            let fileContent;
                                            if (formInstance.getFieldsValue().licensemethod === "Copy") {
                                                fileContent = formInstance.getFieldsValue().licensekey;
                                            } else {
                                                const file = formInstance.getFieldsValue().licensekey.target.files[0];
                                                fileContent = await readFileContent(file);
                                            }
                                            const response = await delSwitchWithCheckLic(fileContent, record.hwid);
                                            if (response.status !== 200) {
                                                message.error(response.info);
                                            } else {
                                                message.success(response.info);
                                                const newData = tableData.data.filter(
                                                    item => item.hwid !== record.hwid
                                                );
                                                const data = {
                                                    data: newData,
                                                    total: newData.length
                                                };
                                                setTableData(data);
                                            }
                                        }
                                    );
                                }}
                            >
                                remove
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const readFileContent = file => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                resolve(reader.result);
            };
            reader.onerror = error => {
                reject(error);
            };
            reader.readAsText(file);
        });
    };

    const onSubmit = async values => {
        let response;
        setIsShowSpin(true);
        if (values.licensemethod === "Copy") {
            response = await importLicense(values.licensekey);
        } else {
            response = await importLicenseFile(values);
        }
        setIsShowSpin(false);
        if (response.status !== 200) {
            message.error(response.msg);
            if (response.invalidSwitch) {
                setIsSwitchModalOpen(true);

                const data = {
                    data: response.invalidSwitch,
                    total: response.invalidSwitch.length
                };
                setTableData(data);
            } else {
                onCancel();
            }
        } else {
            message.success(response.msg);
            onCancel();
        }
        handleCheckCurrentLicenseType().then();
    };

    const onCancel = async () => {
        await onCancelFunc();
    };

    useEffect(() => {
        if (isModalOpen) {
            setIsShowCopy(true);
            setIsShowAdd(false);
            formInstance.resetFields();
        }
    }, [isModalOpen]);

    return (
        <>
            <AmpConCustomModal
                modalClass="ampcon-custom-modal-style import-license-style"
                title="Import"
                onCancel={onCancel}
                childItems={
                    <Form
                        layout="horizontal"
                        form={formInstance}
                        onFinish={onSubmit}
                        labelAlign="left"
                        labelCol={{span: 6}}
                        style={{minHeight: "268px"}}
                    >
                        <Form.Item name="licensemethod" label="License Method" initialValue="Copy">
                            <Radio.Group
                                initialValue="Copy"
                                onChange={e => {
                                    if (e.target.value === "Copy") {
                                        setIsShowCopy(true);
                                        setIsShowAdd(false);
                                        formInstance.setFieldValue("licensekey", "");
                                    } else {
                                        setIsShowAdd(true);
                                        setIsShowCopy(false);
                                        formInstance.setFieldValue("licensekey", "");
                                    }
                                }}
                            >
                                <Radio value="Copy">Copy License.txt</Radio>
                                <Radio value="Add">Add License.lic</Radio>
                            </Radio.Group>
                        </Form.Item>
                        {isShowCopy && (
                            <Form.Item
                                name="licensekey"
                                label="License Key"
                                rules={[{required: true, message: "Please input license key!"}]}
                                style={{minHeight: "227.23px"}}
                            >
                                <Input.TextArea style={{width: "280px", height: "230px"}} />
                            </Form.Item>
                        )}
                        {isShowAdd && (
                            <>
                                {" "}
                                <Form.Item
                                    name="licensekey"
                                    label="License Key"
                                    rules={[{required: true, message: "Please select license file!"}]}
                                    valuePropName="file"
                                    style={{minHeight: "227.23px"}}
                                >
                                    <Input type="file" aria-required="true" style={{width: "280px"}} />
                                </Form.Item>
                            </>
                        )}
                        <Divider style={{width: 700, marginLeft: "-40px"}} />
                        <Row justify="end">
                            <Space>
                                <Button className={styles.buttonWidth} onClick={onCancel} style={{marginRight: "8px"}}>
                                    Cancel
                                </Button>
                                <Button type="primary" htmlType="submit" className={styles.buttonWidth}>
                                    Apply
                                </Button>
                            </Space>
                        </Row>
                    </Form>
                }
                isModalOpen={isModalOpen}
            />
            <AmpConCustomModal
                modalClass="ampcon-max-modal"
                title="InvalidSwitch"
                onCancel={() => {
                    setIsSwitchModalOpen(false);
                }}
                childItems={
                    <>
                        <AmpConCustomStaticTable columns={switchColumns} data={tableData} />
                        <Divider />
                        <Row justify="end">
                            <Space>
                                <Button
                                    className={styles.buttonWidth}
                                    onClick={() => {
                                        setIsSwitchModalOpen(false);
                                    }}
                                >
                                    Close
                                </Button>
                            </Space>
                        </Row>
                    </>
                }
                isModalOpen={isSwitchModalOpen}
            />
            <Spin spinning={isShowSpin} fullscreen>
                <div>Loading...</div>
            </Spin>
        </>
    );
};

const LicenseManagement = () => {
    const tableRef = useRef(null);
    const [isModalOpenImport, setIsModalOpenImport] = useState(false);
    const [isModalOpenExpired, setIsModalOpenExpired] = useState(false);
    const [selectedLicenseFile, setSelectedLicenseFile] = useState(["All Licenses"]);
    const [selectedLicenseType, setSelectedLicenseType] = useState("");
    const [licenseInfo, setLicenseInfo] = useState({});
    const [treeData, setTreeData] = useState([
        {
            title: "All Licenses",
            key: "All Licenses",
            children: []
        }
    ]);
    const [tableData, setTableData] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [invalidCode, setInvalidCode] = useState("");
    const [currentLicenseType, setCurrentLicenseType] = useState("none");
    const navigate = useNavigate();

    const columns = [
        {title: "Hardware ID", dataIndex: "hardware_id", sorter: (a, b) => a.hardware_id.localeCompare(b.hardware_id)},
        {
            title: "Model",
            dataIndex: "platform_model",
            sorter: (a, b) => a.platform_model.localeCompare(b.platform_model)
        },
        {
            title: "License Status",
            dataIndex: "license_file_status",
            sorter: (a, b) => a.license_file_status.localeCompare(b.license_file_status),
            render: (_, record) => {
                return (
                    <div>
                        <Space size="small">{statusIcon(record.license_file_status)}</Space>
                    </div>
                );
            }
        },
        {
            title: "Expiration Date",
            dataIndex: "expiration_date",
            sorter: (a, b) => a.expiration_date.localeCompare(b.expiration_date)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={record.license_file_status === "invalid" ? () => getInvalidCode(record) : null}
                                style={{
                                    color: record.license_file_status === "invalid" ? "#14C9BB" : "#B3BBC8",
                                    cursor: record.license_file_status === "invalid" ? "pointer" : "not-allowed"
                                }}
                                disabled
                            >
                                Show RevokeCode
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const statusIcon = status => {
        if (status === "valid") {
            return <Tag className={styles.avaliableTag}>Avaliable</Tag>;
        }
        if (status === "expired") {
            return <Tag className={styles.expiredTag}>Expired</Tag>;
        }
        if (status === "invalid") {
            return <Tag className={styles.invalidTag}>Invalid</Tag>;
        }
    };

    const getInvalidCode = async record => {
        // 目前只做了单文件hwid失效，跨文件未实现
        const response = await invalidateLicense(record.license_file_id, [record.hardware_id]);
        if (response.status !== 200) {
            message.error(response.msg);
        } else {
            setInvalidCode(response.invalidCode);
            setIsModalOpenExpired(true);
        }
    };

    const handleSearch = e => {
        const newChildren = [];
        if (e && e.trim().length !== 0) {
            Object.keys(licenseInfo).forEach(key => {
                if (key.toLowerCase().includes(e.toLowerCase())) {
                    newChildren.push({
                        title: key,
                        key,
                        isLeaf: true
                    });
                }
            });
        } else {
            Object.keys(licenseInfo).forEach(key => {
                newChildren.push({
                    title: key,
                    key,
                    isLeaf: true
                });
            });
        }

        setTreeData(prevTreeData => [
            {
                ...prevTreeData[0],
                children: newChildren
            }
        ]);
    };

    const onSelectTree = (selectedKeys, selectedNodes) => {
        setSelectedLicenseFile(selectedKeys);
        if (licenseInfo[selectedKeys]) {
            const data = {
                [selectedKeys]: licenseInfo[selectedKeys]
            };
            formatTableData(data);
            if (selectedNodes.node?.license_type === "trial") {
                setSelectedLicenseType("Trial");
            } else if (selectedNodes.node?.license_type === "standard") {
                setSelectedLicenseType("Standard");
            } else {
                setSelectedLicenseType(selectedNodes.node?.license_type);
            }
        } else if (selectedKeys[0] === "All Licenses") {
            formatTableData(licenseInfo);
        }
        setSelectedRowKeys([]);
        setSelectedRows([]);
        tableRef.current.clearSelectedRow();
    };

    const formatTableData = licenseInfo => {
        const transformedData = [];
        for (const [licenseId, license] of Object.entries(licenseInfo)) {
            const {id, license_info, create_time, license_type} = license;

            for (const [hardwareId, licenseDetails] of Object.entries(license_info)) {
                transformedData.push({
                    id: licenseId + hardwareId,
                    license_file_id: id,
                    license_file: licenseId,
                    hardware_id: hardwareId,
                    license_file_status: licenseDetails.status.toLowerCase(),
                    valid_date: create_time,
                    expiration_date: licenseDetails.expire_time,
                    platform_model: licenseDetails.platform_model,
                    license_type
                });
            }
        }
        const data = {
            data: transformedData,
            total: transformedData.length
        };
        setTableData(data);
    };

    const getLicenseInfoApi = async () => {
        const license = {};
        const tree = [];
        const response = await getLicenseInfo();
        if (response.status !== 200) {
            message.error("Get License Info Failed");
        } else {
            for (const item of response.data) {
                license[item.license_id] = {
                    create_time: item.create_time,
                    id: item.id,
                    license_type: item.license_type,
                    license_info: item.license_info
                };
                tree.push({
                    title: item.license_id,
                    key: item.license_id,
                    license_type: item.license_type,
                    isLeaf: true
                });
            }
        }
        setTreeData(prevTreeData => [
            {
                ...prevTreeData[0],
                children: tree
            }
        ]);

        if (license[selectedLicenseFile]) {
            const data = {
                [selectedLicenseFile]: license[selectedLicenseFile]
            };
            formatTableData(data);
        } else if (selectedLicenseFile[0] === "All Licenses") {
            formatTableData(license);
        } else {
            formatTableData(license);
        }
        setLicenseInfo(license);
        setSelectedRowKeys([]);
        setSelectedRows([]);
        tableRef.current.clearSelectedRow();
    };

    async function handleCheckCurrentLicenseType() {
        const res = await checkCurrentLicenseType();
        if (res.status === 200) {
            setCurrentLicenseType(res.data);
        }
    }

    useEffect(() => {
        getLicenseInfoApi().then();
        handleCheckCurrentLicenseType().then();
    }, []);

    return (
        <Card className={styles.cardHeight}>
            <ImportLicenseModal
                handleCheckCurrentLicenseType={handleCheckCurrentLicenseType}
                onCancelFunc={() => {
                    setIsModalOpenImport(false);
                    getLicenseInfoApi().then();
                }}
                isModalOpen={isModalOpenImport}
            />
            <AmpConCustomModal
                modalclass="ampcon-middle-modal"
                title=" Invalid License"
                onCancel={() => {
                    setIsModalOpenExpired(false);
                }}
                childItems={<InvalidItems setIsModalOpenExpired={setIsModalOpenExpired} invalidCode={invalidCode} />}
                isModalOpen={isModalOpenExpired}
            />
            <Flex vertical style={{height: "100%"}}>
                <Flex vertical>
                    <h2 style={{margin: "8px 0 20px"}}>License Management</h2>
                    <Space size={16}>
                        <Button
                            type="primary"
                            style={{height: "32px", width: "98px"}}
                            onClick={() => {
                                setIsModalOpenImport(true);
                            }}
                        >
                            <Icon component={importModalSvg} />
                            Import
                        </Button>
                        <Button
                            style={{height: "32px", width: "147px"}}
                            onClick={() => {
                                navigate("quick_activate");
                            }}
                        >
                            <Icon component={quickActivateSvg} />
                            Quick Activate
                        </Button>
                        {currentLicenseType === "trial" && (
                            <Button
                                style={{height: "32px", width: "205px"}}
                                onClick={() => {
                                    navigate("convert_trial_license");
                                }}
                            >
                                <Icon component={trialConvertStandardColorSvg} />
                                Trial License Conversion
                            </Button>
                        )}
                        <Button
                            style={{height: "32px", width: "104px"}}
                            onClick={() => {
                                getLicenseInfoApi().then();
                                handleCheckCurrentLicenseType().then();
                                message.success("license info refresh success");
                            }}
                        >
                            <Icon component={refreshSvg} />
                            Refresh
                        </Button>
                    </Space>
                    <p className={styles.titleSize1}>License Expiration Code</p>
                </Flex>

                <Flex layout="horizontal" style={{flex: 1, marginBottom: "12px"}}>
                    <Card className={styles.cardStyle1}>
                        <div style={{marginTop: "14px"}}>
                            <Input
                                onChange={e => handleSearch(e.target.value)}
                                placeholder="Search"
                                prefix={<Icon component={searchSvg} />}
                                allowClear
                                style={{width: "300px", height: "32px"}}
                            />
                        </div>
                        <div style={{marginTop: "14px"}}>
                            <DirectoryTree
                                defaultExpandedKeys={["All Licenses"]}
                                onSelect={onSelectTree}
                                treeData={treeData}
                                selectedKeys={selectedLicenseFile}
                            />
                        </div>
                    </Card>
                    <Card className={styles.cardStyle2}>
                        <div className={styles.container}>
                            <ChildComponent selectedKeys={selectedLicenseFile} />
                            <Button
                                className={styles.rightButton}
                                disabled={selectedRowKeys.length === 0}
                                onClick={() =>
                                    confirmModalAction("Are you sure want to invalid license?", async () => {
                                        // 目前只做了单文件hwid失效，跨文件未实现
                                        const {license_file_id} = selectedRows[0];
                                        const hwids = selectedRows.map(item => item.hardware_id);
                                        const response = await invalidateLicense(license_file_id, hwids);
                                        if (response.status !== 200) {
                                            message.error(response.msg);
                                        } else {
                                            setInvalidCode(response.invalidCode);
                                            setIsModalOpenExpired(true);
                                        }
                                        getLicenseInfoApi().then();
                                    })
                                }
                            >
                                Invalid License
                            </Button>
                        </div>
                        <Divider className={styles.divider2} />
                        <AmpConCustomStaticTable
                            ref={tableRef}
                            columns={columns}
                            extraButton={
                                <div className={styles.titleSize2}>
                                    {tableData.data && selectedLicenseFile[0] !== "All Licenses" ? (
                                        <p style={{margin: "0px"}}>License Type: {selectedLicenseType}</p>
                                    ) : (
                                        <p />
                                    )}
                                </div>
                            }
                            rowSelection={{
                                selectedRowKeys,
                                selectedRows,
                                onChange: (keys, rows) => {
                                    setSelectedRowKeys(keys);
                                    setSelectedRows(rows);
                                },
                                getCheckboxProps: record => {
                                    return {
                                        disabled:
                                            selectedLicenseFile[0] === "All Licenses" ||
                                            record.license_file_status === "invalid"
                                    };
                                }
                            }}
                            data={tableData}
                        />
                    </Card>
                </Flex>
            </Flex>
        </Card>
    );
};
export default LicenseManagement;
