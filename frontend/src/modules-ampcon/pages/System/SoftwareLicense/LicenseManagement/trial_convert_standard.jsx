import React, {useEffect, useRef, useState} from "react";
import {QuestionCircleOutlined} from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/trial_convert_standard.module.scss";
import {But<PERSON>, Card, Divider, message, Space, Spin, Tooltip} from "antd";
import {CustomSelect} from "@/modules-ampcon/components/custom_form";
import {
    AmpConCustomModal,
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {
    ApplyToSwitchesSvg,
    refreshSvg,
    trialConvertStandardDisabledSvg,
    trialConvertStandardSvg,
    warningSvg
} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import {convertTrialLicense, getLicenseConvertSwitch} from "@/modules-ampcon/apis/config_api";
import UserExpirationDateSelect from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/user_expiration_date_select";
import {useNavigate} from "react-router-dom";

const TrialConvertStandard = () => {
    const switchRef = useRef(null);
    const userExpirationDateSelectRef = useRef(null);
    const [currentUser, setCurrentUser] = useState("");
    const [currentExpireDate, setCurrentExpireDate] = useState("");
    const [currentQuantity, setCurrentQuantity] = useState(null);
    const [initialized, setInitialized] = useState(false);
    const [isShowSpin, setIsShowSpin] = useState(false);
    const [tableSelectedExpireDates, setTableSelectedExpireDates] = useState({});
    const [showModal, setShowModal] = useState(false);
    const [filterExpireDates, setFilterExpireDates] = useState([]);
    const [disabledExpireDateFlag, setDisabledExpireDateFlag] = useState({});
    const [isDisableConvert, setIsDisableConvert] = useState(true);

    const switchColumns = [
        createColumnConfig("Switch Model", "platform_model", TableFilterDropdown),
        createColumnConfig("Switch SN", "sn", TableFilterDropdown),
        createColumnConfig("Hardware ID", "hwid", TableFilterDropdown),
        {
            title: "Expiration Date",
            width: "400px",
            render: (_, record) => {
                return (
                    <CustomSelect
                        key={`${record.id}-${tableSelectedExpireDates[record.id]}`}
                        options={filterExpireDates}
                        placeholder="Select Expiration Date"
                        style={{width: "100%"}}
                        defaultValue={tableSelectedExpireDates[record.id]}
                        allowClear
                        onChange={value => {
                            setTableSelectedExpireDates(prev => ({
                                ...prev,
                                [record.id]: value
                            }));
                        }}
                    />
                );
            }
        }
    ].filter(Boolean);
    const switchSearchFieldsList = ["platform_model", "sn", "hwid"];
    const switchMatchFieldsList = [
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "hwid", matchMode: "fuzzy"}
    ];
    const navigate = useNavigate();

    useEffect(() => {
        if (currentExpireDate && currentQuantity !== null) {
            const alreadyUsedCount = Object.values(tableSelectedExpireDates).filter(
                date => date === currentExpireDate
            ).length;

            const newDisabledExpireDateFlag = {
                ...disabledExpireDateFlag,
                [currentExpireDate]: alreadyUsedCount >= currentQuantity
            };

            setDisabledExpireDateFlag(newDisabledExpireDateFlag);

            if (userExpirationDateSelectRef.current) {
                const newExpireDates = [];
                for (const e of userExpirationDateSelectRef.current.getAvailableExpireDates()) {
                    if (!newDisabledExpireDateFlag[e]) {
                        newExpireDates.push(e);
                    }
                }
                setFilterExpireDates(newExpireDates);
            }
        }
    }, [currentExpireDate, currentQuantity, tableSelectedExpireDates]);

    useEffect(() => {
        let count = 0;
        for (const e of Object.values(tableSelectedExpireDates)) {
            if (e) {
                count += 1;
            }
        }
        setIsDisableConvert(count === 0);
    }, [tableSelectedExpireDates]);

    const handleApplyExpirationDate = () => {
        const data = switchRef.current.getTableData();

        const alreadyUsedCount = Object.values(tableSelectedExpireDates).filter(
            date => date === currentExpireDate
        ).length;
        const remainToAssign = Math.max(currentQuantity - alreadyUsedCount, 0);
        const newExpireDates = {};
        let assigned = 0;

        for (const row of data) {
            if (assigned >= remainToAssign) break;
            if (!tableSelectedExpireDates[row.id]) {
                newExpireDates[row.id] = currentExpireDate;
                assigned += 1;
            }
        }

        setTableSelectedExpireDates(prev => ({
            ...prev,
            ...newExpireDates
        }));
    };

    const handleRefresh = async () => {
        setIsShowSpin(true);
        setTableSelectedExpireDates({});
        await userExpirationDateSelectRef.current.refreshLicenseInfo();
        switchRef.current.refreshTable();
        setIsShowSpin(false);
    };

    const authenticationFailedMessage = (
        <Space direction="vertical" size={0} align="start">
            <div>The License Portal authentication failed.</div>
            <div>Please try again after updating license portal information in the global system configuration.</div>
        </Space>
    );

    const handleConvert = async () => {
        setShowModal(false);
        setIsShowSpin(true);
        const data = switchRef.current
            .getTableData()
            .filter(row => !!tableSelectedExpireDates[row.id])
            .map(row => {
                return {
                    ...row,
                    expire_date: tableSelectedExpireDates[row.id]
                };
            });
        const res = await convertTrialLicense(data);
        if (res.status === 200) {
            message.success(res.info);
            navigate(-1);
        } else {
            message.error(res.status === 401 ? authenticationFailedMessage : res.info);
        }
        setIsShowSpin(false);
    };

    return (
        <Card style={{display: "flex", flex: 1}}>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            <AmpConCustomModal
                title="Note"
                isModalOpen={showModal}
                onCancel={() => {
                    setShowModal(false);
                }}
                childItems={
                    <Space style={{margin: "10px 0 0 0"}}>
                        <Icon component={warningSvg} />
                        <div>
                            <div>Are you sure you want to convert trial licenses on these switches?</div>
                            <ul style={{listStyle: "disc", padding: "0 0 0 20px", margin: 0}}>
                                <li>
                                    On AmpCon-Campus, you have only one chance to convert trial licenses to standard
                                    licenses.
                                </li>
                                <li>
                                    For switches with a trial license, if no expiration dates are specified, the
                                    switches will be removed from AmpCon-Campus after you perform this operation.
                                </li>
                                <li>
                                    If more switches need trial license conversion, perform the operation in the License
                                    Portal.
                                </li>
                            </ul>
                        </div>
                    </Space>
                }
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button
                        key="cancel"
                        onClick={() => {
                            setShowModal(false);
                        }}
                        style={{width: "100px"}}
                    >
                        No
                    </Button>,
                    <Button style={{width: "100px"}} key="ok" type="primary" onClick={handleConvert}>
                        Yes
                    </Button>
                ]}
            />
            <div style={{display: "flex", alignItems: "center"}}>
                <Tooltip
                    placement="right"
                    title={
                        "Convert trial licenses to standard licenses.\n" +
                        "On AmpCon-Campus, you have only once chance to convert trial licenses to standard licenses."
                    }
                >
                    <Space>
                        <h2 style={{margin: "8px 0 20px"}}>Convert Trial License</h2>
                        <QuestionCircleOutlined className="questioncircle-color" />
                    </Space>
                </Tooltip>
            </div>
            <UserExpirationDateSelect
                ref={userExpirationDateSelectRef}
                setInitialized={setInitialized}
                showLicenseTypes={["standard"]}
                currentUser={currentUser}
                setCurrentUser={setCurrentUser}
                currentQuantity={currentQuantity}
                setCurrentQuantity={setCurrentQuantity}
                currentExpireDate={currentExpireDate}
                setCurrentExpireDate={setCurrentExpireDate}
                expirationDateLabel="Available Expiration Date"
            />
            {initialized && (
                <AmpConCustomTable
                    columns={switchColumns}
                    searchFieldsList={switchSearchFieldsList}
                    extraButton={
                        <Space size={16}>
                            <Button
                                type="primary"
                                onClick={() => setShowModal(true)}
                                style={{height: "32px", width: "108px"}}
                                disabled={isDisableConvert}
                            >
                                <Icon
                                    component={
                                        isDisableConvert ? trialConvertStandardDisabledSvg : trialConvertStandardSvg
                                    }
                                    className={styles.buttonIcon}
                                />
                                Convert
                            </Button>
                            <Button onClick={handleRefresh} style={{height: "32px", width: "104px"}}>
                                <Icon component={refreshSvg} className={styles.buttonIcon} />
                                Refresh
                            </Button>
                            <Button
                                onClick={handleApplyExpirationDate}
                                style={{height: "32px", width: "192px"}}
                                disabled={disabledExpireDateFlag[currentExpireDate]}
                            >
                                <Icon component={ApplyToSwitchesSvg} className={styles.buttonIcon} />
                                Apply Expiration Date
                            </Button>
                        </Space>
                    }
                    pagination={false}
                    matchFieldsList={switchMatchFieldsList}
                    fetchAPIInfo={getLicenseConvertSwitch}
                    ref={switchRef}
                />
            )}
        </Card>
    );
};
export default TrialConvertStandard;
