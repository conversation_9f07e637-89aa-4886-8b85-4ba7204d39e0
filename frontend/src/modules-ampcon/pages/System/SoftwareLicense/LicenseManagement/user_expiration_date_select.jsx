import {Flex, Form, Input, message, Select, Space} from "antd";
import {CustomSelect} from "@/modules-ampcon/components/custom_form";
import React, {forwardRef, useEffect, useImperativeHandle, useState} from "react";
import {
    checkCurrentLicenseType,
    getConfigAccountInfo,
    getLicenseConnectState,
    refreshSystemConfigLicenseInfo
} from "@/modules-ampcon/apis/config_api";

const UserExpirationDateSelect = forwardRef(
    (
        {
            setInitialized,
            showLicenseTypes,
            currentUser,
            setCurrentUser,
            currentExpireDate,
            setCurrentExpireDate,
            currentQuantity,
            setCurrentQuantity,
            expirationDateLabel
        },
        ref
    ) => {
        const [expireDateList, setExpireDateList] = useState([]);
        const [quantityList, setQuantityList] = useState([]);
        const [licenseInfoData, setLicenseInfoData] = useState([]);
        const [isDataLoaded, setIsDataLoaded] = useState(false);
        const [showConfigNotValidMessage, setShowConfigNotValidMessage] = useState(false);
        const [currentLicenseType, setCurrentLicenseType] = useState("");

        const authenticationFailedMessage = (
            <Space direction="vertical" size={0} align="start">
                <div>The License Portal authentication failed.</div>
                <div>
                    Please try again after updating license portal information in the global system configuration.
                </div>
            </Space>
        );

        async function handleCheckCurrentLicenseType() {
            const res = await checkCurrentLicenseType();
            if (res.status === 200) {
                setCurrentLicenseType(res.data);
            }
        }

        useImperativeHandle(ref, () => ({
            refreshLicenseInfo: async () => {
                setIsDataLoaded(false);
                setCurrentUser(null);
                try {
                    await handleCheckCurrentLicenseType();
                    const res = await refreshSystemConfigLicenseInfo();
                    if (res.status === 200) {
                        message.success(res.info);
                    } else {
                        message.error(res.status === 401 ? authenticationFailedMessage : res.info);
                    }
                    await fetchLicenseInfo();
                } catch (error) {
                    message.error("Error during refresh:", error);
                }
            },
            getAvailableExpireDates: () => {
                return expireDateList.filter((_, index) => quantityList[index] > 0);
            }
        }));

        useEffect(() => {
            handleCheckCurrentLicenseType().then();
            fetchLicenseInfo().then();
        }, []);

        useEffect(() => {
            if (!currentUser) {
                return;
            }
            const selectedLicenseInfo = licenseInfoData.find(item => item.license_portal_username === currentUser);
            if (!selectedLicenseInfo) {
                return;
            }
            if (!showConfigNotValidMessage) {
                getLicenseConnectState(
                    selectedLicenseInfo.license_portal_url,
                    selectedLicenseInfo.license_portal_username,
                    "********",
                    selectedLicenseInfo.system_config_name
                ).then(res => {
                    if (res.status !== 200) {
                        message.warning(authenticationFailedMessage).then();
                    }
                    setShowConfigNotValidMessage(true);
                });
            }

            const dates = [];
            const quantities = [];
            selectedLicenseInfo.license_info.forEach(info => {
                const {expire_date, standard_license_count, trial_license_count} = info;
                if (
                    (!showLicenseTypes && currentLicenseType !== "trial") ||
                    (showLicenseTypes && showLicenseTypes.includes("standard"))
                ) {
                    dates.push(expire_date);
                    quantities.push(standard_license_count);
                }
                if (
                    (!showLicenseTypes && currentLicenseType !== "standard") ||
                    (showLicenseTypes && showLicenseTypes.includes("trial"))
                ) {
                    dates.push(`${expire_date} <Trial>`);
                    quantities.push(trial_license_count);
                }
            });
            setExpireDateList(dates);
            setQuantityList(quantities);
            setCurrentExpireDate(dates.length > 0 ? dates[0] : null);
        }, [currentUser, currentLicenseType]);

        useEffect(() => {
            const index = expireDateList.findIndex(date => date === currentExpireDate);
            setCurrentQuantity(index !== -1 ? quantityList[index] : null);
        }, [expireDateList, currentExpireDate, quantityList]);

        const fetchLicenseInfo = async () => {
            try {
                const response = await getConfigAccountInfo();
                if (response.status === 200) {
                    const users = response.data.map(item => {
                        return item.license_portal_username;
                    });
                    if (users.length > 0) {
                        setCurrentUser(users[0]);
                    }
                    setLicenseInfoData(response.data);
                    setInitialized(true);
                }
                setIsDataLoaded(true);
            } catch (error) {
                console.error("Error fetching quick activate data:", error);
            }
        };

        return (
            <Form>
                <Flex gap="80px" style={{width: "100%"}}>
                    <Form.Item label="License Portal User" style={{width: "408px", height: "36px"}}>
                        {isDataLoaded ? (
                            <Input value={currentUser} readOnly />
                        ) : (
                            <Input style={{width: "100%"}} readOnly />
                        )}
                    </Form.Item>
                    <Form.Item
                        label={expirationDateLabel || "Expiration Date"}
                        style={{width: expirationDateLabel ? "445px" : "387px", height: "36px"}}
                    >
                        {isDataLoaded ? (
                            <CustomSelect
                                options={expireDateList}
                                onChange={value => setCurrentExpireDate(value)}
                                placeholder="Select Expiration Date"
                                defaultValue={currentExpireDate}
                            />
                        ) : (
                            <Select style={{width: "100%"}} />
                        )}
                    </Form.Item>
                    <Form.Item label="Quantity" style={{width: "345px", height: "36px"}}>
                        {isDataLoaded ? (
                            <Input value={currentQuantity} readOnly style={{backgroundColor: "#f5f5f5"}} />
                        ) : (
                            <Input readOnly style={{backgroundColor: "#f5f5f5"}} />
                        )}
                    </Form.Item>
                </Flex>
            </Form>
        );
    }
);

export default UserExpirationDateSelect;
