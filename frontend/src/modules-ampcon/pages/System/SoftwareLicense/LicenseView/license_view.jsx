import {Card, message, Space, Tag} from "antd";
import {useEffect, useState} from "react";
import {AmpConCustomStaticTable, TableSelectFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {BasePieEcharts, StaticBarEcharts} from "@/modules-ampcon/components/echarts_common";
import {getLicenseInfo} from "@/modules-ampcon/apis/dashboard_api";
import styles from "./license_view.module.scss";

const LicenseView = () => {
    const [licenseInfo, setLincenseInfo] = useState({});
    const [barData, setBarData] = useState({});
    const [pieData, setPieData] = useState([]);
    const [filterDefaultValue, setFilterDefaultValue] = useState("");

    const statusIcon = status => {
        if (status === "valid") {
            return <Tag className={styles.avaliableTag}>Avaliable</Tag>;
        }
        if (status === "expired") {
            return <Tag className={styles.expiredTag}>Expired</Tag>;
        }
        if (status === "invalid") {
            return <Tag className={styles.invalidTag}>Invalid</Tag>;
        }
    };

    const filterOptions = [
        {
            label: "Normal",
            value: "normal"
        },
        {
            label: "Abnormal",
            value: "abnormal"
        },
        {
            label: "Valid",
            value: "valid"
        },
        {
            label: "Invalid",
            value: "invalid"
        },
        {
            label: "Expired",
            value: "expired"
        }
    ];

    const licenseColumns = [
        {
            title: "License ID",
            dataIndex: "license_file",
            sorter: (a, b) => a.license_file.localeCompare(b.license_file)
        },
        {title: "Hardware ID", dataIndex: "hardware_id", sorter: (a, b) => a.hardware_id.localeCompare(b.hardware_id)},
        {
            title: "Model Name",
            dataIndex: "platform_model",
            sorter: (a, b) => a.platform_model.localeCompare(b.platform_model)
        },
        {
            title: "License Type",
            dataIndex: "license_type",
            sorter: (a, b) => a.license_type.localeCompare(b.license_type)
        },
        {
            title: "License File Status",
            dataIndex: "license_file_status",
            filterDropdown: props => TableSelectFilterDropdown({...props, filterOptions, filterDefaultValue}),
            onFilter: () => true,
            sorter: (a, b) => a.license_file_status.localeCompare(b.license_file_status),
            render: (_, record) => {
                return (
                    <div>
                        <Space size="small">{statusIcon(record.license_file_status)}</Space>
                    </div>
                );
            }
        },
        {title: "Valid Date", dataIndex: "valid_date", sorter: (a, b) => a.valid_date.localeCompare(b.valid_date)},
        {
            title: "Expiration Date",
            dataIndex: "expiration_date",
            sorter: (a, b) => a.expiration_date.localeCompare(b.expiration_date)
        }
    ];

    const formatBarData = licenseInfo => {
        const invalidCount = licenseInfo.data.filter(item => item.license_file_status === "invalid").length;
        const expiredCount = licenseInfo.data.filter(item => item.license_file_status === "expired").length;
        const data = {
            keys: ["All", "Invalid", "Expired"],
            values: [licenseInfo.total, invalidCount, expiredCount]
        };
        setBarData(data);
    };

    const formatPieData = licenseInfo => {
        const normalCount = licenseInfo.data.filter(item => item.license_file_status === "valid").length;
        const abnormalCount = licenseInfo.data.filter(item => item.license_file_status === "invalid").length;
        const expiredCount = licenseInfo.data.filter(item => item.license_file_status === "expired").length;
        const data = [
            {name: `Normal ${normalCount}`, value: normalCount, label: "Normal"},
            {name: `Abnormal ${abnormalCount}`, value: abnormalCount, label: "Abnormal"},
            {name: `Expired ${expiredCount}`, value: expiredCount, label: "Expired"}
        ];
        setPieData(data);
    };

    const getLicenseInfoApi = async () => {
        const transformedData = [];
        const response = await getLicenseInfo();
        if (response.status !== 200) {
            message.error("Get License Info Failed");
        } else {
            for (const item of response.data) {
                const {license_id, license_info, license_type, create_time} = item;

                for (const [hardwareId, licenseDetails] of Object.entries(license_info)) {
                    transformedData.push({
                        id: license_id + hardwareId,
                        license_file: license_id,
                        hardware_id: hardwareId,
                        license_file_status: licenseDetails.status.toLowerCase(),
                        valid_date: create_time,
                        expiration_date: licenseDetails.expire_time,
                        platform_model: licenseDetails.platform_model,
                        license_type
                    });
                }
            }
        }

        const data = {
            data: transformedData,
            total: transformedData.length
        };
        formatBarData(data);
        formatPieData(data);
        setLincenseInfo(data);
    };

    const filterFunc = (data, filterValue) => {
        switch (filterValue.license_file_status?.[0]?.toLowerCase()) {
            case "abnormal":
            case "invalid":
                return data.filter(item => item.license_file_status === "invalid");
            case "expired":
                return data.filter(item => item.license_file_status === "expired");
            case "valid":
            case "normal":
                return data.filter(item => item.license_file_status === "valid");
            default:
                return data;
        }
    };

    useEffect(() => {
        getLicenseInfoApi().then();
    }, []);

    return (
        <div className={styles.licenseView}>
            <Card
                title={<div className={styles.card_title}>License File Status</div>}
                style={{height: "100%", minHeight: "278px"}}
                className="barStyle"
            >
                <StaticBarEcharts
                    data={barData}
                    colorList={["#14C9BB"]}
                    onClicked={param => {
                        setFilterDefaultValue(param.name.toLowerCase() === "all" ? "" : param.name);
                    }}
                    width="15%"
                />
            </Card>
            <Card title={<div className={styles.card_title}>License Usage</div>} className="pieStyle">
                <BasePieEcharts
                    name="License Usage"
                    seriesData={pieData}
                    chartType="ring"
                    colorList={["#14C9BB", "#FFBB00", "#FF6F6F"]}
                    maxWidth="450px"
                    onClicked={param => {
                        setFilterDefaultValue(param.data.label);
                    }}
                    showPercent={false}
                />
            </Card>
            <Card title={<div className={styles.card_title}>License Information</div>} style={{height: "100%"}}>
                <AmpConCustomStaticTable columns={licenseColumns} data={licenseInfo} filterFunc={filterFunc} />
            </Card>
        </div>
    );
};

export default LicenseView;
