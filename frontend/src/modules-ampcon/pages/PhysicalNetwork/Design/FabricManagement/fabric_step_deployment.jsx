import React, {useRef, useEffect, useState} from "react";
import {useLocation} from "react-router-dom";

import {Space, Modal, Divider, Flex, Tag, message, Table, Input, Button} from "antd";
import Icon, {SearchOutlined} from "@ant-design/icons";
import {onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";

import {createColumnConfig} from "@/modules-ampcon/components/custom_table";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";

// import API
import {viewFabric, deployment_status} from "@/modules-ampcon/apis/dc_template_api";

const DeploymentStep = ({submitValid, physicalDeviceData}) => {
    const {state} = useLocation();
    const [topoInfo, setTopoInfo] = useState({});
    const [data, setData] = useState([]);
    const [deployData, setDeployData] = useState([]);
    const [chooseLogInfo, setChooseLogInfo] = useState("");
    const [viewLogModal, setViewLogModal] = useState(false);

    const [searchText, setSearchText] = useState("");
    const [filteredData, setFilteredData] = useState(data);

    const handleSort = dataIndex => {
        return (a, b) => {
            const getValue = (obj, keyPath) => {
                return keyPath.split(".").reduce((o, key) => (o ? o[key] : ""), obj);
            };

            const valueA = getValue(a, dataIndex);
            const valueB = getValue(b, dataIndex);

            if (typeof valueA === "string" && typeof valueB === "string") {
                return valueA.localeCompare(valueB);
            }
            return (valueA || 0) - (valueB || 0);
        };
    };

    // 获取topoInfo信息
    const fetchTopoData = async () => {
        try {
            const res = await viewFabric({fabric_topo_id: state.data.id});
            if (res.status === 200) {
                setTopoInfo(res.data);
            }
        } catch (error) {
            // error
        }
    };

    // 获取Deployment Status信息
    const fetchDeploymentStatus = async () => {
        try {
            const res = await deployment_status({fabric_topo_id: state.data.id});
            if (res.status === 200) {
                setDeployData(res.data);
            }
        } catch (error) {
            // error
        }
    };

    const deploymentColumns = [
        // {...createColumnConfig("Fabric", "name")},
        {...createColumnConfig("Sysname", "host_name"), sorter: handleSort("host_name")},
        {
            ...createColumnConfig("Mgmt IP", "mgt_ip"),
            sorter: handleSort("mgt_ip"),
            render: (_, record) => {
                if (!record.mgt_ip) {
                    return null;
                }
                let iconComponent;
                if (record.reachable_status === 0) iconComponent = onlineSvg;
                else if (record.reachable_status === 1) iconComponent = offlineSvg;
                else iconComponent = exclamationSvg;

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.link_ip_addr ? `${record.mgt_ip}/${record.link_ip_addr}` : record.mgt_ip}
                    </Space>
                );
            }
        },
        {...createColumnConfig("Device Type", "role"), sorter: handleSort("role")},
        {
            ...createColumnConfig("Deployment Status", "task_status"),
            sorter: handleSort("task_status"),
            render: (text, record) => {
                let tagClass;
                switch (record.task_status) {
                    case "SUCCEED":
                        tagClass = styles.successTag;
                        break;
                    case "FAILED":
                        tagClass = styles.failedTag;
                        break;
                    case "RUNNING":
                        tagClass = styles.runningTag;
                        break;
                    default:
                        tagClass = styles.pendingTag;
                        break;
                }
                return <Tag className={tagClass}>{record.task_status}</Tag>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="middle" className={styles.actionLink}>
                    <a
                        onClick={() => {
                            setViewLogModal(true);
                            setChooseLogInfo(record);
                        }}
                    >
                        Log
                    </a>
                </Space>
            )
        }
    ];

    const handleSearchChange = value => {
        setSearchText(value);
        const newData = data.filter(record => {
            return (
                record.host_name.toLowerCase().includes(value?.toLowerCase()) ||
                record.mgt_ip.includes(value) ||
                record.role.toLowerCase().includes(value) ||
                record.task_status.toLowerCase().includes(value)
            );
        });

        setFilteredData(newData);
    };
    useEffect(() => {}, []);

    useEffect(() => {
        const fetchData = async () => {
            await fetchTopoData();
            await fetchDeploymentStatus();
        };
        fetchData();

        const intervalId = setInterval(() => {
            fetchDeploymentStatus();
        }, 30000);

        // 清理定时器以防内存泄漏
        return () => clearInterval(intervalId);
    }, [state]);

    useEffect(() => {
        if (Object.keys(topoInfo).length !== 0) {
            const nodes = topoInfo?.fabric_config?.topology?.nodes;
            const newArray = nodes.map(item => ({
                id: deployData.find(updatedItem => updatedItem.logic_device === item.logic_device)?.id || "",
                logic_device: item.logic_device,
                host_name: item.node_info.hostname,
                mgt_ip: physicalDeviceData?.find(obj => obj.sn === item.switch_sn)?.mgt_ip || "",
                reachable_status:
                    physicalDeviceData?.find(obj => obj.sn === item.switch_sn)?.reachable_status !== undefined
                        ? physicalDeviceData.find(obj => obj.sn === item.switch_sn).reachable_status
                        : "",
                link_ip_addr: physicalDeviceData?.find(obj => obj.sn === item.switch_sn)?.link_ip_addr || "",
                role: item.type,
                task_log:
                    deployData.find(updatedItem => updatedItem.logic_device === item.logic_device)?.task_log || "",
                task_name:
                    deployData.find(updatedItem => updatedItem.logic_device === item.logic_device)?.task_name || "",
                task_status:
                    deployData.find(updatedItem => updatedItem.logic_device === item.logic_device)?.task_status ||
                    "PENDING" // "PENDING", "RUNNING", "SUCCEED", "FAILED"
            }));
            setData(newArray);
            setFilteredData(newArray);
            submitValid("Deployment", topoInfo, true, newArray);
        }
    }, [topoInfo, deployData, physicalDeviceData]);

    useEffect(() => {
        if (data) {
            const isSubmit = data.every(item => item.mgt_ip !== "");
            submitValid("Deployment", topoInfo, isSubmit, data);
        }
    }, [data]);

    const paginationConfig = {
        showSizeChanger: true,
        showTotal: (total, range) => {
            const start = range[0];
            const end = range[1];
            return `${start}-${end} of ${total} items`;
        },
        total: filteredData.length
    };

    return (
        <div className={styles.tabsBorder} style={{paddingTop: 24}}>
            <Input
                placeholder="input search text"
                onChange={e => handleSearchChange(e.target.value)}
                allowClear
                prefix={<SearchOutlined />}
                style={{width: 280, float: "right", clear: "both"}}
            />
            <div style={{marginTop: 48}}>
                <Table
                    columns={deploymentColumns}
                    dataSource={filteredData} // 使用过滤后的数据
                    pagination={paginationConfig}
                    bordered
                    rowKey={record => record.logic_device}
                />
            </div>

            <LogViewModal
                title="Logs"
                chooseLogInfo={chooseLogInfo}
                viewLogModal={viewLogModal}
                onCancel={() => setViewLogModal(false)}
                modalClass="ampcon-max-modal"
            />
        </div>
    );
};

export default DeploymentStep;

const LogViewModal = ({chooseLogInfo, viewLogModal, onCancel}) => {
    const readonlyStyle = {
        minHeight: "330px",
        height: "58vh",
        resize: "vertical",
        border: "none",
        backgroundColor: "#f8fafb",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        maxHeight: "calc(100vh - 500px)"
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <>
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        {`${chooseLogInfo.logic_device} logs`}
                        <Button type="text" className="ant-modal-close" style={{marginRight: "30px"}} />
                    </div>
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </>
            }
            open={viewLogModal}
            onCancel={onCancel}
            footer={null}
        >
            <Flex vertical style={{flex: 1}}>
                <Input.TextArea style={readonlyStyle} value={chooseLogInfo.task_log} rows={19} readOnly />
            </Flex>
        </Modal>
    );
};
