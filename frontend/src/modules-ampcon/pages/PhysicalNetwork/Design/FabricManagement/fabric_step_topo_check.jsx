import React, {useEffect, useState} from "react";
import {useLocation} from "react-router-dom";

import {Row, Space, Modal, Divider, Tag, message, Table, Select, Input, Button} from "antd";
import {createColumnConfig} from "@/modules-ampcon/components/custom_table";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";

import Icon from "@ant-design/icons";
import {CheckCircleIcon, SelectedCheckCircleIcon} from "@/utils/common/iconSvg";
import EditSvg from "@/modules-ampcon/pages/Topo/Topology/resource/edit.svg?react";
import AutoDiscovery from "@/modules-ampcon/pages/Topo/Topology/resource/fabric_autoDiscovery.svg?react";

// import API
import {
    viewFabric,
    editFabric,
    fabric_topo_auto_link,
    fabric_topo_check,
    list_port_dropdown_data
} from "@/modules-ampcon/apis/dc_template_api";

const TopoCheckStep = ({submitValid}) => {
    const {state} = useLocation();
    const [topoInfo, setTopoInfo] = useState({});
    const [topoData, setTopoData] = useState([]);

    const [editModalopen, setEditModalOpen] = useState(false);
    const [editRecord, setEditRecord] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);

    const [portList, setPortList] = useState([]);

    const getTopoInfo = async () => {
        const res = await viewFabric({fabric_topo_id: state.data.id});
        if (res.status === 200) {
            setTopoInfo(res.data);
            getPortList(res.data);
            setSelectedRowKeys([]);
            setSelectedRows([]);
        }
    };

    const getPortList = async topoData => {
        const nodes = topoData?.fabric_config?.topology?.nodes;
        const logic_device_ids = nodes.map(obj => obj.logic_device_id);
        const res = await list_port_dropdown_data({logic_device_ids});
        if (res.status === 200) {
            setPortList(res.data);
        }
    };

    const handleSort = (dataIndex, getValueFn) => {
        return (a, b) => {
            const getValue = (obj, keyPath) => {
                const keys = keyPath.split(".");
                let value = obj;
                for (const key of keys) {
                    if (key.includes("[")) {
                        const [prop, indexStr] = key.split("[");
                        const index = parseInt(indexStr.replace("]", ""), 10);
                        value = value && value[prop] && Array.isArray(value[prop]) && value[prop][index];
                    } else {
                        value = value ? value[key] : null;
                    }
                    if (!value) break;
                }
                return value;
            };

            const valueA = getValueFn ? getValueFn(a) : getValue(a, dataIndex);
            const valueB = getValueFn ? getValueFn(b) : getValue(b, dataIndex);

            if (typeof valueA === "string" && typeof valueB === "string") {
                return valueA.localeCompare(valueB);
            }
            return (valueA || 0) - (valueB || 0);
        };
    };

    const onSelectChange = (newSelectedRowKeys, newSelectedRows) => {
        setSelectedRowKeys(newSelectedRowKeys);
        setSelectedRows(newSelectedRows);
    };

    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange
    };
    const topoCheckColumns = [
        {
            ...createColumnConfig("Link Name", "logic_link"),
            sorter: handleSort("logic_link"),
            fixed: "left"
        },
        {...createColumnConfig("Type", "type"), sorter: handleSort("type")},
        {
            title: "Node 1",
            dataIndex: "node1",
            children: [
                {
                    ...createColumnConfig("Sysname", "source"),
                    render: (text, record) => <Space>{record?.source || "-- "}</Space>,
                    sorter: handleSort("source")
                },
                {
                    ...createColumnConfig("Role", "source_role"),
                    render: (text, record) => <Space>{record?.source_role || "-- "}</Space>,
                    sorter: handleSort("source_role")
                },
                {
                    ...createColumnConfig("Interface", "source_port"),
                    sorter: handleSort("source_port")
                },
                {
                    ...createColumnConfig("IP", "source_routed_address"),
                    render: (text, record) => <Space>{record?.source_routed_address || "-- "}</Space>,
                    sorter: handleSort("source_routed_address")
                }
            ]
        },
        {
            title: "Node 2",
            dataIndex: "node2",
            children: [
                {
                    ...createColumnConfig("Sysname", "target"),
                    render: (text, record) => <Space>{record?.target || "-- "}</Space>,
                    sorter: handleSort("target")
                },
                {
                    ...createColumnConfig("Role", "target_role"),
                    render: (text, record) => <Space>{record?.target_role || "-- "}</Space>,
                    sorter: handleSort("target_role")
                },
                {
                    ...createColumnConfig("Interface", "target_port"),
                    sorter: handleSort("target_port")
                },
                {
                    ...createColumnConfig("IP", "target_routed_address"),
                    render: (text, record) => <Space>{record?.target_routed_address || "-- "}</Space>,
                    sorter: handleSort("target_routed_address")
                }
            ]
        },
        {
            ...createColumnConfig("Status", "link_status"),
            filters: [
                {text: "Passed", value: "passed"},
                {text: "Failed", value: "failed"},
                {text: "Uncheck", value: "uncheck"}
            ],
            onFilter: (value, record) => record.link_status?.indexOf(value) === 0,
            sorter: handleSort("link_status"),
            render: (_, record) => {
                const linkStatus = record?.link_status?.toLowerCase();
                let tagClassName;
                if (linkStatus && linkStatus.includes("passed")) {
                    tagClassName = styles.successTag;
                } else if (linkStatus && linkStatus.includes("failed")) {
                    tagClassName = styles.failedTag;
                } else if (linkStatus && linkStatus.includes("uncheck")) {
                    tagClassName = styles.uncheckedTag;
                }
                return (
                    <Space>
                        <Tag className={tagClassName}>
                            {record.link_status.charAt(0).toUpperCase() + record.link_status.slice(1)}
                        </Tag>
                    </Space>
                );
            }
        },
        ...(topoInfo.status !== "Deployed"
            ? [
                  {
                      title: "Operation",
                      fixed: "right",
                      render: (_, record) => (
                          <Space size="middle" className={styles.actionLink}>
                              <a
                                  style={{color: "#14C9BB"}}
                                  onClick={() => {
                                      setEditModalOpen(true);
                                      setEditRecord([record]);
                                  }}
                              >
                                  Edit
                              </a>
                          </Space>
                      )
                  }
              ]
            : [])
    ];

    const checkTopoData = () => {
        let passStatus = true;
        // 检查 link_info 中的 source_port 和 target_port 是否为空
        const hasEmptyPort = topoInfo?.fabric_config?.topology?.edges?.some(link => {
            return link.link_info.some(info => {
                return info.source_port === "" || info.target_port === "" || info.link_status !== "passed";
            });
        });
        passStatus = !hasEmptyPort;
        submitValid("Check Topo", topoInfo, passStatus);
    };

    const saveEdit = async data => {
        const edges = topoInfo?.fabric_config?.topology?.edges;
        data.forEach(dataItem => {
            edges.forEach(edge => {
                edge.link_info.forEach(linkInfo => {
                    if (linkInfo.logic_link === dataItem.logic_link) {
                        linkInfo.source_port = dataItem.source_port;
                        linkInfo.target_port = dataItem.target_port;
                        linkInfo.link_status = dataItem.link_status;
                    }
                });
            });
        });
        const sendData = {
            fabric_name: state.data.fabric_name,
            template_name: state.data.template_name,
            fabric_config: topoInfo.fabric_config
        };
        const result = await editFabric(sendData);
        if (result.status === 200) {
            message.success(result.info);
            setEditModalOpen(false);
            getTopoInfo();
        } else {
            message.error(result.info);
        }
    };

    // Auto Discovery
    const autoDiscovery = async () => {
        const res = await fabric_topo_auto_link({fabric_topo_id: state.data.id});
        if (res.status === 200) {
            setTopoInfo(res.data);
            message.success(res.info);
        } else {
            message.error(res.info);
        }
    };
    // check检查
    const checkLog = async () => {
        const data = {
            fabric_topo_id: state.data.id,
            check_list: selectedRows.map(item => item.logic_link)
        };
        const res = await fabric_topo_check(data);
        if (res.status === 200) {
            setTopoInfo(res.data);
            message.success(res.info);
        } else {
            message.error(res.info);
        }
    };

    useEffect(() => {
        getTopoInfo();
    }, [state]);

    useEffect(() => {
        const edges = topoInfo?.fabric_config?.topology?.edges || [];
        const nodes = topoInfo?.fabric_config?.topology?.nodes || [];
        const updatedTopoData = edges?.map(record => {
            const updatedLinkInfo = record.link_info?.map(link => ({
                ...link,
                editStatus: false
            }));

            return {
                ...record,
                link_info: updatedLinkInfo
            };
        });

        const newArray = [];
        let index = 0;

        updatedTopoData.forEach(item1 => {
            item1.source = nodes.find(obj => obj.logic_device === item1.source).node_info.hostname;
            item1.source_logic_device_id = nodes.find(obj => obj.node_info.hostname === item1.source).logic_device_id;

            item1.target = nodes.find(obj => obj.logic_device === item1.target).node_info.hostname;
            item1.target_logic_device_id = nodes.find(obj => obj.node_info.hostname === item1.target).logic_device_id;

            const {link_info, ...rest} = item1;
            link_info.forEach(item2 => {
                const newObj = {...rest, ...item2, index};
                newArray.push(newObj);
                index++;
            });
        });
        setTopoData(newArray);
        checkTopoData();
    }, [topoInfo]);

    useEffect(() => {
        selectedRowKeys.map(item => {
            topoData[item].editStatus = false;
        });
        setSelectedRowKeys([]);
        setSelectedRows([]);
    }, []);

    const [paginationConfig, setPaginationConfig] = useState({
        showSizeChanger: true,
        showTotal: (total, range) => {
            const start = range[0];
            const end = range[1];
            return `${start}-${end} of ${total} items`;
        },
        total: topoData?.length
    });

    const handleTableChange = (pagination, filters, sorter, extra) => {
        const filteredData = data => {
            for (const filterName in filters) {
                if (filters.hasOwnProperty.cell(filterName)) {
                    const filterValue = filters[filterName];
                    if (filterValue && filterValue.length > 0) {
                        data = data.filter(record => {
                            const fieldValue = record[filterName];
                            return filterValue.some(value => fieldValue?.indexOf(value) === 0);
                        });
                    }
                }
            }
            return data;
        };

        const newData = filteredData(topoData);
        setPaginationConfig({
            ...paginationConfig,
            total: newData.length,
            current: pagination.current
        });
    };

    return (
        <div className={styles.tabsBorder} style={{height: "100%"}}>
            {topoInfo.status !== "Deployed" && (
                <div className={styles.button_box2} style={{marginBottom: "24px"}}>
                    <Button
                        type="primary"
                        icon={<Icon component={EditSvg} />}
                        onClick={() => {
                            setEditModalOpen(true);
                            setEditRecord(selectedRows);
                        }}
                        disabled={selectedRows.length === 0}
                    >
                        Edit
                    </Button>

                    <Button type="default" onClick={() => autoDiscovery()}>
                        <Icon component={AutoDiscovery} />
                        Auto Discovery
                    </Button>
                    <Button
                        className={styles.checkBtn}
                        type="default"
                        onClick={() => checkLog()}
                        disabled={selectedRows.length === 0}
                    >
                        <Icon component={selectedRowKeys.length === 0 ? CheckCircleIcon : SelectedCheckCircleIcon} />
                        Check
                    </Button>
                </div>
            )}
            <div>
                <Table
                    columns={topoCheckColumns}
                    rowKey={record => record.index}
                    dataSource={topoData}
                    bordered
                    rowSelection={topoInfo.status !== "Deployed" ? rowSelection : ""}
                    pagination={paginationConfig}
                    scroll={{
                        x: "max-content"
                    }}
                    onChange={handleTableChange}
                />
            </div>

            <EditInterfaceModal
                title="Edit Topo"
                editRecord={editRecord}
                topoInfo={topoInfo}
                portList={portList}
                editModalopen={editModalopen}
                setEditModalOpen={setEditModalOpen}
                onCancel={() => setEditModalOpen(false)}
                onSubmit={saveEdit}
                modalClass="ampcon-max-modal"
            />
        </div>
    );
};

export default TopoCheckStep;

const EditInterfaceModal = ({
    title,
    editRecord,
    topoInfo,
    portList,
    editModalopen,
    // setEditModalOpen,
    onCancel,
    onSubmit
}) => {
    const [data, setData] = useState([]);
    const [saveStatus, setSaveStatus] = useState(false);
    const [portDisabledList, setPortDisabledList] = useState({});

    const handleSort = dataIndex => {
        return (a, b) => {
            const getValue = (obj, keyPath) => {
                return keyPath.split(".").reduce((o, key) => (o ? o[key] : ""), obj);
            };

            const valueA = getValue(a, dataIndex);
            const valueB = getValue(b, dataIndex);

            if (typeof valueA === "string" && typeof valueB === "string") {
                return valueA.localeCompare(valueB);
            }
            return (valueA || 0) - (valueB || 0);
        };
    };

    const handleSelectChange = (record, event, name) => {
        setData(prevData => {
            return prevData.map(item => {
                if (item.index === record.index) {
                    return {
                        ...item,
                        [name]: event
                    };
                }
                return item;
            });
        });
    };

    const handleSelectUnique = (value, logicDeviceID) => {};
    // 定义提取逻辑的函数
    const extractAndMerge = (data, idKey, portKey) => {
        const result = {};
        data.forEach(item => {
            const id = item[idKey];
            const port = item[portKey];
            if (!result[id]) {
                result[id] = [];
            }
            if (!result[id].includes(port)) {
                result[id].push(port);
            }
        });
        return Object.entries(result).map(([key, value]) => ({[key]: value}));
    };

    // 合并 sourceResult 和 targetResult 并去重合并值
    const mergeResults = results => {
        const combined = {};
        results.forEach(result => {
            const [key, value] = Object.entries(result)[0];
            if (!combined[key]) {
                combined[key] = [];
            }
            value.forEach(port => {
                if (!combined[key].includes(port)) {
                    combined[key].push(port);
                }
            });
        });
        return Object.entries(combined).map(([key, value]) => ({[key]: value}));
    };

    const topoCheckColumns = [
        {
            ...createColumnConfig("Link Name", "logic_link", "", "200px"),
            render: (_, record) => <Space>{record.logic_link}</Space>,
            sorter: handleSort("logic_link")
        },
        {...createColumnConfig("Type", "type")},
        {
            title: "Node 1",
            dataIndex: "node1",
            width: 200,
            children: [
                {
                    ...createColumnConfig("Sysname", "source"),
                    render: (text, record) => <Space>{record?.source || "-- "}</Space>,
                    sorter: handleSort("source")
                },
                {
                    ...createColumnConfig("Role", "source_role"),
                    render: (text, record) => <Space>{record?.source_role || "-- "}</Space>,
                    sorter: handleSort("source_role")
                },
                {
                    ...createColumnConfig("Interface", "source_port"),
                    render: (text, record) => (
                        <Space>
                            <Select
                                value={record.source_port}
                                options={portList[record?.source_logic_device_id]?.map(item => ({
                                    value: item,
                                    label: item
                                }))}
                                allowClear
                                onChange={event => {
                                    handleSelectChange(record, event, "source_port");
                                    handleSelectUnique(event, record?.source_logic_device_id);
                                }}
                                suffixIcon={<span>{portList[record?.source_logic_device_id]?.length} </span>}
                                style={{width: 180}}
                            />
                        </Space>
                    ),
                    sorter: handleSort("source_port")
                },
                {
                    ...createColumnConfig("IP", "source_routed_address"),
                    render: (text, record) => <Space>{record?.source_routed_address || "-- "}</Space>,
                    sorter: handleSort("source_routed_address")
                }
            ]
        },
        {
            title: "Node 2",
            dataIndex: "node2",
            width: 200,
            children: [
                {
                    ...createColumnConfig("Sysname", "target"),
                    render: (text, record) => <Space>{record?.target || "-- "}</Space>,
                    sorter: handleSort("target")
                },
                {
                    ...createColumnConfig("Role", "target_role"),
                    render: (text, record) => <Space>{record?.target_role || "-- "}</Space>,
                    sorter: handleSort("target_role")
                },
                {
                    ...createColumnConfig("Interface", "target_port"),
                    render: (text, record) => (
                        <Space>
                            <Select
                                value={record.target_port}
                                options={portList[record?.target_logic_device_id]?.map(item => ({
                                    value: item,
                                    label: item
                                }))}
                                allowClear
                                onChange={event => {
                                    handleSelectChange(record, event, "target_port");
                                }}
                                suffixIcon={<span>{portList[record?.target_logic_device_id]?.length} </span>}
                                style={{width: 180}}
                            />
                        </Space>
                    ),
                    sorter: handleSort("target_port")
                },
                {
                    ...createColumnConfig("IP", "target_routed_address"),
                    render: (text, record) => <Space>{record?.target_routed_address || "-- "}</Space>,
                    sorter: handleSort("target_routed_address")
                }
            ]
        },
        {
            ...createColumnConfig("Status", "link_status"),
            filters: [
                {text: "Passed", value: "passed"},
                {text: "Failed", value: "failed"},
                {text: "Uncheck", value: "uncheck"}
            ],
            onFilter: (value, record) => record.link_status?.indexOf(value) === 0,
            sorter: handleSort("link_status"),
            render: (_, record) => {
                const linkStatus = record?.link_status?.toLowerCase();
                let tagClassName;
                if (linkStatus && linkStatus.includes("passed")) {
                    tagClassName = styles.successTag;
                } else if (linkStatus && linkStatus.includes("failed")) {
                    tagClassName = styles.failedTag;
                } else if (linkStatus && linkStatus.includes("uncheck")) {
                    tagClassName = styles.uncheckedTag;
                }
                return (
                    <Space>
                        <Tag className={tagClassName}>
                            {record.link_status.charAt(0).toUpperCase() + record.link_status.slice(1)}
                        </Tag>
                    </Space>
                );
            }
        }
    ];

    const handleOk = async () => {
        data.forEach(dataItem => {
            const matchingEditRecord = editRecord.find(editItem => editItem.index === dataItem.index);
            if (matchingEditRecord) {
                if (
                    dataItem.source_port !== matchingEditRecord.source_port ||
                    dataItem.target_port !== matchingEditRecord.target_port
                ) {
                    dataItem.link_status = "uncheck";
                }
            }
        });
        await onSubmit(data);
    };

    useEffect(() => {
        if (editRecord) {
            const newEditRecord = JSON.parse(JSON.stringify(editRecord));

            // 提取 source_logic_device_id 和 source_port
            const sourceResult = extractAndMerge(newEditRecord, "source_logic_device_id", "source_port");
            // 提取 target_logic_device_id 和 target_port
            const targetResult = extractAndMerge(newEditRecord, "target_logic_device_id", "target_port");
            const combinedResult = mergeResults([...sourceResult, ...targetResult]);
            setPortDisabledList(combinedResult);

            setData(newEditRecord);
        }
    }, [editRecord, portList]);

    useEffect(() => {
        if (data) {
            const hasEmptyPorts = data.some(item => {
                return !item.source_port || !item.target_port;
            });
            setSaveStatus(hasEmptyPorts);
        }
    }, [data]);

    return (
        <Space>
            <Modal
                className="ampcon-max-modal"
                title={
                    <div>
                        {title}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={editModalopen}
                onCancel={onCancel}
                footer={
                    <>
                        <Divider style={{marginBottom: "20px", marginTop: "0px"}} />
                        <Button onClick={onCancel}> Cancel </Button>
                        <Button type="primary" onClick={handleOk} disabled={saveStatus}>
                            Apply
                        </Button>
                    </>
                }
            >
                <Table columns={topoCheckColumns} rowKey={record => record.id} dataSource={data} />
            </Modal>
        </Space>
    );
};
