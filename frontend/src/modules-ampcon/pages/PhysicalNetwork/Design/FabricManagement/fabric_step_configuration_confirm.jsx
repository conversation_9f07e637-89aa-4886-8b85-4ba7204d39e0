import React, {useRef, useEffect, useState} from "react";
import {useLocation} from "react-router-dom";
import {Space, Tabs, message, Table} from "antd";
import {createColumnConfig} from "@/modules-ampcon/components/custom_table";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";

// import API
import {viewFabric} from "@/modules-ampcon/apis/dc_template_api";

const ConfigurationConfirmStep = ({submitValid}) => {
    const {state} = useLocation();
    const [fabricType, setFabricType] = useState("");
    const [activeTab, setActiveTab] = useState("spine");

    const [topoInfo, setTopoInfo] = useState({});
    const [superSpineData, setSuperSpineData] = useState([]);
    const [spineData, setSpineData] = useState([]);
    const [leafData, setLeafData] = useState([]);
    const [accessData, setAccessData] = useState([]);

    const [tabsStage3, setTabsStage3] = useState([
        {
            key: "spine",
            label: "Spine",
            children: <SpineTab topoInfo={topoInfo} spineData={spineData} />
        },
        {
            key: "leaf",
            label: "Leaf",
            children: <LeafTab topoInfo={topoInfo} leafData={leafData} />
        }
        // {
        //     key: "access",
        //     label: "Access",
        //     children: <AccessTab topoInfo={topoInfo} accessData={accessData} />
        // }
    ]);
    const [tabsStage5, setTabsStage5] = useState([
        {
            key: "superspine",
            label: "Superspine",
            children: <SuperspineTab topoInfo={topoInfo} superSpineData={superSpineData} />
        },

        {
            key: "leaf",
            label: "Leaf",
            children: <LeafTab topoInfo={topoInfo} leafData={leafData} />
        }
        // {
        //     key: "access",
        //     label: "Access",
        //     children: <AccessTab topoInfo={topoInfo} accessData={accessData} />
        // }
    ]);

    const getTopoInfo = async () => {
        const res = await viewFabric({fabric_topo_id: state.data.id});
        if (res.status === 200) {
            setTopoInfo(res.data);
            const nodes = res.data?.fabric_config?.topology?.nodes;

            const newSuperSpineData = nodes?.filter(item => item.type === "super");
            setSuperSpineData(newSuperSpineData);

            const newSpineData = nodes?.filter(item => item.type === "spine");
            setSpineData(newSpineData);

            const newLeafData = nodes?.filter(item => item.type === "leaf");
            setLeafData(newLeafData);

            const newAccessData = nodes?.filter(item => item.type === "access");
            setAccessData(newAccessData);
        }
    };

    useEffect(() => {
        getTopoInfo();
    }, [state]);

    useEffect(() => {
        if (Object.keys(topoInfo).length > 0) {
            submitValid("Confirm Configuration", topoInfo, true);
        }
        setFabricType(topoInfo?.fabric_config?.type);
    }, [topoInfo]);

    // 监听 topoInfo，实时更新 tabs传递的数据
    useEffect(() => {
        const tabs = fabricType === "5-stage" ? tabsStage5 : tabsStage3;
        const updatedTabs = tabs.map(tab => {
            let newChildren;
            switch (tab.key) {
                case "superspine":
                    newChildren = <SuperspineTab topoInfo={topoInfo} superSpineData={superSpineData} />;
                    break;
                case "spine":
                    newChildren = <SpineTab topoInfo={topoInfo} spineData={spineData} />;
                    break;
                case "leaf":
                    newChildren = <LeafTab topoInfo={topoInfo} leafData={leafData} />;
                    break;
                case "access":
                    newChildren = <AccessTab topoInfo={topoInfo} accessData={accessData} />;
                    break;
                default:
                    newChildren = tab.children;
            }
            return {
                ...tab,
                children: newChildren
            };
        });
        if (fabricType === "5-stage") {
            setTabsStage5(updatedTabs);
        } else {
            setTabsStage3(updatedTabs);
        }
    }, [topoInfo, superSpineData, spineData, leafData, accessData]);

    return (
        <Tabs
            className={`${styles.tabsBorder} radioGroupTabs customTab`}
            activeKey={activeTab}
            bordered
            onChange={key => setActiveTab(key)}
            items={fabricType === "5-stage" ? tabsStage5 : tabsStage3}
        />
    );
};

export default ConfigurationConfirmStep;

/**
 *  Tab组件
 */
const SuperspineTab = ({topoInfo, superSpineData}) => {
    const extractionData = record => {
        let value = "";
        if (topoInfo?.fabric_config?.type === "3-stage") {
            if (record?.node_info?.underlay_routing_protocol === "BGP") value = record?.node_info?.underlay_ebgp_asn;
            if (record?.node_info?.underlay_routing_protocol === "OSPF") value = "0.0.0.0";
        }
        return value;
    };
    const handleSort = (dataIndex, getValueFn) => {
        return (a, b) => {
            const getValue = (obj, keyPath) => {
                const keys = keyPath.split(".");
                let value = obj;
                for (const key of keys) {
                    if (key.includes("[")) {
                        const [prop, indexStr] = key.split("[");
                        const index = parseInt(indexStr.replace("]", ""), 10);
                        value = value && value[prop] && Array.isArray(value[prop]) && value[prop][index];
                    } else {
                        value = value ? value[key] : null;
                    }
                    if (!value) break;
                }
                return value;
            };

            const valueA = getValueFn ? getValueFn(a) : getValue(a, dataIndex);
            const valueB = getValueFn ? getValueFn(b) : getValue(b, dataIndex);

            if (typeof valueA === "string" && typeof valueB === "string") {
                return valueA.localeCompare(valueB);
            }
            return (valueA || 0) - (valueB || 0);
        };
    };

    const [filteredColumns, setFilteredColumns] = useState([]);
    const superspineColumns = [
        {
            ...createColumnConfig("Sysname", "hostname"),
            sorter: handleSort("node_info.hostname"),
            render: (text, record) => <Space>{record?.node_info?.hostname}</Space>
        },
        // {
        //     ...createColumnConfig("Device Index", "logic_device"),
        //     sorter: handleSort("logic_device"),
        //     render: (text, record) => {
        //         if (record.type === "spine") {
        //             return <Space>{record.logic_device}</Space>;
        //         }
        //         const logicDevice = record.logic_device || "";
        //         const group = `${record.group}_` || "";
        //         const result = logicDevice.replace(group, "").trim();
        //         return <Space>{result}</Space>;
        //     }
        // },
        {
            ...createColumnConfig("Role", "type"),
            sorter: handleSort("type")
        },
        {...createColumnConfig("Physical Device", "switch_sn"), sorter: handleSort("switch_sn")},
        {
            ...createColumnConfig("Area ID", "area_id"),
            sorter: handleSort("area_id"),
            render: (text, record, index) => <Space>{extractionData("area_id", record, index) || "--"}</Space>
        },
        {
            ...createColumnConfig("ASN", "asn"),
            sorter: handleSort("asn"),
            render: (text, record) => <Space>{record?.node_info?.asn || "--"}</Space>
        },
        {
            ...createColumnConfig("Router ID", "router_id"),
            sorter: handleSort("router_id"),
            render: (text, record) => <Space>{record?.node_info?.router_id || "--"}</Space>
        },
        {
            title: "Routing Protocol",
            dataIndex: "underlay_routing_protocol",
            render: (text, record) => <Space>{record?.node_info?.underlay_routing_protocol}</Space>
        }
    ];

    const paginationConfig = {
        showSizeChanger: true,
        showTotal: (total, range) => {
            const start = range[0];
            const end = range[1];
            return `${start}-${end} of ${total} items`;
        },
        total: superSpineData?.length
    };
    useEffect(() => {
        if (topoInfo && superSpineData?.length > 0) {
            let newColumns = superspineColumns;
            if (topoInfo?.fabric_config?.underlay_routing_protocol === "BGP") {
                newColumns = superspineColumns.filter(column => column.title !== "Area ID");
            }
            setFilteredColumns(newColumns);
        }
    }, [topoInfo, superSpineData]);

    return (
        <div>
            <Table
                columns={filteredColumns.length > 0 ? filteredColumns : superspineColumns}
                dataSource={superSpineData}
                bordered
                pagination={paginationConfig}
            />
        </div>
    );
};

/**
 * Spine
 */
const SpineTab = ({topoInfo, spineData}) => {
    const handleSort = (dataIndex, getValueFn) => {
        return (a, b) => {
            const getValue = (obj, keyPath) => {
                const keys = keyPath.split(".");
                let value = obj;
                for (const key of keys) {
                    if (key.includes("[")) {
                        const [prop, indexStr] = key.split("[");
                        const index = parseInt(indexStr.replace("]", ""), 10);
                        value = value && value[prop] && Array.isArray(value[prop]) && value[prop][index];
                    } else {
                        value = value ? value[key] : null;
                    }
                    if (!value) break;
                }
                return value;
            };

            const valueA = getValueFn ? getValueFn(a) : getValue(a, dataIndex);
            const valueB = getValueFn ? getValueFn(b) : getValue(b, dataIndex);

            if (typeof valueA === "string" && typeof valueB === "string") {
                return valueA.localeCompare(valueB);
            }
            return (valueA || 0) - (valueB || 0);
        };
    };

    const [filteredColumns, setFilteredColumns] = useState([]);
    const spineColumns = [
        {
            ...createColumnConfig("Sysname", "hostname"),
            sorter: handleSort("node_info.hostname"),
            render: (text, record) => <Space>{record?.node_info?.hostname}</Space>
        },
        // {
        //     ...createColumnConfig("Device Index", "logic_device"),
        //     sorter: handleSort("logic_device"),
        //     render: (text, record) => <Space>{record.logic_device}</Space>
        // },
        {...createColumnConfig("Role", "type"), sorter: handleSort("type")},
        {...createColumnConfig("Physical Device", "switch_sn"), sorter: handleSort("switch_sn")},
        {
            ...createColumnConfig("Area ID", "area_id"),
            sorter: handleSort("node_info.area_id"),
            render: (text, record) => <Space>{record?.node_info.area_id || "--"}</Space>
        },
        {
            ...createColumnConfig("ASN", "asn"),
            sorter: handleSort("node_info.asn"),
            render: (text, record) => <Space>{record?.node_info?.asn || "--"}</Space>
        },
        {
            ...createColumnConfig("Router ID", "router_id"),
            sorter: handleSort("node_info.router_id"),
            render: (text, record) => <Space>{record?.node_info?.router_id || "--"}</Space>
        },
        {
            title: "Routing Protocol",
            dataIndex: "underlay_routing_protocol",
            render: (text, record) => <Space>{record?.node_info?.underlay_routing_protocol}</Space>
        }
    ];

    const paginationConfig = {
        showSizeChanger: true,
        showTotal: (total, range) => {
            const start = range[0];
            const end = range[1];
            return `${start}-${end} of ${total} items`;
        },
        total: spineData?.length
    };

    useEffect(() => {
        if (topoInfo && spineData?.length > 0) {
            let newColumns = spineColumns;
            if (topoInfo?.fabric_config?.underlay_routing_protocol === "BGP") {
                newColumns = spineColumns.filter(column => column.title !== "Area ID");
            }
            setFilteredColumns(newColumns);
        }
    }, [topoInfo, spineData]);

    return (
        <div>
            <Table
                columns={filteredColumns.length > 0 ? filteredColumns : spineColumns}
                dataSource={spineData}
                bordered
                pagination={paginationConfig}
            />
        </div>
    );
};

/**
 * Leaf
 */
const LeafTab = ({topoInfo, leafData}) => {
    const handleSort = (dataIndex, getValueFn) => {
        return (a, b) => {
            const getValue = (obj, keyPath) => {
                const keys = keyPath.split(".");
                let value = obj;
                for (const key of keys) {
                    if (key.includes("[")) {
                        const [prop, indexStr] = key.split("[");
                        const index = parseInt(indexStr.replace("]", ""), 10);
                        value = value && value[prop] && Array.isArray(value[prop]) && value[prop][index];
                    } else {
                        value = value ? value[key] : null;
                    }
                    if (!value) break;
                }
                return value;
            };

            const valueA = getValueFn ? getValueFn(a) : getValue(a, dataIndex);
            const valueB = getValueFn ? getValueFn(b) : getValue(b, dataIndex);

            if (typeof valueA === "string" && typeof valueB === "string") {
                return valueA.localeCompare(valueB);
            }
            return (valueA || 0) - (valueB || 0);
        };
    };

    const [filteredColumns, setFilteredColumns] = useState([]);
    const leafColumns = [
        {
            ...createColumnConfig("Sysname", "hostname"),
            sorter: handleSort("node_info.hostname"),
            render: (text, record) => <Space>{record?.node_info?.hostname || "--"}</Space>
        },
        // {
        //     ...createColumnConfig("Device Index", "logic_device"),
        //     sorter: handleSort("logic_device"),
        //     render: (text, record) => {
        //         const logicDevice = record.logic_device || "";
        //         const group = `${record.group}_` || "";
        //         const result = logicDevice.replace(group, "").trim();
        //         return <Space>{result}</Space>;
        //     }
        // },
        {...createColumnConfig("Role", "type"), sorter: handleSort("type")},
        {...createColumnConfig("Physical Device", "switch_sn"), sorter: handleSort("switch_sn")},
        {
            ...createColumnConfig("Area ID", "area_id"),
            sorter: handleSort("node_info.area_id"),
            render: (text, record) => <Space>{record?.node_info.area_id || "--"}</Space>
        },
        {
            ...createColumnConfig("ASN", "asn"),
            sorter: handleSort("node_info.asn"),
            render: (text, record) => <Space>{record?.node_info?.asn || "--"}</Space>
        },
        {
            ...createColumnConfig("MLAG Peer", "mlag_peer"),
            sorter: handleSort("node_info.hostname"),
            render: (text, record) => {
                let displayText = "";
                if (record?.node_info.strategy && record?.node_info?.strategy === "MLAG") {
                    // const logicDevice = record.logic_device || "";
                    // const group = `${record.group}_` || "";
                    // let result = logicDevice.replace(group, "").trim();
                    let result = record?.node_info.hostname;
                    if (result.endsWith("-1")) {
                        result = `${result.slice(0, -2)}-2`;
                    } else if (result.endsWith("-2")) {
                        result = `${result.slice(0, -2)}-1`;
                    }
                    displayText = result;
                }

                return <Space>{displayText || "--"}</Space>;
            }
        },
        {
            ...createColumnConfig("Router ID", "router_id"),
            sorter: handleSort("node_info.router_id"),
            render: (text, record) => <Space>{record?.node_info?.router_id || "--"}</Space>
        },
        {
            ...createColumnConfig("VTEP Interface", "vtep_interface"),
            sorter: handleSort("node_info.vtep_interface"),
            render: (text, record) => <Space>{record?.node_info?.vtep_interface || "--"}</Space>
        },
        {
            ...createColumnConfig("MLAG Peer Links", "mlag_peerLink"),
            sorter: handleSort("mlag_peerLink"),
            render: (text, record) => {
                if (record?.node_info?.strategy && record?.node_info?.strategy === "MLAG") {
                    return <Space>2</Space>;
                }
                return <Space>--</Space>;
            }
        },
        {
            title: "MLAG",
            dataIndex: "mlag",
            width: 200,
            children: [
                {
                    ...createColumnConfig("MLAG Peer Link VLAN", "peer_vlan_id"),
                    sorter: handleSort("node_info.peer_vlan_id"),
                    render: (text, record) => <Space>{record?.node_info?.peer_vlan_id || "--"}</Space>
                },
                {
                    ...createColumnConfig("L3 Peer Link", "mlag_l3_interface_ip_address"),
                    sorter: handleSort("node_info.mlag_l3_interface_ip_address"),
                    render: (text, record) => <Space>{record?.node_info?.mlag_l3_interface_ip_address || "--"}</Space>
                }
            ]
        },
        {
            title: "Routing Protocol",
            dataIndex: "underlay_routing_protocol",
            render: (text, record) => <Space>{record?.node_info?.underlay_routing_protocol}</Space>
        }
    ];

    const paginationConfig = {
        showSizeChanger: true,
        showTotal: (total, range) => {
            const start = range[0];
            const end = range[1];
            return `${start}-${end} of ${total} items`;
        },
        total: leafData?.length
    };
    useEffect(() => {
        if (topoInfo && leafData?.length > 0) {
            let newColumns = leafColumns;
            if (topoInfo?.fabric_config?.underlay_routing_protocol === "BGP") {
                newColumns = leafColumns.filter(column => column.title !== "Area ID");
            }
            setFilteredColumns(newColumns);
        }
    }, [topoInfo, leafData]);

    return (
        <div>
            <Table
                columns={filteredColumns.length > 0 ? filteredColumns : leafColumns}
                dataSource={leafData}
                bordered
                pagination={paginationConfig}
            />
        </div>
    );
};

const AccessTab = ({topoInfo, accessData}) => {
    const handleSort = (dataIndex, getValueFn) => {
        return (a, b) => {
            const getValue = (obj, keyPath) => {
                const keys = keyPath.split(".");
                let value = obj;
                for (const key of keys) {
                    if (key.includes("[")) {
                        const [prop, indexStr] = key.split("[");
                        const index = parseInt(indexStr.replace("]", ""), 10);
                        value = value && value[prop] && Array.isArray(value[prop]) && value[prop][index];
                    } else {
                        value = value ? value[key] : null;
                    }
                    if (!value) break;
                }
                return value;
            };

            const valueA = getValueFn ? getValueFn(a) : getValue(a, dataIndex);
            const valueB = getValueFn ? getValueFn(b) : getValue(b, dataIndex);

            if (typeof valueA === "string" && typeof valueB === "string") {
                return valueA.localeCompare(valueB);
            }
            return (valueA || 0) - (valueB || 0);
        };
    };

    const accessColumns = [
        {
            ...createColumnConfig("Sysname", "hostname"),
            sorter: handleSort("node_info.hostname"),
            render: (text, record) => <Space>{record?.node_info?.hostname}</Space>
        },
        // {
        //     ...createColumnConfig("Device Index", "logic_device"),
        //     sorter: handleSort("logic_device"),
        //     render: (text, record) => {
        //         if (record.type === "spine") {
        //             return <Space>{record.logic_device}</Space>;
        //         }
        //         const logicDevice = record.logic_device || "";
        //         const group = `${record.group}_` || "";
        //         const result = logicDevice.replace(group, "").trim();
        //         return <Space>{result}</Space>;
        //     }
        // },
        {...createColumnConfig("Role", "type")},
        {...createColumnConfig("Physical Device", "switch_sn"), sorter: handleSort("switch_sn")},
        {...createColumnConfig("Links To Leaf", "link_leaf"), sorter: handleSort("link_leaf")},
        {...createColumnConfig("MLAG Access Mode", "type"), sorter: handleSort("type")},
        {...createColumnConfig("Peer Leaf", "peer_leaf"), sorter: handleSort("peer_leaf")},
        {
            title: "Routing Protocol",
            dataIndex: "underlay_routing_protocol",
            render: (text, record) => <Space>{record?.node_info?.underlay_routing_protocol}</Space>
        }
    ];

    const paginationConfig = {
        showSizeChanger: true,
        showTotal: (total, range) => {
            const start = range[0];
            const end = range[1];
            return `${start}-${end} of ${total} items`;
        },
        total: accessData?.length
    };
    useEffect(() => {}, [topoInfo]);

    useEffect(() => {}, [accessData]);

    return (
        <div>
            <Table
                columns={accessColumns}
                bordered
                rowKey="logic_device"
                dataSource={accessData}
                pagination={paginationConfig}
            />
        </div>
    );
};
