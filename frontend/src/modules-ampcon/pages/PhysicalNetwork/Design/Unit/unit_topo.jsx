import {Graph} from "@antv/x6";
import {useEffect, useRef} from "react";
import {MiniMap} from "@antv/x6-plugin-minimap";
import {register} from "@antv/x6-react-shape?react";
import {calculateVertices, unitTopoLayout} from "@/utils/topo_layout_utils";
import DeviceBriefTooltip from "../unit_tooltip/device_brief_tooltip";
import LinkBriefTooltip from "../unit_tooltip/link_brief_tooltip";

const UnitTopo = ({leafNodes, accessNodes}) => {
    const containerRef = useRef(null);
    const graphRef = useRef(null);
    const miniMapContainerRef = useRef(null);
    const deviceBriefTooltipRef = useRef(null);
    const linkBriefTooltipRef = useRef(null);
    const leafNodesDetailedInfo = [];

    const isFabricTopo = useRef(false);

    const registerLeafNode = () => {
        register({
            shape: "leafNode",
            zIndex: 1000,
            inherit: "rect",
            ports: {
                groups: {
                    out: {
                        position: "bottom",
                        attrs: {
                            circle: {r: 1, magnet: false, stroke: "none", strokeWidth: 1, fill: "none"}
                        }
                    },
                    in: {
                        position: "top",
                        attrs: {
                            circle: {r: 1, magnet: false, stroke: "none", strokeWidth: 1, fill: "none"}
                        }
                    }
                },
                items: [
                    {group: "out", id: "leafBottomPort"},
                    {group: "in", id: "leafTopPort"}
                ]
            },
            attrs: {
                body: {
                    fill: "#B0E6FF",
                    stroke: "transparent",
                    fillOpacity: 0.5,
                    rx: 4,
                    ry: 4
                },
                label: {
                    fill: "#26A5E1",
                    fontSize: 14,
                    fontWeight: 500,
                    textAnchor: "middle",
                    textVerticalAnchor: "middle"
                }
            }
        });
    };

    const updateAllEdgesVertices = () => {
        graphRef.current.getEdges().forEach(edge => {
            if (edge.id === "splitter1") return;
            const sourceNode = graphRef.current.getCellById(edge.store.data.source.cell);
            const targetNode = graphRef.current.getCellById(edge.store.data.target.cell);
            edge.setVertices(calculateVertices(sourceNode, targetNode));
        });
    };

    useEffect(() => {
        graphRef.current = new Graph({
            container: containerRef.current,
            width: containerRef.current.clientWidth,
            height: containerRef.current.clientHeight,

            grid: true,
            rotating: {
                enabled: true
            },
            panning: {
                enabled: true,
                eventTypes: ["leftMouseDown", "mouseWheel"]
            },
            interacting: {
                nodeMovable: false,
                edgeMovable: false
            },
            connecting: {
                connector: {
                    name: "smooth"
                }
            },
            mousewheel: {
                enabled: true,
                modifiers: "ctrl"
            }
        });

        graphRef.current.use(
            new MiniMap({
                container: miniMapContainerRef.current,
                width: 200,
                height: 180,
                padding: 10,
                scalable: true,
                scaling: 1,
                minScale: 0.1,
                maxScale: 1,
                graphOptions: {
                    async: true,
                    createCellView(cell) {
                        if (cell.isEdge()) {
                            return null;
                        }
                    }
                }
            })
        );

        registerLeafNode();

        if (leafNodes === undefined || accessNodes === undefined) return;

        const unitInfo = {leafNodes, accessNodes};

        leafNodesDetailedInfo.push(...unitTopoLayout(0, {x: 100, y: 100}, unitInfo, 0));

        leafNodesDetailedInfo.pop();

        graphRef.current.addNodes(leafNodesDetailedInfo);

        graphRef.current.on("node:mouseenter", deviceInfo => {
            deviceBriefTooltipRef.current.showDeviceBriefTooltip(deviceInfo.node.store.data);
            if (deviceInfo.node.shape === "leafNode") {
                deviceInfo.node.attr("body/stroke", "#26A5E1");
                deviceInfo.node.attr("body/strokeWidth", 1);
            }
        });

        graphRef.current.on("node:mouseleave", deviceInfo => {
            deviceBriefTooltipRef.current.hideDeviceBriefTooltip();
            if (deviceInfo.node.shape === "leafNode") {
                deviceInfo.node.attr("body/stroke", "transparent");
                deviceInfo.node.attr("body/strokeWidth", 1);
            }
        });

        graphRef.current.on("edge:mouseenter", edgeInfo => {
            if (edgeInfo.edge.id === "splitter1") return;
            linkBriefTooltipRef.current.showLinkBriefTooltip(edgeInfo.edge.store.data);
        });

        graphRef.current.on("edge:mouseleave", () => {
            linkBriefTooltipRef.current.hideLinkBriefTooltip();
        });

        updateAllEdgesVertices();

        return () => {
            graphRef.current.dispose();
        };
    }, [leafNodes, accessNodes]);

    return (
        <>
            <div
                style={{
                    position: "relative",
                    flex: "1",
                    width: "100%",
                    height: "100%"
                }}
            >
                <div ref={containerRef} style={{width: "100%", height: "100%"}} />
                <div
                    ref={miniMapContainerRef}
                    style={{
                        position: "absolute",
                        right: 0,
                        bottom: 0,
                        zIndex: 999,
                        width: "200px",
                        height: "50px",
                        backgroundColor: "#F0F0F0"
                    }}
                />
            </div>
            <DeviceBriefTooltip ref={deviceBriefTooltipRef} offsetX={10} offsetY={10} topoType="unit" />
            <LinkBriefTooltip ref={linkBriefTooltipRef} offsetX={10} offsetY={10} isFabricTopo={isFabricTopo.current} />
        </>
    );
};

export default UnitTopo;
