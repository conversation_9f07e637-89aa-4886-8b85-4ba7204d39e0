import {useEffect, useState} from "react";
import {Space, Card, Tabs} from "antd";
import {Graph} from "@antv/x6";
import topoUnitStyle from "@/modules-ampcon/pages/PhysicalNetwork/Design/Unit/unit.module.scss";
import UnitTopo from "@/modules-ampcon/pages/PhysicalNetwork/Design/Unit/unit_topo";

const TopoUnitView = ({setComponent}) => {
    const graphRef = useState(null);
    const leafContainerRef = useState(null);
    useEffect(() => {
        // graphRef.current = new Graph({
        //     container: leafContainerRef.current,
        //     width: leafContainerRef.current?.clientWidth,
        //     height: leafContainerRef.current?.clientHeight,
        //     grid: false,
        //     rotating: {
        //         enabled: true
        //     },
        //     panning: {
        //         // enabled: !isEdit,
        //         eventTypes: ["leftMouseDown", "mouseWheel"]
        //     },
        //     connector: "smooth",
        //     mousewheel: {
        //         enabled: true,
        //         modifiers: "ctrl"
        //     },
        //     interacting: {
        //         // nodeMovable: isNodeMovable, // Disable node movement
        //         edgeMovable: false
        //     }
        // });
        // const commonAttrs = {
        //     body: {
        //         fill: "#fff",
        //         stroke: "#8f8f8f",
        //         strokeWidth: 1
        //     },
        //     label: {
        //         refX: 0.5,
        //         refY: "100%",
        //         refY2: 4,
        //         textAnchor: "middle",
        //         textVerticalAnchor: "top"
        //     }
        // };
        // graphRef.current.addNode({
        //     shape: "image",
        //     x: 290,
        //     y: 150,
        //     width: 60,
        //     height: 40,
        //     imageUrl: "https://gw.alipayobjects.com/os/s/prod/antv/assets/image/logo-with-text-73b8a.svg",
        //     label: "image",
        //     attrs: commonAttrs
        // });
    }, []);
    return (
        <div>
            <Card style={{display: "flex", flex: 1}}>
                <Space
                    size={16}
                    direction="vertical"
                    style={{
                        display: "flex",
                        height: "100%",
                        flexDirection: "column",
                        justifyContent: "space-between"
                    }}
                >
                    <a style={{color: "#14c9bb"}} onClick={() => setComponent("Home")}>
                        Back
                    </a>
                    <Tabs
                        rootClassName={topoUnitStyle.unitComponent}
                        items={[
                            {
                                key: "Topology View",
                                label: "Leafs",
                                children: (
                                    <div>
                                        <div
                                            ref={leafContainerRef}
                                            style={{
                                                flex: "1",
                                                width: "100%",
                                                height: "70vh",
                                                marginRight: "8px",
                                                marginLeft: "8px",
                                                borderRadius: "5px",
                                                boxShadow:
                                                    "0 12px 5px -10px rgb(0 0 0 / 10%), 0 0 4px 0 rgb(0 0 0 / 10%)"
                                            }}
                                        >
                                            <UnitTopo />
                                        </div>
                                    </div>
                                )
                            },
                            {
                                key: "Unit Info",
                                label: "Access",
                                children: (
                                    <div className={topoUnitStyle.viewContainer}>
                                        <div>
                                            <h3>Basic Info</h3>
                                            <div>
                                                <div>
                                                    <span>Unit Name</span>
                                                    <span>Demo</span>
                                                </div>
                                                <div>
                                                    <span>Description</span>
                                                    <span>--</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <h3>Leaf Layer</h3>
                                            <div>
                                                <div>
                                                    <span>Leaf Name</span>
                                                    <span>Demo</span>
                                                </div>
                                                <div>
                                                    <span>Leaf Strategy</span>
                                                    <span>MLAG</span>
                                                </div>
                                                <div>
                                                    <span>MLAG Peer-Link VLAN ID</span>
                                                    <span>3</span>
                                                </div>
                                                <div>
                                                    <span>MLAG VLAN ID</span>
                                                    <span>3; 3; 5; 6</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <h3>Access Layer</h3>
                                            <div>
                                                <div>
                                                    <span>Access Name</span>
                                                    <span>Demo</span>
                                                </div>
                                                <div>
                                                    <span>Link Description</span>
                                                    <span>test</span>
                                                </div>
                                                <div>
                                                    <span>Leaf</span>
                                                    <span>Demo</span>
                                                </div>
                                                <div>
                                                    <span>Access Method</span>
                                                    <span>Signle-Homed</span>
                                                </div>
                                                <div>
                                                    <span>Peef Leaf</span>
                                                    <span>name 1</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }
                        ]}
                    />
                </Space>
            </Card>
        </div>
    );
};

export default TopoUnitView;
