import {useState, useRef, useEffect, useImperativeHandle, forwardRef} from "react";
import {Flex} from "antd";
import {flushSync} from "react-dom";

const LinkInfoBody = ({edge, sourceNode, targetNode, isFabricTopo}) => {
    const baseStyle = {
        height: "17px",
        fontWeight: 400,
        fontSize: 14,
        lineHeight: "17px",
        textTransform: "none",
        color: "#212519",
        marginBottom: "8px"
    };
    const edgeInfoKeys = ["Node", "Name", "Interface", "IP Address"];
    return (
        <Flex vertical>
            <div
                style={{
                    height: "40px",
                    background: "#F8FAFB",
                    borderRadius: "4px 4px 0px 0px",
                    lineHeight: "19px",
                    fontFamily: "Lato, Lato",
                    fontWeight: 600,
                    fontSize: "16px",
                    textAlign: "left",
                    fontStyle: "normal",
                    textTransform: "none",
                    paddingTop: 10,
                    paddingLeft: 16,
                    paddingRight: 16,
                    wrap: "nowrap"
                }}
            >
                {isFabricTopo
                    ? `${edge?.source?.cell}====${edge?.target?.cell}`
                    : `${sourceNode?.name}====${targetNode?.name}`}
            </div>
            <Flex style={{margin: "16px"}}>
                <Flex vertical>
                    {edgeInfoKeys.map((key, index) => {
                        return (
                            <div
                                key={`key-1-${index}`}
                                style={{
                                    ...baseStyle,
                                    color: index === 0 ? "#212519" : "#929A9E",
                                    marginBottom: (() => {
                                        if (index === 0) return 10;
                                        if (index === edgeInfoKeys.length - 1) return 16;
                                        return 8;
                                    })()
                                }}
                            >
                                {index === 0 ? `${key} 1` : key}
                            </div>
                        );
                    })}
                    {edgeInfoKeys.map((key, index) => {
                        return (
                            <div
                                key={`key-2-${index}`}
                                style={{
                                    ...baseStyle,
                                    color: index === 0 ? "#212519" : "#929A9E",
                                    marginBottom: index === 0 ? 10 : 8
                                }}
                            >
                                {index === 0 ? `${key} 2` : key}
                            </div>
                        );
                    })}
                </Flex>
                {isFabricTopo ? (
                    <Flex vertical style={{marginTop: "27px", marginLeft: "16px"}}>
                        <div style={{...baseStyle}}>{edge?.source?.cell}</div>
                        <div style={{...baseStyle}}>
                            {edge?.link_info?.length > 0 && edge.link_info[0].source_port !== ""
                                ? edge.link_info[0].source_port
                                : "N/A"}
                        </div>
                        <div style={{...baseStyle, marginBottom: "43px"}}>
                            {edge?.link_info?.length > 0 && edge.link_info[0]?.source_routed_address !== undefined
                                ? edge.link_info[0]?.source_routed_address
                                : "N/A"}
                        </div>
                        <div style={{...baseStyle}}>{edge?.target?.cell}</div>
                        <div style={{...baseStyle}}>
                            {edge?.link_info?.length > 0 && edge.link_info[0].target_port !== ""
                                ? edge.link_info[0].target_port
                                : "N/A"}
                        </div>
                        <div style={{...baseStyle}}>
                            {edge?.link_info?.length > 0 && edge.link_info[0]?.target_routed_address !== undefined
                                ? edge.link_info[0].target_routed_address
                                : "N/A"}
                        </div>
                    </Flex>
                ) : (
                    <Flex vertical style={{marginTop: "27px", marginLeft: "16px"}}>
                        <div style={{...baseStyle}}>{sourceNode?.name}</div>
                        <div style={{...baseStyle}}>
                            {sourceNode?.ports?.items[0].id !== undefined && sourceNode?.ports?.items[0].id !== ""
                                ? sourceNode?.ports?.items[0].id
                                : "N/A"}
                        </div>
                        <div style={{...baseStyle, marginBottom: "43px"}}>N/A</div>
                        <div style={{...baseStyle}}>{targetNode?.name}</div>
                        <div style={{...baseStyle}}>
                            {targetNode?.ports?.items[1].id !== undefined && targetNode?.ports?.items[1].id !== ""
                                ? targetNode?.ports?.items[1].id
                                : "N/A"}
                        </div>
                        <div style={{...baseStyle}}>N/A</div>
                    </Flex>
                )}
            </Flex>
        </Flex>
    );
};

const LinkBriefTooltip = forwardRef(({offsetX, offsetY, isFabricTopo}, ref) => {
    const baseStyle = {
        position: "fixed",
        background: "#FFFFFF",
        boxShadow: "0px 1px 12px 1px #E6E8EA",
        borderRadius: "4px 4px 4px 4px",
        display: "block",
        pointerEvents: "none",
        zIndex: 1000,
        transform: "none",
        whiteSpace: "pre"
    };

    const [isTooltipVisible, setTooltipVisible] = useState(false);
    const [edge, setEdge] = useState({});
    const [sourceNode, setSourceNode] = useState({});
    const [targetNode, setTargetNode] = useState({});
    const [linkStyle, setLinkStyle] = useState({});
    const shouldShowTooltip = useRef(false);

    useEffect(() => {
        const handleMouseMove = e => {
            calculateLinkBriefTooltipStyle(e.clientX, e.clientY);
        };
        window.addEventListener("mousemove", handleMouseMove);

        return () => {
            window.removeEventListener("mousemove", handleMouseMove);
        };
    }, []);

    useEffect(() => {
        if (shouldShowTooltip.current) {
            setTooltipVisible(true);
            shouldShowTooltip.current = false;
        }
    }, [sourceNode, targetNode, edge]);

    useImperativeHandle(ref, () => ({
        showLinkBriefTooltip: (edge, sourceNode, targetNode) => {
            flushSync(() => {
                if (isFabricTopo) {
                    setEdge(edge);
                } else {
                    setSourceNode(sourceNode);
                    setTargetNode(targetNode);
                }
            });
            setTooltipVisible(true);
            shouldShowTooltip.current = true;
        },
        hideLinkBriefTooltip: () => {
            setTooltipVisible(false);
        }
    }));

    const calculateLinkBriefTooltipStyle = (x, y) => {
        const adjustX = x + offsetX;
        const adjustY = y + offsetY;

        const baseStyleTemp = {...baseStyle};
        const linkBriefTooltipHidden = document.getElementsByClassName("link_brief_tooltip_hidden")[0];
        if (linkBriefTooltipHidden) {
            const rectHidden = linkBriefTooltipHidden.getBoundingClientRect();
            if (adjustX + 400 + rectHidden.width > window.innerWidth) {
                baseStyleTemp.right = "50px";
                delete baseStyleTemp.left;
            } else {
                baseStyleTemp.left = `${adjustX + 10}px`;
                delete baseStyleTemp.right;
            }
            if (adjustY + 200 + rectHidden.height > window.innerHeight) {
                baseStyleTemp.bottom = "70px";
                delete baseStyleTemp.top;
            } else {
                baseStyleTemp.top = `${adjustY + 10}px`;
                delete baseStyleTemp.bottom;
            }
        }
        setLinkStyle(baseStyleTemp);
    };

    return (
        <>
            {isTooltipVisible ? (
                <div ref={ref} className="link_brief_tooltip" style={linkStyle}>
                    {isFabricTopo ? (
                        <LinkInfoBody edge={edge} isFabricTopo={isFabricTopo} />
                    ) : (
                        <LinkInfoBody sourceNode={sourceNode} targetNode={targetNode} isFabricTopo={isFabricTopo} />
                    )}
                </div>
            ) : null}
            <div
                ref={ref}
                className="link_brief_tooltip_hidden"
                style={{
                    position: "fixed",
                    background: "#FFFFFF",
                    boxShadow: "0px 1px 12px 1px #E6E8EA",
                    borderRadius: "4px 4px 4px 4px",
                    right: window.innerWidth / 2,
                    bottom: window.innerHeight / 2,
                    display: "block",
                    pointerEvents: "none",
                    zIndex: -1,
                    transform: "none",
                    whiteSpace: "pre",
                    visibility: "hidden"
                }}
            >
                {isFabricTopo ? (
                    <LinkInfoBody edge={edge} isFabricTopo={isFabricTopo} />
                ) : (
                    <LinkInfoBody sourceNode={sourceNode} targetNode={targetNode} isFabricTopo={isFabricTopo} />
                )}
            </div>
        </>
    );
});

export default LinkBriefTooltip;
