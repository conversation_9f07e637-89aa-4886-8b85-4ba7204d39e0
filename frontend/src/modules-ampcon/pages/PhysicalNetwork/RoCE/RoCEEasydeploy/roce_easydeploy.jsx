import React, {useRef, useState} from "react";
import Icon, {QuestionCircleOutlined} from "@ant-design/icons";
import {Space, Button, Input, Form, message, Divider, Flex, Radio, Tooltip, Row, Col, Spin} from "antd";
import {useForm} from "antd/es/form/Form";

import {easydeployConfigSave, easydeployConfigList, easydeployConfigDelete} from "@/modules-ampcon/apis/roce_api";
import {addSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {showCustomErrorMessage} from "@/modules-ampcon/pages/PhysicalNetwork/RoCE/RoCEEasydeploy/custom_message";
import {
    createColumnConfig,
    AmpConCustomTable,
    TableFilterDropdown,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {ConfigurationDetails} from "./configuration_details";

import styles from "./configuration_details.module.scss";

const RoCEEasydeploy = ({onSwitchTab}) => {
    const tableRef = useRef(null);
    const [form] = useForm();
    const [formData, setFormData] = useState({});
    const [applyForm] = useForm();

    const [isEdit, setIsEdit] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [applyConfimModalOpen, setApplyConfirmModalOpen] = useState(false);
    const [isShowSpin, setIsShowSpin] = useState(false);

    const [ApplyDetails, setApplyDetails] = useState(null);

    const columns = [
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown),
            onFilter: (value, record) => record.sysname.toLowerCase().includes(value.toLowerCase()),
            sorter: (a, b) => a.sysname.localeCompare(b.sysname)
        },
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            render: (_, record) => {
                let port = record.port.join(", ");
                if (record.port?.length === 1 && record.port[0] === "all ports") {
                    port = "All Ports";
                }
                return record?.port.length > 5 ? (
                    <Tooltip placement="bottom" title={port}>
                        <p style={{maxWidth: 280, overflow: "hidden", textOverflow: "ellipsis"}}>{port}</p>
                    </Tooltip>
                ) : (
                    <span>{port}</span>
                );
            },
            onFilter: (value, record) => record.port?.some(port => port.toLowerCase().includes(value.toLowerCase())),
            sorter: (a, b) => a.port.join(", ").localeCompare(b.port.join(", "))
        },
        {
            ...createColumnConfig("Queues", "queue", TableFilterDropdown),
            render: (_, record) => {
                const queue = Array.isArray(record.queue) ? record.queue.join(", ") : record.queue;
                return <span>{queue}</span>;
            },
            onFilter: (value, record) => record.queue?.some(queue => queue.includes(value)),
            sorter: (a, b) => a.queue.join(", ").localeCompare(b.queue.join(", "))
        },
        {
            ...createColumnConfig("RoCE EasyDeploy", "enabled", TableFilterDropdown),
            render: (_, record) => <span>{record.enabled.charAt(0).toUpperCase() + record.enabled.slice(1)}</span>,
            onFilter: (value, record) => record.enabled.toLowerCase().includes(value.toLowerCase()),
            sorter: (a, b) => a.enabled.localeCompare(b.enabled)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a onClick={() => editConfiguration(record)}>Edit</a>
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete the configuration item?", () =>
                                        delConfiguration(record.id)
                                    )
                                }
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const matchFieldsList = [
        {name: "sysname", matchMode: "fuzzy"},
        {name: "port", matchMode: "fuzzy"},
        {name: "queue", matchMode: "fuzzy"},
        {name: "enabled", matchMode: "fuzzy"}
    ];
    const searchFieldsList = ["sysname", "port", "queue", "enabled"];

    const easydeployConfigListWrapper = async (page, pageSize, filterFields, sortFields, searchFields) => {
        const res = await easydeployConfigList(page, pageSize, filterFields, sortFields, searchFields);

        const formatedData = res.data.map(item => {
            return {
                ...item,
                port: item.port.sort().map(port => {
                    return port === "all_ports" ? "all ports" : port;
                }),
                queue: item.queue.sort(),
                enabled: item.enabled ? "enabled" : "disabled"
            };
        });

        return {
            ...res,
            data: formatedData
        };
    };

    const onSubmit = async () => {
        const configuration = JSON.parse(JSON.stringify(form.getFieldsValue().configurations));
        if (Array.isArray(configuration)) {
            configuration.forEach((config, index) => {
                if (!config) {
                    configuration[index] = {};
                }
                const sysnameTemp = configuration[index].sysname;
                const selectSysname = [];
                const selectSN = [];
                // sysname(sn) => sysname and sn
                sysnameTemp?.map(item => {
                    selectSysname.push(item.match(/^(.*?)\((.*?)\)$/)[1]);
                    selectSN.push(item.match(/^(.*?)\((.*?)\)$/)[2]);
                });
                configuration[index].sysname = selectSysname;
                configuration[index].switch_sn = selectSN;
            });
        }

        const data = {
            enable_pfc: enable === "enabled",
            mode: enable === "disabled" ? null : form.getFieldsValue().mode,
            configurations: configuration
        };
        setFormData(data);
        const ret = await easydeployConfigSave(data, "preview");
        if (ret.status === 200) {
            let content = ``;
            ret.commands.map(command => {
                command.cli.map(cli => {
                    content += cli;
                    content += `\n`;
                });
            });
            setApplyDetails(content);
            setIsModalOpen(false);
            setApplyConfirmModalOpen(true);
        } else {
            message.error(ret.msg);
        }
    };
    const onApplyClick = async () => {
        setIsShowSpin(true);
        const ret = await easydeployConfigSave(formData, "save");
        setIsShowSpin(false);
        if (ret.status === 200) {
            message.success(ret.msg);

            form.resetFields();
            setApplyDetails();
            setApplyConfirmModalOpen(false);

            return onSwitchTab("overview");
        }

        // 处理多分段错误 (Handle multi-segment errors)
        if (ret.data && Array.isArray(ret.data) && ret.data.length > 0) {
            showCustomErrorMessage(ret.msg, ret.data, 6);

            form.resetFields();
            setApplyConfirmModalOpen(false);
        } else {
            message.error(ret.msg);

            form.resetFields();
            setApplyConfirmModalOpen(false);
        }
        tableRef.current.refreshTable();
    };

    const [enable, setEnable] = useState("enabled");
    const [mode, setMode] = useState("lossless");

    const [fields, setFields] = useState([{fabric: null, sysname: [], ports: [], queue_num: []}]);

    const formItems = () => {
        return (
            <>
                <Form.Item
                    className={styles.formItem}
                    name="enabled"
                    label={
                        <>
                            <span>RoCE EasyDeploy</span>
                            <Tooltip title="Enables RoCE configurations (lossy mode or lossless mode) in one click. The default mode is lossless mode.">
                                <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer"}} />
                            </Tooltip>
                        </>
                    }
                >
                    <Row gutter={[16, 16]}>
                        <Col span={8}>
                            <Radio.Group
                                value={enable}
                                onChange={e => {
                                    setEnable(e.target.value);
                                    if (e.target.value === "disabled") {
                                        const updatedConfigurations = form.getFieldsValue().configurations;
                                        updatedConfigurations.map(config => {
                                            if (config) {
                                                config.ports = [];
                                                config.queue_num = [];
                                            }
                                        });
                                        form.setFieldsValue({
                                            configurations: updatedConfigurations
                                        });
                                        form.getFieldsValue().configurations.map((config, index) => {
                                            form.validateFields([
                                                ["configurations", index, "ports"],
                                                ["configurations", index, "queue_num"]
                                            ]);
                                        });
                                    }
                                }}
                            >
                                <Radio value="enabled">
                                    <span style={{marginRight: "26px"}}>Enable</span>
                                </Radio>
                                <Radio value="disabled">Disable</Radio>
                            </Radio.Group>
                        </Col>
                    </Row>
                </Form.Item>
                <Form.Item
                    className={styles.formItem}
                    name="mode"
                    label="RoCE Mode"
                    style={{display: enable === "enabled" ? "" : "none"}}
                >
                    <Row gutter={[16, 16]}>
                        <Col span={8}>
                            <Radio.Group value={mode} onChange={e => setMode(e.target.value)}>
                                <Radio value="lossless">
                                    <span>Lossless</span>
                                    <Tooltip title="Lossless mode enables both PFC and ECN to avoid packet loss and ensure reliable and lossless data transmission.">
                                        <QuestionCircleOutlined
                                            style={{color: "#999", marginLeft: 4, cursor: "pointer"}}
                                        />
                                    </Tooltip>
                                </Radio>
                                <Radio value="lossy">
                                    <span>Lossy</span>
                                    <Tooltip title="Lossy mode enables only ECN and allows packet loss to improve network throughput.">
                                        <QuestionCircleOutlined
                                            style={{color: "#999", marginLeft: 4, cursor: "pointer"}}
                                        />
                                    </Tooltip>
                                </Radio>
                            </Radio.Group>
                        </Col>
                    </Row>
                </Form.Item>
                <div
                    style={{
                        fontSize: "18px",
                        fontWeight: "bold",
                        borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                        margin: "5px -24px 20px -24px",
                        padding: "0 24px 10px 24px"
                    }}
                >
                    Configuration Details
                </div>
                <Form.List name="configurations" initialValue={fields}>
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name, fieldNames, ...restField}, index) => {
                                return (
                                    <Row key={key}>
                                        <Col span={24}>
                                            <ConfigurationDetails
                                                name={name}
                                                index={index}
                                                form={form}
                                                isModalOpen={isModalOpen}
                                                isEdit={isEdit}
                                                enable={enable}
                                                add={add}
                                                remove={remove}
                                            />
                                        </Col>
                                    </Row>
                                );
                            })}
                        </>
                    )}
                </Form.List>
            </>
        );
    };

    const editConfiguration = record => {
        const configurations = [
            {
                config_id: record.id,
                fabric: record.fabric,
                sysname: [`${record.sysname} ${record.switch_sn}`],
                ports: record.port,
                queue_num: record.queue
            }
        ];
        form.setFieldsValue({
            id: record.id,
            enabled: record.enabled,
            mode: record.mode || "lossless",
            configurations
        });
        setEnable(record.enabled);
        setMode(record.mode || "lossless");
        setIsEdit(true);
        setIsModalOpen(true);
    };

    const delConfiguration = async configId => {
        setIsShowSpin(true);
        const data = {
            config_id: configId
        };
        const ret = await easydeployConfigDelete(data);
        setIsShowSpin(false);
        if (ret.status === 200) {
            message.success(ret.msg);
        } else {
            if (ret.data && Array.isArray(ret.data) && ret.data.length > 0) {
                showCustomErrorMessage(ret.msg, ret.data, 6);
            } else {
                message.error(ret.msg);
            }
        }
        tableRef.current.refreshTable();
    };

    return (
        <>
            <AmpConCustomModalForm
                title={isEdit ? "Edit Configuration" : "Create Configuration"}
                isModalOpen={isModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={formItems}
                onCancel={() => {
                    setIsModalOpen(false);
                    form.resetFields();
                }}
                onSubmit={onSubmit}
                modalClass={`ampcon-max-modal ${styles.modalForm}`}
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button
                        key="cancel"
                        onClick={() => {
                            setIsModalOpen(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={form.submit}>
                        Apply
                    </Button>
                ]}
            />
            <AmpConCustomModalForm
                modalClass="CheckResult-custom-wider-modal"
                title="Confirm RoCE Configurations to Be Applied"
                isModalOpen={applyConfimModalOpen}
                formInstance={applyForm}
                layoutProps={{
                    labelCol: {
                        span: 5
                    }
                }}
                CustomFormItems={
                    <Flex flex={1} layout="horizontal" style={{minHeight: "260.23px"}}>
                        <Input.TextArea
                            style={{
                                height: `${window.innerHeight / 2}px`,
                                border: "none",
                                backgroundColor: "#F8FAFB",
                                fontSize: "16px",
                                borderRadius: "4px",
                                boxShadow: "none",
                                resize: "none",
                                padding: "16px"
                            }}
                            value={ApplyDetails}
                            readOnly
                        />
                    </Flex>
                }
                footer={
                    <Flex vertical>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Flex justify="flex-end">
                            <Button
                                onClick={() => {
                                    setApplyConfirmModalOpen(false);
                                    setIsModalOpen(true);
                                    applyForm.resetFields();
                                }}
                            >
                                Cancel
                            </Button>
                            <Button type="primary" onClick={() => onApplyClick()}>
                                Apply
                            </Button>
                        </Flex>
                    </Flex>
                }
                onCancel={() => {
                    form.resetFields();
                    setApplyConfirmModalOpen(false);
                }}
            />
            <AmpConCustomTable
                columns={columns}
                fetchAPIInfo={easydeployConfigListWrapper}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <Button
                        type="primary"
                        style={{width: 140, height: 32}}
                        onClick={() => {
                            setIsEdit(false);
                            setIsModalOpen(true);
                            form.setFieldValue("enabled", "enabled");
                            form.setFieldValue("mode", "lossless");
                            setEnable("enabled");
                            setMode("lossless");
                        }}
                    >
                        <Icon component={addSvg} />
                        Configuration
                    </Button>
                }
                ref={tableRef}
            />
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
        </>
    );
};

export default RoCEEasydeploy;
