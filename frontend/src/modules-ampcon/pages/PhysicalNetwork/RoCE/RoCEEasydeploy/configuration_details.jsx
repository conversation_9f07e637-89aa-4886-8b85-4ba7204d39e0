import React, {useEffect, useState, useRef} from "react";
import {Row, Col, Form, Select, TreeSelect, message, Button, Tooltip} from "antd";
import {PlusOutlined, MinusOutlined} from "@ant-design/icons";

import {getFabric} from "@/modules-ampcon/apis/lifecycle_api";
import {
    roceGetFabricSwitches,
    getSwitchPorts,
    easydeployConfigList,
    getFilterSwitchPorts,
    validateEasydeployConfig
} from "@/modules-ampcon/apis/roce_api";

import shrinkSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink.svg?react";
import unfoldSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold.svg?react";
import shrinkHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink_hover.svg?react";
import unfoldHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold_hover.svg?react";

import styles from "./configuration_details.module.scss";

const {Option} = Select;

export const ConfigurationDetails = ({name, index, form, isModalOpen, isEdit, enable, add, remove}) => {
    const prevSysnames = useRef([]);

    const [record, setRecord] = useState([]);
    const [fabricLists, setFabricLists] = useState([]);
    const [selectedFabric, setSelectedFabric] = useState();
    const [switchData, setSwitchData] = useState();
    const [allSysnameLists, setAllSysnameLists] = useState([]);
    const [filteredSysnameLists, setFilteredSysnameLists] = useState([]);
    const [sysname, setSysname] = useState([]);
    const [selectedSysname, setSelectedSysname] = useState([]);
    const [selectedSN, setSelectedSN] = useState([]);
    const [queueLists, setQueueLists] = useState([]);
    const [portLists, setPortLists] = useState([]);
    const [selectPorts, setSelectPorts] = useState([]);

    const checkSelectAll = () => {
        const configuration = form.getFieldValue("configurations") || [];
        return !configuration.some(item => item?.fabric === "All Fabrics");
    };

    const [hoveredIcons, setHoveredIcons] = useState({});

    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };
    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };

    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);

        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px", marginLeft: "8px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    const maxTagPlaceholder = omittedValues => {
        return (
            <span title={omittedValues.map(item => item.label || item.value).join("\n")}>+{omittedValues.length}</span>
        );
    };

    const fetchFabricList = async () => {
        try {
            // const responses = await getFabric();
            const response = await roceGetFabricSwitches({query_model: null});
            if (response.status === 200) {
                // 取有可用 sysname 的 fabric
                Object.keys(response.data).forEach(key => {
                    response.data[key] = response.data[key].filter(
                        item => item.sysname !== null && item.sysname !== undefined && item.enabled
                    );
                    if (response.data[key].length === 0) delete response.data[key];
                });
                setSwitchData(response.data);
                setFabricLists(Object.keys(response.data));
                setFabricLists(prevLists => ["All Fabrics", ...prevLists]);
            } else {
                message.error(response.info);
            }
        } catch (error) {
            message.error("Failed to get Fabric.");
        }
    };

    const getExistingSysnames = () => {
        const existingSysnames = new Set();
        const configurations = form.getFieldValue("configurations") || [];
        configurations
            .filter((_, configIndex) => configIndex !== index)
            .forEach(config => {
                if (config?.sysname) {
                    config.sysname.map(item => {
                        existingSysnames.add(item);
                    });
                }
            });
        return existingSysnames;
    };

    const onChangeSysname = value => {
        setSysname(value);
        const configuration = form.getFieldsValue().configurations;
        configuration[index].ports = [];

        const selectSysname = [];
        const selectSN = [];
        // Sysname(SN) => Sysname and SN
        value.map(item => {
            selectSysname.push(item.match(/^(.*?)\((.*?)\)$/)[1]);
            selectSN.push(item.match(/^(.*?)\((.*?)\)$/)[2]);
        });
        setSelectedSysname(selectSysname);
        setSelectedSN(selectSN);
    };

    const fetchSysnameLists = async () => {
        try {
            const record = await easydeployConfigList(1, 10, [], [], {});
            if (record.status === 200) {
                setRecord(record.data);
            } else {
                return message.error(record.msg);
            }

            let sysnameList;
            if (selectedFabric === "All Fabrics") {
                sysnameList = Object.values(switchData).flatMap(array =>
                    array.map(item => {
                        return `${item.sysname}(${item.switch_sn})`;
                    })
                );
            } else {
                sysnameList = switchData[selectedFabric].map(item => {
                    return `${item.sysname}(${item.switch_sn})`;
                });
            }
            const existingSysnames = getExistingSysnames();
            const config_ids = form.getFieldsValue().configurations.map(item => item?.config_id);
            record.data
                .filter(item => !config_ids.includes(item.id))
                .map(item => {
                    existingSysnames.add(`${item.sysname}(${item.switch_sn})`);
                });
            sysnameList = sysnameList.filter(item => !existingSysnames.has(item));
            setAllSysnameLists(sysnameList);

            if (selectedFabric === "All Fabrics") {
                const updatedConfigurations = form.getFieldsValue().configurations;
                updatedConfigurations[index] = {
                    ...updatedConfigurations[index],
                    sysname: sysnameList
                };
                form.setFieldsValue({
                    configurations: updatedConfigurations
                });
                onChangeSysname(sysnameList);
                form.validateFields([
                    ["configurations", index, "sysname"],
                    ["configurations", index, "ports"]
                ]);
            } else {
                const sysnameTreeData = [
                    {
                        key: "all",
                        value: "all",
                        title: "All",
                        children: sysnameList.map(item => ({
                            title: item,
                            value: item,
                            key: item
                        }))
                    }
                ];
                if (sysnameList.length > 0) setFilteredSysnameLists(sysnameTreeData);
                else setFilteredSysnameLists([]);
            }
        } catch (error) {
            message.error("Failed to obtain sysname information.");
        }
    };

    const fetchPortLists = async () => {
        form.getFieldsValue().configurations.map(item => {
            const configId = item?.config_id || "";
            item?.sysname.map(async eachSysname => {
                // 解析旧数据
                if (eachSysname.includes(" ")) {
                    const formatedSysname = `${eachSysname.split(" ")[0]}(${eachSysname.split(" ")[1]})`;
                    eachSysname = formatedSysname;
                }
                const sysname = eachSysname.match(/^(.*?)\((.*?)\)$/)[1];
                const switch_sn = eachSysname.match(/^(.*?)\((.*?)\)$/)[2];
                const data = {
                    config_id: configId,
                    sysname,
                    switch_sn
                };
                const ret = await validateEasydeployConfig(data);
                if (ret.status !== 200) return;
            });
        });
        let portTreeData = [
            {
                key: "all_ports",
                value: "all_ports",
                title: "All Ports"
            }
        ];
        const queueTreeData = [
            {
                key: "all",
                value: "all",
                title: "All",
                children: ["0", "1", "2", "4", "5", "7"].map(item => ({
                    title: item,
                    value: item,
                    key: item
                }))
            }
        ];
        setQueueLists(queueTreeData);

        if (sysname.length === 1) {
            try {
                const data = {
                    switch_sn: selectedSN,
                    query_model: "RoceEasyDeployConfiguration"
                };
                // const response = await getSwitchPorts(data);
                const response = await getFilterSwitchPorts(data);
                if (response.status === 200) {
                    if (response.data.length > 0) {
                        portTreeData = [
                            {
                                key: "all_ports",
                                value: "all_ports",
                                title: "All Ports",
                                children: response.data
                                    // .filter(item => item.enabled === true)
                                    .map(item => ({
                                        title: item.port_name,
                                        value: item.port_name,
                                        key: item.port_name
                                    }))
                            }
                        ];
                        setPortLists(portTreeData);
                    } else setPortLists([]);
                } else {
                    message.error(response.msg);
                }
            } catch (error) {
                message.error("Failed to obtain port information.");
            }
        }
        if (sysname.length > 1 || (sysname.length === 1 && selectedFabric === "All Fabrics")) {
            setPortLists(portTreeData);
            if (enable === "enabled") {
                setSelectPorts(["all_ports"]);
                const updatedConfigurations = form.getFieldsValue().configurations;
                updatedConfigurations[index] = {
                    ...updatedConfigurations[index],
                    ports: ["all_ports"]
                };
                form.setFieldsValue({
                    configurations: updatedConfigurations
                });
                form.validateFields([["configurations", index, "ports"]]);
            }
        }
    };

    useEffect(() => {
        if (!isModalOpen || enable === "disabled") return;

        const config = form.getFieldsValue().configurations;
        // 数据解析sysname and ports
        const originalConfig = config[index];
        if (originalConfig) {
            config[index].sysname = originalConfig.sysname?.map(item => {
                if (item.includes(" ")) return `${item.split(" ")[0]}(${item.split(" ")[1]})`;
                return item;
            });
            config[index].ports = originalConfig.ports?.map(item => {
                if (item === "all ports") return "all_ports";
                return item;
            });
        }
        setSelectedFabric(config[index]?.fabric);
        const sysname = config[index]?.sysname;
        setSysname(config[index]?.sysname);
        const selectSysname = [];
        const selectSN = [];
        sysname?.map(item => {
            selectSysname.push(item.match(/^(.*?)\((.*?)\)$/)[1]);
            selectSN.push(item.match(/^(.*?)\((.*?)\)$/)[2]);
        });
        setSelectedSysname(selectSysname);
        setSelectedSN(selectSN);
    }, [isModalOpen, enable]);

    useEffect(() => {
        fetchFabricList();
    }, []);

    useEffect(() => {
        if (selectedFabric && switchData) {
            fetchSysnameLists();
        }
    }, [selectedFabric, switchData]);

    useEffect(() => {
        if (selectedFabric) {
            const configuration = form.getFieldsValue().configurations || [];
            const currentSysnames = configuration.map(config => config?.sysname).filter(Boolean);

            if (JSON.stringify(currentSysnames) !== JSON.stringify(prevSysnames.current)) {
                const existingSysnames = getExistingSysnames();
                const config_ids = form.getFieldsValue().configurations.map(item => item?.config_id);
                record
                    .filter(item => !config_ids.includes(item.id))
                    .map(item => {
                        existingSysnames.add(`${item.sysname}(${item.switch_sn})`);
                    });
                const sysnameTreeData = [
                    {
                        key: "all",
                        value: "all",
                        title: "All",
                        children: allSysnameLists
                            .filter(item => !existingSysnames.has(item))
                            .map(item => ({
                                title: item,
                                value: item,
                                key: item
                            }))
                    }
                ];
                if (allSysnameLists.filter(item => !existingSysnames.has(item)).length > 0)
                    setFilteredSysnameLists(sysnameTreeData);
                else setFilteredSysnameLists([]);
            }
            prevSysnames.current = currentSysnames;
        }
    }, [form.getFieldsValue().configurations]);

    useEffect(() => {
        setPortLists();
        if (sysname) {
            fetchPortLists();
        }
    }, [sysname]);

    return (
        <Row gutter={8}>
            <Col span={5}>
                <Form.Item
                    // {...restField}
                    className={styles.formItem}
                    name={[name, "fabric"]}
                    label={
                        index === 0 ? (
                            <>
                                Fabric <span className={styles.requiredIcon1}>*</span>
                            </>
                        ) : (
                            ""
                        )
                    }
                    labelCol={{span: 24}}
                    wrapperCol={{span: 24}}
                    rules={[{required: true, message: "Please select fabric!"}]}
                >
                    <Select
                        disabled={
                            (isEdit && !!form.getFieldValue(["configurations", name, "config_id"])) ||
                            (isEdit &&
                                enable === "disabled" &&
                                !!form.getFieldValue(["configurations", name, "config_id"]))
                        }
                        value={selectedFabric}
                        onChange={value => {
                            setSelectedFabric(value);
                            const configuration = form.getFieldsValue().configurations;
                            configuration[index].sysname = [];
                            configuration[index].ports = [];
                            setSysname([]);
                        }}
                        placeholder="Fabric"
                        style={{width: "100%"}}
                    >
                        {fabricLists.map(name => (
                            <Option key={name} value={name}>
                                {name}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
            </Col>
            <Col span={5}>
                <Form.Item
                    className={styles.formItem}
                    name={[name, "sysname"]}
                    label={
                        index === 0 ? (
                            <>
                                Sysname <span className={styles.requiredIcon1}>*</span>
                            </>
                        ) : (
                            ""
                        )
                    }
                    labelCol={{span: 24}}
                    wrapperCol={{span: 24}}
                    rules={[{required: true, message: "Please select sysname!"}]}
                >
                    <TreeSelect
                        popupClassName="custom-popup"
                        disabled={
                            selectedFabric === "All Fabrics" ||
                            (isEdit && !!form.getFieldValue(["configurations", name, "config_id"])) ||
                            (isEdit &&
                                enable === "disabled" &&
                                !!form.getFieldValue(["configurations", name, "config_id"]))
                        }
                        maxTagCount={2}
                        maxTagTextLength={6}
                        title={sysname?.join("\n")}
                        treeData={filteredSysnameLists}
                        value={sysname}
                        maxTagPlaceholder={omittedValues => maxTagPlaceholder(omittedValues)}
                        onChange={value => onChangeSysname(value)}
                        treeCheckable
                        showSearch={false}
                        treeDefaultExpandAll
                        switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                        placeholder="Sysname"
                        style={{height: 32, width: "100%"}}
                        allowClear
                        virtual={false}
                        showCheckedStrategy={TreeSelect.SHOW_CHILD}
                    />
                </Form.Item>
            </Col>
            <Col span={5}>
                <Form.Item
                    className={styles.formItem}
                    name={[name, "ports"]}
                    label={
                        index === 0 ? (
                            <>Ports {enable !== "disabled" && <span className={styles.requiredIcon1}>*</span>}</>
                        ) : (
                            ""
                        )
                    }
                    labelCol={{span: 24}}
                    wrapperCol={{span: 24}}
                    rules={[{required: enable !== "disabled", message: "Please select port!"}]}
                >
                    <TreeSelect
                        popupClassName="custom-popup"
                        disabled={sysname?.length > 1 || enable === "disabled"}
                        maxTagCount={2}
                        maxTagTextLength={12}
                        treeData={portLists}
                        value={selectPorts}
                        onChange={value => setSelectPorts(value)}
                        treeCheckable
                        showSearch={false}
                        treeDefaultExpandAll
                        maxTagPlaceholder={omittedValues => maxTagPlaceholder(omittedValues)}
                        switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                        placeholder="Ports"
                        style={{height: 32, width: "100%"}}
                        allowClear
                        virtual={false}
                        showCheckedStrategy={TreeSelect.SHOW_PARENT}
                    />
                </Form.Item>
            </Col>
            <Col span={5}>
                <Form.Item
                    className={styles.formItem}
                    name={[name, "queue_num"]}
                    label={
                        index === 0 ? (
                            <>Queues {enable !== "disabled" && <span className={styles.requiredIcon1}>*</span>}</>
                        ) : (
                            ""
                        )
                    }
                    labelCol={{span: 24}}
                    wrapperCol={{span: 24}}
                    rules={[{required: enable !== "disabled", message: "Please select queue!"}]}
                >
                    <TreeSelect
                        popupClassName="custom-popup"
                        disabled={enable === "disabled"}
                        maxTagCount={2}
                        maxTagTextLength={6}
                        treeData={queueLists}
                        treeCheckable
                        showSearch={false}
                        treeDefaultExpandAll
                        switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                        placeholder="Queues"
                        style={{height: 32, width: "100%"}}
                        allowClear
                        virtual={false}
                        showCheckedStrategy={TreeSelect.SHOW_CHILD}
                    />
                </Form.Item>
            </Col>
            {index === 0 ? (
                checkSelectAll() && (
                    <Col>
                        <Button
                            onClick={() => add()}
                            style={{
                                backgroundColor: "transparent",
                                color: "#BFBFBF",
                                marginBottom: "12px",
                                marginTop: "29px",
                                width: "auto"
                            }}
                            type="link"
                            icon={<PlusOutlined />}
                        />
                    </Col>
                )
            ) : (
                <Col>
                    <Button
                        style={{
                            backgroundColor: "transparent",
                            color: "#BFBFBF",
                            marginBottom: "16px",
                            width: "auto"
                        }}
                        type="link"
                        icon={<MinusOutlined />}
                        onClick={() => {
                            remove(index);
                        }}
                    />
                </Col>
            )}
        </Row>
    );
};
