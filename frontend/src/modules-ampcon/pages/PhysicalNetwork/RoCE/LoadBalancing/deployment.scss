h3 {
 font-weight: 700;
 font-size: 18px;
 line-height: 22px;
}

.device-status {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 14px;
  
  &--online {
    font-family: Lato, Lato;
    color: #2BC174;
    background: rgba(43,193,116,0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #2BC174;
  }
  
  &--offline {
    font-family: Lato, Lato;
    color: #F53F3F;
    background: rgba(245, 63, 63, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #F53F3F;
  }

  &--unkown {
    font-family: Lato, Lato;
    color: #B3BBC8;
    background: rgba(244, 245, 247, 1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #DADCE1 ;
  }
}
.dlb-config-icon {
    color: #B3BBC8; /* 默认颜色 */
    transition: color 0.2s, background 0.2s;
    border-radius: 50%;
    background: transparent;
}
.dlb-config-icon:hover {
  color: #14C9BB;
}

/* 选中时外围变白色 */
.ant-radio-checked .ant-radio-inner {
  background-color:#fff !important;
  border-color: #14C9BB !important;  /* 外圈变白色 */
}

/* 选中时中间圆点为主题色 */
.ant-radio-checked .ant-radio-inner::after {
  background-color: #14C9BB !important;
}
