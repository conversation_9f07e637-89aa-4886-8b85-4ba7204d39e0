import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import Deployment from "./Deployment";
import Overview from "./Overview";
import ProtectedRoute from "@/modules-ampcon/utils/util";

const LoadBalancingManager = () => {
    const items = [
        {
            key: "deployment",
            label: "Deployment",
            children: <ProtectedRoute component={Deployment} />
        },
        {
            key: "overview",
            label: "Overview",
            children: <ProtectedRoute component={Overview} />
        }
    ];

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    useEffect(() => {
        const currentPath = location.pathname;
        if (/(deployment|overview)$/.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(/(deployment|overview)$/)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (/(deployment|overview)$/.test(currentPath)) {
            const matchLength = currentPath.match(/(deployment|overview)$/)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <div className="easydeployTabs">
            <Tabs
                style={{flex: 1}}
                activeKey={currentActiveKey}
                items={items}
                onChange={onChange}
                destroyInactiveTabPane
            />
        </div>
    );
};

export default LoadBalancingManager;
