import React, {useEffect, useState, useRef} from "react";
import {Tooltip} from "antd";
import {QuestionCircleFilled, QuestionCircleOutlined} from "@ant-design/icons";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {getPfcWdConfigList} from "@/modules-ampcon/apis/roce_api";
import CustomQuestionIcon from "./QuestionIcon";
import {ExtendedTable} from "./ext_table";
import {renderArrayColumn} from "./utils";

// PFC WatchDog Configuration Overview 组件
const PFCWatchDogConfigurationOverview = () => {
    const [loading, setLoading] = useState(false);
    const tableRef = useRef(null);

    const searchFieldsList = ["sysname", "port"];
    const matchFieldsList = [
        {name: "sysname", matchMode: "fuzzy"},
        {name: "port", matchMode: "fuzzy"}
    ];

    // 修改列配置，与 PFCWDConfiguration 保持一致
    const watchdogColumns = [
        // {
        //     title: "",
        //     key: "expand",
        //     width: 50,
        //     render: (_, record) => {}
        // },
        createColumnConfig("Sysname", "sysname", TableFilterDropdown),
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            sorter: (a, b) => a.port.join("").localeCompare(b.port.join("")),
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("PFC Watchdog Enabled", "enabled"),
            sorter: (a, b) => a.enabled - b.enabled,
            render: (_, record) => {
                return <div>{record.enabled ? "Enabled" : "Disabled"}</div>;
            }
        },
        {
            ...createColumnConfig("Queues", "queue", TableFilterDropdown),
            sorter: (a, b) => a.queue.join("").localeCompare(b.queue.join("")),
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("Detect Interval (ms)", "detection_interval"),
            sorter: (a, b) => a.detection_interval - b.detection_interval,
            render: text => text || "--"
        },
        {
            ...createColumnConfig("Restore Interval (ms)", "restore_interval"),
            sorter: (a, b) => a.restore_action - b.restore_action,
            render: text => text || "--"
        }
        // createColumnConfig("Restore Action", "restore_action"),
        // createColumnConfig("Restore Mode", "restore_mode")
        // createColumnConfig("Granularity", "granularity", TableFilterDropdown),
        // createColumnConfig("Restore Action", "restore_action", TableFilterDropdown),
        // createColumnConfig("Restore Mode", "restore_mode", TableFilterDropdown)
    ];

    return (
        <div style={{marginBottom: 24, marginTop: 24}}>
            <h3 style={{marginBottom: 4}}>
                PFC WatchDog Information
                <Tooltip
                    title="Enable PFC watchdog to detect and resolve PFC deadlocks. PFC watchdog monitors the duration of PFC pause frames and takes corrective actions if a potential deadlock is detected."
                    placement="right"
                >
                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}} />
                </Tooltip>
            </h3>
            <ExtendedTable
                columns={watchdogColumns}
                fetchAPIInfo={getPfcWdConfigList}
                // loading={loading}
                isShowPagination
                ref={tableRef}
                bordered
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
            />
        </div>
    );
};

export default PFCWatchDogConfigurationOverview;
