import {useState, useRef, useEffect, useMemo} from "react";
import {
    Button,
    Tooltip,
    Form,
    Space,
    Row,
    Col,
    Select,
    Input,
    InputNumber,
    TreeSelect,
    Divider,
    message,
    Spin
} from "antd";
import Icon, {QuestionCircleOutlined, PlusOutlined, MinusOutlined, DownOutlined} from "@ant-design/icons";
import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {
    getSchedulingConfigList,
    getFabricSwitches,
    getSwitchPorts,
    getFilterSwitchPorts,
    saveSchedulingConfig,
    updateSchedulingConfig,
    deleteSchedulingConfig,
    getQosConfigDetailBySwitch,
    validateQosBasicConfig,
    validateQosIngressConfig,
    validateQosEgressConfig
} from "@/modules-ampcon/apis/roce_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CustomTreeSelect from "./customTreeSelect";
import {ExtendedTable} from "./ext_table";
import CustomQuestionIcon from "./QuestionIcon";
import style from "./roce_policies.module.scss";
import {fetchQosFabricSwitchList, renderArrayColumn} from "./utils";
import {addSvg} from "@/utils/common/iconSvg";
import {AmpConTreeSelect} from "@/modules-ampcon/components/custom_tree";
import {showCustomErrorMessage} from "@/modules-ampcon/pages/PhysicalNetwork/RoCE/RoCEEasydeploy/custom_message";

// 统一的表格单元格溢出样式
const cellOverflowStyle = {
    maxWidth: "200px",
    // lineHeight: "48px",
    overflow: "hidden",
    textOverflow: "ellipsis",
    // whiteSpace: "nowrap",
    cursor: "pointer"
};

const {Option} = Select;

const SchedulingConfig = () => {
    // 状态管理 (State Management)
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [form] = Form.useForm();
    const [isShowSpin, setIsShowSpin] = useState(false);
    const [selectedSysnames, setSelectedSysnames] = useState([]);
    // 优化数据源管理 - 参考 PFCConfiguration
    const [fabricTreeData, setFabricTreeData] = useState([]);
    const [ingressPortTreeData, setIngressPortTreeData] = useState([]); // 分离ingress端口树数据
    const [egressPortTreeData, setEgressPortTreeData] = useState([]); // 分离egress端口树数据
    const [ingressExpandedKeys, setIngressExpandedKeys] = useState([]);
    const [egressExpandedKeys, setEgressExpandedKeys] = useState([]);
    const [portDataCache, setPortDataCache] = useState(new Map()); // 添加端口数据缓存

    // 在 SchedulingConfig 组件中添加状态变量
    const tableRef = useRef(null);

    // 参考 PFCConfiguration 添加编辑相关状态
    const [editRecord, setEditRecord] = useState(null);
    const [saveLoading, setSaveLoading] = useState(false);
    const [editPortData, setEditPortData] = useState([]);

    // 修改状态定义，支持未选择sysname的情况
    const [schedulerOptionsMap, setSchedulerOptionsMap] = useState({}); // 格式: {switch_sn: [scheduler1, scheduler2, ...], 'temp_row_0': [...], 'temp_row_1': [...]}
    const [searchValueMap, setSearchValueMap] = useState({}); // 存储每行的临时输入值
    const [schedulerCount, setSchedulerCount] = useState(1);
    const [searchValue, setSearchValue] = useState("");

    const matchFieldsList = [
        {name: "sysname", matchMode: "fuzzy"},
        {name: "port", matchMode: "fuzzy"}
    ];

    // 添加状态来跟踪每行的scheduler值
    const [schedulerValuesMap, setSchedulerValuesMap] = useState({}); // 格式: {row_0: 'scheduler_name', row_1: 'scheduler_name'}

    // 添加状态来跟踪trust mode变化，触发队列选项更新
    const [trustModeMap, setTrustModeMap] = useState({}); // 格式: {row_0: 'dscp', row_1: 'inet-precedence'}

    const ingressInitValue = {
        sysname: null,
        classifier: "",
        trust_mode: null,
        ports: [],
        forwarding_class: null,
        queue: []
    };

    const egressInitValue = {
        sysname: null,
        scheduler_profile: "",
        scheduler: null,
        ports: [],
        forwarding_class: null,
        local_priority: null
    };

    // Helper function to parse queue range string into array
    const parseQueueRanges = (queueString, trustMode) => {
        if (!queueString || queueString.trim() === "") {
            return [];
        }

        const maxQueue = trustMode === "dscp" ? 63 : 7;
        const ranges = queueString.split(",").map(range => range.trim());
        const queueSet = new Set();

        for (const range of ranges) {
            if (range.includes("-")) {
                const [start, end] = range.split("-").map(num => parseInt(num.trim(), 10));
                if (isNaN(start) || isNaN(end)) {
                    throw new Error(`Invalid range format: ${range}. Valid range: 0-${maxQueue}`);
                }
                if (start > end) {
                    throw new Error(
                        `Invalid range: ${range}. Start value must be less than or equal to end value. Valid range: 0-${maxQueue}`
                    );
                }
                if (start < 0 || end > maxQueue) {
                    throw new Error(`Queue range ${range} is out of bounds. Valid range: 0-${maxQueue}`);
                }
                for (let i = start; i <= end; i++) {
                    if (queueSet.has(i)) {
                        throw new Error(`Duplicate queue value: ${i}. Valid range: 0-${maxQueue}`);
                    }
                    queueSet.add(i);
                }
            } else {
                const num = parseInt(range, 10);
                if (isNaN(num)) {
                    throw new Error(`Invalid queue number: ${range}. Valid range: 0-${maxQueue}`);
                }
                if (num < 0 || num > maxQueue) {
                    throw new Error(`Queue number ${num} is out of bounds. Valid range: 0-${maxQueue}`);
                }
                if (queueSet.has(num)) {
                    throw new Error(`Duplicate queue value: ${num}. Valid range: 0-${maxQueue}`);
                }
                queueSet.add(num);
            }
        }

        return Array.from(queueSet).sort((a, b) => a - b);
    };

    // Helper function to validate queue range format
    const validateQueueFormat = (queueString, trustMode) => {
        const maxQueue = trustMode === "dscp" ? 63 : 7;
        const validRangeText = `Valid range: 0-${maxQueue}`;

        if (!queueString || queueString.trim() === "") {
            return {isValid: false, message: `Please enter queue ranges! ${validRangeText}`};
        }

        // Check if only contains numbers, hyphens, commas and spaces
        if (!/^[\d\s,-]+$/.test(queueString)) {
            return {isValid: false, message: `Only numbers, hyphens, and commas are allowed! ${validRangeText}`};
        }

        // Check for incomplete ranges (ending with - or starting with -)
        if (/^-|,$|--|-$/.test(queueString.trim())) {
            return {isValid: false, message: `Incomplete range detected! ${validRangeText}`};
        }

        try {
            const parsedQueues = parseQueueRanges(queueString, trustMode);
            const allQueues = Array.from({length: maxQueue + 1}, (_, i) => i);
            const isAllQueues =
                parsedQueues.length === allQueues.length && parsedQueues.every(q => allQueues.includes(q));

            return {isValid: true, parsedQueues, isAllQueues};
        } catch (error) {
            return {isValid: false, message: error.message};
        }
    };

    // Helper function to validate input characters (only allow letters, numbers, underscore, hyphen)
    const validateInputCharacters = value => {
        if (!value) return true; // Allow empty values
        return /^[\w-]*$/.test(value);
    };

    // Helper function to create character validation rule
    const createCharacterValidationRule = fieldName => ({
        validator: (_, value) => {
            if (validateInputCharacters(value)) {
                return Promise.resolve();
            }
            return Promise.reject(
                new Error(`${fieldName} can only contain letters, numbers, underscore (_), and hyphen (-)`)
            );
        }
    });

    // Helper function to convert queue array back to range string
    const convertQueueArrayToRangeString = queueArray => {
        if (!queueArray || queueArray.length === 0) {
            return "";
        }

        // Convert to numbers and sort the array
        const sortedQueues = [...queueArray]
            .map(q => parseInt(q, 10))
            .filter(q => !isNaN(q))
            .sort((a, b) => a - b);

        if (sortedQueues.length === 0) {
            return "";
        }

        const ranges = [];
        let start = sortedQueues[0];
        let end = sortedQueues[0];

        for (let i = 1; i < sortedQueues.length; i++) {
            if (sortedQueues[i] === end + 1) {
                // Consecutive number, extend the range
                end = sortedQueues[i];
            } else {
                // Non-consecutive, add current range and start a new one
                if (start === end) {
                    ranges.push(start.toString());
                } else {
                    ranges.push(`${start}-${end}`);
                }
                start = sortedQueues[i];
                end = sortedQueues[i];
            }
        }

        // Add the last range
        if (start === end) {
            ranges.push(start.toString());
        } else {
            ranges.push(`${start}-${end}`);
        }

        return ranges.join(", ");
    };

    useEffect(() => {
        fetchFabricTreeData();
    }, []);

    useEffect(() => {
        console.log("schedulerOptionsMap updated:", schedulerOptionsMap);
    }, [schedulerOptionsMap]);

    // 监听表单变化，当其他行的scheduler值变化时，触发重新渲染
    const configurations = Form.useWatch("configurations", form);

    const fetchFabricTreeData = async () => {
        const tree = await fetchQosFabricSwitchList("QosConfiguration");
        if (tree) {
            setFabricTreeData(tree);
        }
    };

    // 初始化Ingress端口树数据，默认所有端口都为enabled状态
    const initIngressPortTreeData = portData => {
        console.log("Scheduling initIngressPortTreeData - portData:", portData);

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: false // 初始化时所有端口都可用
        }));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: false, // 初始化时All Ports也可用
                children: allPortsForDisplay
            }
        ];

        setIngressPortTreeData(tree);
        setIngressExpandedKeys([tree[0].value, ...tree[0].children.map(child => child.key)]);
    };

    // 初始化Egress端口树数据，默认所有端口都为enabled状态
    const initEgressPortTreeData = portData => {
        console.log("Scheduling initEgressPortTreeData - portData:", portData);

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: false // 初始化时所有端口都可用
        }));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: false, // 初始化时All Ports也可用
                children: allPortsForDisplay
            }
        ];

        setEgressPortTreeData(tree);
        setEgressExpandedKeys([tree[0].value, ...tree[0].children.map(child => child.key)]);
    };

    // 处理Ingress端口选择器获得焦点时的逻辑
    const handleIngressPortFocus = name => {
        const currentSysname = form.getFieldValue(["ingressConfigurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.switch_sn) {
                    const cacheKey = `${sysObj.switch_sn}_QosIngressConfiguration`;
                    const portData = portDataCache.get(cacheKey);
                    if (portData) {
                        // 收集当前switch_sn下非当前行占用的端口
                        const occupiedPorts = new Set();
                        const currentFormValues = form.getFieldsValue();

                        if (currentFormValues.ingressConfigurations) {
                            currentFormValues.ingressConfigurations.forEach((config, configIndex) => {
                                // 跳过当前行
                                if (configIndex === name) return;

                                if (config.sysname && config.ports) {
                                    try {
                                        const configSysObj = JSON.parse(config.sysname);
                                        // 只处理相同switch_sn的配置
                                        if (configSysObj.switch_sn === sysObj.switch_sn) {
                                            let selectedPorts = [];
                                            if (Array.isArray(config.ports) && config.ports.length > 0) {
                                                const allPortsValue = JSON.stringify(
                                                    portData.map(item => item.port_name)
                                                );
                                                if (config.ports[0] === allPortsValue) {
                                                    selectedPorts = JSON.parse(config.ports[0]);
                                                } else {
                                                    selectedPorts = config.ports;
                                                }
                                            }
                                            selectedPorts.forEach(port => occupiedPorts.add(port));
                                        }
                                    } catch (e) {
                                        console.error("Error parsing config sysname:", e);
                                    }
                                }
                            });
                        }

                        // 构建新的树形数据，将占用的端口置为disabled
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: occupiedPorts.has(item.port_name)
                        }));

                        // 如果有任意端口被占用，All Ports也置为disabled
                        const hasAnyPortOccupied = occupiedPorts.size > 0;

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: hasAnyPortOccupied,
                                children: allPortsForDisplay
                            }
                        ];

                        setIngressPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 处理Ingress端口选择器失去焦点时的逻辑
    const handleIngressPortBlur = name => {
        const currentSysname = form.getFieldValue(["ingressConfigurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.switch_sn) {
                    const cacheKey = `${sysObj.switch_sn}_QosIngressConfiguration`;
                    const portData = portDataCache.get(cacheKey);
                    if (portData) {
                        // onBlur时将所有option置为enable
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: false
                        }));

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: false,
                                children: allPortsForDisplay
                            }
                        ];

                        setIngressPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 处理Egress端口选择器获得焦点时的逻辑
    const handleEgressPortFocus = name => {
        const currentSysname = form.getFieldValue(["egressConfigurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.switch_sn) {
                    const cacheKey = `${sysObj.switch_sn}_QosEgressConfiguration`;
                    const portData = portDataCache.get(cacheKey);
                    if (portData) {
                        // 收集当前switch_sn下非当前行占用的端口
                        const occupiedPorts = new Set();
                        const currentFormValues = form.getFieldsValue();

                        if (currentFormValues.egressConfigurations) {
                            currentFormValues.egressConfigurations.forEach((config, configIndex) => {
                                // 跳过当前行
                                if (configIndex === name) return;

                                if (config.sysname && config.ports) {
                                    try {
                                        const configSysObj = JSON.parse(config.sysname);
                                        // 只处理相同switch_sn的配置
                                        if (configSysObj.switch_sn === sysObj.switch_sn) {
                                            let selectedPorts = [];
                                            if (Array.isArray(config.ports) && config.ports.length > 0) {
                                                const allPortsValue = JSON.stringify(
                                                    portData.map(item => item.port_name)
                                                );
                                                if (config.ports[0] === allPortsValue) {
                                                    selectedPorts = JSON.parse(config.ports[0]);
                                                } else {
                                                    selectedPorts = config.ports;
                                                }
                                            }
                                            selectedPorts.forEach(port => occupiedPorts.add(port));
                                        }
                                    } catch (e) {
                                        console.error("Error parsing config sysname:", e);
                                    }
                                }
                            });
                        }

                        // 构建新的树形数据，将占用的端口置为disabled
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: occupiedPorts.has(item.port_name)
                        }));

                        // 如果有任意端口被占用，All Ports也置为disabled
                        const hasAnyPortOccupied = occupiedPorts.size > 0;

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: hasAnyPortOccupied,
                                children: allPortsForDisplay
                            }
                        ];

                        setEgressPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 处理Egress端口选择器失去焦点时的逻辑
    const handleEgressPortBlur = name => {
        const currentSysname = form.getFieldValue(["egressConfigurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.switch_sn) {
                    const cacheKey = `${sysObj.switch_sn}_QosEgressConfiguration`;
                    const portData = portDataCache.get(cacheKey);
                    if (portData) {
                        // onBlur时将所有option置为enable
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: false
                        }));

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: false,
                                children: allPortsForDisplay
                            }
                        ];

                        setEgressPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 修改fetchPortsBySn函数，支持分离的端口数据初始化
    const fetchPortsBySn = async (sn, query_model, forceRefresh = false) => {
        try {
            const cacheKey = `${sn}_${query_model}`;
            const res = await getFilterSwitchPorts({
                switch_sn: sn,
                query_model
            });

            if (res && res.status === 200 && Array.isArray(res.data)) {
                const portData = res.data;
                setPortDataCache(prev => new Map(prev).set(cacheKey, portData));

                // 根据query_model类型初始化对应的端口树数据
                if (query_model === "QosIngressConfiguration") {
                    initIngressPortTreeData(portData);
                } else if (query_model === "QosEgressConfiguration") {
                    initEgressPortTreeData(portData);
                }
            } else {
                message.error(res.msg);
            }
        } catch (error) {
            console.error("Failed to fetch filtered ports:", error);
            message.error("Failed to fetch ports");

            // 根据query_model类型清空对应的端口树数据
            if (query_model === "QosIngressConfiguration") {
                setIngressPortTreeData([]);
            } else if (query_model === "QosEgressConfiguration") {
                setEgressPortTreeData([]);
            }
        }
    };

    // 修改编辑处理函数 - 根据switch_sn加载数据
    const handleEdit = async record => {
        try {
            setIsModalVisible(true);
            setEditRecord(record);

            // 预加载端口数据
            if (record.switch_sn) {
                await fetchPortsBySn(record.switch_sn, "QosIngressConfiguration", true);
                await fetchPortsBySn(record.switch_sn, "QosEgressConfiguration", true);
            }

            // 根据 switch_sn 查询该交换机下所有的 QoS 配置记录
            const detailResponse = await getQosConfigDetailBySwitch({
                switch_sn: record.switch_sn
            });

            let defaultSysname = null;
            let {sysname} = record;
            let {switch_sn} = record;

            if (detailResponse && detailResponse.status === 200) {
                const configs = detailResponse.data || [];

                if (configs && configs.length > 0) {
                    console.log("QoS configs:", configs);
                    const firstConfig = configs[0];
                    console.log("First QoS config:", firstConfig);
                    sysname = firstConfig.sysname;
                    switch_sn = firstConfig.switch_sn;
                }

                defaultSysname = JSON.stringify({switch_sn, sysname});
                console.log("QoS defaultSysname:", defaultSysname);

                setFabricTreeData([
                    {
                        title: record.fabric,
                        value: record.fabric,
                        key: record.fabric,
                        children: [
                            {
                                title: `${sysname} ${switch_sn}`,
                                value: defaultSysname,
                                key: defaultSysname
                            }
                        ]
                    }
                ]);

                // 处理主配置数据
                const configurations = configs.map(config => ({
                    sysname: defaultSysname,
                    config_id: config.id,
                    forwarding_class: config.forwarding_class,
                    local_priority: config.local_priority,
                    scheduler: config.scheduler,
                    qos_mode: config.mode,
                    weight: config.weight,
                    guaranteed_rate: config.guaranteed_rate
                }));

                // 处理入站配置数据
                const ingressConfigurations = [];
                configs.forEach(config => {
                    if (config.ingress_configurations && config.ingress_configurations.length > 0) {
                        config.ingress_configurations.forEach(ingressConfig => {
                            // 处理端口数据
                            let ports = [];
                            if (ingressConfig.is_all_ports && ingressConfig.port) {
                                ports = Array.isArray(ingressConfig.port)
                                    ? [JSON.stringify(ingressConfig.port)]
                                    : [ingressConfig.port];
                            } else {
                                ports = Array.isArray(ingressConfig.port)
                                    ? ingressConfig.port
                                    : JSON.parse(ingressConfig.port);
                            }

                            // 处理队列数据 - 转换为范围字符串格式
                            let queues = "";
                            if (ingressConfig.queue) {
                                let queueArray = [];
                                if (Array.isArray(ingressConfig.queue)) {
                                    queueArray = ingressConfig.queue;
                                } else if (typeof ingressConfig.queue === "string") {
                                    try {
                                        queueArray = JSON.parse(ingressConfig.queue);
                                    } catch {
                                        queueArray = [ingressConfig.queue];
                                    }
                                }
                                // 将队列数组转换为范围字符串
                                queues = convertQueueArrayToRangeString(queueArray);
                            }

                            ingressConfigurations.push({
                                sysname: defaultSysname,
                                config_id: config.id,
                                ingress_id: ingressConfig.id,
                                classifier: ingressConfig.classifier,
                                trust_mode: ingressConfig.trust_mode,
                                ports,
                                forwarding_class: ingressConfig.forwarding_class,
                                queues,
                                is_all_ports: ingressConfig.is_all_ports,
                                is_all_queues: ingressConfig.is_all_queues
                            });
                        });
                    }
                });

                // 处理出站配置数据
                const egressConfigurations = [];
                configs.forEach(config => {
                    if (config.egress_configurations && config.egress_configurations.length > 0) {
                        config.egress_configurations.forEach(egressConfig => {
                            // 处理端口数据
                            let ports = [];
                            if (egressConfig.is_all_ports && egressConfig.port) {
                                ports = Array.isArray(egressConfig.port)
                                    ? [JSON.stringify(egressConfig.port)]
                                    : [egressConfig.port];
                            } else {
                                ports = Array.isArray(egressConfig.port)
                                    ? egressConfig.port
                                    : JSON.parse(egressConfig.port);
                            }

                            egressConfigurations.push({
                                sysname: defaultSysname,
                                config_id: config.id,
                                egress_id: egressConfig.id,
                                scheduler_profile: egressConfig.scheduler_profile,
                                scheduler: egressConfig.scheduler,
                                ports,
                                forwarding_class: egressConfig.forwarding_class,
                                local_priority: egressConfig.local_priority,
                                is_all_ports: egressConfig.is_all_ports
                            });
                        });
                    }
                });

                // 如果没有配置数据，提供默认值
                if (configurations.length === 0) {
                    configurations.push({
                        sysname: defaultSysname,
                        config_id: null,
                        forwarding_class: "",
                        local_priority: null,
                        scheduler: "",
                        qos_mode: "SP",
                        weight: "",
                        guaranteed_rate: ""
                    });
                }

                if (ingressConfigurations.length === 0) {
                    ingressConfigurations.push({...ingressInitValue, sysname: defaultSysname});
                }

                if (egressConfigurations.length === 0) {
                    egressConfigurations.push({...egressInitValue, sysname: defaultSysname});
                }

                // 设置表单数据
                form.setFieldsValue({
                    configurations,
                    ingressConfigurations,
                    egressConfigurations
                });

                console.log("QoS Detail loaded:", {
                    configs,
                    configurations,
                    ingressConfigurations,
                    egressConfigurations
                });
            } else {
                message.warning("Failed to load QoS configurations");
                setIsModalVisible(false);
                setEditRecord(null);
            }
        } catch (error) {
            console.error("failed to load scheduling config detail:", error);
            message.error("failed to load scheduling config detail");
            setIsModalVisible(false);
            setEditRecord(null);
        }
    };

    // 参考 PFCConfiguration 的 handleCreate 实现
    const handleCreate = () => {
        setIsModalVisible(true);
        setEditRecord(null);
        form.resetFields();
        setIngressPortTreeData([]); // 清空ingress端口数据
        setEgressPortTreeData([]); // 清空egress端口数据

        // 设置默认值
        form.setFieldsValue({
            configurations: [
                {
                    sysname: null,
                    forwarding_class: "",
                    local_priority: null,
                    scheduler: null,
                    qos_mode: null,
                    weight: null,
                    guaranteed_rate: null
                }
            ],
            ingressConfigurations: [ingressInitValue],
            egressConfigurations: [egressInitValue]
        });
        fetchFabricTreeData();
    };

    // 修改 handleModalCancel 函数
    const handleModalCancel = () => {
        setIsModalVisible(false);
        setEditRecord(null);
        form.resetFields();
        setIngressPortTreeData([]); // 清空ingress端口数据
        setEgressPortTreeData([]); // 清空egress端口数据
        setPortDataCache(new Map()); // 清空端口缓存
        setEditPortData([]); // 清空编辑端口数据
        // 重置 scheduler 相关状态
        setSchedulerOptionsMap({});
        setSearchValueMap({});
        setSchedulerValuesMap({}); // 清空scheduler值记录
        setSearchValue("");
    };

    // 修改 handleSubmit 函数，参考 PFCConfiguration
    const handleSubmit = async values => {
        console.log("Form values before processing:", values); // 调试用，可以删除

        setSaveLoading(true);
        try {
            const configurationsArray = Array.isArray(values.configurations) ? values.configurations : [];

            const configs = configurationsArray
                .filter(config => {
                    // 过滤掉 sysname 为空的数据
                    return config.sysname && config.sysname.trim() !== "";
                })
                .map(config => {
                    let sysObj = {};
                    try {
                        sysObj = JSON.parse(config.sysname);
                    } catch (e) {
                        console.error("Failed to parse sysname:", e);
                        return;
                    }

                    // Configuration 部分不需要端口数据处理

                    return {
                        // 如果是编辑，带上 config_id
                        ...(config.config_id ? {config_id: config.config_id} : {}),
                        sysname: sysObj.sysname || config.sysname || "",
                        switch_sn: sysObj.switch_sn || "",
                        forwarding_class: config.forwarding_class,
                        local_priority: config.local_priority,
                        scheduler: config.scheduler,
                        mode: config.qos_mode,
                        weight: config.weight,
                        guaranteed_rate: config.guaranteed_rate
                    };
                });

            const params = {
                configurations: configs.filter(Boolean),
                ingress_configurations: (values.ingressConfigurations || [])
                    .filter(item => {
                        // 过滤掉 sysname 为空的数据
                        if (!item.sysname || item.sysname.trim() === "") {
                            return false;
                        }

                        // 编辑模式下，如果除 sysname 外的其他字段都为空，则忽略该配置（不报错）
                        if (editRecord) {
                            const hasOtherFields =
                                item.classifier ||
                                item.trust_mode ||
                                item.forwarding_class ||
                                (item.ports && item.ports.length > 0) ||
                                (item.queues && item.queues.trim() !== "");
                            return hasOtherFields;
                        }

                        return true;
                    })
                    .map(item => {
                        let sysObj = {};
                        try {
                            sysObj = JSON.parse(item.sysname);
                        } catch (e) {
                            console.log(e);
                        }

                        // 参考PFCWDConfiguration处理ingress配置
                        const portData = portDataCache.get(`${sysObj.switch_sn}_QosIngressConfiguration`);
                        const allPortsValue = portData ? JSON.stringify(portData.map(port => port.port_name)) : null;

                        // 根据实际值判断是否为全选
                        const is_all_ports = item.ports && item.ports.length === 1 && item.ports[0] === allPortsValue;

                        let ports = [];
                        let queues = [];
                        let is_all_queues = false;

                        if (is_all_ports) {
                            ports = JSON.parse(item.ports[0]);
                        } else {
                            ports = item.ports || [];
                        }

                        // 处理队列字符串，解析为数组
                        if (item.queues && typeof item.queues === "string") {
                            try {
                                const validation = validateQueueFormat(item.queues, item.trust_mode);
                                if (validation.isValid) {
                                    queues = validation.parsedQueues;
                                    is_all_queues = validation.isAllQueues;
                                }
                            } catch (error) {
                                console.error("Error parsing queue ranges:", error);
                                queues = [];
                            }
                        } else {
                            queues = item.queues || [];
                            is_all_queues = item.is_all_queues || false;
                        }

                        return {
                            ...(item.config_id ? {config_id: item.config_id} : {}),
                            ...(item.ingress_id ? {ingress_id: item.ingress_id} : {}),
                            sysname: sysObj.sysname || item.sysname || "",
                            switch_sn: sysObj.switch_sn || "",
                            classifier: item.classifier,
                            trust_mode: item.trust_mode,
                            port: ports,
                            forwarding_class: item.forwarding_class,
                            queue: queues,
                            is_all_ports,
                            is_all_queues
                        };
                    }),
                egress_configurations: (values.egressConfigurations || [])
                    .filter(item => {
                        // 过滤掉 sysname 为空的数据
                        if (!item.sysname || item.sysname.trim() === "") {
                            return false;
                        }

                        // 编辑模式下，如果除 sysname 外的其他字段都为空，则忽略该配置（不报错）
                        if (editRecord) {
                            const hasOtherFields =
                                item.scheduler_profile ||
                                item.scheduler ||
                                item.forwarding_class ||
                                (item.local_priority !== null &&
                                    item.local_priority !== undefined &&
                                    item.local_priority !== "") ||
                                (item.ports && item.ports.length > 0);
                            return hasOtherFields;
                        }

                        return true;
                    })
                    .map(item => {
                        let sysObj = {};
                        try {
                            sysObj = JSON.parse(item.sysname);
                        } catch (e) {
                            console.log(e);
                        }

                        // 参考PFCWDConfiguration处理egress配置
                        const portData = portDataCache.get(`${sysObj.switch_sn}_QosEgressConfiguration`);
                        const allPortsValue = portData ? JSON.stringify(portData.map(port => port.port_name)) : null;

                        // 根据实际值判断是否为全选
                        const is_all_ports = item.ports && item.ports.length === 1 && item.ports[0] === allPortsValue;

                        let ports = [];

                        if (is_all_ports) {
                            ports = JSON.parse(item.ports[0]);
                        } else {
                            ports = item.ports || [];
                        }

                        return {
                            ...(item.config_id ? {config_id: item.config_id} : {}),
                            ...(item.egress_id ? {egress_id: item.egress_id} : {}),
                            sysname: sysObj.sysname || item.sysname || "",
                            switch_sn: sysObj.switch_sn || "",
                            scheduler_profile: item.scheduler_profile,
                            scheduler: item.scheduler,
                            port: ports,
                            forwarding_class: item.forwarding_class,
                            local_priority: item.local_priority,
                            is_all_ports,
                            is_all_queues: false // egress配置没有queue字段
                        };
                    })
            };
            console.log("Submitting params:", params); // 调试用，可以删除
            setIsShowSpin(true);
            let ret;
            if (editRecord) {
                // 编辑模式：调用 update API，使用扁平结构
                const updateParams = {
                    switch_sn: editRecord.switch_sn,
                    configurations: params.configurations,
                    ingress_configurations: params.ingress_configurations,
                    egress_configurations: params.egress_configurations
                };
                console.log("Update params:", updateParams); // 调试用，可以删除
                ret = await updateSchedulingConfig(updateParams);
            } else {
                // 创建模式：调用 save API
                ret = await saveSchedulingConfig(params);
            }
            setIsShowSpin(false);
            if (ret.status === 200) {
                form.resetFields();
                setIsModalVisible(false);
                setEditRecord(null);
                message.success(ret.msg);
                tableRef.current?.refreshTable();
            } else {
                // 处理多分段错误 (Handle multi-segment errors)
                if (ret.data && Array.isArray(ret.data) && ret.data.length > 0) {
                    showCustomErrorMessage(ret.msg, ret.data, 6);
                } else {
                    message.error(ret.msg);
                }
                form.resetFields();
                setIsModalVisible(false);
                setEditRecord(null);
                tableRef.current?.refreshTable();
            }
        } catch (e) {
            console.error("Save failed:", e);
            message.error("Save failed");
        } finally {
            setSaveLoading(false);
        }
    };

    // 删除处理函数
    const handleDeleteConfirm = async record => {
        try {
            setIsShowSpin(true);
            const ret = await deleteSchedulingConfig({config_id: record.id});
            setIsShowSpin(false);
            if (ret.status === 200) {
                message.success(ret.msg);
                tableRef.current.refreshTable();
            } else {
                // 处理多分段错误 (Handle multi-segment errors)
                if (ret.data && Array.isArray(ret.data) && ret.data.length > 0) {
                    showCustomErrorMessage(ret.msg, ret.data, 6);
                } else {
                    message.error(ret.msg);
                }
                tableRef.current.refreshTable();
            }
        } catch (e) {
            console.error(e);
            message.error("Deleted failed");
        }
    };

    // 添加获取scheduler选项的辅助函数
    const getSchedulerOptions = (sysname, rowIndex) => {
        let switch_sn = "";

        if (sysname) {
            try {
                const obj = JSON.parse(sysname);
                switch_sn = obj.switch_sn;
            } catch (e) {
                console.error("Failed to parse sysname for scheduler options:", e);
            }
        }
        // 如果有switch_sn，使用switch_sn作为key；否则使用临时行key
        const key = switch_sn || `temp_row_${rowIndex}`;
        const baseOptions = schedulerOptionsMap[key] || [];

        // 如果有switch_sn，还需要收集同一交换机下其他行的scheduler值
        if (switch_sn) {
            const formData = form.getFieldsValue();
            const configurations = formData.configurations || [];
            const otherRowSchedulers = [];

            configurations.forEach((config, idx) => {
                // 跳过当前行
                if (idx === rowIndex) return;

                // 检查是否是同一交换机
                if (config && config.sysname) {
                    try {
                        const configObj = JSON.parse(config.sysname);
                        if (configObj.switch_sn === switch_sn && config.scheduler) {
                            otherRowSchedulers.push(config.scheduler);
                        }
                    } catch (e) {
                        console.error("Failed to parse config sysname:", e);
                    }
                }
            });

            // 合并基础选项和其他行的scheduler值
            const allOptions = [...new Set([...baseOptions, ...otherRowSchedulers])];
            return allOptions;
        }

        return baseOptions;
    };

    // 修改setSchedulerOptions函数，同时记录值
    const setSchedulerOptions = (sysname, rowIndex, newScheduler) => {
        let switch_sn = "";

        if (sysname) {
            try {
                const obj = JSON.parse(sysname);
                switch_sn = obj.switch_sn;
            } catch (e) {
                console.error("Failed to parse sysname for setting scheduler options:", e);
            }
        }

        // 如果有switch_sn，使用switch_sn作为key；否则使用临时行key
        const key = switch_sn || `temp_row_${rowIndex}`;

        setSchedulerOptionsMap(prev => {
            const currentOptions = prev[key] || [];
            if (!currentOptions.includes(newScheduler)) {
                return {
                    ...prev,
                    [key]: [newScheduler]
                };
            }
            return prev;
        });

        // 同时记录这个值到schedulerValuesMap
        setSchedulerValuesMap(prev => ({
            ...prev,
            [`row_${rowIndex}`]: newScheduler
        }));
    };

    // 清理非法的 forwarding_class 和 scheduler 值
    const cleanupInvalidValues = () => {
        const formData = form.getFieldsValue();

        // 按交换机分组收集合法的 forwarding_class 和 scheduler
        const validValuesBySwitch = {};

        // 遍历 Configuration 部分，收集合法的值
        const configurations = formData.configurations || [];
        configurations.forEach(config => {
            if (config.sysname) {
                try {
                    const sysObj = JSON.parse(config.sysname);
                    const switchSn = sysObj.switch_sn;

                    if (!validValuesBySwitch[switchSn]) {
                        validValuesBySwitch[switchSn] = {
                            forwardingClasses: new Set(),
                            schedulers: new Set()
                        };
                    }

                    if (config.forwarding_class) {
                        validValuesBySwitch[switchSn].forwardingClasses.add(config.forwarding_class);
                    }

                    if (config.scheduler) {
                        validValuesBySwitch[switchSn].schedulers.add(config.scheduler);
                    }
                } catch (e) {
                    console.error("Error parsing configuration sysname:", e);
                }
            }
        });

        console.log("Valid values by switch:", validValuesBySwitch);

        // 清理 Ingress Configurations 中的非法值
        const ingressConfigurations = formData.ingressConfigurations || [];
        ingressConfigurations.forEach((ingressConfig, index) => {
            if (ingressConfig.sysname) {
                try {
                    const sysObj = JSON.parse(ingressConfig.sysname);
                    const switchSn = sysObj.switch_sn;
                    const validValues = validValuesBySwitch[switchSn];

                    if (validValues) {
                        // 检查 forwarding_class 是否合法
                        if (
                            ingressConfig.forwarding_class &&
                            !validValues.forwardingClasses.has(ingressConfig.forwarding_class)
                        ) {
                            console.log(
                                `Clearing invalid forwarding_class in ingress ${index}: ${ingressConfig.forwarding_class}`
                            );
                            form.setFieldValue(["ingressConfigurations", index, "forwarding_class"], null);
                        }
                    }
                } catch (e) {
                    console.error("Error parsing ingress sysname:", e);
                }
            }
        });

        // 清理 Egress Configurations 中的非法值
        const egressConfigurations = formData.egressConfigurations || [];
        egressConfigurations.forEach((egressConfig, index) => {
            if (egressConfig.sysname) {
                try {
                    const sysObj = JSON.parse(egressConfig.sysname);
                    const switchSn = sysObj.switch_sn;
                    const validValues = validValuesBySwitch[switchSn];

                    if (validValues) {
                        // 检查 forwarding_class 是否合法
                        if (
                            egressConfig.forwarding_class &&
                            !validValues.forwardingClasses.has(egressConfig.forwarding_class)
                        ) {
                            console.log(
                                `Clearing invalid forwarding_class in egress ${index}: ${egressConfig.forwarding_class}`
                            );
                            form.setFieldValue(["egressConfigurations", index, "forwarding_class"], null);
                            // 同时清空 local_priority
                            form.setFieldValue(["egressConfigurations", index, "local_priority"], null);
                        }

                        // 检查 scheduler 是否合法
                        if (egressConfig.scheduler && !validValues.schedulers.has(egressConfig.scheduler)) {
                            console.log(`Clearing invalid scheduler in egress ${index}: ${egressConfig.scheduler}`);
                            form.setFieldValue(["egressConfigurations", index, "scheduler"], null);
                        }
                    }
                } catch (e) {
                    console.error("Error parsing egress sysname:", e);
                }
            }
        });
    };

    // ========== 前端全局校验函数 (React + Ant Design 规范) ==========

    /**
     * 解析 sysname 获取 switch_sn
     */
    const parseSwitchSn = sysname => {
        if (!sysname) return null;
        try {
            const obj = JSON.parse(sysname);
            return obj.switch_sn;
        } catch (e) {
            return null;
        }
    };

    /**
     * Configuration 部分校验器 - 校验 forwarding_class 唯一性
     * 规则：同一交换机下的 forwarding_class 不能重复
     */
    const validateConfigurationForwardingClass = (rule, value, index) => {
        if (!value) {
            return Promise.resolve();
        }

        const formData = form.getFieldsValue();
        const configurations = formData.configurations || [];

        // 获取当前行的 switch_sn
        const currentConfig = configurations[index];
        if (!currentConfig || !currentConfig.sysname) {
            return Promise.resolve();
        }

        const switchSn = parseSwitchSn(currentConfig.sysname);
        if (!switchSn) {
            return Promise.resolve();
        }

        // 检查同一交换机下是否有重复的 forwarding_class
        const duplicateIndex = configurations.findIndex((config, idx) => {
            if (idx === index) return false; // 跳过当前行
            if (!config || !config.sysname || !config.forwarding_class) return false;

            const configSwitchSn = parseSwitchSn(config.sysname);
            return configSwitchSn === switchSn && config.forwarding_class === value;
        });

        if (duplicateIndex !== -1) {
            return Promise.reject(new Error(`Forwarding class '${value}' already exists on switch ${switchSn}`));
        }
        return Promise.resolve();
    };

    /**
     * EgressConfigurations 部分校验器 - 校验 scheduler_profile 中的 forwarding_class + scheduler 唯一性
     * 规则：同一 scheduler_profile 中 forwarding_class 只能对应一个 scheduler，且 scheduler 必须与 configuration 中定义的一致
     */
    const validateEgressConfigurations = (rule, value, index) => {
        if (!value) {
            return Promise.resolve();
        }

        const formData = form.getFieldsValue();
        const egressConfigurations = formData.egressConfigurations || [];
        // const configurations = formData.configurations || [];

        // 获取当前行的数据
        const currentEgress = egressConfigurations[index];
        if (
            !currentEgress ||
            !currentEgress.sysname ||
            !currentEgress.scheduler_profile ||
            !currentEgress.forwarding_class ||
            !currentEgress.scheduler
        ) {
            return Promise.resolve();
        }

        const switchSn = parseSwitchSn(currentEgress.sysname);
        if (!switchSn) {
            return Promise.resolve();
        }

        // 校验同一 scheduler_profile 中 forwarding_class 是否已经被其他 scheduler 使用
        const currentSchedulerProfile = currentEgress.scheduler_profile;
        const currentForwardingClass = currentEgress.forwarding_class;
        if (!currentSchedulerProfile) {
            return Promise.resolve();
        }

        const duplicateIndex = egressConfigurations.findIndex((egress, idx) => {
            if (idx === index) return false; // 跳过当前行
            if (
                !egress ||
                !egress.sysname ||
                !egress.scheduler_profile ||
                !egress.forwarding_class ||
                !egress.scheduler
            )
                return false;

            const egressSwitchSn = parseSwitchSn(egress.sysname);
            console.log("rule.field", rule.field);
            if (rule.field === `egressConfigurations.${index}.scheduler`) {
                return (
                    egressSwitchSn === switchSn &&
                    egress.scheduler_profile === currentSchedulerProfile &&
                    egress.scheduler === currentEgress.scheduler
                );
            }
            if (rule.field === `egressConfigurations.${index}.forwarding_class`) {
                return (
                    egressSwitchSn === switchSn &&
                    egress.scheduler_profile === currentSchedulerProfile &&
                    egress.forwarding_class === currentForwardingClass
                );
            }
        });
        console.log("duplicateIndex", duplicateIndex);

        if (duplicateIndex !== -1) {
            const existingScheduler = egressConfigurations[duplicateIndex].scheduler;
            return Promise.reject(
                new Error(
                    `Forwarding class '${value}' is already used by scheduler '${existingScheduler}' in profile '${currentSchedulerProfile}' on switch ${switchSn}`
                )
            );
        }

        return Promise.resolve();
    };

    // 监听 configuration 区块
    const formConfigurations = Form.useWatch(["configurations"], form) || [];
    const snFcPriorityMap = useMemo(() => {
        const map = {};

        formConfigurations.forEach(cfg => {
            try {
                const sys = JSON.parse(cfg.sysname || "{}");
                const sn = sys.switch_sn;
                const fc = cfg.forwarding_class;
                const lp = cfg.local_priority;

                if (sn && fc !== undefined && lp !== undefined && lp !== null) {
                    map[`${sn}-${fc}`] = lp;
                }
            } catch (e) {
                // 忽略非法 sysname
            }
        });

        return map;
    }, [JSON.stringify(formConfigurations)]);

    // 按交换机序列号分组 forwarding class 选项
    const forwardingClassOptionsBySn = useMemo(() => {
        const optionsMap = {};

        formConfigurations.forEach(cfg => {
            if (!cfg?.sysname) {
                return;
            }
            const sys = JSON.parse(cfg.sysname || "{}");
            const sn = sys.switch_sn || ""; // 如果 sn 为空，使用空字符串作为 key
            const fc = cfg.forwarding_class;

            if (fc && fc.trim() !== "") {
                if (!optionsMap[sn]) {
                    optionsMap[sn] = [];
                }
                if (!optionsMap[sn].includes(fc)) {
                    optionsMap[sn].push(fc);
                }
            }
        });

        return optionsMap;
    }, [JSON.stringify(formConfigurations)]);
    // 优化表单项函数中的端口配置
    const formItems = () => {
        // 获取指定 sn 的 forwarding class 选项
        const getForwardingClassOptionsBySn = sn => {
            const options = forwardingClassOptionsBySn[sn || ""] || [];
            return options;
        };
        const queueOptions = Array.from({length: 8}, (_, i) => i);

        return (
            <>
                {/* Configuration Section */}
                <div
                    style={{
                        fontSize: "18px",
                        fontWeight: "bold",
                        borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                        paddingBottom: "10px",
                        marginBottom: "20px"
                    }}
                >
                    <span>Configuration </span>
                    <span className={style.requiredIcon1} style={{display: "inline-block", height: 27}}>
                        *
                    </span>
                </div>
                <Form.List name="configurations" initialValue={[{}]}>
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name}, index) => (
                                <Row key={key} gutter={[8, 16]}>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "sysname"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Sysname <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select sysname!"}]}
                                            validateTrigger={["onChange", "onBlur"]}
                                        >
                                            <AmpConTreeSelect
                                                onChange={async value => {
                                                    if (value !== null) {
                                                        try {
                                                            const obj = JSON.parse(value);
                                                            if (obj.switch_sn) {
                                                                // 获取当前行的临时scheduler选项和记录的scheduler值
                                                                const tempKey = `temp_row_${name}`;
                                                                const tempSchedulers =
                                                                    schedulerOptionsMap[tempKey] || [];
                                                                const savedSchedulerValue =
                                                                    schedulerValuesMap[`row_${name}`];

                                                                // 收集同一交换机下其他行的scheduler值
                                                                const formData = form.getFieldsValue();
                                                                const configurations = formData.configurations || [];
                                                                const otherRowSchedulers = [];

                                                                configurations.forEach((config, idx) => {
                                                                    // 跳过当前行
                                                                    if (idx === name) return;

                                                                    // 检查是否是同一交换机
                                                                    if (config && config.sysname) {
                                                                        try {
                                                                            const configObj = JSON.parse(
                                                                                config.sysname
                                                                            );
                                                                            if (
                                                                                configObj.switch_sn === obj.switch_sn &&
                                                                                config.scheduler
                                                                            ) {
                                                                                otherRowSchedulers.push(
                                                                                    config.scheduler
                                                                                );
                                                                            }
                                                                        } catch (e) {
                                                                            console.error(
                                                                                "Failed to parse config sysname:",
                                                                                e
                                                                            );
                                                                        }
                                                                    }
                                                                });

                                                                setSchedulerOptionsMap(prev => {
                                                                    const existingOptions = prev[obj.switch_sn] || [];
                                                                    console.log("existingOptions", existingOptions);
                                                                    console.log("tempSchedulers", tempSchedulers);
                                                                    console.log(
                                                                        "otherRowSchedulers",
                                                                        otherRowSchedulers
                                                                    );
                                                                    console.log(
                                                                        "savedSchedulerValue",
                                                                        savedSchedulerValue
                                                                    );

                                                                    // 合并所有选项：现有选项 + 临时选项 + 保存的值 + 其他行的scheduler值
                                                                    const allOptions = [
                                                                        ...existingOptions,
                                                                        ...tempSchedulers,
                                                                        ...otherRowSchedulers,
                                                                        ...(savedSchedulerValue
                                                                            ? [savedSchedulerValue]
                                                                            : [])
                                                                    ];
                                                                    const mergedOptions = [...new Set(allOptions)];

                                                                    // 移除临时key，添加到正式key
                                                                    const newMap = {...prev};
                                                                    delete newMap[tempKey];
                                                                    newMap[obj.switch_sn] = mergedOptions;

                                                                    return newMap;
                                                                });

                                                                // 恢复保存的scheduler值
                                                                if (savedSchedulerValue) {
                                                                    form.setFieldValue(
                                                                        ["configurations", name, "scheduler"],
                                                                        savedSchedulerValue
                                                                    );
                                                                }
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    }

                                                    setSelectedSysnames(prev => {
                                                        const newSysnames = [...prev];
                                                        newSysnames[name] = value;
                                                        return newSysnames;
                                                    });
                                                }}
                                                treeData={fabricTreeData}
                                                placeholder="Sysname"
                                                disabled={editRecord !== null}
                                                treeDefaultExpandAll
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "forwarding_class"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Forwarding Class <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{
                                                span: 24,
                                                style: {
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis"
                                                }
                                            }}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (_, value) => {
                                                        const currentConfig = form.getFieldValue([
                                                            "ingressConfigurations",
                                                            name
                                                        ]);
                                                        // 如果 sysname 为空，则忽略其他字段校验
                                                        if (!currentConfig?.sysname) {
                                                            return Promise.resolve();
                                                        }

                                                        // 编辑模式下，检查当前配置的其他字段是否都为空
                                                        if (editRecord) {
                                                            const hasOtherFields =
                                                                currentConfig.classifier ||
                                                                currentConfig.trust_mode ||
                                                                (currentConfig.ports && currentConfig.ports.length > 0);
                                                            // 如果其他字段都为空，则不进行必填验证
                                                            if (!hasOtherFields) {
                                                                return Promise.resolve();
                                                            }
                                                        }

                                                        // 如果 sysname 不为空，则 forwarding_class 必填
                                                        if (!value || value.trim() === "") {
                                                            return Promise.reject(
                                                                new Error("Please input forwarding class!")
                                                            );
                                                        }
                                                        if (value.length > 30) {
                                                            return Promise.reject(
                                                                new Error(
                                                                    "Forwarding class name cannot exceed 30 characters!"
                                                                )
                                                            );
                                                        }
                                                        // 验证字符规则
                                                        const charValidation =
                                                            createCharacterValidationRule("Forwarding class");
                                                        if (charValidation) {
                                                            const result = await charValidation.validator(_, value);
                                                            if (result) return result;
                                                        }
                                                        // 验证唯一性
                                                        return validateConfigurationForwardingClass(_, value, name);
                                                    }
                                                }
                                            ]}
                                        >
                                            <Input
                                                placeholder="Forwarding Class"
                                                onBlur={() => {
                                                    cleanupInvalidValues();
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "local_priority"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Local Priority <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select local priority!"}]}
                                        >
                                            <Select
                                                placeholder="Select local priority"
                                                onChange={value => {
                                                    const sysname = form.getFieldValue([
                                                        "configurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    const forwardclass = form.getFieldValue([
                                                        "configurations",
                                                        name,
                                                        "forwarding_class"
                                                    ]);
                                                    const egressConfigurations =
                                                        form.getFieldValue("egressConfigurations") || [];
                                                    egressConfigurations.forEach((config, index) => {
                                                        if (
                                                            config.sysname === sysname &&
                                                            config.forwarding_class === forwardclass
                                                        ) {
                                                            form.setFieldValue(
                                                                ["egressConfigurations", index, "local_priority"],
                                                                value !== undefined && value !== null ? value : ""
                                                            );
                                                        }
                                                    });
                                                }}
                                            >
                                                {Array.from({length: 8}, (_, i) => (
                                                    <Option key={i} value={i}>
                                                        {i}
                                                    </Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "scheduler"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Scheduler <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {required: true, message: "Please select or add a scheduler!"},
                                                {max: 30, message: "Scheduler name cannot exceed 30 characters!"},
                                                createCharacterValidationRule("Scheduler")
                                            ]}
                                        >
                                            <Select
                                                showSearch
                                                suffixIcon={<DownOutlined />}
                                                placeholder="Select or input a scheduler"
                                                style={{width: "100%"}}
                                                options={(() => {
                                                    const sysname = form.getFieldValue([
                                                        "configurations",
                                                        name,
                                                        "sysname"
                                                    ]);

                                                    // 使用辅助函数获取选项
                                                    const options = getSchedulerOptions(sysname, name);
                                                    return options.map(item => ({
                                                        label: item,
                                                        value: item
                                                    }));
                                                })()}
                                                filterOption={(input, option) =>
                                                    option?.label.toLowerCase().includes(input.toLowerCase())
                                                }
                                                onChange={value => {
                                                    // 同步更新schedulerValuesMap
                                                    setSchedulerValuesMap(prev => ({
                                                        ...prev,
                                                        [`row_${name}`]: value
                                                    }));
                                                }}
                                                onSearch={value => {
                                                    setSearchValueMap(prev => ({
                                                        ...prev,
                                                        [name]: value
                                                    }));
                                                }}
                                                onBlur={() => {
                                                    const sysname = form.getFieldValue([
                                                        "configurations",
                                                        name,
                                                        "sysname"
                                                    ]);

                                                    const currentSearchValue = searchValueMap[name];
                                                    if (currentSearchValue?.trim()) {
                                                        // 使用辅助函数设置选项
                                                        setSchedulerOptions(sysname, name, currentSearchValue);

                                                        // 设置字段值并清除错误
                                                        form.setFields([
                                                            {
                                                                name: ["configurations", name, "scheduler"],
                                                                value: currentSearchValue,
                                                                errors: []
                                                            }
                                                        ]);

                                                        // 手动触发该字段验证以确保错误状态被清除
                                                        setTimeout(() => {
                                                            form.validateFields([
                                                                ["configurations", name, "scheduler"]
                                                            ]).catch(() => {
                                                                // 忽略验证错误，因为我们已经设置了值
                                                            });
                                                        }, 0);
                                                    }

                                                    setSearchValueMap(prev => ({
                                                        ...prev,
                                                        [name]: ""
                                                    }));

                                                    // 清理非法的 forwarding_class 和 scheduler 值
                                                    cleanupInvalidValues();
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "qos_mode"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        QoS Mode <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select QoS mode!"}]}
                                        >
                                            <Select
                                                placeholder="QoS Mode"
                                                onChange={() => {
                                                    // 当QoS Mode失去焦点时，触发weight和guaranteed_rate字段的校验
                                                    setTimeout(() => {
                                                        form.validateFields([
                                                            ["configurations", name, "weight"],
                                                            ["configurations", name, "guaranteed_rate"]
                                                        ]).catch(() => {
                                                            // 忽略验证错误，因为我们只是想触发校验显示错误提示
                                                        });
                                                    }, 0);
                                                }}
                                            >
                                                <Option value="SP">SP</Option>
                                                <Option value="WFQ">WFQ</Option>
                                                <Option value="WRR">WRR</Option>
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "weight"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Weight <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                ({getFieldValue}) => ({
                                                    required:
                                                        getFieldValue(["configurations", name, "qos_mode"]) !== "SP",
                                                    message: "Please input weight!"
                                                }),
                                                {
                                                    type: "number",
                                                    min: 1,
                                                    max: 15,
                                                    message: "Weight must be a number between 1-15!"
                                                },
                                                {
                                                    validator: async (_, value) => {
                                                        if (value && value % 1 !== 0) {
                                                            return Promise.reject(
                                                                new Error("Decimal numbers are not allowed")
                                                            );
                                                        }
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                        >
                                            <InputNumber
                                                placeholder="1-15"
                                                controls={false}
                                                disabled={
                                                    form.getFieldValue(["configurations", name, "qos_mode"]) === "SP"
                                                }
                                                style={{width: "100%"}}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "guaranteed_rate"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Guaranteed Rate <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{
                                                span: 24,
                                                style: {
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis"
                                                }
                                            }}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                ({getFieldValue}) => ({
                                                    required:
                                                        getFieldValue(["configurations", name, "qos_mode"]) === "WFQ" ||
                                                        getFieldValue(["configurations", name, "qos_mode"]) === null,
                                                    message: "Please input guaranteed rate!"
                                                }),
                                                {
                                                    type: "number",
                                                    min: 8,
                                                    max: 40000000,
                                                    message: "Guaranteed rate must be a number between 8-40000000!"
                                                },
                                                {
                                                    validator: async (_, value) => {
                                                        if (value && value % 1 !== 0) {
                                                            return Promise.reject(
                                                                new Error("Decimal numbers are not allowed")
                                                            );
                                                        }
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                        >
                                            <InputNumber
                                                placeholder="8-40000000"
                                                controls={false}
                                                disabled={
                                                    form.getFieldValue(["configurations", name, "qos_mode"]) &&
                                                    form.getFieldValue(["configurations", name, "qos_mode"]) !== "WFQ"
                                                }
                                                style={{width: "100%"}}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col>
                                        {index === 0 ? (
                                            <Button
                                                onClick={() => {
                                                    if (editRecord === null) {
                                                        add();
                                                    } else {
                                                        const currentSysname = form.getFieldValue([
                                                            "configurations",
                                                            name,
                                                            "sysname"
                                                        ]);
                                                        add({
                                                            sysname: currentSysname
                                                        });
                                                    }

                                                    // 获取当前有端口数据的交换机，重新计算端口状态
                                                    const currentValues = form.getFieldsValue();

                                                    // 找到有ingress端口数据的交换机并重新计算
                                                    const switchesWithIngressPorts = new Set();
                                                    const switchesWithEgressPorts = new Set();

                                                    const allConfigs = [
                                                        ...(currentValues.ingressConfigurations || []),
                                                        ...(currentValues.egressConfigurations || [])
                                                    ];

                                                    allConfigs.forEach(config => {
                                                        if (config.sysname) {
                                                            try {
                                                                const sysObj = JSON.parse(config.sysname);
                                                                if (sysObj.switch_sn) {
                                                                    const ingressCacheKey = `${sysObj.switch_sn}_QosIngressConfiguration`;
                                                                    const egressCacheKey = `${sysObj.switch_sn}_QosEgressConfiguration`;

                                                                    if (portDataCache.has(ingressCacheKey)) {
                                                                        switchesWithIngressPorts.add(sysObj.switch_sn);
                                                                    }
                                                                    if (portDataCache.has(egressCacheKey)) {
                                                                        switchesWithEgressPorts.add(sysObj.switch_sn);
                                                                    }
                                                                }
                                                            } catch (e) {
                                                                console.log(e);
                                                            }
                                                        }
                                                    });
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "12px",
                                                    marginTop: "29px",
                                                    width: "auto"
                                                }}
                                                type="link"
                                                icon={<PlusOutlined />}
                                            />
                                        ) : (
                                            <Button
                                                onClick={() => {
                                                    const currentValues = form.getFieldsValue();
                                                    const configToRemove = currentValues.configurations[name];

                                                    remove(name);

                                                    // 清理非法的 forwarding_class 和 scheduler 值
                                                    setTimeout(() => {
                                                        cleanupInvalidValues();
                                                    }, 100);

                                                    if (configToRemove && configToRemove.sysname) {
                                                        try {
                                                            const sysObj = JSON.parse(configToRemove.sysname);
                                                            if (sysObj.switch_sn) {
                                                                setTimeout(() => {
                                                                    // Configuration 部分不需要端口数据重新计算
                                                                }, 100);
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    }
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "12px",
                                                    width: "auto"
                                                }}
                                                type="link"
                                                icon={<MinusOutlined />}
                                            />
                                        )}
                                    </Col>
                                </Row>
                            ))}
                        </>
                    )}
                </Form.List>
                {/* Ingress Port Configuration Section */}
                <div
                    style={{
                        fontSize: "18px",
                        fontWeight: "bold",
                        borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                        paddingBottom: "10px",
                        marginBottom: "20px",
                        marginTop: "40px"
                    }}
                >
                    Ingress Port Configuration
                </div>
                <Form.List name="ingressConfigurations" initialValue={[{}]}>
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name}, index) => (
                                <Row key={key} gutter={[8, 16]}>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "sysname"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Sysname <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (_, value) => {
                                                        // Ingress sysname 可以为空，如果为空则忽略其他字段校验
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                            validateTrigger={["onChange", "onBlur"]}
                                        >
                                            <AmpConTreeSelect
                                                onChange={value => {
                                                    if (value) {
                                                        try {
                                                            const obj = JSON.parse(value);
                                                            if (obj.switch_sn) {
                                                                fetchPortsBySn(
                                                                    obj.switch_sn,
                                                                    "QosIngressConfiguration",
                                                                    true
                                                                );
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    } else {
                                                        setIngressPortTreeData([]);
                                                    }

                                                    form.setFields([
                                                        {
                                                            name: ["ingressConfigurations", name, "ports"],
                                                            value: []
                                                        },
                                                        {
                                                            name: ["ingressConfigurations", name, "forwarding_class"],
                                                            value: undefined
                                                        }
                                                    ]);
                                                }}
                                                treeData={fabricTreeData}
                                                placeholder="Sysname"
                                                disabled={editRecord !== null}
                                                treeDefaultExpandAll
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "classifier"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Classifier <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (_, value) => {
                                                        const currentConfig = form.getFieldValue([
                                                            "ingressConfigurations",
                                                            name
                                                        ]);
                                                        // 如果 sysname 为空，则忽略其他字段校验
                                                        if (!currentConfig?.sysname) {
                                                            return Promise.resolve();
                                                        }

                                                        // 编辑模式下，检查当前配置的其他字段是否都为空
                                                        if (editRecord) {
                                                            const hasOtherFields =
                                                                currentConfig.classifier ||
                                                                currentConfig.trust_mode ||
                                                                currentConfig.forwarding_class ||
                                                                (currentConfig.ports &&
                                                                    currentConfig.ports.length > 0) ||
                                                                (currentConfig.queues &&
                                                                    currentConfig.queues.trim() !== "");
                                                            // 如果其他字段都为空，则不进行必填验证
                                                            if (!hasOtherFields) {
                                                                return Promise.resolve();
                                                            }
                                                        }

                                                        // 如果 sysname 不为空，则 classifier 必填
                                                        if (!value || value.trim() === "") {
                                                            return Promise.reject(
                                                                new Error("Please input classifier!")
                                                            );
                                                        }
                                                        if (value.length > 30) {
                                                            return Promise.reject(
                                                                new Error(
                                                                    "Classifier name cannot exceed 30 characters!"
                                                                )
                                                            );
                                                        }
                                                        // 唯一性校验：同一 switch_sn 下 classifier 唯一
                                                        try {
                                                            let currentSn = "";
                                                            try {
                                                                const sysObj = JSON.parse(
                                                                    currentConfig.sysname || "{}"
                                                                );
                                                                currentSn = sysObj.switch_sn || "";
                                                            } catch (e) {
                                                                currentSn = "";
                                                            }
                                                            const allConfigs =
                                                                form.getFieldValue(["ingressConfigurations"]) || [];
                                                            const target = String(value).trim();
                                                            const hasDuplicate = allConfigs.some((cfg, idx) => {
                                                                if (idx === name) return false;
                                                                if (!cfg) return false;
                                                                let sn = "";
                                                                try {
                                                                    const obj = cfg.sysname
                                                                        ? JSON.parse(cfg.sysname)
                                                                        : {};
                                                                    sn = obj.switch_sn || "";
                                                                } catch (e) {
                                                                    sn = "";
                                                                }
                                                                const cls = String(cfg.classifier || "").trim();
                                                                return sn === currentSn && cls !== "" && cls === target;
                                                            });
                                                            if (hasDuplicate) {
                                                                return Promise.reject(
                                                                    new Error(
                                                                        "Ingress classifier must be unique within the same switch!"
                                                                    )
                                                                );
                                                            }
                                                        } catch (e) {
                                                            // 忽略唯一性检查中的解析错误
                                                        }
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                        >
                                            <Input placeholder="Classifier" />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "trust_mode"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Trust Mode <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (_, value) => {
                                                        const currentConfig = form.getFieldValue([
                                                            "ingressConfigurations",
                                                            name
                                                        ]);
                                                        // 如果 sysname 为空，则忽略其他字段校验
                                                        if (!currentConfig?.sysname) {
                                                            return Promise.resolve();
                                                        }

                                                        // 编辑模式下，检查当前配置的其他字段是否都为空
                                                        if (editRecord) {
                                                            const hasOtherFields =
                                                                currentConfig.classifier ||
                                                                currentConfig.trust_mode ||
                                                                currentConfig.forwarding_class ||
                                                                (currentConfig.ports &&
                                                                    currentConfig.ports.length > 0) ||
                                                                (currentConfig.queues &&
                                                                    currentConfig.queues.trim() !== "");
                                                            // 如果其他字段都为空，则不进行必填验证
                                                            if (!hasOtherFields) {
                                                                return Promise.resolve();
                                                            }
                                                        }

                                                        // 如果 sysname 不为空，则 trust_mode 必填
                                                        if (!value) {
                                                            return Promise.reject(
                                                                new Error("Please select trust mode!")
                                                            );
                                                        }
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                        >
                                            <Select
                                                allowClear
                                                placeholder="Trust Mode"
                                                onChange={value => {
                                                    // Update trust mode state to trigger re-render
                                                    setTrustModeMap(prev => ({
                                                        ...prev,
                                                        [`ingress_${name}`]: value
                                                    }));

                                                    // Clear the queues field when trust mode changes
                                                    form.setFieldValue(["ingressConfigurations", name, "queues"], "");
                                                    form.setFieldValue(
                                                        ["ingressConfigurations", name, "is_all_queues"],
                                                        false
                                                    );
                                                }}
                                            >
                                                <Option value="dscp">DSCP</Option>
                                                <Option value="inet-precedence">Inet Precedence</Option>
                                                <Option value="ieee-802.1">IEEE 802.1</Option>
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "ports"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Ports <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (_, value) => {
                                                        const currentConfig = form.getFieldValue([
                                                            "ingressConfigurations",
                                                            name
                                                        ]);
                                                        // 如果 sysname 为空，则忽略其他字段校验
                                                        if (!currentConfig?.sysname) {
                                                            return Promise.resolve();
                                                        }

                                                        // 编辑模式下，检查当前配置的其他字段是否都为空
                                                        if (editRecord) {
                                                            const hasOtherFields =
                                                                currentConfig.classifier ||
                                                                currentConfig.trust_mode ||
                                                                currentConfig.forwarding_class ||
                                                                (currentConfig.ports &&
                                                                    currentConfig.ports.length > 0) ||
                                                                (currentConfig.queues &&
                                                                    currentConfig.queues.trim() !== "");
                                                            // 如果其他字段都为空，则不进行必填验证
                                                            if (!hasOtherFields) {
                                                                return Promise.resolve();
                                                            }
                                                        }

                                                        // 如果 sysname 不为空，则 ports 必填
                                                        if (!value || value.length === 0) {
                                                            return Promise.reject(new Error("Please select ports!"));
                                                        }
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                        >
                                            <CustomTreeSelect
                                                popupClassName="custom-popup"
                                                treeData={
                                                    form.getFieldValue(["ingressConfigurations", name, "sysname"])
                                                        ? ingressPortTreeData
                                                        : []
                                                }
                                                treeExpandedKeys={ingressExpandedKeys}
                                                onTreeExpand={keys => setIngressExpandedKeys(keys)}
                                                placeholder="Ports"
                                                onFocus={() => handleIngressPortFocus(name)}
                                                onBlur={() => handleIngressPortBlur(name)}
                                                onChange={(value, label) => {
                                                    console.log("value", value);
                                                    setEditPortData(value);
                                                    const isAllPorts = label[0] === "All Ports";
                                                    form.setFieldValue(
                                                        ["ingressConfigurations", name, "is_all_ports"],
                                                        isAllPorts
                                                    );
                                                    form.setFieldValue(["ingressConfigurations", name, "ports"], value);
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "forwarding_class"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Forwarding Class <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{
                                                span: 24,
                                                style: {
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis"
                                                }
                                            }}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (_, value) => {
                                                        const currentConfig = form.getFieldValue([
                                                            "ingressConfigurations",
                                                            name
                                                        ]);
                                                        // 如果 sysname 为空，则忽略其他字段校验
                                                        if (!currentConfig?.sysname) {
                                                            return Promise.resolve();
                                                        }
                                                        if (editRecord) {
                                                            const hasOtherFields =
                                                                currentConfig.classifier ||
                                                                currentConfig.trust_mode ||
                                                                currentConfig.forwarding_class ||
                                                                (currentConfig.ports && currentConfig.ports.length > 0);
                                                            // 如果其他字段都为空，则不进行必填验证
                                                            if (!hasOtherFields) {
                                                                return Promise.resolve();
                                                            }
                                                        }
                                                        // 如果 sysname 不为空，则 forwarding_class 必填
                                                        if (!value) {
                                                            return Promise.reject(
                                                                new Error("Please select forwarding class!")
                                                            );
                                                        }
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                        >
                                            <Select placeholder="Forwarding Class" allowClear>
                                                {(() => {
                                                    const sysname = form.getFieldValue([
                                                        "ingressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    let sn = "";
                                                    try {
                                                        if (sysname) {
                                                            const sysObj = JSON.parse(sysname);
                                                            sn = sysObj.switch_sn || "";
                                                        }
                                                    } catch (e) {
                                                        sn = "";
                                                    }
                                                    const options = getForwardingClassOptionsBySn(sn);
                                                    return options.map(opt => (
                                                        <Option key={opt} value={opt}>
                                                            {opt}
                                                        </Option>
                                                    ));
                                                })()}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "queues"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Queues <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: (_, value) => {
                                                        const currentConfig = form.getFieldValue([
                                                            "ingressConfigurations",
                                                            name
                                                        ]);
                                                        // 如果 sysname 为空，则忽略其他字段校验
                                                        if (!currentConfig?.sysname) {
                                                            return Promise.resolve();
                                                        }

                                                        // 编辑模式下，检查当前配置的其他字段是否都为空
                                                        if (editRecord) {
                                                            const hasOtherFields =
                                                                currentConfig.classifier ||
                                                                currentConfig.trust_mode ||
                                                                currentConfig.forwarding_class ||
                                                                (currentConfig.ports && currentConfig.ports.length > 0);
                                                            // 如果其他字段都为空，则不进行必填验证
                                                            if (!hasOtherFields) {
                                                                return Promise.resolve();
                                                            }
                                                        }

                                                        const trustMode =
                                                            trustModeMap[`ingress_${name}`] ||
                                                            form.getFieldValue([
                                                                "ingressConfigurations",
                                                                name,
                                                                "trust_mode"
                                                            ]);

                                                        // 如果 sysname 不为空，则 queues 必填
                                                        if (!trustMode && !value) {
                                                            return Promise.reject(
                                                                new Error("Please enter queue ranges!")
                                                            );
                                                        }
                                                        if (!value) {
                                                            return Promise.reject(
                                                                new Error("Please enter queue ranges!")
                                                            );
                                                        }

                                                        const validation = validateQueueFormat(value, trustMode);
                                                        if (!validation.isValid) {
                                                            return Promise.reject(new Error(validation.message));
                                                        }
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                        >
                                            <Input
                                                placeholder="Example: 1-3, 5-7"
                                                onChange={e => {
                                                    const {value} = e.target;
                                                    form.setFieldValue(
                                                        ["ingressConfigurations", name, "queues"],
                                                        value
                                                    );

                                                    // Try to validate and calculate is_all_queues
                                                    const trustMode =
                                                        trustModeMap[`ingress_${name}`] ||
                                                        form.getFieldValue([
                                                            "ingressConfigurations",
                                                            name,
                                                            "trust_mode"
                                                        ]);
                                                    if (trustMode && value) {
                                                        const validation = validateQueueFormat(value, trustMode);
                                                        if (validation.isValid) {
                                                            form.setFieldValue(
                                                                ["ingressConfigurations", name, "is_all_queues"],
                                                                validation.isAllQueues
                                                            );
                                                        }
                                                    }
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col>
                                        {index === 0 ? (
                                            <Button
                                                onClick={() => {
                                                    if (editRecord === null) {
                                                        add();
                                                    } else {
                                                        const currentSysname = form.getFieldValue([
                                                            "ingressConfigurations",
                                                            name,
                                                            "sysname"
                                                        ]);
                                                        add({
                                                            sysname: currentSysname
                                                        });
                                                    }
                                                    // 获取当前有端口数据的交换机，重新计算端口状态
                                                    const currentValues = form.getFieldsValue();
                                                    const allConfigs = [
                                                        ...(currentValues.ingressConfigurations || []),
                                                        ...(currentValues.egressConfigurations || [])
                                                    ];

                                                    // 找到有端口数据的交换机并重新计算
                                                    const switchesWithPorts = new Set();
                                                    allConfigs.forEach(config => {
                                                        if (config.sysname) {
                                                            try {
                                                                const sysObj = JSON.parse(config.sysname);
                                                                if (
                                                                    sysObj.switch_sn &&
                                                                    portDataCache.has(
                                                                        `${sysObj.switch_sn}_QosIngressConfiguration`
                                                                    )
                                                                ) {
                                                                    switchesWithPorts.add(sysObj.switch_sn);
                                                                }
                                                            } catch (e) {
                                                                console.log(e);
                                                            }
                                                        }
                                                    });
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "12px",
                                                    marginTop: "29px",
                                                    width: "auto"
                                                }}
                                                type="link"
                                                icon={<PlusOutlined />}
                                            />
                                        ) : (
                                            <Button
                                                onClick={() => {
                                                    const currentValues = form.getFieldsValue();
                                                    const configToRemove = currentValues.ingressConfigurations[name];

                                                    remove(name);
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "12px",
                                                    width: "auto"
                                                }}
                                                type="link"
                                                icon={<MinusOutlined />}
                                            />
                                        )}
                                    </Col>
                                </Row>
                            ))}
                        </>
                    )}
                </Form.List>
                {/* Egress Port Configuration Section */}
                <div
                    style={{
                        fontSize: "18px",
                        fontWeight: "bold",
                        borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                        paddingBottom: "10px",
                        marginBottom: "20px",
                        marginTop: "40px"
                    }}
                >
                    Egress Port Configuration
                </div>
                <Form.List name="egressConfigurations" initialValue={[{}]}>
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name}, index) => (
                                <Row key={key} gutter={[8, 16]}>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "sysname"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Sysname <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (_, value) => {
                                                        // Egress sysname 可以为空，如果为空则忽略其他字段校验
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                            validateTrigger={["onChange", "onBlur"]}
                                        >
                                            <AmpConTreeSelect
                                                onChange={value => {
                                                    if (value) {
                                                        try {
                                                            const obj = JSON.parse(value);
                                                            if (obj.switch_sn) {
                                                                fetchPortsBySn(
                                                                    obj.switch_sn,
                                                                    "QosEgressConfiguration",
                                                                    true
                                                                );
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    } else {
                                                        setEgressPortTreeData([]);
                                                    }

                                                    form.setFields([
                                                        {
                                                            name: ["egressConfigurations", name, "ports"],
                                                            value: []
                                                        },
                                                        {
                                                            name: ["egressConfigurations", name, "forwarding_class"],
                                                            value: undefined
                                                        },
                                                        {
                                                            name: ["egressConfigurations", name, "local_priority"],
                                                            value: ""
                                                        }
                                                    ]);
                                                }}
                                                treeData={fabricTreeData}
                                                placeholder="Sysname"
                                                disabled={editRecord !== null}
                                                treeDefaultExpandAll
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "scheduler_profile"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Scheduler Profile <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{
                                                span: 24,
                                                style: {
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis"
                                                }
                                            }}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (rule, value) => {
                                                        const currentConfig = form.getFieldValue([
                                                            "egressConfigurations",
                                                            name
                                                        ]);
                                                        // 如果 sysname 为空，则忽略其他字段校验
                                                        if (!currentConfig?.sysname) {
                                                            return Promise.resolve();
                                                        }

                                                        // 编辑模式下，检查当前配置的其他字段是否都为空
                                                        if (editRecord) {
                                                            const hasOtherFields =
                                                                currentConfig.scheduler ||
                                                                currentConfig.forwarding_class ||
                                                                (currentConfig.local_priority !== null &&
                                                                    currentConfig.local_priority !== undefined &&
                                                                    currentConfig.local_priority !== "") ||
                                                                (currentConfig.ports && currentConfig.ports.length > 0);
                                                            // 如果其他字段都为空，则不进行必填验证
                                                            if (!hasOtherFields) {
                                                                return Promise.resolve();
                                                            }
                                                        }

                                                        // 如果 sysname 不为空，则 scheduler_profile 必填
                                                        if (!value || value.trim() === "") {
                                                            return Promise.reject(
                                                                new Error("Please input scheduler profile!")
                                                            );
                                                        }
                                                        if (value.length > 30) {
                                                            return Promise.reject(
                                                                new Error(
                                                                    "Scheduler profile name cannot exceed 30 characters!"
                                                                )
                                                            );
                                                        }
                                                        // Character validation
                                                        if (!/^[\w-]*$/.test(value)) {
                                                            return Promise.reject(
                                                                new Error(
                                                                    "Scheduler profile can only contain letters, numbers, underscore (_), and hyphen (-)"
                                                                )
                                                            );
                                                        }
                                                        // Original egress validation
                                                        return validateEgressConfigurations(rule, value, name);
                                                    }
                                                }
                                            ]}
                                        >
                                            <Input placeholder="Scheduler Profile" />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "scheduler"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Scheduler <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (rule, value) => {
                                                        const currentConfig = form.getFieldValue([
                                                            "egressConfigurations",
                                                            name
                                                        ]);
                                                        // 如果 sysname 为空，则忽略其他字段校验
                                                        if (!currentConfig?.sysname) {
                                                            return Promise.resolve();
                                                        }

                                                        // 编辑模式下，检查当前配置的其他字段是否都为空
                                                        if (editRecord) {
                                                            const hasOtherFields =
                                                                currentConfig.scheduler_profile ||
                                                                currentConfig.forwarding_class ||
                                                                (currentConfig.local_priority !== null &&
                                                                    currentConfig.local_priority !== undefined &&
                                                                    currentConfig.local_priority !== "") ||
                                                                (currentConfig.ports && currentConfig.ports.length > 0);
                                                            // 如果其他字段都为空，则不进行必填验证
                                                            if (!hasOtherFields) {
                                                                return Promise.resolve();
                                                            }
                                                        }

                                                        // 如果 sysname 不为空，则 scheduler 必填
                                                        if (!value) {
                                                            return Promise.reject(
                                                                new Error("Please select scheduler!")
                                                            );
                                                        }
                                                        if (value.length > 30) {
                                                            return Promise.reject(
                                                                new Error("Scheduler name cannot exceed 30 characters!")
                                                            );
                                                        }
                                                        // Character validation
                                                        if (!/^[\w-]*$/.test(value)) {
                                                            return Promise.reject(
                                                                new Error(
                                                                    "Scheduler can only contain letters, numbers, underscore (_), and hyphen (-)"
                                                                )
                                                            );
                                                        }
                                                        // Original egress validation
                                                        return validateEgressConfigurations(rule, value, name);
                                                    }
                                                }
                                            ]}
                                        >
                                            <Select
                                                placeholder="Select scheduler"
                                                style={{width: "100%"}}
                                                options={(() => {
                                                    const sysname = form.getFieldValue([
                                                        "egressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);

                                                    // 使用辅助函数获取选项
                                                    const options = getSchedulerOptions(sysname, `egress_${name}`);
                                                    return options.map(item => ({
                                                        label: item,
                                                        value: item
                                                    }));
                                                })()}
                                                allowClear
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "ports"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Ports <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (_, value) => {
                                                        const currentConfig = form.getFieldValue([
                                                            "egressConfigurations",
                                                            name
                                                        ]);
                                                        // 如果 sysname 为空，则忽略其他字段校验
                                                        if (!currentConfig?.sysname) {
                                                            return Promise.resolve();
                                                        }

                                                        // 编辑模式下，检查当前配置的其他字段是否都为空
                                                        if (editRecord) {
                                                            const hasOtherFields =
                                                                currentConfig.scheduler_profile ||
                                                                currentConfig.scheduler ||
                                                                currentConfig.forwarding_class ||
                                                                (currentConfig.local_priority !== null &&
                                                                    currentConfig.local_priority !== undefined &&
                                                                    currentConfig.local_priority !== "");
                                                            // 如果其他字段都为空，则不进行必填验证
                                                            if (!hasOtherFields) {
                                                                return Promise.resolve();
                                                            }
                                                        }

                                                        // 如果 sysname 不为空，则 ports 必填
                                                        if (!value || value.length === 0) {
                                                            return Promise.reject(new Error("Please select ports!"));
                                                        }
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                        >
                                            <CustomTreeSelect
                                                popupClassName="custom-popup"
                                                treeData={
                                                    form.getFieldValue(["egressConfigurations", name, "sysname"])
                                                        ? egressPortTreeData
                                                        : []
                                                }
                                                treeExpandedKeys={egressExpandedKeys}
                                                onTreeExpand={keys => setEgressExpandedKeys(keys)}
                                                placeholder="Ports"
                                                onFocus={() => handleEgressPortFocus(name)}
                                                onBlur={() => handleEgressPortBlur(name)}
                                                onChange={(value, label) => {
                                                    console.log("value", value);
                                                    setEditPortData(value);
                                                    const isAllPorts = label[0] === "All Ports";
                                                    form.setFieldValue(
                                                        ["egressConfigurations", name, "is_all_ports"],
                                                        isAllPorts
                                                    );
                                                    form.setFieldValue(["egressConfigurations", name, "ports"], value);
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "forwarding_class"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Forwarding Class <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{
                                                span: 24,
                                                style: {
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis"
                                                }
                                            }}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (rule, value) => {
                                                        const currentConfig = form.getFieldValue([
                                                            "egressConfigurations",
                                                            name
                                                        ]);
                                                        // 如果 sysname 为空，则忽略其他字段校验
                                                        if (!currentConfig?.sysname) {
                                                            return Promise.resolve();
                                                        }

                                                        // 编辑模式下，检查当前配置的其他字段是否都为空
                                                        if (editRecord) {
                                                            const hasOtherFields =
                                                                currentConfig.scheduler_profile ||
                                                                currentConfig.scheduler ||
                                                                (currentConfig.local_priority !== null &&
                                                                    currentConfig.local_priority !== undefined &&
                                                                    currentConfig.local_priority !== "") ||
                                                                (currentConfig.ports && currentConfig.ports.length > 0);
                                                            // 如果其他字段都为空，则不进行必填验证
                                                            if (!hasOtherFields) {
                                                                return Promise.resolve();
                                                            }
                                                        }

                                                        // 如果 sysname 不为空，则 forwarding_class 必填
                                                        if (!value) {
                                                            return Promise.reject(
                                                                new Error("Please select forwarding class!")
                                                            );
                                                        }
                                                        // Original egress validation
                                                        return validateEgressConfigurations(rule, value, name);
                                                    }
                                                }
                                            ]}
                                        >
                                            <Select
                                                allowClear
                                                placeholder="Forwarding Class"
                                                onChange={selectedForwardingClass => {
                                                    const sysname = form.getFieldValue([
                                                        "egressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);

                                                    if (!sysname || !selectedForwardingClass) {
                                                        return form.setFieldValue(
                                                            ["egressConfigurations", name, "local_priority"],
                                                            ""
                                                        );
                                                    }

                                                    try {
                                                        const sysObj = JSON.parse(sysname);
                                                        const key = `${sysObj.switch_sn}-${selectedForwardingClass}`;
                                                        const localPriority = snFcPriorityMap[key];
                                                        form.setFieldValue(
                                                            ["egressConfigurations", name, "local_priority"],
                                                            localPriority !== undefined && localPriority !== null
                                                                ? localPriority
                                                                : ""
                                                        );
                                                    } catch (e) {
                                                        console.error(
                                                            "Error parsing sysname in egress forwarding class onChange:",
                                                            e
                                                        );
                                                        form.setFieldValue(
                                                            ["egressConfigurations", name, "local_priority"],
                                                            ""
                                                        );
                                                    }
                                                }}
                                            >
                                                {(() => {
                                                    console.log(name);
                                                    const sysname = form.getFieldValue([
                                                        "egressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    let sn = "";
                                                    try {
                                                        if (sysname) {
                                                            const sysObj = JSON.parse(sysname);
                                                            sn = sysObj.switch_sn || "";
                                                        }
                                                    } catch (e) {
                                                        sn = "";
                                                    }
                                                    const options = getForwardingClassOptionsBySn(sn);
                                                    return options.map(opt => (
                                                        <Option key={opt} value={opt}>
                                                            {opt}
                                                        </Option>
                                                    ));
                                                })()}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            className={style.formItem}
                                            name={[name, "local_priority"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Local Priority <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[
                                                {
                                                    validator: async (_, value) => {
                                                        const currentConfig = form.getFieldValue([
                                                            "egressConfigurations",
                                                            name
                                                        ]);
                                                        // 如果 sysname 为空，则忽略其他字段校验
                                                        if (!currentConfig?.sysname) {
                                                            return Promise.resolve();
                                                        }

                                                        // 编辑模式下，检查当前配置的其他字段是否都为空
                                                        if (editRecord) {
                                                            const hasOtherFields =
                                                                currentConfig.scheduler_profile ||
                                                                currentConfig.scheduler ||
                                                                currentConfig.forwarding_class ||
                                                                (currentConfig.local_priority !== null &&
                                                                    currentConfig.local_priority !== undefined &&
                                                                    currentConfig.local_priority !== "") ||
                                                                (currentConfig.ports && currentConfig.ports.length > 0);
                                                            // 如果其他字段都为空，则不进行必填验证
                                                            if (!hasOtherFields) {
                                                                return Promise.resolve();
                                                            }
                                                        }

                                                        // 如果 sysname 不为空，则 local_priority 必填
                                                        // 注意：local_priority 可能为 0，所以需要特殊处理
                                                        if (value === null || value === undefined || value === "") {
                                                            return Promise.reject(
                                                                new Error("Please input local priority!")
                                                            );
                                                        }
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                        >
                                            <Input placeholder="Local Priority" disabled />
                                        </Form.Item>
                                    </Col>
                                    <Col>
                                        {index === 0 ? (
                                            <Button
                                                onClick={() => {
                                                    if (editRecord === null) {
                                                        add();
                                                    } else {
                                                        const currentSysname = form.getFieldValue([
                                                            "egressConfigurations",
                                                            name,
                                                            "sysname"
                                                        ]);
                                                        add({
                                                            sysname: currentSysname
                                                        });
                                                    }
                                                    // 获取当前有端口数据的交换机，重新计算端口状态
                                                    const currentValues = form.getFieldsValue();
                                                    const allConfigs = [
                                                        ...(currentValues.ingressConfigurations || []),
                                                        ...(currentValues.egressConfigurations || [])
                                                    ];

                                                    // 找到有端口数据的交换机并重新计算
                                                    const switchesWithPorts = new Set();
                                                    allConfigs.forEach(config => {
                                                        if (config.sysname) {
                                                            try {
                                                                const sysObj = JSON.parse(config.sysname);
                                                                if (
                                                                    sysObj.switch_sn &&
                                                                    portDataCache.has(
                                                                        `${sysObj.switch_sn}_QosEgressConfiguration`
                                                                    )
                                                                ) {
                                                                    switchesWithPorts.add(sysObj.switch_sn);
                                                                }
                                                            } catch (e) {
                                                                console.log(e);
                                                            }
                                                        }
                                                    });
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "12px",
                                                    marginTop: "29px",
                                                    width: "auto"
                                                }}
                                                type="link"
                                                icon={<PlusOutlined />}
                                            />
                                        ) : (
                                            <Button
                                                onClick={() => {
                                                    const currentValues = form.getFieldsValue();
                                                    const configToRemove = currentValues.egressConfigurations[name];

                                                    remove(name);
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "12px",
                                                    width: "auto"
                                                }}
                                                type="link"
                                                icon={<MinusOutlined />}
                                            />
                                        )}
                                    </Col>
                                </Row>
                            ))}
                        </>
                    )}
                </Form.List>
            </>
        );
    };

    // Service Scheduling 表格配置
    const schedulingColumns = [
        // {
        //     title: "",
        //     key: "expand",
        //     width: 50,
        //     render: (_, record) => {}
        // },
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown),
            ellipsis: true
        },
        {
            ...createColumnConfig("Forwarding Class", "forwarding_class"),
            sorter: (a, b) => a.forwarding_class.join("").localeCompare(b.forwarding_class.join("")),
            ellipsis: true
        },
        {
            ...createColumnConfig("Local Priority", "local_priority"),
            sorter: (a, b) => a.local_priority - b.local_priority,
            ellipsis: true
        },
        {
            ...createColumnConfig("Scheduler", "scheduler"),
            sorter: (a, b) => a.scheduler.join("").localeCompare(b.scheduler.join("")),
            ellipsis: true
        },
        {
            ...createColumnConfig("QoS Mode", "mode"),
            sorter: (a, b) => a.mode.join("").localeCompare(b.mode.join("")),
            ellipsis: true
        },
        {
            ...createColumnConfig("Weight", "weight"),
            sorter: (a, b) => a.weight - b.weight,
            ellipsis: true,
            render: text => text || "--"
        },
        {
            ...createColumnConfig("Guaranteed Rate", "guaranteed_rate"),
            sorter: (a, b) => a.guaranteed_rate - b.guaranteed_rate,
            ellipsis: true,
            render: text => text || "--"
        },
        {
            ...createColumnConfig("Ingress Ports", "ingress_ports"),
            sorter: (a, b) => a.ingress_ports.join("").localeCompare(b.ingress_ports.join("")),
            render: (text, record) => {
                // 根据 is_ingress_all_ports 字段判断显示内容
                if (record.is_ingress_all_ports) {
                    return "All Ports";
                }

                if (Array.isArray(record.ingress_ports) && record.ingress_ports.length === 0) {
                    return "--";
                }

                return renderArrayColumn(text);
            }
        },
        {
            ...createColumnConfig("Egress Ports", "egress_ports"),
            sorter: (a, b) => a.egress_ports.join("").localeCompare(b.egress_ports.join("")),
            render: (text, record) => {
                // 根据 is_egress_all_ports 字段判断显示内容
                if (record.is_egress_all_ports) {
                    return "All Ports";
                }

                if (Array.isArray(record.egress_ports) && record.egress_ports.length === 0) {
                    return "--";
                }

                return renderArrayColumn(text);
            }
        },
        {
            title: "Operation",
            key: "operation",
            render: (_, record) => (
                <Space size="large" className="actionLink">
                    <a onClick={() => handleEdit(record)}>Edit</a>
                    <a
                        onClick={() =>
                            confirmModalAction("Are you sure you want to delete the configuration items?", () =>
                                handleDeleteConfirm(record)
                            )
                        }
                    >
                        Delete
                    </a>
                </Space>
            )
        }
    ];

    return (
        <div>
            <h3>
                Service Scheduling
                <Tooltip
                    title="Configure Quality of Service (QoS) to ensure different levels of quality and performance for different types of traffic in a network."
                    placement="right"
                >
                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}} />
                </Tooltip>
            </h3>
            <div style={{marginBottom: 4}}>
                <Button type="primary" onClick={handleCreate}>
                    <Icon component={addSvg} />
                    Configuration
                </Button>
            </div>
            <ExtendedTable
                columns={schedulingColumns}
                fetchAPIInfo={getSchedulingConfigList}
                ref={tableRef}
                matchFieldsList={matchFieldsList}
                bordered
            />

            {/* 修改模态框，参考 PFCConfiguration */}
            <AmpConCustomModalForm
                title={editRecord ? "Edit Configuration" : "Create Scheduling Configuration"}
                isModalOpen={isModalVisible}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={formItems}
                onCancel={handleModalCancel}
                onSubmit={handleSubmit}
                modalClass="ampcon-max-modal"
                confirmLoading={saveLoading}
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={handleModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={form.submit}>
                        Apply
                    </Button>
                ]}
            />
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
        </div>
    );
};

export default SchedulingConfig;
