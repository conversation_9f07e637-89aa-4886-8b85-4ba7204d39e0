import React, {useEffect, useState, useRef} from "react";
import {Tooltip, message} from "antd";
import {QuestionCircleOutlined} from "@ant-design/icons";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {getEcnConfigList, getEcnPortConfigList} from "@/modules-ampcon/apis/roce_api";
import CustomQuestionIcon from "./QuestionIcon";
import {ExtendedTable, PaginationTable} from "./ext_table";
import {renderArrayColumn} from "./utils";

const ECNOverview = () => {
    const [loading, setLoading] = useState(false);
    const [portConfigLoading, setPortConfigLoading] = useState(false);

    // 添加表格引用
    const ecnTableRef = useRef(null);
    const portConfigTableRef = useRef(null);

    const matchFieldsList = [
        {name: "sysname", matchMode: "fuzzy"},
        {name: "port", matchMode: "fuzzy"}
    ];
    const searchFieldsList = ["sysname", "port"];

    const columns = [
        createColumnConfig("Sysname", "sysname", TableFilterDropdown),
        {
            ...createColumnConfig("Easy ECN", "enabled"),
            render: (_, record) => {
                return <div className="force-wrap-text">{record.enabled ? "Enabled" : "Disabled"}</div>;
            }
        },
        {
            ...createColumnConfig("Mode", "mode", TableFilterDropdown),
            render: text => {
                if (text === "throughput-first") {
                    return "Throughput First";
                }
                if (text === "latency-first") {
                    return "Latency First";
                }
                return "--";
            }
        }
    ];

    // 修改端口配置表格列配置
    const portConfigColumns = [
        // {
        //     title: "",
        //     key: "expand",
        //     width: 50,
        //     render: (_, record) => {}
        // },
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown),
            ellipsis: true
        },
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            sorter: (a, b) => a.port.join("").localeCompare(b.port.join("")),
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }

                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Queues", "queue", TableFilterDropdown),
            sorter: (a, b) => a.queue.join("").localeCompare(b.queue.join("")),
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }

                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("Min Threshold", "min_threshold"),
            sorter: (a, b) => a.min_threshold - b.min_threshold
        },
        {
            ...createColumnConfig("Max Threshold", "max_threshold"),
            sorter: (a, b) => a.max_threshold - b.max_threshold
        },
        {
            ...createColumnConfig("Drop Probability", "drop_probability"),
            sorter: (a, b) => a.drop_probability - b.drop_probability
        },
        {
            ...createColumnConfig("ECN Threshold", "ecn_threshold"),
            sorter: (a, b) => a.ecn_threshold - b.ecn_threshold
        },
        {
            ...createColumnConfig("WRED Status", "wred_status", TableFilterDropdown),
            sorter: (a, b) => a.wred_status.join("").localeCompare(b.wred_status.join("")),
            render: (_, record) => {
                return <div className="force-wrap-text">{record.wred_status ? "Enabled" : "Disabled"}</div>;
            }
        }
    ];

    return (
        <div>
            <h3 style={{marginBottom: 20}}>
                ECN Information
                <Tooltip
                    title={
                        <>
                            Configure ECN for congestion management through Easy ECN or standard ECN: <br />· To use
                            Easy ECN for globally default ECN configurations with a click, select Enable. <br />· To use
                            standard ECN for fine-tuning of ECN configurations on specific interface queues, select
                            Disable.
                        </>
                    }
                    placement="right"
                >
                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}} />
                </Tooltip>
            </h3>
            <div style={{fontSize: 16, fontWeight: 700, marginBottom: 4}}>Configuration</div>
            <PaginationTable
                columns={columns}
                fetchAPIInfo={getEcnConfigList}
                // loading={loading}
                isShowPagination
                ref={ecnTableRef}
                bordered
            />

            <div style={{fontSize: 16, fontWeight: 700, marginBottom: 4, marginTop: 24}}>Port Configuration</div>
            <ExtendedTable
                columns={portConfigColumns}
                fetchAPIInfo={getEcnPortConfigList}
                matchFieldsList={matchFieldsList}
                searchFieldsList={searchFieldsList}
                // loading={portConfigLoading}
                isShowPagination
                ref={portConfigTableRef}
                bordered
            />
        </div>
    );
};

export default ECNOverview;
