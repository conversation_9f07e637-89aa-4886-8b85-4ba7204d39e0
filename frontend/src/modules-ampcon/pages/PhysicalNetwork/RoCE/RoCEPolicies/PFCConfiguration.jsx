import React, {useState, useEffect, useRef, useMemo} from "react";
import {Button, Tooltip, Form, Space, Row, Col, Select, Input, TreeSelect, message, Divider, Spin} from "antd";
import Icon, {PlusOutlined, MinusOutlined, QuestionCircleOutlined} from "@ant-design/icons";
import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {
    getPfcConfigList,
    savePfcConfig,
    updatePfcConfig,
    getFabricSwitches,
    getSwitchPorts,
    getFilterSwitchPorts,
    deletePfcConfig,
    validatePfcConfig,
    getPfcConfigDetailBySwitch
} from "@/modules-ampcon/apis/roce_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CustomQuestionIcon from "./QuestionIcon";
import CustomTreeSelect from "./customTreeSelect";
import {ExtendedTable} from "./ext_table";
import style from "./roce_policies.module.scss";
import {fetchFabricSwitchList, renderArrayColumn} from "./utils";
import {addSvg} from "@/utils/common/iconSvg";
import {AmpConTreeSelect} from "@/modules-ampcon/components/custom_tree";
import {showCustomErrorMessage} from "@/modules-ampcon/pages/PhysicalNetwork/RoCE/RoCEEasydeploy/custom_message";

const {Option} = Select;

const PFCConfiguration = () => {
    // 状态管理
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [form] = Form.useForm();
    const [fabricTreeData, setFabricTreeData] = useState([]);
    const [portTreeData, setPortTreeData] = useState([]);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [editRecord, setEditRecord] = useState(null);
    const [editPortData, setEditPortData] = useState([]);

    const [isShowSpin, setIsShowSpin] = useState(false);

    // PFC Enabled 选项
    const pfcEnabledOptions = ["Enabled", "Disabled"];
    const queueLists = ["0", "1", "2", "3", "4", "5", "6", "7"];

    const searchFieldsList = ["sysname", "profile_name", "port"];
    const matchFieldsList = [
        {name: "sysname", matchMode: "fuzzy"},
        {name: "port", matchMode: "fuzzy"}
    ];

    const [portDataCache, setPortDataCache] = useState(new Map());

    const tableRef = useRef(null);

    const fetchFabricTreeData = async () => {
        const tree = await fetchFabricSwitchList("PfcConfiguration");
        if (tree) {
            setFabricTreeData(tree);
        }
    };

    // 初始化端口树数据，默认所有端口都为enable状态
    const initPortTreeData = portData => {
        console.log("portData", portData);

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: false // 初始化时所有端口都可用
        }));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: false, // 初始化时All Ports也可用
                children: allPortsForDisplay
            }
        ];

        setPortTreeData(tree);
        setExpandedKeys([tree[0].value, ...tree[0].children.map(child => child.key)]);
    };

    // 简化 fetchPortsBySn
    const fetchPortsBySn = async (sn, forceRefresh = false) => {
        try {
            const res = await getFilterSwitchPorts({
                switch_sn: sn,
                query_model: "PfcConfiguration"
            });

            if (res && res.status === 200 && Array.isArray(res.data)) {
                const portData = res.data;
                setPortDataCache(prev => new Map(prev).set(sn, portData));
                initPortTreeData(portData);
            } else {
                message.error(res.msg);
            }
        } catch (error) {
            console.error("Failed to fetch filtered ports:", error);
            message.error("Failed to fetch ports");
            setPortTreeData([]);
        }
    };

    const handleCreate = () => {
        setIsModalVisible(true);
        setEditRecord(null);
        form.resetFields();
        setPortTreeData([]);

        // 在创建时获取 fabric 数据
        fetchFabricTreeData();

        form.setFieldsValue({
            configurations: [
                {
                    sysname: null,
                    pfc_profile: "",
                    ports: [],
                    queues: [],
                    pfc_enabled: null
                }
            ]
        });
    };

    // 编辑
    const handleEdit = async record => {
        try {
            setIsModalVisible(true);
            setEditRecord(record);

            await fetchPortsBySn(record.switch_sn, true);

            // 根据 switch_sn 获取该交换机下的所有PFC配置
            const result = await getPfcConfigDetailBySwitch({
                switch_sn: record.switch_sn
            });

            let defaultSysname = null;
            const fabricName = record.fabric;
            let {sysname} = record;
            let {switch_sn} = record;

            if (result.status === 200 && result.data) {
                if (result.data && result.data.length > 0) {
                    console.log("result.data", result.data);
                    const firstConfig = result.data[0];
                    console.log(firstConfig);
                    sysname = firstConfig.sysname;
                    switch_sn = firstConfig.switch_sn;
                }

                defaultSysname = JSON.stringify({sn: switch_sn, sysname});
                console.log("defaultSysname", defaultSysname);

                setFabricTreeData([
                    {
                        title: record.fabric,
                        value: record.fabric,
                        key: record.fabric,
                        children: [
                            {
                                title: `${sysname} ${switch_sn}`,
                                value: defaultSysname,
                                key: defaultSysname
                            }
                        ]
                    }
                ]);

                // 将获取到的配置数据转换为表单需要的格式
                const configurations = result.data.map(config => {
                    return {
                        config_id: config.id,
                        sysname: defaultSysname,
                        pfc_profile: config.profile_name,
                        ports: config.is_all_ports ? [JSON.stringify(config.port)] : config.port,
                        queues: config.is_all_queues ? [JSON.stringify(config.queue)] : config.queue,
                        pfc_enabled: config.enabled ? "Disabled" : "Enabled",
                        is_all_ports: config.is_all_ports,
                        is_all_queues: config.is_all_queues
                    };
                });

                form.setFieldsValue({
                    configurations
                });

                // 初始化端口占用记录
                const newConfigPortOccupancy = new Map();
                configurations.forEach((config, index) => {
                    try {
                        const sysObj = JSON.parse(config.sysname);
                        const configKey = `${sysObj.sn}-${index}`;
                        let ports = [];

                        if (config.ports && Array.isArray(config.ports)) {
                            if (config.is_all_ports) {
                                ports = JSON.parse(config.ports[0]);
                            } else {
                                ports = config.ports;
                            }
                        }
                        console.log("configKey", configKey, ports);
                        newConfigPortOccupancy.set(configKey, ports);
                    } catch (e) {
                        console.error("Error initializing port occupancy:", e);
                    }
                });
                // setConfigPortOccupancy(newConfigPortOccupancy);
            } else {
                message.warning("Failed to load switch configurations");
                setIsModalVisible(false);
                setEditRecord(null);
            }
        } catch (error) {
            console.error("Failed to load PFC configurations:", error);
            message.error("Failed to load configurations");
            setIsModalVisible(false);
            setEditRecord(null);
        }
    };

    // 取消
    const handleModalCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
        setPortTreeData([]);
    };

    // 修改提交函数，在提交时计算 is_all_ports 和 is_all_queues
    const handleSubmit = async values => {
        try {
            const configurationsArray = Array.isArray(values.configurations) ? values.configurations : [];

            const configs = configurationsArray.map(config => {
                let sysObj = {};
                try {
                    sysObj = JSON.parse(config.sysname);
                } catch (e) {
                    console.error("Failed to parse sysname:", e);
                    return;
                }

                // 获取对应交换机的端口数据来判断是否是全选
                const portData = portDataCache.get(sysObj.sn);
                const allPortsValue = portData ? JSON.stringify(portData.map(item => item.port_name)) : null;
                const allQueuesValue = JSON.stringify(queueLists);

                // 计算 is_all_ports
                const is_all_ports = config.ports && config.ports.length === 1 && config.ports[0] === allPortsValue;

                // 计算 is_all_queues
                const is_all_queues =
                    config.queues && config.queues.length === 1 && config.queues[0] === allQueuesValue;

                let ports = [];
                let queues = [];

                if (is_all_ports) {
                    ports = JSON.parse(config.ports[0]);
                } else {
                    ports = config.ports;
                }

                if (is_all_queues) {
                    queues = JSON.parse(config.queues[0]);
                } else {
                    queues = config.queues.length === 0 ? queueLists : config.queues;
                }

                return {
                    sysname: sysObj.sysname,
                    switch_sn: sysObj.sn,
                    port: ports,
                    queue: queues,
                    profile_name: config.pfc_profile,
                    enabled: config.pfc_enabled === "Disabled",
                    is_all_ports,
                    is_all_queues: queues.length === queueLists.length ? true : is_all_queues,
                    ...(config.config_id ? {config_id: config.config_id} : {})
                };
            });

            const filteredConfigs = configs.filter(Boolean);
            let ret;
            setIsShowSpin(true);
            if (editRecord) {
                // 编辑模式：使用 update 接口
                const params = {
                    switch_sn: editRecord.switch_sn,
                    configurations: filteredConfigs
                };
                console.log("Update params:", params);
                ret = await updatePfcConfig(params);
            } else {
                // 创建模式：使用 save 接口
                const params = {configurations: filteredConfigs};
                console.log("Create params:", params);
                ret = await savePfcConfig(params);
            }
            setIsShowSpin(false);
            if (ret.status === 200) {
                form.resetFields();
                setIsModalVisible(false);
                setEditRecord(null);
                message.success(ret.msg);
                tableRef.current.refreshTable();
            } else {
                // 处理多分段错误 (Handle multi-segment errors)
                if (ret.data && Array.isArray(ret.data) && ret.data.length > 0) {
                    showCustomErrorMessage(ret.msg, ret.data, 6);
                } else {
                    message.error(ret.msg);
                }
                form.resetFields();
                setIsModalVisible(false);
                setEditRecord(null);
                tableRef.current.refreshTable();
            }
        } catch (e) {
            console.error("Submit failed:", e);
            message.error("Submit failed");
        }
    };

    // 删除
    const handleDeleteConfirm = async record => {
        try {
            setIsShowSpin(true);
            const ret = await deletePfcConfig(record.id);
            setIsShowSpin(false);
            if (ret.status === 200) {
                message.success(ret.msg);
                tableRef.current.refreshTable();
            } else {
                // 处理多分段错误 (Handle multi-segment errors)
                if (ret.data && Array.isArray(ret.data) && ret.data.length > 0) {
                    showCustomErrorMessage(ret.msg, ret.data, 6);
                } else {
                    message.error(ret.msg);
                }
                tableRef.current.refreshTable();
            }
        } catch (e) {
            message.error("Delete failed");
        }
    };

    /**
     * 解析 sysname 获取 switch_sn (PFC 使用 sn 字段)
     */
    const parseSwitchSn = sysname => {
        if (!sysname) return null;
        try {
            const obj = JSON.parse(sysname);
            return obj.sn; // PFC 使用 sn 字段而不是 switch_sn
        } catch (e) {
            return null;
        }
    };

    /**
     * PFC Profile 校验器 - 校验 switch_sn 和 profile_name 组合的唯一性
     * 规则：同一交换机下的 profile_name 不能重复
     */
    const validateProfileName = (rule, value, index) => {
        if (!value) {
            return Promise.resolve();
        }

        const formData = form.getFieldsValue();
        const configurations = formData.configurations || [];

        // 获取当前行的 switch_sn
        const currentConfig = configurations[index];
        if (!currentConfig || !currentConfig.sysname) {
            return Promise.resolve();
        }

        const switchSn = parseSwitchSn(currentConfig.sysname);
        if (!switchSn) {
            return Promise.resolve();
        }

        // 检查同一交换机下是否有重复的 profile_name
        const duplicateIndex = configurations.findIndex((config, idx) => {
            if (idx === index) return false; // 跳过当前行
            if (!config || !config.sysname || !config.pfc_profile) return false;

            const configSwitchSn = parseSwitchSn(config.sysname);
            return configSwitchSn === switchSn && config.pfc_profile === value;
        });

        if (duplicateIndex !== -1) {
            return Promise.reject(new Error(`PFC Profile '${value}' already exists on switch ${switchSn}`));
        }
        return Promise.resolve();
    };

    // 处理端口选择器获得焦点时的逻辑
    const handlePortFocus = name => {
        const currentSysname = form.getFieldValue(["configurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const portData = portDataCache.get(sysObj.sn);
                    if (portData) {
                        // 收集当前sn下非当前行占用的端口
                        const occupiedPorts = new Set();
                        const currentFormValues = form.getFieldsValue();

                        if (currentFormValues.configurations) {
                            currentFormValues.configurations.forEach((config, configIndex) => {
                                // 跳过当前行
                                if (configIndex === name) return;

                                if (config.sysname && config.ports) {
                                    try {
                                        const configSysObj = JSON.parse(config.sysname);
                                        // 只处理相同sn的配置
                                        if (configSysObj.sn === sysObj.sn) {
                                            let selectedPorts = [];
                                            if (Array.isArray(config.ports) && config.ports.length > 0) {
                                                const allPortsValue = JSON.stringify(
                                                    portData.map(item => item.port_name)
                                                );
                                                if (config.ports[0] === allPortsValue) {
                                                    selectedPorts = JSON.parse(config.ports[0]);
                                                } else {
                                                    selectedPorts = config.ports;
                                                }
                                            }
                                            selectedPorts.forEach(port => occupiedPorts.add(port));
                                        }
                                    } catch (e) {
                                        console.error("Error parsing config sysname:", e);
                                    }
                                }
                            });
                        }

                        // 构建新的树形数据，将占用的端口置为disabled
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: occupiedPorts.has(item.port_name)
                        }));

                        // 如果有任意端口被占用，All Ports也置为disabled
                        const hasAnyPortOccupied = occupiedPorts.size > 0;

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: hasAnyPortOccupied,
                                children: allPortsForDisplay
                            }
                        ];

                        setPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 处理端口选择器失去焦点时的逻辑
    const handlePortBlur = name => {
        const currentSysname = form.getFieldValue(["configurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const portData = portDataCache.get(sysObj.sn);
                    if (portData) {
                        // onBlur时将所有option置为enable
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: false
                        }));

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: false,
                                children: allPortsForDisplay
                            }
                        ];

                        setPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 重新调整 formItems 函数，接受必要的参数
    const formItems = () => {
        return (
            <Form.List name="configurations">
                {(fields, {add, remove}) => (
                    <>
                        {fields.map(({key, name}, index) => (
                            <Row key={key} gutter={8}>
                                {/* Sysname */}
                                <Col span={4}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "sysname"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Sysname <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select sysname!"}]}
                                    >
                                        <AmpConTreeSelect
                                            onChange={value => {
                                                if (value) {
                                                    const obj = JSON.parse(value);
                                                    fetchPortsBySn(obj.sn, true);
                                                } else {
                                                    setPortTreeData([]);
                                                }

                                                form.setFields([
                                                    {
                                                        name: ["configurations", name, "ports"],
                                                        value: []
                                                    }
                                                ]);
                                            }}
                                            treeData={fabricTreeData}
                                            placeholder="Sysname"
                                            disabled={editRecord !== null}
                                            treeDefaultExpandAll
                                        />
                                    </Form.Item>
                                </Col>

                                {/* PFC Profile */}
                                <Col span={4}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "pfc_profile"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    PFC Profile <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{
                                            span: 24,
                                            style: {
                                                whiteSpace: "nowrap",
                                                overflow: "hidden",
                                                textOverflow: "ellipsis"
                                            }
                                        }}
                                        wrapperCol={{span: 24}}
                                        validateTrigger="onBlur"
                                        rules={[
                                            {required: true, message: "Please input PFC Profile!"},
                                            {
                                                max: 30,
                                                message: "PFC Profile cannot exceed 30 characters"
                                            },
                                            {
                                                pattern: /^[\dA-Za-z]*$/,
                                                message: "PFC Profile can only contain letters and numbers"
                                            },
                                            {
                                                validator: (rule, value) => validateProfileName(rule, value, name)
                                            }
                                        ]}
                                    >
                                        <Input placeholder="PFC Profile" maxLength={30} />
                                    </Form.Item>
                                </Col>

                                {/* Ports */}
                                <Col span={4}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "ports"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Ports <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select port!"}]}
                                    >
                                        <CustomTreeSelect
                                            popupClassName="custom-popup"
                                            treeData={
                                                form.getFieldValue(["configurations", name, "sysname"])
                                                    ? portTreeData
                                                    : []
                                            }
                                            treeExpandedKeys={expandedKeys}
                                            onTreeExpand={keys => setExpandedKeys(keys)}
                                            placeholder="Ports"
                                            onFocus={() => handlePortFocus(name)}
                                            onBlur={() => handlePortBlur(name)}
                                            onChange={(value, label) => {
                                                console.log("value", value);
                                                setEditPortData(value);
                                            }}
                                        />
                                    </Form.Item>
                                </Col>

                                {/* Queues */}
                                <Col span={4}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "queues"]}
                                        label={index === 0 ? "Queues" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please select queue!"}]}
                                    >
                                        <CustomTreeSelect
                                            popupClassName="custom-popup"
                                            treeData={[
                                                {
                                                    title: "All Queues",
                                                    value: JSON.stringify(queueLists),
                                                    children: queueLists.map(queue => ({
                                                        title: queue.toString(),
                                                        value: queue.toString(),
                                                        key: queue.toString()
                                                    }))
                                                }
                                            ]}
                                            placeholder="Queues"
                                            onChange={(value, label) => {
                                                form.setFieldsValue({
                                                    configurations: {
                                                        [name]: {
                                                            queues: value
                                                        }
                                                    }
                                                });
                                            }}
                                        />
                                    </Form.Item>
                                </Col>

                                {/* PFC Enabled */}
                                <Col span={4}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "pfc_enabled"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    PFC Enabled <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select PFC status!"}]}
                                    >
                                        <Select
                                            placeholder="PFC Enabled"
                                            dropdownStyle={{
                                                // maxHeight: 200, // 均受限于list-holder 256px
                                                overflow: "auto"
                                            }}
                                        >
                                            {pfcEnabledOptions.map(option => (
                                                <Option key={option} value={option}>
                                                    {option}
                                                </Option>
                                            ))}
                                        </Select>
                                    </Form.Item>
                                </Col>

                                {/* 添加按钮列 */}
                                <Col>
                                    {index === 0 ? (
                                        <Button
                                            onClick={() => {
                                                if (editRecord === null) {
                                                    setPortTreeData([]);
                                                    add({
                                                        sysname: null,
                                                        pfc_profile: "",
                                                        ports: [],
                                                        queues: [],
                                                        pfc_enabled: null
                                                    });
                                                } else {
                                                    const currentSysname = form.getFieldValue([
                                                        "configurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    add({
                                                        sysname: currentSysname,
                                                        pfc_profile: "",
                                                        ports: [],
                                                        queues: [],
                                                        pfc_enabled: null
                                                    });
                                                }
                                            }}
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "12px",
                                                marginTop: "29px",
                                                width: "auto"
                                            }}
                                            type="link"
                                            icon={<PlusOutlined />}
                                        />
                                    ) : (
                                        <Button
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "12px",
                                                marginTop: index === 0 ? "40px" : "0",
                                                width: "auto"
                                            }}
                                            type="link"
                                            icon={<MinusOutlined />}
                                            onClick={() => {
                                                remove(name);
                                            }}
                                        />
                                    )}
                                </Col>

                                {/* 隐藏的表单项 */}
                                <Form.Item name={[name, "config_id"]} hidden>
                                    <Input type="hidden" />
                                </Form.Item>
                            </Row>
                        ))}
                    </>
                )}
            </Form.List>
        );
    };

    // 表格列

    const pfcColumns = [
        // {
        //     title: "",
        //     key: "expand",
        //     width: 50,
        //     render: (_, record) => {}
        // },
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown),
            ellipsis: true
        },
        {
            ...createColumnConfig("PFC Profile", "profile_name"),
            sorter: (a, b) => a.profile_name.localeCompare(b.profile_name),
            ellipsis: true
        },
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            sorter: (a, b) => a.port.join("").localeCompare(b.port.join("")),
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Queues", "queue"),
            sorter: (a, b) => a.queue.join("").localeCompare(b.queue.join("")),
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("PFC Enabled", "enabled"),
            sorter: (a, b) => a.enabled - b.enabled,
            render: (_, record) => {
                return <div>{record.enabled ? "Disabled" : "Enabled"}</div>;
            }
        },
        {
            title: "Operation",
            key: "operation",
            render: (_, record) => (
                <Space size="large" className="actionLink">
                    <a onClick={() => handleEdit(record)}>Edit</a>
                    <a
                        onClick={() =>
                            confirmModalAction("Are you sure you want to delete the configuration items?", () =>
                                handleDeleteConfirm(record)
                            )
                        }
                    >
                        Delete
                    </a>
                </Space>
            )
        }
    ];

    return (
        <div>
            <div style={{marginBottom: 30}}>
                <h3 style={{margin: 0, marginBottom: "20px"}}>
                    PFC Configuration
                    <Tooltip
                        title="Configure Priority Flow Control (PFC) to manage congestion on Ethernet networks. 
                        PFC helps maintain performance and reliability for critical applications."
                        placement="right"
                    >
                        <QuestionCircleOutlined
                            style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}}
                        />
                    </Tooltip>
                </h3>
                <ExtendedTable
                    columns={pfcColumns}
                    fetchAPIInfo={getPfcConfigList}
                    ref={tableRef}
                    bordered
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    extraButton={
                        <div style={{marginBottom: 4}}>
                            <Button type="primary" onClick={handleCreate}>
                                <Icon component={addSvg} />
                                Configuration
                            </Button>
                        </div>
                    }
                />
            </div>
            <AmpConCustomModalForm
                title={editRecord ? "Edit Configuration" : "Create PFC Configuration"}
                isModalOpen={isModalVisible}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={formItems}
                onCancel={handleModalCancel}
                onSubmit={handleSubmit}
                modalClass="ampcon-max-modal"
                loading
                // confirmLoading={
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={handleModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={form.submit}>
                        Apply
                    </Button>
                ]}
            />
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
        </div>
    );
};

export default PFCConfiguration;
