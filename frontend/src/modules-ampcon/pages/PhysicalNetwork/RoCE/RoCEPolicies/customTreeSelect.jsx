import React, {useState} from "react";
import {TreeSelect} from "antd";
import unfoldSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold.svg?react";
import unfoldHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold_hover.svg?react";
import shrinkSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink.svg?react";
import shrinkHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink_hover.svg?react";
import "./customTreeSelect.css";

const CustomTreeSelect = ({
    treeData,
    onChange,
    value,
    placeholder = "Please select",
    multiple = false,
    treeCheckable = true,
    maxTagCount = 1,
    maxTagTextLength = 10,
    style,
    className,
    ...props
}) => {
    const [hoveredIcons, setHoveredIcons] = useState({});

    // 图标悬停处理
    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    // 获取展开/收起图标
    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };

    // 自定义切换图标
    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);
        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px", marginRight: "8px", marginLeft: "8px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    const mergedStyle = {
        width: "100%",
        ...style
    };

    // 组合className，添加溢出样式控制
    const mergedClassName = ["custom-tree-select-multiple", className].filter(Boolean).join(" ");

    return (
        <TreeSelect
            className={mergedClassName}
            treeData={treeData}
            placeholder={placeholder}
            allowClear
            multiple={multiple}
            treeCheckable={treeCheckable}
            showCheckedStrategy={TreeSelect.SHOW_PARENT}
            // maxTagCount={maxTagCount}
            // maxTagTextLength={maxTagTextLength}
            // maxTagPlaceholder={omittedValues => `+${omittedValues.length}`}
            treeNodeFilterProp="title"
            treeIcon={false}
            treeDefaultExpandAll
            switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
            onChange={onChange}
            value={value}
            style={mergedStyle}
            {...props}
        />
    );
};

export default CustomTreeSelect;
