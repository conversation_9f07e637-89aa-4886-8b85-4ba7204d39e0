import React, {useEffect, useState, useRef} from "react";
import {Tooltip, Select, Row, Col, Space, Form, TreeSelect, message} from "antd";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {getPfcBufferTrafficConfigList, getFabricSwitches, getSwitchPorts} from "@/modules-ampcon/apis/roce_api";
import CustomQuestionIcon from "./QuestionIcon";
import unfoldSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold.svg?react";
import unfoldHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold_hover.svg?react";
import shrinkSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink.svg?react";
import shrinkHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink_hover.svg?react";
import {ExtendedTable} from "./ext_table";
import {fetchFabricSwitchList, renderArrayColumn} from "./utils";
import {QuestionCircleOutlined} from "@ant-design/icons";

const PFCBufferConfigurationOverview = () => {
    const [selectedSysname, setSelectedSysname] = useState("");
    const [selectedPort, setSelectedPort] = useState([]);
    const [switchList, setSwitchList] = useState([]);
    const [hoveredIcons, setHoveredIcons] = useState({});
    const [portTreeData, setPortTreeData] = useState([]);

    const searchFieldsList = ["sysname", "port"];

    const matchFieldsList = [
        {name: "sysname", matchMode: "fuzzy"},
        {name: "port", matchMode: "fuzzy"}
    ];

    // 添加表格引用
    const ingressTableRef = useRef(null);
    const egressTableRef = useRef(null);

    useEffect(() => {
        fetchSwitchList();
    }, []);

    useEffect(() => {
        const fetchPorts = async () => {
            if (selectedSysname) {
                try {
                    // 解析选中的交换机信息
                    const parsed = JSON.parse(selectedSysname);
                    if (parsed.sn) {
                        const res = await getSwitchPorts({switch_sn: parsed.sn});
                        if (res && Array.isArray(res.data)) {
                            const portTree = res.data.map(item => ({
                                title: item.port_name,
                                value: item.port_name,
                                key: item.port_name
                            }));
                            setPortTreeData(portTree);
                        }
                    }
                } catch (error) {
                    message.error("Failed to get ports");
                    setPortTreeData([]);
                }
            } else {
                setPortTreeData([]);
                setSelectedPort([]);
            }
        };

        fetchPorts();
    }, [selectedSysname]);

    useEffect(() => {
        ingressTableRef.current.refreshTable();
        egressTableRef.current.refreshTable();
    }, [selectedSysname, selectedPort]);

    // 获取交换机列表
    const fetchSwitchList = async () => {
        const tree = await fetchFabricSwitchList();
        if (tree) {
            setSwitchList(tree);
        }
    };

    const getBufferIngressConfigListWrapper = async (page, pageSize, filterFields, sortFields, searchFields) => {
        const parsed = selectedSysname ? JSON.parse(selectedSysname) : null;
        const switchSn = parsed?.sn;

        const extraParams = {
            ...(switchSn ? {switch_sn: [switchSn]} : {}),
            traffic_type: "ingress"
        };
        if (selectedPort.length > 0) {
            extraParams.port = selectedPort;
        }
        return await getPfcBufferTrafficConfigList(page, pageSize, filterFields, sortFields, searchFields, extraParams);
    };

    const getBufferEgressConfigListWrapper = async (page, pageSize, filterFields, sortFields, searchFields) => {
        const parsed = selectedSysname ? JSON.parse(selectedSysname) : null;
        const switchSn = parsed?.sn;

        const portList = selectedPort;

        const extraParams = {
            ...(switchSn ? {switch_sn: [switchSn]} : {}),
            traffic_type: "egress"
        };
        if (portList.length > 0) {
            extraParams.port = portList;
        }
        return await getPfcBufferTrafficConfigList(page, pageSize, filterFields, sortFields, searchFields, extraParams);
    };

    // Handle sysname changes
    const handleSysnameChange = value => {
        setSelectedSysname(value);
    };

    // Handle port changes
    const handlePortChange = values => {
        setSelectedPort(values);
    };

    // Reference to Deployment hover handling method
    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    // Reference to Deployment icon selection method
    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };

    // Reference to Deployment switcherIcon implementation
    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);
        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px", marginRight: "8px", marginLeft: "8px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    // 修改 Ingress buffer columns 定义，与 PFCBufferConfiguration 保持一致
    const bufferColumnsIngress = [
        // {
        //     title: "",
        //     key: "expand",
        //     width: 50,
        //     render: (_, record) => {}
        // },
        // createColumnConfig("Sysname", "sysname", TableFilterDropdown),
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            sorter: (a, b) => a.port.join("").localeCompare(b.port.join("")),
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Ingress Queues", "queue", TableFilterDropdown),
            sorter: (a, b) => a.queue.join("").localeCompare(b.queue.join("")),
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("Shared Ratio (%)", "shared_ratio"),
            sorter: (a, b) => a.shared_ratio - b.shared_ratio,
            render: text => text || "--"
        },
        {
            ...createColumnConfig("Threshold", "threshold"),
            sorter: (a, b) => a.threshold - b.threshold,
            render: text => text || "--"
        },
        {
            ...createColumnConfig("Guaranteed", "guaranteed"),
            sorter: (a, b) => a.guaranteed - b.guaranteed,
            render: text => text || "--",
            ellipsis: true
        },
        {
            ...createColumnConfig("Reset Offset", "reset_offset"),
            sorter: (a, b) => a.reset_offset - b.reset_offset,
            render: text => text || "--",
            ellipsis: true
        },
        {
            ...createColumnConfig("Headroom", "headroom"),
            sorter: (a, b) => a.headroom - b.headroom,
            render: text => text || "--",
            ellipsis: true
        }
    ];

    // 修改 Egress buffer columns 定义，与 PFCBufferConfiguration 保持一致
    const bufferColumnsEgress = [
        // {
        //     title: "",
        //     key: "expand",
        //     width: 50,
        //     render: (_, record) => {}
        // },
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            sorter: (a, b) => a.port.join("").localeCompare(b.port.join("")),
            render: (_, record) => {
                if (record.is_all_ports) {
                    return <div>All Ports</div>;
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Egress Queues", "queue", TableFilterDropdown),
            sorter: (a, b) => a.queue.join("").localeCompare(b.queue.join("")),
            render: (_, record) => {
                if (record.is_all_queues) {
                    return <div>All Queues</div>;
                }

                if (Array.isArray(record.queue) && record.queue.length === 0) {
                    return "--";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("Shared Ratio (%)", "shared_ratio"),
            sorter: (a, b) => a.shared_ratio - b.shared_ratio,
            render: text => text || "--"
        },
        {
            ...createColumnConfig("Threshold", "threshold"),
            sorter: (a, b) => a.threshold - b.threshold,
            render: text => text || "--"
        }
    ];

    return (
        <div>
            <h3 style={{marginTop: 24}}>
                PFC Buffer Information
                <Tooltip
                    title="Configure PFC buffer to implement traffic control and PFC watchdog, which is based on interface and priority queue. 
                    The storage space of each interface is divided into different buffers independently and a certain action will be executed after the number of accumulated packets reaches the buffer threshold (the unit is cell)."
                    placement="right"
                >
                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}} />
                </Tooltip>
            </h3>
            <div style={{marginBottom: 24, display: "flex", alignItems: "center"}}>
                <Space size={32}>
                    <Form.Item label="Sysname" style={{marginBottom: 0}}>
                        <TreeSelect
                            style={{width: 280}}
                            placeholder="Please select"
                            allowClear
                            value={selectedSysname}
                            onChange={handleSysnameChange}
                            treeData={switchList}
                            switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                            treeNodeFilterProp="title"
                            treeIcon={false}
                            treeDefaultExpandAll
                        />
                    </Form.Item>
                    <Form.Item label="Port" style={{marginBottom: 0}}>
                        <TreeSelect
                            style={{width: 280}}
                            placeholder="Please select"
                            allowClear
                            value={selectedPort}
                            onChange={handlePortChange}
                            treeData={portTreeData}
                            disabled={!selectedSysname}
                            multiple
                            treeCheckable
                            showCheckedStrategy={TreeSelect.SHOW_CHILD}
                            maxTagCount={2}
                            maxTagTextLength={10}
                            maxTagPlaceholder={omittedValues => `+${omittedValues.length} more`}
                        />
                    </Form.Item>
                </Space>
            </div>

            <div style={{fontSize: 16, fontWeight: 700, marginBottom: 4}}>Ingress Buffer Information</div>
            <ExtendedTable
                columns={bufferColumnsIngress}
                fetchAPIInfo={getBufferIngressConfigListWrapper}
                isShowPagination
                ref={ingressTableRef}
                bordered
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
            />

            <div style={{fontSize: 16, fontWeight: 700, marginBottom: 4, marginTop: 24}}>Egress Buffer Information</div>
            <ExtendedTable
                columns={bufferColumnsEgress}
                fetchAPIInfo={getBufferEgressConfigListWrapper}
                isShowPagination
                ref={egressTableRef}
                bordered
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
            />
        </div>
    );
};

export default PFCBufferConfigurationOverview;
