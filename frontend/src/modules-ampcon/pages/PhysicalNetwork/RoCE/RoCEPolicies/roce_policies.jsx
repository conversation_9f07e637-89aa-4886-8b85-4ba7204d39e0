/* eslint-disable react/no-unknown-property */
import React, {useState} from "react";
import {Tabs} from "antd";
import PFCConfig from "./pfcconfig";
import ECNConfig from "./ecnconfig";
import SchedulingConfig from "./schedulingconfig";
import PFCOverview from "./PFCOverview";
import ECNOverview from "./ecnoverview";
import SchedulingOverview from "./schedulingoverview";

const ConfigButtons = ({activeConfig, setActiveConfig}) => {
    const tabItems = [
        {
            key: "pfc",
            label: "PFC"
        },
        {
            key: "ecn",
            label: "ECN"
        },
        {
            key: "scheduling",
            label: "Service Scheduling"
        }
    ];

    return (
        <div style={{marginBottom: 20}}>
            <Tabs
                className="radioGroupTabs customTab roceTabs"
                activeKey={activeConfig}
                onChange={setActiveConfig}
                items={tabItems}
            />
        </div>
    );
};

export const RoCEDeployment = () => {
    const [activeConfig, setActiveConfig] = useState("pfc");
    return (
        <div>
            <ConfigButtons activeConfig={activeConfig} setActiveConfig={setActiveConfig} />
            {activeConfig === "pfc" && <PFCConfig />}
            {activeConfig === "ecn" && <ECNConfig />}
            {activeConfig === "scheduling" && <SchedulingConfig />}
        </div>
    );
};

export const RoCEOverview = () => {
    const [activeConfig, setActiveConfig] = useState("pfc");
    return (
        <div>
            <ConfigButtons activeConfig={activeConfig} setActiveConfig={setActiveConfig} />
            {activeConfig === "pfc" && <PFCOverview />}
            {activeConfig === "ecn" && <ECNOverview />}
            {activeConfig === "scheduling" && <SchedulingOverview />}
        </div>
    );
};
