import {forwardRef, useImperativeHandle, useState} from "react";
import {Menu} from "@antv/x6-react-components";
import "@antv/x6-react-components/es/menu/style/index.css";
import {usePopper} from "react-popper";
import EditConnectionSvg from "./resource/edit_connection_icon.svg?react";
import DeviceManagementSvg from "./resource/device_management_icon.svg?react";
import DeleteDeviceSvg from "./resource/delete_device_icon.svg?react";
import AddConnectionSvg from "./resource/add_connection_black_icon.svg?react";

const RightClickPopUpMenu = forwardRef(
    ({addConnectionCallback, editConnectionCallback, deviceManagementCallback, deleteNodeCallback}, ref) => {
        const MenuItem = Menu.Item;

        const [isDeviceRightClickPopUpMenuVisible, setIsDeviceRightClickPopUpMenuVisible] = useState(false);
        const [selectedNode, setSelectedNode] = useState(null);
        const [deviceType, setDeviceType] = useState("");

        const [referenceElement, setReferenceElement] = useState(null);
        const [menuElement, setMenuElement] = useState(null);

        const {styles, attributes} = usePopper(referenceElement, menuElement, {
            placement: "right-start",
            modifiers: [
                {
                    name: "offset",
                    options: {
                        offset: [10, 10] // Adjust the offset as needed
                    }
                }
            ]
        });

        useImperativeHandle(ref, () => ({
            showDeviceRightClickPopUpMenu: (node, e) => {
                setDeviceType(node.shape);
                setReferenceElement({
                    getBoundingClientRect: () => ({
                        width: 0,
                        height: 0,
                        top: e.clientY,
                        left: e.clientX,
                        right: e.clientX,
                        bottom: e.clientY
                    }),
                    contextElement: document.body
                });
                setIsDeviceRightClickPopUpMenuVisible(true);
                setSelectedNode(node);
            },
            hideDeviceRightClickPopUpMenu: () => {
                setIsDeviceRightClickPopUpMenuVisible(false);
            },
            isShowDeviceRightClickPopUpMenu: () => {
                return isDeviceRightClickPopUpMenuVisible;
            }
        }));

        return isDeviceRightClickPopUpMenuVisible ? (
            <div
                ref={setMenuElement}
                style={{
                    ...styles.popper,
                    zIndex: 1000
                }}
                {...attributes.popper}
            >
                <Menu hasIcon>
                    {deviceType !== "virtual-network" ? (
                        <>
                            <MenuItem
                                name="add_connection"
                                text="Add Connection"
                                icon={<AddConnectionSvg />}
                                onClick={() => {
                                    addConnectionCallback(selectedNode);
                                    setIsDeviceRightClickPopUpMenuVisible(false);
                                }}
                            />
                            <MenuItem
                                name="edit_device"
                                text="Edit Connection"
                                icon={<EditConnectionSvg />}
                                onClick={() => {
                                    editConnectionCallback(selectedNode);
                                    setIsDeviceRightClickPopUpMenuVisible(false);
                                }}
                            />
                            <MenuItem
                                name="device_management"
                                text="Device Management"
                                icon={<DeviceManagementSvg />}
                                onClick={() => {
                                    deviceManagementCallback(selectedNode);
                                    setIsDeviceRightClickPopUpMenuVisible(false);
                                }}
                            />
                            <MenuItem
                                name="delete_device"
                                text="Delete Device"
                                icon={<DeleteDeviceSvg />}
                                onClick={() => {
                                    deleteNodeCallback(selectedNode);
                                    setIsDeviceRightClickPopUpMenuVisible(false);
                                }}
                            />
                        </>
                    ) : (
                        <MenuItem
                            name="delete_device"
                            text="Delete Device"
                            icon={<DeleteDeviceSvg />}
                            onClick={() => {
                                deleteNodeCallback(selectedNode);
                                setIsDeviceRightClickPopUpMenuVisible(false);
                            }}
                        />
                    )}
                </Menu>
            </div>
        ) : null;
    }
);

export default RightClickPopUpMenu;
