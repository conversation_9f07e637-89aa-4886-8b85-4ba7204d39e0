import {forwardRef, useImperativeHandle, useState, useRef, useEffect} from "react";
import {Modal, Form, Select, Input, Button, Flex, Space, Divider, message} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import SelectDeviceModal from "./select_device_modal";
import {addAssociateDevice} from "@/modules-ampcon/apis/dc_virtual_resource_api";
import deletePod from "@/modules-ampcon/pages/Topo/CampusFabric/resource/delete_pod.svg";
import "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/nodes_selector.scss";

const {Option} = Select;

const AssociateDeviceModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showAssociateDeviceModal: (vl2ID, nodeGroupOptions) => {
            setIsShowModal(true);
            setVl2ID(vl2ID);
            setNodeGroupOptions(nodeGroupOptions);
            setFormList([{id: 1}]);
            setSelectedDeviceMap({});
            form.resetFields();
        },
        hideAssociateDeviceModal: () => {
            setIsShowModal(false);
            setFormList([{id: 1}]);
            setSelectedDeviceMap({});
            form.resetFields();
        }
    }));
    const currentSelectingItemIdRef = useRef(null);
    const SelectDeviceModalRef = useRef(null);
    const [isShowModal, setIsShowModal] = useState(false);
    const [vl2ID, setVl2ID] = useState(null);
    const [form] = Form.useForm();

    const [formList, setFormList] = useState([{id: 1}]);
    const [nodeGroupOptions, setNodeGroupOptions] = useState([]);
    const [selectedDeviceMap, setSelectedDeviceMap] = useState({});

    const [usedPgIdMap, setUsedPgIdMap] = useState({});

    const addForm = () => {
        const newId = formList.length ? Math.max(...formList.map(f => f.id)) + 1 : 1;
        setFormList([...formList, {id: newId}]);
    };

    const removeForm = id => {
        if (formList.length <= 1) {
            message.error("At least one item must be retained.");
            return;
        }

        form.setFieldsValue({
            [`nodegroup_${id}`]: undefined,
            [`device_${id}`]: undefined,
            [`device_pg_ids_${id}`]: undefined
        });

        setSelectedDeviceMap(prev => {
            const newMap = {...prev};
            delete newMap[id];
            return newMap;
        });

        setFormList(formList.filter(item => item.id !== id));
    };

    const handleSave = async () => {
        try {
            const values = form.getFieldsValue();

            const emptyItems = formList.filter(item => {
                const pgIds = values[`device_pg_ids_${item.id}`];
                return !pgIds || pgIds.length === 0;
            });

            if (emptyItems.length > 0) {
                message.error("Please add at least one node group and device.");
                return;
            }

            const allPgIds = formList.flatMap(item => {
                const pgIds = values[`device_pg_ids_${item.id}`];
                return pgIds || [];
            });

            const res = await addAssociateDevice(vl2ID, allPgIds);

            if (res?.status === 200) {
                message.success("Devices associated successfully.");
                setIsShowModal(false);
                SelectDeviceModalRef.current?.refreshData?.();
                form.resetFields();
                if (props.saveCallback) {
                    props.saveCallback();
                }
            } else {
                message.error(`${res?.info}`);
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.message || error?.message || "Unexpected error occurred.";
            message.error(`Failed to associate devices: ${errorMsg}`);
        }
    };

    useEffect(() => {
        const map = {};
        formList.forEach(item => {
            const nodeGroupId = form.getFieldValue(`nodegroup_${item.id}`);
            const pgIds = selectedDeviceMap[item.id]?.pg_ids || [];

            if (!nodeGroupId) return;

            if (!map[nodeGroupId]) {
                map[nodeGroupId] = new Set();
            }

            pgIds.forEach(pgId => map[nodeGroupId].add(pgId));
        });

        // 转换 Set 为 Array
        const plainMap = {};
        Object.keys(map).forEach(key => {
            plainMap[key] = Array.from(map[key]);
        });

        setUsedPgIdMap(plainMap);
    }, [selectedDeviceMap, formList]);

    return (
        <div>
            <SelectDeviceModal
                ref={SelectDeviceModalRef}
                saveCallback={({pg_ids, pg_names}) => {
                    const currentItemId = currentSelectingItemIdRef.current;

                    setSelectedDeviceMap(prev => ({
                        ...prev,
                        [currentItemId]: {
                            pg_ids,
                            pg_names
                        }
                    }));

                    form.setFieldsValue({
                        [`device_${currentItemId}`]: pg_names.join(", "),
                        [`device_pg_ids_${currentItemId}`]: pg_ids
                    });
                }}
            />

            <Modal
                width={860}
                styles={{
                    body: {
                        minHeight: 500,
                        maxHeight: 800,
                        overflowY: "auto"
                    }
                }}
                title={
                    <div>
                        Associate Device
                        <Divider style={{width: 860, margin: "16px -24px 16px -24px"}} />
                    </div>
                }
                open={isShowModal}
                onCancel={() => {
                    setIsShowModal(false);
                    form.resetFields();
                    setFormList([{id: 1}]);
                    setSelectedDeviceMap({});
                    form.resetFields();
                }}
                footer={
                    <Flex vertical>
                        <Divider style={{width: 860, margin: "16px -24px 16px -24px"}} />
                        <Flex justify="flex-end">
                            <Button
                                onClick={() => {
                                    setIsShowModal(false);
                                    form.resetFields();
                                    setFormList([{id: 1}]);
                                    setSelectedDeviceMap({});
                                    form.resetFields();
                                }}
                            >
                                Cancel
                            </Button>
                            <Button type="primary" onClick={handleSave}>
                                Apply
                            </Button>
                        </Flex>
                    </Flex>
                }
            >
                <Form layout="vertical" form={form}>
                    {formList.map((item, index) => (
                        <div
                            key={item.id}
                            style={{
                                border: "1px dashed #dcdcdc",
                                borderRadius: 4,
                                padding: 16,
                                paddingTop: 8,
                                marginBottom: 16,
                                position: "relative"
                            }}
                        >
                            <div
                                style={{
                                    fontWeight: 600,
                                    fontSize: "18px",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "space-between"
                                }}
                            >
                                <span>Connect Bare Metal {index + 1}</span>
                                <Form.Item style={{marginTop: 0, marginBottom: 0}}>
                                    <Button
                                        className="imgSvg"
                                        type="text"
                                        style={{
                                            position: "absolute",
                                            top: 3,
                                            right: -7,
                                            backgroundColor: "transparent",
                                            visibility: formList.length > 1 ? "visible" : "hidden"
                                        }}
                                        icon={<img src={deletePod} alt="Delete Pod" />}
                                        onClick={() => removeForm(item.id)}
                                    />
                                </Form.Item>
                            </div>

                            <Space size="large" style={{display: "flex", height: "75px", marginTop: "-10px"}}>
                                <Form.Item
                                    label="Node Group"
                                    name={`nodegroup_${item.id}`}
                                    rules={[{required: true, message: "Please select node group"}]}
                                    style={{flex: 1}}
                                >
                                    <Select
                                        placeholder="Please select node group"
                                        style={{width: 280}}
                                        onChange={() => {
                                            form.setFieldsValue({
                                                [`device_${item.id}`]: undefined,
                                                [`device_pg_ids_${item.id}`]: undefined
                                            });
                                        }}
                                    >
                                        {nodeGroupOptions.map(item => (
                                            <Option key={item.id} value={item.id}>
                                                {item.nodegroup_name}
                                            </Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                                <Form.Item
                                    label="Device"
                                    name={`device_${item.id}`}
                                    rules={[{required: true, message: "Please select device"}]}
                                    style={{flex: 1}}
                                >
                                    <Input
                                        placeholder="Please select device"
                                        style={{width: 280}}
                                        readOnly
                                        title={form.getFieldValue(`device_${item.id}`) || "Please select device"}
                                        onClick={() => {
                                            currentSelectingItemIdRef.current = item.id;
                                            const nodeGroupId = form.getFieldValue(`nodegroup_${item.id}`);
                                            if (!nodeGroupId) {
                                                message.warning("Please select a node group first.");
                                                return;
                                            }

                                            const currentSelected = selectedDeviceMap?.[item.id]?.pg_ids || [];

                                            // 获取该 NodeGroup 中其他 item 使用过的 pgid
                                            const usedPgIds = usedPgIdMap?.[nodeGroupId] || [];
                                            const disabledPgIds = usedPgIds.filter(
                                                pgId => !currentSelected.includes(pgId)
                                            );

                                            SelectDeviceModalRef.current.showSelectDeviceModal(
                                                nodeGroupId,
                                                currentSelected,
                                                disabledPgIds
                                            );
                                        }}
                                    />
                                </Form.Item>
                                <Form.Item label=" " style={{marginTop: 16}}>
                                    <Button
                                        style={{width: 100}}
                                        onClick={() => {
                                            currentSelectingItemIdRef.current = item.id;
                                            const nodeGroupId = form.getFieldValue(`nodegroup_${item.id}`);
                                            if (!nodeGroupId) {
                                                message.warning("Please select a node group first.");
                                                return;
                                            }

                                            const currentSelected = selectedDeviceMap?.[item.id]?.pg_ids || [];

                                            // 获取该 NodeGroup 中其他 item 使用过的 pgid
                                            const usedPgIds = usedPgIdMap?.[nodeGroupId] || [];
                                            const disabledPgIds = usedPgIds.filter(
                                                pgId => !currentSelected.includes(pgId)
                                            );

                                            SelectDeviceModalRef.current.showSelectDeviceModal(
                                                nodeGroupId,
                                                currentSelected,
                                                disabledPgIds
                                            );
                                        }}
                                    >
                                        Select
                                    </Button>
                                </Form.Item>
                                <Form.Item name={`device_pg_ids_${item.id}`} hidden>
                                    <Input />
                                </Form.Item>
                            </Space>
                        </div>
                    ))}
                    <div className="hover-box" onClick={addForm}>
                        <PlusOutlined /> Associate Device
                    </div>
                </Form>
            </Modal>
        </div>
    );
});

export default AssociateDeviceModal;
