import React, {useRef} from "react";
import {
    AmpConCustomTable,
    createColumnConfig,
    createColumnConfigMultipleParams,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {Space, Card, Button, message} from "antd";
import Icon from "@ant-design/icons";
import {addSvg} from "@/utils/common/iconSvg";
import {fetchNetworkIAccessInfo, deleteNetworkAccess} from "@/modules-ampcon/apis/dc_virtual_resource_api";
import AddVL2Modal from "./add_VL2_modal";
import EditVl2Modal from "./edit_VL2_modal";
import {useNavigate} from "react-router-dom";

const NetworkAccessTableView = () => {
    const navigate = useNavigate();
    const addVL2ModalRef = useRef(null);
    const editVL2ModalRef = useRef(null);
    const tableRef = useRef();

    const configSearchFieldsList = ["network_name"];
    const configMatchFieldsList = [{name: "network_name", matchMode: "fuzzy"}];

    const deleteVL2Callback = record => {
        if (record.virtual_network_id !== undefined && record.virtual_network_id !== null) {
            message.error("This Network Access is associated with a virtual network and cannot be deleted.");
            return;
        }

        deleteNetworkAccess(record.id).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const networkAccesscolumns = [
        createColumnConfig("ID", "id", null, "", "8%"),
        createColumnConfig("Name", "network_name", null, "", "12%"),
        createColumnConfig("Fabric", "fabric_name", null, "", "12%"),
        createColumnConfig("PoD", "az_name", null, "", "12%"),
        createColumnConfig("VLAN", "vlan_id", null, "", "8%"),
        createColumnConfigMultipleParams({
            title: "User",
            dataIndex: "user",
            filterDropdownComponent: TableFilterDropdown,
            enableFilter: false,
            enableSorter: false,
            width: "10%"
        }),
        createColumnConfig("Auto-Generated VPC", "vpc_name", null, "", "12%"),
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            {/* <a onClick={() => createAssociateDeviceCallback(record)}>Associate Device</a> */}
                            <a
                                onClick={() => {
                                    navigate(`/service_provision/network_access/${record.network_name}`, {
                                        state: {record} // 传整个对象
                                    });
                                }}
                            >
                                Associate Device
                            </a>
                            <a
                                onClick={() => {
                                    editVL2ModalRef.current.showEditVL2Modal(record);
                                }}
                            >
                                Edit
                            </a>
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure you want to delete the VL2?", () => {
                                        deleteVL2Callback(record);
                                    })
                                }
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{margin: "8px 0 20px"}}>Network Access</h2>
            <AddVL2Modal
                ref={addVL2ModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
            <EditVl2Modal
                ref={editVL2ModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
            <AmpConCustomTable
                columns={networkAccesscolumns}
                searchFieldsList={configSearchFieldsList}
                matchFieldsList={configMatchFieldsList}
                ref={tableRef}
                fetchAPIInfo={fetchNetworkIAccessInfo}
                extraButton={
                    <Button
                        type="primary"
                        onClick={() => {
                            addVL2ModalRef.current.showAddVL2Modal();
                        }}
                    >
                        <Icon component={addSvg} />
                        VL2
                    </Button>
                }
            />
        </Card>
    );
};

export default NetworkAccessTableView;
