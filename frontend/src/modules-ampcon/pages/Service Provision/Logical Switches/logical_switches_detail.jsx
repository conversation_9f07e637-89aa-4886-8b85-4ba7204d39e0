import React, {useState, useEffect} from "react";
import {useNavigate, useLocation} from "react-router-dom";
import {ArrowLeftOutlined} from "@ant-design/icons";
import {Space, Tag, Card, Tabs, message} from "antd";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

import PrevFormTable from "@/modules-ampcon/components/prevform_table";
import {
    fetchLogicalSwitchPortDetail,
    fetchLogicalSwitchPodDetail,
    fetchLogicalSwitchStatusMonitor,
    deleteLogicalPort
} from "@/modules-ampcon/apis/dc_template_api";

import styles from "@/modules-ampcon/pages/Service Provision/Logical Routers/logical_router.module.scss";

const LogicalSwitchesDetail = ({showModal = false, ls_id = null, ls_info = null}) => {
    const navigate = useNavigate();
    const location = useLocation();
    const [id, setId] = useState(location?.state?.data?.id || ls_id || "");

    const [activeKey, setActiveKey] = useState("Device Detail");

    const deviceColumns = [
        {
            title: "ID",
            render: (_, record, index) => {
                return index + 1;
            }
        },
        {title: "VLAN", dataIndex: "vlan_id", sorter: (a, b) => a.vlan_id.localeCompare(b.vlan_id)},
        {
            title: "Connected Resource",
            dataIndex: "host_name",
            sorter: (a, b) => a.host_name.localeCompare(b.host_name),
            render: (_, record) => {
                return `${record.host_name}(${record.host_ip})`;
            }
        },
        {
            title: "Port Status",
            dataIndex: "status",
            sorter: (a, b) => a.status.localeCompare(b.status),
            render: (_, record) => {
                let tagClass;
                switch (record.status) {
                    case "Connect Successful":
                        tagClass = styles.successTag;
                        break;
                    case "Connect Failed":
                        tagClass = styles.failedTag;
                        break;
                    case "Conneting":
                        tagClass = styles.runningTag;
                        break;
                    case "Disconnecting":
                        tagClass = styles.runningTag;
                        break;
                    case "Disconnected":
                        tagClass = styles.pendingTag;
                        break;
                    case "Disconnect Failed":
                        tagClass = styles.failedTag;
                        break;
                    default:
                        tagClass = styles.pendingTag;
                        break;
                }
                return <Tag className={tagClass}>{record.status}</Tag>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    confirmModalAction("Do you want to delete?", () => {
                                        deleteSwitchPort(record);
                                    });
                                }}
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const searchPortList = ["host_name", "host_ip"];
    const matchPortList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "host_ip", matchMode: "fuzzy"}
    ];

    const podColumns = [
        {
            title: "ID",
            render: (_, record, index) => {
                return index + 1;
            }
        },
        {title: "PoD", dataIndex: "az", sorter: (a, b) => a.az.localeCompare(b.az)},
        {title: "VLAN Domain", dataIndex: "vlan_domain", sorter: (a, b) => a.vlan_domain.localeCompare(b.vlan_domain)},
        {title: "VTEP Device", dataIndex: "switch_sn", sorter: (a, b) => a.switch_sn.localeCompare(b.switch_sn)},
        {title: "Mgmt IP", dataIndex: "mgmt_ip", sorter: (a, b) => a.mgmt_ip.localeCompare(b.mgmt_ip)},
        {title: "VTEP IP", dataIndex: "vtep_id", sorter: (a, b) => a.vtep_id.localeCompare(b.vtep_id)}
    ];

    const searchPodList = ["az", "vlan_domain", "switch_sn", "mgmt_ip", "vtep_id"];
    const matchPodList = [
        {name: "az", matchMode: "fuzzy"},
        {name: "vlan_domain", matchMode: "fuzzy"},
        {name: "switch_sn", matchMode: "fuzzy"},
        {name: "mgmt_ip", matchMode: "fuzzy"},
        {name: "vtep_id", matchMode: "fuzzy"}
    ];

    const [prevDeviceForm, setPrevDeviceForm] = useState({
        name: "",
        status: ""
    });

    const monitorColumns = [
        {
            title: "ID",
            render: (_, record, index) => {
                return index + 1;
            }
        },
        {title: "VLAN Domain", dataIndex: "vlan_domain", sorter: (a, b) => a.vlan_domain.localeCompare(b.vlan_domain)},
        {title: "Leaf Switch", dataIndex: "switch_sn", sorter: (a, b) => a.switch_sn.localeCompare(b.switch_sn)},
        {title: "L2 VNI", dataIndex: "l2_vni", sorter: (a, b) => a.l2_vni - b.l2_vni},
        {title: "VLAN", dataIndex: "l2_vlan", sorter: (a, b) => a.vlan - b.vlan},
        {title: "RD", dataIndex: "rd", sorter: (a, b) => a.rd.localeCompare(b.rd)},
        {title: "RT", dataIndex: "rt", sorter: (a, b) => a.rt.localeCompare(b.rt)},
        {
            title: "Connected LR",
            dataIndex: "connected_lr",
            sorter: (a, b) => a.connected_lr.localeCompare(b.connected_lr)
        }
    ];

    const [prevMonitorForm, setPrevMonitorForm] = useState({
        name: "",
        status: ""
        // "ARP/ND Suppression": ""
    });

    const searchMonitorList = ["vlan_domain"];
    const matchMonitorList = [{name: "vlan_domain", matchMode: "fuzzy"}];

    const items = [
        {
            key: "Device Detail",
            label: "Device Detail",
            children: (
                <>
                    <PrevFormTable
                        title="Logical Port Detail"
                        prevForm={prevDeviceForm}
                        columns={deviceColumns}
                        fetchApi={fetchLogicalSwitchPortDetail}
                        fetchAPIParams={[id]}
                        searchFieldsList={searchPortList}
                        matchFieldsList={matchPortList}
                    />
                    <PrevFormTable
                        title="PoD Detail"
                        columns={podColumns}
                        fetchApi={fetchLogicalSwitchPodDetail}
                        fetchAPIParams={[id]}
                        searchFieldsList={searchPodList}
                        matchFieldsList={matchPodList}
                    />
                </>
            )
        },
        {
            key: "Status Monitor",
            label: "Status Monitor",
            children: (
                <PrevFormTable
                    title="Logical Port Detail"
                    prevForm={prevMonitorForm}
                    columns={monitorColumns}
                    fetchApi={fetchLogicalSwitchStatusMonitor}
                    fetchAPIParams={[id]}
                    searchFieldsList={searchMonitorList}
                    matchFieldsList={matchMonitorList}
                />
            )
        }
    ];

    const deleteSwitchPort = async record => {
        const data = {
            id: record.port_id,
            link_id: record.link_id,
            type: record.type
        };
        const res = await deleteLogicalPort(data);
        if (res.status === 200) {
            message.success("Delete port task start...");
        } else {
            message.error(`Failed to delete: ${res.info}`);
        }
    };

    const fetchPrevDeviceForm = async () => {
        const prevData = showModal ? ls_info : location?.state?.data;
        if (prevData) {
            let status;
            if (prevData.status === 0) status = ["Create", "Normal"];
            if (prevData.status === 1) status = ["Create", "Error"];
            if (prevData.status === 2) status = ["Delete", "Normal"];
            if (prevData.status === 3) status = ["Delete", "Error"];
            setPrevDeviceForm({
                name: prevData.name,
                status
            });
            setPrevMonitorForm({
                name: prevData.name,
                status
                // "ARP/ND Suppression": prevData.arp_nd_suppress ? "Enable" : "Disable"
            });
        }
    };

    useEffect(() => {
        fetchPrevDeviceForm();
    }, []);

    return !showModal ? (
        <Card style={{display: "flex", flex: 1, paddingTop: "12px"}}>
            {/* <p
                className={styles.goBack}
                onClick={() => navigate("/service_provision/logical_switches")}
                style={{marginBottom: "23px"}}
            >
                <ArrowLeftOutlined style={{marginRight: "8px"}} />
                <span>Back</span>
            </p> */}
            <div className={styles.logicalTab}>
                <Tabs onChange={setActiveKey} activeKey={activeKey} items={items} />
            </div>
        </Card>
    ) : (
        <div className={styles.inModalLogicalTab}>
            <Tabs onChange={setActiveKey} activeKey={activeKey} items={items} />
        </div>
    );
};

export default LogicalSwitchesDetail;
