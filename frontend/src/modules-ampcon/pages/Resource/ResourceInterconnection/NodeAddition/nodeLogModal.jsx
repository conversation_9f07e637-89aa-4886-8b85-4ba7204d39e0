import React, {useRef, useEffect, useState, forwardRef} from "react";
import {useLocation, useNavigate} from "react-router-dom";
import {Row, Space, Flex, Tag, message, Input, Table, Modal, Divider, Button} from "antd";
import Icon, {SearchOutlined} from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";
import {useTableInitialElement} from "@/modules-ampcon/hooks/useModalTable";
import {
    TableFilterDropdown,
    handleTableChange,
    createMatchMode,
    createColumnConfig,
    GlobalSearchInput,
    createFilterFields
} from "@/modules-ampcon/components/custom_table";
// import API
import {NodeLogInfo} from "@/modules-ampcon/apis/node_addition_api";
/**
 * Custom Node Template
 */
const NodeLogModal = ({title, nodeType, nodesInfo, isNodeLogModal, onCancel}) => {
    const [chooseLogInfo, setChooseLogInfo] = useState("");
    const [viewLogModal, setViewLogModal] = useState(false);

    const [
        isModalOpen,
        setIsModalOpen,
        searchFields,
        setSearchFields,
        data,
        setData,
        loading,
        setLoading,
        pagination,
        setPagination
    ] = useTableInitialElement([], true);
    const [groups, setGroups] = useState([]);
    const [sorter, setSorter] = useState({});
    const [filters, setFilters] = useState({});

    const nodeInfoColumns = [
        {...createColumnConfig("Sysname", "sysname")},
        {...createColumnConfig("Mgmt IP", "mgmt_ip")},
        {...createColumnConfig("Device Type", "type")},
        {
            ...createColumnConfig("Configuration Status", "status"),
            render: (text, record) => {
                const statusClassMap = {
                    "Not Deployed": styles.notDeployedTag,
                    Deploying: styles.runningTag,
                    Deployed: styles.successTag,
                    "Deploy Failed": styles.failedTag,
                    Deleting: styles.runningTag,
                    "Delete Failed": styles.failedTag
                };
                return <Tag className={statusClassMap[record?.status] || styles.uncheckedTag}>{record?.status}</Tag>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="middle" className={styles.actionLink}>
                    <a
                        onClick={() => {
                            setChooseLogInfo(record);
                            setViewLogModal(true);
                        }}
                    >
                        Log
                    </a>
                </Space>
            )
        }
    ];
    const checkSortedColumn = columns => {
        for (const columnKey in columns) {
            if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                const columnConfig = columns[columnKey];
                if (columnConfig.defaultSortOrder !== null) {
                    return [columnConfig.dataIndex, columnConfig.defaultSortOrder];
                }
            }
        }
        return [undefined, undefined];
    };

    const matchModes = createMatchMode([
        {name: "sysname", matchMode: "exact"},
        {name: "mgmt_ip", matchMode: "fuzzy"},
        {name: "type", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"}
    ]);

    const handleSearchChange = e => {
        setSearchFields({
            fields: ["sysname", "mgmt_ip", "type", "status"],
            value: e.target.value
        });
    };

    const tableChange = async (pagination, filters, sorter) => {
        setSorter(sorter);
        setFilters(filters);
        await handleTableChange(
            pagination,
            filters,
            sorter,
            setPagination,
            searchFields,
            fetchNodeLogData,
            "",
            setData,
            matchModes,
            setLoading
        );
    };

    const fetchNodeLogData = async () => {
        setLoading(true);
        const filterFields = filters ? createFilterFields(filters, matchModes) : [];
        const sortFields = [];
        if (sorter.field && sorter.order) {
            sortFields.push({
                field: sorter.field,
                order: sorter.order === "ascend" ? "asc" : "desc"
            });
        }

        try {
            const response = await NodeLogInfo(
                {
                    id: nodesInfo.id,
                    type: nodeType
                },
                pagination.current,
                pagination.pageSize,
                filterFields,
                sortFields,
                searchFields
            );

            setGroups(response.allGroups);
            setData(response.data);
            setPagination(prev => ({
                ...prev,
                total: response.total,
                current: response.page,
                pageSize: response.pageSize
            }));
        } catch (error) {
            // error
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchNodeLogData().then();
    }, [searchFields]);

    useEffect(() => {
        if (isNodeLogModal) {
            fetchNodeLogData().then(() => {
                const [sortedColumn, sortedOrder] = checkSortedColumn(nodeInfoColumns);
                if (sortedColumn) {
                    sorter.field = sortedColumn;
                    sorter.order = sortedOrder;
                    tableChange("", "", sorter);
                }
            });
        }
    }, [nodesInfo, isNodeLogModal]);

    return (
        <Space>
            <Modal
                className="ampcon-max-modal"
                title={
                    <div>
                        {title}_Log
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={isNodeLogModal}
                onCancel={onCancel}
                footer={
                    <>
                        <Divider style={{marginBottom: "20px", marginTop: "0px"}} />
                        <Row justify="end">
                            <Space>
                                <Button onClick={onCancel}> Cancel </Button>
                            </Space>
                        </Row>
                    </>
                }
            >
                <div style={{height: 32, marginBottom: 24}}>
                    <GlobalSearchInput onChange={handleSearchChange} />
                </div>
                <Table
                    bordered
                    loading={loading}
                    columns={nodeInfoColumns}
                    dataSource={data}
                    onChange={tableChange}
                    pagination={{
                        showSizeChanger: true,
                        showTotal: (total, range) => {
                            const start = range[0];
                            const end = range[1];
                            return `${start}-${end} of ${total} items`;
                        },
                        total: data?.length
                    }}
                />

                <LogViewModal
                    title="Logs"
                    chooseLogInfo={chooseLogInfo}
                    viewLogModal={viewLogModal}
                    onCancel={() => setViewLogModal(false)}
                    modalClass="ampcon-middle-modal"
                />
            </Modal>
        </Space>
    );
};
export default NodeLogModal;

const LogViewModal = ({chooseLogInfo, viewLogModal, onCancel}) => {
    const readonlyStyle = {
        minHeight: "330px",
        height: "58vh",
        resize: "vertical",
        // border: "1px solid rgb(217, 217, 217)",
        border: "none",
        backgroundColor: "#F8FAFB",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        maxHeight: "calc(100vh - 500px)"
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <>
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        {`${chooseLogInfo.sysname} logs`}
                        <Button type="text" className="ant-modal-close" style={{marginRight: "30px"}} />
                    </div>
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </>
            }
            open={viewLogModal}
            onCancel={onCancel}
            footer={null}
        >
            <Flex vertical style={{flex: 1}}>
                <Input.TextArea style={readonlyStyle} value={chooseLogInfo.log} rows={19} readOnly />
            </Flex>
        </Modal>
    );
};
