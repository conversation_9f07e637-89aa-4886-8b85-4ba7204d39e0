import React, {useEffect, useState, useRef, forwardRef} from "react";
import {useLocation, useNavigate} from "react-router-dom";
import {Card, Space, Menu, message, Table, Form, Input, InputNumber, Select, Radio, Button} from "antd";
import Icon, {PlusOutlined, Arrow<PERSON>eftOutlined, EditOutlined, DeleteOutlined} from "@ant-design/icons";
import EditGreySvg from "@/modules-ampcon/pages/PhysicalNetwork/resource/editGrey.svg?react";
import EditGreyHover from "@/modules-ampcon/pages/PhysicalNetwork/resource/editGreyHover.svg?react";
import DeleteSvg from "@/modules-ampcon/pages/PhysicalNetwork/resource/delete.svg?react";
import DeleteGreyHover from "@/modules-ampcon/pages/PhysicalNetwork/resource/deleteGreyHover.svg?react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";
import SelectSwitchPortModal from "@/modules-ampcon/pages/Resource/ResourceInterconnection/NodeAddition/selectSwitchPortModal";
import CustomNodeTemplateModal from "@/modules-ampcon/pages/Resource/ResourceInterconnection/NodeAddition/customNodeTemplateModal";

// import API
import {
    fetchFabricInfo,
    getNodeTemplateList,
    fetchPODInfo,
    list_vd_dropdown_data,
    save_node_group,
    delete_custom_node,
    save_custom_node
} from "@/modules-ampcon/apis/node_addition_api";
import {viewFabric} from "@/modules-ampcon/apis/dc_template_api";
import FabricTopo from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_topo";

/**
 * Create Fabric Modal
 */
const CreateBareMetalNodeAddition = forwardRef(() => {
    const {state} = useLocation();
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const [nodeInfoCopy, setNodeInfoCopy] = useState({});
    const NAME_MATCH_REGEX = /^[\s\w:-]+$/;
    const NAME_MATCH_REGEX2 = /^[\s\w-]+$/;
    const [topoInfo, setTopoInfo] = useState({});

    const [isNodeCountDisabled, setIsNodeCountDisabled] = useState(true);
    const [isNodeTemplateDisabled, setIsNodeTemplateDisabled] = useState(true);

    // Switch Port Group组
    const [editNodeInfoCopy, setEditNodeInfoCopy] = useState({});
    const [switchGroupNumLimit, setSwitchGroupNumLimit] = useState(1);
    const [switch_portgroup, setSwitchPortgroupList] = useState({});
    const [isSelectSwitchPortModal, setIsSelectSwitchPortModal] = useState(false);
    const [isCustomNodeTemplateModal, setIsCustomNodeTemplateModal] = useState(false);
    const [nicPortGroupList, setNicPortGroupList] = useState([]);
    const [portgroup_info, setPortgroup_info] = useState([]);
    const [nodeDetails, setNodeDetails] = useState({});

    const [deleteSVGHover, setDeleteSVGHover] = useState(false);

    /**
     * 获取Fabric列表
     */
    const [fabricList, setFabricList] = useState([]);
    const getFabricList = async () => {
        const result = await fetchFabricInfo();
        if (result.status === 200) {
            setFabricList(result.data);
        } else message.error(result.info);
    };

    // 获取topoInfo信息
    const getTopoData = async e => {
        const fabric_topo_item = fabricList.find(obj => obj.id === e) || {};
        const res = await viewFabric({fabric_topo_id: fabric_topo_item?.fabric_topo_id});
        if (res.status === 200) {
            setTopoInfo(res.data);
        } else message.error(res.info);
    };

    /**
     * 获取Node Templates列表
     */
    const [customNodeList, setCustomNodeList] = useState([]);
    const getCustomNode = async () => {
        setCustomNodeList([]);
        const result = await getNodeTemplateList();
        if (result.status === 200) {
            setCustomNodeList(result.data);
        } else message.error(result.info);
    };
    const [visible, setVisible] = useState(false);
    const [editHoverStates, setEditHoverStates] = useState({});
    const [deleteHoverStates, setDeleteHoverStates] = useState({});
    const [selectedNode, setSelectedNode] = useState(null);
    const [selectedNodeCopy, setSelectedNodeCopy] = useState(null);

    // 处理图标悬停状态的函数
    const handleIconHover = (itemId, iconType, isHovered) => {
        if (iconType === "edit") {
            setEditHoverStates(prev => ({
                ...prev,
                [itemId]: isHovered
            }));
        } else if (iconType === "delete") {
            setDeleteHoverStates(prev => ({
                ...prev,
                [itemId]: isHovered
            }));
        }
    };

    const deleteCustomNode = async id => {
        try {
            const res = await delete_custom_node({id});
            if (res.status === 200) {
                message.success(res.info);
                getCustomNode();
                const node_template_name = form.getFieldValue("node_template_name");
                const currentCustomNode = customNodeList.find(obj => obj.id === id);
                if (currentCustomNode.template_name === node_template_name) {
                    form.setFieldsValue({node_template_name: ""});
                    form.setFieldsValue({node_template_id: ""});
                    setSelectedNode({});
                    setSelectedNodeCopy({});
                    resetNICData();
                }
            } else {
                message.error(res.info);
            }
        } catch (error) {
            // error
        }
    };

    const nodeTemplateDropdownMenu = (
        <Menu style={{width: 280, maxHeight: 300, overflowY: "auto", marginLeft: "-3px"}}>
            <Menu.Item
                key="icon-display"
                disabled
                style={{height: 60, padding: "8px", marginLeft: "-6px !important", borderBottom: "1px solid #F2F2F2"}}
            >
                <ul className={styles.portLegend}>
                    <li>
                        <div className={styles.legend_1G} />
                        <span>1G</span>
                    </li>
                    <li>
                        <div className={styles.legend_10G} />
                        <span>10G</span>
                    </li>
                    <li>
                        <div className={styles.legend_25G} />
                        <span>25G</span>
                    </li>
                    <li>
                        <div className={styles.legend_40G} />
                        <span>40G</span>
                    </li>
                    <li>
                        <div className={styles.legend_100G} />
                        <span>100G</span>
                    </li>
                    <li>
                        <div className={styles.legend_200G} />
                        <span>200G</span>
                    </li>
                    <li>
                        <div className={styles.legend_400G} />
                        <span>400G</span>
                    </li>
                </ul>
            </Menu.Item>
            {customNodeList?.map(item => (
                <Menu.Item key={item.id} onClick={() => selectCustomNode(item.id)} className={styles.NodeTemplateItem}>
                    <div>
                        <ul>
                            <li style={{height: 60}}>
                                <p>Node Template Name</p>
                                <p> {item.template_name}</p>
                            </li>
                            <li style={{height: 60, marginBottom: 8}}>
                                <p>Total Port Group Number</p>
                                <p>{Object.keys(item.template_info).length || 0}</p>
                            </li>
                            <li style={{marginBottom: 0}}>
                                {(() => {
                                    //  当 total_ports 为空或无效时，不渲染
                                    if (!item.total_ports || item.total_ports < 1) return null;

                                    // 封装生成端口元素的逻辑
                                    const generatePortElements = () => {
                                        const groups = Object.values(item.template_info);
                                        const portElements = [];
                                        let portIndex = 0;

                                        groups.forEach(group => {
                                            const {port_num = 0, speed = 1} = group;
                                            const legendClass = `legend_${speed}G`;
                                            for (let i = 0; i < port_num; i++) {
                                                if (portIndex < item.total_ports) {
                                                    // 使用最新的 item.total_ports 进行判断
                                                    portElements.push(
                                                        <li key={portIndex}>
                                                            <div
                                                                className={`${styles.legend_default} ${styles[legendClass]}`}
                                                            >
                                                                {portIndex + 1}
                                                            </div>
                                                        </li>
                                                    );
                                                    portIndex++;
                                                }
                                            }
                                            if (portIndex < item.total_ports) {
                                                portElements.push(
                                                    <hr
                                                        key={`divider-${portIndex}`}
                                                        style={{
                                                            width: 1,
                                                            height: 24,
                                                            background: "#B2B2B2",
                                                            margin: "0 3px"
                                                        }}
                                                    />
                                                );
                                            }
                                        });

                                        // 填充剩余端口
                                        while (portIndex < item.total_ports) {
                                            portElements.push(
                                                <li key={portIndex}>
                                                    <div className={styles.legend_default}>{portIndex + 1}</div>
                                                </li>
                                            );
                                            portIndex++;
                                        }

                                        return portElements;
                                    };

                                    return (
                                        <ul
                                            className={`${styles.portLegend} ${styles.CustomNodeModal}`}
                                            style={{minHeight: 20}}
                                        >
                                            {generatePortElements()}
                                        </ul>
                                    );
                                })()}
                            </li>
                        </ul>
                        <div className={styles.NodeTemplateItemActive}>
                            <Icon
                                component={editHoverStates[item.id] ? EditGreyHover : EditGreySvg}
                                onMouseEnter={() => handleIconHover(item.id, "edit", true)}
                                onMouseLeave={() => handleIconHover(item.id, "edit", false)}
                                className={styles.EditGreySvg}
                                style={{marginLeft: 8, cursor: "pointer"}}
                                onClick={e => {
                                    e.stopPropagation();
                                    selectCustomNode(item.id);
                                    setCustomNodeTemplateModalTitle("Edit Node Template");
                                    setIsCustomNodeTemplateModal(true);
                                }}
                            />
                            {!item.is_used && (
                                <Icon
                                    component={deleteHoverStates[item.id] ? DeleteGreyHover : DeleteSvg}
                                    onMouseEnter={() => handleIconHover(item.id, "delete", true)}
                                    onMouseLeave={() => handleIconHover(item.id, "delete", false)}
                                    className={styles.deleteSvg}
                                    style={{marginLeft: 8, cursor: "pointer"}}
                                    onClick={e => {
                                        e.stopPropagation();
                                        confirmModalAction("Are you sure you want to delete the node template?", () =>
                                            deleteCustomNode(item.id)
                                        );
                                    }}
                                />
                            )}
                        </div>
                    </div>
                </Menu.Item>
            ))}
            <Menu.Item disabled style={{cursor: "pointer", textAlign: "center", padding: 0}}>
                <div
                    className={styles.CustomNodeTemplate}
                    onClick={() => {
                        setCustomNodeTemplateModalTitle("Custom Node Template");
                        setIsCustomNodeTemplateModal(true);
                        setCurrentCustomNodeTemplate({});
                    }}
                >
                    <Icon component={PlusOutlined} style={{marginRight: 8, cursor: "pointer"}} />
                    Custom Node Template
                </div>
            </Menu.Item>
        </Menu>
    );

    /**
     * 获取POD列表
     */
    const [podList, setPodList] = useState([]);
    const getPodList = async e => {
        const result = await fetchPODInfo({type: "BareMetal", fabric_id: e});
        if (result.status === 200) {
            setPodList(result.data);
        } else message.error(result.info);
    };

    const changeFabric = () => {
        // 1. 重置 PoD 选择
        form.setFieldsValue({az_id: ""});
        // 2. 重置 switch_portgroup 中与 VLAN Domain 相关的字段
        const currentSwitchPortgroups = form.getFieldValue("switch_portgroup") || [];
        const cleanedSwitchPortgroups = currentSwitchPortgroups.map(group => ({
            ...group,
            vlan_domain_id: "",
            vdSwitchList: [],
            hostname: "",
            peer_leaf: "",
            link_type: "",
            portgroup_info: []
        }));

        // 更新表单中的 switch_portgroup
        form.setFieldsValue({
            switch_portgroup: cleanedSwitchPortgroups
        });

        // 3. 同步更新 switch_portgroup 状态
        Object.keys(switch_portgroup).forEach(key => {
            const group = switch_portgroup[key];
            switch_portgroup[key] = {
                ...group,
                vlan_domain_id: "",
                vdSwitchList: [],
                hostname: "",
                peer_leaf: "",
                link_type: "",
                portgroup_info: []
            };
        });
        setSwitchPortgroupList({...switch_portgroup});
    };

    /**
     * 创建 node group 表格队列
     */
    const nodeTableColumns = [
        {title: "Node Name", dataIndex: "host_name", key: "host_name"},
        {
            title: "IP",
            dataIndex: "ip_addr",
            key: "ip_addr",
            render: (text, record, key) => {
                return (
                    <Form.Item
                        name={`ip_${key}`}
                        style={{margin: 0}}
                        rules={[
                            {
                                required: true,
                                validator: async (_, value) => {
                                    if (!value || value.trim() === "") {
                                        return Promise.reject(new Error("Please enter the IP!"));
                                    }
                                    const ipPattern =
                                        /^(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/;
                                    if (!ipPattern.test(value)) {
                                        return Promise.reject(new Error("Invalid IP!"));
                                    }
                                    const repeatIP = nodeHostList.filter(
                                        (item, index) => item.ip_addr === value && index !== key
                                    );
                                    if (repeatIP.length > 0) {
                                        return Promise.reject(new Error("IP cannot be repeated."));
                                    }
                                    return Promise.resolve();
                                }
                            }
                        ]}
                        shouldUpdate
                    >
                        <Input
                            style={{width: 100}}
                            placeholder="x.x.x.x"
                            disabled={state?.actionType === "Edit"}
                            onChange={e => {
                                record.ip_addr = e.target.value;
                            }}
                        />
                    </Form.Item>
                );
            }
        },
        {
            title: "User",
            dataIndex: "username",
            key: "username",
            render: (text, record, key) => {
                return (
                    <Form.Item name={`username_${key}`} style={{margin: 0}}>
                        <Input
                            style={{width: 100}}
                            maxLength="64"
                            onChange={e => {
                                record.username = e.target.value;
                            }}
                        />
                    </Form.Item>
                );
            }
        },
        {
            title: "Password",
            dataIndex: "password",
            key: "password",
            render: (text, record, key) => {
                return (
                    <Form.Item name={`password_${key}`} style={{margin: 0}}>
                        <Input.Password
                            style={{width: 100}}
                            maxLength="64"
                            onChange={e => {
                                record.password = e.target.value;
                            }}
                        />
                    </Form.Item>
                );
            }
        },
        {title: "Total Number", dataIndex: "total_number", key: "total_number"},
        {title: "NIC Port Groups", dataIndex: "nic_port_groups", key: "nic_port_groups"}
    ];

    /**
     * Node Count、Node Group Name变更时，node group表格数据条数同步变化
     * @param {*} e
     */
    const [nodeHostList, setNodeList] = useState([]);
    const updateNodeGroupTable = (name, e) => {
        const formData = form.getFieldValue();
        switch (name) {
            case "nodegroup_name":
                setNodeList(nodeHostList.map((item, index) => ({...item, host_name: `${e}_${index + 1}`})));
                break;

            case "node_count":
                const currentLength = nodeHostList.length;
                if (e === currentLength) {
                    return;
                }
                const currentCustomNode = customNodeList.find(obj => obj.id === formData.node_template_id);
                if (e > currentLength) {
                    const newNodeGroups = Array.from({length: e - currentLength}, (_, i) => ({
                        host_name: `${form.getFieldsValue().nodegroup_name || ""}_${currentLength + i + 1}`,
                        ip_addr: "",
                        username: "",
                        password: "",
                        total_number: currentCustomNode?.total_ports || "",
                        nic_port_groups: currentCustomNode
                            ? Object.keys(currentCustomNode?.template_info).join(", ")
                            : ""
                    }));
                    const newNodeHostList = newNodeGroups.map((item, index) => ({
                        ...item,
                        ip_addr: formData[`ip_${index}`],
                        username: formData[`username_${index}`],
                        password: formData[`password_${index}`]
                    }));
                    setNodeList(prevData => [...prevData, ...newNodeHostList]);
                } else {
                    setNodeList(prevData => prevData.slice(0, e));
                }
                break;

            default:
                break;
        }
    };

    /**
     * Node Count变更时，清空Select Switch Ports
     * @param {*} e
     */
    const updateSelectSwitchPort = () => {
        const formData = form.getFieldValue();
        if (!formData.switch_portgroup) return;
        const updatedSwitchPortgroup = formData?.switch_portgroup?.map(group => {
            const updatedPortgroupInfo = group?.portgroup_info?.map(info => ({
                ...info,
                port_list: [],
                disable_port_list: []
            }));

            return {
                ...group,
                portgroup_info: updatedPortgroupInfo
            };
        });
        form.setFieldsValue({
            ...formData,
            switch_portgroup: updatedSwitchPortgroup
        });
    };

    /**
     * 选择 Node Templates
     */
    const [customNodeTemplateModalTitle, setCustomNodeTemplateModalTitle] = useState(null);

    const [currentCustomNodeTemplate, setCurrentCustomNodeTemplate] = useState(null);
    const selectCustomNode = e => {
        const currentCustomNode = customNodeList?.find(obj => obj.id === e);
        form.setFieldsValue({node_template_name: currentCustomNode?.template_name || ""});
        form.setFieldsValue({node_template_id: e || ""});
        if (!currentCustomNode) return;

        setSelectedNode(currentCustomNode);
        setSelectedNodeCopy(currentCustomNode);
        setCurrentCustomNodeTemplate(currentCustomNode);
        setVisible(false);
        // 更新Total Number、NIC Port Groups信息
        const nicPortGroups = Object.keys(currentCustomNode.template_info).join(", ");
        form.setFieldsValue({node_template_name: currentCustomNode.template_name});
        const totalNumber = currentCustomNode.total_ports;
        setSwitchGroupNumLimit(totalNumber);
        setNodeList(prevData => {
            return prevData.map((item, index) => ({
                ...item,
                nic_port_groups: nicPortGroups,
                total_number: totalNumber
            }));
        });
        // 更新NIC Port Group下拉信息
        setNicPortGroupList([]);
        const templateArray =
            currentCustomNode && currentCustomNode.template_info
                ? Object.entries(currentCustomNode.template_info).map(([nic_portgroup_name, details]) => ({
                      nic_portgroup_name,
                      ...details
                  }))
                : [];
        setNicPortGroupList(templateArray);
        // 更新Switch Port Group中的NIC Port Group、Physical Link Count per Leaf Switch及其他相关信息
        if (state.actionType !== "Edit" && selectedNodeCopy?.id !== e) resetNICData();
    };

    const resetNICData = () => {
        const currentSwitchPortGroups = form.getFieldValue("switch_portgroup") || [];
        const resetSwitchPortGroups = currentSwitchPortGroups.map(item => ({
            ...item,
            nic_portgroup_name: "",
            nicSelectPortGroupNum: 0,
            link_count: "",
            nicPortUpNum: 0
        }));
        form.setFieldsValue({switch_portgroup: resetSwitchPortGroups});
        resetSwitchPortGroups.forEach((item, index) => {
            setSwitchPortgroupList(prev => ({
                ...prev,
                [`link_item${index}`]: {...prev[`link_item${index}`], ...item}
            }));
        });
    };

    const saveCustomNodeTemplate = async data => {
        try {
            const res = await save_custom_node(data);
            if (res.status === 200) {
                message.success(res.info);
                setIsCustomNodeTemplateModal(false);
                getCustomNode();
            } else {
                message.error(res.info);
            }
        } catch (error) {
            // error
        }
    };
    /**
     * 获取VLAN Domain列表
     */
    const [vlanDomainList, setVlanDomainList] = useState([]);
    const getVlanDomain = async e => {
        setVlanDomainList([]);
        const result = await list_vd_dropdown_data({fabric_id: e});
        if (result.status === 200) {
            setVlanDomainList(result.data);
        } else message.error(result.info);
    };

    const selectNICPortTableColumns = [
        {
            title: "VLAN Domain",
            dataIndex: "vlan_domain_name",
            key: "vlan_domain_name",
            render: (_, record) => {
                const vlanDomain = vlanDomainList.find(domain =>
                    domain.switch_info.some(device => device.logic_device_id === record.logic_device_id)
                );
                return <span>{vlanDomain?.name || "--"}</span>;
            }
        },
        {
            title: "Sysname",
            dataIndex: "hostname",
            key: "hostname",
            render: (_, record) => {
                const device = vlanDomainList
                    .flatMap(d => d.switch_info)
                    .find(item => item.logic_device_id === record.logic_device_id);
                return <span>{device?.hostname || "--"}</span>;
            }
        },
        {
            title: "Port",
            render: (_, record) => {
                return (
                    <div>
                        <div>
                            <Space size="large" className="actionLink">
                                <a
                                    onClick={() => {
                                        setPortgroup_info(record);
                                        setNodeDetails(form.getFieldValue());
                                        setIsSelectSwitchPortModal(true);
                                    }}
                                >
                                    Select Switch Ports
                                </a>
                            </Space>
                        </div>
                    </div>
                );
            }
        }
    ];

    // 更新 Switch Port Group组数据
    const handleLinkItemChange = (name, value, key, nameIndex) => {
        // currentHostLink1当前修改的Switch Port Group对象
        const hostLinkData = form.getFieldValue("switch_portgroup") || [];
        const currentHostLink = {...switch_portgroup[`link_item${key}`]};
        let currentSwitchCopy = {};
        if (editNodeInfoCopy && editNodeInfoCopy?.switch_portgroup?.length > 0) {
            currentSwitchCopy = editNodeInfoCopy?.switch_portgroup?.find(
                obj => obj.portgroup_name === currentHostLink.portgroup_name
            );
        }
        switch (name) {
            case "portgroup_name":
                currentHostLink.portgroup_name = value;
                break;
            case "vlan_domain_id":
                // chooseSwitch:当前VD下的switch list
                const chooseSwitch = vlanDomainList?.find(obj => obj.id === value) || [];
                currentHostLink.vlan_domain_id = value;
                currentHostLink.vdSwitchList = chooseSwitch?.switch_info;
                currentHostLink.hostname = chooseSwitch?.switch_info?.map(device => device.hostname).join(",");
                currentHostLink.link_type = chooseSwitch.switch_info.length >= 2 ? "MLAG Leaf" : "Single leaf"; // MLAG Leaf || Single leaf
                currentHostLink.access_mlag_mode = "Single-Homed";
                currentHostLink.peer_leaf = chooseSwitch?.switch_info[0]?.logic_device_id;
                currentHostLink.connect_mode = currentHostLink.connect_mode ? currentHostLink.connect_mode : "access";

                currentHostLink.portgroup_info = [];
                const newPortObj = {
                    logic_device_id: chooseSwitch?.switch_info[0].logic_device_id,
                    switch_sn: chooseSwitch?.switch_info[0].sn,
                    disable_port_list: [],
                    port_list: [],
                    nic_port_list: []
                };
                currentHostLink.portgroup_info.push(newPortObj);
                break;

            // Physical link count per individual switch
            case "nic_portgroup_name":
                const nicSelectPortGroupNum =
                    nicPortGroupList.find(obj => obj.nic_portgroup_name === value)?.port_num || 0;
                currentHostLink.nicSelectPortGroupNum = nicSelectPortGroupNum;
                currentHostLink.nic_portgroup_name = value;
                currentHostLink.link_count = 1;
                if (nicSelectPortGroupNum === 1) {
                    currentHostLink.access_mlag_mode = "Single-Homed"; // Single-Homed || Dual-Homed
                }
                currentHostLink.nicPortUpNum = currentHostLink.link_count * 1;
                if (currentHostLink.link_type === "MLAG Leaf") {
                    currentHostLink.nicPortUpNum =
                        currentHostLink.link_count * (currentHostLink.access_mlag_mode === "Single-Homed" ? 1 : 2);
                }
                break;

            // Access MLAG Mode
            case "access_mlag_mode":
                currentHostLink.access_mlag_mode = value; // Single-Homed || Dual-Homed
                if (value === "Dual-Homed") {
                    currentHostLink.portgroup_info = [];
                    currentHostLink.portgroup_info = currentHostLink?.vdSwitchList?.map((device, index) => ({
                        logic_device_id: device.logic_device_id,
                        switch_sn: device.sn,
                        disable_port_list:
                            currentSwitchCopy?.portgroup_info?.find(obj => obj.switch_sn === device.sn)?.port_info
                                ?.disable_port_list || [],
                        port_list:
                            currentSwitchCopy?.portgroup_info?.find(obj => obj.switch_sn === device.sn)?.port_info
                                ?.port_list || [],
                        nic_port_list:
                            currentSwitchCopy?.portgroup_info?.find(obj => obj.switch_sn === device.sn)?.port_info
                                ?.nic_port_list || []
                    }));
                    if (
                        currentHostLink?.link_count &&
                        currentHostLink.link_count * 2 > currentHostLink?.nicSelectPortGroupNum
                    ) {
                        currentHostLink.link_count = Math.floor(currentHostLink.nicSelectPortGroupNum / 2);
                    }
                } else {
                    currentHostLink.portgroup_info = [];
                    const currentPeerLeafInfo = currentHostLink.vdSwitchList.find(
                        obj => obj.logic_device_id === currentHostLink.peer_leaf
                    );
                    const newPortObj = {
                        logic_device_id: currentPeerLeafInfo?.logic_device_id,
                        switch_sn: currentPeerLeafInfo?.sn,
                        disable_port_list:
                            currentSwitchCopy?.portgroup_info?.find(obj => obj.switch_sn === currentPeerLeafInfo?.sn)
                                ?.port_info?.disable_port_list || [],
                        port_list:
                            currentSwitchCopy?.portgroup_info?.find(obj => obj.switch_sn === currentPeerLeafInfo?.sn)
                                ?.port_info?.port_list || [],
                        nic_port_list:
                            currentSwitchCopy?.portgroup_info?.find(obj => obj.switch_sn === currentPeerLeafInfo?.sn)
                                ?.port_info?.nic_port_list || []
                    };
                    currentHostLink.portgroup_info.push(newPortObj);
                }
                if (currentHostLink.link_count) {
                    let multiple = 0;
                    if (currentHostLink.link_type === "MLAG Leaf") {
                        multiple = currentHostLink.access_mlag_mode === "Single-Homed" ? 1 : 2;
                    } else {
                        multiple = 1;
                    }
                    currentHostLink.nicPortUpNum = currentHostLink.link_count * multiple;
                }
                break;

            // Peer Leaf
            case "peer_leaf":
                currentHostLink.peer_leaf = value;
                currentHostLink.portgroup_info = [];
                const currentSwitch = currentHostLink?.vdSwitchList?.find(obj => obj.logic_device_id === value);
                const newPortObj2 = {
                    logic_device_id: currentSwitch?.logic_device_id,
                    switch_sn: currentSwitch?.sn,
                    disable_port_list:
                        currentSwitchCopy?.portgroup_info?.find(obj => obj.switch_sn === currentSwitch.sn)?.port_info
                            ?.disable_port_list || [],
                    port_list:
                        currentSwitchCopy?.portgroup_info?.find(obj => obj.switch_sn === currentSwitch.sn)?.port_info
                            ?.port_list || [],
                    nic_port_list:
                        currentSwitchCopy?.portgroup_info?.find(obj => obj.switch_sn === currentSwitch.sn)?.port_info
                            ?.nic_port_list || []
                };
                currentHostLink.portgroup_info.push(newPortObj2);

                break;

            case "connect_mode":
                currentHostLink.connect_mode = value;
                break;

            // Physical link count per individual switch
            case "link_count":
                currentHostLink.link_count = value;
                let multiple = 0;
                if (currentHostLink.link_type === "MLAG Leaf") {
                    multiple = currentHostLink.access_mlag_mode === "Single-Homed" ? 1 : 2;
                } else {
                    multiple = 1;
                }
                currentHostLink.nicPortUpNum = value * multiple;
                if (currentHostLink.portgroup_info) {
                    currentHostLink.portgroup_info = currentHostLink.portgroup_info.map(item => ({
                        ...item,
                        port_list: []
                    }));
                }
                break;

            default:
                break;
        }
        currentHostLink.portgroup_info?.map((item, index) => {
            item.portgroup_name = currentHostLink?.portgroup_name;
            item.vlan_domain_id = currentHostLink?.vlan_domain_id;
            item.group_index = index + 1;
            return item;
        });
        setSwitchPortgroupList(prev => ({...prev, [`link_item${key}`]: currentHostLink}));
        // 更新form;
        const newHostLink2 = hostLinkData.map((item, index) => {
            if (index === nameIndex) {
                return {
                    ...item,
                    ...currentHostLink
                };
            }
            return item;
        });
        form.setFieldsValue({switch_portgroup: newHostLink2});
    };

    const saveSelectSwitchPort = data => {
        const formData = form.getFieldsValue();
        const updatedFormData = {
            ...formData,
            switch_portgroup: formData.switch_portgroup.map(group => {
                if (group.vlan_domain_id === data.vlan_domain_id && group.portgroup_name === data.portgroup_name) {
                    const updatedPortgroupInfo = group.portgroup_info.map(info => {
                        if (info.logic_device_id === data.logic_device_id) {
                            return {
                                ...info,
                                id: data.id,
                                group_index: data.group_index,
                                switch_sn: data.switch_sn,
                                port_list: data.port_list,
                                disable_port_list: data.disable_port_list,
                                nic_port_list: data.nic_port_list,
                                vlan_domain_id: data.vlan_domain_id,
                                portgroup_name: data.portgroup_name
                            };
                        }
                        return info;
                    });

                    return {
                        ...group,
                        portgroup_info: updatedPortgroupInfo
                    };
                }

                return group;
            })
        };
        form.setFieldsValue(updatedFormData); // 更新表单数据

        updatedFormData?.switch_portgroup?.forEach((item, index) => {
            const chooseSwitch = vlanDomainList?.find(obj => obj.id === item?.vlan_domain_id) || [];
            item.vdSwitchList = chooseSwitch?.switch_info || [];
            item.hostname = chooseSwitch?.switch_info?.map(device => device.hostname).join(",") || "";

            const nicSelectPortGroupNum =
                nicPortGroupList.find(obj => obj.nic_portgroup_name === item.nic_portgroup_name)?.port_num || 0;
            item.nicSelectPortGroupNum = nicSelectPortGroupNum;

            let multiple = 0;
            if (item.link_type === "MLAG Leaf") {
                multiple = item.access_mlag_mode === "Single-Homed" ? 1 : 2;
            } else {
                multiple = 1;
            }
            item.nicPortUpNum = item.link_count * multiple;
            item?.portgroup_info?.forEach((groupItem, groupIndex) => {
                groupItem.portgroup_name = item.portgroup_name;
                groupItem.vlan_domain_id = item.vlan_domain_id;
                groupItem.group_index = groupIndex + 1;
            });
            if (item.access_mlag_mode === "Single-Homed") {
                item.peer_leaf = item?.portgroup_info[0]?.logic_device_id;
            }
            // 更新Switch Port Group数据
            setSwitchPortgroupList(prev => {
                let foundKey = null;
                if (item.id) {
                    foundKey = Object.keys(prev).find(key => prev[key].id === item.id);
                }

                if (!foundKey && item.portgroup_name && item.vlan_domain_id) {
                    foundKey = Object.keys(prev).find(
                        key =>
                            prev[key].portgroup_name === item.portgroup_name &&
                            prev[key].vlan_domain_id === item.vlan_domain_id
                    );
                }
                if (foundKey) {
                    return {
                        ...prev,
                        [foundKey]: {
                            ...prev[foundKey],
                            ...item
                        }
                    };
                }
            });
        });
        setIsSelectSwitchPortModal(false);
    };

    /**
     * 编辑信息初始化
     */
    const editDataFormatting = data => {
        if (data?.fabric_id) {
            if (data?.node_host) {
                setNodeList(data?.node_host);
                data?.node_host?.forEach((item, index) => {
                    data[`ip_${index}`] = item.ip_addr;
                    data[`username_${index}`] = item.username;
                    data[`password_${index}`] = item.password;
                });
            }
            data?.switch_portgroup?.forEach((item, index) => {
                const chooseSwitch = vlanDomainList?.find(obj => obj.id === item?.vlan_domain_id) || [];
                item.vdSwitchList = chooseSwitch?.switch_info || [];
                item.hostname = chooseSwitch?.switch_info?.map(device => device.hostname).join(",") || "";

                const nicSelectPortGroupNum =
                    nicPortGroupList.find(obj => obj.nic_portgroup_name === item.nic_portgroup_name)?.port_num || 0;
                item.nicSelectPortGroupNum = nicSelectPortGroupNum;

                let multiple = 0;
                if (item.link_type === "MLAG Leaf") {
                    multiple = item.access_mlag_mode === "Single-Homed" ? 1 : 2;
                } else {
                    multiple = 1;
                }
                item.nicPortUpNum = item.link_count * multiple;
                item?.portgroup_info?.forEach((groupItem, groupIndex) => {
                    groupItem.portgroup_name = item.portgroup_name;
                    groupItem.vlan_domain_id = item.vlan_domain_id;
                    groupItem.group_index = groupIndex + 1;
                    const {disable_port_list, nic_port_list, port_list, sw_port_info} = groupItem.port_info || {};
                    groupItem.disable_port_list = disable_port_list;
                    groupItem.nic_port_list = nic_port_list;
                    groupItem.port_list = port_list;
                    groupItem.sw_port_info = sw_port_info;
                    // delete groupItem.port_info;
                });
                if (item.access_mlag_mode === "Single-Homed") {
                    item.peer_leaf = item?.portgroup_info[0]?.logic_device_id;
                }
                setSwitchPortgroupList(prev => ({...prev, [`link_item${index}`]: item}));
            });
            setNodeInfoCopy(data);
            form.setFieldsValue(data);
        }
    };

    /**
     * 校验Create/Edit下发数据
     */
    const checkSubmitData = data => {
        if (!data.switch_portgroup || data.switch_portgroup.length === 0) {
            return true;
        }
        for (const item of data.switch_portgroup) {
            if (!item.portgroup_info || !Array.isArray(item.portgroup_info)) {
                continue;
            }
            for (const portInfo of item.portgroup_info) {
                if (portInfo?.port_list?.length === 0) {
                    return false;
                }
            }
        }
        return true;
    };

    // 新增节点
    const handlCreateApply = async () => {
        const formData = await form.validateFields();
        const newnode_host = nodeHostList.map((item, index) => {
            return {
                host_name: item.host_name,
                ip_addr: item.ip_addr,
                username: item.username,
                password: item.password
            };
        });
        const currentCustomNode = customNodeList.find(obj => obj.template_name === formData.node_template_name);
        const updatedObject = {
            fabric_id: formData.fabric_id,
            nodegroup_name: formData.nodegroup_name,
            description: formData.description,
            node_count: formData.node_count,
            node_template_id: currentCustomNode?.id || "",
            az_id: formData.az_id,
            node_host: newnode_host,
            switch_portgroup: []
        };
        if (formData.switch_portgroup) {
            updatedObject.switch_portgroup = formData?.switch_portgroup?.map((item, index) => {
                return {
                    vlan_domain_id: item.vlan_domain_id,
                    portgroup_name: item.portgroup_name,
                    nic_portgroup_name: item.nic_portgroup_name,
                    connect_mode: item.connect_mode,
                    link_type: item.link_type,
                    link_count: item.link_count,
                    portgroup_info: item.portgroup_info?.map((infoItem, infoIndex) => {
                        return {
                            disable_port_list: infoItem.disable_port_list,
                            logic_device_id: infoItem.logic_device_id,
                            nic_port_list: infoItem.nic_port_list,
                            port_list: infoItem.port_list,
                            switch_sn: infoItem.switch_sn
                        };
                    })
                };
            });
        }
        if (!checkSubmitData(updatedObject)) {
            message.warning("Please select a switch port");
            return false;
        }
        try {
            const res = await save_node_group(updatedObject);
            if (res.status === 200) {
                message.success(res.info);
                navigate(`/resource/resource_interconnection/node_addition`, {
                    state: {tabKey: "bareMeTalNodes"}
                });
            } else {
                message.error(res.info);
            }
        } catch (error) {
            // error
        }
    };
    // 编辑节点
    const handleEditApply = async () => {
        const formData = await form.validateFields();
        const currentCustomNode = customNodeList.find(obj => obj.template_name === formData.node_template_name);
        const updatedObject = {
            id: state.data.id,
            fabric_id: formData.fabric_id,
            nodegroup_name: formData.nodegroup_name,
            description: formData.description,
            node_count: formData.node_count,
            node_template_id: currentCustomNode?.id || "",
            az_id: formData.az_id,
            node_host: nodeHostList
                ? nodeHostList?.map(node => ({
                      id: node.id,
                      host_name: node.host_name,
                      ip_addr: node.ip_addr,
                      username: node.username,
                      password: node.password,
                      pg_list: node.pg_list
                  }))
                : [],
            switch_portgroup: formData.switch_portgroup
                ? formData.switch_portgroup.map(group => ({
                      id: group.id,
                      vlan_domain_id: group.vlan_domain_id,
                      portgroup_name: group.portgroup_name,
                      nic_portgroup_name: group.nic_portgroup_name,
                      connect_mode: group.connect_mode,
                      link_type: group.link_type,
                      link_count: group.link_count,
                      portgroup_info:
                          group.portgroup_info.map(info => ({
                              id: info.id,
                              logic_device_id: info.logic_device_id,
                              switch_sn: info.switch_sn,
                              disable_port_list: info?.disable_port_list,
                              port_list: info?.port_list,
                              nic_port_list: info?.nic_port_list
                          })) || []
                  }))
                : []
        };
        if (!checkSubmitData(updatedObject)) {
            message.warning("Please select a switch port");
            return false;
        }
        try {
            const res = await save_node_group(updatedObject);
            if (res.status === 200) {
                message.success(res.info);
                navigate(`/resource/resource_interconnection/node_addition`, {
                    state: {tabKey: "bareMeTalNodes"}
                });
            } else {
                message.error(res.info);
            }
        } catch (error) {
            // error
        }
    };

    useEffect(() => {
        getFabricList();
        getCustomNode();
    }, []);

    useEffect(() => {
        if (state.actionType === "Edit" && fabricList.length > 0) {
            getTopoData(state.data.fabric_id);
            getPodList(state.data.fabric_id);
        }
    }, [state, fabricList]);

    useEffect(() => {
        form.resetFields();
        form.setFieldsValue({});
        setSwitchPortgroupList({});
        setNodeList([]);
        if (state.actionType === "Edit") {
            getVlanDomain(state.data.fabric_id);
            editDataFormatting(state.data);
            const copyData = JSON.parse(JSON.stringify(state.data));
            setEditNodeInfoCopy(copyData);
        }
    }, [state, form]);

    useEffect(() => {
        editDataFormatting(state.data);
        if (state.actionType === "Edit") {
            selectCustomNode(state.data.node_template_id);
        }
    }, [state, fabricList, customNodeList, podList, vlanDomainList]);

    // useEffect(() => {
    //     const allIpFields = nodeHostList.map((_, index) => `ip_${index}`);
    //     form.validateFields(allIpFields);
    // }, [nodeHostList]);

    return (
        <div style={{display: "flex", flex: 1, position: "relative"}}>
            <div className={styles.editNodeAdditionBox}>
                <h2 className={styles.pagesTitle}>{state?.actionType} Bare Metal Nodes </h2>
                <div className={styles.editNodeAdditionContent}>
                    <Form
                        form={form}
                        labelAlign="left"
                        rootClassName={styles.nodesForm}
                        style={{width: 710, minWidth: 350, maxHeight: "100%", overflowY: "auto"}}
                    >
                        <Form.Item
                            name="fabric_id"
                            label="Fabric"
                            labelCol={{style: {width: 154}}}
                            className={styles.nodeItem}
                            rules={[{required: true, message: "Please select fabric!"}]}
                        >
                            <Select
                                style={{width: 280}}
                                onChange={e => {
                                    getVlanDomain(e);
                                    getTopoData(e);
                                    getPodList(e);
                                    changeFabric();
                                }}
                                disabled={state.actionType === "Edit"}
                                options={fabricList?.map(item => ({
                                    value: item.id,
                                    label: item.fabric_name
                                }))}
                            />
                        </Form.Item>

                        <h2 className={styles.secondaryTitle}>Node Group</h2>
                        <Form.Item
                            name="nodegroup_name"
                            label="Node Group Name"
                            labelCol={{style: {width: 154}}}
                            className={styles.nodeItem}
                            rules={[
                                {
                                    required: true,
                                    validator: async (_, value) => {
                                        if (!value || value.trim() === "") {
                                            return Promise.reject(new Error("Please enter the node group name!"));
                                        }
                                        if (value.length > 64) {
                                            return Promise.reject(new Error("Enter a maximum of 64 characters"));
                                        }
                                        if (value === "All") {
                                            return Promise.reject(new Error("Please input a valid node group name!"));
                                        }
                                        if (value.trim() !== value) {
                                            return Promise.reject(
                                                new Error("Node group name should not have leading or trailing spaces.")
                                            );
                                        }
                                        if (!NAME_MATCH_REGEX.test(value)) {
                                            return Promise.reject(
                                                new Error(
                                                    "Node group name can only contain letters, numbers, underscores, hyphens and spaces."
                                                )
                                            );
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <Input
                                className={styles.formWidth}
                                disabled={state?.actionType === "Edit" && state?.data?.usage_state}
                                onChange={e => {
                                    updateNodeGroupTable("nodegroup_name", e.target.value);
                                    if (!e.target.value) {
                                        setIsNodeCountDisabled(true);
                                    } else {
                                        setIsNodeCountDisabled(false);
                                    }
                                }}
                            />
                        </Form.Item>

                        <Form.Item
                            name="description"
                            label="Description"
                            labelCol={{style: {width: 154}}}
                            className={styles.nodeItem}
                        >
                            <Input className={styles.formWidth} maxLength="128" />
                        </Form.Item>

                        <Form.Item
                            name="node_count"
                            label="Node Count"
                            labelCol={{style: {width: 154}}}
                            className={styles.nodeItem}
                            rules={[
                                {required: true, message: "Please enter the  node count!"},
                                {
                                    type: "number",
                                    message: "Please enter a valid number!"
                                },
                                {
                                    validator: (_, value) => {
                                        if (!Number.isInteger(value)) {
                                            return Promise.reject(new Error("Please enter an integer!"));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <InputNumber
                                className={styles.formWidth}
                                min={1}
                                max={32}
                                disabled={isNodeCountDisabled || state?.actionType === "Edit"}
                                placeholder="Range 1-32"
                                onChange={e => {
                                    updateNodeGroupTable("node_count", e);
                                    updateSelectSwitchPort();
                                    if (!e) {
                                        setIsNodeTemplateDisabled(true);
                                    } else {
                                        setIsNodeTemplateDisabled(false);
                                    }
                                }}
                            />
                        </Form.Item>

                        <Form.Item
                            name="node_template_name"
                            label="Node Template"
                            labelCol={{style: {width: 154}}}
                            className={styles.nodeItem}
                            rules={[{required: true, message: "Please select the node template!"}]}
                        >
                            <Select
                                style={{width: 280}}
                                rootClassName={styles.customizeSelect}
                                placeholder="Please select Node Template"
                                disabled={isNodeTemplateDisabled}
                                dropdownRender={() => nodeTemplateDropdownMenu}
                                open={visible}
                                value={selectedNode?.template_name || null}
                                onDropdownVisibleChange={newVisible => {
                                    setVisible(newVisible);
                                }}
                            />
                        </Form.Item>

                        <Form.Item
                            name="az_id"
                            label="PoD"
                            labelCol={{style: {width: 154}}}
                            className={styles.nodeItem}
                            rules={[{required: true, message: "Please select the PoD!"}]}
                        >
                            <Select
                                style={{width: 280}}
                                disabled={nodeInfoCopy.id || state?.actionType === "Edit"}
                                options={podList?.map(item => ({
                                    value: item.id,
                                    label: item.az_name
                                }))}
                            />
                        </Form.Item>
                        <div style={{marginBottom: 16}}>
                            <Table
                                bordered
                                columns={nodeTableColumns}
                                dataSource={nodeHostList}
                                pagination={false}
                                scroll={{x: 1000}}
                            />
                        </div>

                        <Form.Item name="switch_portgroup" label="">
                            <Form.List name="switch_portgroup">
                                {(fields, {add, remove}) => (
                                    <>
                                        {fields.map(({key, name, fieldKey, ...restField}) => {
                                            const currentHostLinkItem = {
                                                portgroup_name:
                                                    switch_portgroup[`link_item${key}`]?.portgroup_name || "",
                                                vlan_domain_id:
                                                    switch_portgroup[`link_item${key}`]?.vlan_domain_id || "",
                                                vdSwitchList: switch_portgroup[`link_item${key}`]?.vdSwitchList || [],
                                                hostname: switch_portgroup[`link_item${key}`]?.hostname || "",
                                                nic_portgroup_name:
                                                    switch_portgroup[`link_item${key}`]?.nic_portgroup_name || "",
                                                nicSelectPortGroupNum:
                                                    switch_portgroup[`link_item${key}`]?.nicSelectPortGroupNum || 0,
                                                link_type: switch_portgroup[`link_item${key}`]?.link_type || "", // MLAG Leaf || Single leaf
                                                access_mlag_mode:
                                                    switch_portgroup[`link_item${key}`]?.access_mlag_mode || "", // Single-Homed || Dual-Homed
                                                peer_leaf: switch_portgroup[`link_item${key}`]?.peer_leaf || "",
                                                connect_mode: switch_portgroup[`link_item${key}`]?.connect_mode || "", // Access || Trunk
                                                link_count: switch_portgroup[`link_item${key}`]?.link_count || "",
                                                nicPortUpNum: switch_portgroup[`link_item${key}`]?.nicPortUpNum || 0,
                                                portgroup_info:
                                                    switch_portgroup[`link_item${key}`]?.portgroup_info || []
                                            };
                                            // 获取除当前项之外的所有 Port Group Name、NIC Port Group
                                            const otherPortGroupNames = [];
                                            const otherNicPortgroupNames = [];

                                            for (const itemKey in switch_portgroup) {
                                                if (itemKey !== `link_item${key}`) {
                                                    const portGroupName = switch_portgroup[itemKey].portgroup_name;
                                                    const {nic_portgroup_name} = switch_portgroup[itemKey];
                                                    if (portGroupName) {
                                                        otherPortGroupNames.push(portGroupName);
                                                    }
                                                    if (nic_portgroup_name) {
                                                        otherNicPortgroupNames.push(nic_portgroup_name);
                                                    }
                                                }
                                            }

                                            const isLinkId = switch_portgroup[`link_item${key}`]?.id;
                                            let peerLeaf_maxNum = 1;
                                            if (currentHostLinkItem.link_type === "MLAG Leaf") {
                                                if (currentHostLinkItem.access_mlag_mode === "Single-Homed") {
                                                    peerLeaf_maxNum = currentHostLinkItem.nicSelectPortGroupNum;
                                                } else {
                                                    peerLeaf_maxNum =
                                                        currentHostLinkItem.nicSelectPortGroupNum > 1
                                                            ? currentHostLinkItem.nicSelectPortGroupNum / 2
                                                            : 1;
                                                }
                                            } else if (currentHostLinkItem.link_type === "Single leaf") {
                                                peerLeaf_maxNum = currentHostLinkItem.nicSelectPortGroupNum;
                                            }

                                            return (
                                                <div key={key} className={styles.nodeLinkList}>
                                                    <div className={styles.groupHeader}>
                                                        <p>Switch Port Group</p>
                                                        <div className={styles.activeIcon}>
                                                            {(!state.data.usage_state || !isLinkId) && (
                                                                <Icon
                                                                    className={styles.deleteLink}
                                                                    component={
                                                                        !deleteSVGHover ? DeleteSvg : DeleteGreyHover
                                                                    }
                                                                    onMouseEnter={() => {
                                                                        setDeleteSVGHover(true);
                                                                    }}
                                                                    onMouseLeave={() => {
                                                                        setDeleteSVGHover(false);
                                                                    }}
                                                                    onClick={() => {
                                                                        remove(name);
                                                                        delete switch_portgroup[`link_item${key}`];
                                                                        setSwitchPortgroupList(switch_portgroup);
                                                                    }}
                                                                    style={{marginLeft: 8, cursor: "pointer"}}
                                                                />
                                                            )}
                                                        </div>
                                                    </div>

                                                    <Form.Item
                                                        name={[name, "portgroup_name"]}
                                                        label="Port Group Name"
                                                        labelCol={{style: {width: 164}}}
                                                        className={styles.linkItem}
                                                        rules={[
                                                            {
                                                                required: true,
                                                                validator: async (_, value) => {
                                                                    if (!value || value.trim() === "") {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Please input a valid port group name!"
                                                                            )
                                                                        );
                                                                    }
                                                                    if (value.length > 64) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Enter a maximum of 64 characters"
                                                                            )
                                                                        );
                                                                    }
                                                                    if (value === "All") {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Please input a valid port group name!"
                                                                            )
                                                                        );
                                                                    }
                                                                    if (value.trim() !== value) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Port group name should not have leading or trailing spaces."
                                                                            )
                                                                        );
                                                                    }
                                                                    if (!NAME_MATCH_REGEX2.test(value)) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Port group name can only contain letters, numbers, underscores, hyphens and spaces."
                                                                            )
                                                                        );
                                                                    }
                                                                    if (otherPortGroupNames.includes(value)) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Port Group Name cannot be repeated."
                                                                            )
                                                                        );
                                                                    }
                                                                    return Promise.resolve();
                                                                }
                                                            }
                                                        ]}
                                                    >
                                                        <Input
                                                            disabled={
                                                                state?.actionType === "Edit" &&
                                                                state?.data?.usage_state &&
                                                                isLinkId
                                                            }
                                                            className={styles.formWidth}
                                                            onChange={e => {
                                                                handleLinkItemChange(
                                                                    "portgroup_name",
                                                                    e.target.value,
                                                                    key,
                                                                    name
                                                                );
                                                            }}
                                                        />
                                                    </Form.Item>

                                                    <Form.Item
                                                        name={[name, "vlan_domain_id"]}
                                                        label="VLAN Domain"
                                                        labelCol={{style: {width: 164}}}
                                                        className={styles.linkItem}
                                                        rules={[{required: true, message: "VLAN Domain is required!"}]}
                                                    >
                                                        <Select
                                                            style={{width: 280}}
                                                            disabled={
                                                                (state?.actionType === "Edit" && isLinkId) ||
                                                                !currentHostLinkItem.portgroup_name
                                                            }
                                                            onChange={e => {
                                                                handleLinkItemChange("vlan_domain_id", e, key, name);
                                                            }}
                                                            options={vlanDomainList?.map(item => ({
                                                                value: item.id,
                                                                label: item.name
                                                            }))}
                                                        />
                                                    </Form.Item>

                                                    <Form.Item
                                                        name={[name, "hostname"]}
                                                        colon={false}
                                                        wrapperCol={{offset: 0, span: 24}}
                                                        style={{marginBottom: 10}}
                                                    >
                                                        <span className="custom-label">Switch: </span>
                                                        <span className="ml-2">{currentHostLinkItem?.hostname}</span>
                                                    </Form.Item>

                                                    <Form.Item
                                                        name={[name, "nic_portgroup_name"]}
                                                        label="NIC Port Group"
                                                        labelCol={{style: {width: 164}}}
                                                        className={styles.linkItem}
                                                        rules={[
                                                            {
                                                                required: true,
                                                                validator: async (_, value) => {
                                                                    if (!value || value.trim() === "") {
                                                                        return Promise.reject(
                                                                            new Error("Please select a NIC Port Group!")
                                                                        );
                                                                    }
                                                                    if (otherNicPortgroupNames.includes(value)) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "NIC Port Group cannot be repeated."
                                                                            )
                                                                        );
                                                                    }
                                                                    return Promise.resolve();
                                                                }
                                                            }
                                                        ]}
                                                    >
                                                        <Select
                                                            style={{width: 280}}
                                                            disabled={state?.actionType === "Edit" && isLinkId}
                                                            onChange={e => {
                                                                handleLinkItemChange(
                                                                    "nic_portgroup_name",
                                                                    e,
                                                                    key,
                                                                    name
                                                                );
                                                            }}
                                                            options={nicPortGroupList?.map(item => ({
                                                                value: item.nic_portgroup_name,
                                                                label: item.nic_portgroup_name
                                                            }))}
                                                        />
                                                    </Form.Item>

                                                    {currentHostLinkItem.vlan_domain_id !== "" &&
                                                        currentHostLinkItem?.link_type === "MLAG Leaf" && (
                                                            <>
                                                                <Form.Item
                                                                    name={[name, "access_mlag_mode"]}
                                                                    label="Access MLAG Mode"
                                                                    labelCol={{style: {width: 164}}}
                                                                    className={styles.linkItem}
                                                                >
                                                                    <Radio.Group
                                                                        defaultValue="Single-Homed"
                                                                        disabled={
                                                                            state?.actionType === "Edit" && isLinkId
                                                                        }
                                                                        onChange={e => {
                                                                            handleLinkItemChange(
                                                                                "access_mlag_mode",
                                                                                e.target.value,
                                                                                key,
                                                                                name
                                                                            );
                                                                        }}
                                                                    >
                                                                        <Radio value="Single-Homed">Single-Homed</Radio>
                                                                        {currentHostLinkItem.nicSelectPortGroupNum !==
                                                                            1 && (
                                                                            <Radio value="Dual-Homed">Dual-Homed</Radio>
                                                                        )}
                                                                    </Radio.Group>
                                                                </Form.Item>

                                                                {/* 控制 Peer Leaf 字段的显示与隐藏 */}
                                                                {currentHostLinkItem?.access_mlag_mode ===
                                                                    "Single-Homed" && (
                                                                    <Form.Item
                                                                        name={[name, "peer_leaf"]}
                                                                        label="Peer Leaf"
                                                                        labelCol={{style: {width: 164}}}
                                                                        className={styles.linkItem}
                                                                    >
                                                                        <Radio.Group
                                                                            name="radiogroup"
                                                                            disabled={
                                                                                state?.actionType === "Edit" && isLinkId
                                                                            }
                                                                            defaultValue={
                                                                                currentHostLinkItem?.vdSwitchList[0]
                                                                                    ?.logic_device_id
                                                                            }
                                                                            onChange={e => {
                                                                                handleLinkItemChange(
                                                                                    "peer_leaf",
                                                                                    e.target.value,
                                                                                    key,
                                                                                    name
                                                                                );
                                                                            }}
                                                                            options={currentHostLinkItem?.vdSwitchList.map(
                                                                                device => ({
                                                                                    label: device.hostname,
                                                                                    value: device.logic_device_id
                                                                                })
                                                                            )}
                                                                        />
                                                                    </Form.Item>
                                                                )}
                                                            </>
                                                        )}

                                                    <Form.Item
                                                        name={[name, "connect_mode"]}
                                                        label="Port Mode"
                                                        labelCol={{style: {width: 164}}}
                                                        className={styles.linkItem}
                                                    >
                                                        <Radio.Group
                                                            disabled={
                                                                state?.actionType === "Edit" &&
                                                                state?.data?.usage_state &&
                                                                isLinkId
                                                            }
                                                            defaultValue="access"
                                                            onChange={e => {
                                                                handleLinkItemChange(
                                                                    "connect_mode",
                                                                    e.target.value,
                                                                    key,
                                                                    name
                                                                );
                                                            }}
                                                        >
                                                            <Radio value="access">Access</Radio>
                                                            <Radio value="trunk">Trunk</Radio>
                                                        </Radio.Group>
                                                    </Form.Item>

                                                    <Form.Item
                                                        name={[name, "link_count"]}
                                                        label="Physical Link Count per Leaf Switch"
                                                        className={styles.linkItem}
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    "Please input a valid Physical Link Count per Leaf Switch!"
                                                            },
                                                            {
                                                                type: "number",
                                                                message: "Please enter a valid number!"
                                                            },
                                                            {
                                                                validator: (_, value) => {
                                                                    if (!Number.isInteger(value)) {
                                                                        return Promise.reject(
                                                                            new Error("Please enter an integer!")
                                                                        );
                                                                    }
                                                                    return Promise.resolve();
                                                                }
                                                            }
                                                        ]}
                                                    >
                                                        <InputNumber
                                                            min={1}
                                                            max={peerLeaf_maxNum}
                                                            precision={0}
                                                            className={styles.formWidth}
                                                            disabled={state?.actionType === "Edit" && isLinkId}
                                                            onChange={e => {
                                                                handleLinkItemChange("link_count", e, key, name);
                                                            }}
                                                        />
                                                    </Form.Item>

                                                    <br />

                                                    <Form.Item
                                                        name={[name, ""]}
                                                        label="Select NIC Ports"
                                                        labelCol={{style: {display: "inline-block", width: 164}}}
                                                        wrapperCol={{style: {display: "inline-block"}}}
                                                        className={styles.linkItem}
                                                    >
                                                        {currentHostLinkItem.vlan_domain_id !== "" && (
                                                            <ul className={styles.NICPortsli}>
                                                                {Array.from(
                                                                    {
                                                                        length: currentHostLinkItem.nicSelectPortGroupNum
                                                                    },
                                                                    (_, i) => {
                                                                        const isPortUsed =
                                                                            currentHostLinkItem.nicPortUpNum <=
                                                                                currentHostLinkItem.nicSelectPortGroupNum &&
                                                                            i < currentHostLinkItem.nicPortUpNum;
                                                                        return (
                                                                            <li
                                                                                key={i}
                                                                                style={
                                                                                    isPortUsed
                                                                                        ? {background: "#D0DC42"}
                                                                                        : {}
                                                                                }
                                                                            >
                                                                                <span
                                                                                    style={
                                                                                        isPortUsed
                                                                                            ? {background: "#D0DC42"}
                                                                                            : {}
                                                                                    }
                                                                                >
                                                                                    {i + 1}
                                                                                </span>
                                                                            </li>
                                                                        );
                                                                    }
                                                                )}
                                                            </ul>
                                                        )}
                                                    </Form.Item>
                                                    <br />

                                                    <div>
                                                        <Table
                                                            bordered
                                                            columns={selectNICPortTableColumns}
                                                            dataSource={currentHostLinkItem?.portgroup_info}
                                                            pagination={false}
                                                        />
                                                    </div>
                                                </div>
                                            );
                                        })}
                                        {!state.data.usage_state && (
                                            <Button
                                                className={styles.addSwitchPortGroupBtn}
                                                type="dashed"
                                                onClick={() => {
                                                    if (fields.length < switchGroupNumLimit) {
                                                        add();
                                                    } else {
                                                        message.warning(
                                                            `Up to ${switchGroupNumLimit} switch port group can be added.`
                                                        );
                                                    }
                                                }}
                                            >
                                                <PlusOutlined style={{marginRight: 8}} />
                                                Switch Port Group
                                            </Button>
                                        )}
                                    </>
                                )}
                            </Form.List>
                        </Form.Item>
                    </Form>

                    <div className={styles.topologyGraph}>
                        <FabricTopo topoInfo={topoInfo} />
                    </div>
                </div>

                <SelectSwitchPortModal
                    title="Select Switch Ports"
                    nodeType="BareMetal"
                    portgroup_info={portgroup_info}
                    nodeDetails={nodeDetails}
                    isSelectSwitchPortModal={isSelectSwitchPortModal}
                    onCancel={() => setIsSelectSwitchPortModal(false)}
                    onSubmit={saveSelectSwitchPort}
                    modalClass="ampcon-max-modal"
                />

                <CustomNodeTemplateModal
                    title={customNodeTemplateModalTitle}
                    setCustomNodeTemplateModalTitle={setCustomNodeTemplateModalTitle}
                    currentCustomNodeTemplate={currentCustomNodeTemplate}
                    isCustomNodeTemplateModal={isCustomNodeTemplateModal}
                    setIsCustomNodeTemplateModal={setIsCustomNodeTemplateModal}
                    onCancel={() => setIsCustomNodeTemplateModal(false)}
                    onSubmit={saveCustomNodeTemplate}
                    modalClass="ampcon-middle-modal"
                />
            </div>
            <div className={styles.IssueOperationBar}>
                <Button
                    onClick={() => {
                        navigate(`/resource/resource_interconnection/node_addition`, {
                            state: {tabKey: "bareMeTalNodes"}
                        });
                    }}
                >
                    Cancel
                </Button>
                <Button
                    type="primary"
                    onClick={() => {
                        if (state.actionType === "Edit") handleEditApply();
                        if (state.actionType === "Create") handlCreateApply();
                    }}
                >
                    Apply
                </Button>
            </div>
        </div>
    );
});
export default CreateBareMetalNodeAddition;
