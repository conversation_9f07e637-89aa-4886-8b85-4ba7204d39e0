import React, {useRef, useEffect, useState} from "react";
import {useLocation, useNavigate} from "react-router-dom";
import {Space, Tabs, Tag, message, Tooltip, Table, Button} from "antd";
import {AmpConCustomTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";
import {useTableInitialElement} from "@/modules-ampcon/hooks/useModalTable";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";
import Icon from "@ant-design/icons";
import {addSvg} from "@/utils/common/iconSvg";
import NodeLinksInfoModal from "./nodeLinksInfoModal";
import NodeGroupDetailModal from "./nodeGroupDetailsModal";
import NodeLogModal from "./nodeLogModal";

// import API
import {
    deleteBareMetalNodeInfo,
    fetchBareMetalNodeInfo,
    fetchCloudInfo,
    delete_virtual_resource_host
} from "@/modules-ampcon/apis/node_addition_api";

const NodeFabricator = () => {
    const {state} = useLocation();
    const [activeTab, setActiveTab] = useState("bareMeTalNodes");

    const [tabs, setTabs] = useState([
        {
            key: "bareMeTalNodes",
            label: "Bare Metal Nodes",
            forceRender: false,
            children: <BareMeTalNodesTab />
        },
        {
            key: "cloudPlatformNodes",
            label: "Cloud Platform Nodes",
            forceRender: false,
            children: <CloudPlatformNodesTab />
        }
    ]);

    useEffect(() => {
        if (state?.tabKey) setActiveTab(state.tabKey);
    }, [state]);
    return (
        <Tabs
            items={tabs}
            activeKey={activeTab}
            onChange={key => setActiveTab(key)}
            className="radioGroupTabs customTab"
        />
    );
};

export default NodeFabricator;

/**
 *  Bare Metal Nodes
 */
const BareMeTalNodesTab = () => {
    const {state} = useLocation();
    const navigate = useNavigate();
    const [linksInfoModal, setLinksInfoModal] = useState(false);
    const [isNodeGroupDetailModal, setIsNodeGroupDetailModal] = useState(false);
    const [isNodeLogModal, setIsNodeLogModal] = useState(false);
    const [nodesInfo, setNodesInfo] = useState({});
    const BareMetalTableRef = useRef(null);

    const bareMeTalColumns = [
        {...createColumnConfig("Fabric", "fabric_name")},
        {...createColumnConfig("PoD", "az_name")},
        {...createColumnConfig("Node Group", "nodegroup_name")},
        {
            ...createColumnConfig("Description", "description"),
            render: (text, record) => {
                return (
                    <span
                        title={record?.description}
                        style={{
                            maxWidth: 200,
                            display: "inline-block",
                            overflow: "hidden",
                            textWrap: "wrap",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap"
                        }}
                    >
                        {record?.description}
                    </span>
                );
            }
        },
        {
            title: "User",
            dataIndex: "username",
            render: (text, record) => {
                return <span>admin</span>;
            }
        },
        {
            title: "Usage Status",
            dataIndex: "usage_state",
            render: (text, record) => {
                return (
                    <Tag className={record?.usage_state ? styles.successTag : styles.uncheckedTag}>
                        {record?.usage_state ? "Used" : "Unused"}
                    </Tag>
                );
            }
        },
        {
            ...createColumnConfig("Configuration Status", "status"),
            render: (text, record) => {
                const statusClassMap = {
                    "Not Deployed": styles.notDeployedTag,
                    Deploying: styles.runningTag,
                    Deployed: styles.successTag,
                    "Deploy Failed": styles.failedTag,
                    Deleting: styles.runningTag,
                    "Delete Failed": styles.failedTag
                };
                return <Tag className={statusClassMap[record?.status] || styles.uncheckedTag}>{record?.status}</Tag>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="middle" className={styles.actionLink}>
                    <a
                        onClick={() => {
                            setNodesInfo(record);
                            setIsNodeGroupDetailModal(true);
                        }}
                    >
                        Details
                    </a>
                    <a
                        onClick={() => {
                            setNodesInfo(record);
                            setLinksInfoModal(true);
                        }}
                    >
                        Link Info
                    </a>
                    <a
                        onClick={() => {
                            if (record.status === "Deleting" || record.status === "Deploying") {
                                message.error(`Node group task is being ${record.status} and cannot be edited`);
                                return false;
                            }
                            navigate(
                                `/resource/resource_interconnection/node_addition/bare_metal/${record.fabric_name}`,
                                {
                                    state: {actionType: "Edit", nodeType: "BareMetal", data: record}
                                }
                            );
                        }}
                        style={record.status === "Deleting" || record.status === "Deploying" ? {color: "#929a9e"} : {}}
                    >
                        Edit
                    </a>
                    <a
                        onClick={() => {
                            if (record.status === "Deleting" || record.status === "Deploying" || record.usage_state) {
                                message.error(
                                    `Node group task is being ${record.usage_state ? "Used" : record.status} and cannot be deleted`
                                );
                                return false;
                            }
                            confirmModalAction("Are you sure you want to delete the node group?", () =>
                                delete_node(record)
                            );
                        }}
                        style={
                            record.status === "Deleting" || record.status === "Deploying" || record.usage_state
                                ? {color: "#B3BBC8"}
                                : {}
                        }
                    >
                        Delete
                    </a>
                    <a
                        onClick={() => {
                            setNodesInfo(record);
                            setIsNodeLogModal(true);
                        }}
                    >
                        Log
                    </a>
                </Space>
            )
        }
    ];

    const matchFieldsList = [
        {name: "fabric_name", matchMode: "exact"},
        {name: "az_name", matchMode: "fuzzy"},
        {name: "nodegroup_name", matchMode: "fuzzy"},
        {name: "description", matchMode: "fuzzy"},
        {name: "username", matchMode: "fuzzy"},
        {name: "usage_state", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["nodegroup_name", "description", "status"];

    const delete_node = async record => {
        try {
            const res = await deleteBareMetalNodeInfo({
                id: record.id
            });
            if (res.status === 200) {
                message.success(res.info);
                BareMetalTableRef.current.refreshTable();
            } else {
                message.error(res.info);
            }
        } catch (error) {
            // error
        }
    };

    useEffect(() => {
        const timer = setInterval(() => {
            BareMetalTableRef.current.refreshTable();
        }, 30000);
        return () => {
            clearInterval(timer);
        };
    }, []);

    return (
        <>
            <AmpConCustomTable
                searchFieldsList={searchFieldsList}
                fetchAPIInfo={fetchBareMetalNodeInfo}
                matchFieldsList={matchFieldsList}
                columns={bareMeTalColumns}
                ref={BareMetalTableRef}
                extraButton={
                    <Button
                        type="primary"
                        htmlType="button"
                        icon={<Icon component={addSvg} />}
                        onClick={() => {
                            navigate(`/resource/resource_interconnection/node_addition/bare_metal/create`, {
                                state: {actionType: "Create", nodeType: "BareMetal", data: {}}
                            });
                        }}
                    >
                        Node Group
                    </Button>
                }
                isShowPagination
            />

            <NodeLinksInfoModal
                title="Link Info"
                nodeType="BareMetal"
                nodesInfo={nodesInfo}
                linksInfoModal={linksInfoModal}
                setLinksInfoModal={setLinksInfoModal}
                onCancel={() => setLinksInfoModal(false)}
                modalClass="ampcon-max-modal"
            />

            <NodeGroupDetailModal
                title="Node Group Detail"
                nodeType="BareMetal"
                nodesInfo={nodesInfo}
                isNodeGroupDetailModal={isNodeGroupDetailModal}
                setIsNodeGroupDetailModal={setIsNodeGroupDetailModal}
                onCancel={() => setIsNodeGroupDetailModal(false)}
                modalClass="ampcon-middle-modal"
            />

            <NodeLogModal
                title={nodesInfo.fabric_name}
                nodeType="BareMetal"
                nodesInfo={nodesInfo}
                isNodeLogModal={isNodeLogModal}
                setIsNodeLogModal={setIsNodeLogModal}
                onCancel={() => setIsNodeLogModal(false)}
                modalClass="ampcon-max-modal"
            />
        </>
    );
};

/**
 * Cloud Platform Nodes
 */
const CloudPlatformNodesTab = () => {
    const {state} = useLocation();
    const navigate = useNavigate();
    const [linksInfoModal, setLinksInfoModal] = useState(false);
    const [isNodeGroupDetailModal, setIsNodeGroupDetailModal] = useState(false);
    const [isNodeLogModal, setIsNodeLogModal] = useState(false);
    const [nodesInfo, setNodesInfo] = useState({});
    const CloudTableRef = useRef(null);

    const cloudColumns = [
        {...createColumnConfig("Fabric", "fabric_name")},
        {...createColumnConfig("PoD", "az_name")},
        {...createColumnConfig("Node", "host_name")},
        {
            ...createColumnConfig("Description", "description"),
            render: (text, record) => {
                return (
                    <span
                        title={record?.description}
                        style={{
                            maxWidth: 200,
                            display: "inline-block",
                            overflow: "hidden",
                            textWrap: "wrap",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap"
                        }}
                    >
                        {record?.description}
                    </span>
                );
            }
        },
        {
            title: "User",
            dataIndex: "username",
            render: (text, record) => {
                return <span>admin</span>;
            }
        },
        {
            title: "Usage Status",
            dataIndex: "usage_state",
            render: (text, record) => {
                return (
                    <Tag className={record?.usage_state ? styles.successTag : styles.uncheckedTag}>
                        {record?.usage_state ? "Used" : "Unused"}
                    </Tag>
                );
            }
        },
        {
            ...createColumnConfig("Configuration Status", "status"),
            render: (text, record) => {
                const statusClassMap = {
                    "Not Deployed": styles.notDeployedTag,
                    Deploying: styles.runningTag,
                    Deployed: styles.successTag,
                    "Deploy Failed": styles.failedTag,
                    Deleting: styles.runningTag,
                    "Delete Failed": styles.failedTag
                };
                return <Tag className={statusClassMap[record?.status] || styles.uncheckedTag}>{record?.status}</Tag>;
            }
        },
        {
            ...createColumnConfig("Connectivity Status", "connect_status"),
            render: (text, record) => {
                return (
                    <Tag className={record?.connect_status ? styles.successTag : styles.failedTag}>
                        {record?.connect_status ? "Connected" : "Disconnected"}
                    </Tag>
                );
            }
        },
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="middle" className={styles.actionLink}>
                    <a
                        onClick={() => {
                            setNodesInfo(record);
                            setIsNodeGroupDetailModal(true);
                        }}
                    >
                        Details
                    </a>
                    <a
                        onClick={() => {
                            setNodesInfo(record);
                            setLinksInfoModal(true);
                        }}
                    >
                        Link Info
                    </a>
                    <a
                        onClick={() => {
                            if (record.status === "Deleting" || record.status === "Deploying") {
                                message.error(`Node group task is being ${record.status} and cannot be edited`);
                                return false;
                            }
                            navigate(`/resource/resource_interconnection/node_addition/cloud/${record.fabric_name}`, {
                                state: {actionType: "Edit", nodeType: "Cloud", data: record}
                            });
                        }}
                        style={record.status === "Deleting" || record.status === "Deploying" ? {color: "#929a9e"} : {}}
                    >
                        Edit
                    </a>
                    <a
                        onClick={() => {
                            if (record.status === "Deleting" || record.status === "Deploying" || record.usage_state) {
                                message.error(
                                    `Node task is being ${record.usage_state ? "Used" : record.status} and cannot be deleted`
                                );
                                return false;
                            }
                            confirmModalAction("Are you sure you want to delete the node?", () => delete_node(record));
                        }}
                        style={
                            record.status === "Deleting" || record.status === "Deploying" || record.usage_state
                                ? {color: "#B3BBC8"}
                                : {}
                        }
                    >
                        Delete
                    </a>
                    <a
                        onClick={() => {
                            setNodesInfo(record);
                            setIsNodeLogModal(true);
                        }}
                    >
                        Log
                    </a>
                </Space>
            )
        }
    ];

    const matchFieldsList = [
        {name: "fabric_name", matchMode: "exact"},
        {name: "az_name", matchMode: "fuzzy"},
        {name: "host_name", matchMode: "fuzzy"},
        {name: "description", matchMode: "fuzzy"},
        {name: "username", matchMode: "fuzzy"},
        {name: "usage_state", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"},
        {name: "connect_status", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["host_name", "description", "status", "connect_status"];

    const delete_node = async record => {
        try {
            const res = await delete_virtual_resource_host({
                host_id: record.id
            });
            if (res.status === 200) {
                message.success(res.info);
                CloudTableRef.current.refreshTable();
            } else {
                message.error(res.info);
            }
        } catch (error) {
            // error
        }
    };

    useEffect(() => {
        const timer = setInterval(() => {
            CloudTableRef.current.refreshTable();
        }, 30000);
        return () => {
            clearInterval(timer);
        };
    }, []);

    return (
        <>
            <AmpConCustomTable
                searchFieldsList={searchFieldsList}
                fetchAPIInfo={fetchCloudInfo}
                matchFieldsList={matchFieldsList}
                columns={cloudColumns}
                ref={CloudTableRef}
                // extraButton={
                //     <Button
                //         type="primary"
                //         htmlType="button"
                //         icon={<Icon component={addSvg} />}
                //         onClick={() => {
                //             navigate(`/resource/resource_interconnection/node_addition/cloud/create`, {
                //                 state: {actionType: "Create", nodeType: "Cloud", data: {}}
                //             });
                //         }}
                //     >
                //         Node
                //     </Button>
                // }
                isShowPagination
            />

            <NodeLinksInfoModal
                title="Link Info"
                nodeType="Cloud"
                nodesInfo={nodesInfo}
                linksInfoModal={linksInfoModal}
                setLinksInfoModal={setLinksInfoModal}
                onCancel={() => setLinksInfoModal(false)}
                modalClass="ampcon-max-modal"
            />

            <NodeGroupDetailModal
                title="Node Detail"
                nodeType="Cloud"
                nodesInfo={nodesInfo}
                isNodeGroupDetailModal={isNodeGroupDetailModal}
                setIsNodeGroupDetailModal={setIsNodeGroupDetailModal}
                onCancel={() => setIsNodeGroupDetailModal(false)}
                modalClass="ampcon-middle-modal"
            />

            <NodeLogModal
                title={nodesInfo.fabric_name}
                nodeType="Cloud"
                nodesInfo={nodesInfo}
                isNodeLogModal={isNodeLogModal}
                setIsNodeLogModal={setIsNodeLogModal}
                onCancel={() => setIsNodeLogModal(false)}
                modalClass="ampcon-middle-modal"
            />
        </>
    );
};

/**
 * Node Hosting
 */
const NodeHostingTab = () => {
    const {state} = useLocation();
    const navigate = useNavigate();
    const NodeHostTableRef = useRef(null);

    const matchFieldsList = [
        {name: "fabric_name", matchMode: "exact"},
        {name: "az_name", matchMode: "fuzzy"},
        {name: "node_name", matchMode: "fuzzy"},
        {name: "description", matchMode: "fuzzy"},
        {name: "username", matchMode: "fuzzy"},
        {name: "usage_state", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"},
        {name: "connect_status", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["node_name", "description", "status", "connect_status"];

    const NodeHostingColumns = [
        {...createColumnConfig("Fabric", "fabric_name")},
        {...createColumnConfig("PoD", "az_name")},
        {...createColumnConfig("Node", "host_name")},
        {
            ...createColumnConfig("Description", "description"),
            render: (text, record) => {
                return (
                    <span
                        title={record?.description}
                        style={{
                            maxWidth: 200,
                            display: "inline-block",
                            overflow: "hidden",
                            textWrap: "wrap",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap"
                        }}
                    >
                        {record?.description}
                    </span>
                );
            }
        },
        {
            title: "User",
            dataIndex: "username",
            render: (text, record) => {
                return <span>admin</span>;
            }
        },
        {
            title: "Usage Status",
            dataIndex: "usage_state",
            render: (text, record) => {
                return (
                    <Tag className={record?.usage_state ? styles.successTag : styles.uncheckedTag}>
                        {record?.usage_state ? "Used" : "Unused"}
                    </Tag>
                );
            }
        },
        {
            ...createColumnConfig("Configuration Status", "status"),
            render: (text, record) => {
                const statusClassMap = {
                    "Not Deployed": styles.notDeployedTag,
                    Deploying: styles.runningTag,
                    Deployed: styles.successTag,
                    "Deploy Failed": styles.failedTag,
                    Deleting: styles.runningTag,
                    "Delete Failed": styles.failedTag
                };
                return <Tag className={statusClassMap[record?.status] || styles.uncheckedTag}>{record?.status}</Tag>;
            }
        },
        {
            ...createColumnConfig("Connect Status", "connect_status"),
            render: (text, record) => {
                return (
                    <Tag className={record?.connect_status ? styles.successTag : styles.failedTag}>
                        {record?.connect_status ? "Connected" : "Disconnected"}
                    </Tag>
                );
            }
        },
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="middle" className={styles.actionLink}>
                    <a onClick={() => {}}>Edit</a>
                    <a onClick={() => {}}>Delete</a>
                </Space>
            )
        }
    ];

    useEffect(() => {
        const timer = setInterval(() => {
            NodeHostTableRef.current.refreshTable();
        }, 30000);
        return () => {
            clearInterval(timer);
        };
    }, []);

    return (
        <div>
            <AmpConCustomTable
                searchFieldsList={searchFieldsList}
                fetchAPIInfo={fetchCloudInfo}
                matchFieldsList={matchFieldsList}
                columns={NodeHostingColumns}
                ref={NodeHostTableRef}
                extraButton={
                    <Button
                        type="primary"
                        htmlType="button"
                        icon={<Icon component={addSvg} />}
                        onClick={() => {
                            navigate(`/resource/resource_interconnection/node_addition/cloud/create`, {
                                state: {actionType: "Create", nodeType: "Cloud", data: {}}
                            });
                        }}
                    >
                        Node
                    </Button>
                }
                isShowPagination
            />
        </div>
    );
};
