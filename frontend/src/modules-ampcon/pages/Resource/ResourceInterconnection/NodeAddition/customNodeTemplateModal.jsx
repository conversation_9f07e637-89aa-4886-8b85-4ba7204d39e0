import React, {useImperative<PERSON><PERSON>le, useEffect, useState, useRef, forwardRef} from "react";
import {useLocation} from "react-router-dom";
import {Row, Space, message, Table, Modal, Divider, Form, Input, InputNumber, Select, Button} from "antd";

import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";

// import API
import {} from "@/modules-ampcon/apis/node_addition_api";

/**
 * Custom Node Template
 */
const CustomNodeTemplateModal = ({
    title,
    currentCustomNodeTemplate,
    isCustomNodeTemplateModal,
    setIsCustomNodeTemplateModal,
    onCancel,
    onSubmit
}) => {
    const {state} = useLocation();
    const [form] = Form.useForm();
    const NAME_MATCH_REGEX = /^[\s\w:-]+$/;
    const [data, setData] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRowData, setSelectedRowData] = useState(null);
    const [isDisabledEdit, setIsDisabledEdit] = useState(false);

    const columns = [
        {
            title: "NIC Port Group",
            dataIndex: "nic_portgroup_name",
            key: "nic_portgroup_name"
        },
        {
            title: "Speed",
            dataIndex: "speed",
            key: "speed",
            render: (text, record, key) => {
                const legendClass = `fontColor_${record.speed}G`;
                return (
                    <p className={`${styles.legendClass} ${styles[legendClass]}`}>
                        {record.port_num} x {record.speed} Gbps
                    </p>
                );
            }
        },
        {
            title: "Port Member",
            dataIndex: "port_num",
            key: "port_num",
            render: (text, record, key) => {
                return (
                    <>
                        {Array.from({length: record.port_num}, (_, index) => (
                            <span key={index} style={{marginRight: 5}}>
                                {record.nic_portgroup_name}_{index + 1}
                                {index + 1 < record.port_num && <>,</>}
                            </span>
                        ))}
                    </>
                );
            }
        }
    ];
    const updatePanel = () => {
        const totalPorts = form.getFieldValue("total_ports");
        // 当 total_ports 为空或无效时，不渲染
        if (!totalPorts || totalPorts < 1) return null;

        // 封装生成端口元素的逻辑
        const generatePortElements = () => {
            const portElements = [];
            let portIndex = 0;

            data.forEach(group => {
                const legendClass = `legend_${group.speed}G`;
                for (let i = 0; i < group.port_num; i++) {
                    if (portIndex < totalPorts) {
                        portElements.push(
                            <li key={portIndex}>
                                <div className={`${styles.legend_default} ${styles[legendClass]}`}>{portIndex + 1}</div>
                            </li>
                        );
                        portIndex++;
                    }
                }
                if (portIndex < totalPorts) {
                    portElements.push(
                        <hr
                            key={`divider-${portIndex}`}
                            style={{
                                width: 1,
                                height: 24,
                                background: "#B2B2B2",
                                margin: "0 3px"
                            }}
                        />
                    );
                }
            });

            // 填充剩余端口
            while (portIndex < totalPorts) {
                portElements.push(
                    <li key={portIndex}>
                        <div className={styles.legend_default}>{portIndex + 1}</div>
                    </li>
                );
                portIndex++;
            }

            return portElements;
        };

        return (
            <ul className={`${styles.portLegend} ${styles.CustomNodeModal}`} style={{minHeight: 20}}>
                {generatePortElements()}
            </ul>
        );
    };

    // 行点击事件处理函数
    const handleRowClick = (record, rowIndex) => {
        const isCurrentRowSelected = selectedRowKeys === (record.key || record.nic_portgroup_name);
        if (isCurrentRowSelected) {
            setSelectedRowKeys(null);
            setSelectedRowData(null);
            form.resetFields(["port_num", "speed"]);
        } else {
            setSelectedRowKeys(record.key || record.nic_portgroup_name);
            setSelectedRowData(record);
            const formData = {...form.getFieldValue()};
            formData.port_num = record.port_num;
            formData.speed = record.speed;
            form.setFieldsValue(formData);
        }
    };
    // 配置行属性
    const onRow = (record, rowIndex) => {
        const rowKey = record.key || record.nic_portgroup_name;
        return {
            onClick: () => handleRowClick(record, rowIndex),
            style: {
                backgroundColor: rowKey === selectedRowKeys ? "#F8FAFB" : "transparent"
            }
        };
    };

    const updateDataTable = currentRowData => {
        setData(prevData =>
            prevData.map(item =>
                item.nic_portgroup_name === currentRowData.nic_portgroup_name ? currentRowData : item
            )
        );
    };
    const generateUniquePortGroupName = (existingData, templateName) => {
        let counter = 1;
        while (true) {
            const possibleName = `${templateName}_${counter}`;
            const isDuplicate = existingData.some(item => item.nic_portgroup_name === possibleName);
            if (!isDuplicate) {
                return possibleName;
            }
            counter++;
        }
    };
    /**
     * Create Port Group
     * @returns
     */
    const addPortGroup = () => {
        const formValue = form.getFieldsValue();
        if (!formValue.template_name) {
            message.error("Please enter a valid Node Template Name");
            return;
        }
        const {total_ports} = formValue;
        const {port_num} = formValue;
        const reducePortNum = data.reduce((sum, item) => sum + item.port_num, 0);
        if (reducePortNum + port_num > total_ports) {
            message.error("Port Count exceeds the maximum number of assignable ports");
            return;
        }

        if (!formValue.speed) {
            message.error("Please select speed");
            return;
        }
        const uniqueName = generateUniquePortGroupName(data, formValue.template_name);
        const addPortGroupData = {
            nic_portgroup_name: uniqueName,
            speed: formValue.speed,
            port_num: formValue.port_num
        };
        setData([...data, addPortGroupData]);

        form.setFieldsValue({port_num: "", speed: ""});
        setSelectedRowKeys([]);
        setSelectedRowData(null);
    };

    /**
     * update Port Group
     * @returns
     */
    const updataPortGroup = () => {
        const formData = form.getFieldValue();
        const {total_ports} = formData;
        const {port_num} = formData;
        const filterData = data.filter(obj => obj.nic_portgroup_name !== selectedRowData.nic_portgroup_name);
        const reducePortNum = filterData.reduce((sum, item) => sum + item.port_num, 0);
        if (reducePortNum + port_num > total_ports) {
            message.error("Port Count exceeds the maximum number of assignable ports");
            return;
        }
        updateDataTable({
            nic_portgroup_name: selectedRowData.nic_portgroup_name,
            speed: formData.speed,
            port_num: formData.port_num
        });
        // setSelectedRowKeys([]);
        // setSelectedRowData(null);
    };

    /**
     * Delete Port Group
     * @returns
     */
    const deleteCustomNode = () => {
        if (!selectedRowData) {
            message.error("Please select a NIC Port Group first");
            return;
        }
        setData(prevData => prevData.filter(item => item.nic_portgroup_name !== selectedRowData.nic_portgroup_name));
        setSelectedRowKeys([]);
        setSelectedRowData(null);
        form.setFieldsValue({port_num: "", speed: ""});
    };

    const handleOk = async () => {
        const formData = form.getFieldValue();
        const reducePortNum = data.reduce((sum, item) => sum + item.port_num, 0);
        if (reducePortNum > formData.total_ports) {
            message.error("Exceeding the maximum number of assignable ports");
            return;
        }
        const submitData = {
            id: formData.id,
            template_name: formData.template_name,
            total_ports: formData.total_ports,
            template_info:
                data?.reduce((acc, item) => {
                    const {nic_portgroup_name, ...rest} = item;
                    acc[nic_portgroup_name] = rest;
                    return acc;
                }, {}) || {}
        };
        await onSubmit(submitData);
    };

    useEffect(() => {
        if (isCustomNodeTemplateModal) {
            form.resetFields();
            form.setFieldsValue({});
            setData([]);
            setSelectedRowKeys([]);
            setSelectedRowData(null);

            if (title === "Edit Node Template") {
                const convertedArray = Object.entries(currentCustomNodeTemplate.template_info).map(([key, value]) => ({
                    nic_portgroup_name: key,
                    port_num: value.port_num,
                    speed: value.speed
                }));
                setData(convertedArray);
                form.setFieldsValue(currentCustomNodeTemplate);
                setIsDisabledEdit(currentCustomNodeTemplate?.is_used);
            } else {
                // setCurrentCustomNodeTemplate({});
                setIsDisabledEdit(false);
            }
        }
    }, [form, currentCustomNodeTemplate, isCustomNodeTemplateModal]);

    return (
        <Space>
            <Modal
                className="ampcon-middle-modal"
                title={
                    <div>
                        {title}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={isCustomNodeTemplateModal}
                onCancel={onCancel}
                footer={
                    <>
                        <Divider style={{marginBottom: "20px", marginTop: "0px"}} />
                        <Row justify="end">
                            <Space>
                                <Button onClick={onCancel}> Cancel </Button>
                                {!isDisabledEdit && (
                                    <Button type="primary" onClick={handleOk}>
                                        Apply
                                    </Button>
                                )}
                            </Space>
                        </Row>
                    </>
                }
            >
                <Form
                    form={form}
                    labelAlign="left"
                    rootClassName={styles.customNodeForm}
                    style={{minHeight: "267.23px"}}
                >
                    <Form.Item
                        name="template_name"
                        label="Node Template Name"
                        labelCol={{style: {width: 178}}}
                        className={styles.nodeItem}
                        rules={[
                            {required: true, message: "Please enter the node template name!"},
                            {max: 64, message: "Enter a maximum of 64 characters"},
                            {
                                validator: (_, value) => {
                                    if (value === "All") {
                                        return Promise.reject(new Error("Please input a valid node template name!"));
                                    }
                                    if (value.trim() !== value) {
                                        return Promise.reject(
                                            new Error("Node template name should not have leading or trailing spaces.")
                                        );
                                    }
                                    if (!NAME_MATCH_REGEX.test(value)) {
                                        return Promise.reject(
                                            new Error(
                                                "Node template  name can only contain letters, numbers, underscores, hyphens and spaces."
                                            )
                                        );
                                    }
                                    return Promise.resolve();
                                }
                            }
                        ]}
                    >
                        <Input
                            className={styles.formWidth}
                            disabled={isDisabledEdit}
                            onChange={e => {
                                const updatedData = data.map(item => {
                                    const match = item.nic_portgroup_name.match(/_(\d+)$/);
                                    const suffix = match ? match[1] : "";

                                    return {
                                        ...item,
                                        nic_portgroup_name: `${e.target.value}_${suffix}`
                                    };
                                });

                                setData(updatedData);
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        name="total_ports"
                        label="Total NIC Port Number"
                        labelCol={{style: {width: 178}}}
                        className={styles.nodeItem}
                        rules={[{required: true, message: "Please enter the total nic port number!"}]}
                    >
                        <InputNumber
                            min={1}
                            max={16}
                            disabled={isDisabledEdit}
                            className={styles.formWidth}
                            placeholder="Range (1-16)"
                            onChange={value => {
                                form.setFieldsValue({total_ports: value});
                                updatePanel();
                            }}
                        />
                    </Form.Item>

                    {!isDisabledEdit && (
                        <>
                            <p style={{fontSize: "18px", color: "#212519", fontWeight: 600}}>
                                {selectedRowData ? "Edit" : "Add"} NIC Port Group
                            </p>
                            <Form.Item
                                name="port_num"
                                label="Port Count"
                                labelCol={{style: {width: 178}}}
                                className={styles.nodeItem}
                            >
                                <InputNumber min={1} className={styles.formWidth} />
                            </Form.Item>
                            <Form.Item
                                name="speed"
                                label="Port Group Speed"
                                labelCol={{style: {width: 178}}}
                                className={styles.nodeItem}
                            >
                                <Select style={{width: 280}}>
                                    <Select.Option value={1} key={1}>
                                        1Gbps
                                    </Select.Option>
                                    <Select.Option value={10} key={10}>
                                        10Gbps
                                    </Select.Option>
                                    <Select.Option value={25} key={25}>
                                        25Gbps
                                    </Select.Option>
                                    <Select.Option value={40} key={40}>
                                        40Gbps
                                    </Select.Option>
                                    <Select.Option value={100} key={100}>
                                        100Gbps
                                    </Select.Option>
                                    <Select.Option value={200} key={200}>
                                        200Gbps
                                    </Select.Option>
                                    <Select.Option value={400} key={400}>
                                        400Gbps
                                    </Select.Option>
                                </Select>
                            </Form.Item>
                            <div style={{paddingLeft: 178}} className={styles.btnGroup}>
                                {!selectedRowData && (
                                    <Button type="primary" onClick={addPortGroup}>
                                        Create Port Group
                                    </Button>
                                )}

                                {selectedRowData && (
                                    <>
                                        <Button type="primary" onClick={updataPortGroup}>
                                            Update Port Group
                                        </Button>
                                        <Button onClick={deleteCustomNode}>Delete Port Group</Button>
                                    </>
                                )}
                            </div>
                        </>
                    )}

                    <Form.Item
                        name=""
                        label="Panel"
                        labelCol={{style: {display: "inline-block", width: 178}}}
                        wrapperCol={{style: {display: "inline-block"}}}
                        className={styles.linkItem}
                    >
                        {updatePanel()}
                    </Form.Item>

                    <Table
                        bordered
                        columns={columns}
                        dataSource={data}
                        onRow={onRow}
                        rowKey={record => record.key || record.nic_portgroup_name}
                        pagination={false}
                    />
                </Form>
            </Modal>
        </Space>
    );
};
export default CustomNodeTemplateModal;
