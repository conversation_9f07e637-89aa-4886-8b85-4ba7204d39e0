import {Button, Flex, message, Spin, Card, Table} from "antd";
import {useEffect, useRef, useState} from "react";
import {
    AmpConCustomTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown,
    GlobalSearchInput
} from "@/modules-ampcon/components/custom_table";
import {deleteImage, queryImages, getUpgradeStatus, cancelScheduleTask} from "@/modules-ampcon/apis/upgrade_api";
import Icon from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/upgrade_management_switch.module.scss";
import AddImageModal from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/add_image_modal";
import LogViewTextareaModal from "@/modules-ampcon/components/log_view_textarea_modal";
import BatchSwitchUpgradeModal from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/batch_switch_upgrade_modal";
import {refreshSvg, updateWhiteSvg, updateGreySvg} from "@/utils/common/iconSvg";
import {useSelector} from "react-redux";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {useTableInitialElement} from "@/modules-ampcon/hooks/useModalTable";
import {useGetDevices, useGetDeviceCount} from "@/modules-smb/hooks/Network/Devices";
import {getRevision} from "@/modules-smb/helpers/stringHelper";
import {useGetInventoryTags} from "@/modules-smb/hooks/Network/Inventory";

const APUpgrade = () => {
    const imageTableMatchFieldsList = [
        {name: "image_name", matchMode: "fuzzy"},
        {name: "model", matchMode: "fuzzy"}
    ];

    const imageTableSearchFieldsList = ["image_name", "model"];

    const addImageModalRef = useRef(null);
    const logViewTextareaModalRef = useRef(null);
    const batchSwitchUpgradeModal = useRef(null);
    const imageTableRef = useRef(null);
    const switchListTableRef = useRef(null);

    const [selectedImage, setSelectedImage] = useState([]);
    const [isShowSpin, setIsShowSpin] = useState(false);
    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);

    return (
        <>
            {/* <Card style={{flex: 1, overflowY: "auto"}}> */}
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            <div>
                <AddImageModal
                    saveCallback={() => {
                        imageTableRef.current.refreshTable();
                    }}
                    ref={addImageModalRef}
                />
                <LogViewTextareaModal ref={logViewTextareaModalRef} />
                <BatchSwitchUpgradeModal
                    saveCallback={() => {
                        switchListTableRef.current.clearSelectedRow();
                        switchListTableRef.current.refreshTable();
                    }}
                    ref={batchSwitchUpgradeModal}
                />
            </div>
            <Flex vertical className={styles.container_software}>
                <span
                    style={{
                        fontSize: "18px",
                        paddingBottom: "12px",
                        borderBottom: "1px solid rgb(242, 242, 242)",
                        marginBottom: "24px",
                        fontWeight: 700
                    }}
                >
                    Software
                </span>
                <AmpConCustomTable
                    ref={imageTableRef}
                    rowSelection={{
                        type: "radio",
                        selectedRowKeys,
                        selectedRows,
                        onChange: (currentSelectedRowKeys, currentSelectedRows) => {
                            setSelectedRowKeys(currentSelectedRowKeys);
                            setSelectedRows(currentSelectedRows);
                            setSelectedImage(currentSelectedRows);
                            switchListTableRef.current?.clearSelectedRow();
                        }
                    }}
                    extraButton={
                        <>
                            {userType === "superuser" ? (
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        addImageModalRef.current.showAddImageModal("ap");
                                    }}
                                    icon={<Icon component={updateWhiteSvg} />}
                                >
                                    Upload
                                </Button>
                            ) : null}
                            <Button
                                htmlType="button"
                                onClick={() => {
                                    imageTableRef.current.refreshTable();
                                    message.success("Image table refresh success.");
                                }}
                            >
                                <Icon component={refreshSvg} />
                                Refresh
                            </Button>
                        </>
                    }
                    columns={[
                        createColumnConfigMultipleParams({
                            title: "Image",
                            dataIndex: "image_name",
                            filterDropdownComponent: TableFilterDropdown,
                            width: "20%"
                        }),
                        createColumnConfigMultipleParams({
                            title: "Model",
                            dataIndex: "model",
                            filterDropdownComponent: TableFilterDropdown,
                            width: "12%"
                        }),
                        createColumnConfigMultipleParams({
                            title: "Version",
                            dataIndex: "version",
                            enableFilter: false,
                            enableSorter: false,
                            width: "12%"
                        }),
                        createColumnConfigMultipleParams({
                            title: "Revision",
                            dataIndex: "revision",
                            enableFilter: false,
                            enableSorter: false,
                            width: "12%"
                        }),
                        createColumnConfigMultipleParams({
                            title: "Create Time",
                            dataIndex: "create_time",
                            enableFilter: false,
                            enableSorter: true,
                            width: "12%"
                        }),
                        createColumnConfigMultipleParams({
                            title: "Update Time",
                            dataIndex: "modified_time",
                            enableFilter: false,
                            enableSorter: true,
                            width: "12%",
                            defaultSortOrder: "descend"
                        }),
                        createColumnConfigMultipleParams({
                            title: "Operation",
                            enableFilter: false,
                            enableSorter: false,
                            render: (_, record) => {
                                return userType === "superuser" ? (
                                    <Flex
                                        style={{flexWrap: "wrap", columnGap: "24px", rowGap: "5px"}}
                                        className="actionLink"
                                    >
                                        <a
                                            onClick={() => {
                                                confirmModalAction(
                                                    `This action will delete Image:${record.image_name}, Do you want to continue?`,
                                                    () => {
                                                        setIsShowSpin(true);
                                                        try {
                                                            deleteImage(record.id).then(res => {
                                                                if (res.status !== 200) {
                                                                    message.error(res.info);
                                                                } else {
                                                                    message.success(res.info);
                                                                    // 判断当前选中镜像是否是被删除的
                                                                    if (
                                                                        selectedImage.length > 0 &&
                                                                        selectedImage[0].id === record.id
                                                                    ) {
                                                                        setSelectedImage([]); // 是的话置空选中iamge
                                                                    }
                                                                    imageTableRef.current.refreshTable();
                                                                }
                                                            });
                                                        } catch (e) {
                                                            message.error(
                                                                "An error occurred during the process of delete"
                                                            );
                                                        } finally {
                                                            setIsShowSpin(false);
                                                        }
                                                    }
                                                );
                                            }}
                                        >
                                            Delete
                                        </a>
                                    </Flex>
                                ) : null;
                            }
                        })
                    ]}
                    matchFieldsList={imageTableMatchFieldsList}
                    searchFieldsList={imageTableSearchFieldsList}
                    buttonProps={[]}
                    fetchAPIInfo={queryImages}
                />
            </Flex>
            <Flex vertical className={styles.container_software}>
                <span
                    style={{
                        fontSize: "18px",
                        paddingBottom: "12px",
                        borderBottom: "1px solid rgb(242, 242, 242)",
                        marginBottom: "24px",
                        fontWeight: 700
                    }}
                >
                    AP
                </span>
                <WirelessDeviceTable selectedImage={selectedImage} />
            </Flex>
            {/* </Card> */}
            <div />
        </>
    );
};

export const WirelessDeviceTable = ({selectedImage}) => {
    const logViewTextareaModalRef = useRef(null);
    const batchSwitchUpgradeModal = useRef(null);
    const [isUpgradeDisabled, setIsUpgradeDisabled] = useState(true);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [sorter, setSorter] = useState({field: null, order: null});
    const [originalData, setOriginalData] = useState([]);
    const [shouldRefetch, setShouldRefetch] = useState(false);
    const [isShowSpin, setIsShowSpin] = useState(false);

    const [
        selectModalOpen,
        setSelectModalOpen,
        searchFields,
        setSearchFields,
        data,
        setData,
        loading,
        setLoading,
        pagination,
        setPagination
    ] = useTableInitialElement([], false);

    const {data: deviceCountData, refetch: countRefetch} = useGetDeviceCount("ap", selectedImage[0]?.model); // ap可选
    const count = deviceCountData?.count ?? 0;
    const {data: getDevicesData, refetch: originalRefetch} = useGetDevices({
        pageInfo: {limit: count, offset: (pagination.current - 1) * pagination.pageSize},
        enabled: true,
        platform: "ap", // ap 可筛选 ,all展示全部不可筛选
        model: selectedImage[0]?.model
    });
    const {data: getEntityData = [], refetch: entityRefetch} = useGetInventoryTags({
        enabled: true,
        pageInfo: {limit: count, index: 0},
        sortInfo: [],
        onlyUnassigned: false,
        isSubscribersOnly: false
    });
    // if use refetch，force use useEffect
    const refetch = () => {
        countRefetch();
        originalRefetch();
        entityRefetch();
        setShouldRefetch(prev => !prev);
    };

    useEffect(() => {
        const rawData = getDevicesData?.devicesWithStatus || [];
        if (rawData.length === 0) {
            setData([]);
            setOriginalData([]);
            setLoading(false);
            setIsUpgradeDisabled(true);
            return;
        }
        // console.log("useGetDevice返回的数据：", rawData);
        const entityExtendedInfo = {};
        getEntityData.forEach(item => {
            if (item.serialNumber) {
                entityExtendedInfo[item.serialNumber] = item.extendedInfo;
            }
        });
        // console.log(entityExtendedInfo);
        const serialNumbersOnly = rawData.map(device => device.serialNumber).filter(Boolean);
        const serialNumberString = serialNumbersOnly.join(",");
        let cancelled = false;
        setLoading(true);
        getUpgradeStatus(serialNumberString)
            .then(response => {
                if (cancelled) return;
                const upgradeData = response || [];
                const upgradeMap = {};
                upgradeData.forEach(item => {
                    if (item.sn) {
                        upgradeMap[item.sn] = {
                            upgrade_type: item.upgrade_type,
                            upgrade_status: item.upgrade_status,
                            upgrade_time: item.upgrade_time
                        };
                    }
                });
                // console.log("升级状态：", upgradeMap);
                const mergedData = rawData.map(device => {
                    const upgradeInfo = upgradeMap[device.serialNumber] || {};
                    const extendedInfo = entityExtendedInfo[device.serialNumber] || {};
                    return {
                        ...device,
                        upgrade_type: upgradeInfo.upgrade_type ?? null,
                        upgrade_status: upgradeInfo.upgrade_status ?? null,
                        upgrade_time: upgradeInfo.upgrade_time ?? null,
                        entity: extendedInfo?.venue?.name || ""
                    };
                });
                // console.log("add upgrade schedule：", mergedData);
                setOriginalData(mergedData);
                setLoading(false);
                setIsUpgradeDisabled(selectedImage.length === 0);
            })
            .catch(() => {
                if (!cancelled) {
                    setLoading(false);
                    setIsUpgradeDisabled(true);
                }
            });
        return () => {
            cancelled = true;
        };
    }, [getDevicesData, getEntityData, selectedImage, shouldRefetch, sorter]);

    // sorter
    useEffect(() => {
        if (!originalData.length) {
            setData([]);
            return;
        }
        if (!sorter.field || !sorter.order) {
            setData(originalData);
        } else {
            const sorted = sortData(originalData, sorter);
            setData(sorted);
        }
    }, [sorter, originalData]);
    // console.log("table display data：", data);

    // ap table raw selection
    const allowedStatuses = [0, 1, 2, undefined, null];
    useEffect(() => {
        setSelectedRowKeys([]);
        setSelectedRows([]);
    }, [selectedImage]);
    const rowSelection = {
        selectedRowKeys,
        onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRows(rows);
        },
        getCheckboxProps: record => ({
            disabled: selectedImage.length === 0 || !allowedStatuses.includes(record.upgrade_status)
        })
    };

    const handleTableChange = (newPagination, filters, sorterObj) => {
        setPagination(newPagination);
        if (!sorterObj.order) {
            setSorter({field: null, order: null});
        } else {
            setSorter({field: sorterObj.field, order: sorterObj.order});
        }
        refetch();
    };
    // sort
    const sortData = (data, sorter) => {
        const {field, order} = sorter;
        const direction = order === "ascend" ? 1 : -1;

        return [...data].sort((a, b) => {
            const aVal = a[field]?.toString() ?? "";
            const bVal = b[field]?.toString() ?? "";

            return aVal.localeCompare(bVal, "zh-Hans-CN", {numeric: true}) * direction;
        });
    };
    // search
    const handleSearchChange = e => {
        const {value} = e.target;
        setSearchFields({fields: [], value});
        const raw = originalData;
        if (value) {
            const filteredData = raw.filter(item =>
                Object.values(item).some(v =>
                    typeof v === "string" ? v.toLowerCase().includes(value.toLowerCase()) : false
                )
            );
            const sorted = sorter.field && sorter.order ? sortData(filteredData, sorter) : filteredData;
            setData(sorted);
        } else {
            const fallbackData = sorter.field && sorter.order ? sortData(raw, sorter) : raw;
            setData(fallbackData);
        }
    };
    // bantch upgrade
    const handlebantchUpgrade = () => {
        if (selectedRowKeys.length === 0) {
            message.error("Please select a device first");
            return;
        }
        batchSwitchUpgradeModal.current.showBatchSwitchUpgradeModal(selectedImage, {
            tableSelectedRowKey: selectedRowKeys,
            tableSelectedRows: selectedRows,
            ap: "ap"
        });
        refetch();
    };

    const columns = [
        {title: "Name", dataIndex: "name", key: "name", sorter: true},
        {title: "SN", dataIndex: "serialNumber", key: "serialNumber", sorter: true},
        {title: "IP", dataIndex: "ipAddress", key: "ipAddress", sorter: true},
        // {title: "Model", dataIndex: "manufacturer", key: "manufacturer", sorter: true},
        {title: "Model", dataIndex: "compatible", key: "compatible", sorter: true},
        {title: "Entitiy", dataIndex: "entity", key: "entity", sorter: true},
        {
            title: "Version",
            dataIndex: "firmware",
            key: "firmware",
            sorter: true,
            render: text => getRevision(text)
        },
        {
            title: "Upgrade Status",
            dataIndex: "upgrade_status",
            key: "upgrade_status",
            render: (_, record) => {
                switch (record.upgrade_status) {
                    case 0:
                        return "---";
                    case 1:
                        return "upgraded";
                    case 2:
                        return "upgrade failed";
                    case 3:
                        return "upgrade scheduled";
                    case 4:
                        return "upgrading";
                    default:
                        return "---";
                }
            }
        },
        {
            title: "Operation",
            render: (_, record) => (
                <Flex style={{flexWrap: "wrap", columnGap: "24px", rowGap: "5px"}} className="actionLink">
                    <a
                        onClick={() =>
                            logViewTextareaModalRef.current.showLogViewTextareaModal(record.serialNumber, "ap")
                        }
                        style={{display: "block", margin: "2px 0"}}
                    >
                        Log
                    </a>
                    {record.upgrade_type === 1 && record.upgrade_status === 3 && (
                        <a
                            onClick={() => {
                                confirmModalAction(
                                    `The scheduled ${record.upgrade_time}, upgrade for device ${record.serialNumber} is going to be canceled?`,
                                    () => {
                                        setIsShowSpin(true);
                                        try {
                                            cancelScheduleTask(record.serialNumber).then(res => {
                                                if (res.status !== 200) {
                                                    message.error(res.info);
                                                } else {
                                                    message.success(res.info);
                                                    refetch();
                                                }
                                            });
                                        } catch (e) {
                                            message.error(
                                                "An error occurred during the process of canceling the scheduled task."
                                            );
                                        } finally {
                                            setIsShowSpin(false);
                                        }
                                    }
                                );
                            }}
                            style={{display: "block", margin: "2px 0"}}
                        >
                            Stop Schedule
                        </a>
                    )}
                </Flex>
            )
        }
    ];
    return (
        <div>
            <LogViewTextareaModal ref={logViewTextareaModalRef} />
            <BatchSwitchUpgradeModal
                saveCallback={() => {
                    setSelectedRowKeys([]);
                    setSelectedRows([]);
                    refetch();
                }}
                ref={batchSwitchUpgradeModal}
            />
            <Flex vertical>
                <Flex gap="middle" style={{marginBottom: "20px"}}>
                    <Button
                        type="primary"
                        onClick={handlebantchUpgrade}
                        disabled={isUpgradeDisabled}
                        icon={
                            isUpgradeDisabled ? <Icon component={updateGreySvg} /> : <Icon component={updateWhiteSvg} />
                        }
                    >
                        Upgrade
                    </Button>
                    <Button
                        htmlType="button"
                        onClick={() => {
                            refetch();
                            message.success("Wireless device table refreshed successfully.");
                        }}
                    >
                        <Icon component={refreshSvg} />
                        Refresh
                    </Button>
                    <div style={{flexGrow: 1}} />
                    <GlobalSearchInput onChange={handleSearchChange} />
                </Flex>
                <Table
                    rowSelection={rowSelection}
                    columns={columns}
                    dataSource={data}
                    bordered
                    rowKey={record => record.serialNumber}
                    pagination={{
                        ...pagination,
                        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
                        showSizeChanger: true,
                        pageSizeOptions: ["10", "20", "50", "100"]
                    }}
                    loading={loading}
                    onChange={handleTableChange}
                />
            </Flex>
        </div>
    );
};

export default APUpgrade;
