import {Button, Descriptions, Divider, Form, Input, message, Modal, Tooltip, DatePicker, Radio} from "antd";
import {forwardRef, useImperativeHandle, useState, useEffect} from "react";
import dayjs from "dayjs";
import {InboxOutlined, QuestionCircleOutlined} from "@ant-design/icons";
import <PERSON>agger from "antd/es/upload/Dragger";
import {batchUpgradeSwitch, scheduleUpgradeSwitch} from "@/modules-ampcon/apis/rma_api";
import {batchUpgradeAP} from "@/modules-ampcon/apis/upgrade_api"; // wireless upgrade

const BatchSwitchUpgradeModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showBatchSwitchUpgradeModal: (useImageInfo, selectedSwitchListInfo) => {
            if (useImageInfo.length === 0) {
                return;
            }
            setUseImageInfo(useImageInfo[0]);
            setSelectedSwitchListInfo(selectedSwitchListInfo.tableSelectedRows);
            batchUpgradeForm.setFieldValue("usedImage", useImageInfo[0].image_name);
            setIsShowModal(true);
            setSourceType(selectedSwitchListInfo.ap ?? null);
        },
        hideBatchSwitchUpgradeModal: () => {
            setIsShowModal(false);
            resetModal();
        }
    }));

    const title = "Upgrade";
    const usedImageLabel = "Use Image";
    const selectedSwitchLabel = "Switches for Upgrade";
    const selectedAPLabel = "AP for Upgrade";
    const scriptLabel = "Scripts";
    const scheduleLabel = "Schedule";

    const scriptTooltip =
        "Use this field to specify an optional post-upgrade script file. Click on + to add more than one script file";
    const upgradeTime = "Upgrade Time";

    const {saveCallback} = props;

    const [batchUpgradeForm] = Form.useForm();

    const [isShowModal, setIsShowModal] = useState(false);
    const [useImageInfo, setUseImageInfo] = useState({});
    const [selectedSwitchListInfo, setSelectedSwitchListInfo] = useState([]);
    const [uploadFileList, setUploadFileList] = useState([]);
    const [scheduleType, setScheduleType] = useState("now");

    const [upgradeDate, setUpgradeDate] = useState(null);

    const {RangePicker} = DatePicker;

    const [sourceType, setSourceType] = useState(null);
    const [scheduleOption, setScheduleOption] = useState("0");
    const [selectedTime, setSelectedTime] = useState("");
    useEffect(() => {
        if (scheduleOption === "0") {
            const formattedNow = dayjs().format("YYYY-MM-DD HH:mm:ss");
            setSelectedTime(formattedNow);
        }
    }, [scheduleOption]);
    const handleScheduleChange = e => {
        setScheduleOption(e.target.value);
    };

    const resetModal = () => {
        batchUpgradeForm.resetFields();
        setScheduleType("now");
        setUpgradeDate(null);
        setScheduleOption("0");
        setUploadFileList([]);
    };

    const disabledDate = current => {
        return current && current.isBefore(dayjs().subtract(1, "day").startOf("day"));
    };

    const handleUpgradeTimeChange = (date, dateString) => {
        setUpgradeDate(dateString);
    };

    const handleScheduleTypeChange = e => {
        const newType = e.target.value;
        setScheduleType(newType);

        if (newType === "now") {
            setUpgradeDate(null);
            batchUpgradeForm.setFieldsValue({
                upgradeTime: null
            });
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                resetModal();
            }}
            footer={
                <>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            resetModal();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={async () => {
                            if (sourceType !== "ap") {
                                try {
                                    await batchUpgradeForm.validateFields();
                                } catch (errorInfo) {
                                    message.error("Please input valid data");
                                    return;
                                }

                                let startDate = null;

                                if (scheduleType === "schedule" && upgradeDate) {
                                    startDate = upgradeDate.format("YYYY-MM-DD HH:mm");
                                }

                                if (scheduleType === "now") {
                                    batchUpgradeSwitch(
                                        useImageInfo.image_name,
                                        selectedSwitchListInfo.map(switchItem => switchItem.sn),
                                        uploadFileList
                                    ).then(response => {
                                        if (response.status !== 200) {
                                            message.error(response.info);
                                        } else {
                                            message.success(response.info);
                                            setIsShowModal(false);
                                            resetModal();
                                            saveCallback();
                                        }
                                    });
                                } else {
                                    scheduleUpgradeSwitch(
                                        useImageInfo.image_name,
                                        selectedSwitchListInfo.map(switchItem => switchItem.sn),
                                        uploadFileList,
                                        startDate
                                    ).then(response => {
                                        if (response.status !== 200) {
                                            message.error(response.info);
                                        } else {
                                            message.success(response.info);
                                            setIsShowModal(false);
                                            resetModal();
                                            saveCallback();
                                        }
                                    });
                                }
                            } else if (sourceType === "ap") {
                                // await handleUpgradeAP();
                                batchUpgradeAP(
                                    selectedSwitchListInfo.map(switchItem => switchItem.serialNumber),
                                    useImageInfo.id,
                                    scheduleOption,
                                    selectedTime
                                )
                                    .then(response => {
                                        if (response.status !== 200) {
                                            message.error(response.info);
                                        } else {
                                            message.success(response.info);
                                            resetModal();
                                            saveCallback();
                                        }
                                    })
                                    .catch(() => {
                                        message.error("Upgrade failed. Please try again.");
                                    })
                                    .finally(() => {
                                        setIsShowModal(false);
                                    });
                            } else {
                                message.error("No valid serial numbers or SNs found.");
                                setIsShowModal(false);
                            }
                        }}
                    >
                        Upgrade
                    </Button>
                </>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 7}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={batchUpgradeForm}
                style={{minHeight: "260.23px"}}
            >
                <Form.Item name="usedImage" label={usedImageLabel}>
                    <Input style={{backgroundColor: "#f0f0f0"}} readOnly />
                </Form.Item>

                {sourceType !== "ap" && (
                    <>
                        <Form.Item label={selectedSwitchLabel}>
                            <Descriptions bordered size="small" column={1}>
                                {selectedSwitchListInfo.map(switchItem => (
                                    <Descriptions.Item label={switchItem.host_name}>{switchItem.sn}</Descriptions.Item>
                                ))}
                            </Descriptions>
                        </Form.Item>
                        <Form.Item
                            label={
                                <>
                                    <span style={{marginRight: "10px"}}>{scriptLabel}</span>
                                    <Tooltip title={scriptTooltip}>
                                        <QuestionCircleOutlined className="questioncircle-color" />
                                    </Tooltip>
                                </>
                            }
                            // rules={[{required: true}]}
                        >
                            <Dragger
                                name="file"
                                beforeUpload={file => {
                                    const isAlreadyExist = uploadFileList.some(item => item.name === file.name);
                                    const isContainPostXorplus = uploadFileList.some(
                                        file => file.name === "post-xorplus"
                                    );
                                    if (isAlreadyExist) {
                                        message.error(`Script file named ${file.name} is already exist`);
                                        return false;
                                    }
                                    if (!isContainPostXorplus && file.name !== "post-xorplus") {
                                        message.error(`Script file named post-xorplus is required`);
                                        return false;
                                    }
                                    setUploadFileList([...uploadFileList, file]);
                                    return false;
                                }}
                                fileList={uploadFileList}
                                multiple
                                onRemove={file => {
                                    setUploadFileList(uploadFileList.filter(item => item.uid !== file.uid));
                                }}
                            >
                                <p className="ant-upload-drag-icon">
                                    <InboxOutlined />
                                </p>
                                <p className="ant-upload-text">Click or drag file to this area to upload</p>
                                <p className="ant-upload-hint">Support for a bulk upload.</p>
                            </Dragger>
                        </Form.Item>
                        {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS" && (
                            <Form.Item
                                label="Schedule Type"
                                name="scheduleType"
                                initialValue="now"
                                onChange={handleScheduleTypeChange}
                            >
                                <Radio.Group
                                    options={[
                                        {value: "now", label: "Upgrade Now"},
                                        {value: "schedule", label: "Schedule Upgrade"}
                                    ]}
                                />
                            </Form.Item>
                        )}
                        {scheduleType === "schedule" ? (
                            <Form.Item
                                label={upgradeTime}
                                name="upgradeTime"
                                rules={[
                                    {
                                        validator: (_, value) => {
                                            if (!upgradeDate) {
                                                return Promise.reject(new Error("Please select upgrade time"));
                                            }
                                            return Promise.resolve();
                                        }
                                    }
                                ]}
                            >
                                <DatePicker
                                    value={upgradeDate}
                                    placeholder="Start Time"
                                    style={{width: "100%"}}
                                    disabledDate={disabledDate}
                                    showTime={{format: "YYYY-MM-DD HH:mm"}}
                                    format="YYYY-MM-DD HH:mm"
                                    showNow={false}
                                    onChange={handleUpgradeTimeChange}
                                />
                                {/* <RangePicker
                            value={rangePickerValue}
                            style={{width: "100%"}}
                            disabledDate={disabledDate}
                            showTime={{format: "YYYY-MM-DD HH:mm"}}
                            format="YYYY-MM-DD HH:mm"
                            onChange={handleUpgradeTimeChange}
                        /> */}
                            </Form.Item>
                        ) : null}
                    </>
                )}
                {sourceType === "ap" && (
                    <>
                        <Form.Item label={selectedAPLabel}>
                            <Descriptions bordered size="small" column={1}>
                                {selectedSwitchListInfo.map(
                                    switchItem =>
                                        switchItem.serialNumber && (
                                            <Descriptions.Item label="Serial Number">
                                                {switchItem.serialNumber}
                                            </Descriptions.Item>
                                        )
                                )}
                            </Descriptions>
                        </Form.Item>
                        <Form.Item label={scheduleLabel}>
                            <div style={{display: "flex", alignItems: "center"}}>
                                <Radio.Group value={scheduleOption} onChange={handleScheduleChange}>
                                    <Radio value="0">Now</Radio>
                                    <Radio value="1">Later</Radio>
                                </Radio.Group>
                                {scheduleOption === "1" && (
                                    <DatePicker
                                        showTime={{format: "HH:mm:ss", showSecond: true}}
                                        format="YYYY-MM-DD HH:mm:ss"
                                        style={{height: "32px", marginLeft: "32px"}}
                                        value={selectedTime ? dayjs(selectedTime, "YYYY-MM-DD HH:mm:ss") : null}
                                        onChange={(_, dateString) => setSelectedTime(dateString)}
                                        disabledDate={current => {
                                            return current && current.isBefore(dayjs().startOf("day"), "day");
                                        }}
                                        disabledTime={current => {
                                            if (!current) return {};
                                            const now = dayjs();
                                            const selectedDate = dayjs(current);
                                            if (selectedDate.isSame(now, "day")) {
                                                const currentHour = now.hour();
                                                const currentMinute = now.minute();
                                                const currentSecond = now.second();
                                                return {
                                                    disabledHours: () => Array.from({length: currentHour}, (_, i) => i),
                                                    disabledMinutes: () =>
                                                        selectedDate.hour() === currentHour
                                                            ? Array.from({length: currentMinute}, (_, i) => i)
                                                            : [],
                                                    disabledSeconds: () =>
                                                        selectedDate.hour() === currentHour &&
                                                        selectedDate.minute() === currentMinute
                                                            ? Array.from({length: currentSecond}, (_, i) => i)
                                                            : []
                                                };
                                            }
                                            return {};
                                        }}
                                    />
                                )}
                            </div>
                        </Form.Item>
                    </>
                )}
            </Form>
        </Modal>
    ) : null;
});

export default BatchSwitchUpgradeModal;
