import React, {useRef, useEffect, useState} from "react";
import LogViewTextareaModal from "@/modules-ampcon/components/log_view_textarea_modal";
import {createColumnConfig, AmpConCustomTable, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {Flex, Space, message} from "antd";
import {confirmModalAction, ViewExportModal} from "@/modules-ampcon/components/custom_modal";
import {LoadGroupForm, ViewReportForm} from "@/modules-ampcon/components/custom_form";
import {
    queryLicenseAction,
    viewReport,
    upgradeGroupLicense,
    upgradeLicense,
    loadGroup
} from "@/modules-ampcon/apis/lifecycle_api";
import {onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";

const ActionButton = ({onSelectGroup}) => {
    const [groups, setGroups] = useState(["All"]);
    const [reportTime, setReportTime] = useState([]);
    const [selectedtime, setSelectedTime] = useState();
    const [isViewReportModalOpen, setIsViewReportModalOpen] = useState(false);

    useEffect(() => {
        const fetchData = async () => {
            const retrieveConfig = await queryLicenseAction();
            setGroups(["All", ...retrieveConfig.data.groups]);
            setReportTime(retrieveConfig.data.reportTimeList);
        };
        fetchData()
            .then(() => {})
            .catch(() => {});
    }, []);

    const handleLicenseAction = async values => {
        await upgradeGroupLicense("action", values.groupName).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success("Starting license action");
            }
        });
    };

    const handleViewReport = values => {
        setIsViewReportModalOpen(true);
        setSelectedTime(values.reportTime);
    };

    return (
        <div>
            <Flex gap="small">
                <LoadGroupForm
                    groups={groups}
                    buttonText="License Action"
                    onSelectChange={onSelectGroup}
                    onFinish={handleLicenseAction}
                    toolTip='Either the user must load a group and then click "License Action" or they can go down in list of switches and click the Action button in its row to perform Action on a single switch'
                />
                <div style={{display: "flex", alignItems: "center"}}>
                    <div style={{marginRight: 8}}>Time Selection</div>
                    <ViewReportForm reportTime={reportTime} onFinish={handleViewReport} />
                </div>
            </Flex>
            <ViewExportModal
                isModalOpen={isViewReportModalOpen}
                onCancel={() => setIsViewReportModalOpen(false)}
                fetchDataAPI={viewReport}
                fetchAPIParams={[selectedtime]}
                modalClass="ampcon-middle-modal"
            />
        </div>
    );
};

const LicenseAction = () => {
    const matchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "host_name", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["sn", "host_name", "mgt_ip", "platform_model"];
    const [selectedGroup, setSelectedGroup] = useState("");
    const LogViewTextareaModalRef = useRef(null);

    const licenseAction = async record => {
        await upgradeLicense("action", record.sn).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success("Starting license action");
            }
        });
    };

    const handleSelectedGroupChange = value => {
        setSelectedGroup(value);
    };

    const columns = [
        createColumnConfig("Sysname", "host_name", TableFilterDropdown),
        {
            ...createColumnConfig("IP Address", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            }
        },
        createColumnConfig("Switch SN", "sn", TableFilterDropdown),
        createColumnConfig("Version", "version", TableFilterDropdown),
        createColumnConfig("Hardware ID", "hwid", TableFilterDropdown),
        {
            ...createColumnConfig("License Expiry", "license_expired", TableFilterDropdown),
            render: (_, record) => (record.license_expired ? record.license_expired : "--")
        },
        {
            ...createColumnConfig("License Status", "license_status", TableFilterDropdown),
            render: (_, record) => (record.license_status ? record.license_status : "--")
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to license action?", () =>
                                        licenseAction(record)
                                    )
                                }
                            >
                                License Action
                            </a>
                            <a
                                onClick={() => {
                                    LogViewTextareaModalRef.current.showLogViewTextareaModal(record.sn);
                                }}
                            >
                                Log
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    return (
        <div style={{marginBottom: "32px"}}>
            <AmpConCustomTable
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={<ActionButton onSelectGroup={handleSelectedGroupChange} />}
                fetchAPIInfo={loadGroup}
                fetchAPIParams={[selectedGroup]}
            />
            <LogViewTextareaModal ref={LogViewTextareaModalRef} />
        </div>
    );
};

export default LicenseAction;
