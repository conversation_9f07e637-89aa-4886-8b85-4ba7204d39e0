export const PowerSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
            <g>
                <g />
                <g>
                    <g>
                        <path
                            d="M11.966662239379882,8.444449074325561L10.556292239379882,8.444449074325561L9.762962239379883,5.2444450743255615C9.674812239379882,5.0666670743255615,9.498522239379882,4.8888890743255615,9.322222239379883,4.8888890743255615C9.145922239379882,4.8888890743255615,8.969632239379884,4.977777874325562,8.881482239379883,5.1555560743255615L7.647412239379883,9.066669074325562L7.118522239379883,7.0222190743255615C7.030372239379883,6.844449074325562,6.854072239379883,6.666669074325561,6.677782239379883,6.666669074325561C6.501482239379882,6.666669074325561,6.325182239379883,6.755559074325562,6.237042239379883,6.933339074325561L5.531852239379883,8.444449074325561L4.033333239379883,8.444449074325561C3.7688882393798826,8.444449074325561,3.592592239379883,8.711109074325561,3.592592239379883,8.888889074325562C3.592592239379883,9.155559074325563,3.7688882393798826,9.333339074325561,4.033333239379883,9.333339074325561L5.796292239379882,9.333339074325561C5.972592239379883,9.333339074325561,6.148892239379883,9.244449074325562,6.148892239379883,9.066669074325562L6.501482239379882,8.355559074325562L7.118522239379883,10.75555907432556C7.206662239379883,10.933339074325563,7.294812239379883,11.111109074325562,7.559262239379883,11.111109074325562C7.735552239379883,11.111109074325562,7.911852239379883,11.022219074325562,8.000002239379882,10.844449074325562L9.234072239379882,6.933329074325561L9.762962239379883,8.97777907432556C9.851112239379884,9.155559074325563,10.027412239379883,9.333329074325562,10.203702239379883,9.333329074325562L11.966662239379882,9.333329074325562C12.231112239379883,9.333329074325562,12.407412239379882,9.066669074325562,12.407412239379882,8.888889074325562C12.407412239379882,8.622219074325562,12.231112239379883,8.444449074325561,11.966662239379882,8.444449074325561Z"
                            fill="#212529"
                            fillOpacity="1"
                        />
                        <path
                            d="M12.361732239379883,9.243319074325562Q12.507402239379882,9.075429074325562,12.507412239379883,8.888889074325562Q12.507412239379883,8.647709074325562,12.357212239379884,8.496249074325561Q12.206662239379883,8.344449074325562,11.966662239379882,8.344449074325562L10.634532239379883,8.344449074325562L9.857402239379883,5.209795074325561L9.852552239379882,5.200022074325561Q9.648702239379883,4.788889074325562,9.322222239379883,4.788889074325562Q8.951672239379882,4.788889074325562,8.791892239379884,5.1111340743255615L8.788452239379883,5.118076074325561L7.656922239379883,8.704179074325562L7.212732239379883,6.987119074325562L7.208112239379883,6.977799074325562Q7.004252239379882,6.566669074325562,6.677782239379883,6.566669074325562Q6.3072222393798825,6.566669074325562,6.147442239379883,6.888909074325562L6.146922239379883,6.8899690743255615L5.4681622393798825,8.344449074325562L4.033333239379883,8.344449074325562Q3.803146239379883,8.344449074325562,3.6382660393798827,8.53445907432556Q3.4925922393798827,8.70234907432556,3.4925922393798827,8.888889074325562Q3.4925922393798827,9.130069074325561,3.6427900393798827,9.281529074325562Q3.7933332393798826,9.433339074325563,4.033333239379883,9.433339074325563L5.796292239379882,9.433339074325563Q5.970022239379883,9.433339074325563,6.094492239379883,9.349659074325562Q6.237962239379883,9.25320907432556,6.248112239379883,9.091659074325563L6.471602239379883,8.640929074325562L7.024272239379883,10.790599074325561L7.028932239379882,10.799979074325561Q7.232782239379883,11.211109074325561,7.559262239379883,11.211109074325561Q7.929812239379883,11.211109074325561,8.089592239379883,10.888869074325562L8.093032239379884,10.881929074325562L9.224562239379882,7.295829074325562L9.668752239379883,9.01288907432556L9.673372239379884,9.022199074325561Q9.877222239379883,9.433329074325561,10.203702239379883,9.433329074325561L11.966662239379882,9.433329074325561Q12.196852239379883,9.433339074325563,12.361732239379883,9.243319074325562ZM12.215202239379883,8.637079074325563Q12.307412239379882,8.730069074325561,12.307412239379882,8.888889074325562Q12.307412239379882,9.000759074325561,12.210672239379884,9.112239074325561Q12.105602239379882,9.23332907432556,11.966662239379882,9.23332907432556L10.203702239379883,9.23332907432556Q10.004392239379882,9.233339074325562,9.857122239379883,8.942469074325562L9.243592239379883,6.570839074325562L7.9070722393798825,10.806629074325562Q7.801862239379883,11.011109074325562,7.559262239379883,11.011109074325562Q7.359972239379883,11.011109074325562,7.212712239379883,10.720319074325563L6.531362239379883,8.070179074325562L6.048892239379883,9.043239074325562L6.048892239379883,9.066669074325562Q6.048892239379883,9.233339074325562,5.796292239379882,9.233339074325562L4.033333239379883,9.233339074325562Q3.692592239379883,9.233339074325562,3.692592239379883,8.888889074325562Q3.692592239379883,8.777019074325562,3.789326239379883,8.665539074325562Q3.8944002393798827,8.544449074325561,4.033333239379883,8.544449074325561L5.595542239379883,8.544449074325561L6.327132239379883,6.976749074325562Q6.431882239379883,6.766669074325561,6.677782239379883,6.766669074325561Q6.877092239379882,6.766669074325561,7.024362239379883,7.057529074325561L7.637892239379883,9.429159074325561L8.974412239379884,5.193374074325561Q9.079622239379884,4.988889074325561,9.322222239379883,4.988889074325561Q9.521382239379882,4.988889074325561,9.668582239379884,5.279306074325562L10.478062239379883,8.544449074325561L11.966672239379882,8.544449074325561Q12.123332239379883,8.544449074325561,12.215202239379883,8.637079074325563Z"
                            fillRule="evenodd"
                            fill="#212529"
                            fillOpacity="1"
                        />
                    </g>
                    <g>
                        <path
                            d="M8,15C4.15,15,1,11.85,1,8C1,4.15,4.15,1,8,1C9.4875,1,10.8875,1.4375,12.1125,2.3125C12.2875,2.4875,12.375,2.75,12.2,2.925C12.025,3.1,11.7625,3.1875,11.5875,3.0125C10.5375,2.225,9.3125,1.875,8,1.875C4.5875,1.875,1.875,4.5875,1.875,8C1.875,11.4125,4.5875,14.125,8,14.125C11.4125,14.125,14.125,11.4125,14.125,8C14.125,6.95,13.8625,5.9,13.3375,4.9375C13.25,4.7625,13.25,4.5,13.5125,4.325C13.6875,4.2375,13.95,4.2375,14.125,4.5C14.65,5.55,15,6.775,15,8C15,11.85,11.85,15,8,15Z"
                            fill="#212529"
                            fillOpacity="1"
                        />
                        <path
                            d="M13.1205,13.1205Q15.25,10.99105,15.25,8Q15.25,6.19098,14.3486,4.388199999999999L14.3416,4.374280000000001L14.333,4.361330000000001Q13.97,3.81676,13.4007,4.10139L13.3868,4.10835L13.3738,4.1169899999999995Q12.8293,4.48003,13.1139,5.0493L13.1159,5.0533L13.118,5.05721Q13.875,6.445,13.875,8Q13.875,10.45582,12.1654,12.1654Q10.45582,13.875,8,13.875Q5.54417,13.875,3.83459,12.1654Q2.125,10.45582,2.125,8Q2.125,5.54418,3.83459,3.83459Q5.54418,2.125,8,2.125Q9.97892,2.125,11.425,3.20314Q11.6394,3.40581,11.9428,3.34515Q12.1811,3.29749,12.3768,3.10178Q12.5953,2.88321,12.5326,2.5697200000000002Q12.485,2.33143,12.2893,2.13572L12.2746,2.1210899999999997L12.2578,2.10907Q10.35511,0.75,8,0.75Q5.00895,0.75,2.87947,2.87947Q0.75,5.00895,0.750001,8Q0.750001,10.99106,2.87947,13.1205Q5.00894,15.25,8,15.25Q10.99106,15.25,13.1205,13.1205ZM13.9091,4.62716Q14.75,6.31667,14.75,8Q14.75,10.78395,12.767,12.767Q10.78395,14.75,8,14.75Q5.21605,14.75,3.23303,12.767Q1.25,10.78395,1.250001,8Q1.25,5.21605,3.23303,3.23303Q5.21605,1.25,8,1.25Q10.18362,1.25,11.949,2.50298Q12.0258,2.58488,12.0424,2.66778Q12.0526,2.71887,12.0232,2.74822Q11.9346,2.83689,11.8447,2.85485Q11.7936,2.8650700000000002,11.7643,2.8357200000000002L11.7517,2.8231599999999997L11.7375,2.8125Q10.15417,1.6250010000000001,8,1.625Q5.33707,1.6250010000000001,3.48104,3.48104Q1.625,5.33707,1.625,8Q1.625,10.66293,3.48104,12.519Q5.33707,14.375,8,14.375Q10.66293,14.375,12.519,12.519Q14.375,10.66293,14.375,8Q14.375,6.32002,13.5594,4.82225Q13.4788,4.65669,13.638,4.54211Q13.7977,4.470940000000001,13.9091,4.62716Z"
                            fillRule="evenodd"
                            fill="#212529"
                            fillOpacity="1"
                        />
                    </g>
                </g>
            </g>
        </svg>
    );
};

export const DispersionSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
            <g>
                <g />
                <g>
                    <g>
                        <rect x="1" y="5" width="1" height="6" rx="0.5" fill="#212529" fillOpacity="1" />
                        <rect
                            x="1"
                            y="5"
                            width="1"
                            height="6"
                            rx="0.5"
                            fillOpacity="0"
                            strokeOpacity="1"
                            stroke="#212529"
                            fill="none"
                            strokeWidth="0.30000001192092896"
                        />
                    </g>
                    <g>
                        <rect x="3.5" y="2" width="1" height="12" rx="0.5" fill="#212529" fillOpacity="1" />
                        <rect
                            x="3.5"
                            y="2"
                            width="1"
                            height="12"
                            rx="0.5"
                            fillOpacity="0"
                            strokeOpacity="1"
                            stroke="#212529"
                            fill="none"
                            strokeWidth="0.30000001192092896"
                        />
                    </g>
                    <g>
                        <rect x="6" y="4" width="1" height="8" rx="0.5" fill="#212529" fillOpacity="1" />
                        <rect
                            x="6"
                            y="4"
                            width="1"
                            height="8"
                            rx="0.5"
                            fillOpacity="0"
                            strokeOpacity="1"
                            stroke="#212529"
                            fill="none"
                            strokeWidth="0.30000001192092896"
                        />
                    </g>
                    <g>
                        <rect x="8.5" y="1" width="1" height="14" rx="0.5" fill="#212529" fillOpacity="1" />
                        <rect
                            x="8.5"
                            y="1"
                            width="1"
                            height="14"
                            rx="0.5"
                            fillOpacity="0"
                            strokeOpacity="1"
                            stroke="#212529"
                            fill="none"
                            strokeWidth="0.30000001192092896"
                        />
                    </g>
                    <g>
                        <rect x="11" y="3" width="1" height="10" rx="0.5" fill="#212529" fillOpacity="1" />
                        <rect
                            x="11"
                            y="3"
                            width="1"
                            height="10"
                            rx="0.5"
                            fillOpacity="0"
                            strokeOpacity="1"
                            stroke="#212529"
                            fill="none"
                            strokeWidth="0.30000001192092896"
                        />
                    </g>
                    <g>
                        <rect x="13.5" y="5.5" width="1" height="5" rx="0.5" fill="#212529" fillOpacity="1" />
                        <rect
                            x="13.5"
                            y="5.5"
                            width="1"
                            height="5"
                            rx="0.5"
                            fillOpacity="0"
                            strokeOpacity="1"
                            stroke="#212529"
                            fill="none"
                            strokeWidth="0.30000001192092896"
                        />
                    </g>
                </g>
            </g>
        </svg>
    );
};
