.edit_form {
    &_header {
        display: flex;
        justify-content: space-between;
        padding: 5px;
        border-bottom: 1px solid #f0f0f0;

        &_title {
            font-size: 20px;
        }

        &_operation {
            display: flex;
            align-items: center;
            flex-direction: row-reverse;
        }
    }

    &_content {
        // margin: 10px;
        // height: 85%;
        overflow: auto;
    }
}

.edit_tab {
    height: 100%;

    &_pane {
        overflow: auto;
    }
}

.edit_rpc_form2 {
    height: calc(100vh - 205px);
    overflow: auto;
}

.edit_rpc_form2_dialog {
    top: 65px
}

.multiColumnForm {
    width: 280px;
    &>div:nth-child(2){
        margin-left: -1px;
    }

    :global {
        .ant-input,
        .ant-select,
        .ant-input-group-wrapper,
        .ant-input-number,
        .ant-input-number-group-wrapper,
        .ant-input-number-input {
           width: 100% !important;
        }
    }
}

.hiddenTabsNav {
    :global {
        .ant-tabs-nav {
            display: none;
        }
    }

}

.noFooterMiddleModal {
    :global {
        .ant-modal-confirm-content {
            min-height: 388px !important;
        }
    }
}

.noFooterBigModal {
    :global {
        .ant-modal-confirm-content {
            min-height: 438px !important;
        }
    }
}

