import {But<PERSON>, <PERSON>r, Checkbox, Col, Form, Input, InputNumber, message, Radio, Row, Select, Switch} from "antd";
import React, {useEffect, useState} from "react";
import {isEmpty} from "lodash";
import {Option} from "antd/es/mentions";
import TextArea from "antd/es/input/TextArea";
import styles from "./custom_edit_form.module.scss";
import {convertToArray, getText} from "../utils";
import {bigModal, middleModal} from "./custom_modal";
import {EyeOutlined, EyeInvisibleOutlined} from "@ant-design/icons";

const EditInput = ({config, formType, allData, setAllData, resetDefaultValue, initData, form, formKey, disabled}) => {
    const [data, setData] = useState({});
    const [selectStatus, setSelectStatus] = useState();
    const {
        unit,
        inputType,
        edit,
        dataIndex,
        rows,
        pattern,
        resetAffect,
        showCheckedStrategy,
        range,
        multiple,
        step,
        placeholder,
        mode,
        parserFun
    } = config;
    let regExpTool;
    if (mode === "tags" && pattern) {
        regExpTool = new RegExp(pattern);
    }

    const processResetAffect = _data => {
        if (resetAffect) {
            if (resetAffect instanceof Array) {
                form.resetFields(resetAffect);
                resetAffect.forEach(i => {
                    delete _data[i];
                });
            } else {
                Object.entries(_data).map(([k]) => {
                    if (!resetAffect?.exclude?.includes(k)) {
                        form.resetFields([k]);
                        delete _data[k];
                    }
                });
            }
        }
    };

    const onClear = () => {
        if (form) {
            form.resetFields([formKey ?? dataIndex]);
        }
        const _data = {...allData};
        // processResetAffect(_data);
        if (setAllData) {
            delete _data[formKey ?? dataIndex];
            setAllData(_data);
        }
    };

    const onChange = v => {
        if (regExpTool) {
            let regExpPass = true;
            convertToArray(v).map(item => {
                if (!regExpTool.test(item)) {
                    regExpPass = false;
                }
            });
            if (!regExpPass) {
                setSelectStatus("error");
            } else {
                setSelectStatus(null);
            }
        }
        let newValue = {...allData};
        processResetAffect(newValue);
        newValue[formKey ?? dataIndex] = v;
        if (config?.data?.effect) {
            if (resetDefaultValue) {
                resetDefaultValue(
                    config.data.effect.reduce((p, c) => {
                        p[c] = null;
                        return p;
                    }, {})
                );
            }
        }
        if (config?.covertDefault) {
            config.covertDefault(newValue, form?.getFieldsValue(), data).then(rs => {
                if (!rs) {
                    return;
                }
                if (resetDefaultValue) {
                    resetDefaultValue(rs);
                }
                if (setAllData) {
                    newValue = {...newValue, ...rs};
                    setAllData(newValue);
                }
                if (form) {
                    Object.entries(rs).map(([_k, _v]) => {
                        form.setFieldValue(_k, _v);
                    });
                }
            });
        } else if (setAllData) {
            setAllData(newValue);
        }
    };

    const getOptionsCascader = (data, dataIndex) => {
        if (!data[formKey ?? dataIndex] && initData) {
            if (config.initOptions) {
                return config.initOptions(initData);
            }
            return initData.options.map(i => {
                return typeof i === "string"
                    ? {
                          value: i,
                          label: i,
                          title: i
                      }
                    : i;
            });
        }
        return data?.[formKey ?? dataIndex];
    };

    const getOptions = (data, dataIndex, options) => {
        if (options) {
            return options;
        }
        if (!data[formKey ?? dataIndex] && initData?.options) {
            return initData.options.map(i => {
                return typeof i === "string"
                    ? {
                          value: i,
                          label: i,
                          title: i
                      }
                    : i;
            });
        }
        return (
            data?.[formKey ?? dataIndex]?.map(enumKey => ({
                value: enumKey.value || enumKey,
                label: enumKey.label || enumKey,
                title: enumKey.title || enumKey.value || enumKey
            })) ?? [{value: "", label: "No Data", disabled: true}]
        );
    };

    let _disabled = false;
    const disabledFun = config?.[formType]?.disabled ?? config?.disabled;
    if (disabledFun) {
        if (typeof disabledFun === "function") {
            _disabled = disabledFun(allData, form?.getFieldsValue());
        } else {
            _disabled = disabledFun;
        }
    }
    _disabled = disabled || _disabled;

    if (inputType === "boolean") {
        const options = [
            <Option key="true" value="true">
                true
            </Option>,
            <Option key="false" value="false">
                false
            </Option>
        ];
        return (
            <Select
                key={formKey ?? dataIndex}
                style={{width: 280}}
                onChange={v => {
                    onChange(v);
                }}
            >
                {options}
            </Select>
        );
    }

    if (inputType === "switch") {
        return (
            <Switch
                key={formKey ?? dataIndex}
                checked={allData?.[formKey ?? dataIndex] ?? initData?.value}
                onChange={v => {
                    onChange(v);
                }}
            />
        );
    }

    if (inputType === "number") {
        const rangeArray = range?.split("..");
        return (
            <InputNumber
                style={{width: 280}}
                key={formKey ?? dataIndex}
                min={rangeArray?.[0]}
                max={rangeArray?.[1]}
                step={step}
                addonAfter={unit}
                parser={v => {
                    if (parserFun) {
                        return parserFun(v);
                    }
                    return v;
                }}
                onChange={v => {
                    onChange(v);
                }}
                disabled={_disabled}
            />
        );
    }

    if (inputType === "cascader") {
        return (
            <Cascader
                style={{width: 280}}
                key={formKey ?? dataIndex}
                multiple={multiple}
                showCheckedStrategy={showCheckedStrategy}
                options={getOptionsCascader(data, formKey ?? dataIndex)}
                onClear={onClear}
                onChange={v => {
                    let newValue = {...allData};
                    processResetAffect(newValue);
                    if (!v) {
                        if (setAllData) {
                            setAllData(newValue);
                        }
                        return;
                    }
                    if (!multiple && showCheckedStrategy === "SHOW_CHILD") {
                        if (v.length > 1) {
                            v = v[v.length - 1];
                        }
                    }
                    newValue = {...newValue, [formKey ?? dataIndex]: v};
                    if (form) {
                        form.setFieldValue(formKey ?? dataIndex, v);
                    }
                    if (config?.covertDefault) {
                        config.covertDefault(newValue, form?.getFieldsValue(), data).then(rs => {
                            if (!rs) {
                                return;
                            }
                            if (resetDefaultValue) {
                                resetDefaultValue(rs);
                            }
                            newValue = {...newValue, ...rs};
                            if (form) {
                                Object.entries(rs).map(([_k, _v]) => {
                                    form.setFieldValue(_k, _v);
                                });
                            }
                            if (setAllData) {
                                setAllData(newValue);
                            }
                        });
                    } else if (setAllData) {
                        setAllData(newValue);
                    }
                }}
                onDropdownVisibleChange={async open => {
                    if (open) {
                        if (typeof config.data === "function") {
                            const rs = await config.data(allData, form?.getFieldsValue());
                            setData({...data, [formKey ?? dataIndex]: rs});
                        }
                    }
                }}
            />
        );
    }

    if (inputType === "password") {
        return (
            <Input.Password
                iconRender={visible => {
                    return visible ? (
                        <EyeOutlined style={{color: "#c5c5c5"}} />
                    ) : (
                        <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                    );
                }}
                style={{width: 280}}
                placeholder="Please input password"
                disabled={formType === "edit" ? edit?.disabled : _disabled}
            />
        );
    }
    if (inputType === "select") {
        return (
            <Select
                style={{width: 280}}
                mode={mode}
                status={selectStatus}
                allowClear
                placeholder={placeholder}
                onChange={onChange}
                onClear={onClear}
                disabled={_disabled}
                // onDropdownVisibleChange={async open => {
                //     if (!config.data) {
                //         return;
                //     }
                //     if (open) {
                //         if (typeof config.data === "function") {
                //             const r = await config.data(allData, form?.getFieldsValue());
                //             if (!r) {
                //                 return;
                //             }
                //             const newData = {...data};
                //             newData[formKey ?? dataIndex] = r.map(item => {
                //                 if (typeof item === "string") {
                //                     return {label: item, value: item, title: item};
                //                 }
                //                 return item;
                //             });
                //             setData(newData);
                //             return;
                //         }
                //         if (config.data.options) {
                //             const newData = {...data};
                //             newData[formKey ?? dataIndex] = config.data.options.map(item => {
                //                 if (typeof item === "string") {
                //                     return {
                //                         label: item,
                //                         value: item,
                //                         title: item
                //                     };
                //                 }
                //                 return item;
                //             });
                //             setData(newData);
                //             return;
                //         }
                //         const filter = {};
                //         if (config.data.filter) {
                //             for (let j = 0; j < config.data.filter.length; j++) {
                //                 const i = config.data.filter[j];
                //                 if (
                //                     i.filterIndex &&
                //                     allData[i.filterIndex] &&
                //                     allData[i.filterIndex].toString().trim() !== ""
                //                 ) {
                //                     filter[i.dataIndex] = allData[i.filterIndex];
                //                 } else if (i.key) {
                //                     filter[i.key] = i.value;
                //                 } else if (i.required === false) {
                //                     // 非必要条件，全出来。
                //                 } else {
                //                     return; // 如果过滤条件不选，不出值
                //                 }
                //             }
                //         }
                //     }
                // }}
                key={formKey ?? dataIndex}
                options={getOptions(data, formKey ?? dataIndex, config?.data?.options)}
            />
        );
    }
    if (inputType === "textarea") {
        return (
            <TextArea
                style={{width: 280}}
                rows={rows}
                disabled={_disabled}
                placeholder={placeholder}
                onChange={v => {
                    onChange(v.target.value);
                }}
            />
        );
    }
    if (inputType === "checkbox") {
        return (
            <Checkbox.Group>
                <Checkbox
                    disabled={_disabled}
                    value
                    onChange={v => {
                        setData(v.target.checked);
                        onChange(v.target.checked);
                    }}
                />
            </Checkbox.Group>
        );
    }
    if (inputType === "radio") {
        return (
            <Radio.Group onChange={onChange} value disabled={disabled}>
                {config.data.options.map(i => (
                    <Radio value={i.value}>{i.title}</Radio>
                ))}
            </Radio.Group>
        );
    }
    return (
        <Input
            style={{width: 280}}
            placeholder={placeholder}
            addonAfter={unit}
            disabled={_disabled}
            onChange={v => {
                onChange(v.target.value);
            }}
        />
    );
};

const EditForm = ({
    setForm,
    afterFinish,
    getData,
    setDataAPI,
    columns,
    saveFun,
    onCancel,
    columnNum,
    labelCol,
    wrapperCol
}) => {
    const [form] = Form.useForm();
    setForm?.(form);
    const [originalData, setOriginalData] = useState([]);
    const [data, setData] = useState({});
    let colSpan, colOffset, colSpan2, colOffset2;

    const loadData = () => {
        getData().then(rs => {
            setOriginalData(rs);
            setData(rs);
            form.setFieldsValue(rs);
        });
    };
    const API = setDataAPI();
    if (columnNum === 1) {
        colSpan = 18;
        colOffset = 0;
        colSpan2 = 4;
        colOffset2 = 1;
    } else if (columnNum === 2) {
        colSpan = 8;
        colOffset = 0;
        colSpan2 = 3;
        colOffset2 = 1;
    }

    const submit = () => {
        const value = form.getFieldsValue();
        const diffValue = structuredClone(value);
        Object.keys(diffValue).map(k => {
            if (originalData[k] === value[k] || (originalData[k] === undefined && value[k] === "")) {
                delete diffValue[k];
            }
        });
        if (isEmpty(diffValue)) {
            onCancel();
            return;
        }
        saveFun?.(diffValue, value).then(rs => {
            if (rs === true || rs.message === "SUCCESS" || rs.apiResult === 0) {
                afterFinish(true);
            } else {
                afterFinish(false);
            }
        });
    };

    useEffect(loadData, []);

    return (
        <Form
            style={{overflow: "auto"}}
            labelCol={{span: labelCol ?? 10}}
            wrapperCol={{span: wrapperCol ?? 13}}
            form={form}
            onFinish={submit}
            className={styles.edit_form}
            labelAlign="left"
        >
            {columns.map(i => {
                return (
                    <Row>
                        {i.map(item => {
                            if (item.type === "split") {
                                return (
                                    <div style={{width: "100%", paddingTop: 0, paddingBottom: 20}}>
                                        <div
                                            style={{
                                                width: "100%",
                                                borderTop: "1px solid #f0f0f0"
                                            }}
                                        />
                                    </div>
                                );
                            }
                            if (item.type === "title") {
                                return (
                                    <div style={{width: "100%", paddingTop: 0, paddingBottom: 20, fontWeight: "bold"}}>
                                        {item.title}
                                    </div>
                                );
                            }
                            const rules = [];
                            if (item.required) {
                                if (typeof item.required === "function") {
                                    if (item.required(data, form.getFieldsValue())) {
                                        rules.push({required: true, message: "It is required"});
                                    }
                                } else {
                                    rules.push({required: true, message: "It is required"});
                                }
                            }
                            if (item.pattern) {
                                const p = {pattern: item.pattern};
                                if (item.message) {
                                    p.message = item.message;
                                }
                                rules.push(p);
                            }
                            return (
                                <CustomFormItemButton
                                    item={item}
                                    rules={rules}
                                    data={data}
                                    setData={setData}
                                    colSpan={colSpan}
                                    colOffset={colOffset}
                                    colSpan2={colSpan2}
                                    colOffset2={colOffset2}
                                    buttonDiabled={
                                        item?.dependOn
                                            ? form.getFieldValue(item.dependOn.dataIndex) !== item.dependOn.expected
                                            : false
                                    }
                                    onSend={async () => {
                                        let parameter = {};
                                        if (API.APIParameter) {
                                            parameter = API.APIParameter();
                                        }
                                        const config = {
                                            key: item.setKey ?? item.dataIndex,
                                            value:
                                                item?.transformValue?.(form.getFieldValue(item.dataIndex)) ??
                                                form.getFieldValue(item.dataIndex)
                                        };
                                        await API.APIName({...parameter, ...config}).then(rs => {
                                            if (rs.errorCode === 0) {
                                                message.success("Modify Success");
                                                console.log(rs);
                                            } else {
                                                message.error("Modify Failed");
                                                console.log(rs);
                                            }
                                        });
                                    }}
                                />
                            );
                        })}
                    </Row>
                );
            })}
        </Form>
    );
};

const CustomFormItemButton = ({
    onSend,
    item,
    rules,
    data,
    setData,
    initialActive = false,
    buttonDiabled = false,
    initialContent = "Set",
    stateLoading = false,
    hideLabel = false,
    colSpan = 18,
    colOffset = 0,
    colSpan2 = 4,
    colOffset2 = 1,
    layout = "horizontal"
}) => {
    const [contentActive, setContentActive] = useState(initialActive);
    const [btnContent, setBtnContent] = useState(initialContent);
    const [btnLoading, setBtnLoading] = useState(false);

    const handleClick = async () => {
        if (!contentActive) {
            setContentActive(true);
            setBtnContent("Send");
        } else {
            setBtnLoading(true);
            await onSend();
            setBtnLoading(false);
            setContentActive(false);
            setBtnContent("Set");
        }
    };

    return layout === "horizontal" ? (
        <>
            <Col
                span={colSpan}
                offset={colOffset}
                key={`${item.dataIndex}_col`}
                style={{
                    display: "flex",
                    alignItems: "center"
                }}
            >
                <Form.Item
                    style={{width: "100%"}}
                    key={item.dataIndex}
                    label={!hideLabel ? <span>{getText(item.label ?? item.dataIndex)}</span> : null}
                    name={item.dataIndex}
                    initialValue={data[item.dataIndex]}
                    rules={rules}
                >
                    {EditInput({
                        config: item,
                        allData: data,
                        setAllData: setData,
                        disabled: !contentActive
                    })}
                </Form.Item>
            </Col>
            {!item.noSetButton && (
                <Col span={colSpan2} offset={colOffset2}>
                    <Button
                        type="primary"
                        style={{width: 98}}
                        loading={stateLoading ? true : btnLoading}
                        onClick={handleClick}
                        disabled={buttonDiabled}
                    >
                        {btnContent}
                    </Button>
                </Col>
            )}
        </>
    ) : (
        <Col span={colSpan} offset={colOffset}>
            <div style={{display: "flex", flexDirection: "column", marginLeft: -90, marginTop: 16}}>
                <Form.Item
                    style={{width: "100%"}}
                    key={item.dataIndex}
                    label={!hideLabel ? <span>{getText(item.title ?? item.dataIndex)}</span> : null}
                    name={item.dataIndex}
                    initialValue={data[item.dataIndex]}
                    rules={rules}
                >
                    {EditInput({
                        config: item,
                        allData: data,
                        setAllData: setData,
                        disabled: !contentActive
                    })}
                </Form.Item>
                {!item.noSetButton && (
                    <Form.Item>
                        <Button
                            type="primary"
                            style={{width: 98, marginLeft: 0}}
                            loading={btnLoading}
                            onClick={handleClick}
                            disabled={buttonDiabled}
                        >
                            {btnContent}
                        </Button>
                    </Form.Item>
                )}
            </div>
        </Col>
    );
};

const openCustomEditForm = ({
    title,
    columns,
    getData,
    setDataAPI,
    saveFun,
    afterUpdate,
    columnNum,
    labelCol,
    wrapperCol,
    readOnly
}) => {
    let form;
    // eslint-disable-next-line no-return-assign
    const handle = f => (form = f);
    let modal;
    const afterFinish = success => {
        if (success) {
            modal.destroy();
            afterUpdate?.();
        } else {
            modal.update({okButtonProps: {loading: false}});
        }
    };
    const onCancel = () => {
        modal.destroy();
    };
    modal = (columnNum === 1 ? middleModal : bigModal)({
        title,
        okText: "OK",
        cancelText: "Cancel",
        footer: null,
        className: columnNum === 1 ? styles.noFooterMiddleModal : styles.noFooterBigModal,
        // eslint-disable-next-line no-unused-vars
        onOk: _ => {
            form.validateFields().then(() => {
                modal.update({okButtonProps: {loading: true}});
                form.submit();
            });
        },
        okButtonProps: {
            disabled: readOnly
        },
        content: (
            <EditForm
                setForm={handle}
                afterFinish={afterFinish}
                getData={getData}
                setDataAPI={setDataAPI}
                columns={columns}
                saveFun={saveFun}
                onCancel={onCancel}
                columnNum={columnNum}
                labelCol={labelCol}
                wrapperCol={wrapperCol}
            />
        )
    });
};

export default openCustomEditForm;
