import React, {useEffect, useState} from "react";
import {Card, message, Select, Space, Switch, Table, Tag, theme, Typography} from "antd";
import Icon, {CaretDownOutlined} from "@ant-design/icons";
import styles from "./card.module.scss";
import {editTableIcon} from "@/utils/common/iconSvg";
import {getText, DebounceButton, NULL_VALUE} from "../utils";
import openDisplayDialog from "./display_info";
import openCustomEditForm from "./custom_edit_form";
import {modifyDCSPortNote, modifyFMTConfig, modifyFMTPortNote, queryFMTConfig} from "@/modules-ampcon/apis/fmt";
import {normalizeNumber} from "@/modules-ampcon/utils/util";
import {smallModal} from "./custom_modal";

const {Paragraph} = Typography;

const switchComponent = (value, rowData, disabled, submit) => {
    if (value === NULL_VALUE) {
        return NULL_VALUE;
    }
    return (
        <Switch
            disabled={disabled ?? false}
            defaultChecked={!!value}
            onChange={newVal => {
                if (newVal === value) {
                    return;
                }
                if (submit) {
                    submit(newVal).then(rs => {
                        console.log(rs);
                    });
                    return;
                }
            }}
        />
    );
};

const selectComponent = (value, rowData, options, submit, disabled) => {
    // if (value === NULL_VALUE) {
    //     return NULL_VALUE;
    // }
    return (
        <div className={styles.customSelect}>
            <Select
                suffixIcon={<CaretDownOutlined style={{pointerEvents: "none", marginLeft: "-20px"}} />}
                // defaultValue={value}
                value={value}
                bordered={false}
                options={options}
                style={{
                    width: 80,
                    fontSize: "14px",
                    textAlign: "left",
                    padding: "0"
                }}
                disabled={disabled}
                dropdownStyle={{textAlign: "center"}}
                onChange={newVal => {
                    if (newVal === value) {
                        return;
                    }
                    if (submit) {
                        submit(newVal);
                        return;
                    }
                }}
            />
        </div>
    );
};

const editComponent = (value, rowData, submit) => {
    // if (!value || value === NULL_VALUE) {
    //     return value;
    // }
    return (
        <Paragraph
            style={{margin: 0, display: "flex"}}
            editable={{
                maxLength: 32,
                icon: <Icon component={editTableIcon} />,
                text: value,
                onChange: newVal => {
                    if (!newVal || newVal === value) {
                        return;
                    }
                    if (submit) {
                        submit(newVal);
                        return;
                    }
                },
                triggerType: ["icon", "text"]
            }}
        >
            <div style={{flex: 1}}>{value}</div>
        </Paragraph>
    );
};

const PortList = ({data}) => {
    const {
        token: {colorPrimary}
    } = theme.useToken();
    const {ne_id, type, name, neData, slotIndex} = data ?? {};
    const [columns, setColumns] = useState([]);
    const [title, setTitle] = useState("Port List");
    const [tableData, setTableData] = useState(neData ?? []);
    useEffect(() => {
        const TableConfigs = {
            chassis: {
                columns: [
                    {dataIndex: "slot-no"},
                    {dataIndex: "preconfigured-boards"},
                    {dataIndex: "actual-board"},
                    {dataIndex: "board-status"},
                    {
                        dataIndex: "slot-note",
                        render: (value, rowData, index) =>
                            editComponent(value, rowData, newVal => {
                                const ip = ne_id.split(":")[0];
                                modifyFMTPortNote({
                                    ip,
                                    slotIndex,
                                    port: rowData["slot-no"],
                                    note: newVal
                                })
                                    .then(response => {
                                        if (response.errorCode === 0) {
                                            setTableData(prevData => {
                                                const newData = [...prevData];
                                                newData[index]["slot-note"] = newVal;
                                                return newData;
                                            });
                                            message.success("Update slot note success");
                                        } else {
                                            message.error("Update slot note failed");
                                        }
                                    })
                                    .catch(() => {});
                            })
                    }
                ]
            },
            EDFA: {
                columns: [
                    {dataIndex: "no"},
                    {dataIndex: "name"},
                    {dataIndex: "input-optical-power", unit: "dBm"},
                    {dataIndex: "output-optical-power", unit: "dBm"},
                    // {dataIndex: "laser-switch"},
                    {
                        dataIndex: "port-note",
                        render: (value, rowData, index) =>
                            editComponent(value, rowData, newVal => {
                                const ip = ne_id.split(":")[0];
                                modifyFMTPortNote({
                                    ip,
                                    slotIndex,
                                    port: rowData.no,
                                    note: newVal
                                })
                                    .then(response => {
                                        if (response.errorCode === 0) {
                                            setTableData(prevData => {
                                                const newData = [...prevData];
                                                newData[index]["port-note"] = newVal;
                                                return newData;
                                            });
                                            message.success("Update port note success");
                                        } else {
                                            message.error("Update port note failed");
                                        }
                                    })
                                    .catch(() => {});
                            })
                    }
                ]
            },
            DEDFA: {
                columns: [
                    {dataIndex: "no"},
                    {dataIndex: "name"},
                    {dataIndex: "input-optical-power", unit: "dBm"},
                    {dataIndex: "output-optical-power", unit: "dBm"},
                    // {dataIndex: "laser-switch"},
                    {
                        dataIndex: "port-note",
                        render: (value, rowData, index) =>
                            editComponent(value, rowData, newVal => {
                                const ip = ne_id.split(":")[0];
                                modifyFMTPortNote({
                                    ip,
                                    slotIndex,
                                    port: rowData.no,
                                    note: newVal
                                })
                                    .then(response => {
                                        if (response.errorCode === 0) {
                                            setTableData(prevData => {
                                                const newData = [...prevData];
                                                newData[index]["port-note"] = newVal;
                                                return newData;
                                            });
                                            message.success("Update port note success");
                                        } else {
                                            message.error("Update port note failed");
                                        }
                                    })
                                    .catch(() => {});
                            })
                    }
                ]
            },
            OLP: {
                columns: [
                    {
                        dataIndex: "protection-group",
                        onCell: (_, index) => {
                            return {
                                rowSpan: index % 3 === 0 ? 3 : 0
                            };
                        }
                    },
                    {dataIndex: "name"},
                    {
                        dataIndex: "work-status",
                        onCell: (_, index) => {
                            return {
                                rowSpan: index % 3 === 0 ? 3 : 0
                            };
                        }
                    },
                    {
                        dataIndex: "manual-switching",
                        onCell: (_, index) => {
                            return {
                                rowSpan: index % 3 === 0 ? 3 : 0
                            };
                        },
                        render: (value, rowData) => {
                            const activeCSS = {
                                color: "#14C9BB",
                                backgroundColor: "#E7F9F8"
                            };
                            const inactiveCSS = {
                                color: "#B3BBC8",
                                backgroundColor: "#F4F5F7"
                            };
                            const changeStatus = status => {
                                const modal = smallModal({
                                    content: `Change to ${status}?`,
                                    onOk: async () => {
                                        modal.destroy();
                                        const ip = ne_id.split(":")[0];
                                        modifyFMTConfig({
                                            ip,
                                            slotIndex,
                                            key: "work_route",
                                            value: status === "PRIMARY" ? "1" : "2"
                                        }).then(rs => {
                                            if (rs.errorCode === 0) {
                                                message.success("Modify Success");
                                            } else {
                                                message.error("Modify Failed");
                                            }
                                        });
                                    }
                                });
                            };
                            return (
                                <div style={{display: "flex"}}>
                                    <a
                                        className={styles.cycle_div}
                                        style={rowData["work-status"] === "PRIMARY" ? activeCSS : inactiveCSS}
                                        onClick={() => {
                                            if (rowData["work-status"] === "PRIMARY") {
                                                return;
                                            }
                                            changeStatus("PRIMARY");
                                        }}
                                    >
                                        P
                                    </a>
                                    <a
                                        className={styles.cycle_div}
                                        style={{
                                            ...(rowData["work-status"] === "SECONDARY" ? activeCSS : inactiveCSS),
                                            marginLeft: 15
                                        }}
                                        onClick={() => {
                                            if (rowData["work-status"] === "SECONDARY") {
                                                return;
                                            }
                                            changeStatus("SECONDARY");
                                        }}
                                    >
                                        S
                                    </a>
                                </div>
                            );
                        }
                    },
                    {
                        dataIndex: "protection-config",
                        onCell: (_, index) => {
                            return {
                                rowSpan: index % 3 === 0 ? 3 : 0
                            };
                        },
                        render: (value, rowData) => {
                            return (
                                <Space size={24}>
                                    <DebounceButton
                                        onClick={() => {
                                            openDisplayDialog({
                                                title: "Display",
                                                columns: [
                                                    [
                                                        {
                                                            dataIndex: "wait-to-restore-time",
                                                            label: "wtr-time",
                                                            unit: "ms",
                                                            dot: true
                                                        }
                                                    ],
                                                    [{dataIndex: "primary-switch-threshold", unit: "dBm", dot: true}],
                                                    [{dataIndex: "secondary-switch-threshold", unit: "dBm", dot: true}]
                                                ],
                                                getData: async () => {
                                                    const ip = ne_id.split(":")[0];
                                                    const _data = (await queryFMTConfig(ip, slotIndex)).data
                                                        ?.configData;
                                                    return {
                                                        "wait-to-restore-time": normalizeNumber(_data?.return_delay),
                                                        "primary-switch-threshold": normalizeNumber(
                                                            _data?.r1_switch_limit
                                                        ),
                                                        "secondary-switch-threshold": normalizeNumber(
                                                            _data?.r2_switch_limit
                                                        )
                                                    };
                                                }
                                            });
                                        }}
                                        type="link"
                                        title="Display"
                                        style={{color: colorPrimary}}
                                    >
                                        Display
                                    </DebounceButton>
                                    <DebounceButton
                                        style={{color: colorPrimary}}
                                        type="link"
                                        title="Modify"
                                        onClick={() => {
                                            openCustomEditForm({
                                                title: "Modify",
                                                columnNum: 1,
                                                columns: [
                                                    [
                                                        {
                                                            dataIndex: "return_delay",
                                                            label: "WTR Time",
                                                            unit: "ms",
                                                            inputType: "number",
                                                            required: true,
                                                            transformValue: value => {
                                                                return value.toString().padStart(3, "0");
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "r1_switch_limit",
                                                            label: "Primary Switch Threshold",
                                                            unit: "dBm",
                                                            inputType: "number",
                                                            step: 0.01,
                                                            required: true,
                                                            transformValue: value => {
                                                                return value.toString();
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "r2_switch_limit",
                                                            label: "Secondary Switch Threshold",
                                                            unit: "dBm",
                                                            inputType: "number",
                                                            step: 0.01,
                                                            required: true,
                                                            transformValue: value => {
                                                                return value.toString();
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "work_mode",
                                                            label: "Work Mode",
                                                            inputType: "select",
                                                            required: true,
                                                            data: {
                                                                options: [
                                                                    {label: "Auto", value: "1"},
                                                                    {label: "Manual", value: "0"}
                                                                ]
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "return_mode",
                                                            label: "Return Mode",
                                                            inputType: "select",
                                                            required: true,
                                                            data: {
                                                                options: [
                                                                    {label: "Auto", value: "1"},
                                                                    {label: "Manual", value: "0"}
                                                                ]
                                                            }
                                                        }
                                                    ]
                                                ],
                                                getData: async () => {
                                                    const ip = ne_id.split(":")[0];
                                                    const _data = (await queryFMTConfig(ip, slotIndex)).data
                                                        ?.configData;
                                                    return {
                                                        return_delay: _data?.return_delay,
                                                        r1_switch_limit: _data?.r1_switch_limit,
                                                        r2_switch_limit: _data?.r2_switch_limit,
                                                        return_mode: _data?.return_mode,
                                                        work_mode: _data?.work_mode
                                                    };
                                                },
                                                setDataAPI: () => {
                                                    const ip = ne_id.split(":")[0];
                                                    return {
                                                        APIName: modifyFMTConfig,
                                                        APIParameter: () => {
                                                            return {
                                                                ip,
                                                                slotIndex
                                                            };
                                                        }
                                                    };
                                                }
                                            });
                                        }}
                                    >
                                        Modify
                                    </DebounceButton>
                                </Space>
                            );
                        }
                    }
                ]
            },
            OEO: {
                columns: [
                    {dataIndex: "name", fixed: "left"},
                    {dataIndex: "module-wavelength", unit: "nm"},
                    {dataIndex: "transmission-distance", unit: "km"},
                    {dataIndex: "input-optical-power", unit: "dBm"},
                    {dataIndex: "output-optical-power", unit: "dBm"},
                    // {
                    //     dataIndex: "module-state",
                    //     render: value => {
                    //         if (value === NULL_VALUE) {
                    //             return NULL_VALUE;
                    //         }
                    //         return (
                    //             <div style={{display: "flex", alignItems: "center"}}>
                    //                 <div
                    //                     style={{
                    //                         width: 5,
                    //                         height: 5,
                    //                         backgroundColor: "#14c9bb",
                    //                         borderRadius: "50%",
                    //                         marginRight: 5
                    //                     }}
                    //                 />
                    //                 <div>{value}</div>
                    //             </div>
                    //         );
                    //     }
                    // },
                    {
                        dataIndex: "work-mode",
                        label: "Laser Switch",
                        align: "left",
                        render: (value, rowData, index) =>
                            selectComponent(
                                value,
                                rowData,
                                [
                                    {value: "0.00", label: "Close"},
                                    {value: "1.00", label: "Open"},
                                    {value: "2.00", label: "Auto"}
                                ],
                                newVal => {
                                    const ip = ne_id.split(":")[0];
                                    modifyFMTConfig({
                                        ip,
                                        slotIndex,
                                        key: `control_mode_${rowData.name.toLowerCase()}`,
                                        value: newVal.replace(/\.00$/, "")
                                    })
                                        .then(response => {
                                            if (response.errorCode === 0) {
                                                setTableData(prevData => {
                                                    const newData = [...prevData];
                                                    newData[index]["work-mode"] = newVal;
                                                    return newData;
                                                });
                                                message.success("Modify Success");
                                            } else {
                                                message.error("Modify Failed");
                                            }
                                        })
                                        .catch(() => {});
                                }
                            )
                    },
                    {dataIndex: "module-temperature", unit: "℃"},
                    {dataIndex: "rate", unit: "G/s"},
                    {
                        dataIndex: "service-notes",
                        fixed: "right",
                        render: (value, rowData, index) =>
                            editComponent(value, rowData, newVal => {
                                const ip = ne_id.split(":")[0];
                                modifyFMTPortNote({
                                    ip,
                                    slotIndex,
                                    port: rowData.no,
                                    note: newVal
                                })
                                    .then(() => {
                                        setTableData(prevData => {
                                            const newData = [...prevData];
                                            newData[index]["service-notes"] = newVal;
                                            return newData;
                                        });
                                    })
                                    .catch(() => {});
                            })
                    }
                ]
            },
            VOA: {
                columns: [
                    {dataIndex: "no"},
                    {dataIndex: "name"},
                    {
                        dataIndex: "work-mode",
                        render: value => {
                            return value === "1.00" ? "manual" : "auto";
                        }
                    },
                    {dataIndex: "actual-power", unit: "dBm"},
                    {dataIndex: "expected-power", unit: "dBm"},
                    {dataIndex: "actual-attenuation", unit: "dB"},
                    {dataIndex: "expected-attenuation", unit: "dB"},
                    {dataIndex: "threshold"},
                    {
                        dataIndex: "operation",
                        onCell: (_, index) => {
                            return {
                                rowSpan: index % 4 === 0 ? 4 : 0
                            };
                        },
                        render: (value, rowData) => {
                            return (
                                <Space size={24}>
                                    <DebounceButton
                                        style={{color: colorPrimary}}
                                        type="link"
                                        title="Modify"
                                        onClick={() => {
                                            openCustomEditForm({
                                                title: "Modify",
                                                columnNum: 2,
                                                columns: [
                                                    [
                                                        {
                                                            type: "title",
                                                            title: "VOA1"
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa1_work_mode",
                                                            label: "Work mode of VOA1",
                                                            inputType: "radio",
                                                            data: {
                                                                options: [
                                                                    {title: "auto", value: "0"},
                                                                    {title: "manual", value: "1"}
                                                                ]
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa1_power_configuration",
                                                            label: "expected-power",
                                                            unit: "dBm",
                                                            inputType: "number",
                                                            dependOn: {
                                                                dataIndex: "voa1_work_mode",
                                                                expected: "0"
                                                            }
                                                        },
                                                        {
                                                            dataIndex: "voa1_attenuation_configuration",
                                                            label: "expected-attenuation",
                                                            unit: "dB",
                                                            inputType: "number",
                                                            pattern: /^([01]?\d|2\d|30)$/,
                                                            message: "Please enter a value between 0 and 30",
                                                            dependOn: {
                                                                dataIndex: "voa1_work_mode",
                                                                expected: "1"
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa1_threshold",
                                                            label: "threshold",
                                                            inputType: "number",
                                                            dependOn: {
                                                                dataIndex: "voa1_work_mode",
                                                                expected: "0"
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            type: "split"
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            type: "title",
                                                            title: "VOA2"
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa2_work_mode",
                                                            label: "Work mode of VOA2",
                                                            inputType: "radio",
                                                            data: {
                                                                options: [
                                                                    {title: "auto", value: "0"},
                                                                    {title: "manual", value: "1"}
                                                                ]
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa2_power_configuration",
                                                            label: "expected-power",
                                                            unit: "dBm",
                                                            inputType: "number",
                                                            dependOn: {
                                                                dataIndex: "voa2_work_mode",
                                                                expected: "0"
                                                            }
                                                        },
                                                        {
                                                            dataIndex: "voa2_attenuation_configuration",
                                                            label: "expected-attenuation",
                                                            unit: "dB",
                                                            inputType: "number",
                                                            pattern: /^([01]?\d|2\d|30)$/,
                                                            message: "Please enter a value between 0 and 30",
                                                            dependOn: {
                                                                dataIndex: "voa2_work_mode",
                                                                expected: "1"
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa2_threshold",
                                                            inputType: "number",
                                                            label: "threshold",
                                                            dependOn: {
                                                                dataIndex: "voa2_work_mode",
                                                                expected: "0"
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            type: "split"
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            type: "title",
                                                            title: "VOA3"
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa3_work_mode",
                                                            label: "Work mode of VOA3",
                                                            inputType: "radio",
                                                            data: {
                                                                options: [
                                                                    {title: "auto", value: "0"},
                                                                    {title: "manual", value: "1"}
                                                                ]
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa3_power_configuration",
                                                            label: "expected-power",
                                                            unit: "dBm",
                                                            inputType: "number",
                                                            dependOn: {
                                                                dataIndex: "voa3_work_mode",
                                                                expected: "0"
                                                            }
                                                        },
                                                        {
                                                            dataIndex: "voa3_attenuation_configuration",
                                                            label: "expected-attenuation",
                                                            unit: "dB",
                                                            inputType: "number",
                                                            pattern: /^([01]?\d|2\d|30)$/,
                                                            message: "Please enter a value between 0 and 30",
                                                            dependOn: {
                                                                dataIndex: "voa3_work_mode",
                                                                expected: "1"
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa3_threshold",
                                                            inputType: "number",
                                                            label: "threshold",
                                                            dependOn: {
                                                                dataIndex: "voa3_work_mode",
                                                                expected: "0"
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            type: "split"
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            type: "title",
                                                            title: "VOA4"
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa4_work_mode",
                                                            label: "Work mode of VOA4",
                                                            inputType: "radio",
                                                            data: {
                                                                options: [
                                                                    {title: "auto", value: "0"},
                                                                    {title: "manual", value: "1"}
                                                                ]
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa4_power_configuration",
                                                            label: "expected-power",
                                                            unit: "dBm",
                                                            inputType: "number",
                                                            dependOn: {
                                                                dataIndex: "voa4_work_mode",
                                                                expected: "0"
                                                            }
                                                        },
                                                        {
                                                            dataIndex: "voa4_attenuation_configuration",
                                                            label: "expected-attenuation",
                                                            unit: "dB",
                                                            inputType: "number",
                                                            pattern: /^([01]?\d|2\d|30)$/,
                                                            message: "Please enter a value between 0 and 30",
                                                            dependOn: {
                                                                dataIndex: "voa4_work_mode",
                                                                expected: "1"
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "voa4_threshold",
                                                            inputType: "number",
                                                            label: "threshold",
                                                            dependOn: {
                                                                dataIndex: "voa4_work_mode",
                                                                expected: "0"
                                                            }
                                                        }
                                                    ]
                                                ],
                                                getData: async () => {
                                                    const ip = ne_id.split(":")[0];
                                                    const _data = (await queryFMTConfig(ip, slotIndex)).data
                                                        ?.configData;
                                                    return {
                                                        voa1_attenuation_configuration:
                                                            _data.voa1_attenuation_configuration,
                                                        voa1_power_configuration: _data.voa1_power_configuration,
                                                        voa1_threshold: _data.voa1_threshold,
                                                        voa1_work_mode: _data.voa1_work_mode,
                                                        voa2_attenuation_configuration:
                                                            _data.voa2_attenuation_configuration,
                                                        voa2_power_configuration: _data.voa2_power_configuration,
                                                        voa2_threshold: _data.voa2_threshold,
                                                        voa2_work_mode: _data.voa2_work_mode,
                                                        voa3_attenuation_configuration:
                                                            _data.voa3_attenuation_configuration,
                                                        voa3_power_configuration: _data.voa3_power_configuration,
                                                        voa3_threshold: _data.voa3_threshold,
                                                        voa3_work_mode: _data.voa3_work_mode,
                                                        voa4_attenuation_configuration:
                                                            _data.voa4_attenuation_configuration,
                                                        voa4_power_configuration: _data.voa4_power_configuration,
                                                        voa4_threshold: _data.voa4_threshold,
                                                        voa4_work_mode: _data.voa4_work_mode
                                                    };
                                                },
                                                setDataAPI: () => {
                                                    const ip = ne_id.split(":")[0];
                                                    return {
                                                        APIName: modifyFMTConfig,
                                                        APIParameter: () => {
                                                            return {
                                                                ip,
                                                                slotIndex
                                                            };
                                                        }
                                                    };
                                                }
                                            });
                                        }}
                                    >
                                        Modify
                                    </DebounceButton>
                                </Space>
                            );
                        }
                    }
                ]
            },
            DCM: {
                columns: [
                    {dataIndex: "card"},
                    {
                        dataIndex: "module-state",
                        render: (value, _) => {
                            return (
                                <div>
                                    <Space size="small">
                                        {value === 0 ? (
                                            <Tag className="successTag">Normal</Tag>
                                        ) : (
                                            <Tag className="failedTag">Failed</Tag>
                                        )}
                                    </Space>
                                </div>
                            );
                        }
                    },
                    {dataIndex: "module-temperature", unit: "℃"},
                    {dataIndex: "tdc-setting-value", unit: "ps/nm"},
                    {dataIndex: "tdc-value", unit: "ps/nm"},
                    {
                        dataIndex: "frequency-interval",
                        unit: "GHz",
                        render: (value, _) => {
                            if (value === "196000.00") {
                                return "100G";
                            }
                            if (value === "196050.00") {
                                return "50G";
                            }
                            return "0";
                        }
                    },
                    {
                        dataIndex: "operation",
                        render: (value, rowData) => {
                            return (
                                <Space size={24}>
                                    <DebounceButton
                                        style={{color: colorPrimary}}
                                        type="link"
                                        title="Modify"
                                        onClick={() => {
                                            openCustomEditForm({
                                                title: "Modify",
                                                columnNum: 1,
                                                columns: [
                                                    [
                                                        {
                                                            dataIndex: "tdc_value",
                                                            label: "TDC Value",
                                                            unit: "ps/nm",
                                                            inputType: "number",
                                                            setKey: "tdc",
                                                            transformValue: value => {
                                                                return value.toString().padStart(3, "0");
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "frequency_interval",
                                                            label: "Frequency Interval",
                                                            unit: "GHz",
                                                            inputType: "select",
                                                            setKey: "frequency",
                                                            data: {
                                                                options: [
                                                                    {label: "100G", value: "196000.00"},
                                                                    {label: "50G", value: "196050.00"}
                                                                ]
                                                            },
                                                            transformValue: value => {
                                                                return (parseFloat(value) / 1000).toFixed(3);
                                                            }
                                                        }
                                                    ]
                                                ],
                                                getData: async () => {
                                                    const ip = ne_id.split(":")[0];
                                                    const _data = (await queryFMTConfig(ip, slotIndex)).data
                                                        ?.configData;
                                                    return {
                                                        frequency_interval: _data?.frequency_inter,
                                                        tdc_value: normalizeNumber(_data?.tdc_value)
                                                    };
                                                },
                                                setDataAPI: () => {
                                                    const ip = ne_id.split(":")[0];
                                                    return {
                                                        APIName: modifyFMTConfig,
                                                        APIParameter: () => {
                                                            return {
                                                                ip,
                                                                slotIndex
                                                            };
                                                        }
                                                    };
                                                }
                                            });
                                        }}
                                    >
                                        Modify
                                    </DebounceButton>
                                </Space>
                            );
                        }
                    }
                ]
            },
            TDCM: {
                columns: [
                    {dataIndex: "card"},
                    {
                        dataIndex: "module-state",
                        render: (value, _) => {
                            return (
                                <div>
                                    <Space size="small">
                                        {value === 0 ? (
                                            <Tag className="successTag">Normal</Tag>
                                        ) : (
                                            <Tag className="failedTag">Failed</Tag>
                                        )}
                                    </Space>
                                </div>
                            );
                        }
                    },
                    {dataIndex: "module-temperature", unit: "℃"},
                    {dataIndex: "tdc-setting-value", unit: "ps/nm"},
                    {dataIndex: "tdc-value", unit: "ps/nm"},
                    {
                        dataIndex: "frequency-interval",
                        unit: "GHz",
                        render: (value, _) => {
                            if (value === "196000.00") {
                                return "100G";
                            }
                            if (value === "196050.00") {
                                return "50G";
                            }
                            return "0";
                        }
                    },
                    {
                        dataIndex: "operation",
                        render: (value, rowData) => {
                            return (
                                <Space size={24}>
                                    <DebounceButton
                                        style={{color: colorPrimary}}
                                        type="link"
                                        title="Modify"
                                        onClick={() => {
                                            openCustomEditForm({
                                                title: "Modify",
                                                columnNum: 1,
                                                columns: [
                                                    [
                                                        {
                                                            dataIndex: "module_state",
                                                            label: "Module State",
                                                            inputType: "select",
                                                            data: {
                                                                options: [
                                                                    {label: "Normal", value: 0},
                                                                    {label: "Failed", value: 1},
                                                                    {label: "Busy", value: 1}
                                                                ]
                                                            },
                                                            noSetButton: true
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "tdc_setting_value",
                                                            label: "TDC Setting Value",
                                                            unit: "ps/nm",
                                                            inputType: "number",
                                                            noSetButton: true
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "frequency_interval",
                                                            label: "Frequency Interval",
                                                            unit: "GHz",
                                                            inputType: "select",
                                                            data: {
                                                                options: [
                                                                    {label: "100G", value: "196000.00"},
                                                                    {label: "50G", value: "196050.00"}
                                                                ]
                                                            },
                                                            noSetButton: true
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "module_temperature",
                                                            label: "Module temp",
                                                            unit: "℃",
                                                            inputType: "number",
                                                            noSetButton: true
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "tdc_value",
                                                            label: "TDC Value",
                                                            unit: "ps/nm",
                                                            inputType: "number",
                                                            noSetButton: true
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            type: "split"
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "tdc_value",
                                                            label: "TDC Value",
                                                            unit: "ps/nm",
                                                            inputType: "number",
                                                            setKey: "tdc",
                                                            transformValue: value => {
                                                                return value.toString().padStart(3, "0");
                                                            }
                                                        }
                                                    ],
                                                    [
                                                        {
                                                            dataIndex: "frequency_interval",
                                                            label: "Frequency Interval",
                                                            unit: "GHz",
                                                            inputType: "select",
                                                            data: {
                                                                options: [
                                                                    {label: "100G", value: "196000.00"},
                                                                    {label: "50G", value: "196050.00"}
                                                                ]
                                                            },
                                                            setKey: "frequency",
                                                            transformValue: value => {
                                                                return (parseFloat(value) / 1000).toFixed(3);
                                                            }
                                                        }
                                                    ]
                                                ],
                                                getData: async () => {
                                                    const ip = ne_id.split(":")[0];
                                                    const _data = (await queryFMTConfig(ip, slotIndex)).data
                                                        ?.configData;
                                                    return {
                                                        module_state: Number(_data?.module_state),
                                                        tdc_setting_value: normalizeNumber(_data?.tdc_setting_value),
                                                        frequency_interval: _data?.frequency_inter,
                                                        module_temperature: (
                                                            parseInt(_data?.temperature, 10) / 10
                                                        ).toString(),
                                                        tdc_value: normalizeNumber(_data?.tdc_value)
                                                    };
                                                },
                                                setDataAPI: () => {
                                                    const ip = ne_id.split(":")[0];
                                                    return {
                                                        APIName: modifyFMTConfig,
                                                        APIParameter: () => {
                                                            return {
                                                                ip,
                                                                slotIndex
                                                            };
                                                        }
                                                    };
                                                }
                                            });
                                        }}
                                    >
                                        Modify
                                    </DebounceButton>
                                </Space>
                            );
                        }
                    }
                ]
            },
            OPD: {
                columns: [
                    {dataIndex: "no"},
                    {dataIndex: "name"},
                    {dataIndex: "power", unit: "dBm"},
                    // {
                    //     dataIndex: "threshold",
                    //     render: (value, rowData) =>
                    //         editComponent(value, rowData, newVal => {
                    //             const ip = ne_id.split(":")[0];
                    //             return modifyFMTConfig({
                    //                 ip,
                    //                 slotIndex,
                    //                 key: `threshold_${rowData.no}`,
                    //                 value: newVal
                    //             });
                    //         })
                    // },
                    {
                        dataIndex: "route-type",
                        render: (value, rowData) => {
                            return value === "1" ? "Online" : "Offline";
                        }
                    },
                    {
                        dataIndex: "wavelength",
                        unit: "nm",
                        align: "left",
                        render: (value, rowData, index) =>
                            selectComponent(
                                value,
                                rowData,
                                [
                                    // {value: "0.00", label: "0"},
                                    {value: "0.00", label: "1310"},
                                    {value: "1.00", label: "1550"}
                                ],
                                newVal => {
                                    const ip = ne_id.split(":")[0];
                                    modifyFMTConfig({
                                        ip,
                                        slotIndex,
                                        key: `wavelength_${rowData.no}`,
                                        value: parseFloat(newVal).toString()
                                    })
                                        .then(response => {
                                            if (response.errorCode === 0) {
                                                setTableData(prevData => {
                                                    const newData = [...prevData];
                                                    newData[index].wavelength = newVal;
                                                    return newData;
                                                });
                                                message.success("Modify Success");
                                            } else {
                                                message.error("Modify Failed");
                                            }
                                        })
                                        .catch(() => {});
                                }
                            )
                    },
                    {
                        dataIndex: "port-note",
                        render: (value, rowData, index) =>
                            editComponent(value, rowData, newVal => {
                                const ip = ne_id.split(":")[0];
                                modifyFMTPortNote({
                                    ip,
                                    slotIndex,
                                    port: rowData.no,
                                    note: newVal
                                })
                                    .then(response => {
                                        if (response.errorCode === 0) {
                                            setTableData(prevData => {
                                                const newData = [...prevData];
                                                newData[index]["port-note"] = newVal;
                                                return newData;
                                            });
                                            message.success("Update port note success");
                                        } else {
                                            message.error("Update port note failed");
                                        }
                                    })
                                    .catch(() => {});
                            })
                    }
                ]
            },
            "4M4": {
                columns: [
                    {dataIndex: "name"},
                    {
                        dataIndex: "module-type",
                        render: value => {
                            if (value?.[0] === "02") {
                                return "ZR+";
                            }
                            if (value?.[0] === "04") {
                                return "ZR";
                            }
                            return NULL_VALUE;
                        }
                    },
                    {dataIndex: "module-wavelength", unit: "nm"},
                    {dataIndex: "input-optical-power", unit: "dBm"},
                    {dataIndex: "output-optical-power", unit: "dBm"},
                    {
                        dataIndex: "module-state",
                        render: value => {
                            if (value === "0.00") {
                                return NULL_VALUE;
                            }
                            return (
                                <div style={{display: "flex", alignItems: "center"}}>
                                    <div
                                        style={{
                                            width: 5,
                                            height: 5,
                                            backgroundColor: "#14c9bb",
                                            borderRadius: "50%",
                                            marginRight: 5
                                        }}
                                    />
                                    <div>Present</div>
                                </div>
                            );
                        }
                    },
                    {dataIndex: "laser-switch"},
                    {
                        dataIndex: "port-note",
                        render: (value, rowData, index) =>
                            editComponent(value, rowData, newVal => {
                                const ip = ne_id.split(":")[0];
                                modifyDCSPortNote({
                                    ip,
                                    slotIndex,
                                    port: rowData.no,
                                    note: newVal
                                })
                                    .then(response => {
                                        if (response.errorCode === 0) {
                                            setTableData(prevData => {
                                                const newData = [...prevData];
                                                newData[index]["port-note"] = newVal;
                                                return newData;
                                            });
                                            message.success("Update port note success");
                                        } else {
                                            message.error("Update port note failed");
                                        }
                                    })
                                    .catch(() => {});
                            })
                    },
                    {
                        dataIndex: "operation",
                        fixed: "right",
                        render: (value, rowData) => (
                            <DebounceButton
                                disabled={rowData["module-state"] === "0.00"}
                                onClick={() => {
                                    openDisplayDialog({
                                        title: rowData.name.replace("PORT", "TRANSCEIVER"),
                                        // width: 900,
                                        columns: [
                                            [{dataIndex: "sn"}, {dataIndex: "distance", unit: "km"}],
                                            [{dataIndex: "pn"}, {dataIndex: "temperature", unit: "°C"}]
                                        ],
                                        getData: async () => {
                                            return {
                                                sn: rowData?.["serial-no"]?.slice(0, -4),
                                                distance: rowData?.["transmission-distance"],
                                                pn: rowData?.["board-model"],
                                                temperature: rowData?.temperature
                                            };
                                        }
                                    });
                                }}
                                type="link"
                                title="More"
                                style={{color: rowData["module-state"] === "0.00" ? "#b9b9b9" : colorPrimary}}
                            >
                                More
                            </DebounceButton>
                        )
                    }
                ]
            },
            "4T4": {
                columns: [
                    {dataIndex: "name"},
                    {
                        dataIndex: "module-type",
                        render: value => {
                            if (value === "1.00") {
                                return "1x400G ZR+";
                            }
                            if (value === "2.00") {
                                return "4x100G ZR+";
                            }
                            return NULL_VALUE;
                        }
                    },
                    {dataIndex: "module-wavelength", unit: "nm"},
                    {dataIndex: "input-optical-power", unit: "dBm"},
                    {dataIndex: "output-optical-power", unit: "dBm"},
                    {
                        dataIndex: "module-state",
                        render: value => {
                            if (value === "0.00") {
                                return NULL_VALUE;
                            }
                            return (
                                <div style={{display: "flex", alignItems: "center"}}>
                                    <div
                                        style={{
                                            width: 5,
                                            height: 5,
                                            backgroundColor: "#14c9bb",
                                            borderRadius: "50%",
                                            marginRight: 5
                                        }}
                                    />
                                    <div>Present</div>
                                </div>
                            );
                        }
                    },
                    {dataIndex: "laser-switch"},
                    {
                        dataIndex: "port-note",
                        render: (value, rowData, index) =>
                            editComponent(value, rowData, newVal => {
                                const ip = ne_id.split(":")[0];
                                modifyDCSPortNote({
                                    ip,
                                    slotIndex,
                                    port: rowData.no,
                                    note: newVal
                                })
                                    .then(response => {
                                        if (response.errorCode === 0) {
                                            setTableData(prevData => {
                                                const newData = [...prevData];
                                                newData[index]["port-note"] = newVal;
                                                return newData;
                                            });
                                            message.success("Update port note success");
                                        } else {
                                            message.error("Update port note failed");
                                        }
                                    })
                                    .catch(() => {});
                            })
                    },
                    {
                        dataIndex: "operation",
                        fixed: "right",
                        render: (value, rowData) => (
                            <DebounceButton
                                disabled={rowData["module-state"] === "0.00"}
                                onClick={() => {
                                    openDisplayDialog({
                                        title: rowData.name.replace("PORT", "TRANSCEIVER"),
                                        width: 900,
                                        columns: [
                                            [{dataIndex: "sn"}, {dataIndex: "distance", unit: "km"}],
                                            [{dataIndex: "pn"}, {dataIndex: "temperature", unit: "°C"}]
                                        ],
                                        getData: async () => {
                                            return {
                                                sn: rowData?.["serial-no"]?.slice(0, -4),
                                                distance: rowData?.["transmission-distance"],
                                                pn: rowData?.["board-model"],
                                                temperature: rowData?.temperature
                                            };
                                        }
                                    });
                                }}
                                type="link"
                                title="More"
                                style={{color: rowData["module-state"] === "0.00" ? "#b9b9b9" : colorPrimary}}
                            >
                                More
                            </DebounceButton>
                        )
                    }
                ]
            }
        };

        const titleConfig = {
            chassis: "Slot List",
            FMT: "Slot List"
        };
        const tableConfig = TableConfigs[type] ?? {};
        setColumns(
            tableConfig.columns?.map?.(i => ({
                ...i,
                title: i.label ?? getText(i.dataIndex) + (i.unit ? ` (${i.unit})` : "")
            })) ?? []
        );
        setTableData(neData ?? []);
        setTitle(titleConfig[type] ?? "Port List");
    }, [slotIndex, data]);

    return (
        <Card
            loading={!type}
            title={title}
            className={styles.card}
            style={tableData.length > 5 ? {height: "auto"} : {}}
        >
            <Table
                columns={columns}
                dataSource={tableData}
                rowKey={(record, index) => index}
                scroll={{
                    x: "max-content"
                }}
                tableLayout="fixed"
                style={{flex: 1}}
                bordered
                pagination={false}
            />
        </Card>
    );
};

export default PortList;
