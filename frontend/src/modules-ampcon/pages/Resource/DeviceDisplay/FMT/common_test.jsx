import {Space, Empty, Form, message, Table, Typography} from "antd";
import React, {useState, useEffect} from "react";
import dayjs from "dayjs";
import EmptyPic from "@/assets/images/App/empty.png";
import {
    queryFMTConfig,
    modifyFMTConfig,
    modifyFMTPortNote,
    getFMTDevicePort,
    getFMTDeviceSinglePort
} from "@/modules-ampcon/apis/fmt";
import Icon from "@ant-design/icons";
import openCustomEditForm from "../components/custom_edit_form";
import {DebounceButton} from "../utils";
import {editTableIcon, exportDisabledSvg, exportSvg, refreshDisabledSvg, refreshSvg} from "@/utils/common/iconSvg";

const {Paragraph} = Typography;
/**
    OA板卡类型小类:
    FMT20PA-EDFA
    FMT17BA-EDFA
    FMT26PA-51EDFA
    FMT17BA-51EDFA
    FMT22BA-EDFA
    FMTPA-Array
    FMTBA-Array
    HPA
    FS-SOA
    EDFA-LA
* */

const editComponent = (value, rowData, submit) => {
    return (
        <Paragraph
            style={{margin: 0, display: "flex"}}
            editable={{
                maxLength: 32,
                icon: <Icon component={editTableIcon} />,
                text: value,
                onChange: newVal => {
                    if (newVal === value) return;
                    // direct submission
                    submit(newVal || "");
                },
                triggerType: ["icon", "text"]
            }}
        >
            <div style={{flex: 1}}>{value}</div>
        </Paragraph>
    );
};

export const DynamicTable = ({
    tabType,
    data,
    CardId,
    CardName,
    NeIP,
    NeName = "",
    PortName = "",
    showExportButton = true,
    showRefreshButton = true
}) => {
    const [isShowSpin, setIsShowSpin] = useState(false);
    const [form] = Form.useForm();
    const modifyData = {cardId: CardId, ip: NeIP};
    const reference = CardName ? CardName.match(/FMT(\d+)/)?.[1] : null; // undefined 返回null
    const {ports_data, ...rootData} = data || {};
    const parsedPortsData = ports_data ? JSON.parse(ports_data) : {};
    const refreshDisabled = !CardId;

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: Object.keys(parsedPortsData).length
    });

    const [tableData, setTableData] = useState([]);
    useEffect(() => {
        if (data?.ports_data) {
            const parsedPortsData = JSON.parse(data.ports_data);
            const initialData = Object.entries(parsedPortsData).map(([portId, portData]) => ({
                key: portId,
                ...Object.fromEntries(Object.entries(data).map(([key, value]) => [key, value === "" ? "--" : value])),
                ...Object.fromEntries(
                    Object.entries(portData).map(([key, value]) => [key, value === "" ? "--" : value])
                ),
                "NE Name": NeName,
                "NE ID": `${NeIP}:4001`,
                "Card Name": CardName
            }));
            setTableData(initialData);
        } else {
            setTableData([]);
        }
    }, [data]);

    if (!data || !data.ports_data) {
        return <Empty image={EmptyPic} description="No Data" imageStyle={{margin: 0}} />;
    }
    const TableConfigs = {
        power: {
            EDFA: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "NE Name"},
                    {dataIndex: "NE ID"},
                    {dataIndex: "type", title: "Card Type"},
                    {dataIndex: "Card Name"},
                    {dataIndex: "slot_index", title: "Slot No."},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "Input Optical Power", unit: "dBm"},
                    {dataIndex: "Output Optical Power", unit: "dBm"},
                    {dataIndex: "Input Warning Threshold", unit: "dBm"},
                    {dataIndex: "Output Warning Threshold", unit: "dBm"},
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const inputAlarmThresholdKey = `rx_power_alarm_threshold`;
                            const outputAlarmThresholdKey = `tx_power_alarm_threshold`;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: inputAlarmThresholdKey,
                                                        label: `Input warning threshold`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        required: true,
                                                        transformValue: value => {
                                                            return value.toString();
                                                        }
                                                    }
                                                ],
                                                [
                                                    {
                                                        dataIndex: outputAlarmThresholdKey,
                                                        label: `Output warning threshold`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        required: true,
                                                        transformValue: value => {
                                                            return value.toString();
                                                        }
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [inputAlarmThresholdKey]: _data.rx_power_alarm_threshold,
                                                    [outputAlarmThresholdKey]: _data.tx_power_alarm_threshold
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            },
            OEO: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "NE Name"},
                    {dataIndex: "NE ID"},
                    {dataIndex: "type", title: "Card Type"},
                    {dataIndex: "Card Name"},
                    {dataIndex: "slot_index", title: "Slot No."},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "Input Optical Power", unit: "dBm"},
                    {dataIndex: "Output Optical Power", unit: "dBm"},
                    {dataIndex: "Input Alarm Threshold", unit: "dBm"},
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const portName = rowData.Name;
                            const portNames = Object.values(parsedPortsData).map(port => port.Name.toLowerCase());
                            const portIndex = portNames.indexOf(portName.toLowerCase());
                            const inputAlarmThresholdKey = `input_alarm_threshold_${portName.toLowerCase()}`;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: inputAlarmThresholdKey,
                                                        label: `Input Alarm ${portName} threshold`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        required: true,
                                                        transformValue: value => {
                                                            return value.toString();
                                                        }
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [inputAlarmThresholdKey]: _data.input_alarm_threshold_[portIndex]
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            },
            OLP: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "NE Name"},
                    {dataIndex: "NE ID"},
                    {dataIndex: "type", title: "Card Type"},
                    {dataIndex: "Card Name"},
                    {dataIndex: "slot_index", title: "Slot No."},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "VOA Attenuation", unit: "dB"},
                    {dataIndex: "Optical Power", unit: "dBm"},
                    {dataIndex: "Optical Power Threshold", unit: "dBm"},
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const portName = rowData.Name;
                            const portNames = Object.values(parsedPortsData).map(port => port.Name.toLowerCase());
                            const portIndex = portNames.indexOf(portName.toLowerCase());
                            const ThresholdKey = `${portName.toLowerCase()} Alarm threshold`;
                            const portNameMapping = {
                                "PORT-1-APSP-IN": "r1",
                                "PORT-2-APSS-IN": "r2",
                                "PORT-3-APSC-OUT": "tx"
                            };
                            const prefix = portNameMapping[portName] || portName;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: ThresholdKey,
                                                        label: `${prefix} Alarm threshold`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        transformValue: value => value.toString()
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [ThresholdKey]: _data[prefix]
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            },
            VOA: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "NE Name"},
                    {dataIndex: "NE ID"},
                    {dataIndex: "type", title: "Card Type"},
                    {dataIndex: "Card Name"},
                    {dataIndex: "slot_index", title: "Slot No."},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "Actual Attenuation", unit: "dB"},
                    {dataIndex: "Expected Attenuation", unit: "dB"},
                    {dataIndex: "Actual Power", unit: "dBm"},
                    {dataIndex: "Expected Power", unit: "dBm"},
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const portName = rowData.Name;
                            const ThresholdKey = `${portName.toLowerCase()}_threshold`;
                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: ThresholdKey,
                                                        label: `${portName} threshold`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        required: true,
                                                        transformValue: value => {
                                                            return value.toString();
                                                        }
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [ThresholdKey]: _data[ThresholdKey]
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            },
            OPD: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "NE Name"},
                    {dataIndex: "NE ID"},
                    {dataIndex: "type", title: "Card Type"},
                    {dataIndex: "Card Name"},
                    {dataIndex: "slot_index", title: "Slot No."},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "VOA Attenuation", unit: "dB"},
                    {dataIndex: "Power", unit: "dBm"},
                    {dataIndex: "Wavelength", unit: "nm"},
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const Index = rowData.No;
                            const portNames = Object.values(parsedPortsData).map(port => port.No.toLowerCase());
                            const portIndex = portNames.indexOf(Index.toLowerCase());
                            const Wavelength = `wavelength_${Index.toLowerCase()}`;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: Wavelength,
                                                        label: `Wavelength${Index} `,
                                                        unit: "nm",
                                                        inputType: "select",
                                                        required: true,
                                                        data: {
                                                            options: [
                                                                {label: "1550", value: "1"},
                                                                {label: "1310", value: "0"}
                                                            ]
                                                        }
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [Wavelength]: _data.wavelength_[portIndex]
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            }
        },
        edfa: {
            EDFA: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "Input Optical Power", unit: "dBm"},
                    {dataIndex: "Output Optical Power", unit: "dBm"},
                    {dataIndex: "VOA Attenuation Value", unit: "dB"},
                    {dataIndex: "VOA Attenuation Expected", unit: "dB"},
                    {dataIndex: "EDFA Gain Value", unit: "dB"},
                    {dataIndex: "Expected EDFA Gain", unit: "dB"},
                    {dataIndex: "Gain Slope", unit: "dB/40nm"},
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const GainAdjustment = `gain_adjustmen`;
                            const Pump1State = `pump1_state`;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: GainAdjustment,
                                                        label: `GainAdjustment`,
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        required: true,
                                                        transformValue: value => {
                                                            return value.toString();
                                                        }
                                                    }
                                                ],
                                                [
                                                    {
                                                        dataIndex: Pump1State,
                                                        label: "Pump1 State",
                                                        inputType: "select",
                                                        required: true,
                                                        data: {
                                                            options: [
                                                                {label: "Open", value: "1"},
                                                                {label: "Close", value: "0"}
                                                            ]
                                                        }
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [GainAdjustment]: _data.gain_adjustment,
                                                    [Pump1State]: _data.pump1_state
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            }
        },
        oeo: {
            OEO: {
                columns: [
                    {dataIndex: "No", title: "Index", fixed: "left"},
                    {dataIndex: "Name", title: "Port Name"},
                    {dataIndex: "Module Wavelength", unit: "nm"},
                    {dataIndex: "Input Optical Power", unit: "dBm"},
                    {dataIndex: "Output Optical Power", unit: "dBm"},
                    {dataIndex: "Input Alarm Threshold", unit: "dBm"},
                    {dataIndex: "Transmission Distance"},
                    {dataIndex: "Module Temperature", unit: "℃"},
                    {dataIndex: "Rate"},
                    {
                        dataIndex: "Service Notes",
                        render: (value, rowData) => {
                            return editComponent(value, rowData, async newVal => {
                                const oldValue = value;
                                setTableData(prevData => {
                                    const newData = [...prevData];
                                    const rowIndex = newData.findIndex(item => item.key === rowData.key);
                                    if (rowIndex !== -1) {
                                        newData[rowIndex]["Service Notes"] = newVal;
                                    }
                                    return newData;
                                });
                                try {
                                    const rs = await modifyFMTPortNote({
                                        ip: NeIP,
                                        cardId: CardId,
                                        port: rowData.key,
                                        note: newVal
                                    });
                                    if (!rs || rs.errorCode !== 0) {
                                        throw new Error("Update failed");
                                    }
                                    message.success("Update service notes success");
                                } catch (error) {
                                    setTableData(prevData => {
                                        const newData = [...prevData];
                                        const rowIndex = newData.findIndex(item => item.key === rowData.key);
                                        if (rowIndex !== -1) {
                                            newData[rowIndex]["Service Notes"] = oldValue;
                                        }
                                        return newData;
                                    });
                                    message.error("Update service notes failed");
                                }
                            });
                        }
                    },
                    {
                        dataIndex: "operation",
                        title: "Operation",
                        fixed: "right",
                        render: (value, rowData) => {
                            const portName = rowData.Name;
                            const portNames = Object.values(parsedPortsData).map(port => port.Name.toLowerCase());
                            const portIndex = portNames.indexOf(portName.toLowerCase());
                            const controlModeKey = `control_mode_${portName.toLowerCase()}`;
                            const workModelKey = `work_model_${portName.toLowerCase()}`;
                            const inputAlarmThresholdKey = `input_alarm_threshold_${portName.toLowerCase()}`;

                            return (
                                <DebounceButton
                                    type="link"
                                    title="Modify"
                                    onClick={async () => {
                                        openCustomEditForm({
                                            title: "Modify",
                                            columnNum: 1,
                                            columns: [
                                                [
                                                    {
                                                        dataIndex: controlModeKey,
                                                        label: "Control Model",
                                                        inputType: "select",
                                                        required: true,
                                                        data: {
                                                            options: [
                                                                {label: "Open", value: "1"},
                                                                {label: "Close", value: "0"}
                                                            ]
                                                        }
                                                    }
                                                ],
                                                [
                                                    {
                                                        dataIndex: workModelKey,
                                                        label: "Work Model",
                                                        inputType: "select",
                                                        required: true,
                                                        data: {
                                                            options: [
                                                                {label: "Normal Model", value: "1"},
                                                                {label: "Bypass Model", value: "2"},
                                                                {label: "Loopback Model", value: "3"}
                                                            ]
                                                        }
                                                    }
                                                ],
                                                [
                                                    {
                                                        dataIndex: inputAlarmThresholdKey,
                                                        label: "Input Alarm Threshold",
                                                        unit: "dB",
                                                        inputType: "number",
                                                        step: 0.01,
                                                        required: true,
                                                        transformValue: value => {
                                                            return value.toString();
                                                        }
                                                    }
                                                ]
                                            ],
                                            getData: async () => {
                                                const ip = NeIP;
                                                const response = await queryFMTConfig(ip, null, CardId);
                                                const _data = response.data?.configData;

                                                const initialValues = {
                                                    [controlModeKey]: _data.control_mode_[portIndex],
                                                    [workModelKey]: _data.work_model_[portIndex],
                                                    [inputAlarmThresholdKey]: _data.input_alarm_threshold_[portIndex]
                                                };
                                                return initialValues;
                                            },
                                            setDataAPI: () => {
                                                const ip = NeIP;
                                                return {
                                                    APIName: modifyFMTConfig,
                                                    APIParameter: () => {
                                                        return {
                                                            ip,
                                                            cardId: CardId
                                                        };
                                                    }
                                                };
                                            }
                                        });
                                    }}
                                >
                                    Modify
                                </DebounceButton>
                            );
                        }
                    }
                ]
            }
        }
    };
    const getTableConfig = () => {
        const typeMap = {
            power: data.type,
            edfa: "EDFA",
            oeo: "OEO"
        };
        const configType = typeMap[tabType];
        return TableConfigs[tabType]?.[configType]?.columns || [];
    };

    const columns = getTableConfig().map(col => {
        const {dataIndex, title, unit, render, fixed} = col;
        const column = {
            title: title || dataIndex.replace(/_/g, " ").replace(/(^|\s)\S/g, match => match.toUpperCase()),
            dataIndex,
            key: dataIndex,
            fixed,
            sorter: (a, b) => {
                if (typeof a[dataIndex] === "number" && typeof b[dataIndex] === "number") {
                    return a[dataIndex] - b[dataIndex];
                }
                if (typeof a[dataIndex] === "string" && typeof b[dataIndex] === "string") {
                    return a[dataIndex].localeCompare(b[dataIndex]);
                }
                return 0;
            }
        };
        if (unit) {
            column.title += ` (${unit})`;
        }
        if (render) {
            column.render = render;
        }
        return column;
    });

    const onExportToExcel = () => {
        try {
            if (!tableData?.length) {
                message.warning("No Data");
                return;
            }

            const exportColumns = columns.filter(col => !col.hidden && col.title !== "Operation");
            const headers = exportColumns.map(col => col.title).join(",");

            const data = tableData.reduce((res, row) => {
                const lineData = `${columns
                    .filter(col => !col.hidden)
                    .map(col => {
                        const matchValue = row[col.dataIndex];
                        return `"${matchValue ?? ""}"`;
                    })
                    .join(",")}\n`;
                return `${res}${lineData}`;
            }, `\uFEFF${headers}\n`);

            const blob = new Blob([data], {type: "text/csv"});
            const downloadLink = document.createElement("a");
            downloadLink.href = URL.createObjectURL(blob);
            const fileName = `${tabType}_${dayjs().format("YYYY-MM-DD_HH:mm:ss")}.csv`;
            downloadLink.download = fileName;
            downloadLink.click();

            message.success("Export Success");
        } catch (error) {
            message.error(`Export Failed：${error.message}`);
        }
    };

    const doRefresh = async type => {
        try {
            setIsShowSpin(true);
            let response;
            if (PortName) {
                response = await getFMTDeviceSinglePort(CardId, PortName);
            } else {
                response = await getFMTDevicePort(CardId, type);
            }

            if (response?.data) {
                let newData;
                if (type === "power") {
                    if (PortName) {
                        newData = response.data;
                    } else {
                        newData = response.data.info;
                    }
                } else if (["oeo", "edfa"].includes(type)) {
                    newData = response.data.ports_info;
                }

                const newPortsData = newData?.ports_data ? JSON.parse(newData.ports_data) : {};
                const newDataSource = Object.entries(newPortsData).map(([portId, portData]) => ({
                    key: portId,
                    ...Object.fromEntries(
                        Object.entries(newData).map(([key, value]) => [key, value === "" ? "--" : value])
                    ),
                    ...Object.fromEntries(
                        Object.entries(portData).map(([key, value]) => [key, value === "" ? "--" : value])
                    ),
                    "NE Name": NeName,
                    "NE ID": NeIP,
                    "Card Name": CardName
                }));

                setTableData(newDataSource);
                setPagination(prev => ({
                    ...prev,
                    total: newDataSource.length
                }));
                message.success("Refresh Success");
            } else {
                message.error("Refresh Failed");
            }
        } catch (error) {
            message.error(`Refresh Failed：${error.message}`);
        } finally {
            setIsShowSpin(false);
        }
    };

    return (
        <>
            <div style={{display: "flex", justifyContent: "space-between"}}>
                <Space style={{marginBottom: 24}} size={16}>
                    {showExportButton && (
                        <DebounceButton
                            icon={<Icon component={!tableData.length ? exportDisabledSvg : exportSvg} />}
                            onClick={onExportToExcel}
                            title="Export"
                            disabled={!tableData.length}
                        >
                            Export
                        </DebounceButton>
                    )}
                    {showRefreshButton && (
                        <DebounceButton
                            onClick={() => doRefresh(tabType)}
                            icon={<Icon component={refreshDisabled ? refreshDisabledSvg : refreshSvg} />}
                            disabled={refreshDisabled}
                        >
                            Refresh
                        </DebounceButton>
                    )}
                </Space>
            </div>
            <Table
                columns={columns}
                dataSource={tableData}
                bordered
                pagination={{
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    total: tableData.length,
                    onChange: (page, pageSize) => {
                        setPagination({...pagination, current: page, pageSize});
                    },
                    showSizeChanger: true,
                    pageSizeOptions: ["10", "20", "50", "100"],
                    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
                }}
                style={{whiteSpace: "nowrap"}}
            />
        </>
    );
};
