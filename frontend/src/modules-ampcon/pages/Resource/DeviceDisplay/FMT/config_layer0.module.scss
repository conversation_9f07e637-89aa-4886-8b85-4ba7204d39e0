.container {
    flex: 1;
    display: flex;
    flex-direction: column;
    // overflow: hidden;
    background-color: #ffffff;

    &_tag_green {
        font-size: 14px;
        background: rgba(43, 193, 116, 0.1);
        border-radius: 2px 2px 2px 2px;
        border: 1px solid #2bc174;
        color: #2bc174;
    }

    &_tag_red {
        font-size: 14px;
        color: #f53f3f;
        background: #fff0f6;
        border-color: #ffadd2;
    }

    .up {
        display: flex;
        height: 350px;

        .tree {
            width: 350px;
            border: 1px solid #E7E7E7;
            border-radius: 2px;
            overflow: auto;
            padding: 24px 24px 0;
        }

        .view {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid #E7E7E7;
            border-radius: 2px;
            margin-left: 20px;
            text-align: center;
            background: #f8fafb;
        }
    }

    .down {
        flex: 1;
        display: flex;
        margin-top: 20px;
        padding: 24px;
        padding-top: 0;
        //margin-bottom: 20px;

        border-radius: 2px;
        border: 1px solid #e7e7e7;
        overflow: hidden;

        :global {
            .ant-tabs-tabpane {
                padding: 0;
            }
            .ant-tabs-nav-list .ant-tabs-tab:first-child {
                padding-left: 0px !important;
            }
        }

        .lldp {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
    }

    .service_optics_down {
        border: none;
        padding: 0;
    }

    :global {
        .ant-tabs-content,
        .ant-tabs-tabpane {
            overflow: hidden;
        }
    }
}
.alarmStyle {
    margin-top: -20px;
    width: 100%;
    th {
        white-space: nowrap;
        background-color: #fafafa !important;
    }
    td {
        background-color: #ffffff !important;
    }
}
.infoStyle {
    background: rgba(0, 183, 255, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #14c9bb;
    color: #14c9bb;
    font-size: 14px;
}
.warnStyle {
    background-color: rgba(0, 183, 255, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #00b7ff;
    font-size: 14px;
    color: #00b7ff;
}
.minorStyle {
    background-color: rgba(255, 187, 0, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #ffbb00;
    color: #ffbb00;
    font-size: 14px;
}
.majorStyle {
    background-color: rgba(255, 123, 67, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #ff7b43;
    font-size: 14px;
    color: #ff7b43;
}
.criticalStyle {
    background-color: rgba(245, 63, 63, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #f53f3f;
    color: #f53f3f;
    font-size: 14px;
}
