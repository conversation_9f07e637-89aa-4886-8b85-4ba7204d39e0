import styles from "@/modules-ampcon/pages/Resource/DeviceDisplay/FMT/config_layer0.module.scss";
import React, {useEffect, useState, useRef} from "react";
import {useDispatch, useSelector} from "react-redux";
import {useForm} from "antd/es/form/Form";
import {message, Tabs, Tree, Tag, Button, Space, Form, Input, Empty, Modal, Divider, Select} from "antd";
import {updateSwitchAlarm} from "@/store/modules/otn/notificationSlice";
import {getFMTDevicePort, getFilterFMTEventAPI} from "@/modules-ampcon/apis/fmt";
import {
    EDFA_LA,
    FMT4DL_OEO1OGSFP,
    FMT4DL_OEO25GSFP28,
    FMTBA_EDFA,
    FMTPA_EDFA,
    FMT40_DCM,
    FMT_BDOLP,
    FMT_BDSplitter,
    FMT_OLP2,
    FMTBA_Array,
    FMTPA_Array,
    FMT_OLP,
    FS_SOA,
    FS_TDCM,
    FS_VOA_C,
    FSBR,
    HPA
} from "@/modules-ampcon/pages/Resource/DeviceDisplay/FMT/cards_svg";
import Icon from "@ant-design/icons";
import {AmpConCustomTable, createColumnConfig, AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {DynamicTable} from "@/modules-ampcon/pages/Resource/DeviceDisplay/FMT/common";
import EmptyPic from "@/assets/images/App/empty.png";
import {ackSwitchAlarm, getSwitchAlarm} from "@/modules-ampcon/apis/monitor_api";

/**
    OA板卡类型小类:
    FMT20PA-EDFA
    FMT17BA-EDFA
    FMT26PA_EDFA
    FMT26PA-51EDFA
    FMT17BA-51EDFA
    FMT22BA-EDFA
    FMTPA-Array
    FMTBA-Array
    HPA
    FS-SOA
    EDFA-LA
* */

const deviceSchemaMapping = {
    "FMT40-DCM": FMT40_DCM,
    "FMT80-DCM": FMT40_DCM,
    "EDFA-BA": FMTBA_EDFA,
    "FMT20PA-EDFA": FMTPA_EDFA,
    "FMT17BA-EDFA": FMTBA_EDFA,
    "FMT22BA-EDFA": FMTBA_EDFA,
    "FMT26PA-EDFA": FMTPA_EDFA,
    "FMT26PA-51EDFA": FMTPA_EDFA,
    "FMT17BA-51EDFA": FMTBA_EDFA,
    "FMTPA-Array": FMTPA_Array,
    "FMTBA-Array": FMTBA_Array,
    "EDFA-LA": EDFA_LA,
    HPA,
    OEO10GSFP: FMT4DL_OEO1OGSFP,
    OEO25GSFP28: FMT4DL_OEO25GSFP28,
    "FS-OLP": FMT_OLP2,
    "FMT-OLP2": FMT_OLP2,
    "FMT-OLP": FMT_OLP,
    "FMT-BDOLP": FMT_BDOLP,
    "FMT-BDSplitter": FMT_BDSplitter,
    "FSBR-1x2": FSBR,
    "FS-SOA": FS_SOA,
    "FS-VOA-C": FS_VOA_C,
    "FS-TDCM": FS_TDCM
};
const {TextArea} = Input;

const DynamicIcon = ({cardSvgName}) => {
    if (cardSvgName === "-1") {
        return <Empty image={EmptyPic} description="No Data" imageStyle={{marginTop: 70}} />;
    }
    return <Icon component={deviceSchemaMapping[cardSvgName]} alt="" />;
};

const AlarmTabTable = ({NeIp}) => {
    const [form] = useForm();
    const alarmRef = useRef(null);
    const dispatch = useDispatch();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedRecord, setSelectedRecord] = useState("");
    const currentUser = useSelector(state => state.user.userInfo);

    const tagStyles = {
        info: styles.infoStyle,
        warn: styles.warnStyle,
        minor: styles.minorStyle,
        major: styles.majorStyle,
        critical: styles.criticalStyle,
        error: styles.criticalStyle
    };

    const tagText = {
        info: "Info",
        warn: "Warning",
        minor: "Minor",
        major: "Major",
        critical: "Critical",
        error: "Critical"
    };

    const tagStyle = type => {
        const style = tagStyles[type] || "";
        const text = tagText[type] || "";
        return <Tag className={style}>{text}</Tag>;
    };
    const formItems = () => {
        return (
            <Form.Item
                name="operatorText"
                label="Operator Text"
                tooltip="Text provided by operator when changing alarm state."
                rules={[
                    {
                        required: true,
                        pattern: /^((?!&|<|>|"|').){0,32}$/,
                        message: "Only support 32 characters and not support the symbols < > & \" '"
                    }
                ]}
                labelCol={{span: 8}}
                wrapperCol={{span: 14}}
                labelAlign="left"
                layout="horizontal"
                LabelWrap
                className="label-wrap"
            >
                <TextArea style={{minHeight: 50, width: 280}} />
            </Form.Item>
        );
    };

    const columns = [
        {
            ...createColumnConfig("NE Name", "sn"),
            fixed: "left"
        },
        {
            ...createColumnConfig("Serverity", "type"),
            render: (_, record) => (
                <div>
                    <Space>{tagStyle(record.type)}</Space>
                </div>
            )
        },
        createColumnConfig("Create Time", "create_time"),
        createColumnConfig("Count", "count"),
        createColumnConfig("Text", "msg"),
        createColumnConfig("Operator Text", "operator_text"),
        createColumnConfig("Operator Name", "operator_name"),
        createColumnConfig("Time Acknowledged", "operator_time"),
        {
            title: "Operation",
            dataIndex: "",
            fixed: "right",
            key: "operation",
            render: (_, record) => (
                <a
                    style={{color: "#14c9bb"}}
                    onClick={() => {
                        setIsModalOpen(true);
                        setSelectedRecord(record.id);
                    }}
                >
                    ACK
                </a>
            )
        }
    ];

    return (
        <>
            <AmpConCustomModalForm
                title="ACK"
                isModalOpen={isModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 5
                    }
                }}
                CustomFormItems={formItems}
                onCancel={() => {
                    form.resetFields();
                    setIsModalOpen(false);
                }}
                onSubmit={async values => {
                    const ret = await ackSwitchAlarm({
                        eventId: selectedRecord,
                        operatorName: currentUser.username,
                        operatorText: values.operatorText
                    });
                    if (ret.status === 200) {
                        form.resetFields();
                        setIsModalOpen(false);
                        await getSwitchAlarm().then(res => {
                            if (res.status === 200) {
                                dispatch(updateSwitchAlarm(res.data));
                                message.success("Success saved!");
                            } else {
                                message.error("Error fetching data.");
                            }
                        });
                    } else {
                        message.error(ret.msg);
                    }
                }}
                modalClass="ampcon-middle-modal"
            />
            <AmpConCustomTable
                className={styles.alarmStyle}
                fetchAPIInfo={getFilterFMTEventAPI}
                fetchAPIParams={[NeIp]}
                columns={columns}
                ref={alarmRef}
                isShowPagination
            />
        </>
    );
};

export const Layer0Config = ({NeIp, CardId, CardName, tabType}) => {
    const [treeData, setTreeData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [cardSvgName, setCardSvgName] = useState("");

    const items =
        // eslint-disable-next-line no-nested-ternary
        tabType === "oeo"
            ? [
                  {
                      key: "oeo",
                      label: "Port Management",
                      children: (
                          <div style={{marginTop: 8}}>
                              <DynamicTable
                                  data={tableData}
                                  tabType={tabType}
                                  CardName={CardName}
                                  CardId={CardId}
                                  NeIP={NeIp}
                                  showExportButton={false}
                              />
                          </div>
                      )
                  },
                  {
                      key: "alarm",
                      label: "Alarm",
                      children: <AlarmTabTable NeIp={NeIp} />
                  }
              ]
            : tabType === "edfa"
              ? [
                    {
                        key: "edfa",
                        label: "EDFA",
                        children: (
                            <div style={{marginTop: 8}}>
                                <DynamicTable
                                    data={tableData}
                                    tabType={tabType}
                                    CardName={CardName}
                                    CardId={CardId}
                                    NeIP={NeIp}
                                    showExportButton={false}
                                />
                            </div>
                        )
                    },
                    {
                        key: "alarm",
                        label: "Alarm",
                        children: <AlarmTabTable NeIp={NeIp} />
                    }
                ]
              : [];

    useEffect(() => {
        getFMTDevicePort(CardId, tabType).then(rs => {
            if (rs.errorCode !== 200) {
                message.error("Get port info failed");
            }
            const treeChildren = rs.data.port_name.map(item => ({
                title: item,
                key: item
            }));

            setTableData(rs.data.ports_info);

            setTreeData([
                {
                    title: CardName,
                    key: CardId,
                    children: treeChildren
                }
            ]);
            setExpandedKeys([CardId]);

            const CardType = rs.data.info.type;
            const CardModel = rs.data.info.model;

            if (deviceSchemaMapping[CardModel]) {
                setCardSvgName(CardModel);
            } else {
                const matchingKey = Object.keys(deviceSchemaMapping).find(key => key.includes(CardType));
                if (matchingKey) {
                    setCardSvgName(matchingKey);
                } else {
                    setCardSvgName("-1");
                }
            }
        });
    }, [CardId]);

    return (
        <div className={styles.container}>
            <div className={styles.up}>
                <div className={styles.tree} style={{borderRadius: 5}}>
                    <Tree
                        treeData={treeData}
                        showLine
                        expandedKeys={expandedKeys}
                        onExpand={keys => setExpandedKeys(keys)}
                    />
                </div>
                <div className={styles.view} style={{borderRadius: 5}}>
                    {cardSvgName && <DynamicIcon cardSvgName={cardSvgName} />}
                </div>
            </div>
            <div className={styles.down} style={{borderRadius: 5}}>
                <Tabs style={{overflow: "hidden"}} destroyInactiveTabPane items={items} />
            </div>
        </div>
    );
};
