/* eslint-disable no-bitwise */
import React, {useRef, useState} from "react";
import {Button, Space, Card, Progress, Table, message, Tag} from "antd";
import {addSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import {
    AmpConCustomTable,
    createColumnConfigMultipleParams,
    createColumnConfig,
    TableFilterDropdown,
    TableSelectFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {
    deleteResourcePoolIpPool,
    deleteIpResourceRecord,
    generateIpResourceRecord,
    queryResourcePoolIpTableData
} from "@/modules-ampcon/apis/resource_pool_api";
import CreatePoolModal from "@/modules-ampcon/pages/Resource/Pool/IpPool/create_pool_modal";
import EditPoolModal from "@/modules-ampcon/pages/Resource/Pool/IpPool/edit_pool_modal";
import CopyPoolModal from "@/modules-ampcon/pages/Resource/Pool/IpPool/copy_pool_modal";
import styles from "./ip_pool.module.scss";
import ExpandIcon from "@/modules-ampcon/components/expand_icon";

const CustomHeaderCell = props => (
    <th
        {...props}
        style={{
            ...props.style,
            backgroundColor: "#FCFCFD"
        }}
    />
);

const CustomBodyCell = props => {
    const defaultStyle = {backgroundColor: "#FFFFFF"};
    const hoverStyle = {backgroundColor: "#FFFFFF"};

    const [style, setStyle] = React.useState(defaultStyle);

    return (
        <td
            {...props}
            style={{...props.style, ...style}}
            onMouseEnter={() => setStyle(hoverStyle)}
            onMouseLeave={() => setStyle(defaultStyle)}
        />
    );
};

const IpPool = () => {
    const createPoolModalRef = useRef(null);
    const editPoolModalRef = useRef(null);
    const copyPoolModalRef = useRef(null);
    const configSearchFieldsList = ["name"];
    const configMatchFieldsList = [{name: "name", matchMode: "fuzzy"}];
    const [filterDefaultValue, setFilterDefaultValue] = useState("");

    const tableRef = useRef();

    const progressColors = {"0%": "#14c9bb", "100%": "#14c9bb"};

    const intToIp = num => {
        return [
            Math.floor(num / 256 ** 3),
            Math.floor((num % 256 ** 3) / 256 ** 2),
            Math.floor((num % 256 ** 2) / 256),
            num % 256
        ].join(".");
    };

    const calculateSubnet = (startInt, endInt) => {
        const range = endInt - startInt + 1;

        let maskSize = 32;
        let diff = startInt ^ endInt;
        while (diff !== 0 && maskSize > 0) {
            maskSize--;
            diff >>>= 1;
        }

        const subnet = `${intToIp(startInt)}/${maskSize}`;

        return {
            subnet,
            subnetSize: range
        };
    };

    const deleteIPPool = record => {
        deleteResourcePoolIpPool({
            id: record.id
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const generateResourceRecordCallback = record => {
        generateIpResourceRecord({
            poolId: record.id,
            recordNum: 10
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const deleteResourceRecordCallback = record => {
        deleteIpResourceRecord({
            poolId: record.id,
            recordNum: 10
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const tagStyle = is_in_use => {
        if (is_in_use === true) {
            return <Tag className={styles.UsedStyle}>Used</Tag>;
        }
        if (is_in_use === false) {
            return <Tag className={styles.UnusedStyle}>Unused</Tag>;
        }
    };

    const expandColumns = [
        {
            title: "Subnet",
            width: "20%",
            render: (_, record) => {
                const {subnet} = calculateSubnet(record.start_value, record.end_value);
                return <div>{subnet}</div>;
            }
        },
        // createColumnConfigMultipleParams({
        //     title: "Used IP Range Nums",
        //     dataIndex: "used_count",
        //     filterDropdownComponent: TableFilterDropdown,
        //     width: "20%"
        // }),
        // createColumnConfigMultipleParams({
        //     title: "IP Range Nums",
        //     dataIndex: "ranges_count",
        //     filterDropdownComponent: TableFilterDropdown,
        //     render: (_, record) => {
        //         const {subnetSize} = calculateSubnet(record.start_value, record.end_value);
        //         return <div>{subnetSize}</div>;
        //     },
        //     width: "20%"
        // }),
        {
            ...createColumnConfig("Used IP Range Nums", "used_count"),
            sorter: (a, b) => a.used_count - b.used_count,
            width: "20%"
        },
        {
            ...createColumnConfig("IP Range Nums", "ranges_count"),
            render: (_, record) => {
                const {subnetSize} = calculateSubnet(record.start_value, record.end_value);
                return <div>{subnetSize}</div>;
            },
            sorter: (a, b) => a.ranges_count - b.ranges_count,
            width: "20%"
        },
        {
            title: "IP Range Usage",
            width: "20%",
            render: (_, record) => {
                return (
                    <div style={{marginLeft: "10px", marginRight: "30px"}}>
                        <Progress
                            percent={((record.used_count / record.ranges_count) * 100).toFixed(2)}
                            strokeColor={progressColors}
                            format={() => {
                                const percent = (record.used_count / record.ranges_count) * 100;
                                let displayPercent = percent.toFixed(2);
                                if (displayPercent === "0.00" && percent > 0) {
                                    displayPercent = "< 0.01";
                                } else if (displayPercent === "100.00" && percent < 100) {
                                    displayPercent = "> 99.99";
                                }

                                return `${displayPercent}%`;
                            }}
                        />
                    </div>
                );
            }
        },
        {
            title: "Status",
            width: "20%",
            render: (_, record) => <Space>{tagStyle(record.is_in_use)}</Space>
        }
    ];

    const filterOptions = [
        {
            label: "Used",
            value: "used"
        },
        {
            label: "Unused",
            value: "unused"
        }
    ];

    const configColumns = [
        createColumnConfigMultipleParams({
            title: "Pool Name",
            dataIndex: "name",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%"
        }),
        // createColumnConfigMultipleParams({
        //     title: "Used IP Nums",
        //     dataIndex: "used_count",
        //     filterDropdownComponent: TableFilterDropdown,
        //     width: "15%"
        // }),
        {...createColumnConfig("Used IP Nums", "used_count"), width: "15%"},
        {
            ...createColumnConfig("IP Nums", "ranges_count"),
            width: "15%",
            filterDropdownComponent: TableFilterDropdown,
            render: (_, record) => {
                const ipRanges = record.ranges.map(range => {
                    const {subnetSize} = calculateSubnet(range.start_value, range.end_value);
                    return subnetSize;
                });
                const totalIpCount = ipRanges.reduce((sum, count) => sum + count, 0);
                return <div>{totalIpCount}</div>;
            }
        },
        {
            title: "Usage",
            width: "20%",
            render: (_, record) => {
                return (
                    <div style={{marginLeft: "10px", marginRight: "30px"}}>
                        <Progress
                            percent={((record.used_count / record.ranges_count) * 100).toFixed(2)}
                            strokeColor={progressColors}
                            format={() => {
                                const percent = (record.used_count / record.ranges_count) * 100;
                                let displayPercent = percent.toFixed(2);
                                if (displayPercent === "0.00" && percent > 0) {
                                    displayPercent = "< 0.01";
                                } else if (displayPercent === "100.00" && percent < 100) {
                                    displayPercent = "> 99.99";
                                }
                                return `${displayPercent}%`;
                            }}
                        />
                    </div>
                );
            }
        },
        {
            title: "Status",
            dataIndex: "is_in_use",
            filterDropdown: props => TableSelectFilterDropdown({...props, filterOptions, filterDefaultValue}),
            onFilter: () => true,
            enableFilter: true,
            enableSorter: false,
            render: (_, record) => <Space>{tagStyle(record.is_in_use)}</Space>
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    editPoolModalRef.current.showEditPoolModal(record);
                                }}
                            >
                                Edit
                            </a>
                            <a
                                onClick={() => {
                                    copyPoolModalRef.current.showCopyPoolModal(record);
                                }}
                            >
                                Copy
                            </a>
                            <a
                                className={record.is_in_use ? "disabled" : ""}
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete the IP pool?", () => {
                                        deleteIPPool(record);
                                    })
                                }
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const expandedRowDataFetch = record => {
        return record.ranges;
    };

    const expandedRowRender = record => {
        return (
            <div>
                <Table
                    style={{
                        paddingLeft: 33,
                        paddingRight: 30,
                        paddingBottom: 12,
                        paddingTop: 12,
                        backgroundColor: "#f7f7f7"
                    }}
                    columns={expandColumns}
                    searchFieldsList={configSearchFieldsList}
                    matchFieldsList={configMatchFieldsList}
                    dataSource={expandedRowDataFetch(record)}
                    bordered
                    pagination={false}
                    components={{
                        header: {
                            cell: CustomHeaderCell
                        },
                        body: {
                            cell: CustomBodyCell
                        }
                    }}
                />
            </div>
        );
    };

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{marginTop: "8px", marginBottom: "20px"}}>IP Pools</h2>
            <AmpConCustomTable
                columns={configColumns}
                searchFieldsList={configSearchFieldsList}
                matchFieldsList={configMatchFieldsList}
                fetchAPIInfo={queryResourcePoolIpTableData}
                expandable={{
                    expandedRowRender,
                    expandIcon: props => <ExpandIcon {...props} />
                }}
                ref={tableRef}
                extraButton={
                    <Button
                        type="primary"
                        onClick={() => {
                            createPoolModalRef.current.showCreatePoolModal();
                        }}
                    >
                        <Icon component={addSvg} />
                        IP Pool
                    </Button>
                }
            />
            <CreatePoolModal
                ref={createPoolModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
            <EditPoolModal
                ref={editPoolModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
            <CopyPoolModal
                ref={copyPoolModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
        </Card>
    );
};

export default IpPool;
