/* eslint-disable no-bitwise */
import React, {useState, forwardRef, useImperativeHandle, useEffect} from "react";
import {Button, Modal, Form, Input, Divider, InputNumber, Row, Col, message, Flex} from "antd";
import {PlusOutlined, MinusOutlined} from "@ant-design/icons";
import {editResourcePoolIpPool} from "@/modules-ampcon/apis/resource_pool_api";

const ipToInt = ip => {
    const parts = ip.split(".").map(Number);
    return parts.reduce((acc, part, index) => acc + part * 256 ** (3 - index), 0);
};

const intToIp = num => {
    return [
        Math.floor(num / 256 ** 3),
        Math.floor((num % 256 ** 3) / 256 ** 2),
        Math.floor((num % 256 ** 2) / 256),
        num % 256
    ].join(".");
};

const subnetToRange = subnet => {
    const [baseIp, prefixLength] = subnet.split("/");
    const baseInt = ipToInt(baseIp);
    const maskSize = parseInt(prefixLength, 10);
    const totalIps = 2 ** (32 - maskSize);

    const mask = 2 ** maskSize - 1;
    const startInt = Math.floor(baseInt / 2 ** (32 - maskSize)) * 2 ** (32 - maskSize);

    const endInt = startInt + totalIps - 1;

    return {startInt, endInt};
};

const rangeToSubnet = (startInt, endInt) => {
    const range = endInt - startInt + 1;

    let maskSize = 32;
    let diff = startInt ^ endInt;
    while (diff !== 0 && maskSize > 0) {
        maskSize--;
        diff >>>= 1;
    }

    const subnet = `${intToIp(startInt)}/${maskSize}`;

    return {
        subnet,
        subnetSize: range
    };
};

const EditPoolModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showEditPoolModal: record => {
            setIsShowModal(true);
            const {ranges, name, id} = record;
            const formattedRanges = (ranges || []).map(range => {
                const {subnet} = rangeToSubnet(range.start_value, range.end_value);
                return {
                    startIP: range.start_value,
                    endIP: range.end_value,
                    subnet,
                    rangeId: range.id,
                    status: range.is_in_use,
                    error: false,
                    confict: false
                };
            });
            setFormData(formattedRanges);
            setFields(formattedRanges); // 更新本地状态
            EditPoolForm.setFieldsValue({pn: name, fields: formattedRanges}); // 设置表单值
            setOriginalRanges(formattedRanges); // 记录修改前的数据
            setPoolId(id); // 记录当前编辑的 poolId
        }
    }));

    const [formData, setFormData] = useState("");
    const [, setLoading] = useState(true);
    const [poolId, setPoolId] = useState(""); // 记录当前编辑的 poolId
    const [originalRanges, setOriginalRanges] = useState({}); // 记录修改前的数据
    const [fields, setFields] = useState([]); // 初始字段值
    const [isShowModal, setIsShowModal] = useState(false);
    const [EditPoolForm] = Form.useForm();

    const checkSubnetUniqueness = subnets => {
        const subnetSet = new Set(subnets);
        if (subnetSet.size !== subnets.length) {
            message.error("There are duplicate subnets.");
            return false;
        }
        return true;
    };

    const checkSubnetOverlap = (newRanges, originalRanges) => {
        const allRanges = [...newRanges, ...originalRanges];
        for (let i = 0; i < allRanges.length; i++) {
            for (let j = i + 1; j < allRanges.length; j++) {
                if (allRanges[i].startInt <= allRanges[j].endInt && allRanges[i].endInt >= allRanges[j].startInt) {
                    message.error("Subnet ranges overlap.");
                    return true;
                }
            }
        }
        return false;
    };

    const handleSave = async () => {
        try {
            const value = await EditPoolForm.validateFields();
            const {fields} = value;

            const rangesData = {
                delete: [],
                modify: [],
                add: []
            };

            const subnets = fields.map(range => range.subnet);
            if (!checkSubnetUniqueness(subnets)) return;
            const newRanges = fields.map(range => {
                const {startInt, endInt} = subnetToRange(range.subnet);
                return {startInt, endInt};
            });
            if (checkSubnetOverlap(newRanges, originalRanges)) return;

            const currentRangeIds = fields.map(range => range.rangeId);
            const originalRangeIds = originalRanges.map(range => range.rangeId);
            rangesData.delete = originalRanges
                .filter(range => !currentRangeIds.includes(range.rangeId))
                .map(range => ({rangeId: range.rangeId}));

            rangesData.modify = fields
                .map(range => {
                    const originalRange = originalRanges.find(item => item.rangeId === range.rangeId);
                    if (originalRange && originalRange.subnet !== range.subnet) {
                        const {startInt, endInt} = subnetToRange(range.subnet);
                        return {
                            rangeId: range.rangeId,
                            start: startInt,
                            end: endInt
                        };
                    }
                    return null;
                })
                .filter(item => item !== null);

            rangesData.add = fields
                .filter(range => !originalRangeIds.includes(range.rangeId))
                .map(range => {
                    const {startInt, endInt} = subnetToRange(range.subnet);
                    return {
                        start: startInt,
                        end: endInt
                    };
                });

            const editPoolDate = {
                poolId,
                poolName: value.pn,
                ranges: rangesData
            };

            setLoading(true);
            const res = await editResourcePoolIpPool(editPoolDate);
            if (res.status === 200) {
                message.success("Edit pool success");
                setIsShowModal(false);
                EditPoolForm.resetFields();
            } else {
                message.error(res.info);
            }
            if (props.saveCallback) {
                props.saveCallback();
            }
        } catch (error) {
            console.error("Validation failed:", error);
        } finally {
            setLoading(false);
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    Edit IP Pool
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                EditPoolForm.resetFields();
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                EditPoolForm.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form form={EditPoolForm} style={{minHeight: "267.23px"}}>
                <Form.Item
                    name="pn"
                    label="Pool Name"
                    labelCol={{style: {marginRight: "32px"}}}
                    rules={[
                        {required: true, message: "Please input Pool Name!"},
                        {
                            max: 64,
                            message: "Max length: 64 characters!"
                        },
                        {
                            validator: (_, value) => {
                                if (value === undefined || value === null || value === "") {
                                    return Promise.resolve();
                                }
                                if (value.includes(" ")) {
                                    return Promise.reject(new Error("Name cannot contain spaces!"));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    initialValue={EditPoolForm.getFieldValue("pn") !== "" ? EditPoolForm.getFieldValue("pn") : ""}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.List name="fields" onChange={setFields}>
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name, fieldNames, ...restField}, index) => {
                                const newFormData = [...formData];
                                return (
                                    <Row key={key}>
                                        <Form.Item
                                            {...restField}
                                            name={[name, "subnet"]}
                                            label="Subnet"
                                            labelCol={{style: {paddingRight: "23px", marginRight: "32px"}}}
                                            rules={[
                                                {required: true, message: "Cannot be empty!"},
                                                {
                                                    pattern:
                                                        /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\/(3[0-2]|[12]?\d)$/,
                                                    message: "Invalid subnet format!"
                                                }
                                            ]}
                                        >
                                            <Input style={{width: "280px"}} placeholder="eg: 10.56.0.0/24" />
                                        </Form.Item>
                                        <Col>
                                            {index === 0 ? (
                                                <Button
                                                    onClick={() => add()}
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "24px",
                                                        marginLeft: "8px"
                                                    }}
                                                    type="link"
                                                    icon={<PlusOutlined />}
                                                />
                                            ) : (
                                                <Button
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "24px",
                                                        marginLeft: "8px"
                                                    }}
                                                    type="link"
                                                    icon={<MinusOutlined />}
                                                    onClick={() => {
                                                        remove(name);
                                                    }}
                                                />
                                            )}
                                        </Col>
                                    </Row>
                                );
                            })}
                        </>
                    )}
                </Form.List>
            </Form>
        </Modal>
    ) : null;
});

export default EditPoolModal;
