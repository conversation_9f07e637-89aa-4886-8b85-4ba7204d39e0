import React, {useState, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>} from "react";
import {Button, Modal, Form, Input, Divider, Row, Col, message, Flex} from "antd";
import {PlusOutlined, MinusOutlined} from "@ant-design/icons";
import {createResourcePoolIpPool} from "@/modules-ampcon/apis/resource_pool_api";

const ipToInt = ip => {
    const parts = ip.split(".").map(Number);
    return parts.reduce((acc, part, index) => acc + part * 256 ** (3 - index), 0);
};

const intToIp = num => {
    return [
        Math.floor(num / 256 ** 3),
        Math.floor((num % 256 ** 3) / 256 ** 2),
        Math.floor((num % 256 ** 2) / 256),
        num % 256
    ].join(".");
};

const subnetToRange = subnet => {
    const [baseIp, prefixLength] = subnet.split("/");
    const baseInt = ipToInt(baseIp);
    const maskSize = parseInt(prefixLength, 10);
    const totalIps = 2 ** (32 - maskSize);

    const mask = 2 ** maskSize - 1;
    const startInt = Math.floor(baseInt / 2 ** (32 - maskSize)) * 2 ** (32 - maskSize);

    const endInt = startInt + totalIps - 1;

    return {startInt, endInt};
};

const validateSubnets = subnets => {
    const uniqueSubnets = new Set();
    for (let i = 0; i < subnets.length; i++) {
        const {subnet} = subnets[i];
        if (uniqueSubnets.has(subnet)) {
            return `Subnet ${subnet} is duplicated!`;
        }
        uniqueSubnets.add(subnet);
    }

    // 检查范围重叠
    for (let i = 0; i < subnets.length; i++) {
        const {subnet, startInt, endInt} = subnets[i];
        for (let j = i + 1; j < subnets.length; j++) {
            const {startInt: startInt2, endInt: endInt2} = subnets[j];
            if ((startInt <= endInt2 && startInt >= startInt2) || (endInt >= startInt2 && endInt <= endInt2)) {
                return `Subnet range ${subnet} overlaps with another subnet!`;
            }
        }
    }
    return null;
};

const CreatePoolModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showCreatePoolModal: () => {
            createPoolForm.resetFields();
            setIsShowModal(true);
        }
    }));
    const {saveCallback} = props;
    const [fields, setFields] = useState([{startIP: "*******", endIP: "***************"}]);
    const [isShowModal, setIsShowModal] = useState(false);
    const [createPoolForm] = Form.useForm();
    const [, setLoading] = useState(true);
    const handleSave = async () => {
        try {
            const values = await createPoolForm.validateFields();

            const subnets = (values.fields || []).map(field => {
                const {startInt, endInt} = subnetToRange(field.subnet);
                return {subnet: field.subnet, startInt, endInt};
            });

            const validationError = validateSubnets(subnets);
            if (validationError) {
                message.error(validationError);
                return;
            }

            const createPoolDate = {
                poolName: values.pn,
                ranges: (values.fields || []).map(field => {
                    const {startInt, endInt} = subnetToRange(field.subnet);
                    return {
                        start: startInt,
                        end: endInt
                    };
                })
            };

            setLoading(true);
            const response = await createResourcePoolIpPool(createPoolDate);

            if (response.status === 200) {
                setIsShowModal(false);
                message.success("Create pool success");
                createPoolForm.resetFields();
            } else {
                message.error(response.info);
            }
            if (saveCallback) {
                saveCallback();
            }
        } catch (error) {
            console.error("Validation failed:", error);
        } finally {
            setLoading(false);
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    Create IP Pool
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                createPoolForm.resetFields();
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                createPoolForm.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form form={createPoolForm} labelAlign="left" layout="horizontal" style={{minHeight: "267.23px"}}>
                <Form.Item
                    name="pn"
                    label="Pool Name"
                    labelCol={{style: {marginRight: "32px"}}}
                    rules={[
                        {required: true, message: "Please input Pool Name!"},
                        {
                            max: 64,
                            message: "Max length: 64 characters!"
                        },
                        {
                            validator: (_, value) => {
                                if (value === undefined || value === null || value === "") {
                                    return Promise.resolve();
                                }
                                if (value.includes(" ")) {
                                    return Promise.reject(new Error("Name cannot contain spaces!"));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    initialValue={createPoolForm.getFieldValue("pn") !== "" ? createPoolForm.getFieldValue("pn") : ""}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.List name="fields" initialValue={fields} onChange={setFields}>
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name, fieldNames, ...restField}, index) => (
                                <Row key={key}>
                                    <Form.Item
                                        {...restField}
                                        name={[name, "subnet"]}
                                        label="Subnet"
                                        labelCol={{style: {paddingRight: "23px", marginRight: "32px"}}}
                                        rules={[
                                            {required: true, message: "Please input a subnet!"},
                                            {
                                                pattern:
                                                    /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\/(3[0-2]|[12]?\d)$/,
                                                message: "Invalid subnet format!"
                                            }
                                        ]}
                                    >
                                        <Input style={{width: 280}} placeholder="Example: 10.56.0.0/24" />
                                    </Form.Item>
                                    <Col>
                                        {index === 0 ? (
                                            <Button
                                                onClick={() => add()}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "24px",
                                                    marginLeft: "8px"
                                                }}
                                                type="link"
                                                icon={<PlusOutlined />}
                                            />
                                        ) : (
                                            <Button
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "24px",
                                                    marginLeft: "8px"
                                                }}
                                                type="link"
                                                icon={<MinusOutlined />}
                                                onClick={() => {
                                                    remove(name);
                                                }}
                                            />
                                        )}
                                    </Col>
                                </Row>
                            ))}
                        </>
                    )}
                </Form.List>
            </Form>
        </Modal>
    ) : null;
});

export default CreatePoolModal;
