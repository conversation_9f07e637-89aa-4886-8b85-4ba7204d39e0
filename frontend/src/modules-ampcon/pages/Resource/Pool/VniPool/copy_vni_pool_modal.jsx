import React, {useState, forwardRef, useImperativeHandle, useEffect} from "react";
import {Button, Modal, Form, Input, Divider, InputNumber, Row, Col, message, Flex} from "antd";
import {copyResourcePoolVniPool} from "@/modules-ampcon/apis/resource_pool_api";

const CopyPoolModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showCopyPoolModal: record => {
            setIsShowModal(true);
            setId(record.id);
            CopyPoolForm.setFieldsValue({on: record.name}); // 设置表单值
        }
    }));
    const {saveCallback} = props;
    const [id, setId] = useState(null);
    const [isShowModal, setIsShowModal] = useState(false);
    const [CopyPoolForm] = Form.useForm();
    const [, setLoading] = useState(true);
    const handleSave = async () => {
        const values = CopyPoolForm.getFieldsValue();
        const copyPoolData = {
            poolId: id,
            poolName: values.nn
        };
        console.log(copyPoolData);
        try {
            setLoading(true);
            const response = await copyResourcePoolVniPool(copyPoolData);
            if (response.status === 200) {
                message.success("Copy Pool Success");
            } else {
                message.error("Copy Pool Failed");
            }
        } catch (error) {
            console.error("Copy Pool Failed", error);
        } finally {
            setLoading(false);
            CopyPoolForm.resetFields();
        }
        setIsShowModal(false);
        if (saveCallback) {
            saveCallback();
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    Copy VNI Pool
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                CopyPoolForm.resetFields();
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                CopyPoolForm.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form form={CopyPoolForm} style={{minHeight: "267.23px"}} labelAlign="left" layout="horizontal">
                <Form.Item name="on" label="Origin Name">
                    <Input style={{width: "280px"}} disabled />
                </Form.Item>
                <Form.Item
                    name="nn"
                    label="New Name"
                    rules={[
                        {required: true, message: "Please input Pool Name!"},
                        {
                            max: 64,
                            message: "Max length: 64 characters!" // 超过64字符时的提示信息
                        }
                    ]}
                    initialValue={CopyPoolForm.getFieldValue("pn") !== "" ? CopyPoolForm.getFieldValue("pn") : ""}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default CopyPoolModal;
