/* eslint-disable react/no-unstable-nested-components */
import React, {useEffect, useRef, useState} from "react";
import {Button, Space, Card, Progress, Table, message, Tag} from "antd";
import {
    AmpConCustomTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown,
    createColumnConfig,
    TableSelectFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {addSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {
    deleteResourcePoolVniPool,
    deleteVniResourceRecord,
    generateResourceVniRecord,
    queryResourcePoolVniTableData
} from "@/modules-ampcon/apis/resource_pool_api";
import CreatePoolModal from "@/modules-ampcon/pages/Resource/Pool/VniPool/create_vni_pool_modal";
import EditPoolModal from "@/modules-ampcon/pages/Resource/Pool/VniPool/edit_vni_pool_modal";
import CopyPoolModal from "@/modules-ampcon/pages/Resource/Pool/VniPool/copy_vni_pool_modal";
import styles from "./vni_pool.module.scss";
import ExpandIcon from "@/modules-ampcon/components/expand_icon";

const VniPool = () => {
    const createPoolModalRef = useRef(null);
    const editPoolModalRef = useRef(null);
    const copyPoolModalRef = useRef(null);
    const configSearchFieldsList = ["name", "use"];
    const configMatchFieldsList = [{name: "name", matchMode: "fuzzy"}];
    const [filterDefaultValue, setFilterDefaultValue] = useState("");
    const [currentRecord, setCurrentRecord] = useState({});

    const tableRef = useRef();

    const progressColors = {"0%": "#14c9bb", "100%": "#14c9bb"};

    const deleteVNIPool = record => {
        deleteResourcePoolVniPool({
            id: record.id
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const generateResourceRecordCallback = record => {
        generateResourceVniRecord({
            poolId: record.id,
            recordNum: 10
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const deleteResourceRecordCallback = record => {
        deleteVniResourceRecord({
            poolId: record.id,
            recordNum: 10
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const tagStyle = is_in_use => {
        if (is_in_use === true) {
            return <Tag className={styles.UsedStyle}>Used</Tag>;
        }
        if (is_in_use === false) {
            return <Tag className={styles.UnusedStyle}>Unused</Tag>;
        }
    };

    const filterOptions = [
        {
            label: "Used",
            value: "used"
        },
        {
            label: "Unused",
            value: "unused"
        }
    ];

    const expandColumns = [
        {
            title: "VNI Range",
            width: "15%",
            render: (_, record) => {
                return (
                    <div>
                        {record.start_value} - {record.end_value}
                    </div>
                );
            }
        },
        {
            ...createColumnConfig("Used VNI Range Count", "used_count"),
            sorter: (a, b) => a.used_count - b.used_count,
            width: "16%"
        },
        {
            ...createColumnConfig("VNI Range Count", "ranges_count"),
            sorter: (a, b) => a.ranges_count - b.ranges_count,
            width: "15%"
        },
        {
            title: "VNI Range Usage",
            width: "21%",
            render: (_, record) => {
                return (
                    <div style={{marginLeft: "10px", marginRight: "30px"}}>
                        <Progress
                            percent={((record.used_count / record.ranges_count) * 100).toFixed(2)}
                            strokeColor={progressColors}
                            format={() => {
                                const percent = (record.used_count / record.ranges_count) * 100;
                                let displayPercent = percent.toFixed(2);
                                if (displayPercent === "0.00" && percent > 0) {
                                    displayPercent = "< 0.01";
                                } else if (displayPercent === "100.00" && percent < 100) {
                                    displayPercent = "> 99.99";
                                }

                                return `${displayPercent}%`;
                            }}
                        />
                    </div>
                );
            }
        },
        {
            title: "Status",
            width: "15%",
            render: (_, record) => <Space>{tagStyle(record.is_in_use)}</Space>
        }
    ];

    const useFilterOptions = [
        {
            label: "default",
            value: "default"
        }
        // {
        //     label: "reserve",
        //     value: "reserve"
        // }
    ];

    const configColumns = [
        createColumnConfigMultipleParams({
            title: "Pool Name",
            dataIndex: "name",
            filterDropdownComponent: TableFilterDropdown,
            enableFilter: true,
            enableSorter: true,
            width: "15%"
        }),
        createColumnConfigMultipleParams({
            title: "Use",
            dataIndex: "use",
            width: "auto",
            filterDropdown: props =>
                TableSelectFilterDropdown({
                    ...props,
                    filterOptions: useFilterOptions,
                    filterDefaultValue
                })
        }),
        createColumnConfigMultipleParams({
            title: "Fabric",
            dataIndex: "fabric",
            filterDropdownComponent: TableFilterDropdown,
            width: "auto",
            enableFilter: false,
            enableSorter: false,
            render: (_, record) => {
                return record.fabric && Array.isArray(record.fabric) ? record.fabric.map(f => f.name).join(", ") : "";
            }
        }),
        createColumnConfigMultipleParams({
            title: "Used VNI Count",
            dataIndex: "used_count",
            filterDropdownComponent: TableFilterDropdown,
            enableFilter: false,
            enableSorter: true,
            width: "15%"
        }),
        createColumnConfigMultipleParams({
            title: "VNI Count",
            dataIndex: "ranges_count",
            filterDropdownComponent: TableFilterDropdown,
            enableFilter: false,
            enableSorter: true,
            width: "15%"
        }),
        {
            title: "Usage",
            width: "20%",
            render: (_, record) => {
                return (
                    <div style={{marginLeft: "10px", marginRight: "30px"}}>
                        <Progress
                            percent={((record.used_count / record.ranges_count) * 100).toFixed(2)}
                            strokeColor={progressColors}
                            format={() => {
                                const percent = (record.used_count / record.ranges_count) * 100;
                                let displayPercent = percent.toFixed(2);
                                if (displayPercent === "0.00" && percent > 0) {
                                    displayPercent = "< 0.01";
                                } else if (displayPercent === "100.00" && percent < 100) {
                                    displayPercent = "> 99.99";
                                }

                                return `${displayPercent}%`;
                            }}
                        />
                    </div>
                );
            }
        },
        {
            title: "Status",
            dataIndex: "is_in_use",
            filterDropdown: props => TableSelectFilterDropdown({...props, filterOptions, filterDefaultValue}),
            onFilter: () => true,
            enableFilter: true,
            enableSorter: false,
            render: (_, record) => <Space>{tagStyle(record.is_in_use)}</Space>
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    setCurrentRecord(record);
                                    editPoolModalRef.current.showEditPoolModal(record);
                                }}
                            >
                                Edit
                            </a>
                            {/* <a
                                onClick={() => {
                                    copyPoolModalRef.current.showCopyPoolModal(record);
                                }}
                            >
                                Copy
                            </a> */}
                            <a
                                className={record.is_in_use ? "disabled" : ""}
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete the VNI pool?", () => {
                                        deleteVNIPool(record);
                                    })
                                }
                            >
                                Delete
                            </a>
                            {/* <a
                                onClick={() => {
                                    generateResourceRecordCallback(record);
                                }}
                            >
                                Generate Ten Record
                            </a>
                            <a
                                onClick={() => {
                                    deleteResourceRecordCallback(record);
                                }}
                            >
                                Delete Ten Record
                            </a> */}
                        </Space>
                    </div>
                );
            }
        }
    ];

    const expandedRowDataFetch = record => {
        return record.ranges;
    };

    const expandedRowRender = record => {
        return (
            <div>
                <Table
                    style={{
                        paddingLeft: 33,
                        paddingRight: 30,
                        paddingBottom: 12,
                        paddingTop: 12,
                        backgroundColor: "#f7f7f7"
                    }}
                    columns={expandColumns}
                    searchFieldsList={configSearchFieldsList}
                    matchFieldsList={configMatchFieldsList}
                    dataSource={expandedRowDataFetch(record)}
                    bordered
                    pagination={false}
                    components={{
                        header: {
                            cell: props => (
                                <th
                                    {...props}
                                    style={{
                                        ...props.style,
                                        backgroundColor: "#FAFAFA"
                                    }}
                                />
                            )
                        },
                        body: {
                            cell: props => (
                                <td
                                    {...props}
                                    style={{
                                        ...props.style,
                                        backgroundColor: "#FFFFFF"
                                    }}
                                />
                            )
                        }
                    }}
                />
            </div>
        );
    };

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{marginTop: "8px", marginBottom: "20px"}}>VNI Pools</h2>
            <AmpConCustomTable
                columns={configColumns}
                searchFieldsList={configSearchFieldsList}
                matchFieldsList={configMatchFieldsList}
                fetchAPIInfo={queryResourcePoolVniTableData}
                expandable={{
                    expandedRowRender,
                    expandIcon: props => <ExpandIcon {...props} />
                }}
                ref={tableRef}
                extraButton={
                    <Button
                        type="primary"
                        style={{height: 32}}
                        onClick={() => {
                            createPoolModalRef.current.showCreatePoolModal();
                        }}
                    >
                        <Icon component={addSvg} />
                        VNI Pool
                    </Button>
                }
            />
            <CreatePoolModal
                ref={createPoolModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
            <EditPoolModal
                ref={editPoolModalRef}
                currentRecord={currentRecord}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
            <CopyPoolModal
                ref={copyPoolModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
        </Card>
    );
};

export default VniPool;
