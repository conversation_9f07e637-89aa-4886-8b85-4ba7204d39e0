import React, {useState, forwardRef, useImperative<PERSON>andle, useEffect} from "react";
import {Button, Modal, Form, Input, Divider, InputNumber, Row, Col, message, Flex} from "antd";
import {copyResourcePoolAsnPool} from "@/modules-ampcon/apis/resource_pool_api";

const CopyPoolModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showCopyPoolModal: record => {
            setIsShowModal(true);
            setId(record.id);
            CopyPoolForm.setFieldsValue({on: record.name}); // 设置表单值
        }
    }));
    const {saveCallback} = props;
    const [id, setId] = useState(null);
    const [isShowModal, setIsShowModal] = useState(false);
    const [CopyPoolForm] = Form.useForm();
    const [, setLoading] = useState(true);
    const handleSave = async () => {
        try {
            const values = await CopyPoolForm.validateFields();
            const copyPoolData = {
                poolId: id,
                poolName: values.nn
            };
            setLoading(true);
            const response = await copyResourcePoolAsnPool(copyPoolData);
            if (response.status === 200) {
                setIsShowModal(false);
                message.success("Copy Pool Success");
            } else {
                CopyPoolForm.setFieldsValue({nn: ""});
                message.error(response.info);
            }
            if (saveCallback) {
                saveCallback();
            }
        } catch (error) {
            if (error instanceof Error) {
                message.error(`Validation failed: ${error.message}`);
            }
            CopyPoolForm.setFieldsValue({nn: ""});
        } finally {
            setLoading(false);
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    Copy ASN Pool
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                CopyPoolForm.resetFields();
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                CopyPoolForm.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form form={CopyPoolForm} style={{minHeight: "267.23px"}} labelAlign="left" layout="horizontal">
                <Form.Item name="on" label="Origin Name" labelCol={{style: {paddingRight: "32px"}}}>
                    <Input style={{width: "280px"}} disabled />
                </Form.Item>
                <Form.Item
                    name="nn"
                    label="New Name"
                    labelCol={{style: {paddingRight: "32px"}}}
                    rules={[
                        {required: true, message: "Please input Pool Name!"},
                        {
                            max: 64,
                            message: "Max length: 64 characters!" // 超过64字符时的提示信息
                        },
                        {
                            validator: (_, value) => {
                                if (value === undefined || value === null || value === "") {
                                    return Promise.resolve();
                                }
                                if (value.includes(" ")) {
                                    return Promise.reject(new Error("Name cannot contain spaces!"));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    initialValue={CopyPoolForm.getFieldValue("pn") !== "" ? CopyPoolForm.getFieldValue("pn") : ""}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default CopyPoolModal;
