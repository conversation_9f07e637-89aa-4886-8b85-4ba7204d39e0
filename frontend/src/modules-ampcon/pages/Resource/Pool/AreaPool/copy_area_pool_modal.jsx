import React, {useState, forwardRef, useImperative<PERSON>andle, useEffect} from "react";
import {Button, Modal, Form, Input, Divider, InputNumber, Row, Col, message, Flex} from "antd";
import {copyResourcePoolAreaPool} from "@/modules-ampcon/apis/resource_pool_api";

const CopyAreaPoolModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showCopyAreaPoolModal: record => {
            setIsShowModal(true);
            setId(record.id);
            CopyPoolForm.setFieldsValue({on: record.name}); // 设置表单值
        }
    }));
    const {saveCallback} = props;
    const [id, setId] = useState(null);
    const [isShowModal, setIsShowModal] = useState(false);
    const [CopyPoolForm] = Form.useForm();
    const [, setLoading] = useState(true);

    const handleSave = () => {
        CopyPoolForm.submit();
    };

    const handleOnFinish = async () => {
        const values = CopyPoolForm.getFieldsValue();
        const copyPoolData = {
            poolId: id,
            poolName: values.nn
        };
        try {
            setLoading(true);
            const response = await copyResourcePoolAreaPool(copyPoolData);
            if (response.status === 200) {
                message.success("Copy pool success");
            } else {
                message.error("Copy pool failed");
            }
        } catch (error) {
            console.error("Copy pool failed", error);
        } finally {
            setLoading(false);
            CopyPoolForm.resetFields();
        }
        setIsShowModal(false);
        if (saveCallback) {
            saveCallback();
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    Copy Area Pool
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                CopyPoolForm.resetFields();
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                CopyPoolForm.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Save
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form
                onFinish={handleOnFinish}
                form={CopyPoolForm}
                style={{minHeight: "267.23px"}}
                labelAlign="left"
                layout="horizontal"
            >
                <Form.Item name="on" label="Origin Name">
                    <Input style={{width: "280px"}} disabled />
                </Form.Item>
                <Form.Item
                    name="nn"
                    label="New Name"
                    rules={[
                        {required: true, message: "Please input Pool Name!"},
                        {
                            max: 64,
                            message: "Max length: 64 characters!" // 超过64字符时的提示信息
                        }
                    ]}
                    initialValue={CopyPoolForm.getFieldValue("pn") !== "" ? CopyPoolForm.getFieldValue("pn") : ""}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default CopyAreaPoolModal;
