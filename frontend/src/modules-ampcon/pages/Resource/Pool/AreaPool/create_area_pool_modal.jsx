import React, {useState, forwardRef, useImperative<PERSON>andle} from "react";
import {Button, Modal, Form, Input, Divider, Row, Col, message, Flex} from "antd";
import {PlusOutlined, MinusOutlined} from "@ant-design/icons";
import {createResourcePoolAreaPool} from "@/modules-ampcon/apis/resource_pool_api";
import {FormItemRangeInput} from "@/modules-ampcon/components/form_item_range_input";
import {isRangeListConflict, rangeInputValidator} from "@/utils/pool_utils";

const CreateAreaPoolModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showCreateAreaPoolModal: () => {
            setIsShowModal(true);
        }
    }));
    const [fields, setFields] = useState([{startASN: null, endASN: null}]); // 初始字段值
    const [isShowModal, setIsShowModal] = useState(false);
    const [createAreaPoolForm] = Form.useForm();
    const [, setLoading] = useState(true);

    const handleSave = async () => {
        createAreaPoolForm.submit();
        try {
            const inputs = document.querySelectorAll(".range-list input");
            inputs.forEach(input => {
                const event = new Event("focusout", {bubbles: true});
                input.dispatchEvent(event);
            });
            const values = await createAreaPoolForm.validateFields();
            if (isRangeListConflict(values.fields)) {
                return message.error("Validation failed: Range is invalid.");
            }
        } catch (errorInfo) {
            // console.error("Validation failed:", errorInfo);
            return;
        }
        try {
            const values = createAreaPoolForm.getFieldsValue();
            const createPoolDate = {
                poolName: values.pn,
                ranges: (values.fields || []).map(field => ({
                    start: field.startASN,
                    end: field.endASN
                }))
            };
            setLoading(true);
            const response = await createResourcePoolAreaPool(createPoolDate);
            if (response.status === 200) {
                setIsShowModal(false);
                message.success(response.info);
                createAreaPoolForm.resetFields();
            } else {
                message.error(response.info);
            }
        } catch (error) {
            // console.error("Create Pool Failed", error);
        } finally {
            setLoading(false);
        }
        if (props.saveCallback) {
            props.saveCallback();
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    Create Area Pool
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                createAreaPoolForm.resetFields();
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                createAreaPoolForm.resetFields();
                                setFields([{startASN: "", endASN: ""}]);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Save
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form form={createAreaPoolForm} style={{minHeight: "267.23px"}} labelAlign="left" layout="horizontal">
                <Form.Item
                    name="pn"
                    label="Pool Name"
                    rules={[
                        {required: true, message: "Please input Pool Name!"},
                        {
                            max: 64,
                            message: "Max length: 64 characters!"
                        }
                    ]}
                    initialValue=""
                    labelCol={{style: {paddingRight: "32px"}}}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.List
                    name="fields"
                    initialValue={fields} // 初始数据
                    onChange={setFields} // 设置 fields 数据
                >
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name, fieldNames, ...restField}, index) => {
                                return (
                                    <Row key={key}>
                                        <Col>
                                            <FormItemRangeInput
                                                startOnBlurValidator={() => {
                                                    return rangeInputValidator(createAreaPoolForm, index, true);
                                                }}
                                                endOnBlurValidator={() => {
                                                    return rangeInputValidator(createAreaPoolForm, index, false);
                                                }}
                                                startDefaultValue=""
                                                endDefaultValue=""
                                                index={index}
                                            />
                                        </Col>
                                        <Col>
                                            {index === 0 ? (
                                                <Button
                                                    onClick={() => add()}
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "24px"
                                                    }}
                                                    type="link"
                                                    icon={<PlusOutlined />}
                                                />
                                            ) : (
                                                // 其他 Range 后面是 - 号
                                                <Button
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "24px"
                                                    }}
                                                    type="link"
                                                    icon={<MinusOutlined />}
                                                    onClick={() => {
                                                        // const rows = document.querySelectorAll(".ant-form > .ant-row");
                                                        remove(index);
                                                    }}
                                                />
                                            )}
                                        </Col>
                                    </Row>
                                );
                            })}
                        </>
                    )}
                </Form.List>
            </Form>
        </Modal>
    ) : null;
});

export default CreateAreaPoolModal;
