import React, {useRef, useState, useEffect} from "react";
import {Button, Space, Card, Progress, Table, message, Tag} from "antd";
import {
    AmpConCustomTable,
    createColumnConfigMultipleParams,
    createColumnConfig,
    TableFilterDropdown,
    TableSelectFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {addSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {
    deleteResourcePoolAreaPool,
    deleteResourcePoolAreaRecord,
    generateAreaResourceRecord,
    queryResourcePoolAreaTableData
} from "@/modules-ampcon/apis/resource_pool_api";
import CreateAreaPoolModal from "./create_area_pool_modal";
import EditAreaPoolModal from "./edit_area_pool_modal";
import CopyAreaPoolModal from "./copy_area_pool_modal";
import styles from "../IpPool/ip_pool.module.scss";
import ExpandIcon from "@/modules-ampcon/components/expand_icon";

const AreaPool = () => {
    const createAreaPoolModalRef = useRef(null);
    const editAreaPoolModalRef = useRef(null);
    const copyAreaPoolModalRef = useRef(null);
    const configSearchFieldsList = ["name"];
    const configMatchFieldsList = [{name: "name", matchMode: "fuzzy"}];
    const tableRef = useRef();
    const [filterDefaultValue, setFilterDefaultValue] = useState("");
    const progressColors = {"0%": "#14c9bb", "100%": "#14c9bb"};

    const deleteAreaPool = record => {
        deleteResourcePoolAreaPool({
            id: record.id
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const generateResourceRecordCallback = record => {
        generateAreaResourceRecord({
            poolId: record.id,
            recordNum: 10
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const deleteResourceRecordCallback = record => {
        console.log(record.used_count);
        if (record.used_count < 10) {
            message.error("The used count is less than 10, can't delete");
            return;
        }
        deleteResourcePoolAreaRecord({
            poolId: record.id,
            recordNum: 10
        }).then(response => {
            if (response.status === 200) {
                tableRef.current.refreshTable();
                message.success(response.info);
            } else {
                message.error(response.info);
            }
        });
    };

    const expandColumns = [
        {
            title: "Area Range",
            width: "20%",
            render: (_, record) => {
                return (
                    <div>
                        {record.start_value} - {record.end_value}
                    </div>
                );
            }
        },
        // createColumnConfigMultipleParams({
        //     title: "Used Area Range Nums",
        //     dataIndex: "used_count",
        //     filterDropdownComponent: TableFilterDropdown,
        //     width: "20%"
        // }),
        // createColumnConfigMultipleParams({
        //     title: "Area Range Nums",
        //     dataIndex: "ranges_count",
        //     filterDropdownComponent: TableFilterDropdown,
        //     width: "20%"
        // }),
        {
            ...createColumnConfig("Used Area Range Nums", "used_count"),
            sorter: (a, b) => a.used_count - b.used_count,
            width: "20%"
        },
        {
            ...createColumnConfig("Area Range Nums", "ranges_count"),
            sorter: (a, b) => a.ranges_count - b.ranges_count,
            width: "20%"
        },
        {
            title: "Area Range Usage",
            width: "20%",
            render: (_, record) => {
                return (
                    <div style={{marginLeft: "10px", marginRight: "30px"}}>
                        <Progress
                            percent={((record.used_count / record.ranges_count) * 100).toFixed(2)}
                            strokeColor={progressColors}
                            format={() => {
                                const percent = (record.used_count / record.ranges_count) * 100;
                                let displayPercent = percent.toFixed(2);
                                if (displayPercent === "0.00" && percent > 0) {
                                    displayPercent = "< 0.01";
                                } else if (displayPercent === "100.00" && percent < 100) {
                                    displayPercent = "> 99.99";
                                }

                                return `${displayPercent}%`;
                            }}
                        />
                    </div>
                );
            }
        },
        {
            title: "Status",
            width: "20%",
            render: (_, record) => {
                if (record.is_in_use) {
                    return <Tag className={styles.UsedStyle}>Used</Tag>;
                }
                return <Tag className={styles.UnusedStyle}>Unused</Tag>;
            }
        }
    ];

    const filterOptions = [
        {
            label: "Used",
            value: "used"
        },
        {
            label: "Unused",
            value: "unused"
        }
    ];

    const configColumns = [
        createColumnConfigMultipleParams({
            title: "Pool Name",
            dataIndex: "name",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%"
        }),
        // createColumnConfigMultipleParams({
        //     title: "Used Area Nums",
        //     dataIndex: "used_count",
        //     filterDropdownComponent: TableFilterDropdown,
        //     width: "15%"
        // }),
        // createColumnConfigMultipleParams({
        //     title: "Area Nums",
        //     dataIndex: "ranges_count",
        //     filterDropdownComponent: TableFilterDropdown,
        //     width: "15%"
        // }),
        {...createColumnConfig("Used Area Nums", "used_count"), width: "15%"},
        {...createColumnConfig("Area Nums", "ranges_count"), width: "15%"},
        {
            title: "Usage",
            width: "20%",
            render: (_, record) => {
                return (
                    <div style={{marginLeft: "10px", marginRight: "30px"}}>
                        <Progress
                            percent={((record.used_count / record.ranges_count) * 100).toFixed(2)}
                            strokeColor={progressColors}
                            format={() => {
                                const percent = (record.used_count / record.ranges_count) * 100;
                                let displayPercent = percent.toFixed(2);
                                if (displayPercent === "0.00" && percent > 0) {
                                    displayPercent = "< 0.01";
                                } else if (displayPercent === "100.00" && percent < 100) {
                                    displayPercent = "> 99.99";
                                }

                                return `${displayPercent}%`;
                            }}
                        />
                    </div>
                );
            }
        },
        {
            title: "Status",
            dataIndex: "is_in_use",
            filterDropdown: props => TableSelectFilterDropdown({...props, filterOptions, filterDefaultValue}),
            onFilter: () => true,
            enableFilter: true,
            enableSorter: false,
            render: (_, record) => {
                if (record.is_in_use) {
                    return <Tag className={styles.UsedStyle}>Used</Tag>;
                }
                return <Tag className={styles.UnusedStyle}>Unused</Tag>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    editAreaPoolModalRef.current.showEditPoolModal(record);
                                }}
                            >
                                Edit
                            </a>
                            <a
                                onClick={() => {
                                    copyAreaPoolModalRef.current.showCopyAreaPoolModal(record);
                                }}
                            >
                                Copy
                            </a>
                            <a
                                className={record.is_in_use ? "disabled" : ""}
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete the Area pool?", () => {
                                        deleteAreaPool(record);
                                    })
                                }
                            >
                                Delete
                            </a>
                            <a
                                onClick={() => {
                                    generateResourceRecordCallback(record);
                                }}
                            >
                                Generate Ten Record
                            </a>
                            <a
                                onClick={() => {
                                    deleteResourceRecordCallback(record);
                                }}
                            >
                                Delete Ten Record
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const expandedRowDataFetch = record => {
        return record.ranges;
    };

    const expandedRowRender = record => {
        return (
            <div>
                <Table
                    style={{
                        paddingLeft: 33,
                        paddingRight: 30,
                        paddingBottom: 12,
                        paddingTop: 12,
                        backgroundColor: "#FFFFFF"
                    }}
                    columns={expandColumns}
                    searchFieldsList={configSearchFieldsList}
                    matchFieldsList={configMatchFieldsList}
                    dataSource={expandedRowDataFetch(record)}
                    bordered
                    pagination={false}
                    components={{
                        header: {
                            cell: props => (
                                <th
                                    {...props}
                                    style={{
                                        ...props.style,
                                        backgroundColor: "#FAFAFA" // 设置表头颜色
                                    }}
                                />
                            )
                        },
                        body: {
                            cell: props => (
                                <td
                                    {...props}
                                    style={{
                                        ...props.style,
                                        backgroundColor: "#FFFFFF" // 设置表格单元格背景色
                                    }}
                                />
                            )
                        }
                    }}
                />
            </div>
        );
    };

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{marginTop: "8px", marginBottom: "20px"}}>Area Pool</h2>
            <AmpConCustomTable
                columns={configColumns}
                searchFieldsList={configSearchFieldsList}
                matchFieldsList={configMatchFieldsList}
                fetchAPIInfo={queryResourcePoolAreaTableData}
                expandable={{
                    expandedRowRender,
                    expandIcon: props => <ExpandIcon {...props} />
                }}
                ref={tableRef}
                extraButton={
                    <Button
                        type="primary"
                        onClick={() => {
                            createAreaPoolModalRef.current.showCreateAreaPoolModal();
                        }}
                    >
                        <Icon component={addSvg} />
                        Area Pool
                    </Button>
                }
            />
            <CreateAreaPoolModal
                ref={createAreaPoolModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
            <EditAreaPoolModal
                ref={editAreaPoolModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
            <CopyAreaPoolModal
                ref={copyAreaPoolModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
            />
        </Card>
    );
};

export default AreaPool;
