import {forwardRef, useImperativeHandle, useRef, useState} from "react";
import {But<PERSON>, Divider, Flex, Form, Input, message, Modal, Row, Col, Select, AutoComplete} from "antd";
import {createClient} from "@/modules-ampcon/apis/monitor_api";
import styles from "@/modules-ampcon/pages/Service/Switch/SystemConfig/system_config.module.scss";
import UpdateClientTableModal from "@/modules-ampcon/pages/Monitor/WiredClients/select_switch_table_modal";

const CreateClientModal = forwardRef((props, ref) => {
    const {saveCallback, terminalTypes, setTerminalTypes} = props;

    const title = "Create Wired Client";
    const clientNameLabel = "Client Name";
    const macAddressLabel = "MAC Address";
    const ipAddressLabel = "IP Address";
    const terminalTypeLabel = "Terminal Type";
    const switchLabel = "Switch";

    const selectSwitchTableModalRef = useRef();
    const [createClientForm] = Form.useForm();
    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedSwitchSN, setSelectedSwitchSN] = useState();

    const [filteredTerminalOptions, setFilteredTerminalOptions] = useState([]);
    const [terminalInputValue, setTerminalInputValue] = useState("");

    useImperativeHandle(ref, () => ({
        showCreateClientModal: () => {
            createClientForm.resetFields();
            setIsShowModal(true);
            setFilteredTerminalOptions(terminalTypes.map(opt => ({value: opt})));
        },
        hideCreateClientModal: () => {
            setIsShowModal(false);
            createClientForm.resetFields();
        }
    }));

    const createClientSaveCallback = async () => {
        await createClientForm.validateFields();
        const formValues = createClientForm.getFieldsValue();

        if (formValues.terminalType) {
            const matchedTerminal = terminalTypes.find(
                type => type.toLowerCase() === formValues.terminalType.trim().toLowerCase()
            );
            if (matchedTerminal) {
                formValues.terminalType = matchedTerminal;
            }
        }

        createClient(formValues).then(response => {
            if (response.status === 200) {
                const newTerminalType = formValues.terminalType;
                if (
                    newTerminalType &&
                    !terminalTypes.some(type => type.toLowerCase() === newTerminalType.toLowerCase()) &&
                    typeof setTerminalTypes === "function"
                ) {
                    setTerminalTypes(prev => [...prev, newTerminalType]);
                }

                setIsShowModal(false);
                message.success(response.info);
                saveCallback();
            } else {
                message.error(response.info);
            }
        });
    };

    const generateRegex = input => {
        const letters = input.split("");
        return new RegExp(`.*${letters.join(".*")}.*`, "i");
    };

    const handleTerminalTypeSearch = value => {
        setTerminalInputValue(value);

        if (value.length < 3) {
            setFilteredTerminalOptions(terminalTypes.map(opt => ({value: opt})));
            return;
        }

        const regex = generateRegex(value);
        const filteredOptions = terminalTypes.filter(type => regex.test(type));
        setFilteredTerminalOptions(filteredOptions.map(opt => ({value: opt})));
    };

    return isShowModal ? (
        <>
            <UpdateClientTableModal
                ref={selectSwitchTableModalRef}
                saveCallback={sn => {
                    setSelectedSwitchSN(sn[0]);
                    createClientForm.setFieldsValue({switchSN: sn[0]});
                }}
            />
            <Modal
                className="ampcon-middle-modal"
                title={
                    <div>
                        {title}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={isShowModal}
                onOk={() => {}}
                onCancel={() => {
                    createClientForm.resetFields();
                    setIsShowModal(false);
                    setSelectedSwitchSN(null);
                }}
                footer={
                    <Flex vertical>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Flex justify="flex-end">
                            <Button
                                onClick={() => {
                                    setIsShowModal(false);
                                    setSelectedSwitchSN(null);
                                    createClientForm.resetFields();
                                }}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="primary"
                                onClick={() => {
                                    createClientSaveCallback();
                                    saveCallback();
                                    setTimeout(() => {
                                        setSelectedSwitchSN(null);
                                    }, 2000);
                                }}
                            >
                                Apply
                            </Button>
                        </Flex>
                    </Flex>
                }
            >
                <Form
                    layout="horizontal"
                    labelAlign="left"
                    labelCol={{flex: "120px"}}
                    wrapperCol={{flex: "280px"}}
                    labelWrap
                    className="label-wrap"
                    form={createClientForm}
                >
                    <Form.Item
                        name="clientName"
                        label={clientNameLabel}
                        rules={[
                            {required: true, message: "Please input client name!"},
                            {
                                max: 32,
                                message: "Client name must be less than 32 characters!"
                            },
                            {
                                pattern: /^(?!\s*$)[^\u4e00-\u9fa5]*$/,
                                message: "Invalid client name!"
                            }
                        ]}
                        validateTrigger="onBlur"
                    >
                        <Input />
                    </Form.Item>

                    <Form.Item
                        name="macAddress"
                        label={macAddressLabel}
                        rules={[
                            {
                                required: true,
                                message: "Please input MAC Address!"
                            },
                            {
                                validator: (_, value) => {
                                    if (value && !/^([\dA-Fa-f]{2}[:-]?){5}[\dA-Fa-f]{2}$/.test(value)) {
                                        return Promise.reject(new Error("Please enter a valid MAC address!"));
                                    }
                                    return Promise.resolve();
                                }
                            }
                        ]}
                        validateTrigger="onBlur"
                    >
                        <Input placeholder="Example: 00:00:00:00:00:00" />
                    </Form.Item>

                    <Form.Item
                        name="ipAddress"
                        label={ipAddressLabel}
                        rules={[
                            {
                                pattern: /^(?:25[0-5]|2[0-4]\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|[1-9]?\d)){3}$/,
                                message: "Invalid IP Address format"
                            }
                        ]}
                        validateTrigger="onBlur"
                    >
                        <Input placeholder="Example: ***********" />
                    </Form.Item>

                    <Row style={{alignItems: "center", marginBottom: 24}}>
                        <Col flex="auto">
                            <Form.Item
                                name="switchSN"
                                label={switchLabel}
                                rules={[{required: true, message: "Please select a switch"}]}
                                style={{marginBottom: 0}}
                            >
                                <Input
                                    readOnly
                                    value={selectedSwitchSN || ""}
                                    className={styles.inputFileFilled}
                                    onClick={() =>
                                        selectSwitchTableModalRef.current.showCreateClientTableModal(selectedSwitchSN)
                                    }
                                    style={{cursor: "pointer"}}
                                />
                            </Form.Item>
                        </Col>

                        <Col style={{marginRight: 135}}>
                            <Button
                                onClick={() =>
                                    selectSwitchTableModalRef.current.showCreateClientTableModal(selectedSwitchSN)
                                }
                            >
                                Select
                            </Button>
                        </Col>
                    </Row>

                    <Form.Item
                        name="terminalType"
                        label={terminalTypeLabel}
                        rules={[
                            {
                                pattern: /^(?!\s*$)[^\u4e00-\u9fa5]*$/,
                                message: "Invalid terminal type!"
                            },
                            {
                                max: 32,
                                message: "Terminal type must be less than 32 characters!"
                            }
                        ]}
                    >
                        <AutoComplete
                            style={{width: "100%"}}
                            popupClassName="terminal-type-dropdown"
                            placeholder="Select or input Terminal Type"
                            options={filteredTerminalOptions}
                            onSearch={handleTerminalTypeSearch}
                            filterOption={false}
                            value={terminalInputValue}
                            onChange={value => setTerminalInputValue(value)}
                            onFocus={() => {
                                if (terminalInputValue.length < 3) {
                                    setFilteredTerminalOptions(terminalTypes.map(opt => ({value: opt})));
                                }
                            }}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    ) : null;
});

export default CreateClientModal;
