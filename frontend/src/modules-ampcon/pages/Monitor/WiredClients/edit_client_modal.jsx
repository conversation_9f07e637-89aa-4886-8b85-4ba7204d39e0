import {forwardRef, useImperativeHandle, useState} from "react";
import {But<PERSON>, Divider, Flex, Form, Input, message, Modal, Select, AutoComplete} from "antd";
import {editClient} from "@/modules-ampcon/apis/monitor_api";

let currentClientId = null;

const EditClientModal = forwardRef((props, ref) => {
    const {isOnline, saveCallback, terminalTypes, manufacturers, setTerminalTypes, setManufacturers} = props;

    const title = "Edit Wired Client";
    const clientNameLabel = "Client Name";
    const terminalTypeLabel = "Terminal Type";
    const ipAddressLabel = "IP Address";
    const manufacturer = "Manufacturer";

    const [editClientForm] = Form.useForm();
    const [isShowModal, setIsShowModal] = useState(false);

    const [filteredTerminalOptions, setFilteredTerminalOptions] = useState([]);
    const [filteredManufacturerOptions, setFilteredManufacturerOptions] = useState([]);
    const [terminalInputValue, setTerminalInputValue] = useState("");
    const [manufacturerInputValue, setManufacturerInputValue] = useState("");

    useImperativeHandle(ref, () => ({
        showEditClientModal: record => {
            setIsShowModal(true);
            currentClientId = record.id;
            editClientForm.setFieldsValue({
                clientName: record.client_name,
                ipAddress: record.ip_address,
                manufacturer: record.manufacturer,
                terminalType: record.terminal_type
            });

            setFilteredTerminalOptions(terminalTypes.map(opt => ({value: opt})));
            setFilteredManufacturerOptions(manufacturers.map(opt => ({value: opt})));
            setTerminalInputValue(record.terminal_type || "");
            setManufacturerInputValue(record.manufacturer || "");
        },
        hideEditClientModal: () => {
            editClientForm.resetFields();
            setIsShowModal(false);
        }
    }));

    const generateRegex = input => {
        const letters = input.split("");
        return new RegExp(`.*${letters.join(".*")}.*`, "i");
    };

    const handleTerminalTypeSearch = value => {
        setTerminalInputValue(value);

        if (value.length < 3) {
            setFilteredTerminalOptions(terminalTypes.map(opt => ({value: opt})));
            return;
        }

        const regex = generateRegex(value);
        const filteredOptions = terminalTypes.filter(type => regex.test(type));
        setFilteredTerminalOptions(filteredOptions.map(opt => ({value: opt})));
    };

    const handleManufacturerSearch = value => {
        setManufacturerInputValue(value);

        if (value.length < 3) {
            setFilteredManufacturerOptions(manufacturers.map(opt => ({value: opt})));
            return;
        }

        const regex = generateRegex(value);
        const filteredOptions = manufacturers.filter(m => regex.test(m));
        setFilteredManufacturerOptions(filteredOptions.map(opt => ({value: opt})));
    };

    const editClientSaveCallback = async () => {
        await editClientForm.validateFields();
        const formValues = editClientForm.getFieldsValue();

        if (formValues.terminalType) {
            const matchedTerminal = terminalTypes.find(
                type => type.toLowerCase() === formValues.terminalType.trim().toLowerCase()
            );
            if (matchedTerminal) {
                formValues.terminalType = matchedTerminal;
            }
        }

        if (formValues.manufacturer) {
            const matchedManufacturer = manufacturers.find(
                m => m.toLowerCase() === formValues.manufacturer.trim().toLowerCase()
            );
            if (matchedManufacturer) {
                formValues.manufacturer = matchedManufacturer;
            }
        }

        editClient({clientId: currentClientId, ...formValues}).then(response => {
            if (response.status === 200) {
                const newTerminalType = formValues.terminalType;
                if (
                    newTerminalType &&
                    !terminalTypes.some(type => type.toLowerCase() === newTerminalType.toLowerCase()) &&
                    typeof setTerminalTypes === "function"
                ) {
                    setTerminalTypes(prev => [...prev, newTerminalType]);
                }

                const newManufacturer = formValues.manufacturer;
                if (
                    newManufacturer &&
                    !manufacturers.some(m => m.toLowerCase() === newManufacturer.toLowerCase()) &&
                    typeof setManufacturers === "function"
                ) {
                    setManufacturers(prev => [...prev, newManufacturer]);
                }

                setIsShowModal(false);
                message.success(response.info);
                saveCallback();
            } else {
                message.error(response.info);
            }
        });
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                editClientForm.resetFields();
                setIsShowModal(false);
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                editClientForm.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                editClientSaveCallback();
                                saveCallback();
                            }}
                        >
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{flex: "120px"}}
                wrapperCol={{flex: "280px"}}
                labelWrap
                className="label-wrap"
                form={editClientForm}
            >
                <Form.Item
                    name="clientName"
                    label={clientNameLabel}
                    rules={[
                        {required: true, message: "Please input client name!"},
                        {
                            max: 32,
                            message: "Client name must be less than 32 characters!"
                        },
                        {
                            pattern: /^(?!\s*$)[^\u4e00-\u9fa5]*$/,
                            message: "Invalid client name!"
                        }
                    ]}
                    validateTrigger="onBlur"
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="ipAddress"
                    label={ipAddressLabel}
                    rules={[
                        {
                            pattern: /^(?:25[0-5]|2[0-4]\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|[1-9]?\d)){3}$/,
                            message: "Invalid IP Address format"
                        }
                    ]}
                    validateTrigger="onBlur"
                >
                    <Input placeholder="Please input the IP Address" style={{color: "#1F1F1F"}} disabled={isOnline} />
                </Form.Item>
                <Form.Item
                    name="terminalType"
                    label={terminalTypeLabel}
                    rules={[
                        {
                            pattern: /^(?!\s*$)[^\u4e00-\u9fa5]*$/,
                            message: "Invalid terminal type!"
                        },
                        {
                            max: 32,
                            message: "Terminal type must be less than 32 characters!"
                        }
                    ]}
                >
                    <AutoComplete
                        style={{width: "100%"}}
                        popupClassName="terminal-type-dropdown"
                        placeholder="Select or input Terminal Type"
                        options={filteredTerminalOptions}
                        onSearch={handleTerminalTypeSearch}
                        filterOption={false}
                        value={terminalInputValue}
                        onChange={value => setTerminalInputValue(value)}
                        onFocus={() => {
                            if (terminalInputValue.length < 3) {
                                setFilteredTerminalOptions(terminalTypes.map(opt => ({value: opt})));
                            }
                        }}
                    />
                </Form.Item>
                <Form.Item
                    name="manufacturer"
                    label={manufacturer}
                    rules={[
                        {
                            pattern: /^(?!\s*$)[^\u4e00-\u9fa5]*$/,
                            message: "Invalid manufacturer!"
                        },
                        {
                            max: 128,
                            message: "Manufacturer must be less than 128 characters!"
                        }
                    ]}
                >
                    <AutoComplete
                        style={{width: "100%"}}
                        popupClassName="manufacturer-dropdown"
                        placeholder="Select or input Manufacturer"
                        options={filteredManufacturerOptions}
                        onSearch={handleManufacturerSearch}
                        filterOption={false}
                        value={manufacturerInputValue}
                        onChange={value => setManufacturerInputValue(value)}
                        onFocus={() => {
                            if (manufacturerInputValue.length < 3) {
                                setFilteredManufacturerOptions(manufacturers.map(opt => ({value: opt})));
                            }
                        }}
                    />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default EditClientModal;
