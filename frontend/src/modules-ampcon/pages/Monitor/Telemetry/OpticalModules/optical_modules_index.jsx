import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import OpticalModules from "@/modules-ampcon/pages/Telemetry/Switch/optical_modules";
import ProtectedRoute from "@/modules-ampcon/utils/util";

const items = [
    {
        key: "switch",
        label: "Switch",
        children: <ProtectedRoute component={OpticalModules} />
    }
];

const OpticalModulesIndex = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    useEffect(() => {
        const currentPath = location.pathname;
        if (/(switch)$/.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(/(switch)$/)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (/(switch)$/.test(currentPath)) {
            const matchLength = currentPath.match(/(switch)$/)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <div className="scrollable-container">
            <Tabs activeKey={currentActiveKey} items={items} onChange={onChange} destroyInactiveTabPane />
        </div>
    );
};

export default OpticalModulesIndex;
