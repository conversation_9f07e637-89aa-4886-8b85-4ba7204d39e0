.monitoring {
    :global {
        .ant-select-multiple .ant-select-selection-item-remove:hover {
            color: #14c9bb;
        }

        .ant-select .ant-select-clear:hover {
            color: #14c9bb;
        }
    }
}

.dlbTab {
    :global {
      .ant-tabs-tabpane {
        padding: 0px 0px !important;
        flex-direction: column !important;
      }
  
      .ant-tabs-nav-wrap {
        background: #FFFFFF;
      }
  
      .ant-tabs-nav {
        background: #FFFFFF;

        &::before{
          border: none !important;
        }
        
        .ant-tabs-ink-bar {
          top: unset;
        }
  
        .ant-tabs-tab {
          padding: 14px 0px;
          font-weight: 600;
          margin-left: 32px !important;
          background: #FFFFFF;
          border: none;
        }
      }
    }
  }

// .activeTab {
//     width: 98px;
//     height: 34px;
//     background: #E7F9F8;
//     border: 1px solid #14C9BB;
//     line-height: 34px;
//     display: inline-block;
//     text-align: center;
//     font-weight: 400;
//     font-size: 14px;
//     color: #14C9BB;
// }
// .inactiveTab {
//     width: 98px;
//     height: 34px;
//     background: #FFFFFF;
//     border: 1px solid #C5D0D6;
//     line-height: 34px;
//     display: inline-block;
//     text-align: center;
//     font-weight: 400;
//     font-size: 14px;
//     color: #929A9E;
// }
  
.activeTab {
    width: 98px;
    height: 34px;
    background: #E7F9F8;
    border: 1px solid #14C9BB;
    line-height: 34px;
    display: inline-block;
    text-align: center;
    font-weight: 400;
    font-size: 14px;
    color: #14C9BB;
}
.inactiveTab {
    width: 98px;
    height: 34px;
    background: #FFFFFF;
    border: 1px solid #C5D0D6;
    line-height: 34px;
    display: inline-block;
    text-align: center;
    font-weight: 400;
    font-size: 14px;
    color: #929A9E;
}

// :global {
//   .my-tabs-wrapper .ant-tabs-nav {
//     background: white !important;
//     padding-left: 0 !important;
//       &::before {
//         display: none !important;
//     }
//   }
// }

:global {
  .my-tabs-wrapper .ant-tabs-nav {
    padding-left: 0 !important;
    background: white !important;

    &::before {
      display: none !important; // 去掉下面的横线
    }

    .ant-tabs-tab {
      margin: 0 !important;
      padding: 12px 24px;
      //background: #EAEDF1;
      border: none;
      border-bottom: none;
      //border-radius: 4px 4px 0 0;
      box-shadow: none !important; // 去阴影
    }

    .ant-tabs-tab-active {
      background: #FFFFFF;
      border: none !important;
      border-bottom: none !important;
      box-shadow: none !important; // 去阴影
    }
  }
}
