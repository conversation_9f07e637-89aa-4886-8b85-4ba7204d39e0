import React, {useEffect, useState, useMemo} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import {useSelector} from "react-redux";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import SwitchPage from "./Switch";
import DLB from "@/modules-ampcon/pages/Monitor/Network/dlb";

let items = [
    {
        key: "pfc_ecn",
        label: "PFC & ECN",
        children: <ProtectedRoute component={SwitchPage} />
    },
    {
        key: "DLB",
        label: "DLB",
        children: <ProtectedRoute component={DLB} />
    }
];
const RoCESwitchManager = () => {
    const currentUser = useSelector(state => state.user.userInfo);

    // items = currentUser.type === "readonly" ? items.filter(item => item.key !== "pfc_ecn") : items;

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    useEffect(() => {
        const currentPath = location.pathname;
        if (/(pfc_ecn|DLB)$/.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(/(pfc_ecn|DLB)$/)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (/(pfc_ecn|DLB)$/.test(currentPath)) {
            const matchLength = currentPath.match(/(pfc_ecn|DLB)$/)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <div style={{display: "flex", flex: 1}}>
            <Tabs
                style={{flex: 1}}
                activeKey={currentActiveKey}
                items={items}
                onChange={onChange}
                destroyInactiveTabPane
            />
        </div>
    );
};

export default RoCESwitchManager;
