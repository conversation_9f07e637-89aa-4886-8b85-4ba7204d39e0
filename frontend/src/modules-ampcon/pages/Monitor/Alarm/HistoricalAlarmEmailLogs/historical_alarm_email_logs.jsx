import {
    AmpConCustomTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {Card, message, Space, Tag} from "antd";
import {alarmEmailLogsTableData, deleteAlarmEmailLogs} from "@/modules-ampcon/apis/email_api";
import {useLocation} from "react-router-dom";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {useRef} from "react";
import EmailDetailModal from "@/modules-ampcon/pages/Monitor/Alarm/HistoricalAlarmEmailLogs/email_detail_modal";
import {useSelector} from "react-redux";

const EmailLogSuccessStatusTag = ({label}) => {
    return <Tag className="successTag">{label}</Tag>;
};

const EmailLogFailedStatusTag = ({label}) => {
    return <Tag className="failedTag">{label}</Tag>;
};

const HistoricalAlarmEmailLogs = () => {
    const location = useLocation();
    const {ruleName} = location.state || {};
    const columns = [
        createColumnConfigMultipleParams({
            title: "Rule Name",
            dataIndex: "email_rule_name",
            filterDropdownComponent: TableFilterDropdown,
            width: "15%",
            defaultValue: ruleName
        }),
        createColumnConfigMultipleParams({
            title: "Subject",
            dataIndex: "subject",
            enableFilter: false,
            enableSorter: false,
            width: "20%",
            // 新增换行样式
            render: text => (
                <div
                    style={{
                        whiteSpace: "normal"
                    }}
                >
                    {text}
                </div>
            )
        }),
        createColumnConfigMultipleParams({
            title: "Receivers",
            dataIndex: "receivers",
            enableFilter: false,
            enableSorter: false,
            width: "20%",
            // 新增换行样式
            render: text => (
                <div
                    style={{
                        whiteSpace: "normal"
                    }}
                >
                    {text}
                </div>
            )
        }),
        createColumnConfigMultipleParams({
            title: "Status",
            dataIndex: "status",
            width: "15%",
            filterDropdownComponent: TableFilterDropdown,
            render: statusValue => {
                if (statusValue === "failed") {
                    return <EmailLogFailedStatusTag label="Failed" />;
                }
                return <EmailLogSuccessStatusTag label="Success" />;
            }
        }),
        createColumnConfigMultipleParams({
            title: "Send Time",
            dataIndex: "send_time",
            enableFilter: false,
            enableSorter: false,
            width: "15%"
        }),
        {
            title: "Operation",
            width: "15%",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            {record.status === "success" && (
                                <a
                                    onClick={() => {
                                        emailDetailModalRef.current.showEmailDetailModal(record.id);
                                    }}
                                >
                                    Details
                                </a>
                            )}
                            {!isReadonlyUser ? (
                                <a
                                    onClick={() =>
                                        confirmModalAction("Are you sure want to delete?", () => {
                                            deleteAlarmEmailLogs({emailLogId: record.id}).then(response => {
                                                if (response.status === 200) {
                                                    message.success(response.info);
                                                    tableRef.current.refreshTable();
                                                } else {
                                                    message.error(response.info);
                                                }
                                            });
                                        })
                                    }
                                >
                                    Delete
                                </a>
                            ) : null}
                        </Space>
                    </div>
                );
            }
        }
    ];

    const searchFieldsList = ["email_search_key", "email_rule_name", "subject", "receivers"];

    const matchFieldsList = [
        {name: "email_rule_name", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"}
    ];

    const tableRef = useRef(null);
    const emailDetailModalRef = useRef(null);

    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;
    const isReadonlyUser = userType === "readonly";

    return (
        <Card style={{flex: 1}} className="alarmTablestyle">
            <EmailDetailModal ref={emailDetailModalRef} />
            <h2 style={{margin: "8px 0 20px"}}>Notification History</h2>
            <AmpConCustomTable
                ref={tableRef}
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                fetchAPIInfo={alarmEmailLogsTableData}
            />
        </Card>
    );
};

export default HistoricalAlarmEmailLogs;
