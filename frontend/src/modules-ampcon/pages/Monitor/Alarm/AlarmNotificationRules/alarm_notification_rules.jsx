import {Card, Button, Flex, message, Space, Tag} from "antd";
import {
    deleteEmailRoleSetting,
    getEmailServerSetting,
    getEmailRuleSettingsTableData
} from "@/modules-ampcon/apis/email_api";
import {
    AmpConCustomTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {useEffect, useRef, useState} from "react";
import Icon from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/Monitor/Alarm/alarm.module.scss";
import {addSvg} from "@/utils/common/iconSvg";
import EmailRuleSettingModal from "@/modules-ampcon/pages/Monitor/Alarm/AlarmNotificationRules/email_rule_setting_modal";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {getFabric, getSite} from "@/modules-ampcon/apis/lifecycle_api";
import {useNavigate} from "react-router-dom";
import {useSelector} from "react-redux";

const EmailRuleTag = ({label}) => {
    if (label === "Error") {
        return <Tag className={styles.errorStyle}>{label}</Tag>;
    }
    if (label === "Warning") {
        return <Tag className={styles.warnStyle}>{label}</Tag>;
    }
    if (label === "Enabled") {
        return <Tag className={styles.enabledStyle}>{label}</Tag>;
    }
    if (label === "Disabled") {
        return <Tag className={styles.disabledStyle}>{label}</Tag>;
    }
    return <Tag className={styles.infoStyle}>{label}</Tag>;
};

const AlarmNotificationRules = () => {
    const currentAmpConType = import.meta.env.VITE_APP_EXPORT_MODULE;
    const navigate = useNavigate();

    const columns = [
        createColumnConfigMultipleParams({
            title: "Rule Name",
            dataIndex: "rule_name",
            filterDropdownComponent: TableFilterDropdown,
            width: "12.5%",
            render: text => text || "--"
        }),
        {
            title: `Alert Scope ${currentAmpConType === "AmpCon-DC" ? "(Fabric)" : "(Site)"}`,
            width: "12.5%",
            render: (_, record) => {
                let scope = [];
                if (currentAmpConType === "AmpCon-DC") {
                    scope = record.fabric_name_list;
                } else if (currentAmpConType === "AmpCon-CAMPUS") {
                    scope = record.site_name_list;
                }
                if (scope === "") {
                    return (
                        <Flex style={{flexWrap: "wrap", rowGap: "5px", marginTop: "6px", marginBottom: "6px"}}>--</Flex>
                    );
                }
                return (
                    <Flex style={{flexWrap: "wrap", rowGap: "5px", marginTop: "6px", marginBottom: "6px"}}>
                        {scope.map(item => {
                            return <EmailRuleTag label={item} />;
                        })}
                    </Flex>
                );
            }
        },
        createColumnConfigMultipleParams({
            title: "Alert Level",
            render: (_, record) => {
                const levels = [];
                if (record.alarm_level_error) levels.push("Error");
                if (record.alarm_level_warning) levels.push("Warning");
                if (record.alarm_level_info) levels.push("Info");

                if (levels.length === 0) {
                    return <span>--</span>;
                }

                return (
                    <Flex style={{flexWrap: "wrap", rowGap: "5px", marginTop: "6px", marginBottom: "6px"}}>
                        {levels.map(level => (
                            <EmailRuleTag key={level} label={level} />
                        ))}
                    </Flex>
                );
            },
            enableFilter: false,
            enableSorter: false,
            width: "12.5%"
        }),

        createColumnConfigMultipleParams({
            title: "Alert Type",
            render: (_, record) => {
                const types = [];
                if (record.alarm_type_packet_loss_alert) types.push("Packet Loss Alert");
                if (record.alarm_type_resource_usage_alert) types.push("Resource Usage Alert");
                if (record.alarm_type_ai_monitoring_alert) types.push("AI Monitoring Alert");
                if (record.alarm_type_interface_monitoring_alert) types.push("Interface Monitoring Alert");
                // if (record.alarm_type_optical_module_alert) types.push("Optical Module Alert");

                if (types.length === 0) {
                    return <span>--</span>;
                }

                return (
                    <Flex style={{flexWrap: "wrap", rowGap: "5px", marginTop: "6px", marginBottom: "6px"}}>
                        {types.map(type => (
                            <EmailRuleTag key={type} label={type} />
                        ))}
                    </Flex>
                );
            },
            enableFilter: false,
            enableSorter: false,
            width: "12.5%"
        }),

        createColumnConfigMultipleParams({
            title: "Silent Period (Minutes)",
            dataIndex: "silent_time",
            enableFilter: false,
            enableSorter: false,
            width: "12.5%",
            render: text => `${text / 60}`
        }),
        {
            title: "Status",
            width: "12.5%",
            render: (_, record) => {
                return record.enable === true ? <EmailRuleTag label="Enabled" /> : <EmailRuleTag label="Disabled" />;
            }
        },
        createColumnConfigMultipleParams({
            title: "Create User",
            dataIndex: "create_user",
            filterDropdownComponent: TableFilterDropdown,
            width: "12.5%"
        }),
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            {!isReadonlyUser ? (
                                <a
                                    onClick={() => {
                                        clickEditEmailUserSettingCallback(record);
                                    }}
                                >
                                    Edit
                                </a>
                            ) : null}
                            <a
                                onClick={() => {
                                    navigate("/monitor/alerts/notification_history", {
                                        state: {ruleName: record.rule_name}
                                    });
                                }}
                            >
                                Logs
                            </a>
                            {!isReadonlyUser ? (
                                <a
                                    onClick={() => {
                                        clickDeleteEmailRuleSettingCallback(record);
                                    }}
                                >
                                    Delete
                                </a>
                            ) : null}
                        </Space>
                    </div>
                );
            }
        }
    ];

    const searchFieldsList = ["rule_name", "create_user"];
    const matchFieldsList = [
        {name: "rule_name", matchMode: "fuzzy"},
        {name: "create_user", matchMode: "fuzzy"}
    ];

    const tableRef = useRef();
    const emailRuleSettingModalRef = useRef();

    const [defaultEmailServerSetting, setDefaultEmailServerSetting] = useState({});
    const [allFabricList, setAllFabricList] = useState([]);
    const [allSiteList, setAllSiteList] = useState([]);

    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;
    const isReadonlyUser = userType === "readonly";

    useEffect(() => {
        getEmailServerSettingCallback();
        getAllFabricListCallback();
        getAllSiteListCallback();
    }, []);

    const getEmailServerSettingCallback = () => {
        getEmailServerSetting().then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                setDefaultEmailServerSetting(response.data);
            }
        });
    };

    const getAllFabricListCallback = async () => {
        try {
            const response = await getFabric();
            if (response.status === 200) {
                setAllFabricList(response.data);
            } else {
                setAllFabricList([]);
            }
        } catch (error) {
            setAllFabricList([]);
        }
    };

    const getAllSiteListCallback = async () => {
        try {
            const response = await getSite();
            if (response.status === 200) {
                setAllSiteList(response.data);
            } else {
                setAllSiteList([]);
            }
        } catch (error) {
            setAllSiteList([]);
        }
    };

    const clickEditEmailUserSettingCallback = record => {
        const emailAlertLevel = [];
        const emailAlertType = [];
        if (record.alarm_level_error) {
            emailAlertLevel.push("error");
        }
        if (record.alarm_level_warning) {
            emailAlertLevel.push("warning");
        }
        if (record.alarm_level_info) {
            emailAlertLevel.push("info");
        }
        if (record.alarm_type_ai_monitoring_alert) {
            emailAlertType.push("ai_monitoring_alert");
        }
        if (record.alarm_type_interface_monitoring_alert) {
            emailAlertType.push("interface_monitoring_alert");
        }
        // if (record.alarm_type_optical_module_alert) {
        //     emailAlertType.push("optical_module_alert");
        // }
        if (record.alarm_type_packet_loss_alert) {
            emailAlertType.push("packet_loss_alert");
        }
        if (record.alarm_type_resource_usage_alert) {
            emailAlertType.push("resource_usage_alert");
        }
        emailRuleSettingModalRef.current.showEmailUserSettingModal({
            ruleName: record.rule_name,
            fabric: record.fabric_name_list,
            site: record.site_name_list,
            email: record.email,
            silentPeriod: record.silent_time / 60,
            emailAlertLevel,
            emailAlertType,
            recordId: record.id,
            emailRuleIsEnable: record.enable ? "enabled" : "disabled"
        });
    };

    const clickDeleteEmailRuleSettingCallback = record => {
        confirmModalAction("Are you sure want to delete the alarm notification rule?", () => {
            deleteEmailRoleSetting({emailId: record.id}).then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                    tableRef.current.refreshTable();
                }
            });
        });
    };

    const alarmsNotificationRulePageRender = () => {
        return (
            <>
                <AmpConCustomTable
                    columns={columns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    extraButton={
                        !isReadonlyUser ? (
                            <Button
                                type="primary"
                                onClick={() => {
                                    if (defaultEmailServerSetting.emailServer === undefined) {
                                        message.error("Please set email server setting first.");
                                        return;
                                    }
                                    emailRuleSettingModalRef.current.showEmailUserSettingModal();
                                }}
                            >
                                <Icon component={addSvg} />
                                Rule
                            </Button>
                        ) : null
                    }
                    fetchAPIInfo={getEmailRuleSettingsTableData}
                    ref={tableRef}
                />
                <EmailRuleSettingModal
                    ref={emailRuleSettingModalRef}
                    saveCallback={() => {
                        tableRef.current.refreshTable();
                    }}
                    emailAlertAllTypes={defaultEmailServerSetting.emailAlertAllTypes}
                    emailAlertAllLevels={defaultEmailServerSetting.emailAlertAllLevels}
                    allFabricList={allFabricList}
                    allSiteList={allSiteList}
                />
            </>
        );
    };
    return (
        <Card style={{flex: 1}} className="alarmTablestyle">
            <h2 style={{margin: "8px 0 20px"}}>Notification Rules</h2>
            {alarmsNotificationRulePageRender()}
        </Card>
    );
};

export default AlarmNotificationRules;
