import {forwardRef, useImperativeHandle, useState, useEffect} from "react";
import {Button, Divider, Form, Input, Modal, Spin, message, Transfer, Select, InputNumber, Radio} from "antd";
import {addEmailRuleSetting, updateEmailRuleSetting} from "@/modules-ampcon/apis/email_api";

const EmailRuleSettingModal = forwardRef((props, ref) => {
    const addModalTitle = "Create Rule";
    const editModalTitle = "Edit Rule";
    const ruleNameLabel = "Rule Name";
    const fabricLabel = "Fabric";
    const siteLabel = "Site";
    const emailLabel = "Email";
    const isRuleEnable = "Email Notification";
    const alertLevelLabel = "Alert Level";
    const alertTypeLabel = "Alert Type";
    const silentPeriodLabel = "Silent Period (Minutes)";

    const {saveCallback, emailAlertAllLevels, emailAlertAllTypes, allFabricList, allSiteList} = props;

    useImperativeHandle(ref, () => ({
        showEmailUserSettingModal: defaultEmailUserSetting => {
            if (defaultEmailUserSetting) {
                updateEmailRuleSettingForm.setFieldsValue(defaultEmailUserSetting);
                setIsModalAdd(false);
                setCurrentRecordId(defaultEmailUserSetting.recordId);
                setAlertLevelTargetKeys(defaultEmailUserSetting.emailAlertLevel);
                setAlertTypeTargetKeys(defaultEmailUserSetting.emailAlertType);
            } else {
                updateEmailRuleSettingForm.resetFields();
                setIsModalAdd(true);
                setCurrentRecordId(null);
                setAlertLevelTargetKeys([]);
                setAlertTypeTargetKeys([]);
            }
            setIsShowModal(true);
        },
        hideEmailUserSettingModal: () => {
            setIsShowModal(false);
            updateEmailRuleSettingForm.resetFields();
        }
    }));

    const [updateEmailRuleSettingForm] = Form.useForm();

    const [isShowModal, setIsShowModal] = useState(false);
    const [isShowSpin, setIsShowSpin] = useState(false);
    const [isModalAdd, setIsModalAdd] = useState(true);
    const [currentRecordId, setCurrentRecordId] = useState(null);

    // 新增状态用于管理 Alert Level Transfer 组件的数据
    const [alertLevelData, setAlertLevelData] = useState([]);
    const [alertLevelTargetKeys, setAlertLevelTargetKeys] = useState([]);

    // 新增状态用于管理 Alert Type Transfer 组件的数据
    const [alertTypeData, setAlertTypeData] = useState([]);
    const [alertTypeTargetKeys, setAlertTypeTargetKeys] = useState([]);

    // 生成 Alert Level Transfer 组件的数据
    const getAlertLevelData = () => {
        const tempAlertLevelTargetKeys = [];
        const tempAlertLevelData = emailAlertAllLevels.map(level => ({
            key: level,
            title: level.charAt(0).toUpperCase() + level.slice(1) // 首字母大写
        }));
        setAlertLevelData(tempAlertLevelData);
        setAlertLevelTargetKeys(tempAlertLevelTargetKeys);
    };

    // 生成 Alert Type Transfer 组件的数据
    const getAlertTypeData = () => {
        const tempAlertTypeTargetKeys = [];
        const alertTypeLabelMappings = {
            packet_loss_alert: "Packet Loss Alert",
            resource_usage_alert: "Resource Usage Alert",
            ai_monitoring_alert: "AI Monitoring Alert",
            interface_monitoring_alert: "Interface Monitoring Alert"
            // optical_module_alert: "Optical Module Alert"
        };
        const tempAlertTypeData = emailAlertAllTypes.map(alertType => ({
            key: alertType,
            title:
                alertTypeLabelMappings[alertType] === undefined
                    ? alertType.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())
                    : alertTypeLabelMappings[alertType]
        }));
        setAlertTypeData(tempAlertTypeData);
        setAlertTypeTargetKeys(tempAlertTypeTargetKeys);
    };

    // 新增：初始化 Alert Level 和 Alert Type 数据并保持更新
    useEffect(() => {
        if (emailAlertAllLevels?.length > 0 && emailAlertAllTypes?.length > 0) {
            getAlertLevelData();
            getAlertTypeData();
        }
    }, [emailAlertAllLevels, emailAlertAllTypes]);

    // 新增：处理 Alert Level Transfer 组件的 onChange 事件
    const handleAlertLevelChange = newTargetKeys => {
        setAlertLevelTargetKeys(newTargetKeys);
        const selectedValues = newTargetKeys.map(key => key);
        updateEmailRuleSettingForm.setFieldsValue({emailAlertLevel: selectedValues});
    };
    // 新增：处理 Alert Type Transfer 组件的 onChange 事件
    const handleAlertTypeChange = newTargetKeys => {
        setAlertTypeTargetKeys(newTargetKeys);
        const selectedValues = newTargetKeys.map(key => key);
        updateEmailRuleSettingForm.setFieldsValue({emailAlertType: selectedValues});
    };

    const emailUserInfoAddCallback = async () => {
        setIsShowSpin(true);
        try {
            await updateEmailRuleSettingForm.validateFields();
            const data = {
                ruleName: updateEmailRuleSettingForm.getFieldValue("ruleName"),
                fabric: updateEmailRuleSettingForm.getFieldValue("fabric"),
                site: updateEmailRuleSettingForm.getFieldValue("site"),
                email: updateEmailRuleSettingForm.getFieldValue("email"),
                emailRuleIsEnable: updateEmailRuleSettingForm.getFieldValue("emailRuleIsEnable") === "enabled",
                silentPeriod: updateEmailRuleSettingForm.getFieldValue("silentPeriod") * 60,
                enableError: updateEmailRuleSettingForm.getFieldValue("emailAlertLevel")
                    ? updateEmailRuleSettingForm.getFieldValue("emailAlertLevel").includes("error")
                    : false,
                enableWarning: updateEmailRuleSettingForm.getFieldValue("emailAlertLevel")
                    ? updateEmailRuleSettingForm.getFieldValue("emailAlertLevel").includes("warning")
                    : false,
                enableInfo: updateEmailRuleSettingForm.getFieldValue("emailAlertLevel")
                    ? updateEmailRuleSettingForm.getFieldValue("emailAlertLevel").includes("info")
                    : false,
                // 新增：根据 emailAlertType 字段设置相应的警报类型（true/false）
                enablePacketLossAlert: updateEmailRuleSettingForm.getFieldValue("emailAlertType")
                    ? updateEmailRuleSettingForm.getFieldValue("emailAlertType").includes("packet_loss_alert")
                    : false,
                enableResourceUsageAlert: updateEmailRuleSettingForm.getFieldValue("emailAlertType")
                    ? updateEmailRuleSettingForm.getFieldValue("emailAlertType").includes("resource_usage_alert")
                    : false,
                enableAIMonitoringAlert: updateEmailRuleSettingForm.getFieldValue("emailAlertType")
                    ? updateEmailRuleSettingForm.getFieldValue("emailAlertType").includes("ai_monitoring_alert")
                    : false,
                enableInterfaceMonitoringAlert: updateEmailRuleSettingForm.getFieldValue("emailAlertType")
                    ? updateEmailRuleSettingForm.getFieldValue("emailAlertType").includes("interface_monitoring_alert")
                    : false
                // enableOpticalModuleAlert: updateEmailRuleSettingForm.getFieldValue("emailAlertType")
                //     ? updateEmailRuleSettingForm.getFieldValue("emailAlertType").includes("optical_module_alert")
                //     : false
            };
            addEmailRuleSetting(data).then(response => {
                try {
                    if (response.status !== 200) {
                        message.error(response.info);
                    } else {
                        message.success(response.info);
                        setIsShowModal(false);
                        updateEmailRuleSettingForm.resetFields();
                        saveCallback();
                    }
                } finally {
                    setIsShowSpin(false);
                }
            });
        } catch (e) {
            setIsShowSpin(false);
        }
    };

    const emailUserInfoSaveCallback = async () => {
        setIsShowSpin(true);
        try {
            await updateEmailRuleSettingForm.validateFields();
        } catch (e) {
            setIsShowSpin(false);
            return;
        }
        const data = {
            ruleName: updateEmailRuleSettingForm.getFieldValue("ruleName"),
            fabric: updateEmailRuleSettingForm.getFieldValue("fabric"),
            site: updateEmailRuleSettingForm.getFieldValue("site"),
            email: updateEmailRuleSettingForm.getFieldValue("email"),
            silentPeriod: updateEmailRuleSettingForm.getFieldValue("silentPeriod") * 60,
            emailRuleIsEnable: updateEmailRuleSettingForm.getFieldValue("emailRuleIsEnable") === "enabled",
            enableError: updateEmailRuleSettingForm.getFieldValue("emailAlertLevel").includes("error"),
            enableWarning: updateEmailRuleSettingForm.getFieldValue("emailAlertLevel").includes("warning"),
            enableInfo: updateEmailRuleSettingForm.getFieldValue("emailAlertLevel").includes("info"),
            // 新增：添加 emailAlertType 字段
            enablePacketLossAlert: updateEmailRuleSettingForm
                .getFieldValue("emailAlertType")
                .includes("packet_loss_alert"),
            enableResourceUsageAlert: updateEmailRuleSettingForm
                .getFieldValue("emailAlertType")
                .includes("resource_usage_alert"),
            enableAIMonitoringAlert: updateEmailRuleSettingForm
                .getFieldValue("emailAlertType")
                .includes("ai_monitoring_alert"),
            enableInterfaceMonitoringAlert: updateEmailRuleSettingForm
                .getFieldValue("emailAlertType")
                .includes("interface_monitoring_alert"),
            // enableOpticalModuleAlert: updateEmailRuleSettingForm
            //     .getFieldValue("emailAlertType")
            //     .includes("optical_module_alert"),
            emailId: currentRecordId
        };
        try {
            updateEmailRuleSetting(data).then(response => {
                try {
                    if (response.status !== 200) {
                        message.error(response.info);
                    } else {
                        message.success(response.info);
                        setIsShowModal(false);
                        updateEmailRuleSettingForm.resetFields();
                        saveCallback();
                    }
                } finally {
                    setIsShowSpin(false);
                }
            });
        } catch (e) {
            setIsShowSpin(false);
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {isModalAdd ? addModalTitle : editModalTitle}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                updateEmailRuleSettingForm.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            updateEmailRuleSettingForm.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button type="primary" onClick={isModalAdd ? emailUserInfoAddCallback : emailUserInfoSaveCallback}>
                        {isModalAdd ? "Apply" : "Apply"}
                    </Button>
                </div>
            }
        >
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 7}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={updateEmailRuleSettingForm}
                style={{minHeight: "267.23px"}}
            >
                <Form.Item
                    name="ruleName"
                    rules={[
                        {required: true},
                        {
                            max: 64,
                            message: "Rule name cannot exceed 64 characters"
                        }
                    ]}
                    label={ruleNameLabel}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC" ? (
                    <Form.Item
                        name="fabric"
                        rules={[
                            {
                                required: true
                            }
                        ]}
                        label={fabricLabel}
                    >
                        <Select
                            mode="multiple"
                            allowClear
                            style={{width: 280}}
                            value={{}}
                            options={allFabricList.map(item => ({
                                value: item,
                                label: item
                            }))}
                        />
                    </Form.Item>
                ) : null}
                {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS" ? (
                    <Form.Item
                        name="site"
                        rules={[
                            {
                                required: true
                            }
                        ]}
                        label={siteLabel}
                    >
                        <Select
                            mode="multiple"
                            showSearch={false}
                            allowClear
                            style={{width: 280}}
                            value={{}}
                            options={allSiteList.map(item => ({
                                value: item,
                                label: item
                            }))}
                        />
                    </Form.Item>
                ) : null}

                <Form.Item
                    name="email"
                    rules={[
                        {required: true},
                        {
                            validator: (_, value) => {
                                if (
                                    !/^[\w%+.-]+@[\d.A-Za-z-]+\.[A-Za-z]{2,}(?:,[\w%+.-]+@[\d.A-Za-z-]+\.[A-Za-z]{2,})*$/.test(
                                        value
                                    )
                                ) {
                                    return Promise.reject(
                                        new Error("Please enter valid email addresses separated by commas(,)!")
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    label={emailLabel}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    initialValue={30}
                    name="silentPeriod"
                    label={silentPeriodLabel}
                    rules={[
                        {
                            required: true,
                            message: "Please enter a valid silent period"
                        },
                        {
                            validator: (_, value) => {
                                if (value !== null && Number(value) < 30) {
                                    return Promise.reject(
                                        new Error("Please set the silent period to at least 30 minutes.")
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <InputNumber style={{width: "280px"}} />
                </Form.Item>
                <Form.Item name="emailRuleIsEnable" label={isRuleEnable} initialValue="enabled">
                    <Radio.Group>
                        <Radio value="enabled">Enabled</Radio>
                        <Radio value="disabled">Disabled</Radio>
                    </Radio.Group>
                </Form.Item>

                {/* 新增：添加 Alert Level Transfer 组件 */}
                <Form.Item name="emailAlertLevel" label={alertLevelLabel}>
                    <div id="emailAlertLevel">
                        <Transfer
                            dataSource={alertLevelData}
                            targetKeys={alertLevelTargetKeys}
                            onChange={handleAlertLevelChange}
                            render={item => item.title}
                            showSelectAll={false}
                            listStyle={{
                                height: 196
                            }}
                        />
                    </div>
                </Form.Item>
                {/* 新增：添加 Alert Type Transfer 组件 */}
                <Form.Item name="emailAlertType" label={alertTypeLabel}>
                    <div id="emailAlertType">
                        <Transfer
                            dataSource={alertTypeData}
                            targetKeys={alertTypeTargetKeys}
                            onChange={handleAlertTypeChange}
                            render={item => item.title}
                            showSelectAll={false}
                            listStyle={{
                                height: 204
                            }}
                        />
                    </div>
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default EmailRuleSettingModal;
