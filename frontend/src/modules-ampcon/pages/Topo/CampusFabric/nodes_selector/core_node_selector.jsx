import BaseNodeSelector from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/base_node_selector";
import {Flex} from "antd";

const CoreNodeSelector = ({
    nodes,
    addCoreSwitchCallback,
    deleteCoreSwitchCallback,
    showDeviceBriefTooltipCallback,
    hideDeviceBriefTooltipCallback
}) => {
    return (
        <Flex vertical style={{marginBottom: "30px"}}>
            <h3>Core</h3>
            <BaseNodeSelector
                nodes={nodes}
                addNodeCallback={addCoreSwitchCallback}
                deleteNodeCallback={deleteCoreSwitchCallback}
                showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
            />
        </Flex>
    );
};

export default CoreNodeSelector;
