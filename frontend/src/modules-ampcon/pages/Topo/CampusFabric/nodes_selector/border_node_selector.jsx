import BaseNodeSelector from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/base_node_selector";
import {Flex} from "antd";

const BorderNodeSelector = ({
    nodes,
    addBorderSwitchCallback,
    deleteBorderSwitchCallback,
    showDeviceBriefTooltipCallback,
    hideDeviceBriefTooltipCallback
}) => {
    return (
        <Flex vertical style={{marginBottom: "30px"}}>
            {/* <h3>Border</h3> */}
            <BaseNodeSelector
                nodes={nodes}
                addNodeCallback={addBorderSwitchCallback}
                deleteNodeCallback={deleteBorderSwitchCallback}
                showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
            />
        </Flex>
    );
};

export default BorderNodeSelector;
