import {Flex} from "antd";
import addBottomSvg from "@/modules-ampcon/pages/Topo/CampusFabric/resource/add.svg";
import BaseSwitchNode from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/base_switch_node";
import "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/nodes_selector.scss";

const AddSwitchButtonNode = ({addSwitchCallback}) => {
    return (
        <div
            className="imgSvg"
            style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
                textAlign: "center",
                cursor: "pointer"
            }}
            onClick={addSwitchCallback}
        >
            <Flex justify="center" align="center" style={{width: "35px", height: "39px"}}>
                <img src={addBottomSvg} style={{height: "30px", width: "30px"}} alt="add switch" />
            </Flex>
            <div
                style={{
                    fontFamily: "Lato",
                    fontWeight: 600,
                    fontSize: "14px",
                    color: "#212519",
                    marginTop: "2px",
                    width: "98px",
                    whiteSpace: "nowrap"
                }}
            >
                Select Switches
            </div>
        </div>
    );
};

const BaseNodeSelector = ({
    nodes,
    index,
    addNodeCallback,
    deleteNodeCallback,
    showDeviceBriefTooltipCallback,
    hideDeviceBriefTooltipCallback
}) => {
    return (
        <Flex justify="center" align="center" style={{backgroundColor: "#F8FAFB", minHeight: "140px"}}>
            <Flex gap="50px" wrap="wrap" style={{padding: "30px"}}>
                {nodes &&
                    nodes.map(node => (
                        <BaseSwitchNode
                            node={node}
                            index={index}
                            deleteNodeCallback={deleteNodeCallback}
                            showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                            hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                        />
                    ))}
                <AddSwitchButtonNode
                    addSwitchCallback={() => {
                        addNodeCallback(index);
                    }}
                />
            </Flex>
        </Flex>
    );
};

export default BaseNodeSelector;
