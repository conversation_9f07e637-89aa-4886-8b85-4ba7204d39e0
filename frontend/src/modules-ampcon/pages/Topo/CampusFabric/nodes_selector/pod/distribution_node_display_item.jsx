import {Flex} from "antd";
import BaseNodeDisplayItem from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/base_node_display_item";

const DistributionNodeDisplayItem = ({
    nodes,
    index,
    showDeviceBriefTooltipCallback,
    hideDeviceBriefTooltipCallback
}) => {
    return (
        <Flex vertical>
            <h4>Distribution</h4>
            <BaseNodeDisplayItem
                nodes={nodes}
                index={index}
                showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
            />
        </Flex>
    );
};

export default DistributionNodeDisplayItem;
