import BaseNodeSelector from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/base_node_selector";
import {Flex} from "antd";

const DistributionNodeSelector = ({
    nodes,
    index,
    addDistributionSwitchCallback,
    deleteDistributionSwitchCallback,
    showDeviceBriefTooltipCallback,
    hideDeviceBriefTooltipCallback
}) => {
    return (
        <Flex vertical>
            <h4>Distribution</h4>
            <BaseNodeSelector
                nodes={nodes}
                index={index}
                addNodeCallback={addDistributionSwitchCallback}
                deleteNodeCallback={deleteDistributionSwitchCallback}
                showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
            />
        </Flex>
    );
};

export default DistributionNodeSelector;
