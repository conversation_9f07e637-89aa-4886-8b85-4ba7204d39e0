import {Form, Input, Select, Radio, Divider} from "antd";

import TextArea from "antd/es/input/TextArea";

import {useState, forwardRef, useImperativeHandle} from "react";

export const PortConfigurationForm = forwardRef(({networkData, networks, setCampusFabricData}, ref) => {
    const [isAdvancedEnable, setIsAdvancedEnable] = useState(networks.port_config.advanced.enable === "enable");
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
        getConfiguration: () => {
            return form.getFieldsValue();
        },
        removeRelatedNetwork: networkName => {
            const trunkNetworks = form.getFieldValue("trunk_networks");
            const portNetwork = form.getFieldValue(["advanced", "network"]);
            const newTrunkNetworks = trunkNetworks.filter(network => network !== networkName);
            const newPortNetwork = portNetwork === networkName ? null : portNetwork;
            form.setFieldsValue({
                trunk_networks: newTrunkNetworks,
                advanced: {
                    network: newPortNetwork
                }
            });
            return [newTrunkNetworks, newPortNetwork];
        },
        validate: () => {
            return Promise.resolve();
        }
    }));
    return (
        <div>
            <h3>Core/Access Port Configuration</h3>

            <Form
                ref={ref}
                form={form}
                onFinish={() => {}}
                validateTrigger={["onBlur", "onSubmit"]}
                labelAlign="left"
                style={{width: 505}}
                initialValues={{
                    ...networks.port_config,
                    advanced: {
                        ...networks.port_config.advanced,
                        enable: networks.port_config.advanced.enable === "enable" ? "enable" : "disable"
                    }
                }}
                onValuesChange={changedvalues => {
                    if (changedvalues.advanced) {
                        setCampusFabricData(prev => {
                            return {
                                ...prev,
                                networks: {
                                    ...prev.networks,
                                    port_config: {
                                        ...prev.networks.port_config,
                                        advanced: {
                                            ...prev.networks.port_config.advanced,
                                            ...changedvalues.advanced
                                        }
                                    }
                                }
                            };
                        });
                    } else {
                        setCampusFabricData(prev => {
                            return {
                                ...prev,
                                networks: {
                                    ...prev.networks,
                                    port_config: {
                                        ...prev.networks.port_config,
                                        ...changedvalues
                                    }
                                }
                            };
                        });
                    }
                }}
            >
                <Form.Item name="name" label="Name" labelCol={{style: {width: 175}}}>
                    <Input style={{width: 280}} />
                </Form.Item>
                <Form.Item name="trunk_networks" label="Trunk Networks" labelCol={{style: {width: 175}}}>
                    <Select mode="multiple" allowClear style={{width: 280}}>
                        {networkData.map(item => (
                            <Select.Option key={item.name} value={item.name}>
                                {item.name}({item.vlan_id})
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item
                    name={["advanced", "enable"]}
                    label="Advanced Configuration"
                    labelCol={{style: {width: 175}}}
                >
                    <Radio.Group
                        onChange={e => {
                            setIsAdvancedEnable(e.target.value === "enable");
                        }}
                    >
                        <Radio value="enable">Enable</Radio>
                        <Radio value="disable">Disable</Radio>
                    </Radio.Group>
                </Form.Item>
                {isAdvancedEnable && (
                    <div>
                        <Form.Item
                            name={["advanced", "port_enable"]}
                            label="Port Enable"
                            labelCol={{style: {width: 175}}}
                        >
                            <Radio.Group>
                                <Radio value="enable">Enable</Radio>
                                <Radio value="disable">Disable</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <Form.Item
                            name={["advanced", "description"]}
                            label="Description"
                            labelCol={{style: {width: 175}}}
                        >
                            <TextArea style={{width: 280}} rows={3} />
                        </Form.Item>
                        <Form.Item name={["advanced", "mode"]} label="Mode" labelCol={{style: {width: 175}}}>
                            <Radio.Group>
                                <Radio value="trunk">Trunk</Radio>
                                <Radio value="access">Access</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <Form.Item
                            name={["advanced", "network"]}
                            label={
                                <>
                                    Port Network
                                    <br />
                                    (Unstagged/Native VLAN)
                                </>
                            }
                            labelCol={{style: {width: 175, height: 43}}}
                        >
                            <Select allowClear style={{width: 280}}>
                                {networkData.map(item => (
                                    <Select.Option key={item.name} value={item.name}>
                                        {item.name}
                                    </Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item name={["advanced", "speed"]} label="Speed" labelCol={{style: {width: 175}}}>
                            <Select style={{width: 280}}>
                                <Select.Option value="10G">10G</Select.Option>
                                <Select.Option value="1G">1G</Select.Option>
                            </Select>
                        </Form.Item>
                        <Form.Item name={["advanced", "poe"]} label="POE" labelCol={{style: {width: 175}}}>
                            <Radio.Group>
                                <Radio value="enable">Enable</Radio>
                                <Radio value="disable">Disable</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <Form.Item name={["advanced", "stp_edge"]} label="STP Edge" labelCol={{style: {width: 175}}}>
                            <Radio.Group>
                                <Radio value="yes">Yes</Radio>
                                <Radio value="no">No</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <Form.Item
                            name={["advanced", "mtu"]}
                            label="MTU"
                            labelCol={{style: {width: 175}}}
                            rules={[
                                {
                                    validator: async (_, value) => {
                                        if (value >= 256 && value <= 9216) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject(new Error("Enter a number between 256 and 9216"));
                                    }
                                }
                            ]}
                        >
                            <Input style={{width: 280}} placeholder="Range(256-9216)" />
                        </Form.Item>
                    </div>
                )}
            </Form>
            {/* <Divider style={{marginTop: "32px", marginBottom: "32px"}} /> */}
        </div>
    );
});
