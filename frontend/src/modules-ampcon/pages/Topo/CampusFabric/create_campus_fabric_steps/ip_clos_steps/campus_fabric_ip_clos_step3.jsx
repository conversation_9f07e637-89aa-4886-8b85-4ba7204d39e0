import {useState, useRef, useEffect, forwardRef, useImperative<PERSON><PERSON>le, useLayoutEffect} from "react";
import {Space, Form, message, Divider, Button} from "antd";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {createColumnConfig, AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import Icon from "@ant-design/icons";
import {NetworkModal} from "../../modal/network_modal";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {VRFConfigurationForm} from "../../network_setting_form/vrf_configuration_form";
import {VNIConfigurationForm} from "../../network_setting_form/vni_configuration_form";
import {MLAGConfigurationForm} from "../../network_setting_form/mlag_configuration_form";
import {NACConfigurationForm} from "../../network_setting_form/nac_configuration_form";
import {DHCPRelayForm} from "../../network_setting_form/dhcp_relay_form";
import {OtherIpConfiguration} from "../../network_setting_form/other_ip_configuration";
import styles from "../../network_setting_form/form_style.module.scss";
import ip from "ip";

const tableStyle = {
    maxWidth: "45%",
    minWidth: "650px"
};

const CampusFabricIPClosStep3 = forwardRef(({nodes, networks, usedSubnets, setCampusFabricData, vir_tech}, ref) => {
    function to_frontend_structure(data) {
        return Object.keys(data).map(key => {
            return {
                name: key,
                vlan_id: data[key].vlan_id,
                subnet: data[key].subnet
            };
        });
    }
    function to_backend_structure(data) {
        return data.map(item => {
            return {
                [item.name]: {
                    vlan_id: item.vlan_id,
                    subnet: item.subnet
                }
            };
        });
    }

    const [networkData, setNetworkData] = useState(to_frontend_structure(networks.vlans));
    const [accessNodes, setAccessNodes] = useState(nodes.pods.flatMap(pod => pod.access));
    const [coreNodes, setCoreNodes] = useState(nodes.core);

    const mlagConfigurationFormRef = useRef(null);
    const OtherIpConfigurationRef = useRef(null);
    const vrfConfigurationFormRef = useRef(null);
    const vniConfigurationFormRef = useRef(null);
    const nacConfigurationFormRef = useRef(null);
    const dhcpRelayRef = useRef(null);

    const networkModalRef = useRef(null);

    useEffect(() => {
        const vlans = to_backend_structure(networkData);
        setCampusFabricData(prev => {
            return {
                ...prev,
                networks: {
                    ...prev.networks,
                    vlans: vlans.reduce((acc, cur) => {
                        return {...acc, ...cur};
                    }, {})
                }
            };
        });
    }, [networkData]);

    useEffect(() => {
        if (networkData.length === 0) {
            return;
        }
        const newAddedCoreNodes = nodes.core.filter(coreNode => {
            return coreNode.other_ip_config === undefined || coreNode.other_ip_config.length === 0;
        });
        const newAddedAccessNodes = nodes.pods.flatMap(pod => {
            return pod.access.filter(accessNode => {
                return accessNode.other_ip_config === undefined || accessNode.other_ip_config.length === 0;
            });
        });
        newAddedCoreNodes.forEach(coreNode => {
            coreNode.other_ip_config = networkData.map(item => {
                return {
                    vlan_name: item.name,
                    vlan_id: item.vlan_id,
                    ip_address: "",
                    anycast_ip_address: ip.cidrSubnet(item.subnet).lastAddress
                };
            });
        });
        newAddedAccessNodes.forEach(accessNode => {
            accessNode.other_ip_config = networkData.map(item => {
                return {
                    vlan_name: item.name,
                    vlan_id: item.vlan_id,
                    ip_address: "",
                    anycast_ip_address: ip.cidrSubnet(item.subnet).lastAddress
                };
            });
        });
        return () => {};
    }, []);

    useImperativeHandle(ref, () => ({
        validate: () => {
            if (networkData.length === 0) {
                message.error("Please create network first");
                Promise.reject(new Error("Please create network first"));
                return false;
            }
            if (vir_tech === "mlag") {
                const result = [
                    mlagConfigurationFormRef.current.validate(),
                    vrfConfigurationFormRef.current.validate(),
                    vniConfigurationFormRef.current.validate(),
                    nacConfigurationFormRef.current.validate(),
                    dhcpRelayRef.current.validate(),
                    checkIPAvailability(),
                    checkDuplicateSubnet()
                ].every(item => item === true);
                return result;
            }
            const result = [
                vrfConfigurationFormRef.current.validate(),
                vniConfigurationFormRef.current.validate(),
                nacConfigurationFormRef.current.validate(),
                checkIPAvailability(),
                checkDuplicateSubnet()
            ].every(item => item === true);
            return result;
        }
    }));

    function checkIPAvailability() {
        const requiredIPCounts = accessNodes.length + coreNodes.length;
        const insufficientIPVlans = networkData
            .filter(network => {
                const subnetInfo = ip.cidrSubnet(network.subnet);
                const availableIPs = subnetInfo.numHosts;
                return availableIPs < requiredIPCounts;
            })
            .map(network => network.name);

        if (insufficientIPVlans.length > 0) {
            message.error(`Insufficient IPs in vlans: ${insufficientIPVlans.join(", ")}`);
            return false;
        }
        return true;
    }

    function checkDuplicateSubnet() {
        const duplicateSubnets = networkData.filter(network => usedSubnets.includes(network.subnet));

        if (duplicateSubnets.length > 0) {
            message.error(`Duplicate subnets found: ${duplicateSubnets.map(network => network.subnet).join(", ")}`);
            return false;
        }
        return true;
    }

    const updateRelatedItems = networkName => {
        vrfConfigurationFormRef.current.removeRelatedNetwork(networkName);
        vniConfigurationFormRef.current.removeRelatedNetwork(networkName);
        dhcpRelayRef.current.removeRelatedNetwork(networkName);
        OtherIpConfigurationRef.current.deleteNetworkPostAction(networkName);
        setAccessNodes(
            accessNodes.map(node => {
                if (node.other_ip_config) {
                    node.other_ip_config = node.other_ip_config.filter(item => item.vlan_name !== networkName);
                }
                return node;
            })
        );
    };

    const columns = [
        {...createColumnConfig("Name", "name", "", "", "25%"), sorter: (a, b) => a.name.localeCompare(b.name)},
        {...createColumnConfig("VLAN ID", "vlan_id", "", "", "25%"), sorter: (a, b) => a.vlan_id - b.vlan_id},
        {...createColumnConfig("Subnet", "subnet", "", "", "25%"), sorter: (a, b) => a.subnet.localeCompare(b.subnet)},

        {
            title: "Operation",
            fixed: "right",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    networkModalRef.current.showNetworkModal({mode: "edit"}, record);
                                }}
                            >
                                Edit
                            </a>
                            <a
                                onClick={() => {
                                    confirmModalAction("Are you sure want to delete?", () => {
                                        try {
                                            setNetworkData(networkData.filter(item => item.name !== record.name));
                                            updateRelatedItems(record.name);

                                            message.success("Network deleted successfully");
                                        } catch (error) {
                                            message.error("An error occurred while deleting the network");
                                            console.error(error);
                                        }
                                    });
                                }}
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    return (
        <div className={styles.container}>
            <h2 style={{fontSize: "21px"}}>Configure Networks</h2>
            <h3>Network</h3>
            <Form.Item labelAlign="left" style={{marginBottom: "0px"}} label="Network" labelCol={{style: {width: 175}}}>
                <a
                    style={{
                        border: "none",
                        borderRadius: "4px",
                        color: "#14c9bb"
                    }}
                    onClick={() => {
                        if (networkData.length === 255) {
                            message.error("The maximum number of networks is 255");
                            return;
                        }
                        networkModalRef.current.showNetworkModal({mode: "create"});
                    }}
                >
                    <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                    Add
                </a>
            </Form.Item>
            <NetworkModal
                ref={networkModalRef}
                networkData={networkData}
                handleAddNetwork={data => {
                    setNetworkData([...networkData, data]);
                    OtherIpConfigurationRef.current.addNetworkPostAction(data.name, data.subnet, data.vlan_id);
                }}
                handleEditNetwork={data => {
                    setNetworkData(networkData.map(item => (item.name === data.name ? data : item)));
                    OtherIpConfigurationRef.current.editNetworkPostAction(data.name, data.subnet, data.vlan_id);
                }}
                requiredIPCounts={accessNodes.length + coreNodes.length}
                usedSubnets={usedSubnets}
            />
            <AmpConCustomTable
                dataSource={networkData}
                columns={columns}
                style={tableStyle}
                pagination={{
                    defaultPageSize: 10,
                    showSizeChanger: true,
                    hideOnSinglePage: networkData.length <= 10
                }}
            />
            <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
            <OtherIpConfiguration
                ref={OtherIpConfigurationRef}
                networkData={networkData}
                tableStyle={tableStyle}
                setCampusFabricData={setCampusFabricData}
                accessNodes={accessNodes}
                setAccessNodes={setAccessNodes}
                coreNodes={coreNodes}
                setCoreNodes={setCoreNodes}
            />
            {vir_tech === "mlag" && (
                <MLAGConfigurationForm
                    ref={mlagConfigurationFormRef}
                    networks={networks}
                    setCampusFabricData={setCampusFabricData}
                    usedSubnets={usedSubnets}
                />
            )}
            <VRFConfigurationForm
                type="ipclos"
                networkData={networkData}
                ref={vrfConfigurationFormRef}
                networks={networks}
                setCampusFabricData={setCampusFabricData}
                tableStyle={tableStyle}
            />

            <VNIConfigurationForm
                type="ipclos"
                networkData={networkData}
                ref={vniConfigurationFormRef}
                networks={networks}
                setCampusFabricData={setCampusFabricData}
                tableStyle={tableStyle}
            />

            <NACConfigurationForm
                ref={nacConfigurationFormRef}
                networkData={networkData}
                networks={networks}
                setCampusFabricData={setCampusFabricData}
                tableStyle={tableStyle}
            />
            {vir_tech === "mlag" && (
                <DHCPRelayForm
                    ref={dhcpRelayRef}
                    networkData={networkData}
                    networks={networks}
                    setCampusFabricData={setCampusFabricData}
                    tableStyle={tableStyle}
                />
            )}
        </div>
    );
});

export default CampusFabricIPClosStep3;
