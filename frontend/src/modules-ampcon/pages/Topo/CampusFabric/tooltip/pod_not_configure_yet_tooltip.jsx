import {Flex} from "antd";
import configureNotComplete from "@/modules-ampcon/pages/Topo/CampusFabric/resource/configure_not_complete.svg";

const PodNotConfigureYetTooltip = () => {
    return (
        <Flex style={{width: "50%", alignItems: "center", paddingLeft: "10px", marginBottom: "-12px"}}>
            <img src={configureNotComplete} alt="Configure Not Complete" width="18px" height="18px" />
            <div style={{paddingLeft: "5px", color: "#FF9F30"}}>Configuration not completed</div>
        </Flex>
    );
};

export default PodNotConfigureYetTooltip;
