import React, {forwardRef, useImperativeHandle, useState, useRef, useEffect} from "react";
import styles from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";
import CloseSvg from "../../Topology/resource/deviceInfo_close.svg?react";
import {<PERSON><PERSON>, <PERSON>vider, Flex} from "antd";
import {createColumnConfig, AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import RemoteShell from "../modal/remote_shell";

const MLAGDeviceDetailedInformation = forwardRef(({handleGraphUnhighlight, containerHeight}, ref) => {
    const [isDeviceDetailedInformationVisible, setDeviceDetailedInformationVisible] = useState(false);
    const [deviceInfo, setDeviceInfo] = useState(null);
    const [deviceInfoKeys, setDeviceInfoKeys] = useState([]);
    const [deviceInfoValues, setDeviceInfoValues] = useState([]);
    const tableRef = useRef(null);
    const switchRef = useRef(null);

    useImperativeHandle(ref, () => ({
        showDeviceDetailedInformation: deviceInfo => {
            setDeviceDetailedInformationVisible(true);
            setDeviceInfo(deviceInfo);
            setDeviceInfoKeys(Object.keys(deviceInfo));
            setDeviceInfoValues(Object.values(deviceInfo));
        },
        hideDeviceDetailedInformation: () => {
            setDeviceDetailedInformationVisible(false);
        }
    }));
    const handleCancel = () => {
        setDeviceDetailedInformationVisible(false);
        handleGraphUnhighlight();
    };
    const getStatusIcon = value => {
        value = value?.toString();
        return (
            <svg
                style={{
                    width: "8px",
                    height: "8px",
                    borderRadius: "50%",
                    backgroundColor: value?.toLowerCase() === "online" ? "#14C9BB" : "#F53F3F",
                    marginRight: "4px"
                }}
            />
        );
    };
    const columnsVlans = [
        {
            ...createColumnConfig("ID", "id", "", "", "20%"),
            sorter: false,
            render: (_, record) => <div>{record.id}</div>
        },
        {
            ...createColumnConfig("IP Address", "ipAddress", "", "", "20%"),
            sorter: false,
            render: (_, record) => <div>{record.ipAddress}</div>
        },
        {
            ...createColumnConfig("Name", "name", "", "", "20%"),
            sorter: false,
            render: (_, record) => <div>{record.name}</div>
        }
    ];
    const columnsCore = [
        {
            ...createColumnConfig("Switch", "switch", "", "", "20%"),
            sorter: false,
            render: (_, record) => <div>{record.switch}</div>
        },
        {
            ...createColumnConfig("Port ID", "portId", "", "", "20%"),
            sorter: false,
            render: (_, record) => <div>{record.portId}</div>
        }
    ];
    const columnsAccess = [
        {
            ...createColumnConfig("Switch", "switch", "", "", "20%"),
            sorter: false,
            render: (_, record) => <div>{record.switch}</div>
        },
        {
            ...createColumnConfig("Port ID", "portId", "", "", "20%"),
            sorter: false,
            render: (_, record) => <div>{record.portId}</div>
        }
    ];
    const columnsWan = [
        {
            ...createColumnConfig("Switch", "switch", "", "", "20%"),
            sorter: false,
            render: (_, record) => <div>{record.switch}</div>
        },
        {
            ...createColumnConfig("Port ID", "portId", "", "", "20%"),
            sorter: false,
            render: (_, record) => <div>{record.portId}</div>
        }
    ];

    return isDeviceDetailedInformationVisible ? (
        <div
            ref={ref}
            style={{
                position: "absolute",
                top: deviceInfo.type === "View" ? "215px" : "193px",
                right: deviceInfo.type === "View" ? "58px" : "68px",
                backgroundColor: "white",
                color: "black",
                zIndex: 1000,
                boxSizing: "border-box",
                display: "flex",
                flexDirection: "column",
                boxShadow: "0px 1px 12px 1px #E6E8EA",
                borderRadius: "4px",
                fontFamily: "Lato",
                width: "420px",
                minHeight: "0",
                maxHeight: deviceInfo.type === "View" ? containerHeight - 18 : containerHeight - 40
            }}
        >
            <div
                style={{
                    flexShrink: 0, // 确保头部不会因滚动被隐藏
                    position: "sticky",
                    top: 0,
                    backgroundColor: "white",
                    zIndex: 1001
                }}
            >
                <div
                    style={{
                        position: "absolute",
                        top: "12px",
                        right: "16px",
                        cursor: "pointer"
                    }}
                    onClick={handleCancel}
                >
                    <CloseSvg className={styles.closeIcon} />
                </div>
                <Flex style={{backgroundColor: "#F8FAFB ", width: "100%", height: "40px", top: 0}}>
                    <div
                        style={{
                            marginTop: "0",
                            borderRadius: "4px 4px 0px 0px",
                            paddingLeft: 16,
                            paddingTop: 11,
                            paddingBottom: 12,
                            margin: 0,
                            fontSize: 14,
                            color: "#212519",
                            fontWeight: 600,
                            width: "100%"
                        }}
                    >
                        {deviceInfo.name}
                    </div>
                </Flex>
            </div>
            <div
                style={{
                    flexGrow: 1, // 让内容区域撑满
                    overflowY: "auto", // 开启滚动
                    paddingTop: 15
                }}
            >
                <div style={{fontWeight: "bold", marginBottom: "8px", marginLeft: "16px"}}>Information </div>
                <Flex>
                    <Flex vertical style={{textAlign: "Left", color: "#929A9E ", paddingLeft: 16, fontWeight: 400}}>
                        {deviceInfoKeys.slice(3, 8).map((key, index) => (
                            <div style={{marginBottom: index === deviceInfoKeys.length - 4 ? 16 : 12}}>{key}</div>
                        ))}
                    </Flex>
                    <Flex vertical style={{marginLeft: 32}}>
                        {deviceInfoValues.slice(3, 8).map((value, index) => (
                            <div
                                style={{
                                    color: "#212519",
                                    fontWeight: "bold",
                                    marginBottom: index === deviceInfoValues.length - 4 ? 16 : 12
                                }}
                            >
                                {index === 2 && getStatusIcon(value)}
                                {value}
                            </div>
                        ))}
                    </Flex>
                </Flex>
                <Divider style={{marginTop: 8}} />
                <div style={{fontWeight: "bold", marginLeft: "16px"}}>VLANS</div>
                <AmpConCustomTable
                    ref={tableRef}
                    dataSource={deviceInfo.vlans}
                    columns={columnsVlans}
                    style={{width: 388, margin: "0 auto"}}
                />
                <Divider style={{marginTop: 16}} />
                <div style={{fontWeight: "bold", marginLeft: "16px"}}>Connections to Collapsed Core</div>
                <AmpConCustomTable
                    ref={tableRef}
                    dataSource={deviceInfo.connectionsToCollapsedCore}
                    columns={columnsCore}
                    style={{width: 388, margin: "0 auto"}}
                />
                {deviceInfo.nodeType === "core" && (
                    <>
                        <Divider style={{marginTop: 16}} />
                        <div style={{fontWeight: "bold", marginLeft: "16px"}}>Connections to Access</div>
                        <AmpConCustomTable
                            ref={tableRef}
                            dataSource={deviceInfo.connectionsToAccess}
                            columns={columnsAccess}
                            style={{width: 388, margin: "0 auto"}}
                        />
                    </>
                )}
                {deviceInfo.connectionsToWan && deviceInfo.connectionsToWan.length > 0 && (
                    <>
                        <Divider style={{marginTop: 16}} />
                        <div style={{fontWeight: "bold", marginLeft: "16px"}}>Connections to WAN</div>
                        <AmpConCustomTable
                            ref={tableRef}
                            dataSource={deviceInfo.connectionsToWan}
                            columns={columnsWan}
                            style={{width: 388, margin: "0 auto"}}
                        />
                    </>
                )}
                <Divider style={{marginTop: 16, marginBottom: 1}} />
                <div
                    style={{
                        position: "sticky",
                        bottom: 0,
                        backgroundColor: "white",
                        zIndex: 1001,
                        padding: "16px 0"
                    }}
                >
                    <Flex style={{justifyContent: "space-around"}}>
                        <RemoteShell tableRef={switchRef} mgtIp={deviceInfo.mgtIp} status={deviceInfo.Status} />
                        <Button
                            style={{width: 160}}
                            disabled={deviceInfo.Status !== "online"}
                            onClick={() => {
                                const fullUrl = `${window.location.origin}/device/switches/${deviceInfo.sn}`;
                                window.open(fullUrl, "_blank", "noopener,noreferrer");
                            }}
                        >
                            Switch Insights
                        </Button>
                    </Flex>
                </div>
            </div>
        </div>
    ) : null;
});

export default MLAGDeviceDetailedInformation;
