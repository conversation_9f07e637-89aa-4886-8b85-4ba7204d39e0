import {<PERSON>, Flex, Radio, Select, Button, message} from "antd";
import {forwardRef, useImperativeHandle, useRef, useState} from "react";

const BorderTabSelectedPortCard = forwardRef(
    (
        {
            borderTabUpdateCorePortStatusCallback,
            borderTabRemoveCorePortStatusCallback,
            borderTabUpdateWANPortStatusCallback,
            borderTabRemoveWANPortStatusCallback,
            getCoreSwitchSNOptionsForBorder,
            isMenuCollapsed
        },
        ref
    ) => {
        const selectedPortCardRef = useRef(null);

        const [isShowPortCard, setIsShowPortCard] = useState(false);
        const [displayPos, setDisplayPos] = useState([0, 0]);
        const [displayPortName, setDisplayPortName] = useState("");
        const [selectedPortType, setSelectedPortType] = useState("ge");
        const [selectedSN, setSelectedSN] = useState("");
        const [selectedCoreSwitchSN, setSelectedCoreSwitchSN] = useState("");

        const [isPortSelectedWithCore, setIsPortSelectedWithCore] = useState(false);
        const [isPortSelectedWithWAN, setIsPortSelectedWithWAN] = useState(false);

        const [isSelectedCoreValid, setIsSelectedCoreValid] = useState(true);
        const [currentCoreSwitchSNOptions, setCurrentCoreSwitchSNOptions] = useState([]);

        useImperativeHandle(ref, () => ({
            showSelectedPortCard: (record, portName, portPosX, portPosY) => {
                setSelectedSN(record.sn);
                setDisplayPortName(portName);
                setDisplayPos([portPosX, portPosY]);
                setCurrentCoreSwitchSNOptions(getCoreSwitchSNOptionsForBorder(record.sn));
                setIsSelectedCoreValid(true);
                setIsShowPortCard(true);
                updateCurrentPortStatus(record, portName);
            },
            hideSelectedPortCard: () => {
                setIsShowPortCard(false);
            },
            isSelectedPortCardShown: () => {
                return isShowPortCard;
            },
            isMouseClickOnPortCard: e => {
                const selectedPortCardRect = selectedPortCardRef.current.getBoundingClientRect();
                return (
                    selectedPortCardRect.x <= e.clientX &&
                    e.clientX <= selectedPortCardRect.x + selectedPortCardRect.width &&
                    selectedPortCardRect.y <= e.clientY &&
                    e.clientY <= selectedPortCardRect.y + selectedPortCardRect.height
                );
            }
        }));

        const updateCurrentPortStatus = (record, portName) => {
            if (record.currentLinkToCore && Object.keys(record.currentLinkToCore).includes(portName)) {
                setIsPortSelectedWithCore(true);
                setIsPortSelectedWithWAN(false);
                setSelectedPortType(record.currentLinkToCore[portName].port_type);
                setSelectedCoreSwitchSN(record.currentLinkToCore[portName].core_sn);
            } else if (record.currentLinkToWAN && Object.keys(record.currentLinkToWAN).includes(portName)) {
                setIsPortSelectedWithWAN(true);
                setIsPortSelectedWithCore(false);
                setSelectedPortType(record.currentLinkToWAN[portName].port_type);
                setSelectedCoreSwitchSN(record.currentLinkToWAN[portName].wan_sn);
            } else {
                setIsPortSelectedWithCore(false);
                setIsPortSelectedWithWAN(false);
                const portTypeName = portName.substring(0, 2).toLowerCase();
                setSelectedPortType(["ge", "te", "xe"].includes(portTypeName) ? portTypeName : "ge");
                const enabledSNOptions = getCoreSwitchSNOptionsForBorder(record.sn).filter(item => {
                    return !item.disabled;
                });

                setSelectedCoreSwitchSN(enabledSNOptions.length > 0 ? enabledSNOptions[0].value : "");
            }
        };

        const calculateCardOffsetTop = () => {
            return displayPos[1] + 90 + 270 > window.innerHeight ? displayPos[1] - 90 - 310 : displayPos[1] - 90;
        };

        const calculateCardOffsetLeft = () => {
            const offsetLeft = isMenuCollapsed ? displayPos[0] - 110 : displayPos[0] - 290;
            if (offsetLeft + 600 > window.innerWidth) {
                return offsetLeft - 290 + 5;
            }
            return offsetLeft;
        };

        const getCoreLinkConnectionComponent = () => {
            if (isPortSelectedWithCore) {
                return (
                    <Button
                        id="removeLinkToCore"
                        onClick={() => {
                            borderTabRemoveCorePortStatusCallback(selectedSN, displayPortName);
                        }}
                    >
                        Remove Link to Core
                    </Button>
                );
            }
            if (isPortSelectedWithWAN) {
                return null;
            }
            return (
                <Button
                    id="linkToCore"
                    onClick={() => {
                        if (!selectedCoreSwitchSN) {
                            setIsSelectedCoreValid(false);
                            message.error("Please select a core switch.");
                            return;
                        }
                        borderTabUpdateCorePortStatusCallback(
                            selectedSN,
                            displayPortName,
                            selectedPortType,
                            selectedCoreSwitchSN
                        );
                    }}
                >
                    Link to Core
                </Button>
            );
        };

        const getWANLinkConnectionComponent = () => {
            if (isPortSelectedWithWAN) {
                return (
                    <Button
                        id="removeLinkToWAN"
                        onClick={() => {
                            borderTabRemoveWANPortStatusCallback(selectedSN, displayPortName);
                        }}
                    >
                        Remove Link to WAN
                    </Button>
                );
            }
            if (isPortSelectedWithCore) {
                return null;
            }
            return (
                <Button
                    id="linkToWAN"
                    onClick={() => {
                        borderTabUpdateWANPortStatusCallback(selectedSN, displayPortName, selectedPortType);
                    }}
                >
                    Link to WAN
                </Button>
            );
        };

        return isShowPortCard ? (
            <div
                style={{
                    position: "absolute",
                    top: `${calculateCardOffsetTop()}px`,
                    left: `${calculateCardOffsetLeft()}px`,
                    transition: "background-color 0.3s ease",
                    width: "275px",
                    zIndex: 1000
                }}
            >
                <Card ref={selectedPortCardRef} title={displayPortName} bordered={false}>
                    <Flex vertical>
                        <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Port Type</Flex>
                        <Flex style={{paddingBottom: "12px"}}>
                            <Radio.Group
                                onChange={e => {
                                    setSelectedPortType(e.target.value);
                                }}
                                value={selectedPortType}
                                // disabled={isPortSelectedWithCore}
                                disabled
                                options={[
                                    {value: "ge", label: "ge"},
                                    {value: "te", label: "te"},
                                    {value: "xe", label: "xe"}
                                ]}
                            />
                        </Flex>
                        {/* <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Core Switch SN</Flex>
                        <Flex style={{paddingBottom: "12px"}}>
                            <Select
                                style={{width: "100%"}}
                                value={selectedCoreSwitchSN}
                                onChange={key => {
                                    setSelectedCoreSwitchSN(key);
                                }}
                                options={currentCoreSwitchSNOptions}
                                disabled={isPortSelectedWithCore}
                                status={isSelectedCoreValid ? "" : "error"}
                            />
                        </Flex> */}
                        <Flex style={{paddingBottom: "12px"}}>{getCoreLinkConnectionComponent()}</Flex>
                        <Flex style={{paddingBottom: "12px"}}>{getWANLinkConnectionComponent()}</Flex>
                    </Flex>
                </Card>
            </div>
        ) : null;
    }
);

export default BorderTabSelectedPortCard;
