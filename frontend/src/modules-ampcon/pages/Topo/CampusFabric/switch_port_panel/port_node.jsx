import {Flex} from "antd";
import {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import {
    portGECommonSvg,
    portGEHoverSvg,
    portGESelectedToCoreSvg,
    portGESelectedToAccessSvg,
    portGESelectedToWANSvg,
    portGESelectedToBorderSvg,
    portGESelectedToDisSvg,
    portTECommonSvg,
    portTEHoverSvg,
    portTESelectedToCoreSvg,
    portTESelectedToAccessSvg,
    portTESelectedToWANSvg,
    portTESelectedToBorderSvg,
    portTESelectedToDisSvg,
    portXECommonSvg,
    portXEHoverSvg,
    portXESelectedToCoreSvg,
    portXESelectedToAccessSvg,
    portXESelectedToWANSvg,
    portXESelectedToBorderSvg,
    portXESelectedToDisSvg
} from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/port_svg";
import Icon from "@ant-design/icons";

const PortNode = forwardRef(
    (
        {
            index,
            portName,
            portType,
            status,
            isSvgReverse,
            handleMouseEnter,
            handleMouseLeave,
            selectedPortCardFunc,
            record
        },
        ref
    ) => {
        const iconRef = useRef(null);

        const [displayIndex, setDisplayIndex] = useState(index);
        const [displayPortName, setDisplayPortName] = useState(portName);
        const [displayStatus, setDisplayStatus] = useState(status);
        const [isSelected, setIsSelected] = useState(false);

        useImperativeHandle(ref, () => ({
            updateNodeStatus: newStatus => {
                setIsSelected(false);
                setDisplayStatus(newStatus);
            },
            clearSelectedStatus: () => {
                setIsSelected(false);
            }
        }));

        useEffect(() => {
            if (
                displayStatus === "selectedToCore" ||
                displayStatus === "selectedToAccess" ||
                displayStatus === "selectedToWAN" ||
                displayStatus === "selectedToBorder" ||
                displayStatus === "selectedToDis"
            ) {
                if (status === "hover" || status === "common" || isSelected) {
                    //
                } else {
                    setDisplayStatus(status);
                }
            } else {
                setDisplayStatus(status);
            }
        }, [status]);

        const getPortSvg = () => {
            if (displayStatus === "selectedToCore") {
                if (portType.toUpperCase() === "GE") {
                    return portGESelectedToCoreSvg;
                }
                if (portType.toUpperCase() === "TE") {
                    return portTESelectedToCoreSvg;
                }
                if (portType.toUpperCase() === "XE") {
                    return portXESelectedToCoreSvg;
                }
            } else if (displayStatus === "selectedToAccess") {
                if (portType.toUpperCase() === "GE") {
                    return portGESelectedToAccessSvg;
                }
                if (portType.toUpperCase() === "TE") {
                    return portTESelectedToAccessSvg;
                }
                if (portType.toUpperCase() === "XE") {
                    return portXESelectedToAccessSvg;
                }
            } else if (displayStatus === "selectedToWAN") {
                if (portType.toUpperCase() === "GE") {
                    return portGESelectedToWANSvg;
                }
                if (portType.toUpperCase() === "TE") {
                    return portTESelectedToWANSvg;
                }
                if (portType.toUpperCase() === "XE") {
                    return portXESelectedToWANSvg;
                }
            } else if (displayStatus === "selectedToDis") {
                if (portType.toUpperCase() === "GE") {
                    return portGESelectedToDisSvg;
                }
                if (portType.toUpperCase() === "TE") {
                    return portTESelectedToDisSvg;
                }
                if (portType.toUpperCase() === "XE") {
                    return portXESelectedToDisSvg;
                }
            } else if (displayStatus === "selectedToBorder") {
                if (portType.toUpperCase() === "GE") {
                    return portGESelectedToBorderSvg;
                }
                if (portType.toUpperCase() === "TE") {
                    return portTESelectedToBorderSvg;
                }
                if (portType.toUpperCase() === "XE") {
                    return portXESelectedToBorderSvg;
                }
            } else if (displayStatus === "hover" || isSelected) {
                if (portType.toUpperCase() === "GE") {
                    return portGEHoverSvg;
                }
                if (portType.toUpperCase() === "TE") {
                    return portTEHoverSvg;
                }
                if (portType.toUpperCase() === "XE") {
                    return portXEHoverSvg;
                }
            } else {
                if (portType.toUpperCase() === "GE") {
                    return portGECommonSvg;
                }
                if (portType.toUpperCase() === "TE") {
                    return portTECommonSvg;
                }
                if (portType.toUpperCase() === "XE") {
                    return portXECommonSvg;
                }
            }
        };

        const iconClickCallback = () => {
            setTimeout(() => {
                const iconRect = iconRef.current.getBoundingClientRect();
                setIsSelected(true);
                selectedPortCardFunc.showSelectedPortCard(
                    record,
                    displayPortName,
                    iconRect.x + iconRect.width - 5,
                    iconRect.y + iconRect.height - 5
                );
            }, 100);
        };

        return isSvgReverse ? (
            <Flex vertical>
                <Flex style={{justifyContent: "center"}}>
                    <Icon
                        ref={iconRef}
                        onMouseEnter={handleMouseEnter}
                        onMouseLeave={handleMouseLeave}
                        component={getPortSvg()}
                        style={portType.toUpperCase() === "TE" ? {} : {transform: "rotate(180deg)"}}
                        onClick={iconClickCallback}
                    />
                </Flex>
                <Flex style={{justifyContent: "center", fontSize: "10px"}}>{displayIndex}</Flex>
            </Flex>
        ) : (
            <Flex vertical>
                <Flex style={{justifyContent: "center", fontSize: "10px"}}>{displayIndex}</Flex>
                <Flex style={{justifyContent: "center"}}>
                    <Icon
                        ref={iconRef}
                        onMouseEnter={handleMouseEnter}
                        onMouseLeave={handleMouseLeave}
                        component={getPortSvg()}
                        style={portType.toUpperCase() === "TE" ? {transform: "rotate(180deg)"} : {}}
                        onClick={iconClickCallback}
                    />
                </Flex>
            </Flex>
        );
    }
);

export default PortNode;
