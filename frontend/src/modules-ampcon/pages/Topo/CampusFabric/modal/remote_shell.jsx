import {useState} from "react";
import {AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {useForm} from "antd/es/form/Form";
import {Form, Input, Button} from "antd";
import {EyeOutlined, EyeInvisibleOutlined} from "@ant-design/icons";

const RemoteShell = ({mgtIp, status}) => {
    const [sshModal, setSSHModal] = useState(false);
    const [sshForm] = useForm();

    const sshSubmit = values => {
        const url = `/ssh/?${btoa(`${mgtIp};22;${values.username};${values.password}`)}`;
        window.open(url, "_blank", "noopener,noreferrer");
        setSSHModal(false);
        sshForm.resetFields();
    };

    const sshFormRender = () => {
        return (
            <>
                <Form.Item
                    name="username"
                    label="Username"
                    rules={[{required: true, message: "Please input username."}]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="password"
                    label="Password"
                    rules={[{required: true, message: "Please input password."}]}
                >
                    <Input.Password
                        iconRender={visible => {
                            return visible ? (
                                <EyeOutlined style={{color: "#c5c5c5"}} />
                            ) : (
                                <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                            );
                        }}
                        style={{width: "280px"}}
                    />
                </Form.Item>
            </>
        );
    };

    return (
        <>
            <Button
                disabled={status !== "online"}
                onClick={() => {
                    setSSHModal(true);
                }}
                style={{width: 160}}
            >
                Remote Shell
            </Button>

            <AmpConCustomModalForm
                title="Remote Shell"
                isModalOpen={sshModal}
                formInstance={sshForm}
                layoutProps={{
                    labelCol: {
                        span: 6
                    }
                }}
                CustomFormItems={sshFormRender}
                onCancel={() => {
                    sshForm.resetFields();
                    setSSHModal(false);
                }}
                onSubmit={sshSubmit}
                modalClass="ampcon-middle-modal"
            />
        </>
    );
};

export default RemoteShell;
