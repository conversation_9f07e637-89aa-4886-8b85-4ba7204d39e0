import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Flex, message} from "antd";
import {forwardRef, useImperativeHandle, useRef, useState, useEffect} from "react";
import {
    AmpConCustomModalTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown,
    AmpConCustomModalTABTable,
    AmpConCustomTable
} from "@/modules-ampcon/components/custom_table";
import {fetchIPclosSiteTableData} from "@/modules-ampcon/apis/lifecycle_api";
import {objectGet} from "@/modules-otn/apis/api";

async function getNeInfo(otnlist) {
    const filter = {type: 5};
    const response = await objectGet("config:ne", filter);

    const {apiResult, apiMessage, documents, total} = response;
    if (apiResult === "fail") {
        message.error(apiMessage).then();
        return;
    }
    const _list = documents.map(item => {
        return {
            id: item.value.name,
            label: item.value.name,
            value: item.value.ne_id,
            type: item.value.type,
            disabled: item.value.type === "2" && item.value.runState === 0,
            selected: otnlist.includes(item.value.name)
        };
    });
    _list.sort((a, b) => {
        if (a.selected && !b.selected) {
            return -1;
        }
        if (!a.selected && b.selected) {
            return 1;
        }
        return a.label.localeCompare(b.label, "zh-CN", {numeric: true});
    });
    return {
        data: _list,
        total
    };
}

const AddDeviceModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showAddDeviceModal: (snList, otnList, siteID, type, coreNodesCount, index) => {
            setSwitchSNList(snList);
            setOTNList(otnList);
            setSiteID(siteID);
            SetLayerType(type);
            setCoreNodesCount(coreNodesCount);
            setIsShowModal(true);
            setIndex(index);
        },
        hideAddDeviceModal: () => {
            setIsShowModal(false);
        }
    }));

    const title = "Add Device";
    const {updateNodesCallback} = props;

    const matchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "mac_addr", matchMode: "fuzzy"}
    ];
    const searchFieldsList = ["host_name", "sn", "platform_model", "mac_addr"];

    const tableModalRef = useRef(null);

    const switchTableModalRef = useRef(null);
    const otnTableModalRef = useRef(null);

    const [isShowModal, setIsShowModal] = useState(false);
    const [switchSNList, setSwitchSNList] = useState([]);
    const [otnList, setOTNList] = useState([]);
    const [layerType, SetLayerType] = useState();
    const [siteID, setSiteID] = useState();
    const [disabledRows, setDisabledRows] = useState(new Set());
    const [coreNodesCount, setCoreNodesCount] = useState();
    const [index, setIndex] = useState();

    const rowSelection = {
        selectedRowKeys: [],
        selectedRows: [],
        onChange: (selectedRowKeys, selectedRows) => {
            const enabledRows = selectedRows.filter(row => !row.selected);
            rowSelection.selectedRowKeys = selectedRowKeys;
            rowSelection.selectedRows = [...new Set([...rowSelection.selectedRows, ...enabledRows])];
        },
        getCheckboxProps: record => ({
            disabled: disabledRows.has(record.sn) && record.selected
        })
    };

    const validateSelection = () => {
        const matchedCount = rowSelection.selectedRows.filter(row =>
            rowSelection.selectedRowKeys.includes(row.id)
        ).length;
        if (layerType === "core") {
            const selectedCount = matchedCount + coreNodesCount;
            if (selectedCount !== 2) {
                message.error("Please add two core switches.");
                return false;
            }
        }
        return true;
    };

    useEffect(() => {
        setDisabledRows(prevDisabledRows => {
            const updatedDisabledRows = new Set(prevDisabledRows);
            [...switchSNList].forEach(sn => updatedDisabledRows.add(sn));
            return updatedDisabledRows;
        });
    }, [switchSNList]);

    const switchColumns = [
        createColumnConfigMultipleParams({
            title: "Host Name",
            dataIndex: "host_name",
            filterDropdownComponent: TableFilterDropdown,
            width: "25%"
        }),
        createColumnConfigMultipleParams({
            title: "MAC Address",
            dataIndex: "mac_addr",
            filterDropdownComponent: TableFilterDropdown,
            width: "25%"
        }),
        createColumnConfigMultipleParams({
            title: "SN / Service Tag",
            dataIndex: "sn",
            filterDropdownComponent: TableFilterDropdown,
            width: "25%"
        }),
        createColumnConfigMultipleParams({
            title: "Model",
            dataIndex: "platform_model",
            filterDropdownComponent: TableFilterDropdown,
            width: "25%"
        })
    ];

    const otnColumns = [
        createColumnConfigMultipleParams({
            title: "Name",
            dataIndex: "label",
            filterDropdownComponent: TableFilterDropdown
            // width: "15%"
        }),
        createColumnConfigMultipleParams({
            title: "Ip",
            dataIndex: "value",
            filterDropdownComponent: TableFilterDropdown
            // width: "15%"
        })
    ];

    const tabItems = [
        {
            key: "switch_table",
            label: "Switch Table",
            children: (
                <AmpConCustomTable
                    ref={switchTableModalRef}
                    columns={switchColumns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    fetchAPIInfo={fetchIPclosSiteTableData}
                    fetchAPIParams={[switchSNList, siteID, layerType]}
                    rowSelection={{rowSelection}}
                />
            )
        },
        {
            key: "otn_table",
            label: "OTN Table",
            children: (
                <AmpConCustomTable
                    ref={otnTableModalRef}
                    columns={otnColumns}
                    fetchAPIInfo={getNeInfo}
                    fetchAPIParams={[otnList]}
                    rowSelection={{rowSelection}}
                    isShowPagination
                />
            )
        }
    ];

    return import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-SUPER" ? (
        <AmpConCustomModalTABTable
            title={title}
            selectModalOpen={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
            }}
            items={tabItems}
            modalClass="ampcon-max-modal"
            footer={
                <Flex vertical>
                    <Divider />
                    <Flex justify="flex-end">
                        <Button
                            htmlType="button"
                            onClick={() => {
                                setIsShowModal(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                if (validateSelection()) {
                                    setIsShowModal(false);
                                    updateNodesCallback(
                                        switchTableModalRef.current.getOperations(),
                                        otnTableModalRef.current ? otnTableModalRef.current.getOperations() : []
                                    );
                                }
                            }}
                        >
                            Save
                        </Button>
                    </Flex>
                </Flex>
            }
        />
    ) : (
        <AmpConCustomModalTable
            modalClass="ampcon-max-modal"
            ref={tableModalRef}
            title={title}
            selectModalOpen={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
            }}
            rowSelection={rowSelection}
            columns={switchColumns}
            matchFieldsList={matchFieldsList}
            searchFieldsList={searchFieldsList}
            buttonProps={[]}
            fetchAPIInfo={fetchIPclosSiteTableData}
            fetchAPIParams={[switchSNList, siteID, layerType]}
            footer={
                <Flex vertical>
                    <Divider />
                    <Flex justify="flex-end">
                        <Button
                            htmlType="button"
                            onClick={() => {
                                setIsShowModal(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                if (validateSelection()) {
                                    updateNodesCallback(
                                        tableModalRef.current.getTableRef().current.getOperations(),
                                        layerType,
                                        index,
                                        []
                                    );
                                }
                            }}
                        >
                            Save
                        </Button>
                    </Flex>
                </Flex>
            }
        />
    );
});

export default AddDeviceModal;
