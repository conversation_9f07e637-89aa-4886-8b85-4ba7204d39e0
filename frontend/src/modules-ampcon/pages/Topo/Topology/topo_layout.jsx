import React, {forwardRef, useEffect, useImper<PERSON><PERSON><PERSON><PERSON>, useRef, useState} from "react";
import {message, Spin} from "antd";
import {register} from "@antv/x6-react-shape";
import DeviceNode from "@/modules-ampcon/pages/Topo/Topology/device_node";
import {Graph} from "@antv/x6";
import {Selection} from "@antv/x6-plugin-selection";
import DeviceBriefTooltip from "@/modules-ampcon/pages/Topo/Topology/tooltip/device_brief_tooltip";
import LinkBriefTooltip from "@/modules-ampcon/pages/Topo/Topology/tooltip/link_brief_tooltip";
import DeviceDetailedBottomInformation from "@/modules-ampcon/pages/Topo/Topology/device_detailed_bottom_information";
import DeviceRightClickPopUpMenu from "@/modules-ampcon/pages/Topo/Topology/menu/device_right_click_pop_up_menu";
import LinkRightClickPopUpMenu from "@/modules-ampcon/pages/Topo/Topology/menu/link_right_click_pop_up_menu";
import BlankRightClickPopUpMenu from "@/modules-ampcon/pages/Topo/Topology/menu/blank_right_click_pop_up_menu";
import DeviceDetailedInformation from "@/modules-ampcon/pages/Topo/Topology/device_detailed_information";
import {
    autoDiscoverTopology,
    fetchTopologyToBeAddedSwitch,
    getTopology,
    saveTopologyDetail
} from "@/modules-ampcon/apis/monitor_api";
import LinkDetailedBottomInformation from "@/modules-ampcon/pages/Topo/Topology/link_detailed_bottom_information";
import TopoLeftFloatMenu from "@/modules-ampcon/pages/Topo/Topology/menu/topo_left_float_menu";
import FabricLeftFloatMenu from "@/modules-ampcon/pages/Topo/Topology/menu/fabric_left_float_menu";
import SiteLeftFloatMenu from "@/modules-ampcon/pages/Topo/Topology/menu/site_left_float_menu";
import {Export} from "@antv/x6-plugin-export";
import {getSwitchDetails} from "@/modules-ampcon/apis/inventory_api";
import {MiniMap} from "@antv/x6-plugin-minimap";
import {Snapline} from "@antv/x6-plugin-snapline";
import {History} from "@antv/x6-plugin-history";
import {Keyboard} from "@antv/x6-plugin-keyboard";
import AddDeviceModal from "@/modules-ampcon/pages/Topo/Topology/modal/add_device_modal";
import EditDeviceModal from "@/modules-ampcon/pages/Topo/Topology/modal/edit_device_modal";
import EditLinkModal from "@/modules-ampcon/pages/Topo/Topology/modal/edit_link_modal";
import TopoLegend from "@/modules-ampcon/pages/Topo/Topology/topo_legend";
import {
    calculateVertices,
    circularLayoutDataTraverse,
    convertToUTCString,
    ellipticalLayoutDataTraverse,
    girdLayoutDataTraverse,
    hierarchyLayoutDataTraverse
} from "@/utils/topo_layout_utils";
import TimeSlider from "@/modules-ampcon/pages/Topo/Topology/time_slider";
import AimHistoryTimeSliderModal from "@/modules-ampcon/pages/Topo/Topology/modal/aim_history_time_slider_modal";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

let intervalId = null;
let currentTopoIdVar = null;
let isShowBessel = false;
let isNodeMovable = false;
let isShowGrid = false;
let timeout;
let historyModelStack = [];
let historyRedoModelStack = [];

const TopoLayout = forwardRef((props, ref) => {
    let nodes = null;
    let edges = null;
    let selection = new Selection({
        enabled: false,
        multiple: true,
        rubberband: true,
        movable: true,
        showNodeSelectionBox: true,
        strict: true
    });
    let snapLine = new Snapline({
        enabled: false
    });
    let history = new History({
        enabled: false
    });
    let keyboard = new Keyboard({
        enabled: false,
        global: true
    });

    const containerRef = useRef(null);
    const graphRef = useRef(null);
    const deviceBriefTooltipRef = useRef(null);
    const linkBriefTooltipRef = useRef(null);
    const deviceDetailedBottomInformationRef = useRef(null);
    const linkDetailedBottomInformationRef = useRef(null);
    const deviceRightClickPopUpMenuRef = useRef(null);
    const linkRightClickPopUpMenuRef = useRef(null);
    const blankRightClickPopUpMenuRef = useRef(null);
    const deviceDetailedInformationRef = useRef(null);
    const topoLeftFloatMenuRef = useRef(null);
    const refMiniMapContainer = useRef(null);
    const addDeviceModalRef = useRef(null);
    const editDeviceModalRef = useRef(null);
    const aimHistoryTimeSliderModalRef = useRef(null);
    const editLinkModelRef = useRef(null);
    const topoLegendRef = useRef(null);
    const timeSliderRef = useRef(null);

    useImperativeHandle(ref, () => ({
        renderTopoLayout,
        cleanTopoLayout,
        getIsEditMode: () => isEditMode
    }));

    const [isShowSpin, setIsShowSpin] = useState(false);

    const [isTopoLayoutRendered, setIsTopoLayoutRendered] = useState(false);
    const [isTopoLeftFloatMenuRender, setIsTopoLeftFloatMenuRender] = useState(false);
    const [currentTopoId, setCurrentTopoId] = useState(null);
    const [currentTopoTitle, setCurrentTopoTitle] = useState(null);
    const [currentTopoType, setCurrentTopoType] = useState(null);

    const [isEditMode, setIsEditMode] = useState(false);
    const [isHistoryMode, setIsHistoryMode] = useState(false);
    const [canUndo, setCanUndo] = useState(false);
    const [canRedo, setCanRedo] = useState(false);
    const [canDelete, setCanDelete] = useState(false);
    const [isTopoLegend, setIsTopoLegend] = useState(false);
    const [canTreeLayout, setCanTreeLayout] = useState(false);
    const [addCount, setAddCount] = useState(0);
    const [randomYOffset, setRandomYOffset] = useState(Math.floor(Math.random() * 41) - 20);
    const [currentDisplayedNode, setCurrentDisplayedNode] = useState(null);
    const isHistoryModeRef = useRef(isHistoryMode);
    const [model, setModel] = useState(null);
    const currentSelectedNode = useRef(null);

    useEffect(() => {
        if (isEditMode && intervalId !== null) {
            clearInterval(intervalId);
            intervalId = null;
        } else if (!isEditMode) {
            if (intervalId !== null) {
                clearInterval(intervalId);
                intervalId = null;
            }
            intervalId = setInterval(intervalUpdateNodesAndEdgesState, 15000);
        }
        clearAllMenus();
        setIsHistoryMode(false);
        isNodeMovable = isEditMode;
        isShowGrid = isEditMode;
        selection = new Selection({
            enabled: isEditMode,
            multiple: true,
            rubberband: true,
            movable: true,
            showNodeSelectionBox: true
        });
        snapLine = new Snapline({
            enabled: isEditMode
        });
        history = new History({
            enabled: isEditMode
        });
        keyboard = new Keyboard({enabled: isEditMode, global: true});

        removeAllMiniMap();
        updateTopoOptions(isEditMode);
        clearHistory(200);

        return () => {
            if (intervalId !== null) {
                clearInterval(intervalId);
                intervalId = null;
            }
        };
    }, [isEditMode]);

    useEffect(() => {
        isHistoryModeRef.current = isHistoryMode;
        if (isHistoryMode) {
            const now = new Date();
            const twoHoursAgo = new Date(now.getTime() - 60 * 60 * 1000);
            timeSliderRef.current.showTimeSlider(twoHoursAgo, now);
            clearInterval(intervalId);
            intervalId = null;
        } else {
            timeSliderRef.current.hideTimeSlider();
            if (intervalId !== null) {
                clearInterval(intervalId);
                intervalId = null;
            }
            intervalId = setInterval(intervalUpdateNodesAndEdgesState, 15000);
        }
        clearAllMenus();

        return () => {
            if (intervalId !== null) {
                clearInterval(intervalId);
                intervalId = null;
            }
        };
    }, [isHistoryMode]);

    const clearHistory = (delay = null) => {
        if (isEditMode) {
            const timeout = delay !== null ? delay : 100;
            setTimeout(() => {
                historyModelStack = [];
                historyRedoModelStack = [];
                pushCurrentTopoToHistoryStack();
                graphRef.current.cleanHistory();
                setCanUndo(false);
                setCanRedo(false);
            }, timeout);
        }
    };

    const registerCustomNode = (isEdit = null) => {
        register({
            shape: "switch-node",
            height: 54,
            width: 34,
            // eslint-disable-next-line react/no-unstable-nested-components
            component: props => (
                <DeviceNode
                    {...props}
                    graph={graphRef.current}
                    isEditMode={isEdit === null ? isEditMode : isEdit}
                    isInTreeMode={isShowBessel}
                />
            )
        });
        register({
            shape: "otn-node",
            height: 54,
            width: 34,
            // eslint-disable-next-line react/no-unstable-nested-components
            component: props => (
                <DeviceNode
                    {...props}
                    graph={graphRef.current}
                    isEditMode={isEdit === null ? isEditMode : isEdit}
                    isInTreeMode={isShowBessel}
                />
            )
        });
    };

    const pushCurrentTopoToHistoryStack = (isClearRedoStack = true) => {
        if (isClearRedoStack) {
            if (historyRedoModelStack.length > 0) {
                historyModelStack.push(historyRedoModelStack.pop());
            }
            historyRedoModelStack = [];
        }
        const updatedModel = graphRef.current.toJSON();
        historyModelStack.push({
            isShowBessel,
            nodes: updatedModel.cells.filter(cell => cell.shape !== "edge"),
            edges: updatedModel.cells.filter(cell => cell.shape === "edge")
        });
        updateHistoryButtonState();
    };

    const removeAllMiniMap = () => {
        graphRef.current?.getPlugin("minimap")?.dispose();
        const minimapElements = document.getElementsByClassName("x6-widget-minimap");
        if (minimapElements.length > 1) {
            for (let i = 0; i < minimapElements.length - 1; i++) {
                minimapElements[i].remove();
            }
        }
    };

    const clearAllPopUpMenus = () => {
        deviceRightClickPopUpMenuRef.current.hideDeviceRightClickPopUpMenu();
        linkRightClickPopUpMenuRef.current.hideLinkRightClickPopUpMenu();
        if (blankRightClickPopUpMenuRef.current) {
            blankRightClickPopUpMenuRef.current.hideBlankRightClickPopUpMenu();
        }
    };

    const clearAllTooltips = () => {
        deviceBriefTooltipRef.current.hideDeviceBriefTooltip();
        linkBriefTooltipRef.current.hideLinkBriefTooltip();
    };

    const clearAllInfos = () => {
        deviceDetailedBottomInformationRef.current.hideDeviceDetailedBottomInformation();
        linkDetailedBottomInformationRef.current.hideLinkDetailedBottomInformation();
        deviceDetailedInformationRef.current.hideDeviceDetailedInformation();
    };

    const clearAllMenus = () => {
        clearAllTooltips();
        clearAllInfos();
        clearAllPopUpMenus();
    };

    const renderTopoLayout = async (topoId, topoTitle, topoType = "topology") => {
        cleanTopoLayout();
        setCurrentTopoType(topoType);
        intervalId = setInterval(intervalUpdateNodesAndEdgesState, 15000);
        setCurrentTopoId(topoId);
        currentTopoIdVar = topoId;
        setCurrentTopoTitle(topoTitle);
        await refreshTopoLayoutData(topoId, false, true);
        return () => {
            graphRef.current.dispose();
        };
    };

    const cleanTopoLayout = () => {
        if (graphRef.current) {
            graphRef.current.dispose();
            graphRef.current = null;
        }
        if (intervalId !== null) {
            clearInterval(intervalId);
            intervalId = null;
        }
        setIsTopoLegend(false);
        setIsEditMode(false);
        setIsHistoryMode(false);
        setIsTopoLayoutRendered(false);
        setIsTopoLeftFloatMenuRender(false);
        setAddCount(0);
        setRandomYOffset(Math.floor(Math.random() * 41) - 20);
        deviceBriefTooltipRef.current.hideDeviceBriefTooltip();
        linkBriefTooltipRef.current.hideLinkBriefTooltip();
        deviceDetailedBottomInformationRef.current.hideDeviceDetailedBottomInformation();
        deviceDetailedInformationRef.current.hideDeviceDetailedInformation();
        linkDetailedBottomInformationRef.current.hideLinkDetailedBottomInformation();
        deviceRightClickPopUpMenuRef.current.hideDeviceRightClickPopUpMenu();
        linkRightClickPopUpMenuRef.current.hideLinkRightClickPopUpMenu();
        if (blankRightClickPopUpMenuRef.current) {
            blankRightClickPopUpMenuRef.current.hideBlankRightClickPopUpMenu();
        }
    };

    const enableEditModeCallback = () => {
        setIsEditMode(true);
    };

    const exportAllTopologyElements = () => {
        const allElements = {
            topologyId: currentTopoId,
            topologyNodes: [],
            topologyEdges: [],
            isShowLegend: isTopoLegend,
            zoom: graphRef.current.zoom(),
            translateX: graphRef.current.translate().tx,
            translateY: graphRef.current.translate().ty,
            isInTreeMode: isShowBessel
        };
        const topoElements = graphRef.current.toJSON().cells;

        topoElements.forEach(node => {
            if (node.shape !== "edge") {
                const tempNode = {
                    label: node.label,
                    layer: node.layer,
                    positionX: node.position.x,
                    positionY: node.position.y,
                    switchSN: node.switch_sn,
                    id: node.id,
                    switchId: node.switch_id,
                    monitorTargetId: node.monitor_target_id
                };
                allElements.topologyNodes.push(tempNode);
            } else if (Array.isArray(node.port_info)) {
                node.port_info.forEach(port => {
                    const tempEdge = {
                        source: node.source_sn,
                        target: node.target_sn,
                        sourcePort: port.source_port,
                        targetPort: port.target_port,
                        id: node.id
                    };
                    allElements.topologyEdges.push(tempEdge);
                });
            }
        });
        return allElements;
    };

    const saveTopoCallback = async () => {
        const dataTobeSaved = exportAllTopologyElements();
        setIsShowSpin(true);
        try {
            await saveTopologyDetail(dataTobeSaved).then(async response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    await refreshTopoLayoutData(currentTopoId, false);
                    currentTopoIdVar = currentTopoId;
                    message.success(response.info);
                }
            });
        } catch (e) {
            message.error("An error occurred during the process of restore");
        } finally {
            setIsShowSpin(false);
        }
    };

    const zoomInCallback = () => {
        if (graphRef.current) {
            const currentZoom = graphRef.current.zoom();
            graphRef.current.zoom(currentZoom * 0.1);
        }
    };

    const zoomOutCallback = () => {
        if (graphRef.current) {
            const currentZoom = graphRef.current.zoom();
            graphRef.current.zoom(currentZoom * -0.1);
        }
    };

    const zoomResetCallback = () => {
        if (graphRef.current) {
            graphRef.current.zoomTo(1);
            graphRef.current.centerContent();
        }
    };

    const downloadImgCallback = async () => {
        if (graphRef.current) {
            const stylesheet = `
            .ant-flex-vertical {
                display: flex;
                flex-direction: column;
            }
            .ant-flex-center {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
            }
            .ant-flex-center strong {
                font-weight: bold;
            }
        `;
            setIsShowSpin(true);
            setTimeout(() => {
                graphRef.current.exportJPEG(`${currentTopoTitle}.jpg`, {
                    padding: 50,
                    bbox: true,
                    embedImages: true,
                    stylesheet
                });
            }, 1000);
            setTimeout(() => {
                setIsShowSpin(false);
            }, 2000);
        }
    };

    useEffect(() => {
        if (isTopoLegend) {
            topoLegendRef.current.showTopoLegend();
        } else {
            topoLegendRef.current.hideTopoLegend();
        }
    }, [isTopoLegend]);

    const getNodeStatus = (node, isEdit = null, isHistory = null) => {
        if (isEdit === null) {
            isEdit = isEditMode;
        }
        if (isEdit) {
            return "online";
        }
        if (isHistory) {
            if (node.monitor_status === "offline") {
                return "offline";
            }
        } else if (node.reachable_status === "offline") {
            return "offline";
        }
        return "online";
    };

    const debounce = (func, wait) => {
        return (...args) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    };

    const getEdgeColor = (edge, isEdit = null) => {
        if (isEdit === null) {
            isEdit = isEditMode;
        }
        if (isEdit) {
            return "#A2B1C3";
        }
        if (edge.status === "down") {
            return "#F53F3F";
        }
        if (edge.status === "up") {
            return "#2BC174";
        }
        if (edge.status === "mixed") {
            return "#FFBB00";
        }
        return "#F53F3F";
    };

    const refreshTopoLayoutData = async (topoId, isEdit = null, isFirstRender = false, isKeepBessel = false) => {
        if (topoId === null) {
            return;
        }
        let model = null;
        let currentZoom = null;
        let currentPosition = null;

        if (isEdit === null) {
            isEdit = isEditMode;
        }
        await getTopology({topologyId: topoId}).then(response => {
            if (response.status !== 200) {
                message.error("Get topology failed");
                return;
            }
            setIsTopoLegend(response.data.isShowLegend);
            response.data.nodes.forEach(node => {
                if (node.device_type === 2) {
                    node.shape = "otn-node";
                } else {
                    node.shape = "switch-node";
                }
            });
            nodes = response.data.nodes;
            response.data.edges.forEach(edge => {
                edge.attrs = {
                    line: {
                        stroke: getEdgeColor(edge),
                        strokeWidth: 1,
                        targetMarker: null
                    }
                };
            });
            edges = response.data.edges;
            if (isFirstRender) {
                currentZoom = response.data.zoom;
                currentPosition = {tx: response.data.translateX, ty: response.data.translateY};
                isShowBessel = response.data.isInTreeMode;
            }
            if (isKeepBessel) {
                isShowBessel = response.data.isInTreeMode;
            }
        });

        model = {
            nodes,
            edges
        };

        const modelCalculated = modelPostProcess(model.nodes, model.edges, isFirstRender);
        model.nodes = modelCalculated.nodes;
        model.edges = modelCalculated.edges;
        setModel(model);

        if (graphRef.current && !isFirstRender) {
            currentZoom = graphRef.current.zoom();
            currentPosition = graphRef.current.translate();
            graphRef.current.dispose();
        }
        graphRef.current = new Graph({
            container: containerRef.current,
            width: containerRef.current?.clientWidth,
            height: containerRef.current?.clientHeight,
            grid: isShowGrid,
            rotating: {
                enabled: true
            },
            panning: {
                enabled: !isEdit,
                eventTypes: ["leftMouseDown", "mouseWheel"]
            },
            connector: "smooth",
            mousewheel: {
                enabled: true,
                modifiers: "ctrl"
            },
            interacting: {
                nodeMovable: isNodeMovable, // Disable node movement
                edgeMovable: false
            }
        });

        const graph = graphRef.current;

        registerCustomNode(isEdit);
        selection = new Selection({
            enabled: isEdit,
            multiple: true,
            rubberband: true,
            movable: true,
            showNodeSelectionBox: true
        });
        snapLine = new Snapline({
            enabled: isEdit
        });
        history = new History({
            enabled: isEdit
        });
        keyboard = new Keyboard({enabled: isEdit, global: true});

        graph.use(selection);
        graph.use(snapLine);
        graph.use(history);
        graph.use(new Export());

        graph.use(
            new MiniMap({
                container: refMiniMapContainer.current,
                width: 300,
                height: 225,
                padding: 10
            })
        );

        graph.use(keyboard);

        graph.fromJSON(model);
        updateAllEdgesVertices();
        if (currentZoom && currentPosition) {
            graph.zoomTo(currentZoom);
            graph.translate(currentPosition.tx, currentPosition.ty);
        }

        bindGraphEvents();

        graphRef.current.centerContent();

        setIsTopoLayoutRendered(true);
        setIsTopoLeftFloatMenuRender(true);
        clearHistory(100);
    };

    const reloadCallback = async () => {
        await refreshTopoLayoutData(currentTopoId, isEditMode, false, true);
    };

    const addDeviceCallback = () => {
        let snList = [];
        let otnList = [];
        if (graphRef.current) {
            const model = graphRef.current.toJSON();
            const nodes = model.cells.filter(cell => cell.shape !== "edge");
            nodes.forEach(node => {
                if (node.device_type === 2) {
                    otnList.push(node.ne_name);
                } else {
                    snList.push(node.switch_sn);
                }
            });
            snList = [...new Set(snList)];
            otnList = [...new Set(otnList)];
        }
        addDeviceModalRef.current.showAddDeviceModal(snList, otnList);
    };

    const editNodeCallback = node => {
        if (!isEditMode) return;
        editDeviceModalRef.current.showEditDeviceModal(node);
    };

    const updateDeleteButtonState = () => {
        if (graphRef.current) {
            const nodes = graphRef.current.getSelectedCells();
            setCanDelete(nodes.length > 0);
        }
    };

    const updateTreeLayoutButtonState = () => {
        if (graphRef.current) {
            const nodes = graphRef.current.getSelectedCells();
            setCanTreeLayout(nodes.length > 0);
        }
    };

    const deleteCallback = async () => {
        if (!isEditMode) return;

        graphRef.current.startBatch("delete");
        const selectedCells = graphRef.current.getSelectedCells();
        if (selectedCells.length === 0) return;
        selectedCells.map(cell => {
            if (cell.isNode()) {
                deleteNodeCallback(cell);
            } else if (cell.isEdge()) {
                deleteLinkCallback(cell);
            }
        });
        graphRef.current.stopBatch("delete");
    };

    const autoDiscoverCallback = async () => {
        if (!isEditMode) return;
        confirmModalAction(
            "Are you sure you want to auto-discover? It will replace all links on the current topology.",
            async () => {
                setIsShowSpin(true);

                graphRef.current.startBatch("auto-discover");
                const nodes = graphRef.current.getCells().filter(cell => cell.shape !== "edge");
                const monitorTargetIds = nodes.map(node => node.store.data.monitor_target_id);
                try {
                    await autoDiscoverTopology(monitorTargetIds).then(async response => {
                        if (response.status !== 200) {
                            message.error(response.info);
                        } else {
                            message.success(response.info);
                            handleAutoDiscover(response.data);
                        }
                    });
                } catch (e) {
                    message.error("An error occurred during the process of auto discover topology");
                } finally {
                    graphRef.current.stopBatch("auto-discover");
                    setIsShowSpin(false);
                }
            }
        );
    };

    const handleAutoDiscover = async linkInfo => {
        const nodes = graphRef.current.getCells().filter(cell => cell.shape !== "edge");
        const edges = graphRef.current.getCells().filter(cell => cell.shape === "edge");
        edges.forEach(edge => {
            graphRef.current.removeEdge(edge.id);
        });
        const newEdges = linkInfo
            .map(link => {
                let sourceNode = null;
                let targetNode = null;
                nodes.forEach(node => {
                    if (node.store.data.monitor_target_id === link.source_monitor_id) {
                        sourceNode = node;
                    }
                    if (node.store.data.monitor_target_id === link.target_monitor_id) {
                        targetNode = node;
                    }
                });

                if (sourceNode === null || targetNode === null) {
                    return null;
                }

                return {
                    port_info: link.port_info,
                    source: sourceNode.store.data.id,
                    source_label: sourceNode.store.data.label,
                    source_mac_addr: sourceNode.store.data.mac_addr,
                    source_sn: sourceNode.store.data.switch_sn,
                    target: targetNode.store.data.id,
                    target_label: targetNode.store.data.label,
                    target_mac_addr: targetNode.store.data.mac_addr,
                    target_sn: targetNode.store.data.switch_sn
                };
            })
            .filter(edge => edge !== null);

        const modelCalculated = modelPostProcess(model.nodes, newEdges, false);
        modelCalculated.edges.forEach(edge => {
            graphRef.current.addEdge(edge);
        });
        updateAllEdgesVertices();
    };

    const cancelEditCallback = async () => {
        await refreshTopoLayoutData(currentTopoId, false, false, true);
    };

    const cancelHistoryModeCallback = async () => {
        await refreshTopoLayoutData(currentTopoId, false, false, true);
    };

    const removeAllEdgesMarkers = () => {
        const edges = graphRef.current.getEdges();
        edges.forEach(edge => {
            edge.setAttrs({
                line: {
                    targetMarker: null
                }
            });
        });
    };

    const updateModel = () => {
        const updatedModel = graphRef.current.toJSON();
        setModel({
            nodes: updatedModel.cells.filter(cell => cell.shape !== "edge"),
            edges: updatedModel.cells.filter(cell => cell.shape === "edge")
        });
    };

    const isModelStructureEqualGraph = model => {
        if (!model) {
            return false;
        }
        if (model.isShowBessel !== isShowBessel) {
            return false;
        }
        const modelNodes = model.nodes;
        const graphNodes = graphRef.current.toJSON().cells.filter(cell => cell.shape !== "edge");
        if (modelNodes.length !== graphNodes.length) {
            return false;
        }

        const nodesPositionMatch = modelNodes.every(modelNode => {
            const graphNode = graphNodes.find(node => node.id === modelNode.id);
            return (
                graphNode &&
                graphNode.position.x === modelNode.position.x &&
                graphNode.position.y === modelNode.position.y
            );
        });
        if (!nodesPositionMatch) {
            return false;
        }
        const modelEdges = model.edges;
        const graphEdges = graphRef.current.toJSON().cells.filter(cell => cell.shape === "edge");
        if (modelEdges.length !== graphEdges.length) {
            return false;
        }
        return modelEdges.every(modelEdge => {
            const graphEdge = graphEdges.find(edge => edge.id === modelEdge.id);
            return (
                graphEdge &&
                graphEdge.source.cell === modelEdge.source.cell &&
                graphEdge.target.cell === modelEdge.target.cell &&
                JSON.stringify(graphEdge.port_info) === JSON.stringify(modelEdge.port_info)
            );
        });
    };

    const topoHistoryAttrs = {
        undo: () => {
            let isNeedToPopTwice = false;
            if (graphRef.current) {
                isNeedToPopTwice =
                    historyModelStack.length > 1 &&
                    isModelStructureEqualGraph(historyModelStack[historyModelStack.length - 1]);
                graphRef.current.undo();
                updateModel();
            }
            if (graphRef.current.getPlugin("history")) {
                graphRef.current.getPlugin("history").freezed = true;
            }
            if (isNeedToPopTwice) {
                historyRedoModelStack.push(historyModelStack.pop());
            }
            const localModel = historyModelStack.pop();
            historyRedoModelStack.push(localModel);
            updateTreeModeAllNodesAndEdgesStyle(localModel);
            removeAllEdgesMarkers();
            updateHistoryButtonState();
            graphRef.current.cleanSelection();
            if (graphRef.current.getPlugin("history")) {
                graphRef.current.getPlugin("history").freezed = false;
            }
        },
        redo: () => {
            let isNeedToPopTwice = false;
            if (graphRef.current) {
                isNeedToPopTwice =
                    historyRedoModelStack.length > 1 &&
                    isModelStructureEqualGraph(historyRedoModelStack[historyRedoModelStack.length - 1]);
                graphRef.current.redo();
                updateModel();
            }
            if (graphRef.current.getPlugin("history")) {
                graphRef.current.getPlugin("history").freezed = true;
            }
            if (isNeedToPopTwice) {
                historyModelStack.push(historyRedoModelStack.pop());
            }
            const localModel = historyRedoModelStack.pop();
            historyModelStack.push(localModel);
            updateTreeModeAllNodesAndEdgesStyle(localModel);
            removeAllEdgesMarkers();
            updateHistoryButtonState();
            graphRef.current.cleanSelection();
            if (graphRef.current.getPlugin("history")) {
                graphRef.current.getPlugin("history").freezed = false;
            }
            updateHistoryButtonState();
        },
        canRedo,
        canUndo
    };

    const autoLayoutCallback = (func, onlySelectedNodes = false) => {
        // const isShowBesselPre = isShowBessel;

        graphRef.current.startBatch("auto-layout");
        let nodes = graphRef.current
            .getSelectedCells()
            .filter(cell => cell.isNode())
            .map(node => node.store.data);

        if (nodes.length === 0 && onlySelectedNodes) {
            nodes = graphRef.current.getNodes().map(node => node.store.data);
        }

        if (nodes.length === 0) return;

        const nodeIds = new Set(nodes.map(node => node.id));
        const edges = graphRef.current
            .getEdges()
            .filter(edge => nodeIds.has(edge.store.data.source.cell) || nodeIds.has(edge.store.data.target.cell))
            .map(edge => edge.store.data);

        const model = func(nodes, edges, window.innerWidth - 400, window.innerHeight - 200);

        if (model === false) {
            return;
        }

        nodes.forEach(node => {
            const node_data = model.nodes.find(n => `${n.id}` === `${node.id}`);
            const graphNode = graphRef.current.getCellById(node.id);
            if (graphNode) {
                graphNode.prop("position", {x: node_data.x, y: node_data.y});
            }
        });

        graphRef.current.getEdges().forEach(edge => {
            if (nodeIds.has(edge.prop("source").cell) || nodeIds.has(edge.prop("target").cell)) {
                edge.prop("source", {cell: edge.prop("source").cell, port: "input"});
                edge.prop("target", {cell: edge.prop("target").cell, port: "input"});
                edge.vertices = [];
                edge.setConnector("normal");
            }
        });

        graphRef.current.getNodes().forEach(node => {
            if (nodeIds.has(node.store.data.id)) {
                const ports = node.getPorts();
                const outputPorts = ports.filter(port => port.group === "output");
                outputPorts.forEach(port => {
                    node.removePort(port.id);
                });
            }
        });

        isShowBessel = false;
        graphRef.current.stopBatch("auto-layout");
    };

    const updateTreeModeAllNodesAndEdgesStyle = modelLocal => {
        isShowBessel = modelLocal.isShowBessel;
        const resultModel = {};
        const modelCalculated = modelPostProcess(modelLocal.nodes, modelLocal.edges, false);
        resultModel.nodes = modelCalculated.nodes;
        resultModel.edges = modelCalculated.edges;
        setModel(resultModel);
        graphRef.current.clearCells();
        modelCalculated.nodes.forEach(node => {
            graphRef.current.addNode(node);
        });
        modelCalculated.edges.forEach(edge => {
            graphRef.current.addEdge(edge);
        });
        updateAllEdgesVertices();
    };

    const isHierarchyLayoutValid = () => {
        const nodes = graphRef.current?.getNodes();
        const edges = graphRef.current?.getEdges();
        const rootNode = graphRef.current?.getSelectedCells().filter(cell => cell.isNode());

        if (!nodes || !edges || nodes.length === 0 || edges.length === 0) {
            return false;
        }

        if (rootNode.length === 0) {
            const rootNodeLabel = "Modeling Methods";
            return nodes.some(node => node.store.data.label === rootNodeLabel);
        }
        return true;
    };

    const hasNodes = () => {
        const nodes = graphRef.current?.getNodes();
        return nodes && nodes.length > 0;
    };

    const autolayoutAttrs = {
        autoHierarchyLayout: () => {
            const nodes = graphRef.current.getNodes().map(node => ({
                id: node.store.data.id,
                label: node.store.data.label
            }));
            const nodes_id_label_map = {};
            nodes.forEach(node => {
                nodes_id_label_map[node.label] = node.id;
            });
            const edges = graphRef.current.getEdges().map(node => ({
                source: node.store.data.source.cell,
                target: node.store.data.target.cell
            }));
            const rootNode = graphRef.current.getSelectedCells().filter(cell => cell.isNode());
            let model_local = null;
            // model_local = hierarchyLayoutDataTraverse(nodes, edges, rootNode[0].id);
            model_local = hierarchyLayoutDataTraverse(
                nodes,
                edges,
                rootNode.map(node => node.id)
            );

            graphRef.current.getNodes().forEach(node => {
                // update node position
                const node_id = node.store.data.id;
                const node_data = model_local.nodes.find(n => String(n.id) === String(node_id));
                if (node_data) {
                    node.prop("position", {x: node_data.x, y: node_data.y});
                }
            });
            model.nodes.forEach(node => {
                // update node position
                const node_id = node.id;
                const node_data = model_local.nodes.find(n => String(n.id) === String(node_id));
                if (node_data) {
                    node.x = node_data.x;
                    node.y = node_data.y;
                    node.layer = node_data.layer;
                }
            });
            isShowBessel = true;
            model.edges = graphRef.current.getEdges().map(edge => edge.toJSON());
            const modelCalculated = modelPostProcess(model.nodes, model.edges, false);
            model.nodes = modelCalculated.nodes;
            model.edges = modelCalculated.edges;
            setModel(model);
            graphRef.current.startBatch("auto-hierarchy-layout");
            graphRef.current.clearCells();
            modelCalculated.nodes.forEach(node => {
                graphRef.current.addNode(node);
            });
            modelCalculated.edges.forEach(edge => {
                graphRef.current.addEdge(edge);
            });
            updateAllEdgesVertices();
            graphRef.current.stopBatch("auto-hierarchy-layout");
            graphRef.current.cleanSelection();
        },

        isHierarchyLayoutValid,
        hasNodes,

        autoGirdLayout: () => {
            if (hasNodes()) {
                autoLayoutCallback(girdLayoutDataTraverse, true);
            }
        },
        autoCircularLayout: () => {
            if (hasNodes()) {
                autoLayoutCallback(circularLayoutDataTraverse, true);
            }
        },
        autoEllipticalLayout: () => {
            if (hasNodes()) {
                autoLayoutCallback(ellipticalLayoutDataTraverse, true);
            }
        }
    };

    const updateHistoryButtonState = () => {
        if (graphRef.current) {
            setCanUndo(
                historyModelStack.length > 1 ||
                    (historyModelStack.length === 1 && !isModelStructureEqualGraph(historyModelStack[0]))
            );
            setCanRedo(
                historyRedoModelStack.length > 1 ||
                    (historyRedoModelStack.length === 1 && !isModelStructureEqualGraph(historyRedoModelStack[0]))
            );
        }
    };

    const updateNodesAndEdgesColors = (model, isEdit = null) => {
        if (isEdit === null) {
            isEdit = isEditMode;
        }
        model.cells.forEach(cell => {
            if (cell.shape.startsWith("node")) {
                cell.reachable_status = getNodeStatus(cell, isEdit);
            } else if (cell.shape === "edge") {
                const edgeColor = getEdgeColor(cell, isEdit);
                cell.attrs = {
                    line: {
                        stroke: edgeColor,
                        strokeWidth: 1,
                        targetMarker: null
                    }
                };
            }
        });
        return model;
    };

    const updateTopologyLayout = async (historyDate = null, isShowMessage = false) => {
        if (currentTopoIdVar === null) {
            return;
        }
        const data = {topologyId: currentTopoIdVar, date: historyDate};
        await getTopology(data).then(async response => {
            if (response.status !== 200) {
                message.error("Get topology standard failed");
                return;
            }
            setIsTopoLegend(response.data.isShowLegend);
            const nodes = graphRef.current.getNodes();
            for (let i = 0; i < nodes.length; i++) {
                const node = nodes[i];
                const response_filter_node = response.data.nodes.find(n => n.id === node.id);
                const isHistory = historyDate !== null;
                if (
                    response_filter_node &&
                    node.store.data.shape !== getNodeStatus(response_filter_node, null, isHistory)
                ) {
                    node.prop("reachable_status", getNodeStatus(response_filter_node, null, isHistory));
                } else {
                    refreshTopoLayoutData(currentTopoIdVar, false);
                    return;
                }
            }
            const edges = graphRef.current.getEdges();
            for (let i = 0; i < edges.length; i++) {
                const edge = edges[i];
                const response_filter_edge = response.data.edges.find(
                    e =>
                        (e.target === edge.store.data.target.cell && e.source === edge.store.data.source.cell) ||
                        (e.source === edge.store.data.target.cell && e.target === edge.store.data.source.cell)
                );
                if (response_filter_edge) {
                    const edgeColor = getEdgeColor(response_filter_edge);
                    edge.attr("line/stroke", edgeColor);
                } else {
                    edge.attr("line/stroke", "#F53F3F");
                }
                const tempEdge = response.data.edges.find(
                    e =>
                        (e.target === edge.store.data.target.cell && e.source === edge.store.data.source.cell) ||
                        (e.source === edge.store.data.target.cell && e.target === edge.store.data.source.cell)
                );
                if (tempEdge) {
                    edge.prop("port_info", tempEdge.port_info);
                } else {
                    refreshTopoLayoutData(currentTopoIdVar, false);
                    return;
                }
            }

            if (currentDisplayedNode) {
                const node = graphRef.current.getCellById(currentDisplayedNode.id);
                if (node) {
                    const displayDate = historyDate || timeSliderRef.current.getSelectedDate();
                    if (isHistoryModeRef.current) {
                        deviceDetailedBottomInformationRef.current.showDeviceDetailedBottomInformation(
                            node.store.data.device_type === 2 ? node.store.data.ne_name : node.store.data.switch_sn,
                            node.store.data.device_type,
                            displayDate
                        );
                    } else {
                        deviceDetailedBottomInformationRef.current.showDeviceDetailedBottomInformation(
                            node.store.data.device_type === 2 ? node.store.data.ne_name : node.store.data.switch_sn,
                            node.store.data.device_type
                        );
                    }
                }
            }

            if (isShowMessage && historyDate) {
                message.success(`The topology has been updated, date: ${historyDate}`);
            }
        });
    };

    const intervalUpdateNodesAndEdgesState = async () => {
        await updateTopologyLayout();
    };

    const updateTopoOptions = isEdit => {
        if (graphRef.current) {
            let currentModel = graphRef.current.toJSON();
            const currentZoom = graphRef.current.zoom();
            const currentPosition = graphRef.current.translate();
            graphRef.current?.dispose();
            graphRef.current = new Graph({
                container: containerRef.current,
                width: containerRef.current?.clientWidth,
                height: containerRef.current?.clientHeight,
                grid: isShowGrid,
                panning: {
                    enabled: !isEdit,
                    eventTypes: ["leftMouseDown", "mouseWheel"]
                },
                connector: "smooth",
                mousewheel: {
                    enabled: true,
                    modifiers: "ctrl"
                },
                interacting: {
                    nodeMovable: isNodeMovable,
                    edgeMovable: false
                }
            });

            const graph = graphRef.current;

            registerCustomNode();
            graph.use(selection);
            graph.use(snapLine);
            graph.use(history);
            graph.use(keyboard);
            graph.use(new Export());
            if (isEdit) {
                graph.use(
                    new MiniMap({
                        container: refMiniMapContainer.current,
                        width: 300,
                        height: 225,
                        padding: 10
                    })
                );
            }
            currentModel = updatePortVisibility(currentModel, isEdit);
            currentModel = updateNodesAndEdgesColors(currentModel, isEdit);
            graph.fromJSON(currentModel);
            updateAllEdgesVertices();
            graph.zoomTo(currentZoom);
            graph.translate(currentPosition.tx, currentPosition.ty);
            bindGraphEvents();
        }
    };

    const renderMiniMap = () => {
        const miniMapElement = document.querySelector(".x6-widget-minimap");
        if (miniMapElement) {
            graphRef.current?.getPlugin("minimap")?.dispose();
            const minimapElements = document.getElementsByClassName("x6-widget-minimap");
            graphRef.current.use(
                new MiniMap({
                    container: refMiniMapContainer.current,
                    width: 300,
                    height: 225,
                    padding: 10
                })
            );
            setTimeout(() => {
                if (minimapElements.length > 1) {
                    for (let i = 0; i < minimapElements.length - 1; i++) {
                        minimapElements[i].remove();
                    }
                }
            }, 1000);
        }
    };

    const bindHistoryEvents = (func, batchName = null) => {
        if (!graphRef.current) {
            return;
        }
        if (batchName === null) {
            batchName = "bind-events";
        }
        graphRef.current.startBatch(batchName);
        func();
        setTimeout(() => {
            graphRef.current.stopBatch(batchName);
        }, 100);
    };

    const bindGraphEvents = () => {
        if (!graphRef.current) {
            return;
        }
        const graph = graphRef.current;

        graph.options.connecting = {
            ...graph.options.connecting,
            validateConnection({targetCell}) {
                if (targetCell) {
                    const nodeData = targetCell.store.data;
                    if (nodeData.device_type === 2) {
                        deviceBriefTooltipRef.current.showDeviceBriefTooltip({
                            "NE Name": nodeData.ne_name,
                            PN: nodeData.pn,
                            "Temperature (°C)": nodeData.temperature,
                            "Actual Power (W)": nodeData.actual_power,
                            Status: nodeData.status,
                            "Reachable Status": nodeData.reachable_status
                        });
                    } else {
                        deviceBriefTooltipRef.current.showDeviceBriefTooltip({
                            "Switch Name": nodeData.label,
                            "Switch SN": nodeData.switch_sn,
                            Model: nodeData.model,
                            Version: nodeData.version,
                            Status: nodeData.status,
                            "Reachable Status": nodeData.reachable_status,
                            "Mgmt IP": nodeData.mgt_ip,
                            "MAC Address": nodeData.mac_addr
                        });
                    }
                }
                return true;
            }
        };

        graph.bindKey("ctrl + s", async event => {
            if (isEditMode) {
                event.preventDefault();
                await saveTopoCallback();
                setIsEditMode(false);
            }
        });

        graph.bindKey("ctrl + x", async event => {
            if (isEditMode) {
                event.preventDefault();
                await cancelEditCallback();
                setIsEditMode(false);
            }
        });

        graph.bindKey("delete", event => {
            event.preventDefault();
            if (graph.getSelectedCells().length === 0) {
                return;
            }
            if (graph.getSelectedCells()[0].isEdge() && (currentTopoType === "site" || currentTopoType === "fabric")) {
                linkBriefTooltipRef.current.hideLinkBriefTooltip();
                deleteCallback();
            } else if (
                graph.getSelectedCells()[0].isNode() &&
                (currentTopoType === "site" || currentTopoType === "fabric")
            ) {
                return;
            } else {
                deleteCallback();
                clearAllTooltips();
            }
        });

        graph.bindKey("ctrl + z", async event => {
            if (isEditMode) {
                event.preventDefault();
                topoHistoryAttrs.undo();
            }
        });

        graph.bindKey("ctrl + y", async event => {
            if (isEditMode) {
                event.preventDefault();
                topoHistoryAttrs.redo();
            }
        });

        graph.bindKey("ctrl + a", async event => {
            if (isEditMode) {
                event.preventDefault();
                graph.select(graph.getCells());
            }
        });

        graph.on("node:mouseenter", async ({node}) => {
            if (
                (isEditMode && graphRef.current.getSelectedCells().length > 1) ||
                blankRightClickPopUpMenuRef.current.isShowBlankRightClickPopUpMenu() ||
                deviceRightClickPopUpMenuRef.current.isShowDeviceRightClickPopUpMenu() ||
                linkRightClickPopUpMenuRef.current.isShowLinkRightClickPopUpMenu()
            )
                return;
            if (node.store.data.device_type === 2) {
                deviceBriefTooltipRef.current.showDeviceBriefTooltip({
                    "NE Name": node.store.data.ne_name,
                    PN: node.store.data.pn,
                    "Temperature (°C)": node.store.data.temperature,
                    "Actual Power (W)": node.store.data.actual_power,
                    Status: node.store.data.status,
                    "Reachable Status": node.store.data.reachable_status
                });
            } else {
                deviceBriefTooltipRef.current.showDeviceBriefTooltip({
                    "Switch Name": node.store.data.label,
                    "Switch SN": node.store.data.switch_sn,
                    Model: node.store.data.model,
                    Version: node.store.data.version,
                    Status: node.store.data.status,
                    "Reachable Status": node.store.data.reachable_status,
                    "Mgmt IP": node.store.data.mgt_ip,
                    "MAC Address": node.store.data.mac_addr
                });
            }
        });

        graph.on("node:mouseleave", clearAllTooltips);

        graph.on("edge:mouseenter", async ({edge}) => {
            if (
                (isEditMode && graphRef.current.getSelectedCells().length > 1) ||
                blankRightClickPopUpMenuRef.current.isShowBlankRightClickPopUpMenu() ||
                deviceRightClickPopUpMenuRef.current.isShowDeviceRightClickPopUpMenu() ||
                linkRightClickPopUpMenuRef.current.isShowLinkRightClickPopUpMenu()
            )
                return;
            linkBriefTooltipRef.current.showLinkBriefTooltip({
                Sysname: [edge.store.data.source_label, edge.store.data.target_label],
                SN: [edge.store.data.source_sn, edge.store.data.target_sn],
                Status: edge.store.data.port_info
            });
        });

        graph.on("edge:mouseleave", clearAllTooltips);

        graph.on("node:click", async ({node}) => {
            clearAllTooltips();
            clearAllPopUpMenus();
            if (!isEditMode) {
                if (currentSelectedNode.current) {
                    currentSelectedNode.current.attr({
                        isSelected: false
                    });
                    currentSelectedNode.current = null;
                }
                node.attr({
                    isSelected: true
                });
                currentSelectedNode.current = node;

                deviceRightClickPopUpMenuRef.current.hideDeviceRightClickPopUpMenu();
                linkDetailedBottomInformationRef.current.hideLinkDetailedBottomInformation();
                if (isHistoryModeRef.current) {
                    deviceDetailedBottomInformationRef.current.showDeviceDetailedBottomInformation(
                        node.store.data.device_type === 2 ? node.store.data.ne_name : node.store.data.switch_sn,
                        node.store.data.device_type,
                        timeSliderRef.current.getSelectedDate()
                    );
                } else {
                    deviceDetailedBottomInformationRef.current.showDeviceDetailedBottomInformation(
                        node.store.data.device_type === 2 ? node.store.data.ne_name : node.store.data.switch_sn,
                        node.store.data.device_type
                    );
                }
                if (node.store.data.device_type === 2) {
                    deviceDetailedInformationRef.current.showDeviceDetailedInformation({
                        "NE Name": node.store.data.ne_name,
                        PN: node.store.data.pn,
                        "Hardware Version": node.store.data.hardware_version,
                        "Software Version": node.store.data.software_version,
                        "Temperature (°C)": node.store.data.temperature,
                        "Actual Power (W)": node.store.data.actual_power
                    });
                } else {
                    const res = await getSwitchDetails(node.store.data.switch_sn);
                    deviceDetailedInformationRef.current.showDeviceDetailedInformation({
                        "Switch Name": node.store.data.label,
                        "Switch SN": node.store.data.switch_sn,
                        Model: res.data.platform_model,
                        Version: res.data.version,
                        Status: res.data.status,
                        "Reachable Status": res.data.reachable_status ? "offline" : "online",
                        "Mgmt IP": res.data.mgt_ip,
                        "MAC Address": node.store.data.mac_addr
                    });
                }
            }
            setCurrentDisplayedNode(node);
        });

        graph.on("edge:click", async ({edge}) => {
            clearAllPopUpMenus();
            if (!isEditMode) {
                const sourceNode = graphRef.current.getCellById(edge.source.cell);
                const targetNode = graphRef.current.getCellById(edge.target.cell);
                deviceDetailedBottomInformationRef.current.hideDeviceDetailedBottomInformation();
                deviceDetailedInformationRef.current.hideDeviceDetailedInformation();
                linkDetailedBottomInformationRef.current.showLinkDetailedBottomInformation({
                    sourceNode,
                    targetNode,
                    Status: edge.store.data.port_info,
                    Time: timeSliderRef.current.getSelectedDate()
                });
            }
        });

        graph.on("edge:mouseup", ({edge}) => {
            const target = edge.getTargetCell();
            if (!target) {
                graph.removeCell(edge);
                message.error("The edge must be connected to two switchs.");
            }
        });

        graph.on("edge:connected", async ({edge}) => {
            const sourceNode = edge.getSourceNode()?.store.data;
            const targetNode = edge.getTargetNode()?.store.data;

            if (sourceNode === targetNode) {
                graphRef.current.startBatch("edge-connected");
                graph.removeCell(edge);
                graphRef.current.stopBatch("edge-connected");
                return;
            }

            if (sourceNode.device_type === 2 || targetNode.device_type === 2) {
                graphRef.current.startBatch("edge-connected");
                graph.removeCell(edge);
                graphRef.current.stopBatch("edge-connected");
                message.error("Do not support edit OTN device's edge.");
                return;
            }
            graphRef.current.startBatch("edge-connected");

            let sourceId = sourceNode.switch_sn;
            let targetId = targetNode.switch_sn;

            if (sourceNode.id > targetNode.id) {
                [sourceId, targetId] = [targetId, sourceId];
            }

            const occupiedPorts = getOccupiedPorts(graph, sourceId, targetId);

            const relatedEdges = graph.getEdges().filter(e => {
                const currentSource = e.getSourceNode()?.store.data.switch_sn;
                const currentTarget = e.getTargetNode()?.store.data.switch_sn;
                return (
                    (currentSource === sourceId && currentTarget === targetId) ||
                    (currentSource === targetId && currentTarget === sourceId)
                );
            });

            if (relatedEdges.length === 1) {
                edge.setData({isTemporary: true});
                editLinkModelRef.current.showAddLinkModal(sourceId, targetId, occupiedPorts);
            } else if (relatedEdges.length > 1) {
                edge.setData({isTemporary: true});
                const portInfo = relatedEdges[0]?.getProp("port_info") || [];
                const currentSourceId = relatedEdges[0]?.getSourceNode()?.store.data.switch_sn;
                const currentTargetId = relatedEdges[0]?.getTargetNode()?.store.data.switch_sn;
                if (currentSourceId === targetId && currentTargetId === sourceId) {
                    portInfo.forEach(port => {
                        port.isSwapped = true;
                        const temp = port.source_port;
                        port.source_port = port.target_port;
                        port.target_port = temp;
                    });
                }
                portInfo.forEach(port => {
                    occupiedPorts[sourceId] = occupiedPorts[sourceId].filter(p => p !== port.source_port);
                    occupiedPorts[targetId] = occupiedPorts[targetId].filter(p => p !== port.target_port);
                });
                editLinkModelRef.current.showEditLinkModal(sourceId, targetId, portInfo, occupiedPorts);
            }
        });

        graph.on("node:contextmenu", ({node, e}) => {
            clearAllPopUpMenus();
            if (!isEditMode) return;
            const selectedCounts = graphRef.current.getSelectedCells().length;
            if (selectedCounts > 1) {
                blankRightClickPopUpMenuRef.current.showBlankRightClickPopUpMenu(selectedCounts, e);
                return;
            }
            deviceRightClickPopUpMenuRef.current.showDeviceRightClickPopUpMenu(node, e);
        });

        graph.on("node:change:position", ({node}) => {
            clearAllPopUpMenus();
            updateEdgeVertices(node);
            updateModel();
            if (isEditMode) {
                const existingMiniMap = graph.getPlugin("minimap");
                if (existingMiniMap) {
                    const miniMapElement = document.querySelector(".x6-widget-minimap");
                    if (miniMapElement) {
                        const debouncedRenderMiniMap = debounce(renderMiniMap, 1000);
                        debouncedRenderMiniMap();
                    }
                }
            }
        });

        graph.on("edge:contextmenu", ({edge, e}) => {
            clearAllPopUpMenus();
            if (!isEditMode) return;
            linkRightClickPopUpMenuRef.current.showLinkRightClickPopUpMenu(edge, e);
        });

        graph.on("blank:contextmenu", ({e}) => {
            clearAllPopUpMenus();
            const selectedCounts = graphRef.current.getSelectedCells().length;
            blankRightClickPopUpMenuRef.current.showBlankRightClickPopUpMenu(selectedCounts, e);
        });

        graph.on("edge:changed", () => {
            clearAllPopUpMenus();
            clearAllTooltips();
        });

        graph.on("blank:click", () => {
            clearAllMenus();
            if (currentSelectedNode.current) {
                currentSelectedNode.current.attr({
                    isSelected: false
                });
                currentSelectedNode.current = null;
            }
        });

        graph.on("blank:mouseenter", () => {
            clearAllTooltips();
        });

        graph.on("history:change", () => {
            updateHistoryButtonState();
        });

        graph.on("history:add", () => {
            const pushCurrentTopoToHistoryStackCallback = debounce(pushCurrentTopoToHistoryStack, 100);
            pushCurrentTopoToHistoryStackCallback();
        });

        graph.on("selection:changed", () => {
            updateDeleteButtonState();
            updateTreeLayoutButtonState();
        });
    };

    const updateEdgeVertices = node => {
        const connectedEdges = graphRef.current.getConnectedEdges(node);

        connectedEdges.forEach(edge => {
            updateSingleEdgeStyle(edge);
        });
    };

    const updateSingleEdgeStyle = edge => {
        const sourceNode = graphRef.current.getCellById(edge.source.cell);
        const targetNode = graphRef.current.getCellById(edge.target.cell);
        if (isShowBessel) {
            if (sourceNode.prop("layer") < targetNode.prop("layer")) {
                edge.source = {cell: sourceNode.id, port: "output"}; // sourceNode output
                edge.target = {cell: targetNode.id, port: "input"}; // targetNode input
            } else if (sourceNode.prop("layer") > targetNode.prop("layer")) {
                swapEdgeSourceAndTarget(edge, sourceNode, targetNode);
            } else {
                edge.source = {cell: sourceNode.id, port: "input"};
                edge.target = {cell: targetNode.id, port: "input"};
            }
            if (sourceNode && targetNode) {
                edge.setVertices(calculateVertices(sourceNode, targetNode));
                edge.setConnector("smooth");
            }
        } else {
            const sourceNode = graphRef.current.getCellById(edge.source.cell);
            const targetNode = graphRef.current.getCellById(edge.target.cell);
            edge.source = {cell: sourceNode.id, port: "input"}; // sourceNode output
            edge.target = {cell: targetNode.id, port: "input"}; // targetNode input
            if (sourceNode && targetNode) {
                edge.setVertices([]);
                edge.setConnector("normal");
            }
        }
        edge.attr("line/targetMarker", null);
        edge.attr("line/stroke", getEdgeColor(edge));
        edge.attr("line/strokeWidth", 1);
    };

    const updateAllEdgesVertices = () => {
        if (isShowBessel) {
            graphRef.current.getEdges().forEach(edge => {
                const sourceNode = graphRef.current.getCellById(edge.source.cell);
                const targetNode = graphRef.current.getCellById(edge.target.cell);
                edge.setVertices(calculateVertices(sourceNode, targetNode));
            });
        }
    };

    const swapEdgeSourceAndTarget = (edge, sourceNode, targetNode) => {
        edge.source_sn = targetNode.switch_sn;
        edge.target_sn = sourceNode.switch_sn;
        edge.source = {cell: targetNode.id, port: "output"}; // targetNode output
        edge.target = {cell: sourceNode.id, port: "input"}; // sourceNode input
        edge.source_label = targetNode.label;
        edge.target_label = sourceNode.label;
        edge.source_mac_addr = targetNode.mac_addr;
        edge.target_mac_addr = sourceNode.mac_addr;
        if (edge.port_info) {
            edge.port_info.forEach(port => {
                const temp = port.source_port;
                port.source_port = port.target_port;
                port.target_port = temp;
            });
        }
    };

    const modelPostProcess = (nodes, edges, isFirstRender = true) => {
        nodes.forEach(node => {
            if (isShowBessel) {
                node.ports = {
                    items: [
                        {
                            id: "input",
                            group: "input",
                            attrs: {
                                portType: "input"
                            }
                        },
                        {
                            id: "output",
                            group: "output",
                            attrs: {
                                portType: "output"
                            }
                        }
                    ],
                    groups: {
                        input: {
                            position: {
                                name: "absolute",
                                args: {
                                    x: 17.21, // Adjust x position as needed
                                    y: 15.45 // Adjust y position to place it inside the node but not covered by the icon
                                }
                            },
                            attrs: {
                                circle: {
                                    r: isEditMode ? 4 : 14,
                                    magnet: isEditMode,
                                    stroke: "#31d0c6",
                                    strokeWidth: 2,
                                    fill: "#fff",
                                    visibility: isFirstRender || !isEditMode ? "hidden" : "visible"
                                }
                            }
                        },
                        output: {
                            position: {
                                name: "absolute",
                                args: {
                                    x: 17.21,
                                    y: 45
                                }
                            },
                            attrs: {
                                circle: {
                                    r: isEditMode ? 4 : 6.5,
                                    magnet: false,
                                    stroke: "#31d0c6",
                                    strokeWidth: 2,
                                    fill: "#A2B1C3",
                                    visibility: isFirstRender || !isEditMode ? "hidden" : "visible"
                                }
                            }
                        }
                    }
                };
            } else {
                node.ports = {
                    items: [
                        {
                            id: "input",
                            group: "input",
                            attrs: {
                                portType: "input"
                            }
                        }
                    ],
                    groups: {
                        input: {
                            position: {
                                name: "absolute",
                                args: {
                                    x: 17.21, // Adjust x position as needed
                                    y: 15.45 // Adjust y position to place it inside the node but not covered by the icon
                                }
                            },
                            attrs: {
                                circle: {
                                    r: isEditMode ? 4 : 14,
                                    magnet: isEditMode,
                                    stroke: "#31d0c6",
                                    strokeWidth: 2,
                                    fill: "#fff",
                                    visibility: isFirstRender || !isEditMode ? "hidden" : "visible"
                                }
                            }
                        }
                    }
                };
            }
        });

        edges.forEach(edge => {
            let sourceNode;
            let targetNode;
            if (!edge.source.cell) {
                sourceNode = nodes.find(node => node.id === edge.source);
                targetNode = nodes.find(node => node.id === edge.target);
            } else {
                sourceNode = nodes.find(node => node.id === edge.source.cell);
                targetNode = nodes.find(node => node.id === edge.target.cell);
            }
            if (isShowBessel) {
                if (sourceNode.layer < targetNode.layer) {
                    edge.source = {cell: sourceNode.id, port: "output"}; // sourceNode output
                    edge.target = {cell: targetNode.id, port: "input"}; // targetNode input
                } else if (sourceNode.layer > targetNode.layer) {
                    swapEdgeSourceAndTarget(edge, sourceNode, targetNode);
                } else {
                    edge.source = {cell: sourceNode.id, port: "input"};
                    edge.target = {cell: targetNode.id, port: "input"};
                }
                edge.vertices = calculateVertices(sourceNode, targetNode);
                edge.connector = {
                    name: "smooth"
                    // args: {
                    //     radius: 2 // Set the desired radius value here
                    // }
                };
            } else {
                edge.source = {cell: sourceNode.id, port: "input"};
                edge.target = {cell: targetNode.id, port: "input"};
                edge.vertices = [];
                edge.connector = {
                    name: "normal"
                };
            }

            const edgeColor = getEdgeColor(edge, isFirstRender ? false : isEditMode);
            edge.attrs = {
                line: {
                    stroke: edgeColor,
                    strokeWidth: 1,
                    targetMarker: null
                }
            };
        });
        return {nodes, edges};
    };

    const updatePortVisibility = (model, isEditMode) => {
        if (!model || !Array.isArray(model.cells)) {
            return model;
        }

        model.cells.forEach(node => {
            if (node.ports && node.ports.groups) {
                Object.keys(node.ports.groups).forEach(group => {
                    if (node.ports.groups[group].attrs && node.ports.groups[group].attrs.circle) {
                        node.ports.groups[group].attrs.circle.visibility = isEditMode ? "visible" : "hidden";
                        node.ports.groups[group].attrs.circle.magnet = group === "output" ? false : isEditMode;
                        if (isEditMode) {
                            node.ports.groups[group].attrs.circle.r = group === "output" ? 4 : 6;
                        } else {
                            node.ports.groups[group].attrs.circle.r = group === "output" ? 6.5 : 14;
                        }
                    }
                });
            }
        });

        return model;
    };

    const updateGraphFromModel = model => {
        if (!graphRef.current) return;

        const currentModel = graphRef.current.toJSON();
        const currentNodes = currentModel.cells.filter(cell => cell.shape !== "edge");
        const currentEdges = currentModel.cells.filter(cell => cell.shape === "edge");

        const newNodes = model.nodes;
        const newEdges = model.edges;

        // Identify nodes to be added and removed
        const nodesToAdd = newNodes.filter(newNode => !currentNodes.some(node => node.id === newNode.id));
        const nodesToRemove = currentNodes.filter(node => !newNodes.some(newNode => newNode.id === node.id));

        // Identify edges to be added and removed
        const edgesToAdd = newEdges.filter(newEdge => !currentEdges.some(edge => edge.id === newEdge.id));
        const edgesToRemove = currentEdges.filter(edge => !newEdges.some(newEdge => newEdge.id === edge.id));

        // Add new nodes
        nodesToAdd.forEach(node => {
            graphRef.current.addNode(node);
        });

        // Remove old nodes
        nodesToRemove.forEach(node => {
            graphRef.current.removeNode(node.id);
        });

        // Add new edges
        edgesToAdd.forEach(edge => {
            graphRef.current.addEdge(edge);
        });

        // Remove old edges
        edgesToRemove.forEach(edge => {
            graphRef.current.removeEdge(edge.id);
        });

        // Update all edges color
        graphRef.current.getEdges().forEach(edge => {
            updateSingleEdgeStyle(edge);
        });
    };

    const updateNodesCallback = async (actions, otnActions) => {
        setIsShowSpin(true);
        const removeIdList = [];
        const addIdList = [];
        for (const [id, action] of Object.entries(actions)) {
            if (action === "remove") {
                removeIdList.push(parseInt(id));
            } else if (action === "add") {
                addIdList.push(id);
            }
        }

        const otnRemoveIdList = [];
        const otnAddIdList = [];
        for (const [id, action] of Object.entries(otnActions)) {
            if (action === "remove") {
                otnRemoveIdList.push(id);
            } else if (action === "add") {
                otnAddIdList.push(id);
            }
        }
        if (addIdList.length > 0 || otnAddIdList.length > 0) {
            await fetchTopologyToBeAddedSwitch(addIdList, otnAddIdList).then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                    return;
                }
                const nodes = response.data;
                nodes.forEach(node => {
                    node.label = node.label && node.label.trim() !== "" ? node.label : "PICOS";
                    node.y += (100 + randomYOffset) * addCount + node.y;
                    if (node.device_type === 2) {
                        node.shape = "otn-node";
                    } else {
                        node.shape = "switch-node";
                    }
                });
                model.nodes = modelPostProcess(model.nodes.concat(nodes), []).nodes;
                model.cells = model.cells || [];
                model.cells = model.cells.concat(model.nodes);
                setModel(updatePortVisibility(model, true));
            });
            setAddCount(addCount + 1);
        }

        if (removeIdList.length > 0 || otnRemoveIdList.length > 0) {
            if (model.nodes === undefined) {
                model.nodes = [];
            }
            if (model.edges === undefined) {
                model.edges = [];
            }
            const nodesIdToBeRemoved = model.nodes
                .filter(node => removeIdList.includes(node.switch_id) || otnRemoveIdList.includes(node.ne_name))
                .map(node => node.id);
            model.nodes = model.nodes.filter(
                node => !removeIdList.includes(node.switch_id) && !otnRemoveIdList.includes(node.ne_name)
            );
            model.edges = model.edges.filter(
                edge => !nodesIdToBeRemoved.includes(edge.source.cell) && !nodesIdToBeRemoved.includes(edge.target.cell)
            );
            setModel(model);
        }
        updateGraphFromModel(model);
        updateAllEdgesVertices();
        renderMiniMap();
        setIsShowSpin(false);
    };

    const saveDeviceInfoCallback = (formInfo, node) => {
        const {label, layer} = formInfo;
        node.prop("label", label);
        node.prop("layer", layer);
    };

    const aimHistoryTimeSliderCallback = () => {
        aimHistoryTimeSliderModalRef.current.showAimHistoryTimeSliderModal();
    };

    const removeDuplicateEdges = (graph, sourceNode, targetNode) => {
        const edges = graph.getEdges();
        const matchingEdges = [];

        edges.forEach(edge => {
            if (
                (edge.source.cell === sourceNode.id && edge.target.cell === targetNode.id) ||
                (edge.source.cell === targetNode.id && edge.target.cell === sourceNode.id)
            ) {
                matchingEdges.push(edge);
            }
        });

        if (matchingEdges.length >= 2) {
            graph.removeEdge(matchingEdges[0].id);
        }
    };

    const getOccupiedPorts = (graph, sourceId, targetId) => {
        const occupiedPorts = {
            [sourceId]: [],
            [targetId]: []
        };

        graph.getEdges().forEach(e => {
            let currentSource = e.getSourceNode()?.store.data;
            let currentTarget = e.getTargetNode()?.store.data;
            const portInfoArray = e.getProp("port_info") || [];
            const expectedSourceId = e.getProp("source_sn");
            const expectedTargetId = e.getProp("target_sn");

            if (currentSource && currentTarget) {
                const isDirectionCorrect =
                    currentSource.switch_sn === expectedSourceId && currentTarget.switch_sn === expectedTargetId;
                if (!isDirectionCorrect) {
                    [currentSource, currentTarget] = [currentTarget, currentSource];
                }
                if (!occupiedPorts[expectedSourceId]) {
                    occupiedPorts[expectedSourceId] = [];
                }
                if (!occupiedPorts[expectedTargetId]) {
                    occupiedPorts[expectedTargetId] = [];
                }
                if (currentSource.switch_sn === expectedSourceId) {
                    portInfoArray.forEach(port => {
                        if (port.source_port && !occupiedPorts[expectedSourceId].includes(port.source_port)) {
                            occupiedPorts[expectedSourceId].push(port.source_port);
                        }
                    });
                }
                if (currentTarget.switch_sn === expectedSourceId) {
                    portInfoArray.forEach(port => {
                        if (port.target_port && !occupiedPorts[expectedSourceId].includes(port.target_port)) {
                            occupiedPorts[expectedSourceId].push(port.target_port);
                        }
                    });
                }
                if (currentSource.switch_sn === expectedTargetId) {
                    portInfoArray.forEach(port => {
                        if (port.source_port && !occupiedPorts[expectedTargetId].includes(port.source_port)) {
                            occupiedPorts[expectedTargetId].push(port.source_port);
                        }
                    });
                }
                if (currentTarget.switch_sn === expectedTargetId) {
                    portInfoArray.forEach(port => {
                        if (port.target_port && !occupiedPorts[expectedTargetId].includes(port.target_port)) {
                            occupiedPorts[expectedTargetId].push(port.target_port);
                        }
                    });
                }
            }
        });
        return occupiedPorts;
    };

    const checkDuplicatePortInfo = (graph, sourceNode, targetNode, portInfo) => {
        const edges = graph.getEdges();
        return edges.some(edge => {
            if (edge.source.cell === sourceNode.id && edge.target.cell !== targetNode.id) {
                return portInfo.some(item1 => {
                    const portInfoArray = edge.getProp("port_info");
                    return portInfoArray.some(item2 => {
                        if (item1.source_port === item2.source_port) {
                            message.error(`${sourceNode.store.data.switch_sn}: ${item1.source_port} is duplicated`);
                            return true;
                        }
                        return false;
                    });
                });
            }

            if (edge.source.cell === targetNode.id && edge.target.cell !== sourceNode.id) {
                return portInfo.some(item1 => {
                    const portInfoArray = edge.getProp("port_info");
                    return portInfoArray.some(item2 => {
                        if (item1.target_port === item2.source_port) {
                            message.error(`${targetNode.store.data.switch_sn}: ${item1.target_port} is duplicated`);
                            return true;
                        }
                        return false;
                    });
                });
            }

            if (edge.target.cell === sourceNode.id && edge.source.cell !== targetNode.id) {
                return portInfo.some(item1 => {
                    const portInfoArray = edge.getProp("port_info");
                    return portInfoArray.some(item2 => {
                        if (item1.source_port === item2.target_port) {
                            message.error(`${sourceNode.store.data.switch_sn}: ${item1.source_port} is duplicated`);
                            return true;
                        }
                        return false;
                    });
                });
            }

            if (edge.target.cell === targetNode.id && edge.source.cell !== sourceNode.id) {
                return portInfo.some(item1 => {
                    const portInfoArray = edge.getProp("port_info");
                    return portInfoArray.some(item2 => {
                        if (item1.target_port === item2.target_port) {
                            message.error(`${targetNode.store.data.switch_sn}: ${item1.target_port} is duplicated`);
                            return true;
                        }
                        return false;
                    });
                });
            }

            return false;
        });
    };

    const handleSaveLink = async (sourceSN, targetSN, portInfo) => {
        if (!sourceSN || !targetSN || !graphRef.current) return;
        const nodes = graphRef.current.getCells().filter(cell => cell.isNode());
        let sourceNode = null;
        let targetNode = null;
        nodes.forEach(node => {
            if (node.store.data.switch_sn === sourceSN) {
                sourceNode = node;
            }
            if (node.store.data.switch_sn === targetSN) {
                targetNode = node;
            }
        });
        if (sourceNode && targetNode) {
            removeDuplicateEdges(graphRef.current, sourceNode, targetNode);
            const isDuplicated = checkDuplicatePortInfo(graphRef.current, sourceNode, targetNode, portInfo);
            if (isDuplicated) {
                handleCancel();
            } else {
                const edges = graphRef.current.getEdges();
                edges.forEach(edge => {
                    if (edge.source.cell === sourceNode.id && edge.target.cell === targetNode.id) {
                        edge.prop("port_info", portInfo);
                        edge.prop("source_label", edge.getSourceNode().store.data.label);
                        edge.prop("target_label", edge.getTargetNode().store.data.label);
                        edge.prop("source_sn", edge.getSourceNode().store.data.switch_sn);
                        edge.prop("target_sn", edge.getTargetNode().store.data.switch_sn);
                    } else if (edge.source.cell === targetNode.id && edge.target.cell === sourceNode.id) {
                        const temp = edge.source;
                        edge.source = edge.target;
                        edge.target = temp;
                        edge.prop("port_info", portInfo);
                        edge.prop("source_label", edge.getSourceNode().store.data.label);
                        edge.prop("target_label", edge.getTargetNode().store.data.label);
                        edge.prop("source_sn", edge.getSourceNode().store.data.switch_sn);
                        edge.prop("target_sn", edge.getTargetNode().store.data.switch_sn);
                    }
                    updateSingleEdgeStyle(edge);
                    edge.setData({isTemporary: false});
                    model.edges = graphRef.current.getEdges().map(edge => edge.toJSON());
                });
            }
        }
    };

    const handleCancel = () => {
        try {
            if (graphRef.current) {
                const graph = graphRef.current;
                const edges = graph.getEdges();
                const tempEdges = edges.filter(edge => {
                    const data = edge.getData();
                    return data && data.isTemporary;
                });
                tempEdges.forEach(edge => graph.removeEdge(edge));
                edges.forEach(edge => {
                    const portInfo = edge.getProp("port_info") || [];
                    portInfo.forEach(port => {
                        if (port.isSwapped) {
                            const temp = port.source_port;
                            port.source_port = port.target_port;
                            port.target_port = temp;
                            port.isSwapped = false;
                        }
                    });
                });
            }
        } catch (error) {
            // console.error("Error cancelling link drawing:", error);
        }
    };

    const saveLinkCallback = async (action, sourceSN, targetSN, portInfo, isWithoutBatch = false) => {
        if (action === "add") {
            await handleSaveLink(sourceSN, targetSN, portInfo);
            updateAllEdgesVertices();
        } else if (action === "cancel") {
            handleCancel();
        }
        if (!isWithoutBatch) {
            graphRef.current.stopBatch("addLink");
        }
        graphRef.current.stopBatch("edge-connected");
    };

    const deleteNodeCallback = node => {
        if (!isEditMode) {
            return;
        }
        graphRef.current.startBatch("deleteNode");
        graphRef.current.removeNode(node.id);
        // Update the model state
        setModel(prevModel => {
            const updatedNodes = prevModel.nodes.filter(n => n.id !== node.id);
            const updatedEdges = prevModel.edges.filter(e => e.source.cell !== node.id && e.target.cell !== node.id);
            return {
                nodes: updatedNodes,
                edges: updatedEdges
            };
        });
        graphRef.current.stopBatch("deleteNode");
    };

    const deleteLinkCallback = edge => {
        if (!isEditMode) {
            return;
        }
        graphRef.current.removeEdge(edge.id);
        // Update the model state
        setModel(prevModel => {
            const updatedNodes = prevModel.nodes;
            const updatedEdges = prevModel.edges.filter(
                e =>
                    !(
                        (e.source.cell === edge.source.cell && e.target.cell === edge.target.cell) ||
                        (e.source.cell === edge.target.cell && e.target.cell === edge.source.cell)
                    )
            );
            return {
                nodes: updatedNodes,
                edges: updatedEdges
            };
        });
    };

    const editLinkCallback = edge => {
        if (!isEditMode) {
            return;
        }

        const sourceNode = edge.getSourceNode()?.store.data;
        const targetNode = edge.getTargetNode()?.store.data;

        if (sourceNode.device_type === 2 || targetNode.device_type === 2) {
            message.error("Do not support edit OTN device's edge.");
            return;
        }

        const occupiedPorts = getOccupiedPorts(graphRef.current, sourceNode.switch_sn, targetNode.switch_sn);
        const portInfo = edge.store.data.port_info || [];
        portInfo.forEach(port => {
            occupiedPorts[sourceNode.switch_sn] = occupiedPorts[sourceNode.switch_sn].filter(
                p => p !== port.source_port
            );
            occupiedPorts[targetNode.switch_sn] = occupiedPorts[targetNode.switch_sn].filter(
                p => p !== port.target_port
            );
        });
        editLinkModelRef.current.showEditLinkModal(sourceNode.switch_sn, targetNode.switch_sn, portInfo, occupiedPorts);
    };

    return (
        <>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            <AddDeviceModal ref={addDeviceModalRef} updateNodesCallback={updateNodesCallback} />
            <EditLinkModal
                ref={editLinkModelRef}
                saveLinkCallback={saveLinkCallback}
                bindHistoryEvents={bindHistoryEvents}
            />
            <EditDeviceModal ref={editDeviceModalRef} saveDeviceInfoCallback={saveDeviceInfoCallback} />
            <AimHistoryTimeSliderModal
                ref={aimHistoryTimeSliderModalRef}
                aimHistoryTimeSliderOKCallback={date => {
                    timeSliderRef.current.aimDate(date);
                }}
            />
            <DeviceBriefTooltip ref={deviceBriefTooltipRef} />
            <LinkBriefTooltip ref={linkBriefTooltipRef} />
            <DeviceRightClickPopUpMenu
                ref={deviceRightClickPopUpMenuRef}
                deleteNodeCallback={deleteNodeCallback}
                editNodeCallback={editNodeCallback}
                currentTopoType={currentTopoType}
            />
            <LinkRightClickPopUpMenu
                ref={linkRightClickPopUpMenuRef}
                deleteLinkCallback={deleteLinkCallback}
                editLinkCallback={editLinkCallback}
                currentTopoType={currentTopoType}
            />
            {isTopoLeftFloatMenuRender ? (
                <BlankRightClickPopUpMenu
                    ref={blankRightClickPopUpMenuRef}
                    addDeviceCallback={addDeviceCallback}
                    saveTopoCallback={saveTopoCallback}
                    zoomInCallback={zoomInCallback}
                    zoomResetCallback={zoomResetCallback}
                    zoomOutCallback={zoomOutCallback}
                    reloadCallback={reloadCallback}
                    cancelEditCallback={cancelEditCallback}
                    autoDiscoverCallback={autoDiscoverCallback}
                    autolayoutAttrs={autolayoutAttrs}
                    topoHistoryAttrs={topoHistoryAttrs}
                    isEditMode={isEditMode}
                    setIsEditMode={setIsEditMode}
                    isTopoLegend={isTopoLegend}
                    setIsTopoLegend={setIsTopoLegend}
                    deleteCallback={deleteCallback}
                    canTreeLayout={canTreeLayout}
                    currentTopoType={currentTopoType}
                />
            ) : null}
            <div style={{position: "relative", width: "100%", height: "100%"}}>
                <DeviceDetailedBottomInformation
                    ref={deviceDetailedBottomInformationRef}
                    containerHeight={containerRef.current?.clientHeight}
                />
                <LinkDetailedBottomInformation
                    ref={linkDetailedBottomInformationRef}
                    containerHeight={containerRef.current?.clientHeight}
                />
                <DeviceDetailedInformation ref={deviceDetailedInformationRef} />
                <TopoLegend ref={topoLegendRef} />
                {isTopoLayoutRendered && currentTopoType === "topology" ? (
                    <TopoLeftFloatMenu
                        ref={topoLeftFloatMenuRef}
                        addDeviceCallback={addDeviceCallback}
                        enableEditModeCallback={enableEditModeCallback}
                        saveTopoCallback={saveTopoCallback}
                        zoomInCallback={zoomInCallback}
                        zoomResetCallback={zoomResetCallback}
                        zoomOutCallback={zoomOutCallback}
                        downloadImgCallback={downloadImgCallback}
                        reloadCallback={reloadCallback}
                        deleteCallback={deleteCallback}
                        cancelEditCallback={cancelEditCallback}
                        cancelHistoryModeCallback={cancelHistoryModeCallback}
                        autoDiscoverCallback={autoDiscoverCallback}
                        aimHistoryTimeSliderCallback={aimHistoryTimeSliderCallback}
                        autolayoutAttrs={autolayoutAttrs}
                        topoHistoryAttrs={topoHistoryAttrs}
                        canDelete={canDelete}
                        canTreeLayout={canTreeLayout}
                        isEditMode={isEditMode}
                        setIsEditMode={setIsEditMode}
                        isHistoryMode={isHistoryMode}
                        setIsHistoryMode={setIsHistoryMode}
                        isTopoLegend={isTopoLegend}
                        setIsTopoLegend={setIsTopoLegend}
                    />
                ) : null}
                {isTopoLayoutRendered && currentTopoType === "fabric" ? (
                    <FabricLeftFloatMenu
                        ref={topoLeftFloatMenuRef}
                        addDeviceCallback={addDeviceCallback}
                        enableEditModeCallback={enableEditModeCallback}
                        saveTopoCallback={saveTopoCallback}
                        zoomInCallback={zoomInCallback}
                        zoomResetCallback={zoomResetCallback}
                        zoomOutCallback={zoomOutCallback}
                        downloadImgCallback={downloadImgCallback}
                        reloadCallback={reloadCallback}
                        deleteCallback={deleteCallback}
                        cancelEditCallback={cancelEditCallback}
                        cancelHistoryModeCallback={cancelHistoryModeCallback}
                        autoDiscoverCallback={autoDiscoverCallback}
                        aimHistoryTimeSliderCallback={aimHistoryTimeSliderCallback}
                        autolayoutAttrs={autolayoutAttrs}
                        topoHistoryAttrs={topoHistoryAttrs}
                        canDelete={canDelete}
                        canTreeLayout={canTreeLayout}
                        isEditMode={isEditMode}
                        setIsEditMode={setIsEditMode}
                        isHistoryMode={isHistoryMode}
                        setIsHistoryMode={setIsHistoryMode}
                        isTopoLegend={isTopoLegend}
                        setIsTopoLegend={setIsTopoLegend}
                    />
                ) : null}
                {isTopoLayoutRendered && currentTopoType === "site" ? (
                    <SiteLeftFloatMenu
                        ref={topoLeftFloatMenuRef}
                        addDeviceCallback={addDeviceCallback}
                        enableEditModeCallback={enableEditModeCallback}
                        saveTopoCallback={saveTopoCallback}
                        zoomInCallback={zoomInCallback}
                        zoomResetCallback={zoomResetCallback}
                        zoomOutCallback={zoomOutCallback}
                        downloadImgCallback={downloadImgCallback}
                        reloadCallback={reloadCallback}
                        deleteCallback={deleteCallback}
                        cancelEditCallback={cancelEditCallback}
                        cancelHistoryModeCallback={cancelHistoryModeCallback}
                        autoDiscoverCallback={autoDiscoverCallback}
                        aimHistoryTimeSliderCallback={aimHistoryTimeSliderCallback}
                        autolayoutAttrs={autolayoutAttrs}
                        topoHistoryAttrs={topoHistoryAttrs}
                        canDelete={canDelete}
                        canTreeLayout={canTreeLayout}
                        isEditMode={isEditMode}
                        setIsEditMode={setIsEditMode}
                        isHistoryMode={isHistoryMode}
                        setIsHistoryMode={setIsHistoryMode}
                        isTopoLegend={isTopoLegend}
                        setIsTopoLegend={setIsTopoLegend}
                    />
                ) : null}
                <div
                    ref={containerRef}
                    style={{
                        backgroundColor: "#F8FAFB",
                        minWidth: "100%",
                        minHeight: "100%",
                        width: "auto",
                        height: "auto"
                    }}
                />
                <div
                    ref={refMiniMapContainer}
                    style={{
                        position: "fixed",
                        right: 24,
                        bottom: 24,
                        display: isEditMode ? "block" : "none",
                        zIndex: 999
                    }}
                />
                <div
                    style={{
                        position: "absolute",
                        right: 250,
                        top: 10,
                        left: 80,
                        display: isHistoryMode ? "block" : "none",
                        zIndex: 1000,
                        backgroundColor: "rgba(255,255,255,0.5)"
                    }}
                >
                    <TimeSlider refreshTopoCallback={updateTopologyLayout} ref={timeSliderRef} />
                </div>
            </div>
        </>
    );
});

export default TopoLayout;
