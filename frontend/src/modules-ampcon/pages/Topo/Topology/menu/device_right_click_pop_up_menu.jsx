import {forwardRef, useImperativeHandle, useState} from "react";
import {Menu} from "@antv/x6-react-components";
import "@antv/x6-react-components/es/menu/style/index.css";
import {usePopper} from "react-popper";

const DeviceRightClickPopUpMenu = forwardRef(({deleteNodeCallback, editNodeCallback, currentTopoType}, ref) => {
    const MenuItem = Menu.Item;

    const [isDeviceRightClickPopUpMenuVisible, setIsDeviceRightClickPopUpMenuVisible] = useState(false);
    const [selectedNode, setSelectedNode] = useState(null);

    const [referenceElement, setReferenceElement] = useState(null);
    const [menuElement, setMenuElement] = useState(null);

    const {styles, attributes} = usePopper(referenceElement, menuElement, {
        placement: "right-start"
    });

    useImperativeHandle(ref, () => ({
        showDeviceRightClickPopUpMenu: (node, e) => {
            setReferenceElement({
                getBoundingClientRect: () => ({
                    width: 0,
                    height: 0,
                    top: e.clientY,
                    left: e.clientX,
                    right: e.clientX,
                    bottom: e.clientY
                }),
                contextElement: document.body
            });
            setIsDeviceRightClickPopUpMenuVisible(true);
            setSelectedNode(node);
        },
        hideDeviceRightClickPopUpMenu: () => {
            setIsDeviceRightClickPopUpMenuVisible(false);
        },
        isShowDeviceRightClickPopUpMenu: () => {
            return isDeviceRightClickPopUpMenuVisible;
        }
    }));

    return isDeviceRightClickPopUpMenuVisible ? (
        <div
            ref={setMenuElement}
            style={{
                ...styles.popper,
                zIndex: 1000
            }}
            {...attributes.popper}
        >
            <Menu>
                {currentTopoType === "topology" && (
                    <MenuItem
                        name="delete"
                        text="Delete"
                        hotkey="Delete"
                        onClick={() => {
                            deleteNodeCallback(selectedNode);
                            setIsDeviceRightClickPopUpMenuVisible(false);
                        }}
                    />
                )}
                <MenuItem
                    name="edit"
                    text="Edit"
                    onClick={() => {
                        editNodeCallback(selectedNode);
                        setIsDeviceRightClickPopUpMenuVisible(false);
                    }}
                />
            </Menu>
        </div>
    ) : null;
});

export default DeviceRightClickPopUpMenu;
