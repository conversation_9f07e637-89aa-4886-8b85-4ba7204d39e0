import {forwardRef, useEffect, useImperativeHandle, useState} from "react";
import {Flex} from "antd";

const LinkInfoBody = ({linkInfoKeys, linkInfoValues, getStatusIcon}) => (
    <Flex
        style={{
            margin: "16px",
            textAlign: "Left",
            fontFamily: "Lato, Lato",
            fontSize: "14px",
            fontStyle: "normal",
            textTransform: "none"
        }}
    >
        <Flex vertical>
            {linkInfoKeys?.map((key, index) => (
                <div
                    style={{
                        height: "17px",
                        color: "#929A9E",
                        fontWeight: 400,
                        marginBottom: index === linkInfoKeys.length - 1 ? "4px" : "16px"
                    }}
                >
                    {key.startsWith("link") ? getStatusIcon(key.split(" ")[1]) : key}
                </div>
            ))}
        </Flex>
        <div style={{width: "16px"}} />
        <Flex vertical>
            {linkInfoValues?.map((value, index) => (
                <div>
                    <div
                        style={{
                            height: "17px",
                            color: "#212519",
                            fontWeight: "bold",
                            marginBottom: index === linkInfoKeys.length - 1 ? "4px" : "16px"
                        }}
                    >
                        {value[0]}
                    </div>
                </div>
            ))}
        </Flex>
        <div style={{width: "16px"}} />
        <Flex vertical>
            {linkInfoValues?.map((value, index) => (
                <div>
                    <div
                        style={{
                            height: "17px",
                            color: "#212519",
                            fontWeight: "bold",
                            marginBottom: index === linkInfoKeys.length - 1 ? "4px" : "16px"
                        }}
                    >
                        {value[1]}
                    </div>
                </div>
            ))}
        </Flex>
        <div style={{width: "2px"}} />
    </Flex>
);

const LinkBriefTooltip = forwardRef((props, ref) => {
    const baseStyle = {
        backgroundColor: "#FFFFFF",
        boxShadow: "0px 1px 12px 1px #E6E8EA",
        borderRadius: "4px",
        position: "absolute",
        display: "block",
        color: "white",
        pointerEvents: "none",
        zIndex: 1000,
        transform: "none",
        whiteSpace: "pre"
    };

    const [isTooltipVisible, setTooltipVisible] = useState(false);
    const [, setDeviceInfo] = useState(null);
    const [linkInfoKeys, setDeviceInfoKeys] = useState([]);
    const [linkInfoValues, setDeviceInfoValues] = useState([]);
    const [linkStyle, setLinkStyle] = useState(baseStyle);

    useEffect(() => {
        const handleMouseMove = event => {
            calculateLinkBriefTooltipStyle(event.clientX, event.clientY);
        };

        window.addEventListener("mousemove", handleMouseMove);

        return () => {
            window.removeEventListener("mousemove", handleMouseMove);
        };
    }, []);

    useImperativeHandle(ref, () => ({
        showLinkBriefTooltip: linkInfo => {
            if (!linkInfo.Status) {
                return;
            }
            const linkShownInfo = structuredClone(linkInfo);
            delete linkShownInfo.Status;
            linkInfo.Status.forEach((port, index) => {
                linkShownInfo[`link${index} ${port.status}`] = [port.source_port, port.target_port];
            });
            setDeviceInfo(linkShownInfo);
            setDeviceInfoKeys(Object.keys(linkShownInfo));
            setDeviceInfoValues(Object.values(linkShownInfo));
            if (linkInfo === "") {
                setTooltipVisible(false);
            } else {
                setTooltipVisible(true);
            }
        },
        hideLinkBriefTooltip: () => {
            setTooltipVisible(false);
        }
    }));

    const calculateLinkBriefTooltipStyle = (x, y) => {
        const baseStyleTemp = {...baseStyle};
        const linkBriefTooltipHidden = document.getElementsByClassName("link_brief_tooltip_hidden")[0];
        if (linkBriefTooltipHidden) {
            const rectHidden = linkBriefTooltipHidden.getBoundingClientRect();
            if (x + 30 + rectHidden.width > window.innerWidth) {
                baseStyleTemp.right = "30px";
                delete baseStyleTemp.left;
            } else {
                baseStyleTemp.left = `${x + 10}px`;
                delete baseStyleTemp.right;
            }
            if (y + 30 + rectHidden.height > window.innerHeight) {
                baseStyleTemp.bottom = "30px";
                delete baseStyleTemp.top;
            } else {
                baseStyleTemp.top = `${y + 10}px`;
                delete baseStyleTemp.bottom;
            }
        }
        setLinkStyle(baseStyleTemp);
    };

    const getStatusIcon = value => {
        return (
            <>
                Link&nbsp;&nbsp;
                <svg
                    style={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        backgroundColor: value === "up" ? "#14C9BB" : "#F53F3F",
                        marginRight: "4px"
                    }}
                />
            </>
        );
    };

    return (
        <>
            {isTooltipVisible ? (
                <div ref={ref} className="link_brief_tooltip" style={linkStyle}>
                    <LinkInfoBody
                        linkInfoKeys={linkInfoKeys}
                        linkInfoValues={linkInfoValues}
                        getStatusIcon={getStatusIcon}
                    />
                </div>
            ) : null}
            <div
                ref={ref}
                className="link_brief_tooltip_hidden"
                style={{
                    backgroundColor: "#FFFFFF",
                    boxShadow: "0px 1px 12px 1px #E6E8EA",
                    borderRadius: "4px",
                    position: "absolute",
                    right: window.innerWidth / 2,
                    bottom: window.innerHeight / 2,
                    display: "block",
                    color: "white",
                    pointerEvents: "none",
                    zIndex: -1,
                    transform: "none",
                    whiteSpace: "pre"
                }}
            >
                <LinkInfoBody
                    linkInfoKeys={linkInfoKeys}
                    linkInfoValues={linkInfoValues}
                    getStatusIcon={getStatusIcon}
                />
            </div>
        </>
    );
});

export default LinkBriefTooltip;
