import {But<PERSON>, Divide<PERSON>, <PERSON>, Modal, Select, Tooltip} from "antd";
import {PlusOutlined, MinusOutlined} from "@ant-design/icons";
import {forwardRef, useImperativeHandle, useState, useEffect, useRef} from "react";
import {getPortList} from "@/modules-ampcon/apis/config_api";

const EditLinkModal = forwardRef((props, ref) => {
    const [sourceSN, setSourceSN] = useState(null);
    const [targetSN, setTargetSN] = useState(null);
    const [portList, setPortList] = useState([{sourcePort: null, targetPort: null}]);
    const [sourceErrors, setSourceErrors] = useState([]);
    const [targetErrors, setTargetErrors] = useState([]);
    const [isShowModal, setIsShowModal] = useState(false);
    const [sourcePortsList, setSourcePortsList] = useState([]);
    const [targetPortsList, setTargetPortsList] = useState([]);
    const [isEditMode, setIsEditMode] = useState(false);
    const [occupiedPorts, setOccupiedPorts] = useState([]);
    const [, setLoading] = useState(true);
    const modalRef = useRef(null);

    useImperativeHandle(ref, () => ({
        showAddLinkModal: (sourceSN, targetSN, occupiedPorts = []) => {
            setSourceSN(sourceSN);
            setTargetSN(targetSN);
            setIsEditMode(false);
            setPortList([{sourcePort: null, targetPort: null}]);
            setOccupiedPorts(occupiedPorts);
            setIsShowModal(true);
        },
        showEditLinkModal: (sourceSN, targetSN, portInfo, occupiedPorts = []) => {
            setSourceSN(sourceSN);
            setTargetSN(targetSN);
            setIsEditMode(true);
            if (portInfo) {
                setPortList(
                    portInfo.map(port => ({
                        sourcePort: port.source_port,
                        targetPort: port.target_port
                    }))
                );
            }
            setOccupiedPorts(occupiedPorts);
            setIsShowModal(true);
        },

        hideEditLinkModal: () => {
            setIsShowModal(false);
            resetModal();
        }
    }));

    const {saveLinkCallback, bindHistoryEvents} = props;

    const [batchUpgradeForm] = Form.useForm();

    useEffect(() => {
        const handleClickOutside = event => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                handleCancel();
            }
        };

        if (isShowModal) {
            document.addEventListener("mousedown", handleClickOutside);
        } else {
            document.removeEventListener("mousedown", handleClickOutside);
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [isShowModal]);

    useEffect(() => {
        if (isShowModal && sourceSN && targetSN) {
            const fetchPorts = async () => {
                try {
                    setLoading(true);
                    const response = await getPortList({snList: [sourceSN, targetSN]});
                    if (response && response.data) {
                        setSourcePortsList(response.data[sourceSN] || []);
                        setTargetPortsList(response.data[targetSN] || []);
                    }
                } catch (error) {
                    // console.error("Error fetching model ports:", error);
                } finally {
                    setLoading(false);
                }
            };

            fetchPorts();
        }
    }, [isShowModal, sourceSN, targetSN]);

    const checkDuplicatePorts = (ports, index, type) => {
        return ports.some((port, i) => i !== index && port[type] === ports[index][type]);
    };

    const handleSourcePortChange = (value, index) => {
        const updatedPorts = [...portList];
        const updatedErrors = [...sourceErrors];

        updatedPorts[index].sourcePort = value;

        const isSourceDuplicate = checkDuplicatePorts(updatedPorts, index, "sourcePort");
        updatedErrors[index] = isSourceDuplicate ? "This source port has been selected." : "";

        setPortList(updatedPorts);
        setSourceErrors(updatedErrors);
    };

    const handleTargetPortChange = (value, index) => {
        const updatedPorts = [...portList];
        const updatedErrors = [...targetErrors];

        updatedPorts[index].targetPort = value;

        const isTargetDuplicate = checkDuplicatePorts(updatedPorts, index, "targetPort");
        updatedErrors[index] = isTargetDuplicate ? "This target port has been selected." : "";

        setPortList(updatedPorts);
        setTargetErrors(updatedErrors);
    };

    const resetModal = () => {
        batchUpgradeForm.resetFields();
        setPortList([{sourcePort: null, targetPort: null}]);
        setSourceErrors([]);
        setTargetErrors([]);
    };

    const handleSaveLink = async () => {
        try {
            await batchUpgradeForm.validateFields();
            const portInfo = [];
            portList.forEach(port => {
                portInfo.push({source_port: `${port.sourcePort}`, target_port: `${port.targetPort}`, status: "down"});
            });
            if (saveLinkCallback) {
                await saveLinkCallback("add", sourceSN, targetSN, portInfo, true);
            }
            resetModal();
            setIsShowModal(false);
        } catch (error) {
            // console.error("Form validation failed:", error);
        }
    };

    const handleCancel = () => {
        if (saveLinkCallback) {
            saveLinkCallback("cancel");
        }
        resetModal();
        setIsShowModal(false);
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {isEditMode ? "Edit Link" : "Add Link"}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={handleCancel}
            footer={
                <>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button onClick={handleCancel}>Cancel</Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            bindHistoryEvents(handleSaveLink, isEditMode ? "edit-link" : "add-link");
                        }}
                        disabled={portList.some(
                            port =>
                                !port.sourcePort ||
                                !port.targetPort ||
                                sourceErrors.includes("This source port has been selected.") ||
                                targetErrors.includes("This target port has been selected.")
                        )}
                    >
                        {isEditMode ? "Save Link" : "Add Link"}
                    </Button>
                </>
            }
            ref={modalRef}
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 1}}
                wrapperCol={{span: 30}}
                form={batchUpgradeForm}
                style={{minHeight: "267.63px"}}
            >
                <Form.Item label="SN">
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        <span
                            style={{
                                flex: 1,
                                textAlign: "center",
                                color: "#1F1F1F",
                                transition: "none"
                            }}
                        >
                            {sourceSN}
                        </span>
                        <span
                            style={{
                                flex: 1,
                                textAlign: "center",
                                color: "#1F1F1F",
                                transition: "none"
                            }}
                        >
                            {targetSN}
                        </span>
                    </div>
                </Form.Item>
                <Form.Item colon={false}>
                    <div style={{display: "flex", flexDirection: "column"}}>
                        {portList.map((port, index) => (
                            <div
                                style={{
                                    display: "flex",
                                    alignItems: "center"
                                }}
                            >
                                <Form.Item
                                    label="Ports"
                                    colon={false}
                                    style={{flex: 1, marginBottom: "24px"}}
                                    validateStatus={sourceErrors[index] ? "error" : ""}
                                    help={
                                        sourceErrors[index] ? (
                                            <div style={{marginLeft: "10px"}}>{sourceErrors[index]}</div>
                                        ) : null
                                    }
                                >
                                    <Select
                                        placeholder="Please Select Source Port"
                                        style={{width: "254px", marginLeft: "10px"}}
                                        value={port.sourcePort}
                                        onChange={value => handleSourcePortChange(value, index)}
                                    >
                                        {sourcePortsList.map(port => (
                                            <Select.Option
                                                key={port}
                                                value={port}
                                                disabled={occupiedPorts[sourceSN]?.includes(port)}
                                            >
                                                {port}
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>

                                <Form.Item
                                    colon={false}
                                    validateStatus={targetErrors[index] ? "error" : ""}
                                    help={
                                        targetErrors[index] ? (
                                            <div style={{marginLeft: "40px"}}>{targetErrors[index]}</div>
                                        ) : null
                                    }
                                    style={{flex: 1, marginBottom: "24px"}}
                                >
                                    <Select
                                        placeholder="Please Select Target Port"
                                        style={{width: "254px", marginLeft: "40px"}}
                                        value={port.targetPort}
                                        onChange={value => handleTargetPortChange(value, index)}
                                    >
                                        {targetPortsList.map(port => (
                                            <Select.Option
                                                key={port}
                                                value={port}
                                                disabled={occupiedPorts[targetSN]?.includes(port)}
                                            >
                                                {port}
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>

                                {index === 0 && (
                                    <Tooltip title="Add Port" placement="topRight">
                                        <Button
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "24px"
                                            }}
                                            type="link"
                                            icon={<PlusOutlined />}
                                            onClick={() => {
                                                setPortList([...portList, {sourcePort: null, targetPort: null}]);
                                                setSourceErrors([...sourceErrors, ""]);
                                                setTargetErrors([...targetErrors, ""]);
                                            }}
                                        />
                                    </Tooltip>
                                )}

                                {index !== 0 && (
                                    <Tooltip title="Delete Port" placement="topRight">
                                        <Button
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "24px"
                                            }}
                                            type="minus"
                                            icon={<MinusOutlined />}
                                            onClick={() => {
                                                const newPortList = portList.filter((_, i) => i !== index);
                                                setPortList(newPortList);
                                                setSourceErrors(sourceErrors.filter((_, i) => i !== index));
                                                setTargetErrors(targetErrors.filter((_, i) => i !== index));
                                            }}
                                        />
                                    </Tooltip>
                                )}
                            </div>
                        ))}
                    </div>
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default EditLinkModal;
