import {forwardRef, useImperativeHandle, useState} from "react";
import {Button, Divider, Form, Input, message, Modal} from "antd";
import TextArea from "antd/es/input/TextArea";
import {addTopology} from "@/modules-ampcon/apis/monitor_api";

const AddTopoModal = forwardRef((props, ref) => {
    const title = "Create Topology";
    const addTopoNameLabel = "Name";
    const addTopoDescLabel = "Description";

    const {saveCallback} = props;

    useImperativeHandle(ref, () => ({
        showAddTopoModal: () => {
            setIsShowModal(true);
        },
        hideAddTopoModal: () => {
            setIsShowModal(false);
            addTopoForm.resetFields();
        }
    }));

    const [addTopoForm] = Form.useForm();

    const [isShowModal, setIsShowModal] = useState(false);

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                addTopoForm.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            addTopoForm.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={async () => {
                            try {
                                await addTopoForm.validateFields();
                            } catch (errorInfo) {
                                message.error("Please input valid data");
                                return;
                            }
                            addTopology(addTopoForm.getFieldsValue()).then(response => {
                                if (response.status !== 200) {
                                    message.error(response.info);
                                } else {
                                    saveCallback(addTopoForm.getFieldsValue().name);
                                    setIsShowModal(false);
                                    addTopoForm.resetFields();
                                    message.success(response.info);
                                }
                            });
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 5}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={addTopoForm}
                style={{minHeight: "267.23px"}}
            >
                <Form.Item
                    name="name"
                    label={addTopoNameLabel}
                    rules={[
                        {required: true, message: "Please input your topology name!"},
                        {max: 32, message: "Enter a maximum of 32 characters"},
                        {
                            validator: (_, value) => {
                                if (value === "All") {
                                    return Promise.reject(new Error("Please input a valid topology name!"));
                                }
                                if (value.trim() !== value) {
                                    return Promise.reject(
                                        new Error("Topology name should not have leading or trailing spaces.")
                                    );
                                }
                                if (/\s/.test(value)) {
                                    return Promise.reject(new Error("Topology name should not contain spaces."));
                                }
                                if (!/^[\s\w:-]+$/.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            "Topology name can only contain letters, numbers, underscores, hyphens and colons."
                                        )
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    initialValue=""
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="description"
                    rules={[{max: 256, message: "Enter a maximum of 256 characters"}]}
                    label={addTopoDescLabel}
                    initialValue=""
                >
                    <TextArea rows={5} style={{width: "280px"}} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default AddTopoModal;
