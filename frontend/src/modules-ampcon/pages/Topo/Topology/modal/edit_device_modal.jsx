import {forwardRef, useImperativeHandle, useState} from "react";
import {Button, Divider, Form, Input, Modal} from "antd";

const EditDeviceModal = forwardRef((props, ref) => {
    const title = "Edit Device";
    const editDeviceLabel = "Label";
    const editDeviceLayer = "Layer";

    const {saveDeviceInfoCallback} = props;

    useImperativeHandle(ref, () => ({
        showEditDeviceModal: node => {
            setSelectedNode(node);
            const labelValue =
                node.store.data.label && node.store.data.label.trim() !== "" ? node.store.data.label : "PICOS";

            editDeviceForm.setFieldsValue({
                label: labelValue,
                layer: node.store.data.layer
            });
            setIsShowModal(true);
        },
        hideEditDeviceModal: () => {
            setIsShowModal(false);
        }
    }));

    const [editDeviceForm] = Form.useForm();

    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedNode, setSelectedNode] = useState(null);

    return isShowModal ? (
        <Modal
            className="ampcon-small-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 20}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
            }}
            footer={
                <div style={{marginBottom: "4px"}}>
                    <Divider style={{marginTop: 20, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            editDeviceForm
                                .validateFields()
                                .then(values => {
                                    saveDeviceInfoCallback(values, selectedNode);
                                    setIsShowModal(false);
                                })
                                .catch(() => {});
                        }}
                    >
                        Save
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 5}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={editDeviceForm}
            >
                <Form.Item
                    name="label"
                    label={editDeviceLabel}
                    rules={[
                        {
                            required: true
                        },
                        {
                            validator: (_, value) => {
                                if (value && /^\s*$/.test(value)) {
                                    return Promise.reject(new Error("Topology name should not only contain spaces."));
                                }
                                return Promise.resolve();
                            }
                        },
                        {
                            max: 128,
                            message: "Label cannot exceed 128 characters!"
                        }
                    ]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="layer"
                    label={editDeviceLayer}
                    rules={[
                        {
                            validator: (_, value) => {
                                if (/^(0|\+?[1-9]\d*)$/.test(value)) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error("Please enter a valid integer"));
                            }
                        }
                    ]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default EditDeviceModal;
