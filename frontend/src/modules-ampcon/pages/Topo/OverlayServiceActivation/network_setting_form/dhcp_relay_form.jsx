import {Space, Form, message, Radio} from "antd";
import {addGreenSvg, addGreySvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import DhcpRelayModal from "@/modules-ampcon/pages/Topo/OverlayServiceActivation/modal/dhcp_relay_modal";
import Icon from "@ant-design/icons";
import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import style from "@/modules-ampcon/pages/Topo/OverlayServiceActivation/overlay.module.scss";

const DhcpRelayForm = forwardRef(({dhcpData, tableStyle, networkData, setTopoData, enableDhcp, disabled}, ref) => {
    const [isEnable, setIsEnable] = useState(false);
    const [form] = Form.useForm();
    const dhcpRelayModalRef = useRef(null);
    const [dataSource, setDataSource] = useState([]);
    useEffect(() => {
        setIsEnable(!!enableDhcp);
        form.setFieldsValue({enable: enableDhcp ? "enable" : "disable"});
    }, [enableDhcp]);

    useEffect(() => {
        setDataSource(dhcpData || []);
    }, [dhcpData]);

    const updateDataRelay = newData => {
        setTopoData(prev => ({
            ...prev,
            dhcp_relay: newData
        }));
    };

    const handleAddServer = newData => {
        const updatedData = [...dataSource, newData];
        setDataSource(updatedData);
        updateDataRelay(updatedData);
    };
    const handleEditServer = (newData, currentRecord) => {
        const updatedData = dataSource.map(item =>
            item.dhcp_network === currentRecord.dhcp_network && item.dhcp_server === currentRecord.dhcp_server
                ? newData
                : item
        );
        setDataSource(updatedData);
        updateDataRelay(updatedData);
    };

    const handleDeleteServer = record => {
        const updatedData = dataSource.filter(item => item.vlan_id !== record.vlan_id);
        setDataSource(updatedData);
        updateDataRelay(updatedData);
    };
    const handleConfigChange = e => {
        const enabled = e.target.value === "enable";
        setIsEnable(enabled);
        if (enabled) {
            setDataSource([]);
        }

        setTopoData(prev => {
            const safePrev = prev || {};
            return {
                ...safePrev,
                enable_dhcp_relay: enabled,
                dhcp_relay: enabled ? safePrev.dhcp_relay : []
            };
        });
    };

    useImperativeHandle(ref, () => ({
        validate: () => {
            if (isEnable && dataSource.length === 0) {
                Promise.reject(new Error("Please add at least one dhcp relay configuration."));
                message.error("Please add at least one dhcp relay configuration.");
                return false;
            }
            return true;
        },
        resetFormData: data => {
            if (!data) {
                form.resetFields();
                setDataSource([]);
                return;
            }
            form.setFieldsValue({enable: data.enable || "disable"});
            setDataSource(data.list || []);
        }
    }));

    const columns = [
        {
            title: "Network",
            dataIndex: "dhcp_network",
            width: "25%",
            render: (_, record) => {
                return record.dhcp_network;
            },
            sorter: (a, b) => a.dhcp_network.localeCompare(b.dhcp_network)
        },
        {
            title: "DHCP Server",
            dataIndex: "dhcp_server",
            width: "25%",
            render: (_, record) => {
                return record.dhcp_server;
            },
            sorter: (a, b) => a.dhcp_server.localeCompare(b.dhcp_server)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                dhcpRelayModalRef.current.showNetworkModal({mode: "edit"}, record);
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure want to delete?", () => {
                                    try {
                                        handleDeleteServer(record);
                                        message.success("Delete Successful");
                                    } catch (error) {
                                        message.error("Delete Failed");
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h3 className={style.title}>DHCP Relay</h3>
            <DhcpRelayModal
                ref={dhcpRelayModalRef}
                handleEditServer={handleEditServer}
                handleAddServer={handleAddServer}
                dhcpData={dhcpData}
                networkData={networkData}
            />
            <Form form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505}}>
                <Form.Item name="enable" label="Configuration" labelCol={{style: {width: 118}}}>
                    <Radio.Group onChange={handleConfigChange} value={isEnable ? "enable" : "disable"}>
                        <Radio value="enable">Enable</Radio>
                        <Radio value="disable">Disable</Radio>
                    </Radio.Group>
                </Form.Item>
                {isEnable && (
                    <Form.Item style={{marginBottom: "-10px", marginTop: "-18px"}} labelCol={{style: {width: 175}}}>
                        <a
                            style={{
                                display: "inline-flex",
                                alignItems: "center",
                                border: "none",
                                borderRadius: "4px",
                                color: disabled ? "#B3BBC8" : "#14c9bb",
                                pointerEvents: disabled ? "none" : "auto"
                            }}
                            onClick={() => {
                                dhcpRelayModalRef.current.showNetworkModal({mode: "create"});
                            }}
                        >
                            <Icon component={disabled ? addGreySvg : addGreenSvg} style={{marginRight: "8px"}} />
                            DHCP Relay
                        </a>
                    </Form.Item>
                )}
            </Form>
            {dataSource.length > 0 && isEnable && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: dataSource.length <= 10
                        }}
                    />
                </div>
            )}
        </>
    );
});
export default DhcpRelayForm;
