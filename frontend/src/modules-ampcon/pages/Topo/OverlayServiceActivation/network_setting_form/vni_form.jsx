import {Form, Divider, Space, message} from "antd";
import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import Icon from "@ant-design/icons";
import {addGreenSvg, addGreySvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import VniModal from "@/modules-ampcon/pages/Topo/OverlayServiceActivation/modal/vni_modal";
import style from "@/modules-ampcon/pages/Topo/OverlayServiceActivation/overlay.module.scss";

const VniForm = forwardRef(({vniData, networkData, tableStyle, setTopoData, disabled}, ref) => {
    const [form] = Form.useForm();
    const [dataSource, setDataSource] = useState([]);
    const vniModalRef = useRef();
    useEffect(() => {
        if (vniData) {
            setDataSource(vniData);
        }
    }, [vniData]);
    useImperativeHandle(ref, () => ({
        validate: () => {
            if (dataSource.length === 0) {
                Promise.reject(new Error("Please add at least one vni."));
                message.error("Please add at least one vni.");
                return false;
            }
            return true;
        },
        resetFormData: vniArray => {
            form.resetFields();
            if (vniArray) {
                const transformed = vniData.map(item => ({
                    id: item.id,
                    network: item.network
                }));
                setDataSource(transformed);
            } else {
                setDataSource([]);
            }
        }
    }));
    const columns = [
        {
            title: "VNI",
            dataIndex: "id",
            width: "30%",
            sorter: (a, b) => a.id.localeCompare(b.id)
        },
        {
            title: "Networks",
            dataIndex: "network",
            width: "30%",
            render: (_, record) => {
                const ids = Array.isArray(record.network) ? record.network : [record.network];
                return <div style={{whiteSpace: "pre-line"}}>{ids.join("\n")}</div>;
            },
            sorter: (a, b) => {
                const aIds = Array.isArray(a.network) ? a.network.join(",") : a.network;
                const bIds = Array.isArray(b.network) ? b.network.join(",") : b.network;
                return aIds.localeCompare(bIds);
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a onClick={() => vniModalRef.current.showVniModal({mode: "edit"}, record)}>Edit</a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure want to delete?", () => {
                                    try {
                                        handleDeleteServer(record);
                                        message.success("Delete Successful");
                                    } catch (error) {
                                        message.error("Delete Failed");
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];
    const updateVni = newData => {
        setTopoData(prev => ({
            ...prev,
            vnis: newData
        }));
    };

    const handleAddServer = newData => {
        const updatedData = [
            ...dataSource,
            {...newData, network: Array.isArray(newData.network) ? newData.network : [newData.network]}
        ];
        setDataSource(updatedData);
        updateVni(updatedData);
    };
    const handleEditServer = (newData, currentRecord) => {
        const updatedData = dataSource.map(item =>
            item.id === currentRecord.id
                ? {...newData, network: Array.isArray(newData.network) ? newData.network : [newData.network]}
                : item
        );
        setDataSource(updatedData);
        updateVni(updatedData);
    };

    const handleDeleteServer = record => {
        const updatedData = dataSource.filter(item => item.id !== record.id);
        setDataSource(updatedData);
        updateVni(updatedData);
    };
    return (
        <>
            <h3 className={style.title}>VNI</h3>
            <VniModal
                ref={vniModalRef}
                networkData={networkData}
                vniData={dataSource}
                handleAddServer={handleAddServer}
                handleEditServer={handleEditServer}
            />
            <Form form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505, marginBottom: "-10px"}}>
                <Form.Item style={{marginBottom: "0px"}} labelCol={{style: {width: 175}}}>
                    <a
                        style={{
                            display: "inline-flex",
                            alignItems: "center",
                            border: "none",
                            borderRadius: "4px",
                            color: disabled ? "#B3BBC8" : "#14c9bb",
                            pointerEvents: disabled ? "none" : "auto"
                        }}
                        onClick={() => {
                            vniModalRef.current.showVniModal({mode: "create"});
                        }}
                    >
                        <Icon component={disabled ? addGreySvg : addGreenSvg} style={{marginRight: "8px"}} />
                        VNI
                    </a>
                </Form.Item>
            </Form>
            {dataSource.length > 0 && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: dataSource.length <= 10
                        }}
                    />
                </div>
            )}
            <Divider style={{marginTop: "23px", marginBottom: "3px"}} />
        </>
    );
});
export default VniForm;
