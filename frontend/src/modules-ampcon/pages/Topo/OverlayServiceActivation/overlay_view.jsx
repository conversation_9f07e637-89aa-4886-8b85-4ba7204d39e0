import {useState, useRef, useEffect} from "react";
import {useWatch} from "antd/es/form/Form";
import {Button, Card, Form, Flex, Divider, message, Spin, Select, AutoComplete} from "antd";
import VrfForm from "@/modules-ampcon/pages/Topo/OverlayServiceActivation/network_setting_form/vrf_form";
import VniForm from "@/modules-ampcon/pages/Topo/OverlayServiceActivation/network_setting_form/vni_form";
import DhcpRelayForm from "@/modules-ampcon/pages/Topo/OverlayServiceActivation/network_setting_form/dhcp_relay_form";
import {viewSiteTopo, saveConfig, getOverlayData} from "@/modules-ampcon/apis/campus_blueprint_api";
const OverlayView = () => {
    const [form] = Form.useForm();
    const vrfFormRef = useRef();
    const vniFormRef = useRef();
    const dhcpRelayFormRef = useRef();
    const [fabricOptions, setFabricOptions] = useState([]);
    const [topoData, setTopoData] = useState(null);
    const [initialTopoData, setInitialTopoData] = useState(null);
    const [campusFabricData, setCampusFabricData] = useState([]);
    const [maxContainerHeight, setMaxContainerHeight] = useState(null);
    const tableStyle = {
        maxWidth: "45%",
        minWidth: "650px"
    };
    const [loading, setLoading] = useState(false);
    const fabricValue = useWatch("fabric", form);
    const isFabricValid = fabricOptions.some(opt => opt.label === fabricValue);
    function normalizeNetworks(res) {
        return {
            ...res,
            vlans: Object.entries(res.vlans || {}).map(([name, vlan]) => ({
                name,
                ...vlan
            })),
            vrfs: Object.entries(res.vrfs || {}).map(([name, vrf]) => ({
                name,
                ...vrf
            })),
            vnis: Object.entries(res.vnis || {}).map(([id, vni]) => ({
                id,
                ...vni
            }))
        };
    }
    const fetchFabricOptions = (searchValue = "", setDefault = false) => {
        getOverlayData(searchValue)
            .then(res => {
                if (res.status === 200) {
                    const filteredOptions = res.data.map(item => ({
                        label: item.name,
                        value: item.id
                    }));
                    setFabricOptions(filteredOptions);

                    if (setDefault && filteredOptions.length > 0) {
                        const defaultFabricName = filteredOptions[0].label;
                        form.setFieldsValue({fabric: defaultFabricName});
                        fetchSiteTopo(defaultFabricName, filteredOptions);
                    } else if (filteredOptions.length === 0) {
                        setTopoData(null);
                        setInitialTopoData(null);
                    }
                } else {
                    message.error("Failed to load fabric options");
                }
            })
            .catch(error => {
                console.error("Error fetching site topology list:", error);
                message.error("Failed to load fabric options");
            });
    };
    useEffect(() => {
        fetchFabricOptions("", true);
        const handleResize = () => {
            setMaxContainerHeight("calc(100vh - 200px)");
        };
        handleResize();
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);
    const fetchSiteTopo = (label, options = fabricOptions) => {
        const matched = options.find(opt => opt.label === label);
        if (!matched) {
            setTopoData(null);
            setInitialTopoData(null);
            return;
        }
        const fabricId = matched.value;
        setLoading(true);
        viewSiteTopo(fabricId)
            .then(res => {
                setCampusFabricData(res);
                const normalized = normalizeNetworks(res.networks);
                setTopoData(normalized);
                setInitialTopoData(JSON.parse(JSON.stringify(normalized)));
            })
            .catch(error => {
                console.error("Error fetching site topo:", error);
                message.error("Failed to load site topo");
                form.resetFields();
                setTopoData(null);
                setInitialTopoData(null);
            })
            .finally(() => {
                setLoading(false);
            });
    };
    const transformNetworks = data => {
        const {vlans = [], vnis = [], vrfs = [], ...rest} = data;

        const vlanObj = vlans.reduce((acc, {name, ...rest}) => {
            acc[name] = rest;
            return acc;
        }, {});

        const vniObj = vnis.reduce((acc, {id, ...rest}) => {
            acc[id] = rest;
            return acc;
        }, {});

        const vrfObj = vrfs.reduce((acc, {name, ...rest}) => {
            acc[name] = rest;
            return acc;
        }, {});

        return {
            vlans: vlanObj,
            vnis: vniObj,
            vrfs: vrfObj,
            ...rest
        };
    };
    const handleSave = async () => {
        const isdhcpRelayValid = await dhcpRelayFormRef.current?.validate();
        const isVniValid = await vniFormRef.current?.validate();
        const isVrfValid = await vrfFormRef.current?.validate();
        if (!isdhcpRelayValid || !isVniValid || !isVrfValid) {
            return;
        }
        const newData = transformNetworks(topoData);
        const applyData = {
            site_id: campusFabricData.site_id,
            config_id: campusFabricData.site_config_id,
            topology_name: campusFabricData.topology_name,
            topology: campusFabricData.topology,
            networks: newData,
            type: "ip-clos",
            nodes: {
                border: campusFabricData.nodes.border.map(node => {
                    return {
                        switch_sn: node.switch_sn,
                        router_id: node.router_id,
                        mac_addr: node.mac_addr,
                        other_ip_config: node.other_ip_config,
                        links: node.links,
                        label: node.label,
                        mgt_ip: node.mgt_ip,
                        model: node.model,
                        id: node.id,
                        status: node.status
                    };
                }),
                core: campusFabricData.nodes.core.map(node => {
                    return {
                        switch_sn: node.switch_sn,
                        router_id: node.router_id,
                        mac_addr: node.mac_addr,
                        other_ip_config: node.other_ip_config,
                        links: node.links,
                        label: node.label,
                        mgt_ip: node.mgt_ip,
                        model: node.model,
                        id: node.id,
                        status: node.status
                    };
                }),
                pods: campusFabricData.nodes.pods.map((pod, index) => {
                    return {
                        pod_name: pod?.podName || pod?.pod_name,
                        pod_index: index,
                        ...(pod?.pod_id ? {pod_id: pod.pod_id} : {}),
                        distribution: pod.distribution.map(node => {
                            return {
                                switch_sn: node.switch_sn,
                                router_id: node.router_id,
                                mac_addr: node.mac_addr,
                                other_ip_config: node.other_ip_config,
                                links: node.links,
                                label: node.label,
                                mgt_ip: node.mgt_ip,
                                model: node.model,
                                id: node.id,
                                status: node.status
                            };
                        }),
                        access: pod.access.map(node => {
                            return {
                                switch_sn: node.switch_sn,
                                router_id: node.router_id,
                                mac_addr: node.mac_addr,
                                other_ip_config: node.other_ip_config,
                                links: node.links,
                                label: node.label,
                                mgt_ip: node.mgt_ip,
                                model: node.model,
                                id: node.id,
                                status: node.status
                            };
                        })
                    };
                })
            }
        };
        console.log(applyData);
        try {
            const res = await saveConfig(applyData);
            if (res.status === 200) {
                message.success("Success");
            }
        } catch (error) {
            console.error(error);
        }
    };
    const handleCancel = () => {
        if (initialTopoData) {
            setTopoData(initialTopoData);
        }
        vrfFormRef.current?.resetFormData(initialTopoData.vrfs || []);
        vniFormRef.current?.resetFormData(initialTopoData.vnis || []);
        dhcpRelayFormRef.current?.resetFormData({
            list: initialTopoData.dhcp_relay || [],
            enable: initialTopoData.enable_dhcp_relay
        });
    };
    return (
        <>
            <Spin spinning={loading} tip="Loading..." fullscreen />
            <Card style={{display: "flex", flexDirection: "column", flex: 1}}>
                <Flex
                    vertical
                    flex={1}
                    style={{height: "100%", maxHeight: maxContainerHeight, overflowY: "scroll", paddingRight: "5px"}}
                >
                    <h2 style={{margin: "8px 0 20px"}}>Overlay Service Activation</h2>
                    <h3 style={{margin: "8px 0 20px", fontSize: "18px"}}>Fabric</h3>
                    <Form labelAlign="left" labelCol={{flex: "120px"}} wrapperCol={{flex: "280px"}} form={form}>
                        <Form.Item
                            label="Fabric"
                            name="fabric"
                            rules={[{required: true, message: "Please select fabric!"}]}
                        >
                            <AutoComplete
                                style={{width: "100%"}}
                                placeholder="Select or input Fabric"
                                value={fabricValue}
                                options={fabricOptions.map(opt => ({
                                    label: opt.label,
                                    value: opt.label
                                }))}
                                onSelect={label => {
                                    form.setFieldsValue({fabric: label});
                                    fetchSiteTopo(label);
                                }}
                                onSearch={inputText => {
                                    fetchFabricOptions(inputText);
                                }}
                            />
                        </Form.Item>
                    </Form>
                    <Divider style={{marginTop: "1px", marginBottom: "1px"}} />
                    <VrfForm
                        ref={vrfFormRef}
                        networkData={topoData?.vlans || []}
                        vrfData={topoData?.vrfs || []}
                        setTopoData={setTopoData}
                        tableStyle={tableStyle}
                        disabled={!isFabricValid}
                    />
                    <VniForm
                        ref={vniFormRef}
                        networkData={topoData?.vlans || []}
                        vniData={topoData?.vnis || []}
                        setTopoData={setTopoData}
                        tableStyle={tableStyle}
                        disabled={!isFabricValid}
                    />
                    <DhcpRelayForm
                        ref={dhcpRelayFormRef}
                        networkData={topoData?.vlans || []}
                        dhcpData={topoData?.dhcp_relay || []}
                        enableDhcp={topoData?.enable_dhcp_relay}
                        setTopoData={setTopoData}
                        tableStyle={tableStyle}
                        disabled={!isFabricValid}
                    />
                </Flex>
                <div
                    style={{
                        borderTop: "1px solid #F0F0F0",
                        margin: "12px auto 0",
                        position: "absolute",
                        bottom: "63px",
                        left: 0,
                        right: 0
                    }}
                />
                <Flex
                    style={{
                        marginTop: 24,
                        position: "absolute",
                        bottom: "16px",
                        right: "10px",
                        flexDirection: "row-reverse",
                        width: "100%",
                        paddingRight: "24px",
                        paddingTop: "16px",
                        gap: "16px"
                    }}
                >
                    <Button key="ok" type="primary" onClick={handleSave} disabled={!isFabricValid}>
                        Apply
                    </Button>
                    <Button key="cancel" onClick={handleCancel}>
                        Cancel
                    </Button>
                </Flex>
            </Card>
        </>
    );
};

export default OverlayView;
