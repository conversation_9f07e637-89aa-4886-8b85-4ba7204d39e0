import {<PERSON><PERSON>, Card, Space, message} from "antd";
import {AmpConCustomTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";
import Icon from "@ant-design/icons";
import {addSvg, importSvg} from "@/utils/common/iconSvg";
import {useEffect, useRef} from "react";
import {
    getSwitchTemplateList,
    deleteTemplate,
    downloadTemplate,
    viewTemplate
} from "@/modules-ampcon/apis/campus_blueprint_api";
import styles from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CopyModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/copy_modal";
import ImporModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/import_modal";
import {useNavigate} from "react-router-dom";
import CreateNameModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/create_name_modal";

const SwitchTemplatesTbleView = () => {
    const tableRef = useRef();
    const fetchIntervalRef = useRef();
    const navigate = useNavigate();
    const matchFieldsList = [{name: "topology_name", matchMode: "fuzzy"}];
    const searchFieldsList = ["topology_name"];
    const copyModalRef = useRef();
    const importModalRef = useRef();
    const CreateNameModalRef = useRef();
    const delete_fabric = record => {
        confirmModalAction("Are you sure want to delete this template?", () => {
            deleteTemplate(record.id).then(res => {
                if (res.status === 200) {
                    message.success(res.msg);
                    tableRef.current.refreshTable();
                } else {
                    message.error(res.msg);
                }
            });
        });
    };
    const columns = [
        createColumnConfig("Template", "name", null, "", 250),
        createColumnConfig("Switches", "switch_num", null, "", 250),
        createColumnConfig("Create Time", "create_time", null, "", 250),
        {
            title: "Operation",
            width: 500,
            render: (_, record) => (
                <div>
                    <Space size="middle" className={styles.actionLink}>
                        <a
                            onClick={async () => {
                                try {
                                    const res = await viewTemplate(record.id);
                                    if (res?.status === 200) {
                                        navigate("/network_design/switch_templates/view", {
                                            state: {
                                                id: record.id,
                                                name: record.name,
                                                templateData: res.data
                                            }
                                        });
                                    } else {
                                        message.error(res?.msg || "Error in viewTemplate");
                                    }
                                } catch (error) {
                                    message.error("Error in viewTemplate");
                                }
                            }}
                        >
                            View
                        </a>
                        <a
                            onClick={() => {
                                copyModalRef.current.showCreateCopyModal(record.id, record.name, () => {
                                    tableRef.current.refreshTable();
                                });
                            }}
                        >
                            Copy
                        </a>
                        <a
                            onClick={async () => {
                                try {
                                    const res = await downloadTemplate(record.id);
                                    const blob = new Blob([res], {type: "application/json"});
                                    const url = window.URL.createObjectURL(blob);
                                    const link = document.createElement("a");
                                    link.href = url;
                                    link.download = `${record.name}.json`;
                                    link.click();
                                    window.URL.revokeObjectURL(url);
                                } catch (err) {
                                    message.error("Download failed");
                                    console.error(err);
                                }
                            }}
                        >
                            Download
                        </a>
                        <a
                            onClick={() => {
                                navigate("/network_design/switch_templates/template_deployment_status", {
                                    state: {
                                        id: record.id
                                    }
                                });
                            }}
                        >
                            Template Deployment Status
                        </a>
                        <a onClick={() => delete_fabric(record)}>Delete</a>
                    </Space>
                </div>
            )
        }
    ];
    useEffect(() => {
        fetchIntervalRef.current = setInterval(() => {
            if (tableRef.current) {
                tableRef.current.refreshTable();
            }
        }, 60000);
        return () => {
            if (fetchIntervalRef.current) {
                clearInterval(fetchIntervalRef.current);
            }
        };
    }, []);
    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{margin: "8px 0 20px"}}>Switch Templates</h2>
            <AmpConCustomTable
                ref={tableRef}
                columns={columns}
                fetchAPIInfo={getSwitchTemplateList}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <>
                        <Button
                            type="primary"
                            onClick={() => {
                                CreateNameModalRef.current.showCreateNameModal();
                            }}
                        >
                            <Icon component={addSvg} />
                            Template
                        </Button>
                        <Button
                            onClick={() => {
                                importModalRef.current.showCreateImportModal();
                            }}
                        >
                            <Icon component={importSvg} />
                            Import
                        </Button>
                    </>
                }
            />
            <CopyModal ref={copyModalRef} />
            <ImporModal ref={importModalRef} />
            <CreateNameModal ref={CreateNameModalRef} />
        </Card>
    );
};

export default SwitchTemplatesTbleView;
