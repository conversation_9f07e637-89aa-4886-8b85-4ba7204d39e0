import {Button, Form, message, Divider, Input, Modal} from "antd";
import {useState, forwardRef, useImperativeHandle, useEffect} from "react";

const NetworkModal = forwardRef(({handleAddNetwork, handleEditNetwork, serverData, vrfData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");
    const [existingData, setExistingData] = useState([]);
    const [currentRecord, setCurrentRecord] = useState(null);
    const [title, setTitle] = useState("Create Network");
    useImperativeHandle(ref, () => ({
        showNetworkModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                setTitle("Edit");
            }
            if (mode === "view") {
                setTitle("View");
            }
            if (mode === "create") {
                setTitle("Create Network");
            }
            if (mode === "edit" || mode === "view") {
                form.setFieldsValue(record);
                setCurrentRecord(record);
            } else {
                setCurrentRecord(null);
            }
            setIsShowModal(true);
        }
    }));
    const validateNameUnique = (_, value) => {
        if (!value) return Promise.resolve();
        if (existingData.length === 0) return Promise.resolve();
        const isDuplicate = existingData.some(item => {
            if (mode === "edit" && item.id === currentRecord?.id) return false;
            return item.name === value;
        });

        return isDuplicate ? Promise.reject(new Error("The VLAN name already exists.")) : Promise.resolve();
    };
    const validateNameNotDuplicateWithVrf = (_, value) => {
        if (!value) return Promise.resolve();
        if (!vrfData || vrfData.length === 0) return Promise.resolve();
        const isDuplicate = vrfData.networks.some(vrf => vrf.name === value);
        return isDuplicate
            ? Promise.reject(new Error("The network name already exists as a VRF name."))
            : Promise.resolve();
    };
    const validateIdUnique = (_, value) => {
        if (!value) return Promise.resolve();
        if (existingData.length === 0) return Promise.resolve();
        const isDuplicate = existingData.some(item => {
            if (mode === "edit" && item.id === currentRecord?.id) return false;
            return item.id === value;
        });
        return isDuplicate ? Promise.reject(new Error("The VLAN ID already exists.")) : Promise.resolve();
    };

    useEffect(() => {
        if (isShowModal && serverData) {
            setExistingData(serverData);
        }
    }, [isShowModal, serverData]);

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                mode === "view" ? null : (
                    <div>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                form.submit();
                            }}
                        >
                            Apply
                        </Button>
                    </div>
                )
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            handleAddNetwork(form.getFieldsValue());
                            message.success("Network created successfully");
                        }
                        if (mode === "edit") {
                            handleEditNetwork(form.getFieldsValue());
                            message.success("Network edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the network");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="name"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input name"
                        },
                        {
                            pattern: /^[A-Za-z0-9\-._@=#]{1,11}$/,
                            message:
                                "The network name must be a string of 1–11 characters. Only letters, digits, and special characters (- . _ @ = #) are allowed. Spaces are not allowed."
                        },
                        {validator: validateNameUnique},
                        {validator: validateNameNotDuplicateWithVrf}
                    ]}
                    label="Name"
                    labelCol={{flex: "0 0 74px", style: {textAlign: "left"}}}
                    wrapperCol={{flex: "0 0 312px", style: {paddingLeft: 32}}}
                >
                    <Input style={{width: "280px", color: "#212529"}} disabled={mode === "view" || mode === "edit"} />
                </Form.Item>
                <Form.Item
                    name="id"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input id"
                        },
                        {
                            validator: (_, value) => {
                                if (
                                    Number.isInteger(Number(value)) &&
                                    value > 1 &&
                                    value <= 4094 &&
                                    /^(0|[1-9]\d*)$/.test(value)
                                ) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error(""));
                            },
                            message: "The VLAN ID must be an integer between 2 and 4094."
                        },
                        {validator: validateIdUnique}
                    ]}
                    label="VLAN ID"
                    labelCol={{flex: "0 0 74px", style: {textAlign: "left"}}}
                    wrapperCol={{flex: "0 0 312px", style: {paddingLeft: 32}}}
                >
                    <Input style={{width: "280px", color: "#212529"}} disabled={mode === "view"} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default NetworkModal;
