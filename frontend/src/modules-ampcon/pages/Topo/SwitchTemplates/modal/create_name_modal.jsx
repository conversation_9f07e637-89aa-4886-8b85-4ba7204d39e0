import {Button, Form, Input, Modal, Flex, Divider} from "antd";
import {useState, useImperativeHandle, forwardRef} from "react";
import {useNavigate} from "react-router-dom";
import {checkDuplicateName} from "@/modules-ampcon/apis/campus_blueprint_api";
import debounce from "lodash/debounce";
const CreateNameModal = forwardRef((props, ref) => {
    const navigate = useNavigate();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
        showCreateNameModal: () => {
            form.resetFields();
            setIsModalOpen(true);
        }
    }));
    const checkNameApiDebounced = debounce(async (name, resolve, reject) => {
        try {
            const res = await checkDuplicateName(name, null);
            if (res.status === 200) {
                resolve();
            } else if (res.status === 400) {
                reject(new Error(res.info || "The template name already exists!"));
            } else {
                reject(new Error("The template name is invalid."));
            }
        } catch (e) {
            reject(new Error("The template name is invalid."));
        }
    }, 100);

    const validateName = (_, value) => {
        if (!value) return Promise.resolve();
        return new Promise((resolve, reject) => {
            checkNameApiDebounced(value, resolve, reject);
        });
    };
    const handleApply = async () => {
        try {
            const values = await form.validateFields();
            navigate(`/network_design/switch_templates/create`, {
                state: {
                    name: values.name
                }
            });
            setIsModalOpen(false);
            form.resetFields();
        } catch (error) {
            console.error("Validation failed:", error);
        }
    };
    return isModalOpen ? (
        <Modal
            title={
                <div>
                    Create Template
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onOk={() => {}}
            onCancel={() => {
                form.resetFields();
                setIsModalOpen(false);
            }}
            className="ampcon-middle-modal"
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsModalOpen(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleApply}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form
                labelAlign="left"
                labelCol={{flex: "132px"}}
                wrapperCol={{flex: "280px"}}
                form={form}
                validateTrigger={["onChange", "onSubmit"]}
            >
                <Form.Item
                    name="name"
                    label="Template Name"
                    rules={[
                        {required: true, message: "The template name is required."},
                        {
                            max: 64,
                            message: "The template name cannot exceed 64 characters."
                        },
                        {
                            validator: (_, value) => {
                                if (/\s/.test(value)) {
                                    return Promise.reject(new Error("The template name cannot contain spaces."));
                                }
                                return Promise.resolve();
                            }
                        },
                        {validator: validateName}
                    ]}
                >
                    <Input />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default CreateNameModal;
