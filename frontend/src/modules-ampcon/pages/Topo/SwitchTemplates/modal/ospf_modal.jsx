import {Button, Form, message, Divider, Input, Modal, Select, Space} from "antd";
import {useState, forwardRef, useImperativeHandle, useEffect} from "react";
import {PlusOutlined, MinusOutlined} from "@ant-design/icons";

const OspfModal = forwardRef(({handleAddServer, handleEditServer, ospData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");
    const [type, setType] = useState([
        {label: "Default", value: "Default"},
        {label: "Stub", value: "Stub"},
        {label: "NSSA", value: "NSSA"}
    ]);
    const [existingConfigs, setExistingConfigs] = useState([]);
    const [currentRecord, setCurrentRecord] = useState(null);
    const ipv4NetStrictPattern =
        /^(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\/(\d|[12]\d|3[0-2])$/;

    const validateAreaId = () => ({
        validator(_, value) {
            if (!value) return Promise.reject(new Error("Please input the Area ID."));
            const ipv4Pattern = /^((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$/;
            const isValidIp = ipv4Pattern.test(value);
            const isValidNumber =
                /^\d+$/.test(value) && +value >= 0 && +value <= 4294967295 && (value === "0" || !/^0\d+$/.test(value));
            return isValidIp || isValidNumber
                ? Promise.resolve()
                : Promise.reject(
                      new Error(
                          "The value can be in IPv4 dotted decimal format or an integer ranging from 0 to 4294967295."
                      )
                  );
        }
    });
    const validateAreaType = (_, value) => {
        const areaId = form.getFieldValue("id");
        if ((areaId === "0.0.0.0" || areaId === 0 || areaId === "0") && (value === "Stub" || value === "NSSA")) {
            return Promise.reject(
                new Error(
                    "If the Area ID that you entered is 0.0.0.0 or 0, you can only select Default as the Area Type."
                )
            );
        }
        return Promise.resolve();
    };
    useImperativeHandle(ref, () => ({
        showOspfModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                let ipv4List = [];
                const typeMap = {
                    default: "Default",
                    stub: "Stub",
                    nssa: "NSSA"
                };
                const displayType = typeMap[record.type.toLowerCase()] || record.type;
                if (record.ipv4) {
                    ipv4List = Array.isArray(record.ipv4) ? record.ipv4 : record.ipv4.split(",");
                }
                form.setFieldsValue({
                    ...record,
                    ipv4List,
                    type: displayType
                });
                setCurrentRecord(record);
            } else {
                form.setFieldsValue({
                    ipv4List: [""]
                });
                setCurrentRecord(null);
            }
            setIsShowModal(true);
        }
    }));
    useEffect(() => {
        if (isShowModal && ospData) {
            setExistingConfigs(
                ospData.areas.flatMap(item => {
                    if (Array.isArray(item.ipv4)) {
                        return item.ipv4;
                    }
                    if (typeof item.ipv4 === "string") {
                        return item.ipv4
                            .split(",")
                            .map(ip => ip.trim())
                            .filter(Boolean);
                    }
                    return [];
                })
            );
        }
    }, [isShowModal, ospData]);
    // 验证规则
    const createRequiredOnCountRule = form => ({
        validateTrigger: "onBlur",
        validator: (_, value) => {
            const list = form.getFieldValue("ipv4List") || [];
            if (list.length === 1 && !value) return Promise.resolve();
            if (list.length > 1 && !value)
                return Promise.reject(new Error("Please input the IPv4 address with prefix length."));
            return Promise.resolve();
        }
    });

    const createFormatAndDupRule = (form, existingConfigs, currentRecord, mode) => ({
        validateTrigger: ["onChange", "onBlur"],
        validator: (_, value) => {
            if (!value) return Promise.resolve();
            const ipv4Regex =
                /^(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\/(\d|[12]\d|3[0-2])$/;
            if (!ipv4Regex.test(value.trim()))
                return Promise.reject(new Error("Please enter a valid IPv4 address with prefix length."));

            const list = form.getFieldValue("ipv4List") || [];
            const count = list.filter(ip => ip?.trim() === value.trim()).length;
            if (count > 1) return Promise.reject(new Error("The IPv4/Prefixlen must be unique."));
            const trimmed = value.trim();
            const existsInExisting = existingConfigs.some(existingIp => {
                if (mode === "edit" && currentRecord?.ipv4) {
                    const currentList = Array.isArray(currentRecord.ipv4)
                        ? currentRecord.ipv4
                        : currentRecord.ipv4.split(",").map(ip => ip.trim());
                    if (currentList.includes(trimmed)) return false;
                }
                return existingIp === trimmed;
            });
            if (existsInExisting) return Promise.reject(new Error("The IPv4/Prefixlen already exists."));

            return Promise.resolve();
        }
    });

    const revalidateNonEmpty = () => {
        const list = form.getFieldValue("ipv4List") || [];
        const names = list.map((v, i) => (v ? ["ipv4List", i] : null)).filter(Boolean);
        if (names.length) {
            form.validateFields(names).catch(() => {});
        }
    };

    const handleChange = () => {
        setTimeout(revalidateNonEmpty, 0);
    };

    const withRemove = (removeFn, fieldName) => () => {
        removeFn(fieldName);
        setTimeout(() => {
            const list = form.getFieldValue("ipv4List") || [];
            if (list.length === 1) {
                form.setFields([{name: ["ipv4List", 0], errors: []}]);
            } else {
                revalidateNonEmpty();
            }
        }, 0);
    };
    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {mode === "create" ? "Create OSPF" : "Edit"}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            form.submit();
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    const values = form.getFieldsValue();
                    const ipv4List = (values.ipv4List || []).filter(ip => ip?.trim());
                    const {ipv4List: _, ...rest} = values;
                    const finalData = {
                        ...rest,
                        ipv4: ipv4List
                    };
                    try {
                        if (mode === "create") {
                            handleAddServer(finalData);
                            message.success("Ospf created successfully");
                        }
                        if (mode === "edit") {
                            handleEditServer(finalData, currentRecord);
                            message.success("Ospf edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the ospf");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
                onValuesChange={changedValues => {
                    if ("id" in changedValues && form.getFieldValue("type")) {
                        form.validateFields(["type"]);
                    }
                    if ("type" in changedValues && form.getFieldValue("id") === "0.0.0.0") {
                        form.validateFields(["type"]);
                    }
                }}
            >
                <Form.List name="ipv4List">
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map((field, index) => (
                                <Form.Item
                                    required
                                    key={field.key}
                                    label="IPv4/Prefixlen"
                                    labelCol={{span: 6}}
                                    wrapperCol={{span: 17}}
                                    style={{marginBottom: 24}}
                                    validateFirst
                                >
                                    <Space align="baseline">
                                        <Form.Item
                                            validateFirst
                                            {...field}
                                            name={[field.name]}
                                            validateTrigger={["onBlur", "onChange"]}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: "Please input the IPv4 address with prefix length."
                                                },
                                                createRequiredOnCountRule(form),
                                                createFormatAndDupRule(form, existingConfigs, currentRecord, mode),
                                                {
                                                    validator: (_, value) => {
                                                        if (!value) return Promise.resolve();
                                                        if (/\s/.test(value)) {
                                                            return Promise.reject(
                                                                new Error("The IPv4/Prefixlen cannot contain spaces.")
                                                            );
                                                        }
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                            noStyle
                                        >
                                            <Input
                                                style={{width: 280}}
                                                placeholder="XXX.XXX.XXX.XXX/XX"
                                                onChange={handleChange}
                                            />
                                        </Form.Item>
                                        {index === 0 ? (
                                            <Button
                                                type="link"
                                                icon={<PlusOutlined />}
                                                onClick={() => add()}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF"
                                                }}
                                            />
                                        ) : (
                                            <Button
                                                type="link"
                                                danger
                                                icon={<MinusOutlined />}
                                                onClick={withRemove(remove, field.name)}
                                                style={{backgroundColor: "transparent", color: "#BFBFBF"}}
                                            />
                                        )}
                                    </Space>
                                </Form.Item>
                            ))}
                        </>
                    )}
                </Form.List>

                <Form.Item
                    name="id"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input the Area ID."
                        },
                        validateAreaId()
                        // validateCombinationUnique()
                    ]}
                    label="Area ID"
                    initialValue=""
                >
                    <Input style={{width: "280px"}} placeholder="0-4294967295/XXX.XXX.XXX.XXX" />
                </Form.Item>
                <Form.Item
                    name="type"
                    rules={[{required: true, message: "Please select the Area Type."}, {validator: validateAreaType}]}
                    label="Area Type"
                >
                    <Select style={{width: "280px"}} options={type} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default OspfModal;
