import {Button, Form, message, Divider, Input, Modal} from "antd";
import {useState, forwardRef, useImperativeHandle, useEffect} from "react";

const StaticRouteModal = forwardRef(({handleAddServer, handleEditServer, staticRouteData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");
    const [currentRecord, setCurrentRecord] = useState(null);
    const ipv4NetStrictPattern =
        /^(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\/(\d|[12]\d|3[0-2])$/;

    const isValidIPv4Net = net => ipv4NetStrictPattern.test(net.trim());
    const ipv4StrictPattern =
        /^(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$/;

    const isValidIPv4 = ip => ipv4StrictPattern.test(ip.trim());
    useEffect(() => {}, []);
    useImperativeHandle(ref, () => ({
        showStaticRouteModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                form.setFieldsValue(record);
                setCurrentRecord(record);
            }
            setIsShowModal(true);
        }
    }));

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {mode === "create" ? "Create Static Route" : "Edit"}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            form.submit();
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    const values = form.getFieldsValue();
                    const {nexthop} = values;
                    let parsedNextHop = [];
                    if (typeof nexthop === "string") {
                        parsedNextHop = nexthop
                            .split(",")
                            .map(ip => ip.trim())
                            .filter(Boolean);
                    } else if (Array.isArray(nexthop)) {
                        parsedNextHop = nexthop.map(ip => ip.trim()).filter(Boolean);
                    }
                    const finalValues = {
                        ...values,
                        nexthop: parsedNextHop
                    };
                    try {
                        if (mode === "create") {
                            handleAddServer(finalValues);
                            message.success("Static Route created successfully");
                        }
                        if (mode === "edit") {
                            handleEditServer(finalValues, currentRecord);
                            message.success("Static Route edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the static route");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="destination"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input the static route destination."
                        },
                        {
                            pattern: /^\S+$/,
                            message: "The static route destination cannot contain spaces."
                        },
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve();
                                return isValidIPv4Net(value)
                                    ? Promise.resolve()
                                    : Promise.reject(
                                          new Error(
                                              "The static route destination must be in IPv4 format with prefix length."
                                          )
                                      );
                            }
                        },
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve();
                                const isDuplicate = staticRouteData?.routes?.some(item => {
                                    if (mode === "edit" && item.destination === currentRecord?.destination)
                                        return false;
                                    return item.destination === value;
                                });
                                return isDuplicate
                                    ? Promise.reject(new Error("The static route destination already exists."))
                                    : Promise.resolve();
                            }
                        }
                    ]}
                    label="Destination"
                >
                    <Input style={{width: "280px"}} placeholder="XXX.XXX.XXX.XXX/XX" />
                </Form.Item>
                <Form.Item
                    name="nexthop"
                    validateFirst
                    rules={[
                        {required: true, message: "Please input the static route next hop."},
                        {
                            pattern: /^[^ ]+$/,
                            message: "The static route next hop cannot contain spaces."
                        },
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve();
                                let rawIps = [];
                                if (typeof value === "string") {
                                    rawIps = value.split(",");
                                } else if (Array.isArray(value)) {
                                    rawIps = value;
                                } else {
                                    return Promise.reject(
                                        new Error(
                                            "Enter one or more IPv4 addresses for the next hop, separated by commas."
                                        )
                                    );
                                }
                                const hasEmpty = rawIps.some(ip => ip.trim() === "");
                                if (hasEmpty) {
                                    return Promise.reject(
                                        new Error("The IP address for the next hop cannot be empty.")
                                    );
                                }
                                const ips = rawIps.map(ip => ip.trim());
                                const ipSet = new Set(ips);

                                const invalid = ips.find(ip => !isValidIPv4(ip));
                                if (invalid)
                                    return Promise.reject(
                                        new Error(
                                            "Enter one or more IPv4 addresses for the next hop, separated by commas."
                                        )
                                    );
                                if (ipSet.size !== ips.length) {
                                    return Promise.reject(new Error("The IP address for the next hop must be unique."));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    label="Next Hop"
                    required
                >
                    <Input style={{width: "280px"}} placeholder="XXX.XXX.XXX.XX,XXX.XXX.XXX.XXX" />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default StaticRouteModal;
