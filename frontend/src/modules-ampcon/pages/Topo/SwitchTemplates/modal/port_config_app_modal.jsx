import {Form, Divider, Input, Modal, Button, Select, message} from "antd";
import {useState, forwardRef, useImperativeHandle, useEffect, useMemo} from "react";

const PortConfigAppModal = forwardRef(({portConfigAppData, portConfigData, handleAddServer, handleEditServer}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const {Option} = Select;
    const [mode, setMode] = useState("");
    const [currentRecord, setCurrentRecord] = useState(null);
    const normalizePorts = input => {
        if (typeof input !== "string") return [];
        return input
            .split(",")
            .map(p => p.trim())
            .filter(Boolean)
            .flatMap(p => expandPortRange(p));
    };
    const existingPorts = useMemo(() => {
        const allPorts = new Set();
        (portConfigAppData || []).forEach(item => {
            const portValue = String(item?.port ?? "");
            portValue
                .split(",")
                .map(p => p.trim())
                .filter(Boolean)
                .flatMap(p => {
                    const rangeMatch = p.match(/^([A-Za-z]+-\d+\/\d+\/)(\d+)-(\d+)$/);
                    if (rangeMatch) {
                        const [_, prefix, start, end] = rangeMatch;
                        return Array.from({length: end - start + 1}, (_, i) => `${prefix}${parseInt(start) + i}`);
                    }
                    return [p];
                })
                .forEach(port => allPorts.add(port));
        });

        return allPorts;
    }, [portConfigAppData]);
    const validPrefixes = ["ge", "te", "qe", "xe"];

    const expandPortRange = port => {
        const rangeRegex = /^([A-Za-z]+-\d+\/\d+\/)(\d+)-(\d+)$/;
        const match = port.match(rangeRegex);
        if (!match) return [port.trim()];
        const [_, prefix, start, end] = match;
        const ports = [];
        for (let i = parseInt(start); i <= parseInt(end); i++) {
            ports.push(`${prefix}${i}`);
        }
        return ports;
    };

    const validateInterface = (_, value) => {
        if (!value) return Promise.resolve();
        const singleInterfaceRegex = /^([A-Za-z]+)-\d+\/\d+\/\d+$/;
        const rangeInterfaceRegex = /^([A-Za-z]+)-\d+\/\d+\/(\d+)-(\d+)$/;
        const inputList = value.split(",").map(i => i.trim());
        const allPorts = [];
        for (const p of inputList) {
            if (rangeInterfaceRegex.test(p)) {
                const [, prefix, start, end] = p.match(rangeInterfaceRegex);
                if (!validPrefixes.includes(prefix.toLowerCase())) {
                    return Promise.reject(new Error("The value must start with te, ge, qe, or xe."));
                }
                if (+start > +end) {
                    return Promise.reject(new Error("The start of the range cannot be greater than the end."));
                }
            } else if (singleInterfaceRegex.test(p)) {
                const match = p.match(singleInterfaceRegex);
                if (!validPrefixes.includes(match[1].toLowerCase())) {
                    return Promise.reject(new Error("The value must start with te, ge, qe, or xe."));
                }
            } else {
                return Promise.reject(
                    new Error(`The port format is invalid. Use ${validPrefixes.join("/")} like: ge-0/0/1 or te-1/1/2-4`)
                );
            }
            allPorts.push(...expandPortRange(p));
        }
        const seen = new Set();
        for (const port of allPorts) {
            if (seen.has(port)) {
                return Promise.reject(new Error("Ports cannot be duplicated."));
            }
            seen.add(port);
        }
        const editingPorts = currentRecord ? normalizePorts(currentRecord.port) : [];
        const isEditing = mode === "edit" && currentRecord;
        for (const port of allPorts) {
            if (isEditing && editingPorts.includes(port)) continue;
            if (existingPorts.has(port)) {
                return Promise.reject(new Error("The port already exists."));
            }
        }
        return Promise.resolve();
    };
    useImperativeHandle(ref, () => ({
        showPortConfigAppModal: ({mode}, record) => {
            if (mode === "edit" || mode === "view") {
                setCurrentRecord(record);
                form.setFieldsValue({
                    port: record.port,
                    profile: record.profile
                });
            }
            setMode(mode);
            setIsShowModal(true);
        }
    }));

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {mode === "edit" ? "Edit" : "Create Port Profile Application"}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            form.submit();
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 7}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            handleAddServer(form.getFieldsValue());
                            message.success("Port Profile application created successfully");
                        }
                        if (mode === "edit") {
                            handleEditServer(form.getFieldsValue(), currentRecord);
                            message.success("Port Profile application edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the Port Profile application");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="port"
                    validateFirst
                    label="Ports"
                    required
                    rules={[{required: true, message: "Please input ports."}, {validator: validateInterface}]}
                >
                    <Input style={{width: "280px"}} placeholder="ge-1/1/1,ge-1/1/2-10" />
                </Form.Item>
                <Form.Item
                    name="profile"
                    validateFirst
                    label="Port Profile"
                    required
                    rules={[{required: true, message: "Please select the Port Profile."}]}
                >
                    <Select style={{width: "280px"}} placeholder="Select Port Profile">
                        {portConfigData?.map(item => (
                            <Option key={item.name} value={item.name}>
                                {item.name}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default PortConfigAppModal;
