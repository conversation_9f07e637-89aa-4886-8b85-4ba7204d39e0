import {Button, Form, Input, Modal, Flex, Divider, Select, message} from "antd";
import {useState, useImperativeHandle, forwardRef, useEffect, useRef} from "react";
import ReactDOM from "react-dom";

const LocalUserModal = forwardRef(({handleAddServer, handleEditServer, userData}, ref) => {
    const [mode, setMode] = useState();
    const [record, setRecord] = useState();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [title, setTitle] = useState("Create User");
    const [form] = Form.useForm();
    const [levelOptions, setLevelOptions] = useState([
        {label: "read-only", value: "read-only"},
        {label: "super-user", value: "super-user"}
    ]);
    const [isFocused, setIsFocused] = useState(false);
    const inputRef = useRef(null);
    const validatePassword = context => (_, value) => {
        if (!value) {
            return Promise.reject(new Error(`${context} is required.`));
        }
        if (
            value.length < 6 ||
            !/[a-z]/.test(value) ||
            !/[A-Z]/.test(value) ||
            !/\d/.test(value) ||
            !/[!#$%&()*+,./:;<=>?@[\\\]^_{|}~\-]/.test(value)
        ) {
            return Promise.reject(
                new Error(
                    `${context} must be at least 6 characters including lowercase letters, uppercase letters, digits, and special characters.`
                )
            );
        }
        return Promise.resolve();
    };
    useImperativeHandle(ref, () => ({
        showLocalUserModal: ({mode}, record) => {
            form.resetFields();
            setIsModalOpen(true);
            setMode(mode);
            if (mode === "edit") {
                setRecord(record);
                form.setFieldsValue({
                    name: record.name,
                    level: record.level,
                    password: record.password,
                    confirmpassword: record.password
                });
                setTitle("Edit");
            } else {
                setRecord(null);
                setTitle("Create User");
            }
        }
    }));
    useEffect(() => {}, [mode, record]);
    return isModalOpen ? (
        <Modal
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onOk={() => {}}
            onCancel={() => {
                form.resetFields();
                setIsModalOpen(false);
            }}
            className="ampcon-middle-modal"
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsModalOpen(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            // onClick={() => {
                            //     form.submit();
                            // }}
                            onClick={async () => {
                                try {
                                    await form.validateFields();
                                    form.submit();
                                } catch (err) {
                                    console.error("Validation failed:", err);
                                }
                            }}
                        >
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form
                labelAlign="left"
                labelCol={{flex: "150px"}}
                wrapperCol={{flex: "280px"}}
                form={form}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            handleAddServer(form.getFieldsValue());
                            message.success("Local user created successfully");
                        }
                        if (mode === "edit") {
                            handleEditServer(form.getFieldsValue(), record);
                            message.success("Local user edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the local user");
                        console.error(error);
                    } finally {
                        setIsModalOpen(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="name"
                    label="Username"
                    validateFirst
                    rules={[
                        {required: true, message: "The username is required."},
                        {
                            validator: (_, value) => {
                                if (value && value.toLowerCase() === "admin") {
                                    return Promise.reject(new Error("The username cannot be admin."));
                                }
                                const isDuplicate = userData?.users?.some(
                                    user => user.name === value && value !== record?.name
                                );
                                if (isDuplicate) {
                                    return Promise.reject(new Error("The username already exists."));
                                }
                                if (/^[0-9]/.test(value)) {
                                    return Promise.reject(new Error("The username cannot start with a digit."));
                                }
                                if (!/^[A-Za-z0-9_.-]+$/.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            "The username can only contain letters, digits, and the special characters (_ . -)."
                                        )
                                    );
                                }
                                return Promise.resolve();
                            }
                        },
                        {max: 32, message: "The username cannot exceed 32 characters."}
                    ]}
                >
                    <Input disabled={mode === "edit"} />
                </Form.Item>
                <Form.Item
                    name="level"
                    validateFirst
                    label="User Level"
                    required
                    rules={[{required: true, message: "The user level is required."}]}
                >
                    <Select style={{width: "280px"}} options={levelOptions} />
                </Form.Item>
                <Form.Item
                    name="password"
                    label="Password"
                    validateFirst
                    rules={[{validator: validatePassword("The password")}]}
                    required
                >
                    <Input.Password
                        ref={inputRef}
                        onFocus={() => setIsFocused(true)}
                        onBlur={() => setIsFocused(false)}
                        onChange={() => {
                            form.validateFields(["confirmpassword"]);
                        }}
                        style={{width: 280}}
                    />
                </Form.Item>
                <CustomTooltip visible={isFocused} targetRef={inputRef} />
                <Form.Item
                    name="confirmpassword"
                    label="Confirm Password"
                    validateFirst
                    dependencies={["password"]}
                    required
                    rules={[
                        {validator: validatePassword("The Confirm Password")},
                        ({getFieldValue}) => ({
                            validator(_, value) {
                                if (value !== getFieldValue("password")) {
                                    return Promise.reject(new Error("Passwords do not match."));
                                }
                                return Promise.resolve();
                            }
                        })
                    ]}
                >
                    <Input.Password />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default LocalUserModal;
const CustomTooltip = ({visible, targetRef}) => {
    const [position, setPosition] = useState({top: 0, left: 0});
    const updatePosition = () => {
        if (targetRef?.current?.input) {
            const rect = targetRef.current.input.getBoundingClientRect();
            setPosition({
                top: rect.top + window.scrollY - 40,
                left: rect.right + 35
            });
        }
    };
    useEffect(() => {
        if (!visible) return;
        updatePosition();
        window.addEventListener("resize", updatePosition);
        window.addEventListener("scroll", updatePosition);
        return () => {
            window.removeEventListener("resize", updatePosition);
            window.removeEventListener("scroll", updatePosition);
        };
    }, [visible]);

    if (!visible) return null;

    const style = {
        position: "absolute",
        top: position.top,
        left: position.left,
        zIndex: 9999,
        width: "184px",
        background: "#646569",
        color: "#fff",
        padding: "8px 12px",
        borderRadius: 6,
        fontSize: 12,
        boxShadow: "0 3px 6px rgba(0,0,0,0.2)",
        lineHeight: 1.6
    };

    return ReactDOM.createPortal(
        <div style={style}>
            <div>• Requires at least 6 characters</div>
            <div>
                • Supports lowercase letters (a-z), uppercase letters (A-Z), numbers (such as 0, 1, 2), and special
                characters (such as @, %, !)
            </div>
        </div>,
        document.body
    );
};
