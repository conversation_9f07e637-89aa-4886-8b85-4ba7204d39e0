import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lex, message, Space} from "antd";
import {useState, forwardRef, useImperativeHandle, useRef} from "react";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {getSwitchList} from "@/modules-ampcon/apis/campus_blueprint_api";
import {onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";

const AssignTemplateAddModal = forwardRef(({handleAddSwitch, switchData, templateName}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const tableModalRef = useRef();
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const matchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "site", matchMode: "fuzzy"},
        {name: "template_name", matchMode: "fuzzy"}
    ];
    const searchFieldsList = ["host_name", "sn", "mgt_ip", "platform_model", "site", "template_name"];
    const [snList, setSnList] = useState([]);
    const renderWithFallback = text => (text || text === 0 ? String(text) : "--");
    const columns = [
        {
            ...createColumnConfig("Sysname", "host_name", TableFilterDropdown),
            render: renderWithFallback
        },
        {
            ...createColumnConfig("SN", "sn", TableFilterDropdown),
            render: renderWithFallback
        },
        {
            ...createColumnConfig("Mgmt IP", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                if (!record.mgt_ip) return null;
                let iconComponent;
                if (record?.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record?.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }
                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip || "--"}
                    </Space>
                );
            },
            sorter: (a, b) => a.mgt_ip?.localeCompare?.(b.mgt_ip || "") || 0
        },
        {
            ...createColumnConfig("Model", "platform_model", TableFilterDropdown),
            render: renderWithFallback
        },
        {
            ...createColumnConfig("Site", "site", TableFilterDropdown),
            render: renderWithFallback
        },
        {
            ...createColumnConfig("Configuration Template", "template_name", TableFilterDropdown),
            render: renderWithFallback
        }
    ];

    useImperativeHandle(ref, () => ({
        showAddModal: () => {
            if (switchData) {
                setSnList(switchData.map(item => item.sn));
                const keys = switchData.map(item => item.id);
                setSelectedRowKeys(keys);
                setSelectedRows(switchData);
            }
            setIsShowModal(true);
        }
    }));
    const rowSelection = {
        selectedRowKeys,
        onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRows(prev => [...prev.filter(item => !rows.some(row => row.id === item.id)), ...rows]);
        },
        getCheckboxProps: record => ({
            checked: selectedRows.some(item => item.id === record.id)
        })
    };
    const handleSave = async () => {
        try {
            const response = await getSwitchList([], 1, 10000000, [], [], []);
            const fullData = response?.data || [];
            const completedRows = selectedRowKeys.map(key => {
                const full = fullData.find(item => item.id === key);
                const merged = full || {};
                return {
                    ...merged,
                    template_name: merged.template_name?.split?.("-")[0] || ""
                };
            });
            const hasDifferentTemplate = completedRows.some(
                row => !!row.template_name && row.template_name !== templateName
            );
            if (hasDifferentTemplate) {
                const affectedSwitches = completedRows
                    .filter(row => !!row.template_name && row.template_name !== templateName)
                    .map(row => row.host_name)
                    .join(", ");
                confirmModalAction(
                    `This template will replace the template previously applied to the selected switches: ${affectedSwitches}.`,
                    () => {
                        handleAddSwitch(completedRows);
                        setIsShowModal(false);
                        setSelectedRowKeys([]);
                        setSelectedRows([]);
                        message.success("Template applied successfully");
                    }
                );
            } else {
                handleAddSwitch(completedRows);
                setIsShowModal(false);
                setSelectedRowKeys([]);
                setSelectedRows([]);
            }
        } catch (error) {
            message.error("An error occurred while applying the template");
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-max-modal"
            title={
                <div>
                    Select Switches
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
            }}
            footer={
                <Flex vertical>
                    <Divider />
                    <Flex justify="flex-end">
                        <Button
                            htmlType="button"
                            onClick={() => {
                                setIsShowModal(false);
                                setSelectedRowKeys([]);
                                setSelectedRows([]);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <AmpConCustomTable
                rowSelection={rowSelection}
                columns={columns}
                fetchAPIInfo={getSwitchList}
                fetchAPIParams={[snList]}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                ref={tableModalRef}
                onDataSourceLoaded={data => {
                    setSelectedRows(prev =>
                        prev.map(item => {
                            const fullItem = data.find(d => d.id === item.id);
                            return fullItem ? {...item, ...fullItem} : item;
                        })
                    );
                }}
            />
        </Modal>
    ) : null;
});
export default AssignTemplateAddModal;
