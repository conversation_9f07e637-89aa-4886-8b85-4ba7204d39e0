.goBack {
    width: 70px;
    font-weight: 500;
    font-size: 14px;
    color: #14c9bb;
    text-align: left;
    font-style: normal;
    text-transform: none;
    cursor: pointer;
}

.actionLink a {
    color: #14c9bb;
}

.actionLink a:hover {
    color: #34dccf;
}

.successTag {
    background: rgba(43, 193, 116, 0.1) !important;
    border-radius: 2px !important;
    border: 1px solid #2bc174 !important;
    color: #2bc174 !important;
    font-size: 14px !important;
    font-style: normal;
}

.failedTag {
    background: rgba(245, 63, 63, 0.1) !important;
    border-radius: 2px !important;
    border: 1px solid #f53f3f !important;
    color: #f53f3f !important;
    font-size: 14px !important;
    font-style: normal;
}

.runningTag {
    background: rgba(255, 187, 0, 0.1);
    border-radius: 2px !important;
    border: 1px solid #ffbb00;
    color: #ffbb00 !important;
    font-size: 14px !important;
    font-style: normal;
}

.pendingTag {
    background-color: #f4f5f7;
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #dadce1;
    color: #929a9e;
    font-size: 14px;
}

:global {
    .customCollapse .ant-collapse-item {
        border: 1px solid #f0f0f0;
    }

    .customCollapse {
        border: 0px solid #f0f0f0;
    }

    .customCollapse .ant-collapse-header {
        padding: 4px 8px !important;
        min-height: unset;
        font-size: 12px;
        line-height: 1.1;
        background: #f8fafb !important;
        display: flex;
        align-items: center;
    }

    .customCollapse .ant-collapse-content-box {
        padding: 6px 16px !important;
        background: #ffffff;
        font-size: 12px;
        line-height: 1.3;
    }

    .customCollapse .ant-collapse-expand-icon {
        display: flex;
        align-items: center;
        height: 100%;
        justify-content: flex-end;
        font-size: 12px;
        margin-inline-end: 0;
    }

    .customCollapse .anticon {
        font-size: 12px;
        vertical-align: middle;
    }

    .customCollapse .ant-collapse-expand-icon svg {
        margin-top: 35px;
        color: #bfbfbf;
    }

    .customCollapse .ant-collapse-content {
        border-top: 1px solid #f0f0f0 !important;
    }
}

.divider {
    margin-top: 16px;
    margin-bottom: 20px;
}

.title {
    margin-bottom: 10px;
    margin-top: 14px;
}
