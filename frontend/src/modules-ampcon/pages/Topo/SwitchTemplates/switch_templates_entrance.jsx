import {useState, useRef} from "react";
import SwitchTemplatesTbleView from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates_table_view";
import TemplateDeploymentStatus from "@/modules-ampcon/pages/Topo/SwitchTemplates/template_deployment_status";

const SwitchTemplates = () => {
    const [isInTableView, setIsInTableView] = useState(true);
    const tableRef = useRef(null);

    const backToTableViewCallback = async () => {
        setIsInTableView(true);
        tableRef.current?.refreshTable?.();
    };

    return isInTableView ? <SwitchTemplatesTbleView ref={tableRef} /> : null;
};
// const SwitchTemplates = () => {
// const [isInTableView, setIsInTableView] = useState(true);
// const [isInCreateView, setIsInCreateView] = useState(false);
// const [isInEditView, setIsInEditView] = useState(false);
// const [isInDeploymentStatusView, setIsInDeploymentStatusView] = useState(false);
// const tableRef = useRef(null);
// const [currentTemplateId, setCurrentTemplateId] = useState(null);
// const deploymentStatusCallback = record => {
//     setCurrentTemplateId(record.id);
//     setIsInTableView(false);
//     setIsInCreateView(false);
//     setIsInEditView(false);
//     setIsInDeploymentStatusView(true);
// };
// const backToTableViewCallback = async () => {
//     setIsInEditView(false);
//     setIsInCreateView(false);
//     setIsInTableView(true);
//     tableRef.current?.refreshTable?.();
// };
// const getSwitchTemplatesComponent = () => {
//     if (isInTableView) {
//         return <SwitchTemplatesTbleView ref={tableRef} />;
//     }
// if (isInDeploymentStatusView) {
//     return (
//         <TemplateDeploymentStatus
//             backToTableViewCallback={backToTableViewCallback}
//             currentTemplateId={currentTemplateId}
//         />
//     );
// }
// };

// };

export default SwitchTemplates;
