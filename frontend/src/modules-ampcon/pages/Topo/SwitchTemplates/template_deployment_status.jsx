import {useState, useRef, useEffect} from "react";
import {Card, Space, Tag, Modal, Button, Divider, Input, Flex, message} from "antd";
import Icon, {ReloadOutlined} from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates.module.scss";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {getStatusList, getStatusLog} from "@/modules-ampcon/apis/campus_blueprint_api";
import {useLocation} from "react-router-dom";
import {onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";

const TemplateDeploymentStatus = () => {
    const tableRef = useRef();
    const fetchIntervalRef = useRef();
    const [viewLogModal, setViewLogModal] = useState(false);
    const [logInfo, setLogInfo] = useState(null);
    const [logModel, setLogModel] = useState("");
    const {state} = useLocation();
    const currentTemplateId = state?.id || "";
    const searchFieldsList = ["model", "sn", "mgt_ip", "status"];
    const matchFieldsList = [
        {name: "model", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"}
    ];
    const [currentLogId, setCurrentLogId] = useState(null);
    const fetchStatusLog = record => {
        setCurrentLogId(record.log_id);
        getStatusLog(record.log_id).then(res => {
            if (res.status === 200) {
                setLogInfo(res.log_info);
                setViewLogModal(true);
                setLogModel(record.model);
            } else {
                message.error(res.info);
            }
        });
    };
    const reloadStatusLog = () => {
        if (!currentLogId) return;
        getStatusLog(currentLogId).then(res => {
            if (res.status === 200) {
                message.success("Log reloaded successfully");
                setLogInfo(res.log_info);
            } else {
                message.error(res.info);
            }
        });
    };
    const columns = [
        createColumnConfig("Model", "model", TableFilterDropdown),
        createColumnConfig("SN", "sn", TableFilterDropdown),
        {
            ...createColumnConfig("Mgmt IP", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                if (!record.mgt_ip) return null;
                let iconComponent;
                if (record?.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record?.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }
                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip || "--"}
                    </Space>
                );
            },
            sorter: (a, b) => a.mgt_ip?.localeCompare?.(b.mgt_ip || "") || 0
        },
        createColumnConfig("Site", "site", TableFilterDropdown),
        {
            ...createColumnConfig("Deployment Status", "status"),
            render: (_, record) => {
                return (
                    <div>
                        <Space size="small">
                            {record.status === 2 && <Tag className={styles.successTag}>Success</Tag>}
                            {record.status === 3 && <Tag className={styles.failedTag}>Failed</Tag>}
                            {record.status === 1 && <Tag className={styles.runningTag}>Running</Tag>}
                            {record.status === 0 && <Tag className={styles.pendingTag}>Pending</Tag>}
                        </Space>
                    </div>
                );
            }
        },
        createColumnConfig("Create Time", "create_time"),
        {
            title: "Operation",
            render: (_, record) => (
                <div>
                    <Space size="middle" className={styles.actionLink}>
                        <a onClick={() => fetchStatusLog(record)}>Log</a>
                    </Space>
                </div>
            )
        }
    ];
    useEffect(() => {
        fetchIntervalRef.current = setInterval(() => {
            if (tableRef.current) {
                tableRef.current.refreshTable();
            }
        }, 60000);
        return () => {
            if (fetchIntervalRef.current) {
                clearInterval(fetchIntervalRef.current);
            }
        };
    }, []);
    return (
        <>
            <Card style={{display: "flex", flexDirection: "column", flex: 1}}>
                <h2 style={{margin: "8px 0 20px"}}>Template Deployment Status</h2>
                <AmpConCustomTable
                    ref={tableRef}
                    columns={columns}
                    fetchAPIInfo={getStatusList}
                    fetchAPIParams={[currentTemplateId]}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                />
            </Card>
            <LogViewModal
                title="Logs"
                logInfo={logInfo}
                logModel={logModel}
                viewLogModal={viewLogModal}
                onCancel={() => setViewLogModal(false)}
                modalClass="ampcon-max-modal"
                onReloadLog={reloadStatusLog}
            />
        </>
    );
};

export default TemplateDeploymentStatus;
const LogViewModal = ({logInfo, logModel, viewLogModal, onCancel, onReloadLog}) => {
    const readonlyStyle = {
        minHeight: "330px",
        height: "58vh",
        resize: "vertical",
        border: "1px solid rgb(217, 217, 217)",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        maxHeight: "calc(100vh - 500px)"
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <>
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        {`${logModel} logs`}
                        <Button
                            type="text"
                            className="ant-modal-close"
                            style={{marginRight: "30px"}}
                            icon={<ReloadOutlined className="anticon anticon-close ant-modal-close-icon" />}
                            onClick={onReloadLog}
                        />
                    </div>
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </>
            }
            open={viewLogModal}
            onCancel={onCancel}
            footer={null}
        >
            <Flex vertical style={{flex: 1}}>
                <Input.TextArea
                    style={{
                        resize: "none",
                        backgroundColor: "#F8FAFB",
                        borderRadius: "4px",
                        border: "1px solid #FFFFFF"
                    }}
                    value={logInfo}
                    rows={19}
                    readOnly
                />
            </Flex>
        </Modal>
    );
};
