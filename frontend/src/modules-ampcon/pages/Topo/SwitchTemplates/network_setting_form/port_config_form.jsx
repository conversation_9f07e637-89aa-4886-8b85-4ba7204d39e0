import {Space, Form, Divider, Tooltip, message} from "antd";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {useState, useEffect, forwardRef, useImperativeHandle, useRef} from "react";
import Icon, {QuestionCircleOutlined} from "@ant-design/icons";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import PortConfigDefaultViewModal from "../modal/port_config_default_view_modal";
import style from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates.module.scss";

const PortConfigForm = forwardRef(({portConfigData, tableStyle, networkData, setSwitchTemplateData}, ref) => {
    const [form] = Form.useForm();
    const [dataSource, setDataSource] = useState([]);
    const portConfigDefaultViewModalRef = useRef(null);
    useEffect(() => {
        const defaultRecord = {
            name: "Default",
            portEnable: "enable",
            description: "",
            mode: "access",
            portNetwork: "vlan1",
            speed: "auto",
            poe: "disable",
            stormControl: "disable"
        };
        const hasDefault = portConfigData?.some(item => item.name === "Default");
        setDataSource(hasDefault ? portConfigData : [defaultRecord, ...(portConfigData || [])]);
    }, [portConfigData, form]);
    useImperativeHandle(ref, () => ({}));
    const updatePortConfig = newData => {
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                portConfiguration: newData
            }
        }));
    };
    const handleAddServer = newData => {
        const updatedData = [...dataSource, newData];
        setDataSource(updatedData);
        updatePortConfig(updatedData);
    };
    const handleEditServer = (newData, currentRecord) => {
        const updatedData = dataSource.map(item => (item.name === currentRecord.name ? newData : item));
        setDataSource(updatedData);
        updatePortConfig(updatedData);
    };

    const handleDeleteServer = record => {
        setSwitchTemplateData(prev => {
            const updatedPortConfig = (prev.switch.portConfiguration || []).filter(port => port.name !== record.name);
            const updatedPortApp = (prev.switch.portConfigApplication || []).filter(app => app.profile !== record.name);
            const updatedDataSource = dataSource.filter(item => item.name !== record.name);
            setDataSource(updatedDataSource);
            return {
                ...prev,
                switch: {
                    ...prev.switch,
                    portConfiguration: updatedPortConfig,
                    portConfigApplication: updatedPortApp
                }
            };
        });
    };

    const columns = [
        {
            title: "Name",
            dataIndex: "name",
            width: "50%",
            render: (_, record) => (
                <span>
                    {record.name}
                    {record.name === "Default" && (
                        <Tooltip title="This is a system-defined port profile, which can't be modified and deleted.">
                            <QuestionCircleOutlined
                                style={{
                                    marginLeft: 8,
                                    color: "rgba(0, 0, 0, 0.45)",
                                    cursor: "pointer"
                                }}
                            />
                        </Tooltip>
                    )}
                </span>
            ),
            sorter: (a, b) => a.name.localeCompare(b.name)
        },
        {
            title: "Operation",
            render: (_, record) => {
                if (record.name === "Default") {
                    return (
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    portConfigDefaultViewModalRef.current.showPortConfigDefaultViewModal(
                                        {mode: "view"},
                                        record
                                    );
                                }}
                            >
                                View
                            </a>
                        </Space>
                    );
                }
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                portConfigDefaultViewModalRef.current.showPortConfigDefaultViewModal(
                                    {mode: "edit"},
                                    record
                                );
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure want to delete?", () => {
                                    try {
                                        handleDeleteServer(record);
                                        message.success("Delete Successful");
                                    } catch (error) {
                                        message.error("Delete Failed");
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h2 className={style.title}>Port Profile</h2>
            <PortConfigDefaultViewModal
                ref={portConfigDefaultViewModalRef}
                portConfigData={portConfigData}
                networkData={networkData}
                handleAddServer={handleAddServer}
                handleEditServer={handleEditServer}
            />
            <Form
                ref={ref}
                form={form}
                validateTrigger="onBlur"
                labelAlign="left"
                style={{width: 505, marginBottom: "-10px"}}
            >
                <Form.Item style={{marginBottom: "0px"}} labelCol={{style: {width: 175}}}>
                    <a
                        style={{
                            display: "inline-flex",
                            alignItems: "center",
                            border: "none",
                            borderRadius: "4px",
                            color: "#14c9bb"
                        }}
                        onClick={() => {
                            portConfigDefaultViewModalRef.current.showPortConfigDefaultViewModal({mode: "create"});
                        }}
                    >
                        <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                        Port Profile
                    </a>
                </Form.Item>
            </Form>
            <div>
                <AmpConCustomTable
                    dataSource={dataSource}
                    columns={columns}
                    style={tableStyle}
                    pagination={{
                        defaultPageSize: 10,
                        showSizeChanger: true,
                        hideOnSinglePage: dataSource.length <= 10
                    }}
                />
            </div>

            <Divider className={style.divider} />
        </>
    );
});
export default PortConfigForm;
