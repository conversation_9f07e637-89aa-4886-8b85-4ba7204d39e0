import React, {forwardRef, useEffect, useImperative<PERSON>andle} from "react";
import {Form, Input, Button, Space, Divider} from "antd";
import {PlusOutlined, MinusOutlined} from "@ant-design/icons";
import style from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates.module.scss";

const DnsForm = forwardRef(({data, setSwitchTemplateData}, ref) => {
    const [form] = Form.useForm();
    const updateDnsData = () => {
        const dnsIps = form.getFieldValue("dnsServers") || [];
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                dns: {
                    ip: dnsIps
                }
            }
        }));
    };
    useEffect(() => {
        const ipList = Array.isArray(data?.ip) ? data.ip : [];
        form.setFieldsValue({dnsServers: ipList.length ? ipList : [""]});
    }, [JSON.stringify(data?.ip)]);
    useImperativeHandle(ref, () => ({
        validate: async () => {
            try {
                await form.validateFields();
                return true;
            } catch {
                return false;
            }
        }
    }));
    const requiredOnCountRule = form => ({
        validateTrigger: "onBlur",
        validator: (_, value) => {
            const list = form.getFieldValue("dnsServers") || [];
            if (list.length === 1 && !value) return Promise.resolve();
            if (list.length > 1 && !value) return Promise.reject(new Error("The DNS server IP address is required."));
            return Promise.resolve();
        }
    });
    const formatAndDupRule = form => ({
        validateTrigger: ["onChange", "onBlur"],
        validator: (_, value) => {
            const list = form.getFieldValue("dnsServers") || [];
            const ipv4Regex = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
            if (!value) return Promise.resolve();
            if (!ipv4Regex.test(value)) {
                return Promise.reject(new Error("The DNS server IP address must be in IPv4 format."));
            }

            const count = list.filter(ip => ip === value).length;
            if (count > 1) {
                return Promise.reject(new Error("The DNS server IP address is already in use."));
            }
            return Promise.resolve();
        }
    });
    const revalidateNonEmpty = () => {
        const list = form.getFieldValue("dnsServers") || [];
        const names = list.map((v, i) => (v ? ["dnsServers", i] : null)).filter(Boolean);
        if (names.length) {
            form.validateFields(names).catch(() => {});
        }
    };
    const handleChange = () => {
        setTimeout(revalidateNonEmpty, 0);
    };
    const withRemove = (form, removeFn, fieldName) => () => {
        removeFn(fieldName);
        setTimeout(() => {
            const list = form.getFieldValue("dnsServers") || [];
            if (list.length === 1) {
                form.setFields([{name: ["dnsServers", 0], errors: []}]);
            } else {
                revalidateNonEmpty();
            }
        }, 0);
    };

    return (
        <>
            <h2 className={style.title}>DNS</h2>
            <Form form={form} layout="horizontal" labelAlign="left" style={{width: 505}} onValuesChange={updateDnsData}>
                <Form.List name="dnsServers">
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map((field, index) => (
                                <Form.Item
                                    key={field.key}
                                    label={index === 0 ? "DNS Server IP" : ""}
                                    labelCol={{style: {width: 175}}}
                                    style={{marginBottom: 16, marginLeft: index === 0 ? 0 : 175}}
                                >
                                    <Space>
                                        <Form.Item
                                            {...field}
                                            rules={[requiredOnCountRule(form), formatAndDupRule(form)]}
                                            validateTrigger={["onBlur", "onChange"]}
                                            noStyle
                                        >
                                            <Input style={{width: 280}} onChange={handleChange} />
                                        </Form.Item>

                                        {index === 0 ? (
                                            <Button
                                                type="link"
                                                icon={<PlusOutlined />}
                                                onClick={() => add()}
                                                style={{backgroundColor: "transparent", color: "#BFBFBF"}}
                                            />
                                        ) : (
                                            <Button
                                                type="link"
                                                danger
                                                icon={<MinusOutlined />}
                                                onClick={withRemove(form, remove, field.name)}
                                                style={{backgroundColor: "transparent", color: "#BFBFBF"}}
                                            />
                                        )}
                                    </Space>
                                </Form.Item>
                            ))}
                        </>
                    )}
                </Form.List>
            </Form>

            <Divider className={style.divider} />
        </>
    );
});

export default DnsForm;
