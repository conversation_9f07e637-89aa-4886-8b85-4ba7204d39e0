import {Space, Form, Divider, Tooltip, message} from "antd";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {useState, useEffect, forwardRef, useImperativeHandle, useRef, useMemo} from "react";
import Icon from "@ant-design/icons";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import PortConfigAppModal from "../modal/port_config_app_modal";
import style from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates.module.scss";

const PortConfigAppForm = forwardRef(({portConfigAppData, portConfigData, setSwitchTemplateData, tableStyle}, ref) => {
    const [form] = Form.useForm();
    const PortConfigAppModalRef = useRef(null);
    const [dataSource, setDataSource] = useState([]);
    useEffect(() => {
        setDataSource(portConfigAppData || []);
    }, [portConfigAppData, form]);
    const defaultRecord = {
        name: "Default",
        portEnable: "enable",
        description: "",
        portMode: "access",
        portNetwork: "vlan1",
        speed: "auto",
        poe: "disable",
        stormControl: "disable"
    };
    const mergedPortConfigData = useMemo(() => {
        if (!portConfigData) return [defaultRecord];
        if (portConfigData.some(item => item.name === "Default")) {
            return portConfigData;
        }
        return [defaultRecord, ...portConfigData];
    }, [portConfigData]);
    useImperativeHandle(ref, () => ({}));
    const updatePortConfigApp = newData => {
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                portConfigApplication: newData
            }
        }));
    };
    const handleAddServer = newData => {
        const updatedData = [...dataSource, newData];
        setDataSource(updatedData);
        updatePortConfigApp(updatedData);
    };
    const handleEditServer = (newData, currentRecord) => {
        const updatedData = dataSource.map(item => {
            if (item.port === currentRecord.port) {
                return {
                    ...item,
                    ...newData
                };
            }
            return item;
        });
        setDataSource(updatedData);
        updatePortConfigApp(updatedData);
    };

    const handleDeleteServer = record => {
        const updatedData = dataSource.filter(item => item.port !== record.port);
        setDataSource(updatedData);
        updatePortConfigApp(updatedData);
    };
    const columns = [
        {
            title: "Port",
            dataIndex: "port",
            width: "35%",
            render: (_, record) => {
                return record.port.split(",").map(p => <div key={p.trim()}>{p.trim()}</div>);
            },
            sorter: (a, b) => a.port.localeCompare(b.port)
        },
        {
            title: "Port Profile",
            dataIndex: "profile",
            width: "35%",
            render: (_, record) => {
                return record.profile;
            },
            sorter: (a, b) => a.profile.localeCompare(b.profile)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                PortConfigAppModalRef.current.showPortConfigAppModal({mode: "edit"}, record);
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction(
                                    "Are you sure you want to delete this port configuration application?",
                                    () => {
                                        try {
                                            handleDeleteServer(record);
                                            message.success("Delete Successful");
                                        } catch (error) {
                                            message.error("Delete Failed");
                                        }
                                    }
                                );
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h2 className={style.title}>Port Profile Application</h2>
            <PortConfigAppModal
                ref={PortConfigAppModalRef}
                handleAddServer={handleAddServer}
                handleEditServer={handleEditServer}
                portConfigData={mergedPortConfigData}
                portConfigAppData={dataSource}
            />
            <Form
                ref={ref}
                form={form}
                validateTrigger="onBlur"
                labelAlign="left"
                style={{width: 505, marginBottom: "-10px"}}
            >
                <Form.Item style={{marginBottom: "0px"}} labelCol={{style: {width: 175}}}>
                    <a
                        style={{
                            display: "inline-flex",
                            alignItems: "center",
                            border: "none",
                            borderRadius: "4px",
                            color: "#14c9bb"
                        }}
                        onClick={() => {
                            PortConfigAppModalRef.current.showPortConfigAppModal({mode: "create"});
                        }}
                    >
                        <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                        Port Profile Application
                    </a>
                </Form.Item>
            </Form>
            {dataSource.length > 0 && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: dataSource.length <= 10
                        }}
                    />
                </div>
            )}
            <Divider className={style.divider} />
        </>
    );
});
export default PortConfigAppForm;
