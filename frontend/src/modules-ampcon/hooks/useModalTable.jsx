import {useState} from "react";
import {Form} from "antd";
import {checkPwdInfo} from "@/modules-ampcon/apis/user_api";

export const useTableInitialElement = (searchFieldsList, formFlag = false) => {
    const [useForm] = Form.useForm();
    const [selectModalOpen, setSelectModalOpen] = useState(false);
    const [searchFields, setSearchFields] = useState({fields: [], value: ""});
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        showTotal: searchFieldsList ? (total, range) => `${range[0]}-${range[1]} of ${total} items` : null,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50", "100"]
    });

    const initialElementList = [
        selectModalOpen,
        setSelectModalOpen,
        searchFields,
        setSearchFields,
        data,
        setData,
        loading,
        setLoading,
        pagination,
        setPagination
    ];

    if (formFlag) {
        initialElementList.push(useForm);
    }
    return initialElementList;
};

export const useCheckPwd = formInstance => {
    const [pwdCheckStatus, setPwdCheckStatus] = useState("");
    const [checkPwdTips, setCheckPwdTips] = useState(null);

    const handlePasswordBlur = async e => {
        const password = e.target.value;
        const ret = await checkPwdInfo({password});
        if (ret.status === 200) {
            setPwdCheckStatus("success");
            setCheckPwdTips(null);
        } else {
            setPwdCheckStatus("error");
            setCheckPwdTips(ret.info);
        }
        formInstance.validateFields(["password"]);
    };

    return [pwdCheckStatus, setPwdCheckStatus, checkPwdTips, setCheckPwdTips, handlePasswordBlur];
};
