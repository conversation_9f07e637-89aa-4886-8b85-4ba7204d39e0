import { useFavorites } from '@/modules-smb/contexts/FavoritesProvider';
import { useState, useCallback } from 'react';

type UseEntityFavorite = () => {
    isFavorited: (id: string, setIsFavorit: (v: boolean) => void) => void;
    toggleFavorite: (id: string, setIsFavorit: (v: boolean) => void) => void;
    getFirstVenueFavoriteId: () => string | null;
};

export const useEntityFavorite: UseEntityFavorite = () => {
    const favoriteContext = useFavorites();
    const isFavorited = useCallback((id: string, setIsFavorit: any) => {
        let venueId = getFirstVenueFavoriteId();
        // console.log("isFavorited called with id:", id, "and FirstVenueFavoriteId:", venueId);
        let result = venueId && venueId === id
        setIsFavorit(result);
    }, [favoriteContext.entityFavorites.favorites]);

    const toggleFavorite = useCallback((id: string, setIsFavorit: any) => {
        // console.log("Toggling favorite for ID:", id);
        let venueId = getFirstVenueFavoriteId();
        // console.log("Toggling favorite called with id:", id, "and FirstVenueFavoriteId:", venueId);
        let result = venueId && venueId === id
        // 这里可以发送请求给后端更新收藏状态
        if (result) {
            favoriteContext.entityFavorites.remove({ id, type: "venue" });
        } else {
            favoriteContext.entityFavorites.add({ id, type: "venue" });
        }
        setIsFavorit(!result);
    }, [favoriteContext.entityFavorites.favorites]);

    const getFirstVenueFavoriteId = () => {
        const venueFavorite = favoriteContext.entityFavorites.favorites.find(favorite => favorite.type === 'venue');
        return venueFavorite ? venueFavorite.id : null;
    };

    return { isFavorited, toggleFavorite, getFirstVenueFavoriteId };
};