import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Switch,
  Button,
  Spin,
  Typography,
  Form,
  Space,
  Divider
} from 'antd';
import { useTranslation } from 'react-i18next';
import ConfirmIgnoreCommand from '@/modules-smb/components/Modals/Actions/ConfirmIgnoreCommand';
// import CloseButton from '@/modules-smb/components/Buttons/CloseButton';
import { useFactoryReset } from '@/modules-smb/hooks/Network/GatewayDevices';
import useCommandModal from '@/modules-smb/hooks/useCommandModal';
import { ModalProps } from '@/modules-smb/models/Modal';
import { AmpConCustomModal } from "@/modules-ampcon/components/custom_table";
import "@/modules-smb/Wireless/pages/Devices/Action/Form.scss";

const { Text } = Typography;

interface Props {
  modalProps: ModalProps;
  serialNumber: string;
}

const FactoryResetModal = ({ modalProps: { isOpen, onClose }, serialNumber }: Props) => {
  const { t } = useTranslation();
  const [isRedirector, setIsRedirector] = React.useState(false);
  const { mutateAsync: factoryReset, isLoading } = useFactoryReset({
    serialNumber,
    keepRedirector: isRedirector,
    onClose,
  });

  const { isConfirmOpen, closeConfirm, closeModal, closeCancelAndForm } = useCommandModal({
    isLoading,
    onModalClose: onClose,
  });

  const submit = () => {
    factoryReset();
  };

  return (
    <>
      <AmpConCustomModal
        title={t('commands.factory_reset')}
        isModalOpen={isOpen}
        onCancel={closeModal}
        modalClass="ampcon-middle-modal"
        footer={null}
        childItems={
          isLoading ? (
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Spin size="large" />
            </div>
          ) : (
            <>
              <Alert
                message={
                  <>
                    {/* <strong>Note:</strong>{t('commands.factory_reset_warning')} */}
                    <strong>Note:</strong> Are you sure you want to factory reset this device? This action is not reversible
                  </>
                }
                type="info"
                showIcon
                className="custom-trace-alert"
                closable
                style={{ marginTop: 8, marginBottom: 24}}
              />
              <Form style={{ display: 'flex', flexDirection: 'column' }}>
                <Form.Item label={t('commands.keep_redirector')}>
                  <Switch checked={isRedirector} onChange={setIsRedirector} />
                </Form.Item>
                <Form.Item>
                  <Button
                    size="large"
                    type="primary"
                    onClick={submit}
                    loading={isLoading}
                  >
                    {t('commands.confirm_reset', { serialNumber })}
                  </Button>
                </Form.Item>
              </Form>
            </>
          )
        }
      />

      <ConfirmIgnoreCommand
        modalProps={{ isOpen: isConfirmOpen, onOpen: () => { }, onClose: closeConfirm }}
        confirm={closeCancelAndForm}
        cancel={closeConfirm}
      />
    </>

  );
};

export default FactoryResetModal;
