import React from 'react';
import { But<PERSON>, Dropdown, MenuProps, Spin, Tooltip, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { useBlinkDevice, useGetDeviceRtty, useRebootDevice } from '@/modules-smb/hooks/Network/GatewayDevices';
import useMutationResult from '@/modules-smb/Wireless/hooks/useMutationResult';
import { Device } from '@/modules-smb/models/Device';
import { DownOutlined } from '@ant-design/icons';
interface Props {
  device: Device;
  refresh: () => void;
  isDisabled?: boolean;
  onOpenScan: (serialNumber: string) => void;
  onOpenFactoryReset: (serialNumber: string) => void;
  onOpenUpgradeModal: (serialNumber: string) => void;
}

const DeviceActionDropdown = ({
  device,
  refresh,
  isDisabled,
  onOpenScan,
  onOpenFactoryReset,
  onOpenUpgradeModal,
}: Props) => {
  const { t } = useTranslation();
  const { refetch: getRtty, isInitialLoading: isRtty } = useGetDeviceRtty({
    serialNumber: device.serialNumber,
    extraId: 'inventory-modal',
  }); 
  const { mutateAsync: reboot, isLoading: isRebooting } = useRebootDevice({ serialNumber: device.serialNumber });
  const { mutateAsync: blink } = useBlinkDevice({ serialNumber: device.serialNumber });
  const { onSuccess: onRebootSuccess, onError: onRebootError } = useMutationResult({
    objName: t('devices.one'),
    operationType: 'reboot',
    refresh,
  });
  const { onSuccess: onBlinkSuccess, onError: onBlinkError } = useMutationResult({
    objName: t('devices.one'),
    operationType: 'blink',
    refresh,
  });

  const handleRebootClick = () =>
  reboot(undefined, {
    onSuccess: onRebootSuccess,
    onError: onRebootError,
  });

  const handleBlinkClick = () =>
    blink(undefined, {
      onSuccess: () => {
        onBlinkSuccess();
      },
      onError: (e) => {
        onBlinkError(e);
      },
    });
  const handleOpenScan = () => onOpenScan(device.serialNumber);
  const handleOpenFactoryReset = () => onOpenFactoryReset(device.serialNumber);
  // const handleOpenUpgrade = () => onOpenUpgradeModal(device.serialNumber);
  const handleConnectClick = () => getRtty();

  const items: MenuProps['items'] = [
    {
      key: 'reboot',
      label: t('commands.reboot'),
      onClick: handleRebootClick,
    },
    {
      key: 'blink',
      label: t('commands.blink'),
      onClick: handleBlinkClick,
    },
    {
      key: 'rtty',
      // label: t('commands.rtty'),
      label: "RTTY",
      onClick: handleConnectClick,
    },
    // {
    //   key: 'wifiscan',
    //   label: t('commands.wifiscan'),
    //   onClick: handleOpenScan,
    // },
    // {
    //   key: 'upgrade',
    //   label: t('commands.firmware_upgrade'),
    //   onClick: handleOpenUpgrade,
    // },
    {
      key: 'factory_reset',
      label: t('commands.factory_reset'),
      onClick: handleOpenFactoryReset,
    },
  ];

  return (
    <Dropdown menu={{ items }} disabled={isDisabled} trigger={['click']}
    >
      <Button type="link" style={{ margin: 0, padding: '0 4px' }}>
        {isRebooting || isRtty ? <Spin size="small" /> : t('common.actions')}
        <DownOutlined style={{ marginLeft: '8px' }} />
      </Button>
    </Dropdown>
  );
};

export default React.memo(DeviceActionDropdown);