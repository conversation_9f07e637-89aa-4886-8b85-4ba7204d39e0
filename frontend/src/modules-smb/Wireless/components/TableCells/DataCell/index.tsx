import React, { useMemo } from 'react';
import { Typography } from 'antd';
import { bytesString } from '@/modules-smb/utils/stringHelper';

type Props = {
  bytes?: number;
  showZerosAs?: string;
  divProps?: React.HTMLAttributes<HTMLDivElement>;
};

const DataCell: React.FC<Props> = ({ bytes, showZerosAs, divProps }) => {
  const data = useMemo(() => {
    if (bytes === undefined) return '-';
    if (showZerosAs && bytes === 0) return showZerosAs;
    return bytesString(bytes);
  }, [bytes, showZerosAs]);

  return (
    <div {...divProps}>
      <Typography.Text>{data}</Typography.Text>
    </div>
  );
};

export default React.memo(DataCell);
