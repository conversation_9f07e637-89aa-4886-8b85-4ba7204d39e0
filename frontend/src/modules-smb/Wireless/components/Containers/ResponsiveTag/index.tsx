import React from "react";
import { Tag, Tooltip, Grid } from "antd";
import type { TagProps } from "antd";

export interface ResponsiveTagProps extends TagProps {
  label: string;
  icon?: React.ReactNode;
  tooltip?: string;
  isCompact?: boolean;
}

export const ResponsiveTag: React.FC<ResponsiveTagProps> = React.memo(
  ({ label, icon, tooltip, isCompact, style, ...props }) => {
    const { useBreakpoint } = Grid;
    const screens = useBreakpoint();
    const isCompactVersion =
      isCompact || (!screens.md && !screens.lg && !screens.xl);

    if (isCompactVersion) {
      return (
        <Tooltip title={tooltip ?? label}>
          <Tag
            {...props}
            style={{
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              ...style,
            }}
          >
            {icon}
          </Tag>
        </Tooltip>
      );
    }

    return (
      <Tooltip title={tooltip ?? label}>
        <Tag
          {...props}
          style={{
            display: "inline-flex",
            alignItems: "center",
            gap: 4,
            ...style,
          }}
        >
          {icon}
          <span style={{ fontSize: 12, fontWeight: 400, borderRadius: '2px' }}>{label}</span>
        </Tag>
      </Tooltip>
    );
  }
);
