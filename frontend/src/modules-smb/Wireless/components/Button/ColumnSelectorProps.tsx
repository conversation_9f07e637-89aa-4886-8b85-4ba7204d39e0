import React, { useState, useEffect, useRef } from 'react';
import { Dropdown, Checkbox, Space, Input, Button, Menu, Divider } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import IconSet from '@/modules-smb/Wireless/assets/Monitor/Logo_Set.png';

interface ColumnSelectorProps {
  columns: any[];
  visibleColumns: string[];
  onChange: (visibleColumns: string[]) => void;
}

const ColumnSelector: React.FC<ColumnSelectorProps> = ({ columns, visibleColumns, onChange }) => {
  const shouldShowSearch = columns.length > 1;
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [searchText, setSearchText] = useState('');

  // 搜索过滤列
  const filteredColumns = columns.filter(column =>
    String(column.title).toLowerCase().includes(searchText.toLowerCase())
  );

  // 1. 实时切换单个列（禁止取消固定列）
  const toggleColumn = (columnKey: string) => {
    const column = columns.find(col => col.key === columnKey);
    if (column?.columnsFix) return; // 固定列不可取消

    // 计算新的可见列
    const newVisibleColumns = visibleColumns.includes(columnKey)
      ? visibleColumns.filter(key => key !== columnKey)
      : [...visibleColumns, columnKey];
    const fixedKeys = columns.filter(col => col.columnsFix).map(col => col.key);
    const merged = Array.from(new Set([...newVisibleColumns, ...fixedKeys]));

    onChange(merged);
  };

  const toggleAllColumns = () => {
    const fixedKeys = columns.filter(col => col.columnsFix).map(col => col.key);
    const nonFixedKeys = columns.filter(col => !col.columnsFix).map(col => col.key);
    const allNonFixedSelected = nonFixedKeys.every(key => visibleColumns.includes(key));

    const newVisibleColumns = allNonFixedSelected
      ? fixedKeys
      : [...fixedKeys, ...nonFixedKeys];

    onChange(newVisibleColumns);
  };

  const menu = (
    <Menu style={{
      padding: '16px',
      width: '250px',
      maxHeight: '434px',
      overflowY: 'auto',
      border: 'none', // 边框
      boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)', // 投影效果
    }}>
      {shouldShowSearch && (
        <Input
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ marginBottom: '12px' }}
        />
      )}

      {/* 全选选项 */}
      <div style={{ marginBottom: '8px' }}>
        <Checkbox
          checked={columns.filter(col => !col.columnsFix).every(col => visibleColumns.includes(col.key))}
          indeterminate={
            columns.filter(col => !col.columnsFix).some(col => visibleColumns.includes(col.key)) &&
            columns.filter(col => !col.columnsFix).some(col => !visibleColumns.includes(col.key))
          }
          onChange={toggleAllColumns}
        >
          ALL
        </Checkbox>
      </div>

      {/* 列选项列表 */}
      <Space direction="vertical" style={{ width: '100%' }}>
        {filteredColumns.map(column => (
          <div key={column.key} style={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox
              checked={visibleColumns.includes(column.key)}
              onChange={() => toggleColumn(column.key)}
              disabled={column.columnsFix} // 固定列禁用勾选框
              style={{ marginRight: '8px' }}
            />
            <span>{column.title}</span>
          </div>
        ))}
      </Space>

    </Menu>
  );

  return (
    <Dropdown
      overlay={menu}
      visible={dropdownVisible}
      onVisibleChange={visible => setDropdownVisible(visible)}
      trigger={['click']}
      placement="bottomRight"
      align={{
        offset: [6, 12],
      }}
      overlayStyle={{ zIndex: 1050, position: 'fixed' }}
      getPopupContainer={() => document.body}
    >

      <Button
        type="text"
        icon={
          <img
            src={IconSet}
            style={{
              width: 16,
              height: 16,
              verticalAlign: 'middle',

            }}

          />
        }
        style={{

          background: 'transparent',

        }
        }

      />
    </Dropdown>
  );
};

export default ColumnSelector;