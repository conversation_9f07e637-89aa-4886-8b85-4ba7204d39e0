import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { usePapaParse } from 'react-papaparse';
import { downloadInventoryTemplate } from '@/modules-ampcon/apis/wireless_inventory_api.jsx';
import { Button, Input, Alert, Typography, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { color } from 'framer-motion';

const transformHeader = (header) => header.replace(/"/g, '').trim();

const fileToString = async (file) =>
  new Promise((resolve) => {
    const reader = new FileReader();
    reader.readAsText(file);
    reader.onload = ({ target: { result = null } }) => resolve(result);
    reader.onerror = () => resolve(null);
  });

const propTypes = {
  setPhase: PropTypes.func.isRequired,
  setDevices: PropTypes.func.isRequired,
  setIsCloseable: PropTypes.func.isRequired,
  refreshId: PropTypes.string.isRequired,
};

const ImportDeviceFileAntd = ({ setPhase, setDevices, setIsCloseable, refreshId }) => {
  const { t } = useTranslation();
  const [result, setResult] = useState(null);
  const { readString } = usePapaParse();

  const parseFile = async (file) => {
    setResult({ isLoading: true });

    const fileStr = await fileToString(file);

    if (fileStr === null) {
      setResult({ error: t('generalErrorParsingFile') || 'General error while parsing file' });
    } else {
      const csvConfig = {
        header: true,
        transformHeader,
        skipEmptyLines: true,
        quoteChar: '"',
      };
      const data = readString(fileStr, csvConfig);

      if (data.errors.length > 0) {
        setResult({ error: `Error on row ${data.errors[0].row}: ${data.errors[0].message}` });
      } else {
        setResult({ deviceList: data.data });
      }
    }
  };

  const onChange = (e) => {
    setIsCloseable(false);
    if (e.target.files?.length > 0) parseFile(e.target.files[0]);
  };

  const handleDownloadTemplate = (e) => {
    e.preventDefault();
    downloadInventoryTemplate()
      .then((response) => {
        const url = window.URL.createObjectURL(response);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'inventory_import_template.csv';
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      })
      .catch((error) => {
        if (error.response && error.response.data) {
          const reader = new FileReader();
          reader.onload = function () {
            try {
              const errorData = JSON.parse(reader.result);
              message.error(errorData.info || reader.result);
            } catch (e) {
              message.error(reader.result);
            }
          };
          reader.readAsText(error.response.data);
        } else {
          message.error(error.message || 'Network Error');
        }
      });
  };

  return (
    <div style={{ padding: '0 0 0 0' }}>
      <style jsx global>{`
        .custom-alert .ant-alert-icon {
          color: #367EFF !important;
        }
      `}</style>
      {/* <Alert
        message={'Note：' + t('devices.import_device_warning')}
        type="info"
        showIcon
        closable
        className="custom-alert"
        style={{
          marginBottom: '30px',
          background: '#F3F8FF',
          color: '#367EFF',
          border: 'none',
        }}
      /> */}
      <Alert
        className="custom-trace-alert"
        message={'Note:' + t('devices.import_device_warning')}
        type="info"
        showIcon
        closable
        style={{ marginBottom: 30 }}
      />

      <div style={{ marginBottom: '30px', fontSize: '14px', textAlign: 'left', paddingLeft: 0 }}>
        {/* <Typography.Title level={10} style={{ marginBottom: '30px', fontSize: '14px', textAlign: 'left', paddingLeft: 0 }} > */}
        {/* {t('devices.import_explanation')} */}
        <Typography.Text >
          {'To bulk import devices, you need to use a CSV file with the following columns: '}
        </Typography.Text>
        <Typography.Text strong>
          {'SerialNumber，DeviceType，Name，Label，Description， Note. '}
        </Typography.Text>
        <Typography.Text>
          {'Download device import template '}
        </Typography.Text>

        <Typography.Text>

        </Typography.Text>
        <a
          href="#"
          onClick={handleDownloadTemplate}
          style={{ color: '#14C9BB', textDecoration: 'underline' }}
        >
          {'('}template.csv{')'}
        </a>

        <Typography.Text>

        </Typography.Text>
        {/* </Typography.Title> */}
      </div>

      <Input
        style={{ width: '25%', marginBottom: '20px', textAlign: 'left', paddingLeft: 5 }}
        type="file"
        accept=".csv"
        onChange={onChange}
        key={refreshId}
        // prefix={<UploadOutlined />}
        placeholder={t('noFileSelected') || 'No file selected'}
      />
      {result?.error && (
        <Alert message={result.error} type="error" showIcon style={{ marginTop: '20px' }} />
      )}
      <br />
      <Button
        type="primary"
        style={{ marginTop: '20px', textAlign: 'left', width: '136px' }}
        onClick={() => {
          setDevices(result.deviceList);
          setPhase(1);
        }}
        disabled={!result?.deviceList}
      >
        {t('devices.test_batch') || 'Test Import Data'}
      </Button>
    </div >
  );
};

ImportDeviceFileAntd.propTypes = propTypes;
export default ImportDeviceFileAntd;
