import { InputNumber } from 'antd';
import React from 'react';

interface Props {
  value?: string;
  onChange?: (value: string) => void;
  [key: string]: any;
}

const IntegerInput = ({ value, onChange, ...props }: Props) => {
  // 将字符串值转换为数字用于InputNumber
  const numberValue = (value === undefined || value === null || value === '' || value === '-') ? undefined : parseInt(String(value), 10);
  const handleChange = (value: number | null) => {
    if (value === null) {
      onChange?.('');
    } else if (Number.isInteger(value)) {
      onChange?.(value.toString());
    }
  };

  return (
    <InputNumber
      value={numberValue}
      onChange={handleChange}
      precision={0}
      {...props}
    />
  );
};

export default IntegerInput;
