/* 可调整列宽表格的样式 */
.resizable-table .react-resizable {
  position: relative;
  background-clip: padding-box;
}

.resizable-table .react-resizable-handle {
  position: absolute;
  right: -5px;
  bottom: 0;
  top: 0;
  width: 10px;
  height: 100%;
  cursor: col-resize;
  z-index: 1;
  /* 添加过渡效果 */
  transition: border-color 0.2s ease;
}

.resizable-table .react-resizable-handle:hover {
  border-right: 2px solid #1890ff;
  /* 添加手柄悬停时的过渡效果 */
  transition: border-color 0.2s ease;
}

/* 确保表头单元格的相对定位 */
.resizable-table th.ant-table-cell {
  position: relative;
  overflow: hidden;
}

/* 调整手柄的样式 */
.resizable-table .react-resizable-handle::before {
  content: '';
  position: absolute;
  right: 3px;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 16px;
  background-color: #d9d9d9;
  transition: background-color 0.2s;
}

.resizable-table .react-resizable-handle:hover::before {
  background-color: #1890ff;
}

/* 防止文本选择干扰拖拽，但不影响其他拖拽功能 */
.resizable-table .react-resizable-handle {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 确保不影响其他拖拽功能 */
.resizable-table .ant-dropdown {
  user-select: auto;
}

.resizable-table .ant-dropdown * {
  user-select: auto;
}

/* 添加拖拽时的全局样式 */
.react-resizable-resizing {
  /* 拖拽过程中禁用选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 确保拖拽时的视觉反馈 */
.resizable-table .react-resizable-handle:active {
  border-right: 2px solid #1890ff;
}

.resizable-table .react-resizable-handle:active::before {
  background-color: #1890ff;
}
