import React from 'react';
import { Alert, Typography, Divider } from 'antd';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { AmpConCustomModal } from "@/modules-ampcon/components/custom_table";
import "@/modules-smb/Wireless/pages/Devices/Action/Form.scss";
import errorIcon from '@/modules-smb/Wireless/assets/Inventory/error.svg';
import successIcon from '@/modules-smb/Wireless/assets/Inventory/success.svg';

const { Title } = Typography;

const propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  pushResult: PropTypes.instanceOf(Object),
};

const defaultProps = {
  pushResult: null,

};

const ConfigurationPushModal = ({ isOpen, onClose, pushResult }) => {
  const { t } = useTranslation();
  const isSuccess = pushResult?.errorCode === 0;
  const isError = pushResult?.errorCode !== 0;
  const getErrorMessage = () => {
    if (!pushResult?.errors || pushResult.errors.length === 0) return null;

    const firstError = pushResult.errors[0];
    const [code, ...detailParts] = firstError.split(':');
    const detail = detailParts.join(':').trim();

    return `Configuration not pushed, error code: ${code.trim()}, detail: ${detail}`;
  };

  const getAlertMessage = () => {
    if (pushResult?.errorCode === -1) return pushResult?.msg;
    if (isError) {
      const parsedError = getErrorMessage();
      return parsedError || t('configurations.push_configuration_explanation', {
        code: pushResult?.errorCode ?? 0,
      });
    }
    return t('configurations.push_success');
  };

  const childItems = (
    <>
      <Alert
        style={{
          marginTop: 8,
          marginBottom: 24,
          backgroundColor: isSuccess
            ? 'rgba(43, 193, 116, 0.1)'
            : 'rgba(245, 63, 63, 0.1)',
          border: `1px solid ${isSuccess ? '#2BC174' : '#F53F3F'}`,
        }}
        type={isSuccess ? "success" : "error"}
        showIcon
        icon={
          <img
            src={isSuccess ? successIcon : errorIcon}
            alt={isSuccess ? 'success' : 'error'}
            style={{ width: 20, height: 20 }}
          />
        }
        message={
          <span style={{ color: isError ? '#F53F3F' : '#2BC174', fontWeight: 500 }}>
            {getAlertMessage()}
          </span>
        }
      />
      {isSuccess && (
        <div style={{ marginTop: 24 }}>
          <Title level={5}>{t('configurations.applied_configuration')}</Title>
          <div style={styles.scrollBox}>
            <pre>{JSON.stringify(JSON.parse(pushResult?.appliedConfiguration || '{}'), null, 2)}</pre>
          </div>

          <Divider style={{ marginLeft: "0px", width: "616px" }} />

         <Title level={5} style={{ marginTop: 32 }}>{t('common.errors')}</Title>
          <div style={styles.scrollBox}>
            <pre>{JSON.stringify(pushResult?.errors, null, 2)}</pre>
          </div>

          <Divider style={{ marginLeft: "0px", width: "616px" }} />

          <Title level={5} style={{ marginTop: 32 }}>{t('common.warnings')}</Title>
          <div style={styles.scrollBox}>
            <pre>{JSON.stringify(pushResult?.warnings, null, 2)}</pre>
          </div>
        </div>
      )}
    </>
  );

  return (
    <AmpConCustomModal
      title={t('configurations.configuration_push_result')}
      isModalOpen={isOpen}
      onCancel={onClose}
      modalClass="ampcon-max-modal"
      footer={null}
      childItems={childItems}
    />
  );
};

const styles = {
  scrollBox: {
    border: '1px solid #d9d9d9',
    borderRadius: 4,
    padding: '0px 16px',
    height: '25vh',
    overflowY: 'auto',
    backgroundColor: '#fafafa',
    fontSize: 12,
    margin: '24px 0 32px 0', 
  },
};

ConfigurationPushModal.propTypes = propTypes;
ConfigurationPushModal.defaultProps = defaultProps;

export default ConfigurationPushModal;
