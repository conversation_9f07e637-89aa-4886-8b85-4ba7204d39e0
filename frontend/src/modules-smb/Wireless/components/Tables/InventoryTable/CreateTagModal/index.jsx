import React, { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { Button, Form, Input, Select, Switch, Row, Col, message, Divider } from 'antd';
import { axiosProv } from '@/modules-smb/utils/axiosInstances';
import { useGetDeviceTypes } from "@/modules-ampcon/apis/upgrade_api";
import { useGetVenues } from '@/modules-smb/hooks/Network/Venues';
import LabelSelect from '@/modules-smb/Wireless/components/FormFields/LabelSelect';
import LogoAdd from "@/modules-smb/Wireless/assets/Inventory/Logo_Add.png";
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';
import './Form.scss';

const { Option } = Select;

const propTypes = {
  refresh: PropTypes.func.isRequired,
  entityId: PropTypes.string,
  subId: PropTypes.string,
  deviceClass: PropTypes.string,
  venueId: PropTypes.string,
};

const defaultProps = {
  entityId: '',
  subId: '',
  deviceClass: '',
  venueId: '',
};

const CreateTagModal = ({ refresh, entityId, venueId, subId, deviceClass }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [deviceTypes, setDeviceTypes] = useState([]);
  const [venues, setVenues] = useState([]);
  const [isVenueDisabled, setIsVenueDisabled] = useState(!!venueId);

  // 数据获取逻辑
  const { data: deviceTypesData } = useGetDeviceTypes();
  const { data: venuesData } = useGetVenues({ t, toast: message });

  useEffect(() => {
    if (deviceTypesData) setDeviceTypes(deviceTypesData);
  }, [deviceTypesData]);

  useEffect(() => {
    if (venuesData) setVenues(venuesData);
  }, [venuesData]);

  useEffect(() => {
    const newDisabled = venueId !== undefined && venueId !== null && venueId !== '';
    setIsVenueDisabled(newDisabled);
    form.setFieldsValue({ entity: newDisabled ? `ven:${venueId}` : '' });
  }, [venueId, form]);

  const createDevice = useMutation((newObj) =>
    axiosProv.post(`inventory/${newObj.serialNumber}`, newObj)
  );

  const createParameters = (formData) => ({
    serialNumber: formData.serialNumber.toLowerCase(),
    name: formData.name,
    deviceRules: formData.deviceRules,
    deviceType: formData.deviceType,
    devClass: deviceClass !== '' ? deviceClass : formData.devClass,
    description: formData.description || undefined,
    notes: formData.note ? [{ note: formData.note }] : undefined,
    entity: formData.entity === '' || formData.entity.split(':')[0] !== 'ent' ? '' : formData.entity.split(':')[1],
    venue: formData.entity === '' || formData.entity.split(':')[0] !== 'ven' ? '' : formData.entity.split(':')[1],
    doNotAllowOverrides: formData.doNotAllowOverrides,
    subscriber: subId !== '' ? subId : '',
    labelsName: formData.labelsName?.length ? formData.labelsName.join(',') : ''
  });

  // 表单提交处理
  const handleFinish = async (formValues) => {
    try {
      setConfirmLoading(true);
      const values = {
        ...formValues,
        deviceRules: { rrm: 'inherit', rcOnly: 'inherit', firmwareUpgrade: 'inherit' },
        devClass: deviceClass || 'any',
      };
      const params = createParameters(values);

      await createDevice.mutateAsync(params, {
        onSuccess: () => {
          message.success(t('crud.success_create_obj', { obj: t('certificates.device') }));
          refresh();
          setOpen(false);
          form.resetFields();
        },
        onError: (e) => {
          message.error(t('crud.error_create_obj', {
            obj: t('certificates.device'),
            e: e?.response?.data?.ErrorDescription,
          }));
        },
      });
    } finally {
      setConfirmLoading(false);
    }
  };

  // 取消逻辑
  const handleCancel = () => {
    form.resetFields();
    setOpen(false);
  };

  // 表单初始值
  const initialFormValues = {
    serialNumber: '',
    name: '',
    description: '',
    deviceType: deviceTypes[0] || '',
    deviceRules: { rrm: 'inherit', rcOnly: 'inherit', firmwareUpgrade: 'inherit' },
    devClass: deviceClass || 'any',
    note: '',
    entity: (venueId !== '' && venueId !== undefined && venueId !== null) ? `ven:${venueId}` : '',
    doNotAllowOverrides: false,
    labelsName: "",
  };


  return (
    <>
      <Button
        type="primary"
        onClick={() => setOpen(true)}
        style={{ display: 'flex', alignItems: 'center' }}
        icon={<img src={LogoAdd} alt="Add" style={{ width: 16, height: 16, marginRight: 4 }} />}
      >
        {t('common.create')}
      </Button>

      <FormModal
        open={open}
        title={t('common.create')}
        onCancel={handleCancel}
        onFinish={handleFinish}
        initialValues={initialFormValues}
        form={form}
        modalClass="ampcon-middle-modal"
      >
        <Row className='CreateTagModal'>
          <Col xs={24} >
            <Form.Item
              name="serialNumber"
              label={t('inventory.serial_number')}
              rules={[
                { required: true, message: t('form.required') },
                {
                  validator: (_, val) => {
                    if (!val) return Promise.resolve();
                    if (val.length !== 12 || !/^[a-fA-F0-9]+$/.test(val)) {
                      return Promise.reject(new Error(t('inventory.invalid_serial_number')));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} >
            <Form.Item
              name="name"
              label={t('common.name')}
              rules={[
                { required: true, message: t('form.required') },
                {
                  validator: (_, val) => {
                    if (!val) return Promise.resolve();
                    const reg = /^([a-zA-Z0-9]{1,2}|[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])$/;
                    if (!reg.test(val)) return Promise.reject(new Error(t('inventory.invalid_device_name')));
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} >
            <Form.Item
              name="deviceType"
              label={t('inventory.device_type')}
              rules={[{ required: true, message: t('form.required') }]}
            >
              <Select style={{ width: '100%' }}>
                {deviceTypes.map(type => (
                  <Option key={type} value={type}>{type}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} >
            <Form.Item
              name="entity"
              label={t('inventory.site')}
            >
              <Select disabled={isVenueDisabled} style={{ width: '100%' }}>
                <Option value="">{t('common.none')}</Option>
                {venues.map(ven => (
                  <Option key={`ven:${ven.id}`} value={`ven:${ven.id}`}>
                    {`${ven.name}${ven.description ? `: ${ven.description}` : ''}`}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} >
            <Form.Item
              noStyle
              shouldUpdate={(prev, curr) => prev.entity !== curr.entity}
            >
              {({ getFieldValue }) => {
                const entityVal = getFieldValue('entity');
                const siteId = entityVal?.startsWith('ven:') ? parseInt(entityVal.split(':')[1]) : null;
                return (
                  <Form.Item
                    name="labelsName"
                    label={t('inventory.label')}
                  >
                    <LabelSelect
                      siteId={siteId}
                      value={form.getFieldValue('labelsName') || []}
                      onChange={val => form.setFieldsValue({ labelsName: val })}
                      className="custom-label-select"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                );
              }}
            </Form.Item>
          </Col>
          <Col xs={24} >
            <Form.Item
              name="doNotAllowOverrides"
              label={t('overrides.ignore_overrides')}
              valuePropName="checked"
              rules={[{ required: true, message: t('form.required') }]}
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col xs={24} >
            <Form.Item
              name="description"
              label={t('common.description')}
            >
              <Input style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} >
            <Form.Item
              name="note"
              label={t('common.note')}
            >
              <Input style={{ width: '100%' }} />
            </Form.Item>
          </Col>


        </Row>
      </FormModal>
    </>
  );
};

CreateTagModal.propTypes = propTypes;
CreateTagModal.defaultProps = defaultProps;

export default CreateTagModal;
