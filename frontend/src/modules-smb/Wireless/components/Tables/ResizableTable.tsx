import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Table } from 'antd';
import { Resizable } from 'react-resizable';
import 'react-resizable/css/styles.css';
import './ResizableTable.css';

// 可调整大小的表头组件
const ResizableTitle = (props: any) => {
  const { onResize, width, onResizeStart, onResizeStop, resizable, ...restProps } = props as any;

  // 如果没有传入 onResize（也未显式标记 resizable=true），则认为该列不可调整，直接渲染 th
  if (!onResize && resizable !== true) {
    return <th {...restProps} />;
  }

  // 列为可调整时才渲染 Resizable；如果没有显式宽度，使用 1px 占位以显示 handle
  const widthForResizable = typeof width === 'number' ? width : 1;

  return (
    <Resizable
      width={widthForResizable}
      height={0}
      handle={
        <span
          className="react-resizable-handle"
          style={{
            position: 'absolute',
            right: 0,
            top: 0,
            height: '100%',
            width: 0,
            marginRight: 0,
            cursor: 'col-resize',
            userSelect: 'none'
          }}
          onClick={(e) => {
            e.stopPropagation();
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            try {
              document.body.style.cursor = 'col-resize';
              document.body.style.userSelect = 'none';
            } catch {}
            onResizeStart && onResizeStart(e, { size: { width: 0, height: 0 } });
          }}
        />
      }
      onResize={onResize}
      onResizeStart={(e, data) => {
        try {
          document.body.style.cursor = 'col-resize';
          document.body.style.userSelect = 'none';
        } catch {}
        onResizeStart && onResizeStart(e, data);
      }}
      onResizeStop={(e, data) => {
        try {
          document.body.style.cursor = '';
          document.body.style.userSelect = '';
        } catch {}
        onResizeStop && onResizeStop(e, data);
      }}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th {...restProps} />
    </Resizable>
  );
};

// 可调整列宽的表格组件
const ResizableTable = ({ columns: initialColumns, resizableColumns = false, ...props }) => {
  const [columns, setColumns] = useState(() => {
    if (!resizableColumns) return initialColumns;

    return initialColumns.map((col: any) => {
      const parsedWidth = (col && col.width != null) ? parseInt(String(col.width), 10) : undefined;
      return {
        ...col,
        // 如果没有显式宽度，则不要默认设为 1，保留 undefined 让 AntD 自动布局
        width: typeof col.width === 'number' ? col.width : (Number.isFinite(parsedWidth) ? parsedWidth : undefined),
        minWidth: typeof col.minWidth === 'number' ? col.minWidth : undefined,
        resizable: col.resizable !== false,
      };
    });
  });

  // 当外部列发生变化时，同步更新内部状态
  useEffect(() => {
    if (!resizableColumns) {
      setColumns(initialColumns);
      return;
    }

    // 保持已调整的宽度，但更新列的顺序和其他属性
    const updatedColumns = initialColumns.map((newCol: any) => {
      const newKey = (newCol && (newCol.key ?? newCol.dataIndex));
      const existingCol = columns.find((col: any) => (col.key ?? col.dataIndex) === newKey);
      const parsedNewWidth = (newCol && newCol.width != null) ? parseInt(String(newCol.width), 10) : undefined;
      return {
        ...newCol, // 保留原始列的所有属性
        width: typeof (existingCol?.width) === 'number'
          ? existingCol.width
          : (typeof newCol.width === 'number' ? newCol.width : (Number.isFinite(parsedNewWidth) ? parsedNewWidth : undefined)),
        minWidth: typeof newCol.minWidth === 'number' ? newCol.minWidth : undefined,
        // 确保resizable属性被正确保留
        resizable: newCol.resizable !== false,
      };
    });

    setColumns(updatedColumns);
  }, [initialColumns, resizableColumns]);

  const handleResize = useCallback((index) => (e, { size }) => {
    setColumns((prevColumns) => {
      const nextColumns = [...prevColumns];
      if (!nextColumns[index]) return prevColumns;
      const minWidth = typeof nextColumns[index].minWidth === 'number' ? nextColumns[index].minWidth : 0;
      const newWidth = Math.max(size.width, minWidth); // 允许到 0，不再强制更大

      // 只有当宽度真正改变时才更新
      if (nextColumns[index].width !== newWidth) {
        nextColumns[index] = {
          ...nextColumns[index],
          width: newWidth,
        };
      }

      return nextColumns;
    });
  }, []);

  // 拖拽开始时将该列宽度立即同步为真实渲染宽度，避免需要先拖一段距离
  const handleResizeStart = useCallback((index) => (e) => {
    const handleEl = e?.target as HTMLElement | null;
    const th = handleEl?.closest ? (handleEl.closest('th') as HTMLElement | null) : null;
    const measured = th ? th.getBoundingClientRect().width : undefined;
    if (!measured) return;
    setColumns((prev) => {
      const next = [...prev];
      if (!next[index]) return prev;
      const minWidth = typeof next[index].minWidth === 'number' ? next[index].minWidth : 0;
      const ensured = Math.max(measured, minWidth);
      if (next[index].width !== ensured) {
        next[index] = { ...next[index], width: ensured };
      }
      return next;
    });
  }, []);

  const mergedColumns = useMemo(() => {
    if (!resizableColumns) return initialColumns;

    return columns.map((col, index) => {
      // 如果列明确设置了 resizable: false，则不添加调整功能
      if (col.resizable === false) {
        return col;
      }
      
      // 确保resizable属性被正确设置
        // 我们在 columns 初始化阶段已经将可解析的宽度转为 number 或 undefined，
        // 因此这里只需在是数字时传入，否则传 undefined，让 AntD/浏览器自适应。
        const headerWidth = typeof col.width === 'number' ? col.width : undefined;
        return {
          ...col,
          resizable: true,
          onHeaderCell: (column: any) => ({
            // 只有在显式宽度存在时才传给 ResizableTitle，未指定则交给浏览器/AntD 自适应
            width: headerWidth,
            onResize: handleResize(index),
            // 不延时迟，立即同步宽度
            onResizeStart: handleResizeStart(index),
            ...(col.onHeaderCell ? col.onHeaderCell(column) : {}),
          }),
        };
    });
  }, [columns, handleResize, resizableColumns]);

  const components = useMemo(() => {
    if (!resizableColumns) return undefined;

    return {
      header: {
        cell: ResizableTitle,
      },
    };
  }, [resizableColumns]);

  return (
    <Table
      {...props}
      columns={mergedColumns}
      components={components}
      className={`${props.className || ''} ${resizableColumns ? 'resizable-table' : ''}`}
    />
  );
};

export default ResizableTable;