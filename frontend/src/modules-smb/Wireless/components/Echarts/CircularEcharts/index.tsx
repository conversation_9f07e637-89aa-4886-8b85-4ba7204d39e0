import React, { memo, useEffect, useRef } from "react";
import * as echarts from "echarts";

export const CircularProgressEcharts = memo(({
    value, // 环形进度条的值，如内存使用百分比
    color = "#52c41a", // 占比部分颜色，默认绿色
    bgColor = "#ccc", // 未占比部分颜色，默认灰色
    height = "120px",
    width = "120px"
}) => {
    const chartRef = useRef();
    useEffect(() => {
        const myChart = echarts.init(chartRef.current);
        const option = {
            series: [
                {
                    type: "pie",
                    radius: ["45%", "65%"], // 环形样式
                    center: ["50%", "50%"],
                    data: [
                        {
                            value,
                            name: "",
                            itemStyle: {
                                color
                            }
                        },
                        {
                            value: 100 - value,
                            name: "",
                            itemStyle: {
                                color: bgColor
                            }
                        }
                    ],
                    label: {
                        show: false
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: "rgba(0, 0, 0, 0.5)"
                        }
                    }
                }
            ]
        };
        myChart.setOption(option);

        const handleResize = () => {
            myChart.resize();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
            myChart.dispose();
        };
    }, [value, color, bgColor]);
    return (
        <div style={{ height: height, width,marginLeft: "auto" }} ref={chartRef} />
    );
});

