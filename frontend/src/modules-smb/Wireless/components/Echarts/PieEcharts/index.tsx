  import React, {
    useRef,
    useEffect,
    memo,
    useMemo,
  } from 'react';
  import { Flex } from 'antd';
  import * as echarts from 'echarts';

  export interface PieDataItem {
    name: string;
    value: number;
  }

  export interface PieEchartsProps {
    seriesData: PieDataItem[];
    name: string;
    chartType?: 'pie' | 'ring';
    colorList?: string[];
    height?: string;
    maxWidth?: string;
    onClicked?: (params: echarts.ECElementEvent) => void;
    showPercent?: boolean;
    legendDirection?: 'vertical' | 'horizontal';
  }

  const PieEcharts: React.FC<PieEchartsProps> = ({
    seriesData,
    name,
    chartType = 'pie',
    colorList = [],
    height = '28vh',
    maxWidth = '450px',
    onClicked = null,
    showPercent = true,
    legendDirection
  }) => {
    const chartRef = useRef<HTMLDivElement>(null);

    const chartRadius: [string, string] = useMemo(() => {
      switch (chartType) {
        case 'ring':
          return ['45%', '65%'];
        case 'pie':
        default:
          return ['0%', '70%'];
      }
    }, [chartType]);

    const total = useMemo(() => {
      return seriesData.reduce((sum, item) => sum + item.value, 0);
    }, [seriesData]);

    useEffect(() => {
      if (!chartRef.current) return;
      const myChart = echarts.init(chartRef.current);

      const option: echarts.EChartsOption = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: { color: '#fff' },
          formatter: (params: any) => {
            const percent = total === 0 ? 0 : ((params.value / total) * 100).toFixed(2);
            return `<div style="line-height:1.5">
          <div>${params.name}</div>
          <div>
            <span style="
              display: inline-block;
              width: 10px;
              height: 10px;
              background: ${params.color};
              margin-right: 5px;
              border-radius: 2px;
            "></span>
            ${params.name}: ${params.value} (${percent}%)
          </div>
        </div>`;
          },
        },
        legend: {
          orient: legendDirection === 'horizontal' ? 'horizontal' : 'vertical',
          bottom: legendDirection === 'horizontal' ? 10 : 'auto',
          top: legendDirection === 'horizontal' ? 'auto' : 'center',
          left: legendDirection === 'horizontal' ? 'center' : 10,
          itemGap: 10,
          itemWidth: 12,
          itemHeight: 12,
          formatter(name: string) {
            const nameSplit = name.split(/\s+/);
            if (
              ['Usage', 'Free', 'Used', 'Unused', 'Normal', 'Abnormal', 'Expired'].includes(
                nameSplit[0],
              )
            ) {
              return showPercent
                ? `{name|${nameSplit[0]}}{count|${nameSplit[1]}%}`
                : `{name|${nameSplit[0]}}{count|${nameSplit[1]}}`;
            }
            return name;
          },
        },
        textStyle: {
          rich: {
            name: {
              color: '#929A9E',
              lineHeight: 20,
              textAlign: 'center',
              display: 'inline-block',
              width: maxWidth === '450px' ? 65 : 40,
            },
            count: {
              fontSize: 14,
              fontWeight: 700,
              lineHeight: 20,
              textAlign: 'center',
              display: 'inline-block',
              width: 10,
            },
          },
        },
        series: [
          {
            name,
            type: 'pie',
            center: chartType === 'pie' 
      ? legendDirection === 'horizontal' ? ['40%', '40%'] : ["60%", "40%"] 
      : legendDirection === 'horizontal' ? ['50%', '40%'] : ["75%", "40%"],
            radius: chartRadius,
            label: {
              show: chartType === 'ring',
              position: 'center',
              formatter: () => {
                return showPercent ? '' : `{value|${total}}\n{total|Total}`;
              },
              rich: {
                value: {
                  color: '#333',
                  fontSize: maxWidth === '450px' ? 20 : 16,
                  fontWeight: 'bold',
                  lineHeight: 30,
                },
                total: {
                  color: '#333',
                  fontSize: maxWidth === '450px' ? 16 : 14,
                  lineHeight: 20,
                },
              },
            },
            data: seriesData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      };

      if (colorList.length) {
        option.color = colorList;
      }

      myChart.setOption(option);

      if (onClicked) {
        myChart.on('click', onClicked);
      }

      const handleResize = () => myChart.resize();
      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        myChart.dispose();
      };
    }, [seriesData, chartRadius, colorList, name, onClicked, showPercent, maxWidth, total, chartType]);

    return seriesData?.length > 0 ?(
      <Flex justify="center" align="center">
        <div style={{ height, width: '100%', maxWidth }} ref={chartRef} />
      </Flex>
    ):null;
  };

  export default memo(PieEcharts);
