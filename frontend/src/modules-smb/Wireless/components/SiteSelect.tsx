import React, { useState, useEffect, useMemo } from "react";
import { Select, Input } from "antd";
import { useGetVenues } from "@/modules-smb/hooks/Network/Venues";
import { useEntityFavorite } from "@/modules-smb/Wireless/hooks/useEntityFavorite";
import "../assets/wireless.scss";
import { SearchOutlined } from '@ant-design/icons';
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";

type Option = { value: string; label: string; type: "venue" | "entity" };

const SiteSelect = ({ onChange }) => {
    const { isFavorited, toggleFavorite, getFirstVenueFavoriteId } = useEntityFavorite();
    const getVenues = useGetVenues();
    const [searchValue, setSearchValue] = useState('');
    const [selectedValue, setSelectedValue] = useState('');
    const [defaultLabel, setDefaultLabel] = useState('');
    const { resetFromOtherPage } = useSiteStore();
    const {
        selectedSiteId,
        setSelectedSiteId,
        isAllSitesSelected,
        setAllSites,
        resetToDefault
    } = useSiteStore();

    // 处理选项数据
    const allOptions = useMemo(() => {
        if (!getVenues.data) return [];

        const options = getVenues.data
            .sort((a, b) => {
                if (String(a.id) === "0") return -1;
                if (String(b.id) === "0") return 1;
                return a.name.localeCompare(b.name);
            })
            .map(venue => ({
                label: venue.name,
                value: venue.id,
                type: "venue"
            }));

        // 在options头部添加一个默认选项
        options.unshift({ label: "All Sites", value: "all", type: "venue" });
        return options;
    }, [getVenues.data]);

    // 根据搜索值过滤选项
    const filteredOptions = useMemo(() => {
        if (!searchValue) return allOptions;
        return allOptions.filter(option =>
            option.label.toLowerCase().includes(searchValue.toLowerCase())
        );
    }, [allOptions, searchValue]);

    useEffect(() => {
        // 当组件加载时，如果是第一次从其他页面切换过来
        if (!window.location.hash && !isAllSitesSelected) {
            resetFromOtherPage();
        }
    }, []);

    // 组件加载更新站点hook
    useEffect(() => {
        if (typeof getVenues.refetch === 'function') {
            getVenues.refetch();
        }
    }, []);

    useEffect(() => {
        if (allOptions.length > 0) {
            const hashId = window.location.hash.substring(1);
            const favId = getFirstVenueFavoriteId();

            // 优先级: 1.URL hash 2.全局状态 3.收藏站点 4.第一个站点
            const defaultId = hashId || selectedSiteId || favId || allOptions[0]?.value;
            

            setSelectedValue(defaultId);
            setDefaultLabel(allOptions.find(o => o.value === defaultId)?.label || "");

            // 更新URL但不触发导航
            if (hashId !== defaultId) {
                window.history.replaceState({}, "", `#${defaultId}`);
            }
        }
    }, [allOptions, selectedSiteId]);

    const handleChange = value => {
        if (value === "all") {
            setAllSites(); 
        } else {
            setSelectedSiteId(value); 
        }

        if (onChange) {
            onChange(value);
        }
    };


    const handleSearchChange = (e) => {
        setSearchValue(e.target.value);
    };

    // 当下拉框关闭时清空搜索值
    const handleDropdownVisibleChange = (open) => {
        if (!open) {
            setSearchValue('');
        }
    };
    const displayValue = useMemo(() => {
        return isAllSitesSelected ? "all" : selectedSiteId;
    }, [selectedSiteId, isAllSitesSelected]);

    return (
        <div className="site-select-container">
            <span className="site-title">Site</span>
            <Select
                className="site-select"
                placeholder={defaultLabel}
                options={filteredOptions}
                value={displayValue}
                onChange={handleChange}
                style={{ width: 260 }}
                onDropdownVisibleChange={handleDropdownVisibleChange}
                dropdownRender={(menu) => (
                    <>
                        <div style={{ padding: 8 }}>
                            <Input
                                prefix={<SearchOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                                placeholder="Search sites..."
                                value={searchValue}
                                onChange={handleSearchChange}
                                allowClear
                            />
                        </div>
                        <div >
                            {filteredOptions.length > 0 ? menu : <div style={{ padding: 8, textAlign: 'center' }}>No results found</div>}
                        </div>
                    </>
                )}
            />
        </div>
    );
};

export default SiteSelect;