import React, { useState, useEffect, useMemo } from "react";
import { Button, Dropdown, Menu } from "antd";
import Icon from "@ant-design/icons";
import startMarkSvg from "../assets/Entities/venue_star_mark.svg?react";
import starUnmarkSvg1 from "../assets/Entities/venue_star_unmark1.svg?react";
import starUnmarkSvg from "../assets/Entities/venue_star_unmark.svg?react";
import selectSvg from "../assets/Entities/venue_select.svg?react";
import { useGetVenues } from "@/modules-smb/hooks/Network/Venues";
import { CaretDownOutlined } from '@ant-design/icons';
import homeSvg from "../assets/Entities/venue_home.svg?react";
import { useEntityFavorite } from "@/modules-smb/Wireless/hooks/useEntityFavorite";
import { useNavigate } from 'react-router-dom';
import { normalize } from "path";
import { PlusOutlined } from '@ant-design/icons';
import { Center } from "@chakra-ui/react";
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";


type Option = { value: string; label: string; type: "venue" | "entity" };

const VenueSelect = ({ onChange }) => {
    const { isFavorited, toggleFavorite, getFirstVenueFavoriteId } = useEntityFavorite();
    const getVenues = useGetVenues();
    const [hoverStar, setHoverStar] = useState(false);
    const navigate = useNavigate();
    const [hoveredItem, setHoveredItem] = useState<string | null>(null);
    const [isHovered, setIsHovered] = useState(false);
    const {
        selectedSiteId,
        setSelectedSiteId,
        isAllSitesSelected,
        setAllSites
    } = useSiteStore();

    const options: Option[] = (() => {
        if (!getVenues.data) return [];

        return [...getVenues.data]
            .sort((a, b) => {
                if (String(a.id) === "0") return -1;
                if (String(b.id) === "0") return 1;
                return a.name.localeCompare(b.name);
            })
            .map(venue => ({
                label: venue.name,
                value: venue.id,
                type: "venue"
            }));
    })();

    const maxLabelLength = options.reduce(
        (max, o) => Math.max(max, o.label.length),
        0
    );

    const getHashId = () => {
        const hash = window.location.hash;
        if (hash) {
            return hash.substring(1);
        }
        return null;
    };

    let myFavoriteId: string = getFirstVenueFavoriteId();
    let defaultValue: string = myFavoriteId;
    let defaultLabel: string = "";

    if (getVenues.data) {
        defaultValue = defaultValue == null ? getVenues.data[0].id : defaultValue;
        defaultValue = getHashId() || defaultValue;
        defaultLabel = options.find(option => option.value === defaultValue)?.label || "";
        window.history.replaceState({}, "", `#${defaultValue}`);
    }

    const [selectedValue, setSelectedValue] = useState(defaultValue);
    const [isFavorit, setIsFavorit] = useState(myFavoriteId && myFavoriteId === defaultValue);

    React.useEffect(() => {
        if (typeof getVenues.refetch === 'function') {
            getVenues.refetch();
        }

        if (defaultValue != null && defaultValue !== selectedValue) {
            setSelectedValue(defaultValue);
            setIsFavorit(myFavoriteId && myFavoriteId === defaultValue);
        }
    }, [defaultValue]);

    const handleChange = value => {
        if (value === "all") {
            setAllSites();
        } else {
            setSelectedSiteId(value);
        }
        onChange(value);
    };
    const displayValue = useMemo(() => {
        return isAllSitesSelected ? "all" : selectedSiteId;
    }, [selectedSiteId, isAllSitesSelected]);

    useEffect(() => {
        if (getVenues.data) {
            const favId = getFirstVenueFavoriteId();
            const hashId = window.location.hash.substring(1);

            // 优先级: 1.URL hash 2.全局状态 3.收藏站点 4.第一个站点
            const defaultId = hashId || selectedSiteId || favId || getVenues.data[0]?.id;

            setSelectedValue(defaultId);
            setIsFavorit(favId === defaultId);

            // 更新URL但不触发导航
            if (hashId !== defaultId) {
                window.history.replaceState({}, "", `#${defaultId}`);
            }
        }
    }, [getVenues.data, selectedSiteId]);

    const checkFavorited = (id: string) => {
        return getFirstVenueFavoriteId() === id;
    };


    const menu = (
        <Menu
            style={{
                minWidth: 300,
                borderRadius: 8,
                padding: '10px 0 10px 0',
            }}
        >
            {options.map(option => (
                <Menu.Item
                    key={option.value}
                    onClick={() => handleChange(option.value)}
                    onMouseEnter={() => setHoveredItem(option.value)}
                    onMouseLeave={() => setHoveredItem(null)}
                    style={{
                        height: 30,
                        lineHeight: '30px',
                        fontSize: 16,
                        backgroundColor:
                            selectedValue === option.value ? 'rgba(20, 201, 187, 0.1)' :
                                hoveredItem === option.value ? '#F4F5F7' : 'transparent',
                        color: selectedValue === option.value ? '#14C9BB' : '#000',
                        //padding: '0 16px',
                        margin: '0 8px',
                        borderRadius: 4,
                        transition: 'background-color 0.3s',
                        position: 'relative',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    }}
                >
                    <span
                        style={{
                            display: "inline-block",
                            minWidth: `${maxLabelLength}ch`,
                        }}
                    >
                        {option.label}
                    </span>
                    {checkFavorited(option.value) && (
                        <Icon
                            component={starUnmarkSvg1}
                            style={{
                                width: 16,
                                height: 16,
                                color: '#14C9BB',
                                marginLeft: 120,
                            }}
                        />
                    )}
                </Menu.Item>
            ))}
            <Menu.Divider style={{ borderTop: '1px solid #E7E7E7', marginBottom: -6, marginTop: 8 }} />
            <Menu.Item
                key="create"
                onClick={() => navigate('/resource/site_management')}
                style={{
                    display: 'flex',
                    justifyContent: 'center',
                    width: '100%',
                    height: 30,
                    color: isHovered ? '#34DCCF' : '#14C9BB',
                    background: 'transparent',
                    margin: 0,
                    borderRadius: 0,
                }}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
            >
                <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: -10,
                    whiteSpace: 'nowrap',
                    color: isHovered ? '#34DCCF' : '#14C9BB'
                }}>
                    <PlusOutlined style={{
                        fontSize: 15,
                        color: isHovered ? '#34DCCF' : '#14C9BB',
                        // textAlign: 'center',
                        // marginLeft: 195,
                        // marginRight: 5,
                        marginBottom: -13
                    }} />
                    <span style={{ fontWeight: 400, fontSize: 15, marginBottom: -13 }}>Create New Site</span>
                </div>

            </Menu.Item>
        </Menu>
    );

    if (!getVenues.data || getVenues.data.length === 0) {
        return (
            <Button
                type="primary"
                onClick={() => navigate('/resource/site_management')}
                icon={<PlusOutlined />}
                style={{
                    backgroundColor: '#14C9BB',
                    borderColor: '#14C9BB'
                }}
            >
                Create New Site
            </Button>
        );
    }

    return (
        <Dropdown
            overlay={menu}
            trigger={['click']}
            overlayStyle={{
                minWidth: 300
            }}
        >
            <div style={{
                display: 'flex',
                alignItems: 'center',
                position: 'relative',
                cursor: 'pointer',
            }}>
                <Icon
                    component={homeSvg}
                    style={{
                        //width: 32,
                        height: 32,
                        color: '#212519',
                        marginRight: 4,
                        fontSize: 26
                    }}
                />
                <span style={{
                    marginRight: 4,
                    height: '24px',
                    fontFamily: 'Lato,Lato',
                    fontWeight: 700,
                    fontSize: '20px',
                    color: '#212529',
                    textAlign: 'left',
                    fontStyle: 'normal',
                    lineHeight: '24px',
                    textTransform: 'none',
                    whiteSpace: 'nowrap',
                }}>
                    {defaultLabel}
                </span>
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        marginRight: 8,
                        cursor: 'pointer',
                        position: 'relative',
                        padding: '1px',
                    }}
                    onMouseEnter={() => setHoverStar(true)}
                    onMouseLeave={() => setHoverStar(false)}
                    onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        const newFavorState = !isFavorit;
                        setIsFavorit(newFavorState);
                        toggleFavorite(selectedValue, newFavorState);
                    }}
                >
                    <Icon
                        component={
                            isFavorit
                                ? starUnmarkSvg1
                                : hoverStar
                                    ? starUnmarkSvg
                                    : startMarkSvg
                        }
                        style={{
                            fontSize: 380,
                            transition: 'all 0.3s',
                            width: '22px',
                            height: '22px',
                            marginRight: -5
                        }}
                    />
                </div>

                <Icon
                    component={selectSvg}
                    style={{
                        fontSize: '18px',
                        color: '#666',
                        marginLeft: 4,
                        verticalAlign: 'middle',
                    }}
                />
            </div >
        </Dropdown>

    );
};

export default VenueSelect;