import React, { useState, useEffect } from 'react';
import { Select, message, Tag, Button, Checkbox, Space, Input } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { fetchLables, createLable, deleteLable } from '@/modules-smb/Wireless/apis/lable';
import '@/modules-smb/Wireless/components/FormFields/LabelSelect/createLabel.scss';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import Logo_Delete from "@/modules-smb/Wireless/assets/Inventory/Delete.png";
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';
import { Form } from 'antd';

const LabelSelect = ({ siteId, value: propValue, onChange }) => {
  const { t } = useTranslation();
  const [labels, setLabels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createForm] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  useEffect(() => {
    if (siteId === null || siteId === undefined) return; 
    loadLabels(searchValue);
  }, [siteId, searchValue]);

  // 加载标签列表
  const loadLabels = async (searchKey = '') => {
    try {
      setLoading(true);
      const params = { siteId };
      if (searchKey.trim()) params.key = searchKey;
      const response = await fetchLables(params);
      setLabels(response.info || []);
    } catch (error) {
      message.error('Failed to load labels');
    } finally {
      setLoading(false);
    }
  };

  // 创建新标签
  const handleCreateLabel = async (values) => {
    try {
      setSubmitting(true);
      if (labels.some(label => label.name === values.name)) {
        message.error('Label name already exists');
        return;
      }
      const response = await createLable({ site_id: siteId, name: values.name });
      if (response?.status === 200) {
        message.success('Create label success');
        loadLabels();
        setCreateModalVisible(false);
        createForm.resetFields();
      } else {
        message.error(response?.info || 'Create label failed');
      }
    } catch (error) {
      message.error('Failed to create label');
    } finally {
      setSubmitting(false);
    }
  };

  // 删除标签
  const handleDeleteLabel = (labelName, e) => {
    e.stopPropagation();
    const label = labels.find(item => item.name === labelName);
    if (!label) return;
    confirmModalAction(
      `Are you sure to delete this label ${labelName}?`,
      async () => {
        try {
          const response = await deleteLable(label.id);
          if (response?.status === 200) {
            message.success('Delete label success');
            const newSelected = (propValue || []).filter(name => name !== labelName);
            onChange(newSelected);
            loadLabels();
          }
        } catch (error) {
          message.error('Failed to delete label');
        }
      }
    );
  };

  // 搜索处理
  const handleSearch = (value) => {
    setSearchValue(value);
  };

  const handleChange = (newValues) => {
    onChange(newValues);
    if (searchValue.trim()) {
      setSearchValue('');
      loadLabels('');
    }
  };


  // 自定义选项渲染（带复选框）
  const optionRender = (oriOption) => {
    const isSelected = (propValue || []).includes(oriOption.value);
    return (
      <Space style={{ width: '100%', justifyContent: 'space-between', alignItems: 'center' }}>
        <Space>
          <Checkbox checked={isSelected} />
          <span className="optionLabel">{oriOption.label}</span>
        </Space>

        <span
          onClick={(e) => handleDeleteLabel(oriOption.value, e)}
          style={{ cursor: 'pointer' }}
        >
          <img
            src={Logo_Delete}
            alt="Delete"
            style={{ width: 16, height: 16, verticalAlign: 'middle' }}
          />
        </span>
      </Space>
    );
  };

  // 自定义下拉菜单底部的创建按钮
 const dropdownRender = (menu) => (
  <div>
    {menu}
    <Button
      type="link"
      icon={<PlusOutlined />}
     style={{ margin: '4px 0px 0px -8px', width: 'calc(100% + 16px)', borderTop: '1px solid #E7E7E7' }}
      onClick={() => setCreateModalVisible(true)}
    >
      {t('inventory.create_label')}
    </Button>
  </div>
);


  const labelOptions = labels.map(label => ({
    key: label.id,
    value: label.name,
    label: label.name
  }));

  return (
    <>
      <Select
        mode="multiple"
        style={{ width: '100%' }}
        value={propValue || []}
        options={labelOptions}
        onChange={handleChange}
        onSearch={handleSearch}
        loading={loading}
        dropdownRender={dropdownRender}
        optionRender={optionRender}
        disabled={siteId === undefined || siteId === null}
        filterOption={false}
        showSearch
        popupClassName="mySelect"
      />

      <FormModal
        open={createModalVisible}
        title={t('inventory.create_label')}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        onFinish={handleCreateLabel}
        form={createForm}
        modalClass="ampcon-middle-modal"
      >
        <Form.Item
          name="name"
          label={t('common.name')}
          rules={[
            { required: true, message: t('form.required') },
            {
              validator: (_, value) => {
                if (!value) return Promise.resolve();
                if (!/^[a-zA-Z0-9]+$/.test(value)) {
                  return Promise.reject(t('form.invalid_label_name'));
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <Input />
        </Form.Item>
      </FormModal>
    </>
  );
};

export default LabelSelect;
