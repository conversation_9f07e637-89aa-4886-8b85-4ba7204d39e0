import React, { useCallback } from 'react';
import { useField } from 'formik';
import PropTypes from 'prop-types';
import Field from './FastMultiSelectInput';

const propTypes = {
  name: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
    }),
  ).isRequired,
  isDisabled: PropTypes.bool,
  isRequired: PropTypes.bool,
  isHidden: PropTypes.bool,
  emptyIsUndefined: PropTypes.bool,
  hasVirtualAll: PropTypes.bool,
  canSelectAll: PropTypes.bool,
  isPortal: PropTypes.bool,
  definitionKey: PropTypes.string,
  placeholder: PropTypes.string,
};

// const defaultProps = {
//   isRequired: false,
//   isDisabled: false,
//   isHidden: false,
//   emptyIsUndefined: false,
//   hasVirtualAll: false,
//   canSelectAll: false,
//   isPortal: false,
//   definitionKey: null,
// };

const MultiSelectField = ({
  options,
  name,
  isDisabled=false,
  label,
  isRequired=false,
  isHidden=false,
  emptyIsUndefined=false,
  canSelectAll=false,
  hasVirtualAll=false,
  isPortal=false,
  definitionKey=null,
  w,
  placeholder,
}) => {
  const [{ value }, { touched, error }, { setValue, setTouched }] = useField(name);

  const onChange = useCallback((vals) => {
    if (vals.length === 0 && emptyIsUndefined) {
      setValue(undefined);
    } else if (vals.includes('*')) {
      if (!hasVirtualAll) setValue(['*']);
      else setValue(options.map((o) => o.value));
    } else {
      setValue(vals);
    }
    setTouched(true);
  }, [emptyIsUndefined, hasVirtualAll, options]);

  const onFieldBlur = useCallback(() => {
    setTouched(true);
  }, []);

  return (
    <Field
      canSelectAll={canSelectAll}
      label={label}
      value={value}
      onChange={onChange}
      onBlur={onFieldBlur}
      error={error}
      touched={touched}
      options={options}
      isDisabled={isDisabled}
      isRequired={isRequired}
      isHidden={isHidden}
      isPortal={isPortal}
      definitionKey={definitionKey}
      w={w}
      placeholder={placeholder}
    />
  );
};

MultiSelectField.propTypes = propTypes;
// MultiSelectField.defaultProps = defaultProps;

export default React.memo(MultiSelectField);
