import React, { useMemo } from 'react';
import { Tooltip } from 'antd';
import { useAuth } from '@/modules-smb/contexts/AuthProvider';
// import iconDetails from '@/modules-smb/Wireless/assets/Dashboard/icon_details.png';
import { QuestionCircleFilled } from '@ant-design/icons';
import { LabelTip } from '@/modules-smb/Wireless/components/LabelTip';

const findDefinition = (
  definitionKey?: string,
  CONFIGURATION_DESCRIPTIONS?: {
    [key: string]: { properties?: { [key: string]: { description: string } } };
  },
) => {
  try {
    if (!definitionKey || !CONFIGURATION_DESCRIPTIONS) return null;
    const split = definitionKey.split('.');
    const { length } = split;
    if (length < 2) return null;
    const start = split.slice(0, length - 1);
    const end = split[length - 1];
    return (
      CONFIGURATION_DESCRIPTIONS[start.slice(0, length - 1).join('.')]?.properties?.[end ?? '']?.description ?? null
    );
  } catch (e) {
    return null;
  }
};

interface ConfigurationFieldExplanationProps {
  definitionKey?: string;
}
const ConfigurationFieldExplanation: React.FC<ConfigurationFieldExplanationProps> = ({ definitionKey }) => {
  const { configurationDescriptions } = useAuth();
  const definition = useMemo(
    () =>
      findDefinition(
        definitionKey,
        configurationDescriptions as {
          [key: string]: { properties: { [key: string]: { description: string } } };
        },
      ),
    [configurationDescriptions],
  );
  if (!definition) return null;
  const {title,icon} = LabelTip(definitionKey);

  return (
    <Tooltip title={title}>
      {icon}
    </Tooltip>
  );
};

export default React.memo(ConfigurationFieldExplanation);
