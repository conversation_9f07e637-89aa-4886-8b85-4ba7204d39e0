import React, { useCallback } from 'react';
import Field from './FastToggleInput';
import useFastField from '@/modules-smb/hooks/useFastField';
import { FieldProps } from '@/modules-smb/models/Form';

interface Props extends FieldProps {
  falseIsUndefined?: boolean;
  onChangeCallback?: (e: boolean) => void;
  defaultValue?: boolean;
}

const ToggleField = ({
  name,
  isDisabled = false,
  label,
  isRequired = false,
  defaultValue,
  element,
  falseIsUndefined,
  definitionKey,
  onChangeCallback,
}: Props) => {
  const { value, error, isError, onChange, onBlur } = useFastField<boolean | undefined>({ name });

  const onValueChange = useCallback(
    (checked: boolean) => {
      if (falseIsUndefined && !checked) onChange(undefined);
      else onChange(checked);
      if (onChangeCallback) onChangeCallback(checked);
    },
    [falseIsUndefined, onChangeCallback, onChange],
  );

  return (
    <Field
      label={label ?? name}
      value={value === undefined && defaultValue !== undefined ? defaultValue : value !== undefined && value}
      onChange={onValueChange}
      error={error}
      onBlur={onBlur}
      isError={isError}
      isDisabled={isDisabled}
      isRequired={isRequired}
      element={element}
      definitionKey={definitionKey}
    />
  );
};

export default React.memo(ToggleField);
