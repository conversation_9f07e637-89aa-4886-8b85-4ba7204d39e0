import React, { useState } from 'react';
import { Form, Input, Tooltip, Button } from 'antd';
import { InfoCircleOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import ConfigurationFieldExplanation from '../ConfigurationFieldExplanation';
import { useTranslation } from 'react-i18next';
import { FieldInputProps } from '@/modules-smb/models/Form';
import iconDetails from '@/modules-smb/Wireless/assets/Dashboard/icon_details.png';
import '@/modules-smb/Wireless/assets/wireless.scss';

interface StringInputProps extends FieldInputProps<string | undefined | string[]> {
  isError: boolean;
  hideButton: boolean;
  isArea: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  explanation?: string;
  placeholder?: string;
  autoComplete?: string;
  isDisabled?: boolean;
  h?: number | string;
  w?: number|string;
}

const StringInput: React.FC<StringInputProps> = ({
  label,
  value,
  onChange,
  onBlur,
  isError,
  error,
  hideButton,
  isRequired,
  element,
  isArea,
  isDisabled,
  definitionKey,
  explanation,
  placeholder,
  autoComplete,
  h,
  w,
}) => {
  const { t } = useTranslation();
  const [show, setShow] = useState(false);

  const labelContent = (
    <div style={{ display: 'flex', alignItems: 'center', gap: 2,width:'180px' }}>
             <span>{label}</span>
             {explanation && (
                <Tooltip title={explanation}>
                  <img src={iconDetails} style={{marginLeft: 3}}/>
                </Tooltip>
              )}
             {definitionKey && <ConfigurationFieldExplanation definitionKey={definitionKey} />}
             {isRequired && <span 
                  style={{ color: "#ff4d4f",
                  fontFamily: "SimSun, sans-serif",
                  marginLeft: "2px",
                  marginRight: "4px",
                  display: "inline-block"}}
                  >*</span>}
            </div>
    // <span style={{ fontWeight: 500 }}>
    //   {label}
    //   {explanation && (
    //     <Tooltip title={explanation}>
    //       <img src={iconDetails} style={{marginLeft: 3}}/>
    //     </Tooltip>
    //   )}
    //   {definitionKey && <ConfigurationFieldExplanation definitionKey={definitionKey} />}
    // </span>
  );

  if (element) {
    return (
      <Form.Item
        label={labelContent}
        // required={isRequired}
        validateStatus={isError ? 'error' : ''}
        help={isError ? error : ''}
        className="input-item-error"
      >
        {element}
      </Form.Item>
    );
  }

  const commonProps = {
    value,
    onChange,
    onBlur,
    disabled: isDisabled,
    placeholder,
    autoComplete: autoComplete ?? 'off',
  };

  return (
    <Form.Item
      label={labelContent}
      // required={isRequired}
      validateStatus={isError ? 'error' : ''}
      help={isError ? error : ''}
      className="input-item-error"
    >
      {isArea ? (
        <Input.TextArea {...commonProps} rows={4} style={{ height: h}} />
      ) : hideButton ? (
        <Input.Password
          {...commonProps}
          iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
        />
      ) : (
        <Input {...commonProps} style={{ width: typeof w === 'number' ? `${w}px` : w || '100%' }}/>
      )}
    </Form.Item>
  );
};

export default React.memo(StringInput);
