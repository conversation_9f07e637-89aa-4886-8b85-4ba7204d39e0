import React,{useState} from 'react';
import { Form, InputNumber, Input } from 'antd';
import ConfigurationFieldExplanation from '../ConfigurationFieldExplanation';
import { FieldInputProps } from '@/modules-smb/models/Form';
import '@/modules-smb/Wireless/assets/wireless.scss';
interface Props extends FieldInputProps<string | undefined | string[]> {
  onChange: (v: string) => void;
  isError: boolean;
  hideArrows: boolean;
  unit?: string;
  w?: string | number;
}

const NumberInput: React.FC<Props> = ({
   label,
    value,
    unit,
    onChange,
    onBlur,
    error,
    isError,
    isRequired,
    hideArrows,
    element,
    isDisabled,
    w,
    min,
    max,
    definitionKey
}) => {
  // 统一转换 value 类型为 number 或 undefined
  const numericValue = typeof value === 'string' ? parseFloat(value) : value;
const [isComposing, setIsComposing] = useState(false);
  if (element)
    return (
      <Form.Item
        label={
          <div style={{ display: 'flex', alignItems: 'center', gap: 2,width:'180px' }}>
      <span>{label}</span>
      <ConfigurationFieldExplanation definitionKey={definitionKey} />
      {isRequired && <span style={{ color: "#ff4d4f",
                  fontFamily: "SimSun, sans-serif",
                  marginLeft: "2px",
                  marginRight: "4px",
                  display: "inline-block"}}>*</span>}
    </div>
        }
        required={isRequired}
        validateStatus={isError ? 'error' : ''}
        help={isError ? error : ''}
        className="input-item-error"
      >
        {element}
      </Form.Item>
    );

  const inputNumber = (
    <InputNumber
      value={numericValue}
      // onChange={(v) => onChange(v?.toString() ?? '')}
      onChange={(v) => {
    if (label==" "&&(v === null || v === undefined|| v === '')) {
      onChange('1'); // 清空时回退成 '1'
    } else {
      onChange(v?.toString() ?? '');
    }
  }}
      onBlur={onBlur}
      disabled={isDisabled}
      style={{ width: typeof w === 'number' ? `${w}px` : w || '100%' }}
      min={min}
      max={max}
      controls={!hideArrows}
      onKeyPress={(e) => {
        const val = String(numericValue ?? '');
        // 只允许数字和一个负号在开头
        if (!/[0-9\-]/.test(e.key)) {
          e.preventDefault();
        }
         // 限制负号只能在开头，且只能有一个
        if (e.key === '-') {
          // 如果已经有负号，或者光标位置不是在最开头，就禁止输入
          const selectionStart = (e.target as HTMLInputElement).selectionStart ?? 0;
          if (val.includes('-') || selectionStart !== 0) {
            e.preventDefault();
          }
        }
      }}
       // 输入法组合
        onCompositionStart={() => setIsComposing(true)}
        onCompositionEnd={(e) => {
          setIsComposing(false);
          const val = e.currentTarget.value;
          if (!/^-?\d*\.?\d*$/.test(val)) {
            e.currentTarget.value = '';
            onChange('0');
          }
        }}
        // 实时过滤非法字符
        onInput={(e) => {
          if (isComposing) return;
          const target = e.target as HTMLInputElement;
          if (!/^-?\d*\.?\d*$/.test(target.value)) {
            target.value = target.value.replace(/[^0-9.-]/g, '');
          }
        }}
    />
  );
  return (
    <Form.Item
      label={
        <div style={{ display: 'flex', alignItems: 'center', gap: 2,width:'180px' }}>
          <span>{label}</span>
          <ConfigurationFieldExplanation definitionKey={definitionKey} />
          {isRequired && <span 
                  style={{ color: "#ff4d4f",
                  fontFamily: "SimSun, sans-serif",
                  marginLeft: "2px",
                  marginRight: "4px",
                  display: "inline-block"}}
                  >*</span>}
        </div>
      }
      // required={isRequired}
      validateStatus={isError ? 'error' : ''}
      help={isError ? error : ''}
      className="input-item-error"
    >
      {unit ? (
        <InputNumber
          addonAfter={unit}
          value={numericValue}
          onChange={(v) => onChange(v?.toString() ?? '')}
          onBlur={onBlur}
          disabled={isDisabled}
          style={{ width: typeof w === 'number' ? `${w}px` : w || '100%' }}
          min={min}
          max={max}
          controls={!hideArrows}
          // onKeyPress={(e) => {
          //     // 只允许数字和一个小数点
          //     if (!/[0-9.\-]/.test(e.key)) {
          //       e.preventDefault();
          //     }
          //     // 限制只能有一个小数点
          //     if (e.key === '.' && String(numericValue ?? '').includes('.')) {
          //       e.preventDefault();
          //     }
          //     // 限制负号只能在开头
          //     if (e.key === '-' && String(numericValue ?? '').length > 0) {
          //       e.preventDefault();
          //     }
          //   }}
          //   // 阻止粘贴非数字
          // onPaste={(e) => {
          // const pasteData = e.clipboardData.getData('text');
          // if (!/^\d+$/.test(pasteData)) {
          //   e.preventDefault();
          // }
          // }}
        />
      ) : (
        inputNumber
      )}
    </Form.Item>
  );
};

export default React.memo(NumberInput);
