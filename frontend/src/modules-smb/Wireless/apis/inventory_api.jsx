
import { request } from "@/utils/common/request";
const baseUrl = "/ampcon/wireless/inventory/batch_delete";
// 批量删除设备
export function batchDeleteInventory(idList) {
  return request({
    url: `${baseUrl}`,
    method: "DELETE",
    data: { idList } 
  });
}

// 批量切换设备站点
export function batchSwitchSite({ idList, siteId }) {
  return request({
    url: "/ampcon/wireless/inventory/batch_switch_site",
    method: "PUT",
    data: {
      idList,
      siteId
    }
  });
}