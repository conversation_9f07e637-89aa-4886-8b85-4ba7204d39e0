import { request } from "@/utils/common/request";

const baseUrl = "/ampcon/wireless/monitor";
export function getWirelessClientList({
  status,
  searchValue,
  sortBy,
  sortType,
  pageNum,
  pageSize,
}) {
  return request({
    url: `${baseUrl}/client`,
    method: "GET",
    params: {
      status,
      searchValue,
      sortBy,
      sortType,
      pageNum,
      pageSize
    }
  }).then(response => {
    return response;
  });;
}



export function createEthernetPort({
  type,
  data,
}) {
  return request({
    url: `${baseUrl}/client`,
    method: "POST",
    data: {
      type,
      data,
    }
  });
}

export function getAnalyticsBoardTimepoints({
  id,
  startTime,
  endTime
}) {
  return request({
    url: `${baseUrl}/timepoints`,
    method: "GET",
    params: {
      board_id: id,
      fromDate: Math.floor(startTime.getTime() / 1000),
      endDate: endTime ? Math.floor(endTime.getTime() / 1000) : undefined,
    }
  }).then(response => {
    return response;
  });
}
