import { request } from "@/utils/common/request";


const baseUrl = "/ampcon/wireless/configure";


export function createWirelessConfigure({
  site_id,
  name,
  security,
  radio,
  network_name,
  ssid_configure,
  labels_name,
  network_type,
  vlan_or_dhcp_name,
}) {
  return request({
    url: `${baseUrl}/ssid`,
    method: "POST",
    data: {
      site_id,
      name,
      security,
      radio,
      network_name,
      ssid_configure,
      labels_name,
      network_type,
      vlan_or_dhcp_name,
    }
  });
}

export function updateWirelessConfigure({
  id,
  name,
  security,
  radio,
  network_name,
  ssid_configure,
  labels_name,
  network_type,
  vlan_or_dhcp_name,
}) {
  return request({
    url: `${baseUrl}/ssid`,
    method: "PUT",
    data: {
      id,
      name,
      security,
      radio,
      network_name,
      ssid_configure,
      labels_name,
      network_type,
      vlan_or_dhcp_name,
    }
  });
}


export function updateConfigureGroup({
  id,
  labels_name,
}) {
  return request({
    url: `${baseUrl}/ssid`,
    method: "PUT",
    data: {
      id,
      labels_name,
    }
  });
}

export function enableConfigure({
  id,
  is_enable,
}) {
  return request({
    url: `${baseUrl}/ssid`,
    method: "PUT",
    data: {
      id,
      is_enable,
    }
  });
}

export function deleteWirelessConfigure({
  id
}) {
  return request({
    url: `${baseUrl}/ssid`,
    method: "DELETE",
    data: {
      id
    }
  });
}

export function getWirelessConfigureList(
  site_id,
  page,
  pageSize,
  filterFields = [],
  sortFields = [],
  searchFields = {}
) {
  return request({
    url: `${baseUrl}/ssid/list`,
    method: "POST",
    data: {
      site_id,
      filterFields,
      sortFields,
      searchFields,
      page,
      pageSize
    }
  });
}

export function getWirelessConfigureDetail(id) {
  return request({
    url: `${baseUrl}/ssid`,
    method: "GET",
    params: {
      id
    }
  });
}

export function getWirelessChannels(countryCode) {
  return request({
    url: `${baseUrl}/channel`,
    method: "GET",
    params: {
      countryCode
    }
  });
}

export function updateConfigureGeneral(
  id,
  site_id,
  config
) {
  return request({
    url: `${baseUrl}/general`,
    method: "PUT",
    data: {
      id,
      site_id,
      config
    }
  });
}