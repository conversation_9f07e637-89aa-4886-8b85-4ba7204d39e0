import {request} from "@/utils/common/request";


const baseUrl = "/ampcon/wireless";
/**
 * type类型枚举：
 * 0 - 其它
 * 1 - SSID Radius
 * 2 - MPSK
 * 3 - Portal
 * 4 - Time Range
 */

export function createWirelessProfile({
  site_id,
  name,
  description,
  parameter,
  config_variables,
  type,
}) {
  return request({
    url: `${baseUrl}/profile`,
    method: "POST",
    data: {
        site_id,
        type,
        name,
        parameter,
        description,
        config_variables,
    }
  });
}

export function updateWirelessProfile({
  id,
  site_id,
  name,
  description,
  parameter,
  config_variables,
  type,
}) {
  return request({
    url: `${baseUrl}/profile`,
    method: "PUT",
    data: {
        id,
        site_id,
        type,
        name,
        parameter,
        description,
        config_variables,
    }
  });
}

export function deleteWirelessProfile({
  id
}) {
  return request({
    url: `${baseUrl}/profile`,
    method: "DELETE",
    data: {
        id
    }
  });
}

export function getWirelessProfileList(
    type,
    site_id,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    parameterFilter = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/profile/list`,
        method: "POST",
        data: {
            type,
            site_id,
            filterFields,
            parameterFilter,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function getWirelessProfileDetail(
    profile_id,
) {
    return request({
        url: `${baseUrl}/profile/${profile_id}`,
        method: "GET",
    });
}
