import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Checkbox, TimePicker, Row, Col, message, Divider, Modal, Slider } from 'antd';
import dayjs from 'dayjs';
import { createWirelessProfile, updateWirelessProfile } from '@/modules-smb/Wireless/apis/wireless_profile_api';
import { useTranslation } from 'react-i18next';

interface CreateTimeRangeProps {
  visible: boolean;
  resource?: {
    id: number;
    name?: string;
    description?: string;
    parameter?: object | string;
    config_variables?: string;
  };
  siteId: number;
  onClose: (success?: boolean) => void;
  onSuccess?: () => void;
}

interface TimeRangeItem {
  day: string;
  checked: boolean;
  startTime: dayjs.Dayjs | null;
  endTime: dayjs.Dayjs | null;
}

const dayList = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
const dayMap = { Monday: 1, Tuesday: 2, Wednesday: 3, Thursday: 4, Friday: 5, Saturday: 6, Sunday: 7 };

// 合并相同时间段，days为数组
function mergeByTime(arr: { day: number; start: string; end: string }[]) {
  const map = new Map<string, { days: number[]; start: string; end: string }>();

  arr.forEach(({ day, start, end }) => {
    const key = `${start}-${end}`;
    if (!map.has(key)) {
      map.set(key, { days: [], start, end });
    }
    map.get(key)!.days.push(day);
  });

  return Array.from(map.values()).map(item => ({
    days: item.days.sort((a, b) => a - b),
    start: item.start,
    end: item.end,
  }));
}

const TimeRangeModal: React.FC<CreateTimeRangeProps> = ({ visible, resource, siteId, onClose, onSuccess }) => {
  const [form] = Form.useForm();

  const [timeRanges, setTimeRanges] = useState<TimeRangeItem[]>(
    dayList.map(day => ({
      day,
      checked: false,
      startTime: null,
      endTime: null,
    }))
  );

  useEffect(() => {
    if (resource && visible) {
      form.setFieldsValue({
        name: resource.name || '',
        description: resource.description || '',
      });

      let paramObj = {};
      if (resource.parameter) {
        try {
          paramObj = typeof resource.parameter === 'string' ? JSON.parse(resource.parameter) : resource.parameter;
        } catch (e) {
          // console.warn('parameter parse error:', e);
        }
      }

      if (Array.isArray((paramObj as any).time_range)) {
        const newRanges = dayList.map(day => {
          const matched = (paramObj as any).time_range.find((entry: string) => entry.startsWith(day));
          if (matched) {
            const m = matched.match(/^(\w+)\s+(\d{2}:\d{2})-(\d{2}:\d{2})$/);
            if (m) {
              const [, , start, end] = m;
              return {
                day,
                checked: true,
                startTime: dayjs(start, 'HH:mm'),
                endTime: dayjs(end, 'HH:mm'),
              };
            }
          }
          return {
            day,
            checked: false,
            startTime: null,
            endTime: null,
          };
        });
        setTimeRanges(newRanges);
        return;
      }
    } else if (!visible) {
      form.resetFields();
      setTimeRanges(
        dayList.map(day => ({
          day,
          checked: false,
          startTime: null,
          endTime: null,
        }))
      );
    }
  }, [resource, form, visible]);

  const onCheckChange = (index: number, checked: boolean) => {
    const newRanges = [...timeRanges];
    newRanges[index].checked = checked;
    if (checked) {
      newRanges[index].startTime = newRanges[index].startTime || dayjs('08:00', 'HH:mm');
      newRanges[index].endTime = newRanges[index].endTime || dayjs('18:00', 'HH:mm');
    } else {
      newRanges[index].startTime = null;
      newRanges[index].endTime = null;
    }
    setTimeRanges(newRanges);
  };

  const onStartTimeChange = (index: number, time: dayjs.Dayjs | null) => {
    const newRanges = [...timeRanges];
    newRanges[index].startTime = time;
    if (time && newRanges[index].endTime && time.isAfter(newRanges[index].endTime)) {
      newRanges[index].endTime = time;
    }
    setTimeRanges(newRanges);
  };

  const onEndTimeChange = (index: number, time: dayjs.Dayjs | null) => {
    const newRanges = [...timeRanges];
    newRanges[index].endTime = time;
    if (time && newRanges[index].startTime && time.isBefore(newRanges[index].startTime)) {
      newRanges[index].startTime = time;
    }
    setTimeRanges(newRanges);
  };

  const handleApply = async () => {
    // 是否勾选星期
    const hasChecked = timeRanges.some(item => item.checked);
    if (!hasChecked) {
      message.error('Please select at least one day.');
      return;
    }
    // 选择时间是否有效
    const emptyTime = timeRanges.some(
      item => item.checked && (!item.startTime || !item.endTime)
    );
    if (emptyTime) {
      message.error('Please select a valid time');
      return;
    }

    try {
      const values = await form.validateFields();
      // get parameter
      const timeStrings = timeRanges
        .filter(item => item.checked)
        .map(
          item =>
            `${item.day} ${item.startTime!.format('HH:mm')}-${item.endTime!.format('HH:mm')}`
        );
      const parsed = timeStrings.map(str => {
        const [dayStr, timeRange] = str.split(' ');
        const [start, end] = timeRange.split('-');
        return {
          day: dayMap[dayStr as keyof typeof dayMap],
          start,
          end,
        };
      });
      // get config_variables
      const merged = mergeByTime(parsed);

      const commonParams = {
        site_id: siteId,
        type: 4,
        name: values.name,
        description: values.description || '',
        parameter: { time_range: timeStrings },
        config_variables: JSON.stringify(merged),
      };

      let res;
      if (resource && resource.id) {
        res = await updateWirelessProfile({ ...commonParams, id: resource.id });
      } else {
        res = await createWirelessProfile(commonParams);
      }
      const data = res.data || res; // 兼容返回数据结构

      if (res.status === 200) {
        message.success(resource ? 'Updated successfully' : 'Created successfully');
        // console.log("CreateTimeRangeModal: create success, calling onSuccess");
        onSuccess?.();
        onClose(true);
        form.resetFields();
        setTimeRanges(
          dayList.map(day => ({
            day,
            checked: false,
            startTime: null,
            endTime: null,
          }))
        );
      } else {
        message.error(resource
          ? `Update failed: ${data.info || 'Unknown error'}`
          : `Create failed: ${data.info || 'Unknown error'}`);
      }
    } catch (error) {
      // console.log(error)
      // message.error(resource
      //   ? `Update failed: Unknown error`
      //   : `Create failed: Unknown error`);
    }
  };

  // 取消
  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      title={
        <div>
          {resource ? 'Edit Time Range Profile' : 'Create Time Range Profile'}
          <Divider style={{ marginTop: 8, marginBottom: 0 }} />
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      className="ampcon-max-modal"
      footer={[
        <Divider style={{ margin: '0px 0px 16px -24px', width: 'calc(100% + 48px)' }} />,
        <Button key="cancel" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button key="apply" type="primary" onClick={handleApply}>
          Apply
        </Button>,
      ]}
      zIndex={1060}
      destroyOnClose
    >
      <Form
        form={form}
        layout="horizontal"
        labelAlign="left"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 10 }}
      >
        <TimeRangeFormFields
          form={form}
          timeRanges={timeRanges}
          onCheckChange={onCheckChange}
          onStartTimeChange={onStartTimeChange}
          onEndTimeChange={onEndTimeChange}
        />
      </Form>
    </Modal>
  );
};
export default TimeRangeModal;

export const TimeRangeFormFields: React.FC<{
  form: any;
  timeRanges: TimeRangeItem[];
  onCheckChange: (index: number, checked: boolean) => void;
  onStartTimeChange: (index: number, time: dayjs.Dayjs | null) => void;
  onEndTimeChange: (index: number, time: dayjs.Dayjs | null) => void;
}> = ({ form, timeRanges, onCheckChange, onStartTimeChange, onEndTimeChange }) => {
  const MIN_STEP = 1;
  const MAX_MINUTES = 23 * 60 + 59;
  const { t } = useTranslation();

  const timeToValue = (time: dayjs.Dayjs | null) =>
    time ? time.hour() * 60 + time.minute() : 0;

  const valueToTime = (value: number) =>
    dayjs().hour(Math.floor(value / 60)).minute(value % 60);

  const marks = Array.from({ length: 13 }).reduce((acc, _, i) => {
    const hour = i * 2;
    acc[hour * 60] = {
      label: (
        <div style={{ display: 'flex', flexDirection: 'column', marginBottom: 0, alignItems: 'center' }}>
          {/* 竖线 */}
          <div
            style={{
              width: 1,
              height: 8,
              backgroundColor: '#DADCE1',
              marginBottom: 0,
              marginTop: -8,
            }}
          />
          {/* 文字 */}
          <span style={{ fontSize: 12, color: '#474747' }}>{hour}</span>
        </div>
      ),
    };
    return acc;
  }, {} as Record<number, { label: React.ReactNode }>);

  return (
    <>
      <Form.Item
        label="Name"
        name="name"
        rules={[
          { required: true, message: 'Required!' },
          {
            max: 32,
            message: 'Name cannot exceed 32 characters',
          },
        ]}
        labelCol={{ span: 2, style: { marginRight: 24 } }}
        wrapperCol={{ span: 5 }}
      >
        <Input placeholder="Enter name" />
      </Form.Item>

      <Form.Item
        label="Description"
        name="description"
        rules={[{ max: 128, message: t('form.max_length', { max: 128 }) }]}
        labelCol={{ span: 2, style: { marginRight: 24 } }}
        wrapperCol={{ span: 5 }}
      >
        <Input.TextArea rows={2} placeholder="Enter description" />
      </Form.Item>
      <div style={{ marginBottom: 69 }}>
        {timeRanges.map((item, index) => (
          <Row key={item.day} align="middle" >
            <Col span={2} style={{ marginRight: 24 }}>
              <Checkbox
                checked={item.checked}
                onChange={(e) => onCheckChange(index, e.target.checked)}
              >
                {item.day}
              </Checkbox>
            </Col>
            <Col span={2}>
              <TimePicker
                value={item.startTime}
                onChange={(val) => {
                  const end = item.endTime;
                  if (val && end && !end.isAfter(val)) {
                    onEndTimeChange(index, val.add(MIN_STEP, 'minute'));
                  }
                  onStartTimeChange(index, val);
                }}
                disabled={!item.checked}
                format="HH:mm"
                style={{ width: '100%' }}
                allowClear={false}
                inputReadOnly
                placeholder=""
              />
            </Col>
            {/* <Col span={1} style={{ textAlign: 'center' }}>—</Col> */}
            <Col span={1} style={{ textAlign: 'center' }}>
              <div style={{
                width: '18px',
                borderBottom: '1px solid #B2B2B2 ',
                margin: '0 auto'
              }} />
            </Col>
            <Col span={2}>
              <TimePicker
                value={item.endTime}
                onChange={(val) => {
                  const start = item.startTime;
                  if (val && timeToValue(val) > MAX_MINUTES) {
                    val = dayjs().hour(23).minute(59);
                  }
                  if (val && start && !val.isAfter(start)) {
                    val = start.add(MIN_STEP, 'minute');
                  }
                  onEndTimeChange(index, val);
                }}
                disabled={!item.checked}
                format="HH:mm"
                style={{ width: '100%' }}
                allowClear={false}
                inputReadOnly
                placeholder=""
              />
            </Col>
            <Col span={7} style={{ paddingLeft: 16 }}>
              <div style={{ width: '100%', padding: '0 4px' }}>
                <Slider
                  range
                  min={0}
                  max={MAX_MINUTES}
                  step={1}
                  disabled={!item.checked}
                  value={
                    item.checked
                      ? [timeToValue(item.startTime), timeToValue(item.endTime)]
                      : [0, 0]
                  }
                  onChange={([start, end]) => {
                    if (!item.checked) return;
                    if (end > MAX_MINUTES) end = MAX_MINUTES;

                    let newStart = valueToTime(start);
                    let newEnd = valueToTime(end);

                    if (!newEnd.isAfter(newStart)) {
                      newEnd = newStart.add(MIN_STEP, 'minute');
                    }

                    onStartTimeChange(index, newStart);
                    onEndTimeChange(index, newEnd);
                  }}
                  marks={marks}
                  dotStyle={{ display: 'none' }}
                  activeDotStyle={{ display: 'none' }}
                  trackStyle={
                    item.checked
                      ? [{ backgroundColor: '#52c41a', height: 8 }]
                      : [{ backgroundColor: 'transparent', height: 8 }]
                  }
                  railStyle={{ backgroundColor: '#eee', height: 8 }}
                  handleStyle={[{ display: 'none' }, { display: 'none' }]}
                />
              </div>
            </Col>
          </Row>
        ))}
      </div>
    </>
  );
};