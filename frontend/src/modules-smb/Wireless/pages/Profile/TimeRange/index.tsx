import React, { useState, useEffect } from 'react';
import { Button, Space, message, Table, Divider, Modal } from 'antd';
import Icon from "@ant-design/icons";
import { addSvg } from "@/utils/common/iconSvg";
import TimeRangeModal from '@/modules-smb/Wireless/pages/Profile/TimeRange/TimeRangeModal';
import { createColumnConfigMultipleParams, TableFilterDropdown } from '@/modules-ampcon/components/custom_table';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import { getWirelessProfileList, deleteWirelessProfile } from '@/modules-smb/Wireless/apis/wireless_profile_api';
import type { ColumnsType } from 'antd/es/table';
import { utcToLocalString } from '../../../utils/util';

interface TimeRangeProps {
  siteId?: number;
}

interface TimeRangeItem {
  id: string;
  name: string;
  timeRange: string;
  modify: string;
  description: string;
  parameter: string;
}



const TimeRange: React.FC<TimeRangeProps> = ({ siteId }) => {
  const [data, setData] = useState<TimeRangeItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<TimeRangeItem | null>(null);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  const [searchValue, setSearchValue] = useState('');
  const [sorter, setSorter] = useState<{ field?: string; order?: string }>({});
  const type = 4;
  // url中获取siteId
  if (window.location.hash) {
    const hash = window.location.hash.replace('#', '');
    if (/^\d+$/.test(hash)) {
      siteId = parseInt(hash, 10);
    }
  }

  const parseTimeRangeStr = (parameter: string | { time_range?: string[] }): string => {
    try {
      const paramObj = typeof parameter === 'string' ? JSON.parse(parameter) : parameter;
      const timeRangeArr = paramObj?.time_range;
      return Array.isArray(timeRangeArr) ? timeRangeArr.join('\n') : 'No time range';
    } catch {
      return 'Invalid time range';
    }
  };

  const fetchList = async (page = 1, pageSize = 10, sorterParam = sorter) => {
    setIsLoading(true);
    try {
      const sortFields = sorterParam.field ? [{ field: sorterParam.field, order: sorterParam.order }] : [];
      const res = await getWirelessProfileList(type, siteId, page, pageSize, [], sortFields);
      if (res.status === 200 && Array.isArray(res.info)) {
        let processed = res.info.map((item: any) => ({
          ...item,
          key: item.id,
          timeRange: parseTimeRangeStr(item.parameter),
          modified_time: item.modified_time ? utcToLocalString(item.modified_time) : '',
        }));
        setData(processed);
        setPagination({
          current: page,
          pageSize,
          total: res.total || 0,
        });
      } else {
        message.error('Failed to fetch profile list');
      }
    } catch {
      message.error('Failed to fetch profile list');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchList();
  }, [siteId]);

  const handleDelete = (record: any) => {
    confirmModalAction(
      `Are you sure you want to delete?`,
      () => {
        deleteWirelessProfile({ id: record.key })
          .then(res => {
            if (res?.status !== 200) {
              message.error(res?.info);
              return;
            }
            message.success('Successfully Deleted');
            fetchList(pagination.current, pagination.pageSize);
          })
          .catch(() => message.error('Delete Failed'));
      }
    );
  };

  // 表格分页、排序变化处理
  const handleTableChange = (paginationChange: any, _: any, sorterChange: any) => {
    setPagination({
      ...pagination,
      current: paginationChange.current,
      pageSize: paginationChange.pageSize,
    });

    let order = '';
    let field = '';
    if (!Array.isArray(sorterChange) && sorterChange?.order) {
      order = sorterChange.order === 'ascend' ? 'asc' : 'desc';
      field = sorterChange.field || '';
    }
    setSorter({ field, order });
    fetchList(paginationChange.current, paginationChange.pageSize, { field, order });
  };

  const columns: ColumnsType<any> = [
    createColumnConfigMultipleParams({
      title: 'Name',
      dataIndex: 'name',
      enableSorter: true,
      enableFilter: false,
      filterDropdownComponent: TableFilterDropdown,
      defaultValue: '',
      width: '15%',
    }),
    createColumnConfigMultipleParams({
      title: 'Time Range',
      dataIndex: 'timeRange',
      enableSorter: false,
      enableFilter: false,
      render: (val: string) => (
        <div style={{ padding: '13px 0' }}>
          {val
            .split('\n')
            .filter(Boolean)
            .map((line) => {
              const [weekday, range] = line.split(' ');
              return (
                <div >
                  <span style={{ display: 'inline-block', width: '100px', textAlign: 'left' }}>
                    {weekday}
                  </span>
                  <span >{range}</span>
                </div>
              );
            })}
        </div>
      ),
      width: '15%',
    }),
    createColumnConfigMultipleParams({
      title: 'Modify',
      dataIndex: 'modified_time',
      enableSorter: true,
      enableFilter: false,
      width: '15%',
    }),
    createColumnConfigMultipleParams({
      title: 'Description',
      dataIndex: 'description',
      enableSorter: true,
      enableFilter: false,
      width: '30%',
      render: (text: any) => (
        <div style={{ padding: '13px 0', whiteSpace: 'normal', wordBreak: 'break-word' }}>
          {text}
        </div>
      ),
    }),
    {
      title: 'Operation',
      width: '15%',
      render: (_: any, record: TimeRangeItem) => (
        <Space size={24}>
          <Button
            style={{ padding: 0 }}
            type="link"
            onClick={() => {
              setEditingRecord(record);
              setIsFormVisible(true);
            }}
          >
            Edit
          </Button>
          <Button type="link" style={{ padding: 0 }} onClick={() => handleDelete(record)}>
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Button
          type="primary"
          onClick={() => {
            setEditingRecord(null);
            setIsFormVisible(true);
          }}
        >
          <Icon component={addSvg} />
          Create New Time Range Profile
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={data}
        loading={isLoading}
        onChange={handleTableChange}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
        scroll={{ x: 1000 }}
        rowKey="key"
        bordered
      />
      <TimeRangeModal
        visible={isFormVisible}
        resource={editingRecord}
        siteId={siteId}
        onClose={() => setIsFormVisible(false)}
        onSuccess={() => {
          setIsFormVisible(false);
          fetchList();
        }}
      />
    </div>
  );
};
export default TimeRange;
