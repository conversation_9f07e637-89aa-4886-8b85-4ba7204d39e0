import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Button, Table, Space, message, Modal, Divider } from 'antd';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import { addSvg } from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import { Resource } from '@/modules-smb/models/Resource';
import {
    createWirelessProfile,
    updateWirelessProfile
} from "@/modules-smb/Wireless/apis/wireless_profile_api";
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import { ExclamationCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import { PlusOutlined } from '@ant-design/icons';
import "./index.scss"


interface MPSKEntry {
    mac: string;
    key: string;
    vlan_id?: number;
}

interface FullPageMPSKFormProps {
    editingProfile?: Resource;
    onClose: (success?: boolean) => void;
    siteId: number;
}

interface ConfigVariable {
    type: string;
    weight: number;
    prefix: string;
    value: string;
}

const FullPageMPSKForm: React.FC<FullPageMPSKFormProps> = ({
    editingProfile,
    onClose,
    siteId
}) => {
    const { t } = useTranslation();
    const [initialValues, setInitialValues] = useState({
        name: '',
        description: ''
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [currentEntry, setCurrentEntry] = useState<MPSKEntry>({
        mac: '',
        key: ''
    });
    const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
    const [deletingIndex, setDeletingIndex] = useState<number | null>(null);
    const [sorter, setSorter] = useState<{ field: string; order: string }>({ field: '', order: '' });

    const [entries, setEntries] = useState<MPSKEntry[]>(() => {
        if (!editingProfile?.parameter) return [];

        try {
            let paramStr = editingProfile.parameter;
            if (typeof paramStr === 'string') {
                if (paramStr.startsWith('(') && paramStr.endsWith('}')) {
                    const cleaned = paramStr
                        .slice(1, -1)
                        .replace(/::/g, '":"')
                        .replace(/,/g, '","')
                        .replace(/:/g, '":"');
                    const jsonStr = `{"${cleaned}"}`;
                    const parsed = JSON.parse(jsonStr);

                    return Object.keys(parsed)
                        .filter(key => key.startsWith('entry_'))
                        .map(key => ({
                            mac: parsed[key].mac || '',
                            key: parsed[key].key || '',
                            vlan_id: parsed[key].vlan_id || undefined
                        }));
                }
                paramStr = paramStr.replace(/\\"/g, '"');
                if (paramStr.startsWith('"') && paramStr.endsWith('"')) {
                    paramStr = paramStr.slice(1, -1);
                }
                const parsed = JSON.parse(paramStr);
                return Object.keys(parsed)
                    .filter(key => key.startsWith('entry_'))
                    .map(key => ({
                        mac: parsed[key].mac || '',
                        key: parsed[key].key || '',
                        vlan_id: parsed[key].vlan_id || undefined
                    }));
            }
            return [];
        } catch (e) {
            return [];
        }
    });

    const handleAddEntry = () => {
        setCurrentEntry({ mac: '', key: '', vlan_id: 1813 });
        setModalVisible(true);
    };

    const handleModalCancel = () => {
        setModalVisible(false);
    };
    const checkKeyExists = (key: string): boolean => {
        return entries.some(entry => entry.key === key);
    };

    const handleModalOk = () => {
        if (!currentEntry.key) {
            message.error(t('Key is required'));
            return;
        }
        if (currentEntry.key.length < 8 || currentEntry.key.length > 63) {
            message.error(t('Value needs to be of a length between 8 (inclusive) and 63 (inclusive)'));
            return;
        }
        // if (!currentEntry.key) {
        //     return;
        // }
        // if (checkKeyExists(currentEntry.key)) {
        //     return;
        // }
        // const duplicate = entries.find(entry => entry.key === currentEntry.key);
        // if (duplicate) {
        //     if (!currentEntry.mac || currentEntry.mac === duplicate.mac) {
        //         message.error(currentEntry.mac ? t('The key already exists') : t('The user already exists'));
        //         return;
        //     }
        // }
        if (currentEntry.vlan_id === undefined || currentEntry.vlan_id === null || currentEntry.vlan_id <= 0) {
            message.error('VLAN-ID must be a positive number')
            return;
        }
        if (currentEntry.vlan_id >= 4096) {
            message.error(t('vlan-id must be less than 4097'));
            return;
        }

        let standardizedMac = '';
        if (currentEntry.mac) {
            standardizedMac = currentEntry.mac
                .toLowerCase()
                .replace(/[^a-f0-9]/g, '')
                .match(/.{1,2}/g)
                ?.join(':') || currentEntry.mac;
        }

        const newEntry = {
            ...currentEntry,
            mac: standardizedMac
        };

        if (!newEntry.mac) {
            const duplicateUser = entries.find(
                entry => !entry.mac && entry.key === newEntry.key
            );
            if (duplicateUser) {
                message.error(t('The user already exists'));
                return;
            }
        } else {
            const duplicate = entries.find(
                entry => entry.mac === newEntry.mac && entry.key === newEntry.key
            );
            if (duplicate) {
                message.error(t('The key already exists'));
                return;
            }
        }

        setEntries(prev => [...prev, newEntry]);

        setModalVisible(false);
    };
    const validateMAC = (value: string): Promise<void> => {
        if (!value) {
            return Promise.resolve();
        }
        const normalized = value.toLowerCase();
        const macRegex = /^([0-9a-f]{2}:){5}([0-9a-f]{2})$/;
        if (!macRegex.test(normalized)) {
            return Promise.reject(t('The legal Mac format can only contain lowercase letters and numbers, with a delimiter of ":". Please use the format: 00:00:5e:00:53:af'));
        }
        return Promise.resolve();
    };

    const handleDelete = (index: number) => {
        confirmModalAction(
            t('Are you sure you want to delete?'),
            async () => {
                try {
                    const newEntries = [...entries];
                    newEntries.splice(index, 1);
                    setEntries(newEntries);
                    message.success(t('Successfully Deleted'));
                } catch (error) {
                    message.error(t('Delete failed'));
                }
            }
        );
    };
    const handleConfirmDelete = () => {
        if (deletingIndex !== null) {
            try {
                const newEntries = [...entries];
                newEntries.splice(deletingIndex, 1);
                setEntries(newEntries);
                message.success(t('Successfully Deleted'));
            } catch (error) {
                message.error(t('Delete failed'));
            }
        }
        setDeleteConfirmVisible(false);
    };

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: entries.length
    });

    useEffect(() => {
        setPagination(prev => ({
            ...prev,
            total: entries.length
        }));
    }, [entries]);

    const handleTableChange = (paginationChange: any, _filters: any, sorterChange: any) => {
        setPagination(prev => ({
            ...prev,
            current: paginationChange.current,
            pageSize: paginationChange.pageSize,
        }));

        if (!Array.isArray(sorterChange) && sorterChange?.order) {
            const { field, order } = sorterChange;
            const sorted = [...entries].sort((a, b) => {
                const valA = a[field as keyof MPSKEntry];
                const valB = b[field as keyof MPSKEntry];

                if (valA === undefined || valB === undefined) return 0;

                if (typeof valA === "number" && typeof valB === "number") {
                    return order === "ascend" ? valA - valB : valB - valA;
                }

                return order === "ascend"
                    ? String(valA).localeCompare(String(valB))
                    : String(valB).localeCompare(String(valA));
            });

            setEntries(sorted);
        }
    };


    useEffect(() => {
        if (editingProfile) {
            setInitialValues({
                name: editingProfile.name || '',
                description: editingProfile.description || ''
            });

            try {
                const parsed = editingProfile.parameter || {};

                const newEntries = Object.keys(parsed)
                    .filter(key => key.startsWith('entry_'))
                    .map(key => ({
                        mac: parsed[key]?.mac || '',
                        key: parsed[key]?.key || '',
                        vlan_id: parsed[key]?.vlan_id || undefined
                    }));

                setEntries(newEntries);
            } catch (e) {
                setEntries([]);
            }
        }
    }, [editingProfile]);

    const columns = [
        {
            title: t('Mac'),
            dataIndex: 'mac',
            key: 'mac',
            sorter: true,
            render: (text: string) => <div>{text}</div>
        },
        {
            title: t('Key'),
            dataIndex: 'key',
            key: 'key',
            sorter: true,
            render: (text: string) => <div>{text}</div>
        },
        {
            title: t('VLAN-ID'),
            dataIndex: 'vlan_id',
            key: 'vlan_id',
            sorter: true,
            render: (text: string) => <div>{text}</div>
        },
        {
            title: 'Operation',
            key: 'actions',
            align: 'left',
            render: (_: any, record: MPSKEntry, index: number) => (
                <Space style={{ display: 'flex', justifyContent: 'flex-start', width: '100%', marginLeft: -12 }}>
                    <Button type="link" onClick={() => handleDelete(index)}>
                        Delete
                    </Button>
                </Space>

            ),
        },
    ];

    return (
        <>
            <h2 style={{
                margin: '0 0 24px 0',
                color: 'rgba(0, 0, 0, 0.85)',
                fontWeight: 500,
                fontSize: 20
            }}>
            </h2>

            <Formik
                initialValues={initialValues}
                enableReinitialize={true}
                validateOnChange={true}
                validateOnBlur={true}
                validate={(values) => {
                    const errors: any = {};
                    if (!values.name) {
                        errors.name = t('Please enter profile name');
                    } else if (values.name.length > 32) {
                        errors.name = t('Profile name cannot exceed 32 characters');
                    }
                    if (values.description && values.description.length > 128) {
                        errors.description = t('form.max_length', { max: 128 });
                    }
                    return errors;
                }}

                onSubmit={async (values) => {
                    if (values.name.length > 32) {
                        message.error(t('Profile name cannot exceed 32 characters'));
                        return;
                    }
                    setIsSubmitting(true);
                    try {
                        const parameterObj = entries.reduce((acc, entry, index) => {
                            acc[`entry_${index}`] = {
                                mac: entry.mac,
                                key: entry.key,
                                vlan_id: entry.vlan_id
                            };
                            return acc;
                        }, {} as Record<string, any>);

                        const mpskConfig = entries.map(entry => ({
                            ...(entry.mac ? { mac: entry.mac } : {}),
                            "vlan-id": entry.vlan_id,
                            "key": entry.key
                        }));

                        const payload = {
                            site_id: siteId,
                            type: 2,
                            name: values.name,
                            parameter: parameterObj,
                            description: values.description,
                            config_variables: JSON.stringify(mpskConfig)
                        };

                        let response;

                        if (editingProfile?.id) {
                            response = await updateWirelessProfile({
                                id: editingProfile.id,
                                ...payload
                            });
                        } else {
                            response = await createWirelessProfile(payload);
                        }

                        if (response?.status !== 200) {
                            message.error(response?.info || t('Operation failed'));
                            return;
                        }

                        message.success(editingProfile ? t('Profile updated successfully') : t('Profile created successfully'));
                        onClose(true);
                    } catch (error) {
                        message.error(t('Operation failed: ') + (error as Error).message);
                    } finally {
                        setIsSubmitting(false);
                    }
                }}
            >
                {({
                    values,
                    errors,
                    touched,
                    handleSubmit,
                    handleBlur,
                    handleChange
                }) => (
                    <Form
                        onFinish={handleSubmit}
                        style={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column'
                        }}
                    >
                        <div style={{
                            marginBottom: 24,
                            width: '100%',
                            paddingLeft: 0
                        }}>
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                marginBottom: 16,
                                width: '100%'
                            }}>
                                <span style={{
                                    fontWeight: 500,
                                    minWidth: '140px',
                                    marginRight: -30
                                }}>
                                    {t('Name')} <span style={{ color: 'red' }}>*</span>
                                </span>
                                <div style={{ width: '280px' }}>
                                    <Input
                                        name="name"
                                        value={values.name}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                        placeholder={t('Enter name')}
                                        style={{ width: '100%' }}
                                        status={touched.name && errors.name ? 'error' : ''}
                                    />
                                    {touched.name && errors.name && (
                                        <div style={{ color: '#ff4d4f', marginTop: 4 }}>{errors.name}</div>
                                    )}
                                </div>

                            </div>
                            <div style={{
                                display: 'flex',
                                alignItems: 'flex-start',
                                width: '100%',
                                marginBottom: 16
                            }}>
                                <span style={{
                                    fontWeight: 500,
                                    minWidth: '140px',
                                    marginRight: -30,
                                    paddingTop: 8
                                }}>
                                    {t('Description')}
                                </span>
                                <div style={{ width: '280px', marginBottom: -8 }}>
                                    <Input.TextArea
                                        name="description"
                                        value={values.description}
                                        onChange={handleChange}
                                        placeholder={t('Enter description')}
                                        rows={2}
                                    />
                                    {errors.description && (
                                        <div style={{ color: '#ff4d4f', marginTop: 4 }}>{errors.description}</div>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div>
                            <Button
                                type="primary"
                                onClick={handleAddEntry}
                                style={{ marginBottom: 24, marginTop: 2 }}
                            >
                                <Icon component={addSvg} />
                                {t('Create')}
                            </Button>
                        </div>

                        <div style={{ flex: 1, marginBottom: 24 }}>
                            <Table
                                columns={columns}
                                dataSource={entries}
                                rowKey={(_, index) => index.toString()}
                                pagination={{
                                    ...pagination,
                                    showSizeChanger: true,
                                    showQuickJumper: true,
                                    pageSizeOptions: ['10', '20', '50'],
                                    showTotal: (total) => `Total ${total} items`,
                                    position: ['bottomRight']
                                }}
                                onChange={handleTableChange}
                                scroll={{ x: 'max-content', y: 300 }}
                                bordered
                            />
                        </div>
                        <Divider style={{ margin: '20px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
                        <div style={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            gap: 16,
                            //paddingTop: 16,
                        }}>
                            <Button onClick={() => onClose(false)} disabled={isSubmitting}>
                                {t('Cancel')}
                            </Button>
                            <Button
                                type="primary"
                                htmlType="submit"
                                loading={isSubmitting}
                            >
                                {t('Apply')}
                            </Button>
                        </div>
                    </Form>
                )}
            </Formik>

            <Modal
                title={
                    <div style={{
                        fontFamily: 'Lato, sans-serif',
                        fontWeight: 700,
                        fontSize: '20px',
                        color: '#212519',
                        lineHeight: '24px',
                        textAlign: 'left'
                    }}>
                        {t('Create')}
                    </div>
                }
                visible={modalVisible}
                onCancel={handleModalCancel}
                footer={[
                    <Button key="back" onClick={handleModalCancel} >
                        {t('Cancel')}
                    </Button>,
                    <Button key="submit" type="primary" onClick={handleModalOk} >
                        {t('Apply')}
                    </Button>,
                ]}
                destroyOnClose
                width={680}
                bodyStyle={{
                    height: 340
                }}
                style={{
                    height: '450px',
                    borderRadius: '8px',
                }}
            >
                <Divider style={{ margin: '0px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
                <Form
                    layout="horizontal"
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 18 }}
                    initialValues={currentEntry}
                    onValuesChange={(_, values) => setCurrentEntry(values)}
                    validateTrigger={['onChange', 'onBlur']}
                    onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                        }
                    }}
                >
                    <Form.Item
                        name="mac"
                        label={<span>{t('Mac')}</span>}
                        rules={[
                            { validator: (_, value) => validateMAC(value) }
                        ]}
                        validateTrigger={['onChange', 'onBlur']}
                        labelAlign="left"
                        className="left-error"
                    >
                        <Input
                            autoComplete="off"
                            style={{
                                width: '280px',
                                marginLeft: -70
                            }}
                        />
                    </Form.Item>

                    <Form.Item
                        name="key"
                        label={
                            <span>
                                {t('Key')}
                            </span>
                        }
                        rules={[
                            {
                                required: true,
                                message: t('Required')
                            },
                            {
                                validator: (_, value) => {
                                    if (!value) {
                                        return Promise.resolve();
                                    }
                                    if (value.length < 8 || value.length > 63) {
                                        return Promise.reject(t('Value needs to be of a length between 8 (inclusive) and 63 (inclusive)'));
                                    }
                                    // if (checkKeyExists(value)) {
                                    //     return Promise.reject(t('The key already exists'));
                                    // }
                                    return Promise.resolve();
                                },
                                validateTrigger: ['onChange', 'onBlur']
                            }
                        ]}
                        labelAlign="left"
                        validateTrigger={['onChange', 'onBlur']}
                        className="left-error"
                    >
                        <Input
                            autoComplete="new-password"
                            style={{ width: '280px', marginLeft: -70 }}
                        />
                    </Form.Item>

                    <Form.Item
                        name="vlan_id"
                        label={<span>{t('VLAN-ID')}</span>}
                        rules={[
                            {
                                required: true,
                                message: t('VLAN-ID must be a positive number')
                            },
                            {
                                validator: (_, value) => {
                                    if (value <= 0) {
                                        return Promise.reject(t('vlan-id must be greater than 0'));
                                    }
                                    if (value >= 4096) {
                                        return Promise.reject(t('VLAN-ID must be less than 4097'));
                                    }
                                    return Promise.resolve();
                                },
                                validateTrigger: ['onChange', 'onBlur']
                            }
                        ]}
                        labelAlign="left"
                        validateTrigger={['onChange', 'onBlur']}
                        className="left-error"
                    >
                        <InputNumber
                            //min={0}
                            value={currentEntry.vlan_id}
                            style={{
                                width: '140px',
                                marginLeft: -70
                            }}
                            parser={(value) => {
                                const parsed = parseInt(value || '0', 10);
                                return isNaN(parsed) ? '' : parsed;
                            }}
                            formatter={(value) => {
                                return value ? value.toString() : '';
                            }}
                        />
                    </Form.Item>
                </Form>
                <Divider style={{ margin: '175px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
            </Modal>
        </>
    );
};

export default FullPageMPSKForm;