import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, Input, InputNumber, message } from 'antd';
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';
import { validateMac } from '@/modules-smb/Wireless/utils/util';

interface UsersFormProps {
  open: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  isDisabled?: boolean;
  existingUsers?: any[];
}

const initialUser = { mac: '', 'user-name': '', password: '', 'vlan-id': 1 };

const UsersForm: React.FC<UsersFormProps> = ({ open, onCancel, onOk, isDisabled, existingUsers = [] }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  useEffect(() => {
    if (open) {
      form.setFieldsValue(initialUser);
    }
  }, [open]);
  return (
    <FormModal
      open={open}
      title="Add User"
      onCancel={onCancel}
      onFinish={async values => {
        const { mac, 'user-name': userName, password } = values;
        // 检查是否存在重复用户
        const isDuplicate = existingUsers.some(user => 
          user.mac?.toLowerCase() === mac.toLowerCase() &&
          user['user-name'] === userName &&
          user.password === password
        );
        if (isDuplicate) {
          message.error('User with the same MAC, username and password already exists.');
          return;
        }
        onOk(values);
        form.resetFields();
      }}
      initialValues={initialUser}
      form={form}
      modalClass="ampcon-middle-modal"
    >
      <Form.Item
        label="MAC"
        name="mac"
        rules={[
          { validator: (_, value) => validateMac(value) }
        ]}
      >
        <Input disabled={isDisabled} />
      </Form.Item>
      <Form.Item
        label="User Name"
        name="user-name"
        rules={[
          { required: true, message: t('form.required') },
        ]}
      >
        <Input disabled={isDisabled} />
      </Form.Item>
      <Form.Item
        label="Password"
        name="password"
        rules={[
          { required: true, message: t('form.required') },
          { min: 8, max: 63, message: t('form.min_max_string', { min: 8, max: 63 }) },
        ]}
      >
        <Input.Password disabled={isDisabled} />
      </Form.Item>
      <Form.Item
        label="VLAN ID"
        name="vlan-id"
        rules={[
          { required: true, message: t('form.required') },
          { type: 'number', max: 4094, message: 'vlan-id must be less than 4095' },
          { type: 'number', min: 0, message: 'vlan-id must be greater than -1' },
        ]}
      >
        <InputNumber disabled={isDisabled} />
      </Form.Item>
    </FormModal>
  );
};

export default UsersForm;