import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Form, Input, Select, Button, message, Row, Col, Tooltip, ColorPicker, Upload } from 'antd';
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';
import debounce from 'lodash.debounce';
import clickTemplate from '@/modules-smb/assets/CaptiveTemplate/click.htm';
import credentialsTemplate from '@/modules-smb/assets/CaptiveTemplate/credentials.htm';
import radiusTemplate from '@/modules-smb/assets/CaptiveTemplate/radius.htm';
import { createWirelessProfile, updateWirelessProfile } from '@/modules-smb/Wireless/apis/wireless_profile_api';
import { convertHtmlToBase64 } from '@/modules-smb/utils/configHelpers';
import { useTranslation } from 'react-i18next';
import './WebrootForm.scss';

// 默认值常量
const defaultValues = {
  name: '',
  description: '',
  mode: 'Click',
  backgroundColor: '#F0F8F9',
  logoBase64: '',
  welcomeMessage: 'Welcome to use Wi-Fi',
  termsOfService: '',
  corporateInfo: '© 2025 FS.COM INC. All rights reserved',
};

// 字段最大长度常量
const defaultMaxLength = {
  welcomeMessage: 31,
  termsOfService: 200,
  corporateInfo: 50,
  logo: 10
};

export interface WebRootFormProps {
  isDisabled?: boolean;
  resource?: any;
  onClose: (success?: boolean) => void;
  refresh?: () => void;
  siteId?: number;
  parameterMode?: string;
  open?: boolean; 
}

const WebRootForm: React.FC<WebRootFormProps> = ({
  isDisabled = false,
  resource,
  onClose,
  refresh,
  siteId,
  parameterMode,
  open,
}) => {
  const { t } = useTranslation();
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [templateHtml, setTemplateHtml] = useState<string>('');
  const [mode, setMode] = useState<string>(defaultValues.mode);
  const [delayedWelcome, setDelayedWelcome] = useState<string>(defaultValues.welcomeMessage);
  const [delayedTerms, setDelayedTerms] = useState<string>(defaultValues.termsOfService);
  const [delayedCopyright, setDelayedCopyright] = useState<string>(defaultValues.corporateInfo);
  const [backgroundColor, setBackgroundColor] = useState<string>(defaultValues.backgroundColor);
  const [logoBase64, setLogoBase64] = useState<string>(defaultValues.logoBase64);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [submitting, setSubmitting] = useState(false);

  const [form] = Form.useForm();

  // 处理parameterMode变化时，内部mode和表单同步
  useEffect(() => {
    if (parameterMode && parameterMode !== mode) {
      setMode(parameterMode);
      form.setFieldsValue({ mode: parameterMode });
    }
  }, [parameterMode]);

  const getTemplatePath = (mode: string) => {
    switch (mode) {
      case 'Click': return clickTemplate;
      case 'Radius': return radiusTemplate;
      case 'Credentials': return credentialsTemplate;
      default: return clickTemplate;
    }
  };

  const mergeHtmlContent = (tpl: string, params: any) => {
    let updatedHtml = tpl;
    if (params.backgroundColor) {
      updatedHtml = updatedHtml.replace(
        /background-color:\s*(?:unset|#[a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)\s*;?/i,
        `background-color: ${params.backgroundColor};`
      );
    }
    if (params.logoBase64) {
      updatedHtml = updatedHtml.replace(/<img id="logo"[^>]*src="[^"]*"[^>]*>/, `<img id="logo" src="${params.logoBase64}" alt="logo" />`);
    }
    updatedHtml = updatedHtml.replace(/<p class="lead" id="title">.*?<\/p>/, `<p class="lead" id="title">${params.welcomeMessage}</p>`);
    updatedHtml = updatedHtml.replace(/<div[^>]*id="readmeTxt"[^>]*>([\s\S]*?)<\/div>/, `<div id="readmeTxt">${params.termsOfService}</div>`);
    updatedHtml = updatedHtml.replace(/<div[^>]*id="corporate-info"[^>]*>([\s\S]*?)<\/div>/, `<div id="corporate-info">${params.corporateInfo}</div>`);
    return updatedHtml;
  };

  // 延迟更新 welcome/terms/copyright
  const debounceWelcome = useCallback(debounce((val: string) => setDelayedWelcome(val), 400), []);
  const debounceTerms = useCallback(debounce((val: string) => setDelayedTerms(val), 400), []);
  const debounceCopyright = useCallback(debounce((val: string) => setDelayedCopyright(val), 400), []);

  // 监听mode变化，切换模板
  useEffect(() => {
    const templatePath = getTemplatePath(mode);
    fetch(templatePath)
      .then((response) => response.text())
      .then((html) => {
        setTemplateHtml(html);
        const values = form.getFieldsValue();
        setHtmlContent(mergeHtmlContent(html, {
          ...values,
          backgroundColor,
          logoBase64,
          welcomeMessage: delayedWelcome,
          termsOfService: delayedTerms,
          corporateInfo: delayedCopyright,
        }));
      })
      .catch((error) => console.error('Failed to load defaultHtml:', error));
  }, [mode]);

  // 字段变化时，更新预览html内容
  useEffect(() => {
    if (!templateHtml) return;
    const values = form.getFieldsValue();
    setHtmlContent(mergeHtmlContent(templateHtml, {
      ...values,
      backgroundColor,
      logoBase64,
      welcomeMessage: delayedWelcome,
      termsOfService: delayedTerms,
      corporateInfo: delayedCopyright,
    }));
  }, [delayedWelcome, delayedTerms, delayedCopyright, templateHtml, backgroundColor, logoBase64]);

  // 编辑回显
  useEffect(() => {
    if (resource) {
      const param = resource.parameter || {};
      setMode(param.mode);
      setBackgroundColor(param.background);
      setLogoBase64(param.logo);
      form.setFieldsValue({
        name: resource.name,
        description: resource.description,
        mode: param.mode,
        backgroundColor: param.background,
        logoBase64: param.logo,
        welcomeMessage: param.welcome,
        termsOfService: param.terms_of_service,
        corporateInfo: param.copyright,
      });
      setDelayedWelcome(param.welcome);
      setDelayedTerms(param.terms_of_service);
      setDelayedCopyright(param.copyright);
    }
  }, [resource]);

  // 监听 htmlContent，刷新 iframe 预览内容
  useEffect(() => {
    if (iframeRef.current && htmlContent) {
      iframeRef.current.srcdoc = htmlContent;
      setTimeout(() => {
        try {
          const doc = iframeRef.current?.contentDocument;
          const form = doc?.querySelector('form');
          if (form) {
            form.onsubmit = (e) => {
              e.preventDefault();
              return false;
            };
          }
        } catch {}
      }, 50);
    }
  }, [htmlContent]);

  // 图片上传校验
  const validateImage = (file: File, maxSize: number): boolean => {
    const validTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
    if (!validTypes.includes(file.type)) {
      message.error('Only PNG, JPG, JPEG, or GIF file types are supported.');
      return false;
    }
    if (file.size > maxSize) {
      message.error(`Choose a picture that is no more than ${maxSize / 1024}KB.`);
      return false;
    }
    return true;
  };

  // 提交表单
  const handleFinish = async (values: any) => {
    setSubmitting(true);
    try {
      // 先请求 convertHtmlToBase64
      let base64Html = '';
      try {
        base64Html = await convertHtmlToBase64(htmlContent, values.mode);
      } catch (e) {
        message.error('Failed to convert HTML');
        setSubmitting(false);
        return;
      }
      const parameter = {
        mode: values.mode,
        background: values.backgroundColor,
        logo: values.logoBase64 || '',
        welcome: values.welcomeMessage,
        terms_of_service: values.termsOfService,
        copyright: values.corporateInfo,
      };
      // const config_variables = [
      //   {
      //     prefix: 'captive-webroot',
      //     type: 'string',
      //     value: base64Html,
      //     weight: 0,
      //   },
      // ];
      let res;
      if (resource) {
        res = await updateWirelessProfile({
          id: resource.id,
          site_id: siteId,
          type: 3,
          name: values.name,
          description: values.description,
          parameter: parameter,
          // config_variables: JSON.stringify(config_variables),
          config_variables: base64Html,
        });
        if (res?.status !== 200) {
          message.error(res?.info || 'Failed to update resource');
          setSubmitting(false);
          return;
        }
        message.success('Resource updated successfully');
      } else {
        res = await createWirelessProfile({
          site_id: siteId,
          type: 3,
          name: values.name,
          description: values.description,
          parameter: parameter,
          // config_variables: JSON.stringify(config_variables),
          config_variables: base64Html,
        });
        if (res?.status !== 200) {
          message.error(res?.info || 'Failed to create resource');
          setSubmitting(false);
          return;
        }
        message.success('Resource created successfully');
      }
      refresh && refresh();
      onClose && onClose(true);
    } catch (e) {
      message.error('Failed to create resource');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <FormModal
      open={open}
      title={resource ? 'Edit Portal Webroot Profile' : 'Create Portal Webroot Profile'}
      onCancel={() => onClose(false)}
      onFinish={handleFinish}
      initialValues={defaultValues}
      form={form}
    >
      <Row gutter={24} className="webroot-row">
        <Col span={12}>
          <Form.Item name="name" label={t('common.name')} 
            rules={[
              { required: true, message: t('form.required') },
              { type: 'string', max: 32, message: t('form.max_length', { max: 32 }) },
            ]}
            > 
            <Input disabled={isDisabled} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24} className="webroot-row">
        <Col span={12}>
          <Form.Item name="mode" label="Mode" rules={[{ required: true, message: t('form.required') }]}> 
            <Select
              disabled={isDisabled || !!parameterMode || (resource && resource.usage_count > 0)}
              value={mode}
              onChange={val => { setMode(val); form.setFieldsValue({ mode: val }); }}
            >
              <Select.Option value="Click">Click</Select.Option>
              <Select.Option value="Radius">Radius</Select.Option>
              <Select.Option value="Credentials">Credentials</Select.Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24} className="webroot-row">
        <Col span={12}>
          <Form.Item
            name="description"
            label={t('common.description')}
            rules={[{ max: 128, message: t('form.max_length', { max: 128 }) }]}
          >
            <Input.TextArea disabled={isDisabled} rows={2} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24} style={{ borderRadius: '16px', border: '1px solid #E7E7E7', padding: '16px', margin: 0, marginBottom: 68 }}>
        {/* Configuration */}
        <Col span={11} className="webroot-form">
          <h4 style={{ marginTop: 0, fontSize: '16px' }}>Configuration</h4>
          <Row gutter={24}>
            <Col span={24} style={{ display: 'flex' }}>
              <Form.Item name="backgroundColor" label="Background" rules={[{ required: true, message: 'Background is required.' }]}> 
                <Input type="text" disabled value={backgroundColor} readOnly/>
              </Form.Item>

              <div style={{ marginLeft: 16, position: 'relative' }}>
                <ColorPicker
                  value={backgroundColor}
                  disabled={isDisabled}
                  onChange={(color: any) => {
                    const val = typeof color === 'string' ? color : (color?.toHexString ? color.toHexString() : String(color));
                    setBackgroundColor(val);
                    form.setFieldsValue({ backgroundColor: val });
                  }}
                >
                  <Button className="upload-button" disabled={isDisabled}>Color</Button>
                </ColorPicker>
              </div>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24} style={{ display: 'flex' }}>
              <Form.Item name="logoBase64" label="Upload Logo"> 
                <Tooltip title={`PNG, JPG, JPEG and GIF, Must be less than ${defaultMaxLength.logo}kB`}>
                  <Input type="text" disabled readOnly value={logoBase64}
                    placeholder={`PNG, JPG, JPEG and GIF, Must be less than ${defaultMaxLength.logo}kB`}
                  />
                </Tooltip>
              </Form.Item>
              <div style={{ marginLeft: 16 }}>
                <Upload
                  accept="image/*"
                  showUploadList={false}
                  disabled={isDisabled}
                  style={{ marginLeft: 16 }}
                  beforeUpload={(file: File) => {
                    if (!validateImage(file, defaultMaxLength.logo * 1024)) {
                      return Upload.LIST_IGNORE as any;
                    }
                    const reader = new FileReader();
                    reader.onload = () => {
                      setLogoBase64(reader.result as string);
                      form.setFieldsValue({ logoBase64: reader.result as string });
                    };
                    reader.readAsDataURL(file);
                    return Upload.LIST_IGNORE as any;
                  }}
                >
                  <Button className="upload-button" disabled={isDisabled}>Upload Logo</Button>
                </Upload>
              </div>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={18}>
              <Form.Item name="welcomeMessage" label="Welcome Message"
                rules={[{ max: defaultMaxLength.welcomeMessage, message: `This length should be no more than ${defaultMaxLength.welcomeMessage}.` }]}
              >
                <Input disabled={isDisabled} onChange={e => debounceWelcome(e.target.value)} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={18}>
              <Form.Item name="termsOfService" label="Terms of Service"
                rules={[{ max: defaultMaxLength.termsOfService, message: `This length should be no more than ${defaultMaxLength.termsOfService}.` }]}
              >
                <Input.TextArea disabled={isDisabled} rows={5} onChange={e => debounceTerms(e.target.value)} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={18}>
              <Form.Item
                name="corporateInfo"
                label="Copyright"
                rules={[{ max: defaultMaxLength.corporateInfo, message: `This length should be no more than ${defaultMaxLength.corporateInfo}.` }]}
                className="form-item-uniform"
              >
                <Input.TextArea
                  disabled={isDisabled}
                  rows={5}
                  onChange={e => debounceCopyright(e.target.value)}
                />
              </Form.Item>
            </Col>
          </Row>
        </Col>
        {/* Preview */}
        <Col span={13} style={{ display: 'flex', flexDirection: 'column' }}>
          <h4 style={{ marginTop: 0, marginLeft: 18, fontSize: '16px' }}>Preview</h4>
          <div className="previewBox">
            <iframe ref={iframeRef} className="previewContent"/>
          </div>
        </Col>
      </Row>
    </FormModal>
  );
};

export default WebRootForm;