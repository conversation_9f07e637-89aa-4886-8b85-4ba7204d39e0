.tab-button {
  width: 120px;
  height: 36px;
  font-size: 14px;
  padding: 0;
  font-weight: 400;
  font-family: Lato, sans-serif;
  // border: 1px solid #C5D0D6;
  // background-color: #ffffff;
  // color: #333;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  position: relative;

  &:not(.active) {
    background-color: #FFFFFF !important;
    border: 1px solid #C5D0D6 !important;
    color: #929A9E !important;

    &:hover {
      color: #14C9BB !important;
      background-color: #FFFFFF !important;
      border-color: #C5D0D6 !important;
    }
  }


  &.active {
    background-color: rgba(20, 201, 187, 0.1) !important;
    border: 1px solid #14C9BB !important;
    color: #14C9BB !important;
    z-index: 1;
  }

  &:first-child {
    border-top-left-radius: 2px !important;
    border-bottom-left-radius: 2px !important;
  }

  &:last-child {
    border-top-right-radius: 2px !important;
    border-bottom-right-radius: 2px !important;
  }

  &+& {
    margin-left: -1px;
  }
}