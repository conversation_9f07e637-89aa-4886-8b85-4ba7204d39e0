import React, { Suspense } from "react";
import { Al<PERSON>, Col, Row, Spin } from "antd";
import { useTranslation } from "react-i18next";

import AssociationsPieChart from "./AssociationsPieChart";
import CommandsBarChart from "./CommandsBarChart";
import Connected<PERSON><PERSON><PERSON><PERSON> from "./ConnectedPieChart";
import DeviceTypes<PERSON><PERSON><PERSON>hart from "./DeviceTypesPieChart";
import Memory<PERSON><PERSON>Chart from "./MemoryPieChart";
import MemorySimpleChart from "./MemorySimpleChart";
import OverallHealthSimple from "./OverallHealth";
import OverallHealthPieChart from "./OverallHealthPieChart";
import UptimesBarChart from "./UptimesBarChart";
import devicesLogo from "@/modules-smb/Wireless/assets/Dashboard/icon_Devices.png";
import SimpleIconStatDisplay from "@/modules-smb/Wireless/components/Containers/SimpleIconStatDisplay";
import { useGetControllerDashboard } from "@/modules-smb/hooks/Network/Controller";

const WirelessViewPage: React.FC = () => {
  const { t } = useTranslation();
  const getDashboard = useGetControllerDashboard();
  console.log('getDashboard.data',getDashboard.data);
  return (
    <>
      {getDashboard ? (
        <Suspense fallback={<div>Loading...</div>}>
          {/* <Card style={{ marginBottom: 20 }} bordered={false}>
            {renderHeader()}
          </Card> */}

          {getDashboard.isLoading && (
            <div style={{ textAlign: "center", marginTop: 100 }}>
              <Spin size="large" />
            </div>
          )}

          {getDashboard.error && (
            <div style={{ textAlign: "center", marginTop: 100 }}>
              <Alert
                type="error"
                message={t("controller.dashboard.error_fetching")}
                description={
                  // @ts-ignore
                  getDashboard.error?.response?.data?.ErrorDescription || "Unknown error"
                }
                showIcon
              />
            </div>
          )}

          {getDashboard.data && (
            <Row gutter={[16, 16]}
            style={{ margin: 0, width: "100%", overflowX: "hidden",display: "flex", alignItems: "stretch" }}>
              <Col span={8}>
                <SimpleIconStatDisplay
                  title={t("devices.title")}
                  value={getDashboard.data?.numberOfDevices ?? 0}
                  description={t("controller.dashboard.devices_explanation")}
                  icon={<img src={devicesLogo} style={{ width: 30, height: 30 }} />}
                  color="#1677ff"
                />
              </Col>
              <Col span={8}>
                <OverallHealthSimple data={getDashboard.data.healths} />
              </Col>
              <Col span={8}>
                <MemorySimpleChart data={getDashboard.data.memoryUsed} />
              </Col>
              <Col span={6}>
                <AssociationsPieChart data={getDashboard.data.associations} />
              </Col>
              <Col span={6}>
                <MemoryPieChart data={getDashboard.data.memoryUsed} />
              </Col>
              <Col span={6}>
                <OverallHealthPieChart data={getDashboard.data.healths} />
              </Col>
              <Col span={6}>
                <ConnectedPieChart data={getDashboard.data} />
              </Col>
              <Col span={8}>
                <CommandsBarChart data={getDashboard.data.commands}/>
              </Col>
              <Col span={8}>
                <DeviceTypesPieChart data={getDashboard.data.deviceType}  />
              </Col>
              <Col span={8}>
                <UptimesBarChart data={getDashboard.data.upTimes}  />
              </Col>
            </Row>
          )}
        </Suspense>
      ) : (
        "error"
      )}
    </>
  );
};

export default WirelessViewPage;
