import * as React from 'react';
import { useTranslation } from 'react-i18next';
import SimpleIconStatDisplay from '@/modules-smb/Wireless/components/Containers/SimpleIconStatDisplay';
import { ControllerDashboardMemoryUsed } from '@/modules-smb/hooks/Network/Controller';
import iconHighMemoryDevices from "@/modules-smb/Wireless/assets/Dashboard/icon_High_Memory_Devices.png";

type Props = {
  data: ControllerDashboardMemoryUsed[];
};

const MemorySimpleChart = ({ data }: Props) => {
  const { t } = useTranslation();

  const highMemoryDevices = React.useMemo(
    () =>
      data.reduce((acc, curr) => {
        if (curr.tag === '> 75%') return acc + curr.value;
        return acc;
      }, 0),
    [data],
  );

  return (
    <SimpleIconStatDisplay
      title="High Memory Devices (>90%)"
      value={highMemoryDevices}
      description={t('controller.dashboard.memory_explanation')}
      icon={<img src={iconHighMemoryDevices} style={{ width: 30, height: 30 }} />}
      color={highMemoryDevices === 0 ? ['teal.300', 'teal.300'] : ['red.300', 'red.300']}
    />
  );
};

export default MemorySimpleChart;
