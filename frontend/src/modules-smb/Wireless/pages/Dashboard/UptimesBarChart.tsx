import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import BarEcharts from "@/modules-smb/Wireless/components/Echarts/BarEcharts";
import { COLORS } from "@/modules-smb/Wireless/constants/colors";
import { ControllerDashboardUptimes } from "@/modules-smb/hooks/Network/Controller";
import GraphStatDisplay from '@/modules-smb/Wireless/components/Containers/GraphStatDisplay';

type Props = {
  data: ControllerDashboardUptimes[];
};

const UptimesBarChart: React.FC<Props> = ({ data }) => {
  const { t } = useTranslation();

  const { labels, values } = useMemo(() => {
    const defaultObj = {
      now: 0,
      ">hour": 0,
      ">day": 0,
      ">week": 0,
      ">month": 0
    };

    for (const { tag, value } of data) {
      if (tag in defaultObj) defaultObj[tag] = value;
    }

    return {
      labels: ["now", ">day", ">week", ">month", ">hour"],
      values: [
        defaultObj.now,
        defaultObj[">day"],
        defaultObj[">week"],
        defaultObj[">month"],
        defaultObj[">hour"]
      ]
    };
  }, [data]);

  return (
    <GraphStatDisplay
          title={t('controller.dashboard.uptimes')}
          explanation={t('controller.dashboard.uptimes_explanation')}
          chart={(isModal)=>(
            <BarEcharts
              xAxis={labels}
              yAxisData={values}
              colorList={COLORS[0]}
              height={isModal? "300px":"31vh"}
              width="45%"
              // title=""
              tooltipStyle={{
                trigger: "axis",
                backgroundColor: 'rgba(0, 0, 0, 0.6)',  // 添加黑色半透明背景
                textStyle: { color: '#fff' },  
                formatter: (params: any[]) => {
                  const { name, value,color } = params[0];
                  const total = values.reduce((a, b) => a + b, 0);
                  const percent = total ? ((value / total) * 100).toFixed(2) : 0;
                  return `<div style="line-height:1.5">
                          <div>${name}</div>
                          <div>
                            <span style="
                              display: inline-block;
                              width: 10px;
                              height: 10px;
                              background: ${color};
                              margin-right: 5px;
                              border-radius: 2px;
                            "></span>
                            ${name}: ${value}(${percent}%)
                          </div>
                        </div>`;
                }
              }}
            />
          )           
          }
    />
  );
};

export default UptimesBarChart;
