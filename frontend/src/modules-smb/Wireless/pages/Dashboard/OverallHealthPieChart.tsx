import React, { useMemo, useState } from 'react';
import { Typo<PERSON>, Modal, Tooltip, Button, Spin, Alert, List } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import PieEcharts from '@/modules-smb/Wireless/components/Echarts/PieEcharts';
import GraphStatDisplay from '@/modules-smb/Wireless/components/Containers/GraphStatDisplay';
import { useGetDevicesWithHealthBetween } from '@/modules-smb/hooks/Network/HealthChecks';
import { ControllerDashboardHealth } from '@/modules-smb/hooks/Network/Controller';
import { AxiosError } from '@/modules-smb/models/Axios';
import { COLORS } from '@/modules-smb/Wireless/constants/colors';
import copy from 'copy-to-clipboard';

const { Title, Text, Link } = Typography;

const LABEL_TO_LIMITS = {
  '100%': { lowerLimit: 100, upperLimit: 100, label: 'With 100% Health' },
  '>=90%': { lowerLimit: 90, upperLimit: 99, label: 'Between 90% and 99%' },
  '>=60%': { lowerLimit: 60, upperLimit: 89, label: 'Between 60% and 89%' },
  '<60%': { lowerLimit: 0, upperLimit: 59, label: 'Between 0% and 59%' },
} as const;

type Props = {
  data: ControllerDashboardHealth[];
};

const OverallHealthPieChart: React.FC<Props> = ({ data }) => {
  const { t } = useTranslation();
  const [modalOpen, setModalOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  const [deviceCategory, setDeviceCategory] = useState<{ lowerLimit: number; upperLimit: number; label: string }>({
    lowerLimit: 0,
    upperLimit: 100,
    label: 'Between 0% and 100%',
  });

  const serialNumbersFromCategory = useGetDevicesWithHealthBetween(deviceCategory);

  const parsedData = useMemo(() => {
    const total = data.reduce(
      (acc, curr) => {
        let healthScore = 0;
        if (curr.tag === '100%') healthScore = curr.value * 100;
        else if (curr.tag === '>90%') healthScore = curr.value * 95;
        else if (curr.tag === '>60%') healthScore = curr.value * 75;
        else if (curr.tag === '<60%') healthScore = curr.value * 30;

        acc.totalHealth += healthScore;
        acc.totalDevices += curr.value;
        acc[curr.tag] = curr.value;
        return acc;
      },
      {
        totalHealth: 0,
        totalDevices: 0,
        '100%': 0,
        '>90%': 0,
        '>60%': 0,
        '<60%': 0,
      } as Record<string, number>
    );

    const labels: string[] = [];
    const seriesData: { name: string; value: number }[] = [];
    seriesData.push({ name: '100%', value: total['100%'] });
    seriesData.push({ name: '>=90%', value: total['>90%'] });
    seriesData.push({ name: '>=60%', value: total['>60%'] });
    seriesData.push({ name: '<60%', value: total['<60%'] });
    // if (total['100%'] > 0) seriesData.push({ name: '100%', value: total['100%'] });
    // if (total['>90%'] > 0) seriesData.push({ name: '>90%', value: total['>90%'] });
    // if (total['>60%'] > 0) seriesData.push({ name: '>60%', value: total['>60%'] });
    // if (total['<60%'] > 0) seriesData.push({ name: '<=60%', value: total['<60%'] });

    return seriesData;
  }, [data]);

  const onChartClick = (params: any) => {
    const label = params.name as keyof typeof LABEL_TO_LIMITS;
    if (LABEL_TO_LIMITS[label]) {
      setDeviceCategory(LABEL_TO_LIMITS[label]);
      setModalOpen(true);
    }
  };

  const handleCopy = () => {
    if (serialNumbersFromCategory.data) {
      copy(serialNumbersFromCategory.data.join(','));
      setCopied(true);
      setTimeout(() => setCopied(false), 1500);
    }
  };

  return (
    <>
      <GraphStatDisplay
        title={t('controller.dashboard.overall_health')}
        explanation={t('controller.dashboard.overall_health_explanation_pie')}
        chart={(isModal) => (
          <PieEcharts
            seriesData={parsedData}
            name={t('controller.dashboard.overall_health')}
            chartType="ring"
            height={isModal ? "300px" : "31vh"}
            maxWidth={"100%"}
            showPercent
            colorList={[COLORS[8],COLORS[11], COLORS[9], COLORS[10]]}
            onClicked={onChartClick}
            legendDirection="horizontal"
          />)
        }
      />

      <Modal
        open={modalOpen}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={500}
        title={t('controller.dashboard.overall_health')}
        extra={
          serialNumbersFromCategory.data?.length ? (
            <Tooltip title={copied ? `${t('common.copied')}!` : t('common.copy')}>
              <Button
                type="primary"
                icon={<CopyOutlined />}
                onClick={handleCopy}
              />
            </Tooltip>
          ) : null
        }
      >
        {serialNumbersFromCategory.isFetching ? (
          <Spin size="large" style={{ display: 'block', margin: '40px auto' }} />
        ) : serialNumbersFromCategory.error ? (
          <Alert
            message={t('common.error')}
            description={(serialNumbersFromCategory.error as AxiosError)?.response?.data?.ErrorDescription}
            type="error"
            showIcon
          />
        ) : (
          <>
            <Title level={5}>
              {serialNumbersFromCategory.data?.length} {t('devices.title')} {deviceCategory.label}
            </Title>
            <List
              size="small"
              bordered
              dataSource={serialNumbersFromCategory.data?.sort((a, b) => a.localeCompare(b))}
              style={{ maxHeight: '60vh', overflowY: 'auto' }}
              renderItem={(item) => (
                <List.Item>
                  <Text code>
                    <Link href={`/wireless/devices/${item}#/device/${item}`}>{item}</Link>
                  </Text>
                </List.Item>
              )}
            />
          </>
        )}
      </Modal>
    </>
  );
};

export default OverallHealthPieChart;
