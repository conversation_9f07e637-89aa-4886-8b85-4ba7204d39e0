import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import GraphStatDisplay from '@/modules-smb/Wireless/components/Containers/GraphStatDisplay';
import PieEcharts from '@/modules-smb/Wireless/components/Echarts/PieEcharts';
import { ControllerDashboardDeviceType } from '@/modules-smb/hooks/Network/Controller';
import { COLORS } from '@/modules-smb/Wireless/constants/colors';

type Props = {
  data: ControllerDashboardDeviceType[];
};

const DeviceTypesPieChart = ({ data }: Props) => {
  const { t } = useTranslation();

  const seriesData = useMemo(() => {
    const sorted = [...data].sort((a, b) => b.value - a.value);
    const result: { name: string; value: number }[] = [];

    let otherTotal = 0;
    for (let i = 0; i < sorted.length; i++) {
      if (i < 5) {
        result.push({ name: sorted[i].tag, value: sorted[i].value });
      } else {
        otherTotal += sorted[i].value;
      }
    }

    if (otherTotal > 0) {
      result.push({ name: t('controller.dashboard.others'), value: otherTotal });
    }

    return result;
  }, [data, t]);
  
  return (
    <GraphStatDisplay
      title={t('controller.dashboard.device_types')}
      explanation={t('controller.dashboard.device_types_explanation')}
      chart={(isModal) => (
        <PieEcharts
          seriesData={seriesData}
          name={t('controller.dashboard.device_types')}
          chartType="pie"
          height={isModal ? "300px" : "31vh"} 
          maxWidth={"100%"}
          showPercent={true}
          colorList={COLORS}
          legendDirection="vertical"
        />)
      }
    />
  );
};

export default DeviceTypesPieChart;
