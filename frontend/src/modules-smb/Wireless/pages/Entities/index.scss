.div-main {
  width: 100%;
  height: 100%;
  flex-direction: column;
  padding: 0;
  margin: 0;
}

.div-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 16px 16px 8px 0;
  background-color: transparent;
  border-bottom: none;
}

.div-tabs {
  flex: 1;
  background: #fff;
  margin-top: 10px;
  display: flex;
  overflow: hidden;

  .ant-tabs-content {
    overflow: auto !important;
  }

  .chakra-tabs__tablist {
    border-bottom: none !important;
    padding: 0;
    margin-bottom: 20px;
  }

  .chakra-tabs__tab {
    color: #929A9EFF !important;
    cursor: pointer;
    padding: 5px;
    margin-right: 20px;
    background: none;
  }

  .css-ph9xv1[aria-selected=true],
  .css-ph9xv1[data-selected] {
    color: #14C9BBFF !important;
    border-bottom-width: 3px;
  }

  .css-ph9xv1:hover {
    color: #14C9BB !important;
  }
}

.div-header> :first-child {
  margin-right: auto;
}

.action-buttons {
  margin-left: auto;

  .anticon {
    font-size: 35px !important;
  }

  .ant-btn.ant-btn-text.ant-btn-icon-only {
    background: transparent !important;
    border-color: transparent !important;
    box-shadow: none !important;
    transition: background-color 0.2s ease !important;

    &:hover,
    &:focus,
    &:active {
      background: transparent !important;
      border-color: transparent !important;
      box-shadow: none !important;
    }
  }


  .monitor-button {
    &:hover .anticon svg {
      rect {
        fill: #34DCCF;
      }
    }
  }


  .refresh-button {
    &:hover .anticon svg {

      rect {
        fill: #E4E7E8;
      }
    }
  }

  .actions-button {
    &:hover .anticon svg {

      rect {
        fill: #E4E7E8;
      }
    }
  }
}

.stop-button.ant-btn {
  background-color: #14C9BB !important;
  border-color: #14C9BB !important;
  color: #fff !important;
}


.stop-button.ant-btn:hover,
.stop-button.ant-btn:focus {
  background-color: #34DCCF !important;
  border-color: #34DCCF !important;
  color: #fff !important;
}