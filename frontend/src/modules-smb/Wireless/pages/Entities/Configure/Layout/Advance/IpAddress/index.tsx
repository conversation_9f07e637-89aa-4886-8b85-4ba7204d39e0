import React, {useEffect, useState} from "react";
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import { Radio,Row,Col } from 'antd';
import useFastField from '@/modules-smb/hooks/useFastField';
import {useFormikContext} from 'formik';
const IpAddress = () => {
    const { value: vlan } = useFastField({ name: "configuration['IP address'].vlan.enabled" });
    const { value: vlanAll } = useFastField({ name: "configuration['IP address'].vlan" });
    const { value: ipv6Enabled } = useFastField({ name: "configuration['IP address'].ipv6.enabled" });
    const { value: ipv6 } = useFastField({ name: "configuration['IP address'].ipv6" });

    const { values, setFieldValue, errors } = useFormikContext<any>();
    useEffect(() => {
      if (vlan) {
        if(!vlanAll||(vlanAll&&Object.keys(vlanAll).length<=1&&vlanAll.enabled===true))
        setFieldValue("configuration['IP address'].vlan.id", 1080);
        setFieldValue("configuration['IP address'].vlan.enabled", true);
      } else{
        setFieldValue("configuration['IP address'].vlan", undefined);
      }
    }, [vlan]);

    useEffect(() => {
      if (ipv6Enabled) {
        if(!ipv6||(ipv6&&Object.keys(ipv6).length<=1&&ipv6.enabled===true))
        setFieldValue("configuration['IP address'].ipv6.addressing", "dynamic");
        setFieldValue("configuration['IP address'].ipv6.enabled", true);
      } else{
        setFieldValue("configuration['IP address'].ipv6", undefined);
      }
    }, [ipv6Enabled]);
    return (
        <>
        <div style={{display:"flex"}}>
            <Row gutter={[20, 0]} style={{ marginBottom: 0, marginTop: 0, width: '100%' }}>
                <Col>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 2,width:'180px'}}>
                        <span>IPv4</span>
                        <Radio style={{ marginLeft: '160px'}} name="configuration['IP address'].ipv4.addressing" defaultChecked disabled={false}>
                            Dynamic
                        </Radio>
                    </div>
                </Col>
            </Row>
            <Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: -6, width: '100%' }}>
                <Col>
                    <ToggleField
                    name="configuration['IP address'].ipv6.enabled"
                    label="IPv6"
                    definitionKey="interface.ipv6.port-forward.protocol"
                    />
                </Col>
            </Row>
        </div>   
        <ToggleField
            name="configuration['IP address'].vlan.enabled"
            label="VLAN"
            definitionKey="interface.vlan"
        />
        {vlan&&(<NumberField name={`configuration['IP address'].vlan.id`} label=" " w={140} />)}
        </>
    );
};

export default IpAddress;