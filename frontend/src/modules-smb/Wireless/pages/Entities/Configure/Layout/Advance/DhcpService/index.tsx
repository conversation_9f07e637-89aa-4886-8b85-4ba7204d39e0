import React, { useState, useEffect, useCallback } from "react";
import { Table, Button, Space, message, Modal, Flex } from "antd";
import Icon from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import DhcpForm from './DhcpForm/Form';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import { getDhcpServiceList,getDhcpServiceDetail,deleteDhcpService} from '@/modules-smb/Wireless/apis/dhcp_service_api';
import FormattedDate from '@/modules-smb/components/FormattedDate';
import { AmpConCustomModal } from '@/modules-ampcon/components/custom_table';
import { addSvg } from "@/utils/common/iconSvg";
interface Props {
    siteId?: number;
}

const Network: React.FC<Props> = ({ siteId }) => {
    // url中获取siteId
    if (window.location.hash) {
        const hash = window.location.hash.replace('#', '');
        if (/^\d+$/.test(hash)) {
            siteId = parseInt(hash, 10);
        }
    }
    const [data, setData] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [modalOpen, setModalOpen] = useState(false);
    const [editingResource, setEditingResource] = useState<any>(null);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [sortInfo, setSortInfo] = useState<{ id: any; sort: string; }[]>([]);
    const formatModifiedTime = useCallback((value ) => {
        const timestamp = Math.floor(new Date(value).getTime() / 1000) ;
        return <FormattedDate date={timestamp} />;
    }, []);

    const fetchList = (currentPagination: typeof pagination, currentSortInfo: typeof sortInfo) => {
        setIsLoading(true);
        if (siteId==null || siteId==undefined) {
            setIsLoading(false);
            return;
        }
        const sortBy = currentSortInfo.length > 0 ? currentSortInfo[0].field : '';
        const sortType = currentSortInfo.length > 0 ? currentSortInfo[0].order : '';

        const params: any = {
            siteId,
            pageNum: currentPagination.current,
            pageSize: currentPagination.pageSize,
        };

 
        if (sortBy) {
            params.sortBy = sortBy;
            params.sortType = sortType;
        }

        getDhcpServiceList(params)
            .then(res => {
                if (res?.status !== 200) {
                    message.error(res?.info);
                    return;
                }
                setData(res?.info || []);
                setPagination({
                    ...currentPagination,
                    total: res?.total || 0,
                });
            })
            .catch(() => message.error('Failed to fetch list'))
            .finally(() => setIsLoading(false));
    };


    useEffect(() => {
        fetchList(pagination, sortInfo);
    }, [siteId]);


    const handleTableChange = (pagination, filters, sorter) => {
        // 更新分页状态
        const updatedPagination = {
            current: pagination.current || 1,
            pageSize: pagination.pageSize || 10,
            total: pagination.total, // 总条数不变
        };
        let sortField = sorter.field;
        const updatedSortInfo = sorter.field
            ? [{
                field: sortField,
                order: sorter.order === 'ascend' ? 'asc' : 'desc'
            }]
            : [];
        setSortInfo(updatedSortInfo);
        fetchList(updatedPagination, updatedSortInfo);
    };


    const handleDelete = (record: any) => {
        confirmModalAction(
            `Are you sure you want to delete this ${record.name}?`,
            () => {
                deleteDhcpService({ id: record.id })
                    .then(res => {
                        if (res?.status !== 200) {
                            message.error(res?.info);
                            return;
                        }
                        message.success('Deleted successfully');
                        fetchList(pagination, sortInfo); // 用当前分页和排序刷新
                    })
                    .catch(() => message.error('Delete failed'));
            }
        );
    };


    const handleEdit = (record: any) => {
        getDhcpServiceDetail({ id: record.id })
            .then(res => {
                if (res?.status !== 200) {
                    message.error(res?.info);
                    return;
                }
                let detail = res?.info || null;
                setEditingResource(detail);
                setModalOpen(true);
            })
            .catch(() => message.error('Failed to fetch detail'));
    };




    const handleCreate = () => {
        setEditingResource(null);
        setModalOpen(true);
    };

    // 关闭模态框
    const handleCloseModal = () => {
        setModalOpen(false);
        setEditingResource(null);
    };


    const columns: ColumnsType<any> = [
        { title: 'Name', dataIndex: 'name', key: 'name', sorter: true },
        { title: 'IPv4 subNet', dataIndex: 'subnet', key: 'subnet', sorter: true },
        { title: 'VLAN', dataIndex: 'vlan', key: 'vlan', sorter: true },
        { title: 'Modified', dataIndex: 'modified_time', key: 'modified_time', render: (text) => formatModifiedTime(text), sorter: true },
        { title: 'Description', dataIndex: 'description', key: 'description', sorter: true },
        {
            title: 'Operation',
            key: 'operation',
            render: (_: any, record: any) => (
                <Space size={24}>
                    <Button style={{ padding: 0 }} type="link" onClick={() => handleEdit(record)}>Edit</Button>
                    <Button style={{ padding: 0 }} type="link" onClick={() => handleDelete(record)}>Delete</Button>
                </Space>
            ),
        },
    ];
    return (
        <div>
            <div style={{ marginBottom: 24 }}>
                <Button type="primary" onClick={handleCreate}>
                    <Icon component={addSvg} />
                    Create
                </Button>
            </div>
            <Flex vertical style={{ position: 'relative', width: '100%',marginBottom: data && data.length > 0?'8px':'24px' }}>
            <Table
                columns={columns}
                dataSource={data}
                loading={isLoading}
                onChange={handleTableChange} 
                pagination={{
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    total: pagination.total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `Total ${total} items`,
                    pageSizeOptions: ['10', '20', '50', '100'],
                }}
                scroll={{ x: 1000 }}
                rowKey="key"
                bordered
            />
           </Flex>
            <AmpConCustomModal
                isModalOpen={modalOpen}
                title={editingResource ? 'Edit' : 'Create'}
                onCancel={handleCloseModal}
                modalClass="ampcon-max-modal"
                childItems={
                    <DhcpForm
                        onClose={handleCloseModal}
                        refresh={() => fetchList(pagination, sortInfo)} // 刷新时用当前分页和排序
                        resource={editingResource}
                        siteId={siteId}
                    />
                }
            />
            </div>
    );
};

export default Network;