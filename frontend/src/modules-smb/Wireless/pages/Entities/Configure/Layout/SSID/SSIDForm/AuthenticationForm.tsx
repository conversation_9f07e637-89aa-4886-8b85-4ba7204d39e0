import React, { useState }  from 'react';
import { Form, Input, Select, Row, Col, Switch } from 'antd';
import { useTranslation } from 'react-i18next';
import { LabelTip } from '@/modules-smb/Wireless/components/LabelTip';
import {
  ENCRYPTION_PROTOS_REQUIRE_RADIUS,
  ENCRYPTION_PROTOS_REQUIRE_IEEE,
} from '@/modules-smb/pages/ConfigurationPage/ConfigurationCard/ConfigurationSectionsCard/InterfaceSection/interfacesConstants';
import ProfileSelect from '@/modules-smb/Wireless/components/ProfileSelect';
import ProfileSwitchSelect from '@/modules-smb/Wireless/components/ProfileSwitchSelect';

interface AuthenticationFormProps {
  resource?: any;
  siteId?: number;
}

const PROTO_OPTIONS = {
  Enterprise: [
    { value: "wpa", label: "WPA-Enterprise" },
    { value: "wpa2", label: "WPA2-Enterprise EAP-TLS" },
    { value: "wpa-mixed", label: "WPA-Enterprise-Mixed" },
    { value: "wpa3", label: "WPA3-Enterprise EAP-TLS" },
    { value: "wpa3-192", label: "WPA3-192-Enterprise EAP-TLS" },
    { value: "wpa3-mixed", label: "WPA3-Enterprise-Mixed" },
  ],
  Personal: [
    { value: "psk", label: "WPA-PSK" },
    { value: "psk2", label: "WPA2-PSK" },
    { value: "psk2-radius", label: "PSK2-RADIUS" },
    { value: "psk-mixed", label: "WPA-PSK/WPA2-PSK Personal Mixed" },
    { value: "sae", label: "WPA3-SAE" },
    { value: "sae-mixed", label: "WPA2/WPA3 Transitional" },
  ],
  Open: [
    { value: "none", label: "None" },
    { value: "owe", label: "OWE" },
    { value: "owe-transition", label: "OWE-Transition" },
  ],
};

const MULTI_PROTOS = ["psk", "psk2", "psk-mixed"];
const ENCRYPTION_PROTOS_REQUIRE_KEY = ["psk", "psk2", "psk-mixed", "sae", "sae-mixed"];


const AuthenticationForm: React.FC<AuthenticationFormProps> = ({ resource, siteId }) => {
  const { t } = useTranslation();
  const [multiPskEnabled, setMultiPskEnabled] = useState(!!resource?.['multi-psk']);
  const form = Form.useFormInstance();
  const wifiBands = Form.useWatch('wifi-bands', form);

  // 根据proto初始值自动选中Security Level
  const initialProto = resource?.encryption?.proto || 'psk2';
  function getSecurityLevelByProto(protoValue: string): 'Enterprise' | 'Personal' | 'Open' {
    for (const key of Object.keys(PROTO_OPTIONS)) {
      if (PROTO_OPTIONS[key as 'Enterprise' | 'Personal' | 'Open'].some(opt => opt.value === protoValue)) {
        return key as 'Enterprise' | 'Personal' | 'Open';
      }
    }
    return 'Personal';
  }
  // 监听表单proto字段
  const proto = Form.useWatch(['encryption', 'proto'], form) || initialProto;
  const [securityLevel, setSecurityLevel] = useState<'Enterprise' | 'Personal' | 'Open'>(getSecurityLevelByProto(proto));

  // 切换Security Level时自动设置proto为第一个选项
  const isInitRef = React.useRef(true);

  React.useEffect(() => {
    if (isInitRef.current) {
      isInitRef.current = false;
      return;
    }
    const firstProto = PROTO_OPTIONS[securityLevel][0]?.value;
    if (firstProto) {
      form.setFieldsValue({ encryption: { ...form.getFieldValue('encryption'), proto: firstProto } });
    }
  }, [securityLevel]);
  
  // proto依赖的逻辑动态变化
  const [isKeyNeeded, setIsKeyNeeded] = useState(ENCRYPTION_PROTOS_REQUIRE_KEY.includes(proto));
  const [needIeee, setNeedIeee] = useState(ENCRYPTION_PROTOS_REQUIRE_IEEE.includes(proto));
  const [isUsingRadius, setIsUsingRadius] = useState(ENCRYPTION_PROTOS_REQUIRE_RADIUS.includes(proto));

  React.useEffect(() => {
    setIsKeyNeeded(ENCRYPTION_PROTOS_REQUIRE_KEY.includes(proto));
    setNeedIeee(ENCRYPTION_PROTOS_REQUIRE_IEEE.includes(proto));
    setIsUsingRadius(ENCRYPTION_PROTOS_REQUIRE_RADIUS.includes(proto));
    // proto变更时自动切换Security Level
    const level = getSecurityLevelByProto(proto);
    if (level !== securityLevel) {
      setSecurityLevel(level);
    }
    // 每次 proto 切换时，将 ieee80211w 设为 'required'
    const current = form.getFieldValue('encryption') || {};
    form.setFieldsValue({ encryption: { ...current, ieee80211w: 'required' } });
  }, [proto]);
  
  // 当wifi-bands变化时，重新验证proto字段
  React.useEffect(() => {
    if (wifiBands) {
      form.validateFields([['encryption', 'proto']]);
    }
  }, [wifiBands]);

  function hexToRgba(hex: string, alpha: number) {
    let c = hex.replace('#', '');
    if (c.length === 8) c = c.slice(0, 6); // 去掉透明度
    if (c.length === 3) c = c.split('').map(x => x + x).join('');
    const num = parseInt(c, 16);
    const r = (num >> 16) & 255;
    const g = (num >> 8) & 255;
    const b = num & 255;
    return `rgba(${r},${g},${b},${alpha})`;
  }

  return (
    <>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item label="Security Level">
            <div style={{ width: '100%', display: 'flex'}}>
              <div style={{ display: 'flex', gap: 2 }}>
                {[{ key: 'Enterprise', color: '#2BC174FF' }, { key: 'Personal', color: '#F8961EFF' }, { key: 'Open', color: '#F53F3FFF' }].map((seg, idx, arr) => (
                  <div
                    key={seg.key}
                    onClick={() => setSecurityLevel(seg.key as any)}
                    style={{
                      cursor: 'pointer',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <div
                      style={{
                        width: 90,
                        height: 8,
                        background: seg.color,
                        borderTopLeftRadius: idx === 0 ? 24 : 0,
                        borderBottomLeftRadius: idx === 0 ? 24 : 0,
                        borderTopRightRadius: idx === arr.length - 1 ? 24 : 0,
                        borderBottomRightRadius: idx === arr.length - 1 ? 24 : 0,
                        position: 'relative',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginBottom: 4,
                      }}
                    >
                      {securityLevel === seg.key && (
                        <span style={{
                          position: 'absolute',
                          right: '40%',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          width: 12,
                          height: 12,
                          borderRadius: '50%',
                          border: '3px solid #fff',
                          background: seg.color,
                          boxShadow: `0px 0px 6px 0px ${hexToRgba(seg.color, 0.3)}`,
                          display: 'inline-block',
                        }} />
                      )}
                    </div>
                    <div style={{ color: '#333', fontSize: 12, fontWeight: 500, marginTop: 2 }}>{seg.key}</div>
                  </div>
                ))}
              </div>
            </div>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name={['encryption', 'proto']}
            label="Protocol"
            tooltip={LabelTip('interface.ssid.encryption.proto')}
            rules={[
              { required: true, message: t('form.required') },
              {
                validator: (_: any, v: any) => {
                  // 如果选择了 6G 频段，则不能选择 OWE-Transition
                  const bands = form.getFieldValue('wifi-bands')
                  if (v === 'owe-transition' && bands && bands.includes('6G')) {
                    return Promise.reject(new Error(t('form.invalid_proto_6g')));
                  }
                  return Promise.resolve();
                }
              },
            ]}
          >
            <Select>
              {PROTO_OPTIONS[securityLevel].map(opt => (
                <Select.Option key={opt.value} value={opt.value}>{opt.label}</Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          {/* 根据 needIeee 判断是否显示 ieee80211w 字段 */}
          {needIeee && (
            <Form.Item
              name={['encryption', 'ieee80211w']}
              label="IEEE80211W"
              tooltip={LabelTip('interface.ssid.encryption.ieee80211w')}
              rules={[
                { required: true, message: t('form.required') },
                {
                  validator: (_: any, v: any) => {
                    // 如果是 OWE 类型则不能为 disabled
                    if ((proto === 'owe' || proto === 'owe-transition') && v === 'disabled') {
                      return Promise.reject(new Error(t('form.invalid_ieee')));
                    }
                    return Promise.resolve();
                  }
                },
                {
                  validator: (_: any, v: any) => {
                    // 对于 WPA3/SAE 必须为 required
                    if ((proto === 'wpa3' || proto === 'wpa3-192' || proto === 'wpa3-mixed' || proto === 'sae') && v !== 'required') {
                      return Promise.reject(new Error(t('form.invalid_ieee_required')));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <Select placeholder="ieee80211w">
                <Select.Option value="disabled">Disabled</Select.Option>
                <Select.Option value="optional">Optional</Select.Option>
                <Select.Option value="required">Required</Select.Option>
              </Select>
            </Form.Item>
          )}
        </Col>
        <Col span={12}>
          {/* 根据 isKeyNeeded 判断是否显示 key 字段 */}
          {isKeyNeeded && (
            <Form.Item
              name={['encryption', 'key']}
              label="Key"
              tooltip={LabelTip('interface.ssid.encryption.key')}
              rules={[
                { required: true, message: t('form.min_max_string', { min: 8, max: 63 }) },
                { min: 8, max: 63, message: t('form.min_max_string', { min: 8, max: 63 }) },
              ]}
            >
              <Input.Password placeholder="key" />
            </Form.Item>
          )}
        </Col>
      </Row>
      {/* Radius 配置部分 */}
      {isUsingRadius && (
        <Row gutter={24}>
          <Col span={24}>
            <ProfileSelect
              label="Radius"
              formName={['radius']}
              type={1}
              siteId={siteId}
              proto={proto}
              edit={resource?.radius}
            />
          </Col>
        </Row>
      )}
      {/* multi-psk 模块 */}
      {MULTI_PROTOS.includes(proto) && (
        <Row gutter={24}>
          <Col span={24}>
            <ProfileSwitchSelect
              label="MPSK"
              formName={['multi-psk']}
              switchEnabled={multiPskEnabled}
              onSwitchChange={setMultiPskEnabled}
              type={2}
              siteId={siteId}
              edit={resource?.['multi-psk']}
            />
          </Col>
        </Row>
      )}
    </>
  );
};

export default AuthenticationForm;