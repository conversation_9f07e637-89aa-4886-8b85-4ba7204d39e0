import React, { useState, useRef, useEffect, useMemo, useImperativeHandle, forwardRef } from "react";
import SetRadioForm, { SetRadioFormRef } from "./RadioForm/SetRadioForm";
import { Button, Form, message, Spin, Select, Switch, Radio, Divider, Input, Row, Col } from "antd";
import { useNavigate } from "react-router-dom";
import { SINGLE_RADIO_SCHEMA } from "./radiosConstants";
import { useTranslation } from "react-i18next";
import TimeRangeModal from "@/modules-smb/Wireless/pages/Profile/TimeRange/TimeRangeModal"
import { getWirelessProfileList } from "@/modules-smb/Wireless/apis/wireless_profile_api";
import { setProfileVariable, getProfileVariable } from "@/modules-smb/Wireless/utils/util";
import { PlusOutlined } from "@ant-design/icons";
import { useGetConfiguration } from "@/modules-smb/hooks/Network/Configurations";
import { updateConfigureGeneral } from "@/modules-smb/Wireless/apis/configure_api";
import "@/modules-smb/Wireless/pages/Entities/Configure/Layout/Radios/RadioForm.css";
import CountryListSelector from "@/modules-smb/Wireless/pages/Entities/Configure/Layout/Radios/CountryListSelector";
import { LabelTip } from '@/modules-smb/Wireless/components/LabelTip';
import isEqual from 'lodash-es/isEqual';
import _ from "lodash";
import { filterNullValues } from '@/modules-smb/Wireless/utils/util';

type BandType = "2G" | "5G" | "6G";
const itemLayout = { labelCol: { style: { width: 160 } }, wrapperCol: { style: { width: 300 } } };

function radiosArrayToObject(payload: { radios?: any[] }) {
  const result: Record<string, any> = {};
  (payload.radios || []).forEach((r) => {
    const band = r.band as BandType;
    if (band) result[band] = r;
  });
  return result;
}

// 获取time range profile列表
function useSiteProfiles(siteId: any, type: number) {
  const [profiles, setProfiles] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (siteId == null) return;
    setLoading(true);
    getWirelessProfileList(type, siteId, 1, 10000)
      .then(res => res?.status === 200 && setProfiles(res.info || []))
      .catch(() => message.error("Failed to fetch time range profiles"))
      .finally(() => setLoading(false));
  }, [siteId, type]);

  return { profiles, setProfiles, loading };
}

// 通过configid获取配置信息
function useRadioConfig(siteId: any) {
  const configId = siteId != null ? `${siteId}-radio` : undefined;
  const { data: configurations = [], refetch, isLoading } = useGetConfiguration({ id: configId });

  useEffect(() => {
    if (siteId) refetch();
  }, [siteId, refetch]);

  const raw = configurations?.configuration?.[0]?.configuration;
  const radioValues = useMemo(() => {
    if (!raw) return null;
    try {
      return radiosArrayToObject(JSON.parse(raw));
    } catch {
      return null;
    }
  }, [raw]);

  return { radioValues, configId, isLoading, refetch };
}

type RadiosProps = {
  onDirtyChange?: (dirty: boolean) => void;
};

export type RadiosRef = {
  reset: () => void;
  apply: () => void;
};

const Radios = forwardRef<RadiosRef, RadiosProps>(({ onDirtyChange }, ref) => {
  const [form] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const formikRef = useRef<SetRadioFormRef>(null);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const type = 4; // time range profile 类型
  const bands: BandType[] = ["2G", "5G", "6G"];
  const [parentDirty, setParentDirty] = useState(false);
  const [childDirty, setChildDirty] = useState(false);
  const isModified = parentDirty || childDirty;
  // useBlockNavigation(isModified);

  useEffect(() => {
    onDirtyChange?.(isModified);
  }, [isModified, onDirtyChange]);

  useImperativeHandle(ref, () => ({
    reset: () => handleCancel(),
    apply: () => getFormmik(),
  }));

  // get siteid
  const [siteId, setSiteId] = useState<number | null>(null);
  useEffect(() => {
    const hash = location.hash.replace("#", "");
    const parsed = Number(hash);
    setSiteId(parsed);
  }, [location.hash]);
  useEffect(() => {
    setParentDirty(false);
    setChildDirty(false);
  }, [siteId]);
  // console.log(siteId)

  const { profiles, setProfiles, loading: profilesLoading } = useSiteProfiles(siteId, type);
  const { radioValues, configId, isLoading: configLoading, refetch } = useRadioConfig(siteId);
  const pageLoading = siteId == null || profilesLoading || configLoading;

  //父组件初始化表单
  const initialValues = useMemo(() => {
    const base = radioValues?.["2G"] || {};
    return {
      country: base.country || "US",
      "radio-schedule-enable": base["radio-schedule-enable"] === 1,
      "radio-schedule-radio-mode": base["radio-schedule-radio-mode"] ?? 0,
      "time-range-index": base["time-range-index"] ? String(getProfileVariable(base["time-range-index"])) : undefined,
      "time-range-name": base["time-range-name"] || undefined,
    };
  }, [radioValues]);

  //子组件radio初始化表单
  const radioInitialValues = useMemo(() => {
    if (!radioValues) return null;
    const copy = JSON.parse(JSON.stringify(radioValues));

    Object.entries(copy).forEach(([band, config]: [string, any]) => {
      delete config["country"];
      delete config["radio-schedule-enable"];
      delete config["radio-schedule-radio-mode"];
      delete config["time-range-name"];
      delete config["time-range-index"];
    });
    return copy;
  }, [radioValues]);
  // console.log(radioInitialValues);

  //数据处理完再渲染
  const initialValuesRef = useRef(initialValues);
  const radioInitialValuesRef = useRef(radioInitialValues);
  useEffect(() => {
    if (!pageLoading) {
      form.setFieldsValue(initialValues);
      initialValuesRef.current = initialValues;
      setParentDirty(false);
      if (radioInitialValues) {
        radioInitialValuesRef.current = radioInitialValues;
        setChildDirty(false);
      }
    }
  }, [pageLoading, initialValues, radioInitialValues]);


  const countrycode = Form.useWatch("country", form);
  const [currentCountry, setCurrentCountry] = useState(initialValues.country);
  useEffect(() => {
    if (countrycode) setCurrentCountry(countrycode);
  }, [countrycode]);

  // 父组件表单校验
  const getFormmik = () => {
    form
      .validateFields()
      .then(() => {
        formikRef.current?.submit();
      })
      .catch(() => {
        message.error("Please check your input");
      });
  };

  //处理子组件form
  const transformToRadiosconfig = (values: Record<string, any>, parentValues: Record<string, any>) => {
    const enableSchedule = parentValues["radio-schedule-enable"];
    const shared: Record<string, any> = {
      "radio-schedule-enable": enableSchedule ? 1 : 0,
      country: parentValues.country,
    };

    if (enableSchedule) {
      if (parentValues["time-range-index"]) {
        shared["time-range-index"] = setProfileVariable(parentValues["time-range-index"]);
      }
      if (parentValues["time-range-name"]) {
        shared["time-range-name"] = parentValues["time-range-name"];
      }
      if (typeof parentValues["radio-schedule-radio-mode"] !== "undefined") {
        shared["radio-schedule-radio-mode"] = parentValues["radio-schedule-radio-mode"];
      }
    }

    const radios = bands
      .map(band => {
        const bandConfig = values[band];
        if (!bandConfig || bandConfig.enabled === false) return null;
        const configCopy = { ...bandConfig };
        if (!enableSchedule) {
          delete configCopy["radio-schedule-radio-mode"];
          delete configCopy["time-range-name"];
          delete configCopy["time-range-index"];
        }
        return {
          ...configCopy, band, ...shared,
        };
      })
      .filter(Boolean);
    return { radios };
  };

  // 调用接口提交
  const handleFormikApply = async (formikValues: Partial<Record<BandType, any>>) => {
    // 校验函数校验
    try {
      for (const band of Object.keys(formikValues) as BandType[]) {
        const value = formikValues[band];
        if (value) {
          await SINGLE_RADIO_SCHEMA(t).validate(value, { abortEarly: false });
        }
      }
    } catch (validationError: any) {
      message.error("Please check your input");
      return;
    }
    const parentValues = form.getFieldsValue();
    const radiosObj = transformToRadiosconfig(formikValues, parentValues);
    const payload = [{ name: "Radio", description: "Radio configuration", weight: 0, configuration: JSON.stringify(radiosObj) }];
    // console.log("configuration",payload)
    try {
      await updateConfigureGeneral(configId, siteId, JSON.stringify(payload));
      setParentDirty(false);
      setChildDirty(false);
      message.success("Successfully applied radio configuration");
    } catch (err) {
      message.error("Failed to apply radio configuration");
      return;
    }

    const result = await refetch();
    if (result.error) {
      message.warning("Radio configuration saved, but refresh failed");
    }
  };

  const handleCancel = async () => {
    const result = await refetch();
    if (result.error) {
      message.error("Failed to reset changes");
    } else {
      form.resetFields(); //reset parent form
      formikRef.current?.resetForm();//reset radio form
      setParentDirty(false);
      setChildDirty(false);
      message.info("Changes have been reset.");
    }
  };

  const radioScheduled = Form.useWatch("radio-schedule-enable", form);

  return (
    <Spin spinning={pageLoading}>
      <div style={{ minWidth: 1050 }}>
        <Form
          form={form}
          layout="horizontal"
          initialValues={initialValues}
          labelAlign="left"
          {...itemLayout}
          onValuesChange={() => setParentDirty(true)}
        >
          {/* Country */}
          <Row gutter={16} align="middle">
            <Col span={10} style={{ marginLeft: 12 }}>
              <Form.Item
                label="Country"
                tooltip={LabelTip('radio.country')}
                required
                name="country"
                noStyle={false}
              >
                <CountryListSelector
                  value={form.getFieldValue("country")}
                  onChange={(val) => form.setFieldsValue({ country: val })}
                  style={{ width: 280 }}
                />
              </Form.Item>
            </Col>
          </Row>
          {/* Radio Scheduled */}
          <Row gutter={16} align="middle" >
            <Col span={10} style={{ marginLeft: 12 }}>
              <Form.Item
                label="Radio Scheduled"
                name="radio-schedule-enable"
                valuePropName="checked"
                noStyle={false}
              >
                <Switch onChange={(checked: boolean) => {
                  form.setFieldsValue({
                    "radio-schedule-enable": checked,
                    "time-range-index": undefined,
                    "time-range-name": undefined,
                  });
                  if (checked && siteId != null) {
                    getWirelessProfileList(type, siteId, 1, 10000)
                      .then(res => res?.status === 200 && setProfiles(res.info || []))
                      .catch(() => message.error("Failed to reload time range profiles"));
                  }
                }} />
              </Form.Item>
            </Col>
          </Row>
          {radioScheduled && (
            <>
              {/* Radio Mode */}
              <Row gutter={16} >
                <Col span={10} style={{ marginLeft: 12 }}>
                  <Form.Item
                    label="Radio Mode"
                    name="radio-schedule-radio-mode"
                    noStyle={false}
                  >
                    <Radio.Group>
                      <Radio value={1}>Radio On</Radio>
                      <Radio value={0}>Radio Off</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>
              {/* Hidden Time Range Name */}
              <Form.Item name="time-range-name" noStyle>
                <Input type="hidden" />
              </Form.Item>
              {/* Time Range */}
              <Row gutter={16} >
                <Col span={10} style={{ marginLeft: 12 }}>
                  <div style={{ display: 'flex', alignItems: 'flex-start', gap: 162 }}>
                    <div style={{ width: 280 }}>
                      <Form.Item
                        label="Time Range"
                        name="time-range-index"
                        rules={[{ required: true, message: 'Required!' }]}
                        validateTrigger="onBlur"
                        noStyle={false}
                      >
                        <Select
                          placeholder="Select a Time Range Profile"
                          value={form.getFieldValue("time-range-index")}
                          style={{ width: 280 }}
                          onChange={(selectedId) => {
                            if (selectedId === "custom") {
                              form.setFieldsValue({ "time-range-index": null });
                              setIsModalVisible(true);
                              return;
                            }
                            const selectedProfile = profiles.find(p => String(p.variable_id) === String(selectedId));
                            if (selectedProfile) {
                              form.setFieldsValue({
                                "time-range-index": String(selectedProfile.variable_id),
                                "time-range-name": selectedProfile.name,
                              });
                            }
                          }}
                          dropdownRender={menu => (
                            <div>
                              {menu}
                              <Button
                                type="link"
                                icon={<PlusOutlined />}
                                onClick={() => setIsModalVisible(true)}
                                style={{ margin: '4px 0px 0px -24px', width: 'calc(100% + 48px)', borderTop: '1px solid #E7E7E7' }}
                              >
                                Create Time Range Profile
                              </Button>
                            </div>
                          )}
                        >
                          {profiles.map((item) => (
                            <Select.Option key={item.id} value={item.variable_id}>
                              {item.name}
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </div>
                    <Button
                      type="link"
                      style={{ padding: 4 }}
                      onClick={() => navigate("/wireless/profile/TimeRange")}
                    >
                      Manage Time Range Profile
                    </Button>
                  </div>
                </Col>
              </Row>
            </>
          )}

          {!pageLoading && radioInitialValues && (
            <SetRadioForm
              ref={formikRef}
              initialValues={radioInitialValues}
              onApply={handleFormikApply}
              countryCode={currentCountry}
              onChange={(latestChild) => {
                const dirty = !isEqual(latestChild, radioInitialValuesRef.current);
                if (dirty) {
                  setChildDirty(true);
                }
              }}
            />
          )}
        </Form>

        <TimeRangeModal
          siteId={siteId}
          visible={isModalVisible}
          dataSource={null}
          onClose={() =>
            setIsModalVisible(false)
          }
          onSuccess={() => {
            setIsModalVisible(false);
            if (siteId != null) {
              getWirelessProfileList(type, siteId, 1, 10000)
                .then(res => {
                  if (res?.status === 200) {
                    const updatedProfiles = res.info || [];
                    setProfiles(updatedProfiles);
                    if (updatedProfiles.length > 0) {
                      const lastProfile = updatedProfiles[updatedProfiles.length - 1];
                      form.setFieldsValue({
                        "time-range-index": String(lastProfile.variable_id),
                        "time-range-name": lastProfile.name,
                      });
                    }
                  }
                })
                .catch(() => {
                  message.error("Failed to load time range profiles");
                });
            }
          }}
        />
      </div>
    </Spin>
  );
});

export default Radios;
