import React, { useEffect, useState } from 'react';
import { Select, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

const { Option } = Select;

const labelWithTooltip = (label: string, tooltipText: string): React.ReactNode => (
  <span>
    {label}
    <Tooltip title={tooltipText}>
      <QuestionCircleOutlined style={{ marginLeft: 4, fontSize: 12, verticalAlign: "text-top" }} />
    </Tooltip>
  </span>
);

const CHANNELS = {
  '2G': [1, 6, 11],
  '5G-lower': {
    20: [36, 40, 44, 48, 52, 56, 60, 64, 100, 104, 108, 112, 116, 120, 124, 128, 132, 136, 140, 144],
    40: [38, 46, 54, 63, 102, 110, 118, 126, 134, 142],
    80: [42, 58, 106, 122, 138],
    160: [50, 114],
  },
  '5G-upper': {
    20: [149, 153, 157, 161, 165],
    40: [151, 159],
    80: [155],
  },
  '5G': {
    40: [36, 44, 52, 60, 100, 108, 116, 124, 132, 149, 157, 165, 173, 184, 192],
    80: [36, 52, 100, 116, 132, 149],
  },
  '6G': {
    20: [1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 65, 69, 73, 77, 81, 85, 89, 93, 97, 101, 105, 109,
      113, 117, 121, 125, 129, 133, 137, 141, 145, 149, 153, 157, 161, 165, 169, 173, 177, 181, 185, 189, 193, 197, 201,
      205, 209, 213, 217, 221, 225, 229, 233],
    40: [3, 11, 19, 27, 35, 43, 51, 59, 67, 75, 83, 91, 99, 107, 115, 123, 131, 139, 147, 155, 163, 171, 177, 187, 195, 203,
      211, 219, 227],
    80: [7, 23, 39, 55, 71, 87, 103, 119, 135, 151, 167, 183, 199, 215],
    160: [15, 47, 79, 143],
  },
};

type BandType = '2G' | '5G' | '5G-lower' | '5G-upper' | '6G';

interface ChannelPickerProps {
  value: string | number;
  onChange: (val: string | number) => void;
  band: BandType;
  channelWidth: number;
  isDisabled?: boolean;
}

const ChannelPicker: React.FC<ChannelPickerProps> = ({ value, onChange, band, channelWidth, isDisabled }) => {
  const [channelOptions, setChannelOptions] = useState<{ value: string | number; label: string }[]>([
    { value: 'auto', label: 'auto' },
  ]);

  useEffect(() => {
    let options: (string | number)[] = ['auto'];

    if (band === '2G') {
      options = [...options, ...CHANNELS['2G']];
    } else if (band === '5G-lower') {
      if (CHANNELS['5G-lower'][channelWidth]) {
        options = [...options, ...CHANNELS['5G-lower'][channelWidth]];
      } else {
        options = [
          ...options,
          ...CHANNELS['5G-lower'][20],
          ...CHANNELS['5G-lower'][40],
          ...CHANNELS['5G-lower'][80],
          ...CHANNELS['5G-lower'][160],
        ];
      }
    } else if (band === '5G-upper') {
      if (CHANNELS['5G-upper'][channelWidth]) {
        options = [...options, ...CHANNELS['5G-upper'][channelWidth]];
      } else {
        options = [...options, ...CHANNELS['5G-upper'][20], ...CHANNELS['5G-upper'][40], ...CHANNELS['5G-upper'][80]];
      }
    } else if (band === '5G') {
      switch (channelWidth) {
        case 20:
          options = [...options, ...CHANNELS['5G-lower'][20], ...CHANNELS['5G-upper'][20]];
          break;
        case 40:
          options = [...options, ...CHANNELS['5G'][40]];
          break;
        case 80:
          options = [...options, ...CHANNELS['5G'][80]];
          break;
        case 160:
          options = [...options, ...CHANNELS['5G-lower'][160]];
          break;
        default:
          options = [
            ...options,
            ...CHANNELS['5G-lower'][20],
            ...CHANNELS['5G-lower'][40],
            ...CHANNELS['5G-lower'][80],
            ...CHANNELS['5G-lower'][160],
            ...CHANNELS['5G-upper'][20],
            ...CHANNELS['5G-upper'][40],
            ...CHANNELS['5G-upper'][80],
          ];
      }
    } else if (band === '6G') {
      if (CHANNELS['6G'][channelWidth]) {
        options = [...options, ...CHANNELS['6G'][channelWidth]];
      } else {
        options = [
          ...options,
          ...CHANNELS['6G'][20],
          ...CHANNELS['6G'][40],
          ...CHANNELS['6G'][80],
          ...CHANNELS['6G'][160],
        ];
      }
    }

    options.sort((a, b) => a.toString().localeCompare(b.toString(), 'en', { numeric: true }));

    // 修正传入的value：如果当前value不在options内，重置为options第一个（一般是'auto'）
    if (value !== 'auto' && !options.includes(Number(value))) {
      onChange(options[0]);
    }

    const finalOptions = options.map((opt) => ({ value: opt, label: `${opt}` }));
    setChannelOptions(finalOptions);
  }, [band, channelWidth, value, onChange]);

  return (
    <Select
      value={value}
      onChange={onChange}
      disabled={isDisabled}
      style={{ width: '80%' }}
      placeholder="Select Channel"
    >
      {channelOptions.map((opt) => (
        <Option key={opt.value} value={opt.value}>
          {opt.label}
        </Option>
      ))}
    </Select>
  );
};

export default React.memo(ChannelPicker);

