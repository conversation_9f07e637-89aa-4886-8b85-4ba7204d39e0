import React ,{useEffect}from 'react';
import { Row, Col} from 'antd';
import SelectField from '@/modules-smb/Wireless/components/FormFields/SelectField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import { useTranslation } from 'react-i18next';
import {SYSTEM_SCHEMA} from './unitConstants';
import { useFormikContext } from 'formik';
import useFastField from '@/modules-smb/hooks/useFastField';

type Props = {
  editing: boolean;
};

const SystemSettingsFields: React.FC<Props> = ({ editing }) => {
  const { t } = useTranslation();
  // const { value: systemEnabled } = useFastField({ name: 'configuration.unit' });
  const { values, setFieldValue, errors } = useFormikContext<any>();
  // console.log('unit values',values);
  // useEffect(() => {
  //    if (systemEnabled) {
  //     const defaultUnitConfig = SYSTEM_SCHEMA(t).cast({});
  //     console.log('defaultUnitConfig',defaultUnitConfig);
  //      setFieldValue('configuration.unit', {
  //    ...defaultUnitConfig,
  //    });
  //    }else {
  //  // 可选：关闭时清除 unit 字段
  //  setFieldValue('configuration.unit', undefined);
  //    }
  //  }, [systemEnabled]);

   const timezoneOptions =[
                      { value: '', label: t('common.none') },
                      {
                        value: 'UTC-11:00',
                        label: 'Midway Islands Time (UTC-11:00)',
                      },
                      {
                        value: 'UTC-10:00',
                        label: 'Hawaii Standard Time (UTC-10:00)',
                      },
                      {
                        value: 'UTC-8:00',
                        label: 'Pacific Standard Time (UTC-8:00)',
                      },
                      {
                        value: 'UTC-7:00',
                        label: 'Mountain Standard Time (UTC-7:00)',
                      },
                      {
                        value: 'UTC-6:00',
                        label: 'Central Standard Time (UTC-6:00)',
                      },
                      {
                        value: 'UTC-5:00',
                        label: 'Eastern Standard Time (UTC-5:00)',
                      },
                      {
                        value: 'UTC-4:00',
                        label: 'Puerto Rico and US Virgin Islands Time (UTC-4:00)',
                      },
                      {
                        value: 'UTC-3:30',
                        label: 'Canada Newfoundland Time (UTC-3:30)',
                      },
                      { value: 'UTC-3:00', label: 'Brazil Eastern Time (UTC-3:00)' },
                      {
                        value: 'UTC-1:00',
                        label: 'Central African Time (UTC-1:00)',
                      },
                      {
                        value: 'UTC',
                        label: 'Universal Coordinated Time (UTC)',
                      },
                      {
                        value: 'UTC+1:00',
                        label: 'European Central Time (UTC+1:00)',
                      },
                      {
                        value: 'UTC+2:00',
                        label: 'Eastern European Time (UTC+2:00)',
                      },
                      {
                        value: 'UTC+2:00',
                        label: '(Arabic) Egypt Standard Time (UTC+2:00)',
                      },
                      {
                        value: 'UTC+3:00',
                        label: 'Eastern African Time (UTC+3:00)',
                      },
                      { value: 'UTC+3:30', label: 'Middle East Time (UTC+3:30)' },
                      { value: 'UTC+4:00', label: 'Near East Time (UTC+4:00)' },
                      {
                        value: 'UTC+5:00',
                        label: 'Pakistan Lahore Time (UTC+5:00)',
                      },
                      { value: 'UTC+5:30', label: 'India Standard Time (UTC+5:30)' },
                      {
                        value: 'UTC+6:00',
                        label: 'Bangladesh Standard Time (UTC+6:00)',
                      },
                      {
                        value: 'UTC+7:00',
                        label: 'Vietnam Standard Time (UTC+7:00)',
                      },
                      { value: 'UTC+8:00', label: 'China Taiwan Time (UTC+8:00)' },
                      { value: 'UTC+9:00', label: 'Japan Standard Time (UTC+9:00)' },
                      {
                        value: 'UTC+9:30',
                        label: 'Australia Central Time (UTC+9:30)',
                      },
                      {
                        value: 'UTC+10:00',
                        label: 'Australia Eastern Time (UTC+10:00)',
                      },
                      {
                        value: 'UTC+11:00',
                        label: 'Solomon Standard Time (UTC+11:00)',
                      },
                      {
                        value: 'UTC+12:00',
                        label: 'New Zealand Standard Time (UTC+12:00)',
                      },
                      { value: 'UTC', label: 'UTC,Canary Islands (UTC)' },
                      { value: 'UTC', label: 'Casablanca (UTC)' },
                      { value: 'UTC', label: 'Monrovia, Reykjavik (UTC)' },
                      { value: 'UTC', label: 'Dublin, Edinburgh, Lisbon, London (UTC)' },
                      { value: 'UTC+01:00', label: 'Western Central Africa (UTC+01:00)' },
                      { value: 'UTC+01:00', label: 'Brussels, Copenhagen, Madrid, Paris (UTC+01:00)' },
                      { value: 'UTC+01:00', label: 'Windhoek (UTC+01:00)' },
                      { value: 'UTC+01:00', label: 'Sarajevo, Skopje, Warsaw, Zagreb (UTC+01:00)' },
                      { value: 'UTC+01:00', label: 'Belgrade, Bratislava, Budapest, Ljubljana, Prague (UTC+01:00)' },
                      { value: 'UTC+01:00', label: 'Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna (UTC+01:00)' },
                      { value: 'UTC+02:00', label: 'Harare, Pretoria (UTC+02:00)' },
                      { value: 'UTC+02:00', label: 'Damascus (UTC+02:00)' },
                      { value: 'UTC+02:00', label: 'Amman (UTC+02:00)' },
                      { value: 'UTC+02:00', label: 'Cairo (UTC+02:00)' },
                      { value: 'UTC+02:00', label: 'Minsk (UTC+02:00)' },
                      { value: 'UTC+02:00', label: 'Jerusalem (UTC+02:00)' },
                      { value: 'UTC+02:00', label: 'Beirut (UTC+02:00)' },
                      { value: 'UTC+02:00', label: 'Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius (UTC+02:00)' },
                      { value: 'UTC+02:00', label: 'Athens, Bucharest (UTC+02:00)' },
                      { value: 'UTC+03:00', label: 'Istanbul (UTC+03:00)' },
                      { value: 'UTC+03:00', label: 'Nairobi (UTC+03:00)' },
                      { value: 'UTC+03:00', label: 'Kaliningrad (UTC+03:00)' },
                      { value: 'UTC+03:00', label: 'Baghdad (UTC+03:00)' },
                      { value: 'UTC+03:00', label: 'Kuwait, Riyadh (UTC+03:00)' },
                      { value: 'UTC+03:00', label: 'Moscow, St. Petersburg (UTC+03:00)' },
                      { value: 'UTC+03:30', label: 'Tehran (UTC+03:30)' },
                      { value: 'UTC+04:00', label: 'Volgograd (UTC+04:00)' },
                      { value: 'UTC+04:00', label: 'Yerevan (UTC+04:00)' },
                      { value: 'UTC+04:00', label: 'Baku (UTC+04:00)' },
                      { value: 'UTC+04:00', label: 'Tbilisi (UTC+04:00)' },
                      { value: 'UTC+04:00', label: 'Port Louis (UTC+04:00)' },
                      { value: 'UTC+04:00', label: 'Abu Dhabi, Muscat (UTC+04:00)' },
                      { value: 'UTC+04:30', label: 'Kabul (UTC+04:30)' },
                      { value: 'UTC+05:00', label: 'Islamabad, Karachi (UTC+05:00)' },
                      { value: 'UTC+05:00', label: 'Tashkent (UTC+05:00)' },
                      { value: 'UTC+05:30', label: 'Chennai, Kolkata, Mumbai, New Delhi (UTC+05:30)' },
                      { value: 'UTC+05:30', label: 'Sri Lanka Standard Time (UTC+05:30)' },
                      { value: 'UTC+05:45', label: 'Kathmandu (UTC+05:45)' },
                      { value: 'UTC+06:00', label: 'Ekaterinburg (UTC+06:00)' },
                      { value: 'UTC+06:00', label: 'Dhaka (UTC+06:00)' },
                      { value: 'UTC+06:00', label: 'Astana (UTC+06:00)' },
                      { value: 'UTC+06:30', label: 'Yangon (UTC+06:30)' },
                      { value: 'UTC+07:00', label: 'Novosibirsk (UTC+07:00)' },
                      { value: 'UTC+07:00', label: 'Bangkok, Hanoi, Jakarta (UTC+07:00)' },
                      { value: 'UTC+08:00', label: 'Ulaanbaatar (UTC+08:00)' },
                      { value: 'UTC+08:00', label: 'Irkutsk (UTC+08:00)' },
                      { value: 'UTC+08:00', label: 'Krasnoyarsk (UTC+08:00)' },
                      { value: 'UTC+08:00', label: 'Beijing, Chongqing, Hong Kong, Urumqi (UTC+08:00)' },
                      { value: 'UTC+08:00', label: 'Taipei (UTC+08:00)' },
                      { value: 'UTC+08:00', label: 'Kuala Lumpur, Singapore (UTC+08:00)' },
                      { value: 'UTC+08:00', label: 'Perth (UTC+08:00)' },
                      { value: 'UTC+09:00', label: 'Osaka, Sapporo, Tokyo (UTC+09:00)' },
                      { value: 'UTC+09:00', label: 'Yakutsk (UTC+09:00)' },
                      { value: 'UTC+09:00', label: 'Seoul (UTC+09:00)' },
                      { value: 'UTC+09:30', label: 'Darwin (UTC+09:30)' },
                      { value: 'UTC+09:30', label: 'Adelaide (UTC+09:30)' },
                      { value: 'UTC+10:00', label: 'Guam, Port Moresby (UTC+10:00)' },
                      { value: 'UTC+10:00', label: 'Canberra, Melbourne, Sydney (UTC+10:00)' },
                      { value: 'UTC+10:00', label: 'Brisbane (UTC+10:00)' },
                      { value: 'UTC+10:00', label: 'Hobart (UTC+10:00)' },
                      { value: 'UTC+11:00', label: 'Solomon Islands, New Caledonia (UTC+11:00)' },
                      { value: 'UTC+11:00', label: 'Vladivostok (UTC+11:00)' },
                      { value: 'UTC+12:00', label: 'UTC+12 (UTC+12:00)' },
                      { value: 'UTC+12:00', label: 'Auckland, Wellington (UTC+12:00)' },
                      { value: 'UTC+12:00', label: 'Fiji (UTC+12:00)' },
                      { value: 'UTC+12:00', label: 'Magadan (UTC+12:00)' },
                      { value: 'UTC+13:00', label: 'Nukualofa (UTC+13:00)' },
                      { value: 'UTC+14:00', label: 'Kiritimati (UTC+14:00)' },
                      { value: 'UTC-01:00', label: 'Azores Islands (UTC-01:00)' },
                      { value: 'UTC-01:00', label: 'Cape Verde Islands (UTC-01:00)' },
                      { value: 'UTC-02:00', label: 'Mid-Atlantic (UTC-02:00)' },
                      { value: 'UTC-02:00', label: 'UTC-02 (UTC-02:00)' },
                      { value: 'UTC-03:00', label: 'Cayenne, Fortaleza (UTC-03:00)' },
                      { value: 'UTC-03:00', label: 'Brasilia (UTC-03:00)' },
                      { value: 'UTC-03:00', label: 'Buenos Aires (UTC-03:00)' },
                      { value: 'UTC-03:00', label: 'Greenland (UTC-03:00)' },
                      { value: 'UTC-03:00', label: 'Montevideo (UTC-03:00)' },
                      { value: 'UTC-03:30', label: 'Newfoundland (UTC-03:30)' },
                      { value: 'UTC-04:00', label: 'Georgetown, La Paz, Manaus, San Juan (UTC-04:00)' },
                      { value: 'UTC-04:00', label: 'Asuncion (UTC-04:00)' },
                      { value: 'UTC-04:00', label: 'Santiago (UTC-04:00)' },
                      { value: 'UTC-04:00', label: 'Atlantic Time (Canada) (UTC-04:00)' },
                      { value: 'UTC-04:00', label: 'Cuiaba (UTC-04:00)' },
                      { value: 'UTC-04:30', label: 'Caracas (UTC-04:30)' },
                      { value: 'UTC-05:00', label: 'Eastern Time (US and Canada) (UTC-05:00)' },
                      { value: 'UTC-05:00', label: 'Indiana (East) (UTC-05:00)' },
                      { value: 'UTC-05:00', label: 'Bogota, Lima, Quito (UTC-05:00)' },
                      { value: 'UTC-06:00', label: 'Central America (UTC-06:00)' },
                      { value: 'UTC-06:00', label: 'Central Time (US and Canada) (UTC-06:00)' },
                      { value: 'UTC-06:00', label: 'Guadalajara, Mexico City, Monterrey (UTC-06:00)' },
                      { value: 'UTC-06:00', label: 'Saskatchewan (UTC-06:00)' },
                      { value: 'UTC-07:00', label: 'Arizona (UTC-07:00)' },
                      { value: 'UTC-07:00', label: 'Chihuahua, La Paz, Mazatlan (UTC-07:00)' },
                      { value: 'UTC-07:00', label: 'Mountain Time (US and Canada) (UTC-07:00)' },
                      { value: 'UTC-08:00', label: 'Baja California (UTC-08:00)' },
                      { value: 'UTC-08:00', label: 'Pacific Time (US and Canada) (UTC-08:00)' },
                      { value: 'UTC-09:00', label: 'Alaska (UTC-09:00)' },
                      { value: 'UTC-10:00', label: 'Hawaii (UTC-10:00)' },
                      { value: 'UTC-11:00', label: 'Samoa (UTC-11:00)' },
                      { value: 'UTC-11:00', label: 'UTC-11 (UTC-11:00)' },
                      { value: 'UTC-12:00', label: 'Dateline Standard Time (UTC-12:00)' },
          ]
          .map((option,index) => ({
  // 为每个选项创建唯一标识符
  value: option.value!=='' ? `${option.value}#${index}`:option.value,
  label: option.label,
}));

  return (
    <Row gutter={[16, 0]}>
      <Col span={12}>
      <ToggleField
        name="configuration.unit.hostname_enable"
        label="Hostname"
        definitionKey="unit.hostname"
        isDisabled={!editing}
      />       
      </Col>
      <Col span={12}>
        <SelectField
        w={280}
        name="configuration.unit.timezone"
        label="Timezone"
        definitionKey="unit.timezone"
        emptyIsUndefined
        isDisabled={!editing}
        options={timezoneOptions}
        />
      </Col>
      <Col span={12}>
         <ToggleField
           name="configuration.unit.leds-active"
           label="Leds-Active"
           definitionKey="unit.leds-active"
           isDisabled={!editing}
           isRequired
         />
      </Col>
      <Col span={12}>
        <ToggleField
          name="configuration.unit.random-password"
          label="Random-Password"
          definitionKey="unit.random-password"
          isDisabled={!editing}
          isRequired
        />
      </Col>
    </Row>
  );
};

export default SystemSettingsFields;
