import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Table, Form, Input, Row, Col } from 'antd';
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';
import { addSvg } from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";

interface User {
  username: string;
  password: string;
}

interface CredentialsUsersFormProps {
  value?: User[];
  onChange?: (users: User[]) => void;
}

const initialUser: User = { username: '', password: '' };

const CredentialsUsersForm: React.FC<CredentialsUsersFormProps> = ({ value = [], onChange }) => {
  const { t } = useTranslation();
  const [users, setUsers] = useState<User[]>(value);
  const [modalOpen, setModalOpen] = useState(false);
  const [form] = Form.useForm();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const handleTableChange = useCallback((pagination: any) => {
    setCurrentPage(pagination.current);
    setPageSize(pagination.pageSize);
  }, []);
  
  const handleAddUser = () => {
    setModalOpen(true);
    form.setFieldsValue(initialUser);
  };

  const handleDeleteUser = (record: User) => {
    const newUsers = users.filter(user => user.username !== record.username || user.password !== record.password);
    setUsers(newUsers);
    onChange && onChange(newUsers);
  };

  const columns = [
    { title: 'Username', dataIndex: 'username', key: 'username' },
    { title: 'Password', dataIndex: 'password', key: 'password' },
    {
      title: 'Operation',
      key: 'action',
      render: (_: any, record: User) => (
        <Button type="text" onClick={() => handleDeleteUser(record)}>
          Delete
        </Button>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Button type="primary" icon={<Icon component={addSvg} />} onClick={handleAddUser}>
          Add Credentials
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={users}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          showTotal: (total) => `Total ${total} items`,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ['5', '10', '20', '50'],
          onChange: handleTableChange,
        }}
        rowKey={(_record, idx) => (typeof idx === 'number' ? idx.toString() : '')}
        size="middle"
        bordered
      />
      <FormModal
        open={modalOpen}
        title="Add Credentials"
        onCancel={() => setModalOpen(false)}
        onFinish={values => {
          const newUsers = [values, ...users];
          setUsers(newUsers);
          onChange && onChange(newUsers);
          setModalOpen(false);
          form.resetFields();
        }}
        initialValues={initialUser}
        form={form}
        modalClass="ampcon-middle-modal"
      >
        <Row gutter={24}>
          <Col span={24}>
            <Form.Item
              label='Username'
              name="username"
              rules={[
                { required: true, message: 'Username is required' },
                {
                  validator: (_, value) => {
                    // 检查用户名格式：只能字母开头，由字母和数字组成
                    const formatRegex = /^[a-zA-Z][a-zA-Z0-9]*$/;
                    if (value && !formatRegex.test(value)) {
                      return Promise.reject(new Error('Username must start with a letter and contain only letters and numbers'));
                    }
                    return Promise.resolve();
                  }
                },
                {
                  validator: (_, value) => {
                    // 检查用户名唯一性
                    if (value && users.some(user => user.username === value)) {
                      return Promise.reject(new Error('Username already exists'));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label='Password'
              name="password"
              rules={[{ required: true, message: t('form.required') }]}
            >
              <Input.Password />
            </Form.Item>
          </Col>
        </Row>
      </FormModal>
    </div>
  );
};

export default CredentialsUsersForm;