import React,{useEffect} from 'react';
import { Row, Col } from 'antd';
import CreatableSelectField from '@/modules-smb/Wireless/components/FormFields/CreatableSelectField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import { useTranslation } from 'react-i18next';
import { useFormikContext } from 'formik';
import {getSubSectionDefaults} from './servicesConstants';

const Ntp = () => {
  const { value: ntpEnabled } = useFastField({ name: 'configuration.services.ntp.enabled' });
  const { value: ntp } = useFastField({ name: 'configuration.services.ntp' });

  const {t}=useTranslation();
      
  const { values, setFieldValue, errors } = useFormikContext<any>();
  useEffect(() => {
     if (ntpEnabled) {
      if(!ntp||(ntp&&Object.keys(ntp).length<=1&&ntp.enabled===true)){
      const defaultLldpConfig = getSubSectionDefaults(t,'ntp');
       setFieldValue('configuration.services.ntp', {
     ...defaultLldpConfig,
     enabled: true,
     });
    }
     }else {
   // 可选：关闭时清除 ntp 字段
   setFieldValue('configuration.services.ntp', undefined);
     }
   }, [ntpEnabled]);
  return( 
    <Row gutter={[20, 0]} style={{ marginBottom: 0, width: '100%' }}>
      <Col>
        <ToggleField
          name="configuration.services.ntp.enabled"
          label="NTP"
        />
        {ntpEnabled&& (
          <>
          <CreatableSelectField
         name="configuration.services.ntp.servers"
         label="Servers"
         definitionKey="service.ntp.servers"
        //  isDisabled={!editing}
         isRequired
         w={280}
       />
       <ToggleField
         name="configuration.services.ntp.local-server"
         label="Local-Server"
         definitionKey="service.ntp.local-server"
        //  isDisabled={!editing}
         isRequired
       />
          </>
        )}
      </Col>
    </Row>
    );

};

export default React.memo(Ntp);
