import React from 'react';
import { Divider } from 'antd';
import Statistics from './Statistics';
import WifiFrames from './WifiFrames';
import WifiScan from './WifiScan';
import DhcpSnooping from './DhcpSnooping';
import Telemetry from './Telemetry';
import Realtime from './Realtime';
import Health from './Health';
const Metrics= () => {

  return (
   <>
   <div style={{display:"flex"}}>
    <Statistics/>
    <WifiFrames/>
   </div>
   <Divider style={{marginTop: 0}}/>
   <div style={{display:"flex"}}>
    <WifiScan/>
    <DhcpSnooping/>
   </div>
   <Divider style={{marginTop: 0}}/>
   <div style={{display:"flex"}}>
    <Telemetry/>
    <Realtime/>
   </div>
   <Divider style={{marginTop: 0}}/>
   <div style={{display:"flex"}}>
    <Health/>
   </div>
   </>
  );
};

export default Metrics;
