import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { addSvg, showListSvg } from "@/utils/common/iconSvg";
import { Button, Space, message, Flex } from "antd";
import Icon from '@ant-design/icons';
import EthernetCreateModal from "./EthernetCreateModal";
import {
    WirelessCustomTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown,
    GlobalSearchInput
} from "@/modules-ampcon/components/custom_table";
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import { values } from 'lodash';
import { getEthernetPortList, deleteEthernetPort } from "@/modules-smb/Wireless/apis/wireless_ethernet_ports";

interface Props {
    siteId?: number;
}

interface WirelessCustomTableRef {
    refreshTable: () => void;
}

const Ethernet: React.FC<Props> = ({ siteId }) => {
    // url中获取siteId
    if (window.location.hash) {
        const hash = window.location.hash.replace('#', '');
        if (/^\d+$/.test(hash)) {
            siteId = parseInt(hash, 10);
        }
    }
    
    const { t } = useTranslation();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const ethernetTableRef = useRef<WirelessCustomTableRef>(null);
    const [isShowSpin, setIsShowSpin] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [pageInfo, setPageInfo] = useState({ index: 0, limit: 10 });
    const [sortInfo, setSortInfo] = useState([]);
    const [data, setData] = useState([]);
    const [total, setTotal] = useState(0);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        setPageInfo({ index: 0, limit: pageInfo.limit });
        
    }, [siteId]);
    useEffect(() => {
        
        if (siteId !== undefined && siteId !== null) {
            // setPageInfo({ index: 0, limit: pageInfo.limit });
            fetchEthernetPortList();
        }
    }, [siteId, pageInfo, sortInfo]);


    const fetchEthernetPortList = async () => {
        setLoading(true);
        try {
            const sortBy = sortInfo?.[0]?.id;
            const sortType = sortInfo?.[0]?.sort;

            const res = await getEthernetPortList({
                siteId,
                pageNum: pageInfo.index + 1,
                pageSize: pageInfo.limit,
                sortBy,
                sortType
            });

            if (res.status === 200) {
                setData(res.info);
                setTotal(res.total);
            } else {
                message.error(res.info || "Failed to fetch Ethernet port list");
                setData([]);
                setTotal(0);
            }
        } catch (error) {
            message.error("Error fetching Ethernet port list");
            setData([]);
            setTotal(0);
        } finally {
            setLoading(false);
        }
    };

    const refreshTable = () => {
        fetchEthernetPortList();
    };

    useEffect(() => {
        if (ethernetTableRef.current) {
            ethernetTableRef.current.refreshTable = refreshTable;
        }
    }, [refreshTable]);

    const handleTableChange = (pagination, filters, sorter) => {
        setPageInfo({
            index: pagination.current - 1,
            limit: pagination.pageSize,
        });
        
        if (sorter.field) {
            setSortInfo([{
                id: sorter.field,
                sort: sorter.order === 'ascend' ? 'asc' : 'desc'
            }]);
        } else {
            setSortInfo([]);
        }
    };

    const rowSelection = {
        selectedRowKeys,
        selectedRows,
        onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRows(rows);
        },
    };

    const tableCols = [
        {
            key: 'port',
            title: "Ports",
            dataIndex: "port",
            sorter: true,
            render: (value) => JSON.parse(value).join(","),
            width: "15%"
        },
        {
            key: 'mac',
            title: "Mac Address",
            dataIndex: "mac",
            render: (value) => value == "" ? "--" : value,
            width: "15%"
        },
        {
            key: 'vlan_or_dhcp_name',
            title: "VLAN/DHCP Service",
            dataIndex: "vlan_or_dhcp_name",
            render: (value) => value == null ? "-" : value,
            width: "15%"
        },
        // {
        //     key: 'multicast',
        //     title: "Multicast",
        //     dataIndex: "multicast",
        //     render: (value) => value ? "Yes" : "No",
        //     width: "15%"
        // },
        // {
        //     key: 'learning',
        //     title: "Learning",
        //     dataIndex: "learning",
        //     render: (value) => value ? "Yes" : "No",
        //     width: "15%"
        // },
        // {
        //     key: 'reverse_path',
        //     title: "Reverse Path",
        //     dataIndex: "reverse_path",
        //     render: (value) => value ? "Yes" : "No",
        //     width: "15%"
        // },
        {
            key: 'vlan_tag',
            title: "Vlan Tag",
            dataIndex: "vlan_tag",
            render: (value) => {
                const vlanTagMap = {
                    1: 'auto',
                    2: 'tagged',
                    3: 'un-tagged'
                };
                return vlanTagMap[value] || 'unkown';
            },
            width: "15%"
        },
        {
            key: 'operation',
            title: "Operation",
            render: (_, record) => {
                return (
                    <Flex
                        style={{ flexWrap: "wrap", columnGap: "24px", rowGap: "5px" }}
                        className="actionLink"
                    >
                        <a
                            onClick={() => {
                                confirmModalAction(
                                    `This action will delete ethernet: ${record.port}, Do you want to continue?`,
                                    () => {
                                        setIsShowSpin(true);
                                        try {
                                            deleteEthernetPort({ id: record.id }).then(res => {
                                                if (res.status !== 200) {
                                                    message.error(res.info);
                                                } else {
                                                    message.success(res.info);
                                                    refreshTable();
                                                }
                                            });
                                        } catch (e) {
                                            message.error(
                                                "An error occurred during the process of delete"
                                            );
                                        } finally {
                                            setIsShowSpin(false);
                                        }
                                    }
                                );
                            }}
                        >
                            Delete
                        </a>
                    </Flex>
                );
            },
            width: "15%"
        }
    ];

    return (
        <>
            <EthernetCreateModal
                title={"Create"}
                onText="Apply"
                isModalOpen={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                modalClass="ampcon-middle-modal"
                siteId={siteId}
                onSuccess={refreshTable}
            />
            <Space size={16} style={{ marginTop: "8px" }}>
                <Button type="primary" block onClick={() => setIsModalOpen(true)} style={{ marginBottom: "4px" }}>
                    <Icon component={addSvg} />
                    Create
                </Button>
            </Space>
            <Flex vertical style={{ position: 'relative', width: '100%',marginBottom: data && data.length > 0?'8px':'24px' }}>
                <WirelessCustomTable
                    ref={ethernetTableRef}
                    columns={tableCols}
                    dataSource={data}
                    loading={loading}
                    onChange={handleTableChange}
                    // rowSelection={rowSelection}
                    disableInternalRowSelection
                    pagination={{
                        current: pageInfo.index + 1,
                        pageSize: pageInfo.limit,
                        total: total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: total => `Total ${total} items`,
                        pageSizeOptions: ["10", "20", "50", "100"],
                    }}
                />
            </Flex>
        </>
    );
};

export default Ethernet;