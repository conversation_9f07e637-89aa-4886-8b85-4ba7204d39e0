import React,{useEffect,useState} from 'react';
import { Row, Col } from 'antd';
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import { useFormikContext } from 'formik';
import {getSubSectionDefaults} from './servicesConstants';
import { useTranslation } from 'react-i18next';
const Http = () => {
  const { value: httpEnabled } = useFastField({ name: 'configuration.services.http.enabled' });
  const { value: http } = useFastField({ name: 'configuration.services.http' });
    const {t}=useTranslation();
    
    const { values, setFieldValue, errors } = useFormikContext<any>();
    useEffect(() => {
       if (httpEnabled) {
        if(!http||(http&&Object.keys(http).length<=1&&http.enabled===true)){
        const defaultHttpConfig = getSubSectionDefaults(t,'http');
         setFieldValue('configuration.services.http', {
       ...defaultHttpConfig,
      //  enabled: true,
       });
       }
       }else {
     // 可选：关闭时清除 http 字段
     setFieldValue('configuration.services.http', undefined);
       }
     }, [httpEnabled]);
  return(
    <>
    <Row gutter={[20, 0]} style={{ marginBottom: 0, width: '100%' }}>
      <Col >
        <ToggleField
          name="configuration.services.http.enabled"
          label="HTTP"
        /> 
        {httpEnabled && (
          <NumberField
          name="configuration.services.http.http-port"
          label="HTTP-Port"
          definitionKey="service.http.http-port"
          // isDisabled={!editing}
          isRequired
          w={140}
        />
        )} 
      </Col>
      </Row>
    </>
  );
 
   
};

export default React.memo(Http);
