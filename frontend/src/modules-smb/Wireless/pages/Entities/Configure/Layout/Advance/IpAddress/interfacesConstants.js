import {object, number, string, array, bool} from "yup";
import { testUcMac } from "@/modules-smb/constants/formTests";
import IpAddress from ".";

export const INTERFACE_IP_ADDRESS_SCHEMA = (t, useDefault = false, role = '') =>
  object().shape({
     configuration: object().shape({
    "IP address": IP_ADDRESS_SCHEMA(t,useDefault),
     })
  });
export const IP_ADDRESS_SCHEMA = (t, useDefault = false, role = '') => {
  const shape = object().shape({
    ipv4:object().shape({
      addressing: string()})
                      .default({addressing: "dynamic"}),
    "ipv6": object().shape({
      enabled: bool().default(false),
      addressing: string().default("dynamic")}),
    "vlan":object().shape({
          enabled: bool().default(false),
          id: number().required(t("form.required")).moreThan(0).lessThan(4050).default(1080)
    }).nullable()
      });

  return useDefault
    ? shape
    : shape.nullable().default({vlan: false});
};
