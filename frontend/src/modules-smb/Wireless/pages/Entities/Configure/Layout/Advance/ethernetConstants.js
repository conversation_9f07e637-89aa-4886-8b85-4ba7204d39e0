import {object, number, string, array, bool} from "yup";
import { testUcMac } from "@/modules-smb/constants/formTests";
export const INTERFACE_ETHERNET_SCHEMA = (t, useDefault = false, role = '') => {
  const defaultSelectPorts = role === 'upstream' ? ['WAN*'] : [];

  const shape = object().shape({
    "select-ports": array()
      .of(string())
      .min(1, t("form.required"))
      .default(defaultSelectPorts),
    multicast: bool().default(true),
    learning: bool().default(true),
    isolate: bool().default(false),
    macaddr: string()
      .test("interface.ethernet.mac.length", t("form.invalid_mac_uc"), v =>
        v === undefined ? true : testUcMac(v)
      )
      .default(undefined),
    "reverse-path": bool().default(false),
    "vlan-tag": string().default("auto"),
    "vlan": bool().default(false),
    "vlan_id": number()
      .nullable()
      .when("vlan", {
        is: true,
        then: (schema) => schema.required(t("form.required")).moreThan(0).lessThan(4050).default(1080),
        otherwise: (schema) => schema.notRequired().nullable()
      }),
      });

  return useDefault
    ? shape
    : shape.nullable().default({
       "select-ports": defaultSelectPorts,
        multicast: true,
        learning: true,
        isolate: false,
        macaddr: undefined,
        "reverse-path": false,
        "vlan-tag": "auto",
        "ap-assign-ip":false,
        "vlan":false,
        "vlan_id": null,
      });
};
