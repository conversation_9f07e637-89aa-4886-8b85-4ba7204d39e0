/* SetRadioForm.tsx表单样式 */
.SetRadioForm .ant-collapse-item {
  border: none !important;
  background: #F8FAFB;
}

.SetRadioForm .ant-collapse-header {
  padding-top: 24px !important;
  padding-bottom: 24px !important;
  height: 72px !important;
}

.SetRadioForm .ant-collapse-content {
  border-top: none !important;
}

.SetRadioForm .ant-collapse-content-box {
  padding: 24px 16px 0 24px !important;
}

/* selector禁用，置灰 */
.SetRadioForm .ant-select-disabled .ant-select-selector {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* radio单选框禁用，文字置灰 */
.SetRadioForm .ant-radio-wrapper-disabled,
.SetRadioForm .ant-radio-wrapper-disabled span,
.SetRadioForm .ant-radio-wrapper-disabled.ant-radio-wrapper-checked,
.SetRadioForm .ant-radio-wrapper-disabled.ant-radio-wrapper-checked span,
.SetRadioForm .ant-radio-wrapper-disabled input[type="radio"]:checked+span {
  color: rgba(0, 0, 0, 0.25) !important;
}

.SetRadioForm .ant-radio-disabled,
.SetRadioForm .ant-radio-disabled * {
  color: rgba(0, 0, 0, 0.25) !important;
}

.SetRadioForm .ant-radio-disabled.ant-radio-checked,
.SetRadioForm .ant-radio-disabled.ant-radio-checked * {
  color: rgba(0, 0, 0, 0.25) !important;
}

/* radio单选框禁用，圆圈置灰 */
.SetRadioForm .ant-radio-disabled .ant-radio-inner {
  border-color: #DADCE1 !important;
  background-color: #F4F5F7 !important;
}

.SetRadioForm .ant-radio-disabled.ant-radio-checked .ant-radio-inner {
  border-color: #DADCE1 !important;
  background-color: #F4F5F7 !important;
}

.SetRadioForm .ant-radio-disabled.ant-radio-checked .ant-radio-inner::after {
  background-color: #B3BBCB !important;
  transform: scale(0.375) !important;
}

.inline-error-fixed .ant-form-item-explain {
  position: absolute;
  bottom: -18px;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 0;
}

.inline-error-fixed .ant-form-item-explain-error {
  font-size: 14px;
  line-height: 1.4;
  white-space: nowrap;
  transition: none !important;
  animation: none !important;
}