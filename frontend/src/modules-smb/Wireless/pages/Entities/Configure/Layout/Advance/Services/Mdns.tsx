import React from 'react';
import { Row, Col } from 'antd';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';


const Mdns = () => (
  <Row gutter={[20, 0]} style={{ marginBottom: 0, width: '100%' }}>
    <Col>
      <ToggleField
        name="configuration.services.mdns.enable"
        definitionKey="service.mdns.enable"
        label="MDNS"
      />
    </Col>
  </Row>
);

export default React.memo(Mdns);
