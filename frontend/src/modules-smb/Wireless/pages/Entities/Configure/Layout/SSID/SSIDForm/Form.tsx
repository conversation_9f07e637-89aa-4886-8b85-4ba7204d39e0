import React, { useState } from 'react';
import { Form, Input, InputNumber, Select, Row, Col, Checkbox, Switch, message, Button, Collapse, Divider } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';
import { useTranslation } from 'react-i18next';
import { LabelTip } from '@/modules-smb/Wireless/components/LabelTip';
import { DEFAULT_SSID_SCHEMA, ENCRYPTION_OPTIONS } from './interfacesConstants';
import AuthenticationForm from './AuthenticationForm';
import ProfileSwitchSelect from '@/modules-smb/Wireless/components/ProfileSwitchSelect';
import AdvancedSettingsForm from './AdvancedSettingsForm';
import { createWirelessConfigure, updateWirelessConfigure } from "@/modules-smb/Wireless/apis/configure_api";
import { setProfileVariable, getProfileVariable } from "@/modules-smb/Wireless/utils/util";
import { fetchLables } from '@/modules-smb/Wireless/apis/lable';
import { getDhcpServiceList } from '@/modules-smb/Wireless/apis/dhcp_service_api';
import '@/modules-smb/Wireless/assets/form.scss';
import NetworkForm from '@/modules-smb/Wireless/pages/Entities/Configure/Layout/Advance/DhcpService/DhcpForm/Form';
import { AmpConCustomModal } from '@/modules-ampcon/components/custom_table';
import { filterNullValues } from '@/modules-smb/Wireless/utils/util';

// DHCP Service列表获取方法
const fetchDhcpOptions = async (siteId: number): Promise<{ label: string; value: string }[]> => {
  try {
    const res = await getDhcpServiceList({ siteId, pageNum: 1, pageSize: 100 });
    if (res?.status === 200 && Array.isArray(res.info)) {
      return res.info.map((item: any) => ({ label: item.name, value: item.name }));
    }
    return [];
  } catch {
    return [];
  }
};

interface Props {
  isDisabled?: boolean;
  resource?: any;
  onClose?: () => void;
  refresh?: () => void;
  siteId?: number;
  modalOpen: boolean;
}

const SSIDForm: React.FC<Props> = ({ isDisabled = false, resource, onClose, refresh, siteId, modalOpen }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // VLAN/DHCP互斥开关
  const [vlanEnabled, setVlanEnabled] = useState(resource?.network_type === 2 ? true: false);
  const [dhcpEnabled, setDhcpEnabled] = useState(resource?.network_type === 3 ? true: false);
  const [dhcpOptions, setDhcpOptions] = useState<{ label: string; value: string }[]>([]);
  const [dhcpLoading, setDhcpLoading] = useState(false);
  const [dhcpModalOpen, setDhcpModalOpen] = useState(false);

  const handleDhcpModalClose = async (success?: boolean) => {
    setDhcpModalOpen(false);
    setDhcpLoading(true);
    const options = await fetchDhcpOptions(siteId!);
    setDhcpOptions(options);
    setDhcpLoading(false);
    // 新增成功选中最后一个
    if (success && options && options.length > 0) {
      const lastDhcp = options[options.length - 1];
      form.setFieldValue(['dhcp_name'], lastDhcp?.value);
    }
  };

  const defaultValues = DEFAULT_SSID_SCHEMA(t, true).cast();
  let initialValues
  // 处理编辑参数
  let ssidConfigure: any = {}
  if (resource && resource.ssid_configure) {
    ssidConfigure = { ...resource.ssid_configure }
    ssidConfigure.labels_name = resource.labels_name
    if(ssidConfigure.radius) {
      ssidConfigure.radius = getProfileVariable(ssidConfigure.radius)
    }
    if(ssidConfigure['time-range-index']) {
      ssidConfigure['time-range-index'] = getProfileVariable(ssidConfigure['time-range-index'])
    }
    if(ssidConfigure['multi-psk']) {
      ssidConfigure['multi-psk'] = getProfileVariable(ssidConfigure['multi-psk'])
    }
    if(ssidConfigure.captive?.['web-root']) {
      ssidConfigure.captive['web-root'] = getProfileVariable(ssidConfigure.captive['web-root'])
    }
    // 处理 VLAN/DHCP
    if (resource.network_type === 2) {
      ssidConfigure.vlan = parseInt(resource.vlan_or_dhcp_name);
    } else if (resource.network_type === 3) {
      ssidConfigure.dhcp_name = resource.vlan_or_dhcp_name;
    }
    initialValues = ssidConfigure
  } else {
    initialValues = defaultValues
  }

  const [scheduleEnabled, setScheduleEnabled] = useState(!!initialValues?.['time-range-index']);

  // AP Label 相关
  const [labelOptions, setLabelOptions] = useState<{ label: string; value: string }[]>([]);
  React.useEffect(() => {
    fetchLables({ siteId }).then(res => {
      if (res?.status === 200 && Array.isArray(res.info)) {
        setLabelOptions(res.info.map((g: any) => ({ label: g.name, value: g.name })));
      } else {
        setLabelOptions([]);
      }
    }).catch(() => setLabelOptions([]));
  }, [siteId]);
  
  const handleFinish = (values: any) => {
    // 先过滤掉所有null值
    const submitValues = filterNullValues(values);
    delete submitValues.labels_name;
    delete submitValues.network;
    const name = submitValues.name;
    const security = ENCRYPTION_OPTIONS.find(item => item.value === submitValues.encryption.proto)?.label;
    // 处理 VLAN/DHCP
    let vlan_or_dhcp_name = '';
    let network_type = 1;
    if (vlanEnabled) {
      vlan_or_dhcp_name = submitValues.vlan;
      network_type = 2;
      delete submitValues.vlan;
    }
    if (dhcpEnabled) {
      vlan_or_dhcp_name = submitValues.dhcp_name;
      network_type = 3;
      delete submitValues.dhcp_name;
    }
    if(submitValues.radius) {
      submitValues.radius = setProfileVariable(submitValues.radius)
    }
    if(submitValues['time-range-index']) {
      submitValues['time-range-index'] = setProfileVariable(submitValues['time-range-index'])
    }
    if(!submitValues['time-range-name']) {
      delete submitValues['time-range-name'];
    }
    if(submitValues['multi-psk']) {
      submitValues['multi-psk'] = setProfileVariable(submitValues['multi-psk'])
    }
    if(submitValues.captive?.['web-root']) {
      submitValues.captive['web-root'] = setProfileVariable(submitValues.captive['web-root'])
    }
    const radio = (submitValues['wifi-bands'] || []).join(',');
    const ssid_configure = JSON.stringify(submitValues)
    if (resource && resource.id) {
      updateWirelessConfigure({
        id: resource.id,
        name: name,
        security: security,
        radio: radio,
        vlan_or_dhcp_name: vlan_or_dhcp_name,
        network_type: network_type,
        ssid_configure: ssid_configure,
        labels_name: values.labels_name || [],
      })
      .then(res => {
        if (res?.status !== 200) {
          message.error(res?.info);
          return;
        }
        message.success(res.info)
        refresh && refresh();
        onClose && onClose();
      })
      .catch(() => message.error('Failed to create Configure'))
    } else {
      createWirelessConfigure({
        site_id: siteId,
        name: name,
        security: security,
        radio: radio,
        vlan_or_dhcp_name: vlan_or_dhcp_name,
        network_type: network_type,
        ssid_configure: ssid_configure,
        labels_name: values.labels_name || [],
      })
      .then(res => {
        if (res?.status !== 200) {
          message.error(res?.info);
          return;
        }
        message.success(res.info)
        refresh && refresh();
        onClose && onClose();
      })
      .catch(() => message.error('Failed to create Configure'))
    }
  };

  // dhcp服务列表获取
  React.useEffect(() => {
    if (dhcpEnabled) {
      setDhcpLoading(true);
      fetchDhcpOptions(siteId!).then(options => {
        setDhcpOptions(options);
        setDhcpLoading(false);
      });
    }
  }, [dhcpEnabled, siteId]);

  return (
    <FormModal
      open={modalOpen}
      title={resource ? 'Edit SSID' : 'Create New SSID'}
      onCancel={onClose}
      onFinish={handleFinish}
      initialValues={initialValues}
      form={form}
    >
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="name"
            label="SSID"
            tooltip={LabelTip('interface.ssid.name')}
            rules={[
              { required: true, message: t('form.min_max_string', { min: 1, max: 32 }) },
              { min: 1, max: 32, message: t('form.min_max_string', { min: 1, max: 32 }) },
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col span={12} style={{ display: 'none' }}>
          <Form.Item
            name="bss-mode"
            label="Bss Mode"
            tooltip={LabelTip('interface.ssid.bss-mode')}
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Select placeholder="bss-mode">
              <Select.Option value="ap">ap</Select.Option>
              <Select.Option value="sta">sta</Select.Option>
              <Select.Option value="mesh">mesh</Select.Option>
              <Select.Option value="wds-ap">wds-ap</Select.Option>
              <Select.Option value="wds-sta">wds-sta</Select.Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="wifi-bands"
            label="Wi-Fi Bands"
            tooltip={LabelTip('interface.ssid.wifi-bands')}
            rules={[
              { required: true, message: t('form.required') }
            ]}
          >
            {/* <Select mode="multiple" placeholder="wifi-bands">
              <Select.Option value="2G">2G</Select.Option>
              <Select.Option value="5G">5G</Select.Option>
              <Select.Option value="6G">6G</Select.Option>
            </Select> */}
            <Checkbox.Group options={['2G', '5G', '6G']} value={['2G', '5G', '6G']} />
          </Form.Item>
        </Col>
      </Row>
      {/* Schedule 功能 */}
      <Row gutter={24}>
        <Col span={24}>
          <ProfileSwitchSelect
            label="Schedule Switch-on"
            title="Time Range"
            formName={["time-range-index"]}
            enableName={["ssid-schedule-enable"]}
            selectName={["time-range-name"]}
            switchEnabled={scheduleEnabled}
            onSwitchChange={setScheduleEnabled}
            type={4}
            siteId={siteId}
            edit={ssidConfigure?.["time-range-index"]}
          />
        </Col>
      </Row>
      {/* AP label 功能 */}
      <Row gutter={24}>
        <Col span={24}>
          <Form.Item
            name="labels_name"
            label="AP Label"
          >
            <Select
              mode="multiple"
              options={labelOptions}
              allowClear
            />
          </Form.Item>
        </Col>
      </Row>
      {/* VLAN 与 AP Assign IP 功能 */}
      {!dhcpEnabled && ( 
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item label="VLAN">
            <Switch
              checked={vlanEnabled}
              onChange={checked => {
                setVlanEnabled(checked);
                if (checked) setDhcpEnabled(false);
              }}
              style={{ marginRight: 8 }}
            />            
          </Form.Item>
        </Col>
      </Row>
      )}
      {vlanEnabled && ( 
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="vlan"
              label=" "
              required={false}
              rules={[
                { required: true, message: t('form.required') },
                { type: 'number', max: 4094, message: 'vlan must be less than 4095' },
                { type: 'number', min: 0, message: 'vlan must be greater than -1' }
              ]}
            >
              <InputNumber />
            </Form.Item>
          </Col>
        </Row>
      )}
      {!vlanEnabled && ( 
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item label="AP Assign IP" tooltip={LabelTip('The AP acts as a DHCP server to assign IP addresses to clients')}>
            <Switch
              checked={dhcpEnabled}
              onChange={checked => {
                setDhcpEnabled(checked);
                if (checked) setVlanEnabled(false);
              }}
              style={{ marginRight: 8 }}
            />
          </Form.Item>
        </Col>
      </Row>
      )}
      {dhcpEnabled && (
        <>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="dhcp_name"
                label="DHCP Service Name"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Select
                  options={dhcpOptions}
                  loading={dhcpLoading}
                  dropdownRender={menu => (
                    <>
                      {menu}
                      <Button
                        type="link"
                        icon={<PlusOutlined />}
                        style={{ width: '100%', borderTop: '1px solid #E7E7E7'}}
                        onClick={() => setDhcpModalOpen(true)}
                      >
                        Create New DHCP Service
                      </Button>
                    </>
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
          {/* 新建 DHCP Service 弹窗 */}
          {dhcpModalOpen && (
            <AmpConCustomModal
              isModalOpen={dhcpModalOpen}
              title={'Create DHCP Service'}
              onCancel={() => setDhcpModalOpen(false)}
              modalClass = "ampcon-max-modal"
              childItems={
                <NetworkForm
                  resource={undefined}
                  onClose={handleDhcpModalClose}
                  siteId={siteId}
                />
              }
            />
          )}
        </>
      )}
      {/* Authentication 模块 */}
      <h3 className='header2'>Authentication</h3>
      <AuthenticationForm
        resource={ssidConfigure}
        siteId={siteId}
      />
      {/* Advanced Settings 收缩面板 */}
      <div style={{ borderTop: '1px solid #eee', marginTop: 22 }}></div>
      <Collapse
        bordered={false}
        defaultActiveKey={[]}
        expandIconPosition="end"
        style={{ background: '#FFFFFF', marginBottom: 20 }}
      >
        <Collapse.Panel
          header={<h3 style={{ fontSize: 16, margin: 0 }}>Advanced Settings</h3>}
          key="advanced"
          forceRender
        >
          <AdvancedSettingsForm resource={ssidConfigure} siteId={siteId} />
        </Collapse.Panel>
      </Collapse>
    </FormModal>
  );
};

export default SSIDForm;
