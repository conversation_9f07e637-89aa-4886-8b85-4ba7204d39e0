import React, { useState, useMemo, useEffect } from 'react';
import { Form, Input, Switch, Button, Row, Col, message, Select, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import '@/modules-smb/Wireless/assets/wirelessFormCreate.scss';
import TextArea from 'antd/es/input/TextArea';
import { createDhcpService, updateDhcpService } from '@/modules-smb/Wireless/apis/dhcp_service_api';
import { filterNullValues } from '@/modules-smb/Wireless/utils/util';
import { testIpv4, testLeaseTime } from "@/modules-smb/constants/formTests";
import { testStaticIpv4ClassD, testStaticIpv4ClassE } from "@/modules-smb/utils/formatTests";
import IntegerInput from '@/modules-smb/Wireless/components/Input/IntegerInput';
import { LabelTip } from '@/modules-smb/Wireless/components/LabelTip';

interface Props {
  resource?: any; // 编辑模式的已有数据
  onClose: (success?: boolean) => void;
  refresh?: () => void;
  siteId?: number;
}

const NetworkForm: React.FC<Props> = ({ resource, onClose, refresh, siteId }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  // IPv6 相关状态
  const [ipv6Enabled, setIpv6Enabled] = useState(false);
  const [dhcpV6Enabled, setDhcpV6Enabled] = useState(false);

  // 整数范围验证器（支持最小值、最大值和自定义错误信息）
  const validateIntegerRange = (
    min?: number,
    max?: number,
    fieldPath?: string
  ) => (_, value) => {
    if (value === undefined || value === null || value === '' || value === '-') {
      return Promise.resolve();
    }
    const num = Number(value);
    // 校验是否为整数
    if (!Number.isInteger(num)) {
      return Promise.reject(`configuration.${fieldPath} must be an integer`);
    }
    // 校验最小值
    if (min === 1 && num < min) {
      return Promise.reject(`configuration.${fieldPath} must be a positive number`);
    }
    // 通用最小值校验
    if (min !== undefined && min !== 1 && num < min) {
      return Promise.reject(`configuration.${fieldPath} must be greater than ${min}`);
    }
    // 校验最大值
    if (max !== undefined && num > max) {
      return Promise.reject(`configuration.${fieldPath} must be less than ${max}`);
    }
    return Promise.resolve();
  };

  // 缓存 IPv4 默认值
  const defaultIpv4Values = useMemo(() => ({
    subnet: '***********/24',
    gateway: '***********',
    'send-hostname': true,
    'lease-first': 1,
    'lease-count': 128,
    'lease-time': '6h',
  }), []);

  // 缓存 IPv6 默认值
  const defaultIpv6Values = useMemo(() => ({
    'ipv6-subnet': '',
    'ipv6-gateway': '',
    'ipv6-prefix-size': 64,
    'ipv6-dhcp-mode': 'hybrid',
    'ipv6-announce-dns': undefined,
    'ipv6-filter-prefix': '::/0',
  }), []);

  // 编辑模式：解析 resource 数据并设置表单初始值
  useEffect(() => {
    if (resource) {
      const baseValues = {
        name: resource.name || '',
        description: resource.description || '',
        vlan_id: resource.dhcp_configure?.vlan?.id,
        subnet: resource.subnet || '',
      };

      let dhcpConfigObj = {};
      if (resource.dhcp_configure && typeof resource.dhcp_configure === 'string') {
        try {
          dhcpConfigObj = JSON.parse(resource.dhcp_configure);
        } catch (e) {
          message.error('Failed to parse DHCP configure');
          dhcpConfigObj = {};
        }
      } else {
        dhcpConfigObj = resource.dhcp_configure || {};
      }

      const ipv4Config = dhcpConfigObj.ipv4 || {};
      const dhcpV4Config = ipv4Config.dhcp || {};
      const ipv6Config = dhcpConfigObj.ipv6 || {};
      const dhcpV6Config = ipv6Config.dhcpv6 || {};

      // 设置 IPv6 和 DHCPv6 的启用状态
      const hasIpv6 = !!ipv6Config.addressing;
      setIpv6Enabled(hasIpv6);
      // 如果 IPv6 启用但未配置 DHCPv6，则默认开启 DHCPv6
      setDhcpV6Enabled(!!dhcpV6Config.mode);
      // 组装表单初始值
      const initialValues = {
        ...baseValues,
        ipv6: !!ipv6Config.addressing,
        dhcp_v6: !!dhcpV6Config.mode,
        // IPv4 字段
        gateway: ipv4Config.gateway || '',
        'send-hostname': ipv4Config['send-hostname'] || true,
        'lease-first': dhcpV4Config['lease-first'],
        'lease-count': dhcpV4Config['lease-count'],
        'lease-time': dhcpV4Config['lease-time'],

        // IPv6 字段
        'ipv6-subnet': ipv6Config.subnet || '',
        'ipv6-gateway': ipv6Config.gateway || '',
        'ipv6-prefix-size': ipv6Config['prefix-size'] ,

        // DHCPv6 字段
        'ipv6-dhcp-mode': dhcpV6Config.mode || 'hybrid',
        'ipv6-announce-dns': dhcpV6Config['announce-dns'] || undefined,
        'ipv6-filter-prefix': dhcpV6Config['filter-prefix'] || '',
      };
      form.setFieldsValue(initialValues);
    }
  }, [resource, form]);

  const onFinish = async (values) => {
    setSubmitting(true);
    try {
      const {
        name,
        description,
        vlan_id,
        subnet,
        ipv6,
        ...restValues
      } = values;
      let validVlanId: number | null;
      if (!['-', '', undefined, null].includes(vlan_id)) {
         validVlanId = Number(vlan_id);
      } else {
        validVlanId = null;
      }
      const dhcpV4Config = {
        'lease-count': Number(restValues['lease-count']),
        'lease-first': Number(restValues['lease-first']),
        'lease-time': restValues['lease-time'],
      };

      const dhcpConfigure: any = {
        role: "downstream",
        ipv4: {
          addressing: 'static',
          dhcp: dhcpV4Config,
          gateway: restValues.gateway,
          'send-hostname': true,
          subnet: subnet,
        },
        ...(validVlanId !== null && { vlan: { id: validVlanId } }),
        name: name,
      };

      if (ipv6Enabled) {
        const ipv6PrefixSize = restValues['ipv6-prefix-size'];
        const handlePrefixSize = ipv6PrefixSize === "" || ipv6PrefixSize === undefined || ipv6PrefixSize === null 
          ? undefined 
          : Number(ipv6PrefixSize);
          
        dhcpConfigure.ipv6 = {
          addressing: 'static',
          subnet: restValues['ipv6-subnet'],
          gateway: restValues['ipv6-gateway'],
          ...(handlePrefixSize !== undefined && { 'prefix-size': handlePrefixSize })
        };

        if (dhcpV6Enabled) {
          dhcpConfigure.ipv6.dhcpv6 = {
            mode: restValues['ipv6-dhcp-mode'],
            'announce-dns': restValues['ipv6-announce-dns'],
            'filter-prefix': restValues['ipv6-filter-prefix'],
          };
        }
      }


      const requestParams = {
        site_id: siteId,
        name,
        subnet,
        dhcp_configure: filterNullValues(dhcpConfigure),
        description: description || '',
        vlan: validVlanId ?? '-', 
      };

      // 调用接口：新增或编辑
      let res;
      if (resource?.id) {
        res = await updateDhcpService({
          ...requestParams,
          id: resource.id,
        });
      } else {
        res = await createDhcpService(requestParams);
      }

      // 响应处理
      if (res?.status !== 200) {
        message.error(res?.info || t('crud.error_create_obj', { obj: 'DHCP Service' }));
        return;
      }

      message.success(
        resource?.id
          ? t('crud.success_update_obj', { obj: 'DHCP Service' })
          : t('crud.success_create_obj', { obj: 'DHCP Service' })
      );
      refresh && refresh();
      onClose && onClose(true);
    } catch (error) {
      message.error(t('crud.error_create_obj', { obj: 'DHCP Service' }));
    } finally {
      setSubmitting(false);
    }
  };

  // IPv6 开关逻辑
  const handleIpv6Change = (checked: boolean) => {
    setIpv6Enabled(checked);
    if (checked) {
      // 开启 IPv6 时，默认开启 DHCPv6 并设置默认值
      setDhcpV6Enabled(true);
      form.setFieldsValue({
        ipv6: checked,
        dhcp_v6: true,
        'ipv6-subnet': defaultIpv6Values['ipv6-subnet'],
        'ipv6-gateway': defaultIpv6Values['ipv6-gateway'],
        'ipv6-prefix-size': defaultIpv6Values['ipv6-prefix-size'],
        'ipv6-dhcp-mode': defaultIpv6Values['ipv6-dhcp-mode'],
        'ipv6-filter-prefix': defaultIpv6Values['ipv6-filter-prefix'],
      });
    } else {
      // 关闭 IPv6 时，同时关闭 DHCPv6 并清空字段
      setDhcpV6Enabled(false);
      form.setFieldsValue({
        ipv6: checked,
        dhcp_v6: false,
        'ipv6-subnet': undefined,
        'ipv6-gateway': undefined,
        'ipv6-prefix-size': undefined,
        'ipv6-dhcp-mode': undefined,
        'ipv6-announce-dns': undefined,
        'ipv6-filter-prefix': undefined,
      });
    }
  };

  // DHCPv6 开关逻辑
  const handleDhcpV6Change = (checked: boolean) => {
    setDhcpV6Enabled(checked);
    if (!checked) {
      form.setFieldsValue({
        'ipv6-dhcp-mode': undefined,
        'ipv6-announce-dns': undefined,
        'ipv6-filter-prefix': undefined,
      });
    } else {
      form.setFieldsValue({
        'ipv6-dhcp-mode': defaultIpv6Values['ipv6-dhcp-mode'],
        'ipv6-filter-prefix': defaultIpv6Values['ipv6-filter-prefix'],
      });
    }
  };

  return (
    <Form
      form={form}
      name="networkForm"
      onFinish={onFinish}
      initialValues={{
        name: '',
        description: '',
        vlan_id: '',
        ipv6: false,
        ...defaultIpv4Values,
        ...defaultIpv6Values,
      }}
      className="wirelessFormCreate"
    >

      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="name"
            label={t('common.name')}
            rules={[
              { required: true, message: t('form.required') },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve();
                  if (value && new Blob([value]).size > 64) {
                    return Promise.reject('name must be less than 64 characters');
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input disabled={!!resource?.id} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="description"
            label="Description"
            rules={[
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve();
                  if (value && new Blob([value]).size > 128) {
                    return Promise.reject('description must be less than 128 characters');
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <TextArea rows={2} />
          </Form.Item>
        </Col>
      </Row>


      {/* IPv4 配置 */}
      <Row gutter={24}>
        <Col span={24}>
          <h3 className="header2">IPv4</h3>
        </Col>
        <Col span={12}>
          <Form.Item
            name="subnet"
            label="Subnet"
            tooltip={LabelTip('interface.ipv4.subnet')}
            rules={[
              { required: true, message: t('form.required') },
              {
                validator: (_, value) => {
                  if (value === "auto/24") return Promise.resolve();
                  if (!testIpv4(value)) {
                    return Promise.reject(t("form.invalid_ipv4"));
                  }
                  const loopbackRegex = /^127\.0\.0\.0\/8$/;
                  if (loopbackRegex.test(value)) {
                    return Promise.reject(t("form.invalid_static_ipv4_loopback"));
                  }
                  if (!testStaticIpv4ClassD(value)) {
                    return Promise.reject(t("form.invalid_static_ipv4_d"));
                  }
                  if (!testStaticIpv4ClassE(value)) {
                    return Promise.reject(t("form.invalid_static_ipv4_e"));
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="gateway"
            label="Gateway"
            tooltip={LabelTip('interface.ipv4.gateway')}
            rules={[
              { required: true, message: t('form.required') },
              {
                validator: (_, value) => {
                  const loopbackRegex = /^127\.\d+\.\d+\.\d+(\/\d+)?$/;
                  if (loopbackRegex.test(value)) {
                    return Promise.reject(t("form.invalid_static_ipv4_loopback"));
                  }
                  if (!testStaticIpv4ClassD(value)) {
                    return Promise.reject(t("form.invalid_static_ipv4_d"));
                  }
                  return testIpv4(value)
                    ? Promise.resolve()
                    : Promise.reject(t("form.invalid_ipv4"))
                }
              }
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
        {/* <Col span={12}>
          <Form.Item
            name="send-hostname"
            label="send-hostname"
            tooltip={LabelTip('interface.ipv4.send-hostname')}
            valuePropName="checked"
           rules={[{ required: true, message: t('form.required') }]}
          >
            <Switch />
          </Form.Item>
        </Col> */}

        <Col span={24}>
          <h3 className="header2">DHCPv4</h3>
        </Col>
        <Col span={12}>
          <Form.Item
            name="lease-first"
            label="Lease-First"
            tooltip={LabelTip('interface.ipv4.dhcp.lease-first')}
            rules={[
              { required: true, message: t('form.required') },
              { validator: validateIntegerRange(1, undefined, 'ipv4.dhcp.lease-first') }
            ]}
          >
            <IntegerInput />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="lease-count"
            label="Lease-Count"
            tooltip={LabelTip('interface.ipv4.dhcp.lease-count')}
            rules={[
              { required: true, message: t('form.required') },
              { validator: validateIntegerRange(1, undefined, 'ipv4.dhcp.lease-count') }
            ]}
          >
            <IntegerInput />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="lease-time"
            label="Lease-Time"
            tooltip={LabelTip('interface.ipv4.dhcp.lease-time')}
            rules={[
              {
                validator: (_, value) =>
                  testLeaseTime(value)
                    ? Promise.resolve()
                    : Promise.reject(t("form.invalid_lease_time"))
              }
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>

      {/* IPv6 配置 */}
      <Row gutter={24}>
        <Col span={24}>
          <Form.Item
            name="ipv6"
            label="IPv6"
            valuePropName="checked"
          >
            <Switch onChange={handleIpv6Change} />
          </Form.Item>
        </Col>
      </Row>

      {ipv6Enabled && (
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="ipv6-subnet"
              label="Subnet"
              tooltip={LabelTip('interface.ipv6.subnet')}
              rules={[{ required: true, message: t('form.required') }]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="ipv6-gateway"
              label="Gateway"
              tooltip={LabelTip('interface.ipv6.gateway')}
              rules={[{ required: true, message: t('form.required') }]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="ipv6-prefix-size"
              label="Prefix-Size"
              tooltip={LabelTip('interface.ipv6.prefix-size')}
              rules={[
                { validator: validateIntegerRange(0, 64, 'ipv6.prefix-size') }
              ]}
            >
              <IntegerInput />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="dhcp_v6"
              label="DHCPv6"
              valuePropName="checked"
            >
              <Switch onChange={handleDhcpV6Change} checked={dhcpV6Enabled} />
            </Form.Item>
          </Col>

          {dhcpV6Enabled && (
            <>
              <Col span={12}>
                <Form.Item
                  name="ipv6-dhcp-mode"
                  label="Mode"
                  tooltip={LabelTip('interface.ipv6.dhcpv6.mode')}
                >
                  <Select
                    options={[
                      { value: 'hybrid', label: 'hybrid' },
                      { value: 'stateless', label: 'stateless' },
                      { value: 'stateful', label: 'stateful' },
                    ]}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="ipv6-announce-dns"
                  label="Announce-DNS"
                  tooltip={LabelTip('interface.ipv6.dhcpv6.announce-dns')}
                >
                  <Select
                    mode="tags"
                    allowClear
                    style={{ width: '100%' }}
                    tokenSeparators={[',', ' ']} // 支持逗号和空格分隔创建
                    notFoundContent="Type the value you need to create..."
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="ipv6-filter-prefix"
                  label="Filter-Prefix"
                  tooltip={LabelTip('interface.ipv6.dhcpv6.filter-prefix')}
                >
                  <Input />
                </Form.Item>
              </Col>
            </>
          )}
        </Row>
      )}

      <Row gutter={24}>
        <Col span={24}>
          <h3 className="header2">VLAN</h3>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={24}>
          <Form.Item
            name="vlan_id"
            label="VLAN ID"
            rules={[
              { validator: validateIntegerRange(1, 4050, 'vlan.id') }
            ]}
          >
            <IntegerInput />
          </Form.Item>
        </Col>
      </Row>

      <Divider />

      <Row>
        <Col span={24} style={{ textAlign: 'right' }}>
          <Button
            onClick={() => {
              form.resetFields();
              onClose && onClose();
            }}
            disabled={submitting}
            style={{ marginRight: 8 }}
          >
            Cancel
          </Button>
          <Button type="primary" htmlType="submit" disabled={submitting} loading={submitting}>
            Apply
          </Button>
        </Col>
      </Row>
    </Form>
  );
};

export default NetworkForm;
