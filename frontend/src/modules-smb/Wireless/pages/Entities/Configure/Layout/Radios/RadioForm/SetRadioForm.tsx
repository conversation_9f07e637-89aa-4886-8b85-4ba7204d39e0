import React, { forwardRef, useImperativeHandle, useRef, useEffect, useState } from "react";
import { InputNumber, Select, Radio, Col, Row, Collapse, Switch, Form, Checkbox, Input } from "antd";
import { Formik, Form as FormikForm, FormikValues, FormikProps } from "formik";
import _ from "lodash";
import AdvancedSettings from "./AdvancedSettings";
import "@/modules-smb/Wireless/pages/Entities/Configure/Layout/Radios/RadioForm.css";
import { getWirelessChannels } from '@/modules-smb/Wireless/apis/configure_api';
import { LabelTip } from '@/modules-smb/Wireless/components/LabelTip';
import { filterNullValues } from '@/modules-smb/Wireless/utils/util';

const { Option } = Select;
type BandType = "2G" | "5G" | "6G";

const itemLayout = { labelCol: { style: { width: 150 } }, wrapperCol: { style: { width: 300 } } }


//radio通用选项布局
const renderRadioPanel = (
  band: BandType,
  values: FormikValues,
  setFieldValue: (path: string[], val: any) => void,
  channelData: any
): React.ReactNode => {
  const bandKey = `${band.toLowerCase()}_channel`;
  //channel-width
  const widths = Object.keys(channelData?.[bandKey] || {}).filter((key) => key !== "dfs");
  const selectedWidth = String(_.get(values, [band, "channel-width"]));
  const options = widths.map((w) => ({ label: `${w} MHz`, value: String(w) }));
  const channelOptions = selectedWidth ? channelData?.[bandKey]?.[selectedWidth] || [] : [];
  const value = _.get(values, [band, "maximum-clients"]);
  const showError = typeof value === "number" && value < 1;

  // valid-channels
  const validChannelsValue: number[] = _.get(values, [band, "valid-channels"], []);
  const filteredValidChannels = validChannelsValue.filter(ch => channelOptions.includes(ch));
  const isAllSelected = filteredValidChannels.length > 0 && filteredValidChannels.length === channelOptions.length;

  const handleChannelChange = (checkedValues: number[]) => {
    setFieldValue([band, "valid-channels"], checkedValues);
  };

  const handleSelectAll = (e: any) => {
    if (e.target.checked) {
      setFieldValue([band, "valid-channels"], channelOptions);
    } else {
      setFieldValue([band, "valid-channels"], []);
    }
  };

  return (
    <Form layout="horizontal" labelAlign="left">
      <div>
        <Row gutter={64}>
          <Col span={10}>
            <Form.Item label="TX-Power" {...itemLayout}>
              <Radio.Group
                value={typeof _.get(values, [band, 'tx-power']) === 'number' ? 'Set Power' : 'Default'}
                onChange={(e) => {
                  const val = e.target.value;
                  if (val === 'Default') {
                    setFieldValue([band, 'tx-power'], undefined);
                  } else {
                    // 如果之前不是数字，默认给1
                    const currentVal = _.get(values, [band, 'tx-power']);
                    if (typeof currentVal !== 'number') {
                      setFieldValue([band, 'tx-power'], 1);
                    }
                  }
                }}
              >
                <Radio value="Default">Default</Radio>
                <Radio value="Set Power">
                  <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                    Set Power
                    {typeof _.get(values, [band, 'tx-power']) === 'number' && (
                      <InputNumber
                        min={1}
                        max={30}
                        value={_.get(values, [band, 'tx-power'])}
                        onChange={(val) => setFieldValue([band, 'tx-power'], val)}
                        style={{ width: 110, marginLeft: 8 }}
                        addonAfter="dBm"
                      />
                    )}
                  </span>
                </Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={10}>
            <Form.Item
              label="Channel-Width"
              tooltip={LabelTip('radio.channel-width')}
              required
              {...itemLayout}
            >
              <Select
                style={{ width: 280 }}
                value={options.some(opt => opt.value === selectedWidth) ? selectedWidth : 'None'}
                onChange={(val) => {
                  setFieldValue([band, "channel-width"], val);
                  const newChannels = channelData?.[bandKey]?.[val] || [];
                  // channel-width切换全选
                  setFieldValue([band, "valid-channels"], newChannels);
                }}
              >
                {options.map((opt) => (
                  <Option key={opt.value} value={opt.value}>
                    {opt.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        {/* 隐藏字段channel */}
        <Form.Item hidden={true} name={[band, "channel"]}>
          <Input defaultValue="auto" />
        </Form.Item>

        <Row gutter={64}>
          <Col span={24}>
            <Form.Item
              label="Valid-Channels"
              required
              {...itemLayout}
              validateStatus={filteredValidChannels.length === 0 ? "error" : ""}
              help={filteredValidChannels.length === 0 ? "Valid-Channels cannot be empty" : ""}
            >
              <Checkbox
                indeterminate={filteredValidChannels.length > 0 && !isAllSelected}
                checked={isAllSelected}
                onChange={handleSelectAll}
                style={{ marginBottom: 8, marginTop: 5 }}
                disabled={channelOptions.length === 0}
              >
                ALL
              </Checkbox>
              <Checkbox.Group
                value={filteredValidChannels}
                onChange={handleChannelChange}
                style={{ display: 'flex', flexWrap: 'wrap', gap: '8px 12px' }}
              >
                {channelOptions.map(ch => <Checkbox key={ch} value={ch} style={{ width: 60 }}>{ch}</Checkbox>)}
              </Checkbox.Group>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={64}>
          <Col span={10}>
            <Form.Item
              {...itemLayout}
              label="Maximum-Clients"
              tooltip={LabelTip('radio.maximum-clients')}
              validateStatus={showError ? "error" : ""}
              help={showError ? "Maximum-Clients must be a positive number" : ""}
            >
              <InputNumber
                style={{ width: 140 }}
                value={value}
                onChange={(val) => setFieldValue([band, "maximum-clients"], val)}
              />
            </Form.Item>
          </Col>
          {band === "2G" && (
            <Col span={10}>
              <Form.Item
                label="Legacy-Rates"
                tooltip={LabelTip('radio.legacy-rates')}
                {...itemLayout}
              >
                <Switch
                  checked={_.get(values, [band, "legacy-rates"])}
                  onChange={(checked) => {
                    setFieldValue([band, "legacy-rates"], checked ? true : undefined);
                    setFieldValue([band, "rates", "multicast"], 24000);
                    setFieldValue([band, "rates", "beacon"], 6000);
                  }}
                />
              </Form.Item>
            </Col>
          )}
        </Row>

        <AdvancedSettings
          namePrefix={band}
          values={values}
          setFieldValue={setFieldValue}
        />
      </div>
    </Form>
  );
};

export interface SetRadioFormRef {
  submit: () => void;
  resetForm: () => void;
}

interface SetRadioFormProps {
  initialValues: Record<BandType, any>;
  onApply: (values: Partial<Record<BandType, any>>) => void
  countryCode: string; //for get channels
  onChange?: (values: Record<BandType, any>) => void;
}

const SetRadioForm = forwardRef<SetRadioFormRef, SetRadioFormProps>(
  ({ initialValues, onApply, countryCode, onChange }, ref) => {
    const formikRef = useRef<FormikProps<Record<BandType, any>>>(null);
    const [channelData, setChannelData] = useState<any>({});
    const [formValues, setFormValues] = useState(initialValues);
    const [disabledBands, setDisabledBands] = useState<BandType[]>([]);//存储，没有channel-width的band
    // console.log("渲染的值",initialValues)

    //向父组件暴露，提交的formik
    useImperativeHandle(ref, () => ({
      submit: () => {
        const formik = formikRef.current;
        if (!formik) return;
        const filtered = _.omit(formik.values, disabledBands);
        for (const band in filtered) {//过滤字段，删除null/undefined
          filtered[band] = _.omitBy(filtered[band], _.isNil);
          if (_.has(filtered[band], "channel-width")) {
            const width = filtered[band]["channel-width"];
            if (width !== undefined && width !== null && width !== "auto" && width !== "None") {
              filtered[band]["channel-width"] = Number(width);//提交channel-width是数字
            }
          }

        }
        // console.log("提交的数据:", filtered);
        onApply(filtered);
      },
      resetForm: () => {
        const formik = formikRef.current;
        if (!formik) return;
        formik.resetForm({ values: initialValues });
      },
    }));

    useEffect(() => {
      getWirelessChannels(countryCode).then((res) => {
        const result = res || {};
        setChannelData(result);
        const newDisabledBands: BandType[] = [];
        const updatedValues = _.cloneDeep(initialValues);

        // 先根据channel-result，判断并更新禁用的band 
        (["2G", "5G", "6G"] as BandType[]).forEach((band) => {
          const bandKey = `${band.toLowerCase()}_channel`;
          const widthMap = result[bandKey];
          const availableWidths = Object.keys(widthMap || {});
          const currentWidth = _.get(updatedValues, [band, "channel-width"]);

          // 判断是否禁用
          const hasValidWidths = availableWidths.length > 0;
          if (!hasValidWidths) newDisabledBands.push(band);

          const isWidthValid = currentWidth && availableWidths.includes(String(currentWidth));
          const fallbackWidth = band === "2G" ? "20" : "40";
          const finalWidth = isWidthValid ? currentWidth : (availableWidths.includes(fallbackWidth) ? fallbackWidth : availableWidths[0] || "none");
          _.set(updatedValues, [band, "channel-width"], finalWidth);
          _.set(updatedValues, [band, "channel"], "auto");

          const availableChannels = widthMap?.[String(finalWidth)] || [];

          // Valid-Channels
          const initialValid = _.get(initialValues, [band, "valid-channels"], []);
          const filteredValid = initialValid.filter(ch => availableChannels.includes(ch));
          _.set(updatedValues, [band, "valid-channels"], filteredValid.length > 0 ? filteredValid : availableChannels);

          // Maximum-Clients
          const maxClients = _.get(updatedValues, [band, "maximum-clients"]);
          if (maxClients === undefined && hasValidWidths) {
            _.set(updatedValues, [band, "maximum-clients"], 64);
          }
          // multicast、beacon
          (["2G", "5G", "6G"] as BandType[]).forEach((band) => {
            if (newDisabledBands.includes(band) || !_.has(updatedValues, band)) {
              _.set(updatedValues, [band, "rates", "multicast"], 24000);
              _.set(updatedValues, [band, "rates", "beacon"], 6000);
            }
          });
        });
        setDisabledBands(newDisabledBands);
        setFormValues(updatedValues);
      });
    }, [countryCode, initialValues]);

    return (
      <div >
        <Formik
          innerRef={formikRef}
          enableReinitialize
          initialValues={formValues}
          onSubmit={(values) => {
            onApply(values);
          }}
        >
          {({ values, setFieldValue }) => {

            const handleSetFieldValue = (path: string[], val: any) => {
              if (path[path.length - 1] === "channel-width" && val !== "None") {
                val = Number(val);
              }
              const pathStr = path.join('.');
              const updatedValues = _.set(_.cloneDeep(values), pathStr, val);
              const processedValues = filterNullValues(
                _.omitBy(updatedValues, bandValues => typeof bandValues["channel-width"] === "string")
              );
              onChange?.(processedValues);
              setFieldValue(pathStr, val);
            };
            return (
              <FormikForm >
                <Collapse
                  className="SetRadioForm"
                  style={{ marginBottom: 0, border: "none" }}
                  expandIconPosition="right"
                >
                  {!disabledBands.includes("2G") && (
                    <Collapse.Panel key="2g" header={<h3 style={{ fontSize: "16px", margin: 0, border: "none" }}>2G Radio</h3>}>
                      {renderRadioPanel(
                        "2G",
                        values,
                        handleSetFieldValue,
                        channelData)}
                    </Collapse.Panel>
                  )}
                  <div style={{ height: 20, backgroundColor: "#ffff" }} />
                  {!disabledBands.includes("5G") && (
                    <Collapse.Panel key="5g" header={<h3 style={{ fontSize: "16px", margin: 0, border: "none" }}>5G Radio</h3>}>
                      {renderRadioPanel(
                        "5G",
                        values,
                        handleSetFieldValue,
                        channelData)}
                    </Collapse.Panel>
                  )}
                  <div style={{ height: 20, backgroundColor: "#ffff" }} />
                  {!disabledBands.includes("6G") && (
                    <Collapse.Panel key="6g" header={<h3 style={{ fontSize: "16px", margin: 0, border: "none" }}>6G Radio</h3>}>
                      {renderRadioPanel(
                        "6G",
                        values,
                        handleSetFieldValue,
                        channelData)}
                    </Collapse.Panel>
                  )}
                </Collapse>
              </FormikForm>
            );
          }}
        </Formik>
      </div>
    );
  }
);

export default SetRadioForm;