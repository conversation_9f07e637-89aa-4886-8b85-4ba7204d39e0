import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button, Space, Tooltip } from 'antd';
import MacSearchBar from './MacSearchBar';
import ClientLifecyleTable from './Table';
import CustomRangePicker from '@/modules-smb/Wireless/components/CustomWirelessRangePicker';
import { axiosAnalytics } from '@/modules-smb/utils/axiosInstances';
import { getHoursAgo } from '@/modules-smb/utils/dateFormatting';
import { useTranslation } from 'react-i18next';
import dayjs, { Dayjs } from 'dayjs';
import { useLocation } from 'react-router-dom';

const getPartialClients = async (venueId: string, offset: number) =>
  axiosAnalytics
    .get(`wifiClientHistory?macsOnly=true&venue=${venueId}&limit=500&offset=${offset}`)
    .then(({ data }) => data.entries as string[]);

const getAllClients = async (venueId: string) => {
  const allClients: string[] = [];
  let continueFirmware = true;
  let offset = 0;
  while (continueFirmware) {
    const newClients = await getPartialClients(venueId, offset);
    if (!newClients || newClients.length < 500 || offset >= 50000) {
      continueFirmware = false;
    }
    allClients.push(...newClients);
    offset += 500;
  }
  return allClients;
};

interface Props {
  venueId: string;
}

const ClientLifecycleCard = ({ venueId }: Props) => {
  const { t } = useTranslation();
  const location = useLocation();
  const [macs, setMacs] = useState<string[] | undefined>();
  const [mac, setMac] = useState<string | undefined>();
  const [time, setTime] = useState<[Dayjs, Dayjs] | null>([
    dayjs(getHoursAgo(5 * 24)),
    dayjs(),
  ]);
  const clientLifecycleRef = useRef<HTMLDivElement>(null);


  useEffect(() => {
    if (location.state?.targetMac) {
      const cleanMac = location.state.targetMac.replace(/[:\\-]/g, '');
      setMac(cleanMac);
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state?.scrollToClientLifecycle && clientLifecycleRef.current) {
      clientLifecycleRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [location.state]);

  const getMacs = useCallback(async () => {
    try {
      const newMacs = await getAllClients(venueId);
      return newMacs;
    } catch (e) {
      return undefined;
    }
  }, [venueId]);

  useEffect(() => {
    getMacs().then((res) => {
      setMacs(res);
      if (location.state?.targetMac) {
        const cleanMac = location.state.targetMac.replace(/[:\-]/g, '');
        setMac(cleanMac);
      } else if (res && res.length > 0) {
        setMac(res[0]);
      }
      else {
        setMac(undefined);
      }
    });
  }, [getMacs, location.state?.targetMac]);


  const handleTimeChange = (newTime: [Dayjs, Dayjs] | null) => {
    setTime(newTime);
  };
  const fromDate = time ? time[0]?.valueOf() ? Math.floor(time[0].valueOf() / 1000) : 0 : 0;
  const endDate = time ? time[1]?.valueOf() ? Math.floor(time[1].valueOf() / 1000) : 0 : 0;
  return (
    <div ref={clientLifecycleRef}>
      <ClientLifecyleTable
        fromDate={fromDate}  
        endDate={endDate}   
        venueId={venueId}
        mac={mac}
        timePickers={
          <CustomRangePicker
            value={time}  
            onChange={handleTimeChange}
            tooltipText={t('controller.crud.choose_time')}
          />
        }
        searchBar={<MacSearchBar macs={macs} setMac={setMac} value={mac} />}
      />
    </div>
  );
};

export default ClientLifecycleCard;
