import React, { useMemo, useState } from 'react';
import { Card, Radio, Table } from 'antd';
import { useTranslation } from 'react-i18next';
import { useVenueMonitoring } from '../VenueMonitoringContext';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import { AnalyticsBoardDevice } from '@/modules-smb/models/Analytics';
import { Typography } from 'antd';

const bytesToMBString = (bytes: number, decimals = 2) => {
  if (!bytes || bytes === 0) return '0 MB';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const mb = bytes / (k * k);
  return `${parseFloat(mb.toFixed(dm))} MB`;
};

interface DataCellProps {
  bytes?: number;
  showZerosAs?: string;
  style?: React.CSSProperties;
}

const DataCell = ({ bytes, style }: DataCellProps) => {
  const { Text } = Typography;

  const data = useMemo(() => {
    if (bytes === undefined) return '-';
    return bytesToMBString(bytes);
  }, [bytes]);

  return <Text style={style}>{data}</Text>;
};

const BusiestVenueDevicesCard = () => {

  const { t } = useTranslation();
  const [filter, setFilter] = React.useState<'associations' | 'traffic'>('associations');
  const { monitoring } = useVenueMonitoring();

  const columns = React.useMemo(
    () => [
      {
        title: (
          <span style={{ whiteSpace: 'nowrap' }}>
            {t('inventory.serial_number')}
          </span>
        ),
        dataIndex: 'serialNumber',
        key: 'serialNumber',
        sorter: (a, b) => a.serialNumber.localeCompare(b.serialNumber),
        columnsFix:true
      },
      {
        title: 'RX(MB)',
        dataIndex: 'rxBytes',
        key: 'rxBytes',
        render: (value: number) => (

          <DataCell bytes={value} />

        ),

        sorter: (a, b) => a.rxBytes - b.rxBytes,

      },
      {
        title: 'TX(MB)',
        dataIndex: 'txBytes',
        key: 'txBytes',
        render: (value: number) => (
          <DataCell bytes={value} />
        ),
        sorter: (a, b) => a.txBytes - b.txBytes,

      },
      {
        title: '2G',
        dataIndex: 'associations_2g',
        key: 'associations_2g',
        sorter: (a, b) => a.associations_2g - b.associations_2g,

      },
      {
        title: '5G',
        dataIndex: 'associations_5g',
        key: 'associations_5g',
        sorter: (a, b) => a.associations_5g - b.associations_5g,

      },
      {
        title: '6G',
        dataIndex: 'associations_6g',
        key: 'associations_6g',
        sorter: (a, b) => a.associations_6g - b.associations_6g,

      },
      {
        title: t('analytics.health'),
        dataIndex: 'health',
        key: 'health',
        render: (value: number) => `${Math.floor(value * 100) / 100}%`,
        sorter: (a, b) => a.health - b.health,

      },
      {
        title: t('analytics.memory'),
        dataIndex: 'memory',
        key: 'memory',
        render: (value: number) => `${Math.floor(value * 100) / 100}%`,
        sorter: (a, b) => a.memory - b.memory,

      },
    ],
    [t],
  );

  const busiestDevices = React.useMemo(
    () =>
      monitoring?.map(
          (device) =>
          ({
            ...device.dashboardData,
            totalAssociations:
              device.dashboardData.associations_2g +
              device.dashboardData.associations_5g +
              device.dashboardData.associations_6g,
            totalTraffic: device.deltas.rxBytes + device.deltas.txBytes,
            rxBytes: device.deltas.rxBytes,
            txBytes: device.deltas.txBytes,
          } as AnalyticsBoardDevice & {
            totalAssociations: number;
            totalTraffic: number;
            rxBytes: number;
            txBytes: number;
          }),
        )
        .sort((a, b) =>
          filter === 'associations'
            ? b.totalAssociations - a.totalAssociations
            : b.totalTraffic - a.totalTraffic
        )
        .slice(0, 10),
    [monitoring, filter],
  );


  const extraButton = (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      position: 'relative',
      marginTop: '20px',
    }}>
    
      <span style={{
        width: '45px',
        height: '17px',
        fontFamily: 'Lato, sans-serif',
        fontWeight: 500,
        fontSize: '14px',
        color: '#212519',
        lineHeight: '17px',
        marginRight: '56px'
      }}>
        Sort by
      </span>

      <Radio.Group
        value={filter}
        onChange={(e) => setFilter(e.target.value)}
        style={{ display: 'flex', gap: '56px' }}
      >
        <Radio
          value="associations"
          style={{
            width: '43px',
            height: '17px',
            fontWeight: 400,
            lineHeight: '17px',
            margin: 0
          }}
        >
          Clients
        </Radio>
        <Radio
          value="traffic"
          style={{
            width: '41px',
            height: '17px',
            fontWeight: 400,
            lineHeight: '17px',
            margin: 0
          }}
        >
          Traffic
        </Radio>
      </Radio.Group>
    </div>
  );


  return (
     <Card
      title={(
        <div style={{ fontSize: '18px' }}>
          Top 10 Busiest Devices
        </div>
      )}
      style={{
        height: '100%',
        border: 'none',
      }}
       bodyStyle={{
        padding: '16px',
        display: 'flex',
        flexDirection: 'column',
        height: 'calc(100% - 57px)',
        marginBottom: '20px',
      }}
    >
      {extraButton}
    
       <WirelessCustomTable
        columns={columns}
        dataSource={busiestDevices}
        isShowPagination={false}
        showColumnSelector={true}
        style={{
          maxHeight: '245px',
          overflowY: 'auto',
        }}
        bordered={true} 
    
      />
    </Card>
  );
};

export default BusiestVenueDevicesCard;