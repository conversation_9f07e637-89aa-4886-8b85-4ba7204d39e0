import React, { useEffect } from 'react';
import { VenueMonitoringProvider } from './VenueMonitoringContext';
import BusiestVenueDevicesCard from './BusiestDevicesCard';
import ClientLifecycleCard from './ClientLifecycleCard';
import VenueMonitoringTree from './MonitoringTree';
import VenueStatusBar from './StatusBar';
import { useLocation } from "react-router-dom";
import useGetEntityFavorite from "@/modules-smb/Wireless/hooks/useGetEntityFavorite";

type Props = {
  venueId: string;
  venueData?: any; 
};

const VenueMonitoringTab = ({  venueId='0', venueData }: Props) => {
    if (window.location.hash) {
    const hash = window.location.hash.replace('#', '');
    if (/^\d+$/.test(hash)) {
      venueId = parseInt(hash, 10);
    }
  }
  const location = useLocation();

  useEffect(() => {
    if (location.state?.scrollToClientLifecycle) {
      const el = document.getElementById('client-lifecycle-card');
      if (el) {
        el.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [location]);

    return (
    <VenueMonitoringProvider venueId={venueId} venueData={venueData}>
      <div style={containerStyle}>
        <VenueStatusBar />
        
        <div style={cardsGridStyle}>
          <div style={busiestDevicesWrapperStyle}>
            <BusiestVenueDevicesCard />
          </div>
          
          <div style={monitoringTreeWrapperStyle}>
            <VenueMonitoringTree />
          </div>
        </div>

        <div id="client-lifecycle-card" style={fullWidthCardStyle}>
          <ClientLifecycleCard venueId={venueId} />
        </div>
      </div>
    </VenueMonitoringProvider>
  );
};


const GAP = 32; // 卡片之间的间隙
const CONTAINER_PADDING = 16;

const containerStyle: React.CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  gap: '24px',
  width: '100%',
  padding: `${CONTAINER_PADDING}px`,
  boxSizing: 'border-box',
};

const cardsGridStyle: React.CSSProperties = {
  display: 'grid',
  gridTemplateColumns: '1fr 1fr 1fr 1.22fr', 
  gap: `${GAP}px`, 
  width: '100%',
  boxSizing: 'border-box',
};


const busiestDevicesWrapperStyle: React.CSSProperties = {
  gridColumn: '1 / span 3', // 从第1列开始，跨越3列
  minHeight:'360px',
  background: '#FFFFFF',
  border: '1px solid #E7E7E7',
  borderRadius: '8px',
  overflow: 'hidden',


};

const monitoringTreeWrapperStyle: React.CSSProperties = {
  gridColumn: '4', // 仅占第4列
  minHeight:'360px',
  background: '#FFFFFF',
  border: '1px solid #E7E7E7',
  borderRadius: '8px',
  overflow: 'hidden',
};
const fullWidthCardStyle: React.CSSProperties = {
  height: 'auto',
  minHeight: '380px',
  background: '#FFFFFF',
  border: '1px solid #E7E7E7',
  borderRadius: '8px',
  overflow: 'hidden',
  width: '100%',
  boxSizing: 'border-box',
};

export default VenueMonitoringTab;