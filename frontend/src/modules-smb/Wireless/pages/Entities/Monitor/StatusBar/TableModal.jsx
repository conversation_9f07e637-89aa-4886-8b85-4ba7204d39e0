import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Input, Space, Flex, Typography, Divider } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import Icon from "@ant-design/icons";
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import ColumnPicker from '@/modules-smb/components/ColumnPicker';
import { useGetGatewayUi } from '@/modules-smb/hooks/Network/Endpoints';
import { arrayMoveIndex } from '@/modules-smb/utils/arrayHelpers';
import { minimalSecondsToDetailed } from '@/modules-smb/utils/dateFormatting';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import FormattedDate from '@/modules-smb/components/FormattedDate';
import { AmpConCustomModal } from "@/modules-ampcon/components/custom_table";
const { Title, Link } = Typography;
import { searchSvg } from "@/utils/common/iconSvg";

const defaultSorter = (a, b, field) => {
  const valueA = a[field];
  const valueB = b[field];

  // 处理 null/undefined（排在后面）
  if (valueA === null || valueA === undefined) return 1;
  if (valueB === null || valueB === undefined) return -1;
  if (valueA === valueB) return 0;

  // 数字类型比较
  if (typeof valueA === 'number' && typeof valueB === 'number') {
    return valueA - valueB;
  }

  // 布尔类型（true 排在 false 前面）
  if (typeof valueA === 'boolean' && typeof valueB === 'boolean') {
    return (valueB ? 1 : 0) - (valueA ? 1 : 0);
  }

  // 日期类型（转换为时间戳比较）
  if (typeof valueA === 'string' && !isNaN(Date.parse(valueA)) &&
    typeof valueB === 'string' && !isNaN(Date.parse(valueB))) {
    return new Date(valueA).getTime() - new Date(valueB).getTime();
  }

  // 字符串类型（字典序比较，支持国际化）
  return String(valueA).localeCompare(String(valueB), undefined, {
    sensitivity: 'base',
  });
};

const propTypes = {
  data: PropTypes.instanceOf(Object),
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  tableOptions: PropTypes.shape({
    prioritizedColumns: PropTypes.arrayOf(PropTypes.string),
    sortBy: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string,
        desc: PropTypes.bool,
      })
    ),
  }),
};

const defaultProps = {
  data: null,
  tableOptions: null, // 可能为 null
};

const VenueDashboardTableModal = ({ data, isOpen, onClose, tableOptions }) => {
  const { t } = useTranslation();
  const [filter, setFilter] = useState('');
  const target = window.location.origin;
  const handleOpenInGateway = (serialNumber) => window.open(`${target}/wireless/devices/${serialNumber}#/devices/${serialNumber}`, '_blank');

  // Cell renderers
  const dateCell = useCallback(
    (value) => (value ? <FormattedDate date={value} key={uuid()} /> : '--'),
    []
  );

  const healthCell = useCallback(
    (value) => `${value}%`,
    []
  );

  const statusCell = useCallback(
    (value) => (value ? t('common.connected') : t('common.disconnected')),
    [t]
  );

  const memoryCell = useCallback(
    (value) => `${Math.floor(value * 100) / 100}%`,
    []
  );

  const durationCell = useCallback(
    (value) => (value !== undefined ? minimalSecondsToDetailed(value, t) : ''),
    [t]
  );

  const onFilterChange = useCallback((e) => setFilter(e.target.value));

  const filteredData = useMemo(() => {
    if (!data) return [];
    if (filter.trim().length === 0) return [...data.devices, ...data.ignoredDevices];
    const devices = data.devices.filter((entry) => entry.serialNumber.includes(filter));
    return devices.concat(data.ignoredDevices.filter((entry) => entry.serialNumber.includes(filter)));
  }, [data, filter]);

  const getColumns = () => {
    const fixedColumns = [
      {
        key: 'serialNumber',
        title: (
          <span style={{ whiteSpace: 'nowrap' }}>
            {t('inventory.serial_number')}
          </span>
        ),
        dataIndex: 'serialNumber',
        sorter: (a, b) => defaultSorter(a, b, 'serialNumber'),
        render: (_, record) => (
          <Link
            onClick={() => handleOpenInGateway(record.serialNumber)}
            style={{
              color: '#14C9BB',
              textDecorationLine: 'underline',
            }}
          >
            {record.serialNumber}
          </Link>
        ),
        columnsFix:true,
        fixed: 'left',
      },
    ];

    const configurableColumns = [
      {
        key: 'connected',
        title: t('common.status'),
        dataIndex: 'connected',
        sorter: (a, b) => defaultSorter(a, b, 'connected'),
        render: statusCell,
      },
      {
        key: 'health',
        title: t('analytics.health'),
        dataIndex: 'health',
        sorter: (a, b) => defaultSorter(a, b, 'health'),
        render: healthCell,
      },
      {
        key: 'lastHealth',
        title: (
          <span style={{ whiteSpace: 'nowrap' }}>
            {t('analytics.last_health')}
          </span>
        ),
        dataIndex: 'lastHealth',
        sorter: (a, b) => defaultSorter(a, b, 'lastHealth'),
        render: dateCell,
      },
      {
        key: 'lastPing',
        title: (
          <span style={{ whiteSpace: 'nowrap' }}>
            {t('analytics.last_ping')}
          </span>
        ),
        dataIndex: 'lastPing',
        sorter: (a, b) => defaultSorter(a, b, 'lastPing'),
        render: dateCell,
      },
      {
        key: 'memory',
        title: t('analytics.memory'),
        dataIndex: 'memory',
        sorter: (a, b) => defaultSorter(a, b, 'memory'),
        render: memoryCell,
      },
      {
        key: 'lastConnection',
        title: (
          <span style={{ whiteSpace: 'nowrap' }}>
            {t('analytics.last_connection')}
          </span>
        ),
        dataIndex: 'lastConnection',
        sorter: (a, b) => defaultSorter(a, b, 'lastConnection'),
        render: dateCell,
      },
      {
        key: 'lastContact',
        title: (
          <span style={{ whiteSpace: 'nowrap' }}>
            {t('analytics.last_contact')}
          </span>
        ),
        dataIndex: 'lastContact',
        sorter: (a, b) => defaultSorter(a, b, 'lastContact'),
        render: dateCell,
      },
      {
        key: '2g',
        title: '2G',
        dataIndex: 'associations_2g',
        sorter: (a, b) => defaultSorter(a, b, 'associations_2g'),
      },
      {
        key: '5g',
        title: '5G',
        dataIndex: 'associations_5g',
        sorter: (a, b) => defaultSorter(a, b, 'associations_5g'),
      },
      {
        key: '6g',
        title: '6G',
        dataIndex: 'associations_6g',
        sorter: (a, b) => defaultSorter(a, b, 'associations_6g'),
      },
      {
        key: 'uptime',
        title: t('analytics.uptime'),
        dataIndex: 'uptime',
        sorter: (a, b) => defaultSorter(a, b, 'uptime'),
        render: durationCell,
      },
      {
        key: 'deviceType',
        title: (
          <span style={{ whiteSpace: 'nowrap' }}>
            {t('inventory.device_type')}
          </span>
        ),
        dataIndex: 'deviceType',
        sorter: (a, b) => defaultSorter(a, b, 'deviceType'),
      },
      {
        key: 'lastFirmware',
        title: t('analytics.firmware'),
        dataIndex: 'lastFirmware',
        sorter: (a, b) => defaultSorter(a, b, 'lastFirmware'),
      },
    ];

    const prioritizedColumns = tableOptions?.prioritizedColumns ?? [];

    if (prioritizedColumns.length > 0) {
      const remainingColumns = configurableColumns.filter(
        col => !prioritizedColumns.includes(col.key)
      );

      const orderedColumns = prioritizedColumns
        .map(key => {
          const targetCol = configurableColumns.find(col => col.key === key);
          if (targetCol) {
            return { ...targetCol, columnsFix: true };
          }
          return null;
        })
        .filter(Boolean);

      return [...fixedColumns, ...orderedColumns, ...remainingColumns];
    }

    return [...fixedColumns, ...configurableColumns];
  };

  const columns = useMemo(
    () => getColumns(),
    [tableOptions, t, dateCell, healthCell, statusCell, memoryCell, durationCell]
  );

  const getInitialSort = useCallback(() => {
    if (tableOptions && tableOptions.sortBy && tableOptions.sortBy.length > 0) {
      return tableOptions.sortBy.map(item => ({
        field: item.id,
        order: item.desc ? 'descend' : 'ascend'
      }));
    }
    return [];
  }, [tableOptions]);

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total) => `Total ${total} items`,
  });

  const [sort, setSort] = useState(getInitialSort());

  useEffect(() => {
    if (isOpen) {
      setFilter('');
      setPagination({
        current: 1,
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '50', '100'],
        showTotal: (total) => `Total ${total} items`,
      });
      setSort(getInitialSort());
    }
  }, [isOpen, tableOptions]);

  const handleTableChange = useCallback((newPagination, filters, sorter) => {
    setPagination(prevPagination => ({
      ...prevPagination,
      ...newPagination
    }));
    if (sorter.field) {
      setSort([{
        field: sorter.field,
        order: sorter.order
      }]);
    } else {
      setSort([]);
    }
  }, []);

  const paginatedData = useMemo(() => {
    let sortedData = [...filteredData];
    if (sort && sort.length > 0) {
      sortedData.sort((a, b) => {
        for (const sortItem of sort) {
          const column = columns.find(col => col.dataIndex === sortItem.field);
          if (column?.sorter) {
            const result = column.sorter(a, b);
            if (result !== 0) {
              return sortItem.order === 'ascend' ? result : -result;
            }
          }
        }
        return 0;
      });
    }

    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return sortedData.slice(startIndex, endIndex);
  }, [filteredData, pagination, sort, columns]);

  // 构建弹窗内容
  const modalContent = (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Flex justify="flex-end" align="center">
        <Input
          value={filter}
          onChange={onFilterChange}
          allowClear
          prefix={<Icon component={searchSvg} />}
          placeholder={t('analytics.search_serials')}
          style={{ width: 280, height: "32px", float: "right", borderRadius: "2px" }}
        />
      </Flex>
      <div style={{ overflowX: 'auto', width: '100%' }}>
        <WirelessCustomTable
          key={JSON.stringify([isOpen, tableOptions])}
          columns={columns}
          dataSource={paginatedData}
          rowKey="serialNumber"
          pagination={{
            ...pagination,
            total: filteredData.length,
          }}
          onChange={handleTableChange}
          showColumnSelector={true}
        />
      </div>
    </Space>
  );

  return (
    <AmpConCustomModal
      title={t('analytics.raw_analytics_data')}
      childItems={modalContent}
      isModalOpen={isOpen}
      onCancel={onClose}
      modalClass="ampcon-max-modal"
      footer={null}
    />
  );
};

VenueDashboardTableModal.propTypes = propTypes;
VenueDashboardTableModal.defaultProps = defaultProps;
export default VenueDashboardTableModal;
