import React from 'react';
import { Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { useVenueMonitoring } from '../VenueMonitoringContext';
import CommonMetricCard from '@/modules-smb/Wireless/components/Card/CommonMetricCard';
import associationsIcon from '@/modules-smb/Wireless/assets/Monitor/Logo_Associations.png';

const { Text } = Typography;

const TotalAssociationsCard = () => {
  const { t } = useTranslation();
  const { dashboard, handleDashboardModalOpen } = useVenueMonitoring();

  const associationData = [
    { label: '2G', value: dashboard?.twoGAssociations ?? 0 },
    { label: '5G', value: dashboard?.fiveGAssociations ?? 0 },
    { label: '6G', value: dashboard?.sixGAssociations ?? 0 },
  ];

  const cardContent = (
    <div style={{
      display: 'flex',
      justifyContent: 'space-around',
      padding: '16px',
      height: '100%',
      marginTop: 12,
    }}>
      {associationData.map(({ label, value }) => (
        <div key={label} style={{
          display: 'flex',
          width: '33.33%',
          justifyContent: 'center',
        }}>
          <img
            src={associationsIcon}
            alt={`${label} Associations`}
            style={{ height: 24, width: 24, marginRight: 8, marginTop: 24 }}
          />
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
            <Text style={{ fontSize: '24px', fontWeight: '900' }}>{value}</Text>
            <Text style={{ fontSize: '16px', color: '#777', marginTop: 4 }}>{label}</Text>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <CommonMetricCard
      title="analytics.associations"
      explanation="analytics.associations_explanation"
      bgGradient="linear-gradient(180deg, #F2F9FE 0%, #E6F4FE 100%)"
      content={cardContent}
      buttonColor="#D9EFFC" // 添加按钮hover颜色
      onDetailClick={() =>
        handleDashboardModalOpen({
          prioritizedColumns: ['2g', '5g', '6g'],
          sortBy: [
            { id: '2g', desc: true },
            { id: '5g', desc: true },
            { id: '6g', desc: true },
          ],
        })
      }
    />
  );
};

export default TotalAssociationsCard;