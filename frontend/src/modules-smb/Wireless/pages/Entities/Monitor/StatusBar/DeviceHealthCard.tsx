import React from 'react';
import { Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { useVenueMonitoring } from '../VenueMonitoringContext';
import CommonMetricCard from '@/modules-smb/Wireless/components/Card/CommonMetricCard';
import healthCurveIcon from '@/modules-smb/Wireless/assets/Monitor/Logo_Health.png';

const { Text } = Typography;

const DeviceHealthCard = () => {
  const { t } = useTranslation();
  const { dashboard, handleDashboardModalOpen } = useVenueMonitoring();
  const avgHealth = `${dashboard?.avgHealth ?? 0}%`;

  return (
    <CommonMetricCard
      title="analytics.average_health"
      explanation="analytics.average_health_explanation"
      bgGradient="linear-gradient(180deg, #F2F9FE 0%, #E6F4FE 100%)"
      buttonColor="#D9EFFC" // 添加按钮hover颜色
      content={
        <div style={{
          display: 'flex',
          alignItems: 'center', 
          height: '100%',      
          padding: 0,
        }}>
          <Text
            style={{
              fontSize: '24px',
              fontWeight: 'bold',
              marginLeft: '32px',
              whiteSpace: 'nowrap',
     
            }}
          >
            {avgHealth}
          </Text>

          <div style={{ flex: 1 }} />

          <img
            src={healthCurveIcon}
            alt="Health Curve Icon"
            style={{
              marginRight: '32px',
              width: '115px',    
              height: '70px', 
            }}
          />
        </div>
      }
      onDetailClick={() =>
        handleDashboardModalOpen({
          prioritizedColumns: ['lastHealth', 'health'],
          sortBy: [{ id: 'health', desc: false }],
        })
      }
    />
  );
};

export default DeviceHealthCard;
