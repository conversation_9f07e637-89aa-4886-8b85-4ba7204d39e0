import React from 'react';
import DeviceHealthCard from './DeviceHealthCard';
import DeviceMemoryCard from './DeviceMemoryCard';
import TotalAssociationsCard from './TotalAssociationsCard';
import DeviceStatusCard from './DeviceStatusCard';

const VenueStatusBar = () => {
  return (
    <div style={{ 
      width: '100%', 
      margin: '0 auto ',
      display: 'flex',
      gap: '32px',
    }}>
      <div style={{ flex: 1, minWidth: 0 }}><DeviceHealthCard /></div>
      <div style={{ flex: 1, minWidth: 0 }}><DeviceMemoryCard /></div>
      <div style={{ flex: 1, minWidth: 0 }}><DeviceStatusCard /></div>
      <div style={{ flex: 1.22, minWidth: 0 }}><TotalAssociationsCard /></div>
    </div>
  );
};

export default VenueStatusBar;
