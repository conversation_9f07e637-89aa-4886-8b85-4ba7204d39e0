import React from 'react';
import { <PERSON>, Card, Row, Col, Empty } from 'antd';
import { <PERSON><PERSON>, Tooltip } from 'antd';
import { useVenueMonitoring } from '../VenueMonitoringContext';
import SelectedMonitoringDisplay from './SelectedMonitoringDisplay';
import { WifiHigh, Broadcast, Person } from '@phosphor-icons/react';
import IconSub from '@/modules-smb/Wireless/assets/Monitor/Logo_Sub.png';
import IconAdd from '@/modules-smb/Wireless/assets/Monitor/Logo_Add.png';
import LogoMacAddress from "@/modules-smb/Wireless/assets/Monitor/Logo_MAC Address.png";
import IconWirelessSignal from '@/modules-smb/Wireless/assets/Monitor/Logo_Wireless_Signal.png';
import IconWifi from '@/modules-smb/Wireless/assets/Monitor/Logo_Wifi.png';
import IconUser from '@/modules-smb/Wireless/assets/Monitor/Logo_User.png';
import EmptyPic from "@/assets/images/App/empty.png";
import { healthIcon, ueIcon, noiseIcon } from '../utils';
import { useTranslation } from 'react-i18next';

const switcherIcon = ({ expanded, isLeaf }: { expanded: boolean; isLeaf: boolean }) => {
  if (isLeaf) {
    return <span style={{ display: 'inline-block', width: 16 }} />;
  }
  return (
    <img
      src={expanded ? IconSub : IconAdd}
      alt="Expand/Collapse Icon"
      style={{
        width: '16px',
        height: '16px',
        marginRight: '4px',
        verticalAlign: 'middle'
      }}
    />
  );
};
const EmptyState = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%'
  }}>
    <Empty image={EmptyPic} description="No Data" imageStyle={{ marginTop: 16, marginBottom: 0 }} />

  </div>
);


const VenueMonitoringTree = () => {
  const context = useVenueMonitoring();
  const monitoringData = context.monitoring;


  // 修改 treeData 的生成逻辑，添加空值判断
  const treeData = monitoringData ? monitoringData.map(ap => ({
    key: `ap-${ap.serialNumber}`,
    title: <APTitle data={ap} />,
    children: Object.values(ap.radios).map(radio => ({
      key: `radio-${ap.serialNumber}-${radio.band}`,
      title: <RadioTitle data={radio} serialNumber={ap.serialNumber} />,
      children: Object.values(radio.ssids).map(ssid => ({
        key: `ssid-${ssid.bssid}`,
        title: <SsidTitle data={ssid} />,
        children: Object.values(ssid.ues).map(ue => ({
          key: `ue-${ue.station}`,
          title: <UeTitle data={ue} />,
          isLeaf: true,
        })),
      })),
    })),
  })) : []; // 如果 monitoringData 不存在，返回空数组

  const onSelect = (keys, { node }) => {
    const key = keys[0];
    if (!key) return;
    if (key.startsWith('ap-')) {
      const serialNumber = key.replace('ap-', '');
      const ap = monitoringData.find(ap => ap.serialNumber === serialNumber);
      if (ap) {
        context.onSelectItem({
          type: 'AP',
          data: ap,
        });
      }
    } else if (key.startsWith('radio-')) {
      const [serialNumber, band] = key.replace('radio-', '').split('-');
      const ap = monitoringData.find(ap => ap.serialNumber === serialNumber);
      if (ap) {
        const radio = Object.values(ap.radios).find(r => r.band == band);
        if (radio) {
          context.onSelectItem({
            type: 'RADIO',
            data: radio,
            serialNumber,
          });
        }
      }
    } else if (key.startsWith('ssid-')) {
      const bssid = key.replace('ssid-', '');
      let foundSsid = null;
      monitoringData.forEach(ap => {
        Object.values(ap.radios).forEach(radio => {
          Object.values(radio.ssids).forEach(ssid => {
            if (ssid.bssid === bssid) {
              foundSsid = ssid;
            }
          });
        });
      });
      if (foundSsid) {
        context.onSelectItem({
          type: 'SSID',
          data: foundSsid,
        });
      }
    } else if (key.startsWith('ue-')) {
      const station = key.replace('ue-', '');
      let foundUe = null;
      monitoringData.forEach(ap => {
        Object.values(ap.radios).forEach(radio => {
          Object.values(radio.ssids).forEach(ssid => {
            Object.values(ssid.ues).forEach(ue => {
              if (ue.station === station) {
                foundUe = ue;
              }
            });
          });
        });
      });
      if (foundUe) {
        context.onSelectItem({
          type: 'UE',
          data: foundUe,
        });
      }
    }
  };
  // 判断是否为空状态
  const isEmpty = treeData.length === 0;
  return (
    <Card
      title={(
        <div style={{ fontSize: '18px' }}>
          Live Data
        </div>
      )}
      style={{
        height: '100%', // 确保卡片占据其容器的全部高度
        display: 'flex',
        flexDirection: 'column',
        border: 'none', // 移除卡片边框线
      }}
      bodyStyle={{
        flex: 1,
        overflow: 'hidden', // 防止内容溢出卡片
        padding: '16px' // 添加一些填充
      }}
    >
      <Row gutter={16} style={{ height: '100%' }}>
        <Col span={24} style={{ height: '100%' }}>
          <div style={{
            height: '100%',
            overflowY: isEmpty ? 'hidden' : 'auto',
          }}>
            {treeData.length > 0 ? (
              <Tree
                multiple={false}
                defaultExpandAll={false}
                onSelect={onSelect}
                treeData={treeData}
                showIcon={false}
                switcherIcon={switcherIcon}
                showLine
                expandAction="click"
                style={{
                  fontFamily: 'Lato',
                  lineHeight: '32px',
                  minHeight: '100%'
                }}
              />
            ) : (
              <EmptyState />
            )}
            <Col span={8}>
              <SelectedMonitoringDisplay />
            </Col>
          </div>
        </Col>
      </Row>
    </Card>
  );
};


const APTitle = ({ data }) => {
  const { t } = useTranslation();
  const context = useVenueMonitoring();
  const isSelected = context.selectedItem?.type === 'AP' &&
    context.selectedItem.data.dashboardData.serialNumber === data.dashboardData.serialNumber;
  const target = window.location.origin;

  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      fontWeight: isSelected ? 'bold' : 'normal',
      // backgroundColor: isSelected ? '#f0f0f0' : 'transparent',
      // borderRadius: '10px',
      padding: '2px 4px',
      verticalAlign: 'middle'
    }}>
      <img src={LogoMacAddress} alt="MAC Address Logo" width="20" height="20" />
      <span style={{
        fontFamily: 'Lato',
        marginRight: '16px',
        marginLeft: '4px',
      }}>{data.serialNumber}</span>
      <Tooltip title={t('common.view_in_gateway')} placement="top">
        <Button
          style={{
            width: '48px',
            height: '24px'
          }}
          type="primary"
          onClick={() => window.open(`${target}/wireless/devices/${data.serialNumber}#/devices/${data.serialNumber}`, '_blank')}
        >
          View
        </Button>
      </Tooltip>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        height: '24px',
        gap: '8px',
        marginLeft: '4px',
      }}>
        {ueIcon(data.ues)}
        {healthIcon(data.dashboardData.health)}
      </div>
       {noiseIcon(data.averageRssi)}
    </div>
  );
};


const RadioTitle = ({ data, serialNumber }) => {
  const context = useVenueMonitoring();
  const isSelected = context.selectedItem?.type === 'RADIO' &&
    context.selectedItem.data.band === data.band &&
    context.selectedItem.serialNumber === serialNumber;

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      fontWeight: isSelected ? 'bold' : 'normal',
      // backgroundColor: isSelected ? '#f0f0f0' : 'transparent',
      // borderRadius: '10px',
      padding: '2px 4px'
    }}>
      {/* <WifiHigh size={20} style={{ marginRight: '4px' }} /> */}
      <img
        src={IconWifi}
        alt="WiFi Signal"
        width="20"
        height="20"
        style={{ marginRight: '4px' }}
      />
      <span style={{
        fontFamily: 'Lato',
        marginRight: '4px' // 频段和ueIcon间距
      }}>{data.band}G</span>
      <span style={{ marginRight: '49px' }}>
        {ueIcon(data.amountOfUes)}
      </span>
    </div>
  );
};


const SsidTitle = ({ data }) => {
  const context = useVenueMonitoring();
  const isSelected = context.selectedItem?.type === 'SSID' &&
    context.selectedItem.data.bssid === data.bssid;

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      fontWeight: isSelected ? 'bold' : 'normal',
      // backgroundColor: isSelected ? '#f0f0f0' : 'transparent',
      // borderRadius: '10px',
      padding: '2px 4px'
    }}>
      <img src={IconWirelessSignal} alt="Wireless Signal" width="20" height="20" style={{ marginRight: '4px' }} />
      <span style={{
        fontFamily: 'Lato',
        marginRight: '6px',// SSID和ueIcon间距
        whiteSpace: 'nowrap' /* 防止换行 */
      }}>{data.ssid}</span>
 
        {ueIcon(data.amountOfUes)}
    
    </div>
  );
};


const UeTitle = ({ data }) => {
  const context = useVenueMonitoring();
  const isSelected = context.selectedItem?.type === 'UE' &&
    context.selectedItem.data.station === data.station;

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      fontWeight: isSelected ? 'bold' : 'normal',
      // backgroundColor: isSelected ? '#f0f0f0' : 'transparent',
      // borderRadius: '10px',
      padding: '2px 4px'
    }}>
      <img src={IconUser} alt="User Icon" width="20" height="20" style={{ marginRight: '4px' }} />
      <span style={{ fontFamily: 'Lato', marginRight: '4px' }}>{data.station}</span>
      {noiseIcon(data.rssi)}
    </div>
  );
};



export default VenueMonitoringTree;