import * as React from 'react';
import { Row, Col, Typography, Divider } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useVenueMonitoring } from '../VenueMonitoringContext';
import { AnalyticsAssociationData, AnalyticsRadioData, AnalyticsSsidData } from '@/modules-smb/models/Analytics';
import { bytesString, formatNumberToScientificBasedOnMax } from '@/modules-smb/utils/stringHelper';
import { AmpConCustomModal } from '@/modules-ampcon/components/custom_table';
const { Title, Text } = Typography;

const SelectedMonitoringDisplay = () => {
  const { t } = useTranslation();
  const { selectedItem, onUnselectItem } = useVenueMonitoring();


  const onClose = () => {
    onUnselectItem();
  };

  if (!selectedItem) return null;

  const renderRowItem = (label: string, value: React.ReactNode, index: number) => {
    const isEven = index % 2 === 0;
    return (
      <Col span={12} style={{
        padding: '0 4px', // 左右各4px间隙
        height: '48px',
        display: 'flex',
        alignItems: 'center'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          height: '100%',
          backgroundColor: isEven ? '#F8FAFB' : '#FFFFFF',
        }}>
          <Text style={{
            fontSize: '14px',
            color: '#474747',
            whiteSpace: 'nowrap',
            paddingLeft: '16px',
            width: '160px',
            flexShrink: 0,
          }}>{label}</Text>
          <span style={{
            fontWeight: 600,
            fontSize: '14px',
            color: '#212519',
            whiteSpace: 'nowrap',
          }}>{value}</span>
        </div>
      </Col>
    );
  };
  const renderFullWidthRowItem = (label: string, value: React.ReactNode, index: number) => {
    const isEven = index % 2 === 0;
    return (
      <Col span={24} style={{
        padding: '0 4px',
        height: '48px',
        display: 'flex',
        alignItems: 'center'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          height: '100%',
          backgroundColor: isEven ? '#F8FAFB' : '#FFFFFF', 
        }}>
          <Text style={{
            fontSize: '14px',
            color: '#474747',
            whiteSpace: 'nowrap',
            paddingLeft: '16px',
            width: '160px',
            flexShrink: 0,
          }}>{label}</Text>
          <span style={{
            fontWeight: 600,
            fontSize: '14px',
            color: '#212519',
            whiteSpace: 'nowrap',
          }}>{value}</span>
        </div>
      </Col>
    );
  };
  const renderContent = () => {
    if (selectedItem.type === 'AP') {
      return (
        <div style={{ width: '100%' }}>
          {/* 使用gutter={8}创建行间间隙 */}
          <Row gutter={8}>
            {renderRowItem(t('analytics.health'), `${selectedItem.data.dashboardData.health}%`, 0)}
            {renderRowItem(t('analytics.memory_used'), `${Math.floor(selectedItem.data.dashboardData.memory)}%`, 0)}
          </Row>
          <Row gutter={8}>
            {renderRowItem(t('common.type'), selectedItem.data.dashboardData.deviceType, 1)}
            {renderRowItem(t('analytics.firmware'), selectedItem.data.dashboardData.lastFirmware?.split('/')[1] ?? t('common.unknown'), 1)}
          </Row>
          <Row gutter={8}>
            {renderRowItem('2G Clients', selectedItem.data.dashboardData.associations_2g, 2)}
            {renderRowItem('5G Clients', selectedItem.data.dashboardData.associations_5g, 2)}
          </Row>
          <Row gutter={8}>
            {renderRowItem('6G Clients', selectedItem.data.dashboardData.associations_6g, 3)}
          </Row>

          <Row style={{ marginTop: 8 }}>
            <Col span={24}>
              <Title level={5} style={{ marginBottom: 16 }}>Last 10 Minutes</Title>
            </Col>
          </Row>
          <Row gutter={8}>
            {renderRowItem('Tx', bytesString(selectedItem.data.deltas.txBytes), 4)}
            {renderRowItem('Tx Packets', selectedItem.data.deltas.txPackets.toLocaleString(), 4)}
          </Row>
          <Row gutter={8}>
            {renderRowItem('Rx', bytesString(selectedItem.data.deltas.rxBytes), 5)}
            {renderRowItem('Rx Packets', selectedItem.data.deltas.rxPackets.toLocaleString(), 5)}
          </Row>
        </div>
      );
    }

    if (selectedItem.type === 'RADIO') {
      const latestTimepoint = selectedItem.data.timepoints[selectedItem.data.timepoints.length - 1] as AnalyticsRadioData;
      return (
        <div style={{ width: '100%' }}>
          <Row gutter={8}>
            {renderRowItem(t('analytics.noise'), `${selectedItem.data.averageRssi} dB`, 0)}
            {renderRowItem(t('analytics.channel'), latestTimepoint.channel, 0)}
          </Row>
          <Row gutter={8}>
            {renderRowItem(t('analytics.temperature'), `${latestTimepoint.temperature}°C`, 1)}
            {renderRowItem(t('analytics.airtime'), `${latestTimepoint.transmit_pct.toFixed(2)}%`, 1)}
          </Row>
          <Row gutter={8}>
            {renderRowItem(t('analytics.active'), `${latestTimepoint.active_pct.toFixed(2)}%`, 2)}
            {renderRowItem(t('analytics.busy'), `${latestTimepoint.busy_pct.toFixed(2)}%`, 2)}
          </Row>
          <Row gutter={8}>
            {renderRowItem(t('analytics.receive'), `${latestTimepoint.receive_pct.toFixed(2)}%`, 3)}
          </Row>
        </div>
      );
    }

    if (selectedItem.type === 'SSID') {
      const latestTimepoint = selectedItem.data.timepoints[selectedItem.data.timepoints.length - 1] as AnalyticsSsidData;

      return (
        <div style={{ width: '100%' }}>
          <Row gutter={8}>
            {renderRowItem('Bssid', selectedItem.data.bssid, 0)}
            {renderRowItem('Noise', `${selectedItem.data.averageRssi} dB`, 0)}
          </Row>
          <Row gutter={8}>
            {renderRowItem('TX Bandwidth (avg)', bytesString(latestTimepoint.tx_bytes_bw.avg), 1)}
            {renderRowItem('TX Bandwidth (min)', bytesString(latestTimepoint.tx_bytes_bw.min), 1)}
          </Row>
          <Row gutter={8}>
            {renderRowItem('TX Bandwidth (max)', bytesString(latestTimepoint.tx_bytes_bw.max), 2)}
            {renderRowItem('TX Packets/s', `${latestTimepoint.tx_packets_bw.avg.toFixed(2)} / ${latestTimepoint.tx_packets_bw.min.toFixed(2)} / ${latestTimepoint.tx_packets_bw.max.toFixed(2)}`, 2)}
          </Row>
          <Row gutter={8}>
            {renderRowItem('RX Bandwidth (avg)', bytesString(latestTimepoint.rx_bytes_bw.avg), 3)}
            {renderRowItem('RX Bandwidth (min)', bytesString(latestTimepoint.rx_bytes_bw.min), 3)}
          </Row>
          <Row gutter={8}>
            {renderRowItem('RX Bandwidth (max)', bytesString(latestTimepoint.rx_bytes_bw.max), 4)}
            {renderRowItem('RX Packets/s', `${latestTimepoint.rx_packets_bw.avg.toFixed(2)} / ${latestTimepoint.rx_packets_bw.min.toFixed(2)} / ${latestTimepoint.rx_packets_bw.max.toFixed(2)}`, 4)}
          </Row>
        </div>
      );
    }

    if (selectedItem.type === 'UE') {
      const latestTimepoint = selectedItem.data.timepoints[
        selectedItem.data.timepoints.length - 1
      ] as AnalyticsAssociationData;

      return (
        <div style={{ width: '100%' }}>
          <Row gutter={8}>
            {renderFullWidthRowItem('Connected to', `${selectedItem.data.band}G - ${selectedItem.data.ssid}`, 2)}
            <Col span={24} style={{
              padding: '12px 16px',
              backgroundColor: '#FFFFFF',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
              marginLeft: -12
            }}>
              <Text strong>Data (TX / RX)</Text>
            </Col>
          </Row>
          <Row gutter={8}>
            {renderRowItem(t('analytics.total_data'), `${bytesString(latestTimepoint.tx_bytes)} / ${bytesString(latestTimepoint.rx_bytes)}`, 1)}
            {renderRowItem(t('analytics.delta'), `${bytesString(selectedItem.data.deltas.txBytes)} / ${bytesString(selectedItem.data.deltas.rxBytes)}`, 1)}
          </Row>
          <Row gutter={8}>
            {renderRowItem(t('analytics.bandwidth'), `${bytesString(latestTimepoint.tx_bytes_bw)} / ${bytesString(latestTimepoint.rx_bytes_bw)}`, 2)}
            {renderRowItem(`${t('analytics.packets')}/s`, `${formatNumberToScientificBasedOnMax(latestTimepoint.tx_packets_bw)} / ${formatNumberToScientificBasedOnMax(latestTimepoint.rx_packets_bw)}`, 2)}
          </Row>
          <Row gutter={8}>
            {renderRowItem('MCS', `${latestTimepoint.tx_rate.mcs} / ${latestTimepoint.rx_rate.mcs}`, 3)}
            {renderRowItem('NSS', `${latestTimepoint.tx_rate.nss} / ${latestTimepoint.rx_rate.nss}`, 3)}
          </Row>
        </div>
      );
    }

    return <pre>{JSON.stringify(selectedItem, null, 2)}</pre>;
  };

  const getTitle = () => {
    const titleStyle = {
      margin: 0,
      display: 'inline-block'
    };

    switch (selectedItem.type) {
      case 'AP':
        return <span style={titleStyle}>{selectedItem.data.serialNumber}</span>;
      case 'RADIO':
        return <span style={titleStyle}>{selectedItem.serialNumber} - {selectedItem.data.band}G</span>;
      case 'SSID':
        return <span style={titleStyle}>{selectedItem.data.ssid}</span>;
      case 'UE':
        return <span style={titleStyle}>{selectedItem.data.station}</span>;
      default:
        return <span style={titleStyle}>Details</span>;
    }
  };

  const modalContent = (
    <div style={{
      width: '100%',
      height: 'auto',
      overflowY: 'auto', // 内容超出时滚动
      paddingRight: '8px',
    }}>
      {renderContent()}
    </div>
  );

  return (
    <AmpConCustomModal
      title={
        <div>
          {getTitle()}
          <Divider style={{
            marginBottom: 0,
            marginLeft: -24,
            marginRight: -24,
            width: 'calc(100% + 48px)'
          }} />
        </div>
      }
      childItems={modalContent}
      isModalOpen={!!selectedItem}
      onCancel={onClose}
      footer={null}
      modalClass="ampcon-middle-modal"
    />
  );
};

export default SelectedMonitoringDisplay;
