import React, { useEffect, useState } from 'react';
import { Modal, Form, InputNumber, Button, message, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import { useGetAnalyticsBoard, useUpdateAnalyticsBoard } from '@/modules-smb/hooks/Network/Analytics';
import { useGetVenue } from '@/modules-smb/hooks/Network/Venues';
import { AxiosError } from '@/modules-smb/models/Axios';
import StopMonitoringButton from './StopMonitoringButton';

interface Props {
    boardId: string;
    venueId: string;
    visible: boolean;
    onClose: () => void;
    onApplySuccess?: (value: boolean) => void; 
}

const ViewMonitoringModal = ({ boardId, venueId, visible, onClose, onApplySuccess }: Props) => {
    const { t } = useTranslation();
    const [formKey, setFormKey] = useState(uuid());
    const getBoard = useGetAnalyticsBoard({ id: boardId });
    const updateAnalytics = useUpdateAnalyticsBoard({ id: boardId });
    const getVenue = useGetVenue({ id: venueId });

    useEffect(() => {
        if (visible) setFormKey(uuid());
    }, [visible]);

    const validationSchema = Yup.object().shape({
        interval: Yup.number().required(t('form.required')).moreThan(0).integer(),
        retention: Yup.number().required(t('form.required')).moreThan(0).integer(),
    });

    const formik = useFormik({
        enableReinitialize: true,
        validationSchema,
        validateOnMount: true,
        initialValues: {
            name: getVenue.data?.name ?? '',
            interval: getBoard.data?.venueList[0]?.interval ?? 60,
            retention: (getBoard.data?.venueList[0]?.retention ?? 604800) / (24 * 60 * 60),
            monitorSubVenues: getBoard.data?.venueList[0]?.monitorSubVenues ?? true,
        },
        onSubmit: async (values, { setSubmitting }) => {
            try {
                const { name, interval, retention, monitorSubVenues } = values;
                await updateAnalytics.mutateAsync({
                    name,
                    venueList: [{
                        id: venueId,
                        name,
                        interval,
                        retention: retention * 24 * 60 * 60,
                        monitorSubVenues
                    }],
                });
                message.success(t('crud.success_update_obj', { obj: t('analytics.board') }));
                formik.resetForm();
                onClose();
                
            } catch (e) {
                message.error(t('crud.error_update_obj', {
                    obj: t('analytics.board'),
                    e: (e as AxiosError)?.response?.data?.ErrorDescription,
                }));
            } finally {
                setSubmitting(false);
            }
        },
    });

    const handleClose = () => {
        formik.resetForm();
        onClose();
    };

    return (
        <Modal
            key={formKey}
            title={t('Monitoring')}
            open={visible}
            onCancel={handleClose}
            footer={[
                <Button
                    key="cancel"
                    onClick={handleClose}
                    style={{ color: '#14C9BB', borderColor: '#14C9BB', height: '36px' }}
                >
                    {t('common.cancel')}
                </Button>,
                <Button
                    key="apply"
                    type="primary"
                    onClick={() => formik.submitForm()}
                    disabled={formik.isSubmitting}
                    loading={formik.isSubmitting}
                    style={{
                        height: '36px',
                        backgroundColor: '#14C9BB',
                        borderColor: '#14C9BB'
                    }}
                >
                    {t('Apply')}
                </Button>,
            ]}
            width={680}
            bodyStyle={{
                height: 330
            }}
        >
            <style>
                {`
                  .always-show-controls .ant-input-number-handler-wrap {
                    opacity: 1 !important;
                    display: flex !important;
                    pointer-events: auto !important;
                  }
                  .custom-input-wrapper {
                    position: relative;
                    width: 200px;
                  }
                  .unit-label {
                    position: absolute;
                    right: 30px; /* 放在调整按钮左侧 */
                    top: 50%;
                    transform: translateY(-50%);
                    color: #929A9E;
                    font-size: 14px;
                  }
                  .ant-input-number-input {
                    padding-right: 50px !important; 
                  }
                `}
            </style>

            <Divider style={{ margin: '0px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
            <div style={{ marginBottom: 24 }}>
                <StopMonitoringButton
                    boardId={boardId}
                    venueId={venueId}
                    onSuccess={ ()=>{
                        onClose(); 
                        onApplySuccess?.(true);
                    }}
                />
            </div>
            <Form layout="horizontal" labelAlign="left" style={{ marginBottom: 80 }}>
                <Form.Item
                    label={
                        <span style={{ width: 100, display: 'inline-block' }}>
                            {t('analytics.interval')}
                            <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                        </span>
                    }
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 18 }}
                    style={{ marginBottom: 24 }}
                    validateStatus={formik.errors.interval && formik.touched.interval ? 'error' : ''}
                    help={formik.errors.interval && formik.touched.interval ? formik.errors.interval : ''}
                >
                    <div className="custom-input-wrapper">
                        <InputNumber
                            name="interval"
                            value={formik.values.interval}
                            onChange={(value) => formik.setFieldValue('interval', value ?? 0)}
                            onBlur={() => formik.setFieldTouched('interval', true)}
                            //min={1}
                            style={{ width: '100%' }}
                            controls={true}
                            className="always-show-controls"
                        />
                        <span className="unit-label">{t('common.seconds')}</span>
                    </div>
                </Form.Item>
                <Form.Item
                    label={
                        <span style={{ width: 100, display: 'inline-block' }}>
                            {t('analytics.retention')}
                            <span style={{ color: 'red', marginLeft: 4 }}>*</span>
                        </span>
                    }
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 18 }}
                    style={{ marginBottom: 24 }}
                    validateStatus={formik.errors.retention && formik.touched.retention ? 'error' : ''}
                    help={formik.errors.retention && formik.touched.retention ? formik.errors.retention : ''}
                >
                    <div className="custom-input-wrapper">
                        <InputNumber
                            name="retention"
                            value={formik.values.retention}
                            onChange={(value) => formik.setFieldValue('retention', value ?? 0)}
                            onBlur={() => formik.setFieldTouched('retention', true)}
                            //min={1}
                            style={{ width: '100%' }}
                            controls={true}
                            className="always-show-controls"
                        />
                        <span className="unit-label">{t('common.days')}</span>
                    </div>
                </Form.Item>
            </Form>
            <Divider style={{ margin: '165px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
        </Modal>
    );
};

export default ViewMonitoringModal;