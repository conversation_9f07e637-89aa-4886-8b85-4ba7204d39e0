import React from 'react';
// import { Modal } from 'antd';
// import { ExclamationCircleFilled } from '@ant-design/icons';
// 替换为 confirmModalAction 组件
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
// import '@/modules-smb/Wireless/pages/Entities/RRMOptimize/styles/ConfirmModal.scss';
interface Props {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  confirmLoading?: boolean;
}

const ConfirmModal: React.FC<Props> = ({ visible, onOk, onCancel, confirmLoading }) => {
  // 使用新的 confirmModalAction 组件替换原有的 Modal 组件
  if (visible) {
    confirmModalAction(
      "Channel switching may occur during optimization, which may lead to user disconnection. The whole process is estimated to take 10 minutes. You are advised to avoid peak hours. Are you sure you want to start?",
      onOk,
      onCancel
    );
  }

  return null;

  // 原始代码保留作为注释
  /*
  return (
    <Modal
      open={visible}
      onOk={onOk}
      onCancel={onCancel}
      okText="Yes"
      cancelText="No"
      confirmLoading={confirmLoading}
      className="confirm-modal"
      title={<span className="confirm-title">Note</span>}
      width={480}
      destroyOnClose
    >
      <div className="confirm-content-row">
        <span className="confirm-icon">
          <ExclamationCircleFilled />
        </span>
        <span className="confirm-content-text">
          Channel switching may occur during optimization, which may lead to user disconnection. The whole process is estimated to take 10 minutes. You are advised to avoid peak hours. Are you sure you want to start?
        </span>
      </div>
    </Modal>
  );
  */
};

export default ConfirmModal;