.failed-device-modal-wrapper {
  // 确保模态框在各种缩放比例下都能正确显示
  .ant-modal {
    max-height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
    
    .ant-modal-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
  }
}

.failed-device-modal {
  .ant-modal-content {
    border-radius: 8px;
    padding: 0px 24px 24px 24px;
    background: #fff;
    min-height: 500px;
    display: flex;
    flex-direction: column;
  }

  .ant-modal-header {
    border-radius: 8px 8px 0 0;
    font-weight: bold;
    font-size: 20px;
    color: #222;
    padding: 15px 20px 15px 24px; // 只保留上下内边距
    border-bottom: 1px solid #e5e6eb !important;
    margin: 0 -24px 30px; // 左右负margin，数值等于父级padding, 下边距为20px
    flex-shrink: 0; // 防止头部被压缩
    display: flex !important;
    align-items: center !important;
  }

  .ant-modal-title {
    font-weight: bold;
    font-size: 20px;
    color: #222;
    margin-top: 0px !important;
    height: 27px !important;
    display: flex !important;
    align-items: center !important;
  }

  .ant-modal-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 0 !important; // 移除默认padding
    max-height: calc(100vh - 200px); // 优化最大高度计算
  }

  .failed-device-modal-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 420px;
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 350px; // 确保表格容器有最小高度

    .ant-table-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden; // 确保容器不会溢出

      .ant-spin-nested-loading,
      .ant-spin-container,
      .ant-table {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden; // 防止内容溢出
      }

      .ant-table {
        flex: 1 1 auto;
      }

      .ant-table-container {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      // 使用 Ant Design 的内置滚动处理
      .ant-table-body {
        flex: 1;
        min-height: 300px; // 设置最小高度确保即使在缩放情况下也有足够的显示空间
        max-height: 500px !important; // 设置固定最大高度以确保滚动条显示
        overflow-y: auto;

        // 添加底部边框以确保滚动时表格底部边框可见
        border-bottom: 1px solid #f0f0f0;
        
        // 当内容不足时，确保边框仍然显示
        &:after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 1px;
          background: #f0f0f0;
        }

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 8px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }

      // 确保表头和表体宽度一致
      .ant-table-header {
        overflow: hidden;

        table {
          // 让表头表格与表体保持一致的宽度计算
          table-layout: fixed;
        }
      }

      .ant-table-body {
        table {
          // 让表体表格使用固定布局
          table-layout: fixed;
        }
      }
      
      // 确保表格底部边框可见
      .ant-table-content {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .pagination-container {
    flex-shrink: 0; // 防止分页器被压缩
    padding: 16px 0 0 0;
    min-height: 56px; // 确保分页区域有足够的高度

    .ant-pagination {
      margin: 0;
      justify-content: flex-end;
    }
  }

  .ant-modal-footer {
    border-top: none;
    padding: 16px 24px 0 24px;
  }
}