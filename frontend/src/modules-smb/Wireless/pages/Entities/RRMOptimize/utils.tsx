interface SchedulerConfig {
    enabled: boolean;
    executeTime: string; // HH:mm 格式
    days: string[]; // ['1', '2', '3', '4', '5', '6', '7'] 1=Sunday, 2=Monday, 3=Tuesday, 4=Wednesday, 5=Thursday, 6=Friday, 7=Saturday
}

/**
 * 将本地时间的cron表达式转换为UTC时间（分钟级精度）
 * @param localCron 本地时间的cron表达式
 * @returns UTC时间的cron表达式
 */
function convertCronToUTC(localCron: string): string {
    if (!localCron || localCron.trim() === '' || localCron === '0 0 0 * * *') {
        return localCron;
    }

    const parts = localCron.split(' ');
    let minute = parseInt(parts[1] || '0');
    let hour = parseInt(parts[2] || '0');
    const daysStr = parts[5] || '*';

    // 本地时间（分钟数）
    let totalMinutes = hour * 60 + minute;

    // getTimezoneOffset(): 返回 UTC - Local（分钟差）
    // 所以本地时间转UTC要加上 offsetMinutes
    const offsetMinutes = new Date().getTimezoneOffset();
    totalMinutes += offsetMinutes;

    let dayOffset = 0;
    if (totalMinutes < 0) {
        totalMinutes += 24 * 60;
        dayOffset = -1;
    } else if (totalMinutes >= 24 * 60) {
        totalMinutes -= 24 * 60;
        dayOffset = 1;
    }

    hour = Math.floor(totalMinutes / 60);
    minute = totalMinutes % 60;

    // 调整星期几 (使用1-7格式: 1=Sunday, 2=Monday, ..., 7=Saturday)
    let utcDays = daysStr;
    if (daysStr !== '*') {
        const days = daysStr.split(',').map(d => parseInt(d));
        const adjustedDays = days.map(day => {
            let newDay = day + dayOffset;
            if (newDay < 1) newDay += 7;
            if (newDay > 7) newDay -= 7;
            return newDay.toString();
        });
        utcDays = [...new Set(adjustedDays)].sort((a, b) => parseInt(a) - parseInt(b)).join(',');
    }

    return `0 ${minute} ${hour} * * ${utcDays}`;
}

/**
 * 将UTC时间的cron表达式转换为本地时间（分钟级精度）
 * @param utcCron UTC时间的cron表达式
 * @returns 本地时间的cron表达式
 */
function convertCronToLocal(utcCron: string): string {
    if (!utcCron || utcCron.trim() === '' || utcCron === '0 0 0 * * *') {
        return utcCron;
    }

    const parts = utcCron.split(' ');
    let minute = parseInt(parts[1] || '0');
    let hour = parseInt(parts[2] || '0');
    const daysStr = parts[5] || '*';

    // UTC时间（分钟数）
    let totalMinutes = hour * 60 + minute;

    // getTimezoneOffset(): UTC - Local
    // 所以UTC转本地要减去 offsetMinutes
    const offsetMinutes = new Date().getTimezoneOffset();
    totalMinutes -= offsetMinutes;

    let dayOffset = 0;
    if (totalMinutes < 0) {
        totalMinutes += 24 * 60;
        dayOffset = -1;
    } else if (totalMinutes >= 24 * 60) {
        totalMinutes -= 24 * 60;
        dayOffset = 1;
    }

    hour = Math.floor(totalMinutes / 60);
    minute = totalMinutes % 60;

    // 调整星期几 (使用1-7格式: 1=Sunday, 2=Monday, ..., 7=Saturday)
    let localDays = daysStr;
    if (daysStr !== '*') {
        const days = daysStr.split(',').map(d => parseInt(d));
        const adjustedDays = days.map(day => {
            let newDay = day + dayOffset;
            if (newDay < 1) newDay += 7;
            if (newDay > 7) newDay -= 7;
            return newDay.toString();
        });
        localDays = [...new Set(adjustedDays)].sort((a, b) => parseInt(a) - parseInt(b)).join(',');
    }

    return `0 ${minute} ${hour} * * ${localDays}`;
}

/**
 * 解析 cron 表达式为 SchedulerConfig（支持星期几字段）
 * @param cron cron表达式（假设是本地时间）
 * @param isUTC 是否为UTC时间的cron表达式
 */
function parseSchedule(cron: string, isUTC: boolean = false): SchedulerConfig {
    if (!cron || cron.trim() === '' || cron === '0 0 0 * * *') {
        return {
            enabled: false,
            executeTime: '00:00',
            days: ['2', '3', '4', '5', '6', '7', '1'],
        };
    }

    // 如果是UTC时间的cron，先转换为本地时间
    const localCron = isUTC ? convertCronToLocal(cron) : cron;

    const parts = localCron.split(' ');
    const minute = parts[1] || '00';
    const hour = parts[2] || '00';
    const days = parts[5] && parts[5] !== '*'
        ? parts[5].split(',')
        : ['2', '3', '4', '5', '6', '7', '1'];

    return {
        enabled: true,
        executeTime: `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`,
        days,
    };
}

/**
 * 组装 SchedulerConfig 为 cron 表达式（支持星期几字段）
 * @param scheduler 调度配置
 * @param toUTC 是否转换为UTC时间
 */
function buildSchedule(scheduler: SchedulerConfig, toUTC: boolean = false): string {
    if (!scheduler.enabled) return '';

    const [hour, minute] = scheduler.executeTime.split(':');
    const days = scheduler.days && scheduler.days.length === 7 ? '*' : scheduler.days.join(',');

    const localCron = `0 ${minute || '0'} ${hour || '0'} * * ${days}`;

    // 如果需要转换为UTC时间
    return toUTC ? convertCronToUTC(localCron) : localCron;
}


export {
    parseSchedule,
    buildSchedule,
    convertCronToUTC,
    convertCronToLocal,
};

