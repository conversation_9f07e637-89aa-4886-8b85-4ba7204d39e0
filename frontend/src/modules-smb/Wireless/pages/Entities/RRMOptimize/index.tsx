import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, message, Alert } from "antd";
import { useLocation } from "react-router-dom";
import ConfigModal from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/components/ConfigModal";
import ConfirmModal from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/components/ConfirmModal";
import SchedulerModal from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/components/SchedulerModal";
import FailedDeviceModal from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/components/FailedDeviceModal";
import HistoryTable from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/components/HistoryTable";
import { OptimizeHistoryItem, RRMConfig, RRMApiRequest } from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/types";
import { useGetVenue, useUpdateVenue } from '@/modules-smb/hooks/Network/Venues';
import type { SchedulerConfig } from './types';
import '@/modules-smb/Wireless/pages/Entities/RRMOptimize/styles/index.scss';
import '@/modules-smb/Wireless/pages/Devices/Action/Form.scss';
import { fetchRRMTaskRecords, runOptimizeNow } from '@/modules-smb/Wireless/apis/wireless_rrm_api';
import { useTranslation } from 'react-i18next';
import { parseSchedule, buildSchedule, } from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/utils.tsx";

export const PAGE_SIZE = 10;

// 根据url获取当前页面的 venueId
const getVenueIdFromHash = () => {
  if (window.location.hash) {
    return window.location.hash.replace('#', '');
  }
  return '';
};


/**
 * 解析 rrm 字符串为对象
 */
function parseRrm(rrmStr: string): { algorithms: any[]; schedule: string } {
  try {
    return JSON.parse(rrmStr);
  } catch {
    return { algorithms: [], schedule: '' };
  }
}

/**
 * 解析 algorithms 字符串为数组
 */
function parseAlgorithmsStr(algorithmsStr: string): any[] {
  try {
    return JSON.parse(algorithmsStr);
  } catch {
    return [];
  }
}

/**
 * 解析 algorithms 数组为 RRMConfig
 */
function parseAlgorithms(algorithms: any[]): RRMConfig {
  const config: Partial<RRMConfig> = {};
  algorithms.forEach(item => {
    if (item.name === 'OptimizeTxPower') {
      const params = Object.fromEntries(item.parameters.split(',').map((kv: string) => kv.split('=')));
      config.txPowerMode = params.mode?.replace(/"/g, '') || 'measure_ap_ap';
      config.setDifferentTxPowerPerAp = params.setDifferentTxPowerPerAp === 'true';
      config.targetMcs = params.targetMcs ? Number(params.targetMcs) : '8';
      config.coverageThreshold = params.coverageThreshold ? Number(params.coverageThreshold) : undefined;
      config.nthSmallestRssi = params.nthSmallestRssi ? Number(params.nthSmallestRssi) : undefined;
    }
    if (item.name === 'OptimizeChannel') {
      const params = Object.fromEntries(item.parameters.split(',').map((kv: string) => kv.split('=')));
      config.channelMode = params.mode?.replace(/"/g, '') || 'least_used';
      config.setDifferentChannelPerAp = params.setDifferentChannelPerAp === 'true';
    }
  });

  if (config.channelMode && !config.txPowerMode) {
    return {
      channelMode: config.channelMode || 'least_used',
      setDifferentChannelPerAp: config.setDifferentChannelPerAp ?? false,
    }
  }
  else if (!config.channelMode && config.txPowerMode) {
    return {
      txPowerMode: config.txPowerMode || 'measure_ap_ap',
      setDifferentTxPowerPerAp: config.setDifferentTxPowerPerAp ?? false,
      coverageThreshold: config.coverageThreshold,
      nthSmallestRssi: config.nthSmallestRssi,
      targetMcs: config.targetMcs,
    };
  }
  return {
    channelMode: config.channelMode || 'least_used',
    setDifferentChannelPerAp: config.setDifferentChannelPerAp ?? false,

    txPowerMode: config.txPowerMode || 'measure_ap_ap',
    setDifferentTxPowerPerAp: config.setDifferentTxPowerPerAp ?? false,
    coverageThreshold: config.coverageThreshold,
    nthSmallestRssi: config.nthSmallestRssi,
    targetMcs: config.targetMcs,
  };
}

/**
 * 组装 RRMConfig 为 algorithms 数组
 */
function buildAlgorithms(rrmConfig: RRMConfig) {
  // 组装 OptimizeChannel 参数
  let channelParams = `mode=${rrmConfig.channelMode}`;
  if (rrmConfig.channelMode === 'random') {
    channelParams += `,setDifferentChannelPerAp=${rrmConfig.setDifferentChannelPerAp}`;
  }

  // 组装 OptimizeTxPower 参数
  let txPowerParams = `mode=${rrmConfig.txPowerMode}`;
  if (rrmConfig.txPowerMode === 'random') {
    txPowerParams += `,setDifferentTxPowerPerAp=${rrmConfig.setDifferentTxPowerPerAp}`;
  } else if (rrmConfig.txPowerMode === 'measure_ap_client') {
    txPowerParams += `,targetMcs=${rrmConfig.targetMcs}`;
  } else if (rrmConfig.txPowerMode === 'measure_ap_ap') {
    txPowerParams += `,coverageThreshold=${rrmConfig.coverageThreshold},nthSmallestRssi=${rrmConfig.nthSmallestRssi}`;
  } else if (rrmConfig.txPowerMode === 'location_optimal') {
    // location_optimal模式可能需要特定参数，目前只传mode
    // 如果需要其他参数，可以在这里添加
  }

  if (rrmConfig.channelMode && !rrmConfig.txPowerMode) {
    return [
      {
        name: 'OptimizeChannel',
        parameters: channelParams
      }
    ];
  } else if (!rrmConfig.channelMode && rrmConfig.txPowerMode) {
    return [
      {
        name: 'OptimizeTxPower',
        parameters: txPowerParams
      }
    ];
  }
  return [
    {
      name: 'OptimizeChannel',
      parameters: channelParams
    },
    {
      name: 'OptimizeTxPower',
      parameters: txPowerParams
    }
  ];
}
/**
 * 组装 RRMConfig 为 algorithms 数组
 */
function buildRunAlgorithms(rrmConfig: RRMConfig) {
  // 组装 OptimizeChannel 参数
  let channelParams = `mode=${rrmConfig.channelMode}`;
  if (rrmConfig.channelMode === 'random') {
    channelParams += `,setDifferentChannelPerAp=${rrmConfig.setDifferentChannelPerAp}`;
  }

  // 组装 OptimizeTxPower 参数
  let txPowerParams = `mode=${rrmConfig.txPowerMode}`;
  if (rrmConfig.txPowerMode === 'random') {
    txPowerParams += `,setDifferentTxPowerPerAp=${rrmConfig.setDifferentTxPowerPerAp}`;
  } else if (rrmConfig.txPowerMode === 'measure_ap_client') {
    txPowerParams += `,targetMcs=${rrmConfig.targetMcs}`;
  } else if (rrmConfig.txPowerMode === 'measure_ap_ap') {
    txPowerParams += `,coverageThreshold=${rrmConfig.coverageThreshold},nthSmallestRssi=${rrmConfig.nthSmallestRssi}`;
  } else if (rrmConfig.txPowerMode === 'location_optimal') {
    // location_optimal模式可能需要特定参数，目前只传mode
    // 如果需要其他参数，可以在这里添加
  }

  if (rrmConfig.channelMode && !rrmConfig.txPowerMode) {
    return [
      {
        algorithm: 'OptimizeChannel',
        args: channelParams
      },
    ];
  } else if (!rrmConfig.channelMode && rrmConfig.txPowerMode) {
    return [
      {
        algorithm: 'OptimizeTxPower',
        args: txPowerParams
      }
    ];
  }

  return [
    {
      algorithm: 'OptimizeChannel',
      args: channelParams
    },
    {
      algorithm: 'OptimizeTxPower',
      args: txPowerParams
    }
  ];
}


const RRMOptimize = () => {
  const { t } = useTranslation();

  const location = useLocation();

  // 弹窗控制
  const [configVisible, setConfigVisible] = useState(false);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [schedulerVisible, setSchedulerVisible] = useState(false);
  const [failedModal, setFailedModal] = useState<{ visible: boolean; historyId: string | null }>({ visible: false, historyId: null });

  // 历史数据
  const [history, setHistory] = useState<OptimizeHistoryItem[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(PAGE_SIZE);
  const [loading, setLoading] = useState(false);
  // 排序状态
  const [sortField, setSortField] = useState<string>('create_time');
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>('descend');

  // 使用state管理venueId，支持动态更新
  const [venueId, setVenueId] = useState(() => getVenueIdFromHash());


  // 获取 venue 配置
  // let venueData = useGetVenue({ id: venueId });
  const { data: venue, refetch: refetchVenue, isLoading: venueLoading } = useGetVenue({ id: venueId });
  const updateVenue = useUpdateVenue({ id: venueId });

  // 刷新历史数据的函数
  const loadHistory = useCallback(async (
    p = page,
    ps = pageSize,
    sortFieldParam = sortField,
    sortOrderParam = sortOrder
  ) => {
    setLoading(true);
    try {
      const res = await fetchRRMTaskRecords({
        siteId: venueId,
        sortBy: sortFieldParam || 'create_time',
        sortType: sortOrderParam === 'ascend' ? 'asc' : 'desc',
        pageNum: p,
        pageSize: ps,
      });
      // 直接使用后端字段
      setHistory(res.info || []);
      setTotal(res.total || 0);
      // 更新分页状态
      setPage(p);
      setPageSize(ps);
    } catch {
      message.error("Failed to fetch optimization history");
    } finally {
      setLoading(false);
    }
  }, [venueId, page, pageSize, sortField, sortOrder]);

  // 监听URL hash变化，当站点发生变化时重新获取站点ID
  useEffect(() => {
    const currentVenueId = getVenueIdFromHash();

    if (currentVenueId !== venueId) {
      console.log(`VenueId changed from ${venueId} to ${currentVenueId}`);
      setVenueId(currentVenueId);
    }
  }, [location.hash, venueId]);

  // 当venueId变化时，重新获取历史数据
  // venue配置会通过useGetVenue自动更新，无需手动refetch
  useEffect(() => {
    if (venueId) {
      console.log(`Loading history data for venueId: ${venueId}`);
      // 只需要重新加载历史数据，venue数据由useGetVenue自动处理
      loadHistory(1, pageSize);
    }
  }, [venueId, pageSize, refetchVenue]); // 移除loadHistory依赖，避免无限循环


  // 解析 rrm 字符串为对象
  const rrmObj = React.useMemo(() => {
    // 如果 rrm 值为 "no"，则解析 algorithms 字段
    if (venue?.deviceRules?.rrm === 'no' && venue?.deviceRules?.algorithms) {
      return { 
        algorithms: parseAlgorithmsStr(venue.deviceRules.algorithms), 
        schedule: '' 
      };
    }
    
    // 否则按原来的方式解析 rrm 字段
    if (!venue?.deviceRules?.rrm || venue.deviceRules.rrm === 'inherit' || venue.deviceRules.rrm === 'off') {
      return { algorithms: [], schedule: '' };
    }
    return parseRrm(venue.deviceRules.rrm);
  }, [venue]);

  // 解析为 RRMConfig/SchedulerConfig
  const rrmConfig = React.useMemo(() => parseAlgorithms(rrmObj.algorithms || []), [rrmObj]);
  const schedulerConfig = React.useMemo(() => parseSchedule(rrmObj.schedule || '', true), [rrmObj]);

  // 受控state
  const [rrmForm, setRrmForm] = useState<RRMConfig>(rrmConfig);
  const [schedulerForm, setSchedulerForm] = useState<SchedulerConfig>(schedulerConfig);

  // 拆分算法配置和定时配置
  const [algorithms, setAlgorithms] = useState(rrmObj.algorithms || []);
  const [schedule, setSchedule] = useState(rrmObj.schedule || '');

  // 监听 venue 变化，重置 state
  useEffect(() => {
    setAlgorithms(rrmObj.algorithms || []);
    setSchedule(rrmObj.schedule || '');
    setRrmForm(rrmConfig);
    setSchedulerForm(schedulerConfig);
  }, [rrmObj, rrmConfig, schedulerConfig]);

  // // 算法配置变更
  // const handleConfigChange = (form: RRMConfig) => setRrmForm(form);
  // // 定时配置变更
  // const handleSchedulerChange = (form: SchedulerConfig) => setSchedulerForm(form);

  // Config/Scheduler弹窗apply时直接传递最新表单值
  const handleConfigApply = async (newConfig: RRMConfig) => {
    const algorithms = buildAlgorithms(newConfig);
    const schedule = buildSchedule(schedulerForm, true); // 用当前定时配置
    const rrm = schedulerForm.enabled
      ? { algorithms, schedule }
      : { algorithms }; // 空对象
    // const rrm = JSON.stringify(rrmObj);
    try {
      await updateVenue.mutateAsync({ 
        params: { 
          deviceRules: { 
            ...venue.deviceRules, 
            rrm,
            algorithms: algorithms  // 同时更新 algorithms 字段
          } 
        } 
      });
      refetchVenue();
      message.success('save successfully');
      setConfigVisible(false);
      return true;
    } catch (e) {
      message.error('Failed to save the configuration');
      return false;
      // 不关闭弹窗
    }
  };
  const handleSchedulerApply = async (newScheduler: SchedulerConfig) => {
    const algorithms = buildAlgorithms(rrmForm); // 用当前算法配置
    const schedule = buildSchedule(newScheduler, true);
    const rrm = newScheduler.enabled
      ? { algorithms, schedule }
      : { algorithms }; // 空对象
    // const rrm = JSON.stringify(rrmObj);
    // const rrm = { algorithms, schedule, vendor }
    try {
      // 新增校验：在开启定时任务时,必须选择周几
      if (newScheduler.enabled && (!newScheduler.days || newScheduler.days.length === 0)) {
        message.error('Please select at least one week of execution!');
        return false; // 阻止弹窗关闭
      }
      await updateVenue.mutateAsync({ 
        params: { 
          deviceRules: { 
            ...venue.deviceRules, 
            rrm,
            algorithms: algorithms  // 同时更新 algorithms 字段
          } 
        } 
      });
      refetchVenue();
      message.success('save successfully');
      setSchedulerVisible(false); // 只在成功时关闭弹窗
      return true;
    } catch (e) {
      message.error('Failed to save the configuration');
      return false;
      // 不关闭弹窗
    }
  };

  // 处理排序变化
  const handleSortChange = (sorter: { field: string; order: 'ascend' | 'descend' | null }) => {
    setSortField(sorter.field);
    setSortOrder(sorter.order);
    // 重置到第一页
    loadHistory(1, pageSize, sorter.field, sorter.order);
  };



  // 立即优化
  const handleOptimizeNow = async () => {
    setConfirmVisible(false);

    // 显示加载状态
    // const loadingMessage = message.loading('Optimization in progress...', 0);

    try {
      // 构造API请求参数
      const algorithmsParam = buildRunAlgorithms(rrmConfig)

      const apiParams = {
        parameter: algorithmsParam,
        siteId: venueId
      };

      // 发送优化请求
      const result = await runOptimizeNow(apiParams);

      console.log('Optimization result:', result);
      if (result.status == 300) {
        message.error('Optimization in progress...');
      } else if (result.status == 200) {
        message.success('The task was submitted successfully');
      } else {
        message.error('Failed to submit the task');
      }

    } catch (error: any) {
      // loadingMessage(); // 关闭加载提示
      console.error("Failed to start optimization:", error);
      // message.error('Failed to start optimization');

    } finally {
      // 刷新历史记录
      setTimeout(() => {
        loadHistory(1, pageSize);
      }, 2000);
    }
  };

  return (
    <div className="rrm-optimize-root">
      <div className="rrm-optimize-header">
        <div>
          <h2><strong>WLAN Optimization</strong></h2>
        </div>
        <a
          href="#"
          onClick={e => { e.preventDefault(); setConfigVisible(true); }}
        >
          <span>&#9776;</span> Optimization Config
        </a>
      </div>
      <div className="rrm-optimize-desc">
        With the WLAN optimization service, the organization will determine the optimum operation channels and power concluded from the scanning, considering the traffic, deployment size, and client factors.
      </div>
      {/* <div className="rrm-optimize-alert" > */}
      {/* <Alert
        className="rrm-optimize-alert"
        type="info"
        showIcon
        closable
        message={
          <span>
            <b>Note:</b> The connection to internet will be lost for several minutes during the scanning and optimization. Please select a spare time of network to start scanning.
          </span>
        }
      /> */}

      <Alert
        className="custom-trace-alert"
        // message={t('commands.factory_reset_warning')}
        message='Note: The connection to internet will be lost for several minutes during the scanning and optimization. Please select a spare time of network to start scanning.'
        type="info"
        showIcon
        closable
      // style={{ marginTop: 10, marginBottom: 20 }}
      />
      {/* </div> */}


      <div className="rrm-optimize-actions">
        <Button
          type="primary"
          onClick={() => setConfirmVisible(true)}
        >
          Optimization Now
        </Button>
        <Button
          type="default"
          onClick={() => setSchedulerVisible(true)}
        >
          Optimization Scheduler
        </Button>
      </div>
      <hr />
      <div className="rrm-optimize-history">
        <h3>Optimization History</h3>
        <HistoryTable
          data={history}
          total={total}
          page={page}
          pageSize={pageSize}
          onPageChange={(p: number, ps: number) => loadHistory(p, ps)}
          onSortChange={handleSortChange}
          onShowFailed={(historyId: string) => setFailedModal({ visible: true, historyId })}
        />
      </div>
      <ConfigModal
        visible={configVisible}
        onClose={() => setConfigVisible(false)}
        config={rrmForm}
        // onChange={handleConfigChange}
        onApply={handleConfigApply}
      />
      <SchedulerModal
        visible={schedulerVisible}
        onClose={() => setSchedulerVisible(false)}
        config={schedulerForm}
        // onChange={handleSchedulerChange}
        onApply={handleSchedulerApply}
      />
      <ConfirmModal
        visible={confirmVisible}
        onOk={handleOptimizeNow}
        onCancel={() => setConfirmVisible(false)}
      />

      <FailedDeviceModal
        visible={failedModal.visible}
        historyId={failedModal.historyId || ''}
        onClose={() => setFailedModal({ visible: false, historyId: null })}
      />
    </div>
  );
};

export default RRMOptimize;