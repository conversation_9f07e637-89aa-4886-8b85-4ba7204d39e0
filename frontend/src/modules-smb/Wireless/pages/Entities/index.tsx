import React, { useEffect, useState, useMemo } from "react";
import { <PERSON>bs, Button, Space, Tooltip, Flex, message } from "antd";
import Icon from "@ant-design/icons";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

import StartMonitorModal from "./StartMonitorModal";
import ViewMonitoringModal from "./ViewMonitoringModal";
import { useEntityFavorite } from "@/modules-smb/hooks/useEntityFavorite";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import VenueSelect from "@/modules-smb/Wireless/components/VenueSelect";
import MonitorPage from "./Monitor";
import Configure from "./Configure";
import RRMOptimize from "./RRMOptimize";
import { useGetVenue } from "@/modules-smb/hooks/Network/Venues";
import { useGetAnalyticsBoards } from "@/modules-smb/Wireless/hooks/Network/Analytics";
import VenueActions from "./VenueActions";

import monitorSvg from "../../assets/Entities/venue_monitor.svg?react";
import setttingSvg from "../../assets/Entities/venue_setting.svg?react";
import refreshSvg from "../../assets/Entities/venue_refresh.svg?react";
import NoMonitoringPrompt from "@/modules-smb/Wireless/pages/Entities/Monitor/NoMonitoringPrompt";
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";
import '@/modules-smb/Wireless/assets/tabs.scss';
import "./index.scss";

const VenuePage = () => {
    const currentUser = useSelector((state: any) => state.user.userInfo);
    const location = useLocation();
    const navigate = useNavigate();
    const { selectedSiteId, setSelectedSiteId } = useSiteStore();
    const [currentActiveKey, setCurrentActiveKey] = useState("");

    const { getFirstVenueFavoriteId } = useEntityFavorite({
        id: "",
        type: "venue"
    });

    const selectedVenueId = useMemo(() => {
        const hash = window.location.hash.substring(1);

        return selectedSiteId || hash || getFirstVenueFavoriteId?.() || "0";
    }, [selectedSiteId, location.hash]);

    useEffect(() => {
        if (selectedSiteId && !window.location.hash.includes(selectedSiteId)) {
            navigate(`${location.pathname}#${selectedSiteId}`, { replace: true });
        }
    }, [selectedSiteId]);

    const [refreshKey, setRefreshKey] = useState(0);
    const [monitorModalVisible, setMonitorModalVisible] = useState(false);
    const [settingsModalVisible, setSettingsModalVisible] = useState(false);
    const [showMonitorPrompt, setShowMonitorPrompt] = useState(false);
    const getVenue = useGetVenue({ id: selectedVenueId || "0" });
    const getAnalyticsBoards = useGetAnalyticsBoards({ venueId: selectedVenueId || "0" });
    const hasMonitoring = getVenue.data?.boards?.length > 0;
    const boardId = getVenue.data?.boards?.[0] || "";
    const { resetFromOtherPage } = useSiteStore();
    useEffect(() => {
        resetFromOtherPage();
    }, []);

    const handleTurnOnMonitoring = () => {
        setMonitorModalVisible(true);
    };

    useEffect(() => {
        if (getAnalyticsBoards.data) {
            setShowMonitorPrompt(!(getAnalyticsBoards.data.boards?.length > 0));
        }
    }, [getAnalyticsBoards.data]);

    const handleChange = (value: string | string[]) => {
        const id = Array.isArray(value) ? value[0] : value;
        setSelectedSiteId(id);
        navigate(`${location.pathname}#${id}`);
    };

    const handleMonitor = () => {
        if (!selectedVenueId) {
            message.warning("Please select a venue first");
            return;
        }
        getVenue.refetch();
        const hasMon = getVenue.data?.boards?.length > 0;
        if (hasMon) {
            setSettingsModalVisible(true);
        } else {
            setMonitorModalVisible(true);
        }
    };

    const handleSetting = (action: string) => {
    };

    const handleRefresh = () => {
        setRefreshKey(prev => prev + 1);
        getVenue.refetch();
        message.success("Successfully refreshed");
    };

    // useEffect(() => {
    //     const handleHashChange = () => {
    //         const id = window.location.hash.substring(1);
    //         if (id) {
    //             setSelectedVenueId(id);

    //         }
    //     };
    //     window.addEventListener("hashchange", handleHashChange);
    //     handleHashChange();
    //     return () => {
    //         window.removeEventListener("hashchange", handleHashChange);
    //     };
    // }, []);
    const MonitorPageWithVenue = React.useMemo(() => {
        return () => <MonitorPage venueId={selectedVenueId} venueData={getVenue.data} />;
    }, [selectedVenueId]);

    // useEffect(() => {
    //     getAnalyticsBoards.refetch();
    // });

    const allItems = [
        {
            key: "Monitor",
            label: "Monitor",
            children: showMonitorPrompt ? (
                <NoMonitoringPrompt onTurnOn={handleTurnOnMonitoring} />
            ) : (
                <ProtectedRoute component={MonitorPageWithVenue} />
            ),
        },
        {
            key: "Configure",
            label: "Configure",
            children: <ProtectedRoute component={Configure} />,
        },
        {
            key: "RRM-Optimize",
            label: "RRM Optimize",
            children: <ProtectedRoute component={RRMOptimize} />,
        },
    ];

    const items: any[] = currentUser.type === "readonly" ? [] : allItems;


    useEffect(() => {
        const match = location.pathname.match(/(Monitor|Configure|RRM-Optimize)$/);
        if (match) {
            setCurrentActiveKey(match[0]);
        } else if (items.length > 0) {
            setCurrentActiveKey(items[0].key);
            let path = `${location.pathname.replace(/\/$/, "")}/${items[0].key}`;
            if (selectedSiteId) {
                path += `#${selectedSiteId}`;
            }
            navigate(path, { replace: true });
        }
    }, [location.pathname, items, selectedSiteId]);

    const onChange = (key: string) => {
        let path = location.pathname.replace(/(Monitor|Configure|RRM-Optimize)$/, "");
        path = `${path.replace(/\/$/, "")}/${key}`;
        if (selectedVenueId) {
            path += `#${selectedVenueId}`;
        }
        navigate(path);
    };

    return (
        <Flex className="div-main">
            <div className="div-header">
                <VenueSelect onChange={handleChange} />
                <Space className="action-buttons" style={{ paddingTop: 10 }}>
                    <Tooltip title="Monitoring" placement="bottom">
                        <Button
                            icon={<Icon component={monitorSvg} />}
                            type="text"
                            onClick={handleMonitor}
                            style={{ width: 40, height: 40, borderRadius: 4 }}
                            className="monitor-button"
                        />
                    </Tooltip>
                    <VenueActions
                        venueId={selectedVenueId}
                        isDisabled={!getVenue.data}
                    />
                    <Tooltip title="Refresh" placement="bottom">
                        <Button
                            icon={<Icon component={refreshSvg} />}
                            type="text"
                            onClick={handleRefresh}
                            style={{ width: 40, height: 40, borderRadius: 4 }}
                            className="refresh-button"
                        />
                    </Tooltip>
                </Space>
            </div>

            <div className="div-tabs">
                <Tabs
                    key={refreshKey}
                    activeKey={currentActiveKey}
                    onChange={onChange}
                    destroyInactiveTabPane
                    items={items}
                />
                {selectedVenueId && (
                    <>
                        <StartMonitorModal
                            id={selectedVenueId}
                            visible={monitorModalVisible}
                            onClose={() => {
                                setMonitorModalVisible(false);
                            }}
                            onApplySuccess={setShowMonitorPrompt}
                        />
                        <ViewMonitoringModal
                            boardId={boardId}
                            venueId={selectedVenueId}
                            visible={settingsModalVisible}
                            onClose={() => setSettingsModalVisible(false)}
                            onApplySuccess={setShowMonitorPrompt}
                        />
                    </>
                )}
            </div>

        </Flex>
    );
};

export default VenuePage;
