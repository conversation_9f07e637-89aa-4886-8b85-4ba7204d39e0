import * as React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Button,
  Tooltip,
  Spin,
  Typography,
  Flex,
  Empty
} from 'antd';
import { JsonViewer } from '@textea/json-viewer';
import { useTranslation } from 'react-i18next';
import LoadingOverlay from '@/modules-smb/components/LoadingOverlay';
import { useGetDeviceLastStats } from '@/modules-smb/hooks/Network/Statistics';
import { useDisclosure } from '@chakra-ui/react';
import { useClipboard } from '@chakra-ui/react';
import { useColorMode } from '@chakra-ui/react';
import lastStatisticsIcon from '@/modules-smb/Wireless/assets/Devices/last_statistics.png';
import { AmpConCustomModal } from '@/modules-ampcon/components/custom_table';
import '@/modules-smb/Wireless/pages/Entities/Configure/Layout/Advance/collapse.css';
import EmptyPic from "@/assets/images/App/empty.png";
import "@/modules-smb/Wireless/assets/wireless.scss";
const { Title, Text } = Typography;
type Props = {
  serialNumber: string;
};

const EmptyState = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '300px',
    width: '100%'
  }}>
    <Empty
      image={EmptyPic}
      description="No Data"
      imageStyle={{ marginTop: 16, marginBottom: 0 }}
    />
  </div>
);

const ViewLastStatsModal = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const getLastStats = useGetDeviceLastStats({ serialNumber });
  const { colorMode } = useColorMode();

  const modalContent = (
     <div className="collapse-modal">
      {getLastStats.isFetching ? (
        <Flex justify="center" align="center" style={{ height: '300px' }}>
          <Spin size="large" />
        </Flex>
      ) : !getLastStats.data ? (
        <EmptyState />
      ) : (
        <LoadingOverlay isLoading={getLastStats.isFetching}>

          <Collapse
            // style={{ marginBottom: 0, border: "none" }}
            expandIconPosition="right"
            bordered={false}
            defaultActiveKey={'1'}
            style={{ background: '#FFFFFF',marginTop:"12px"}}
          >
            <Collapse.Panel
              key="1"
              header={<h3 style={{ fontSize: "16px", margin: 0, border: "none",fontWeight: 600 }}>  {t('common.preview')}</h3>}
            >
              <JsonViewer
                rootName={false}
                displayDataTypes={false}
                enableClipboard={false}
                theme={colorMode === 'light' ? undefined : 'dark'}
                defaultInspectDepth={1}
                value={getLastStats.data as object}
                style={{ background: 'unset', display: 'unset', }}
              />
            </Collapse.Panel>
       
            <Collapse.Panel
              key="2"
              header={<h3 style={{ fontSize: "16px", margin: 0, border: "none",fontWeight: 600 }}>  {t('analytics.raw_data')}</h3>}
            >
          <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
            {JSON.stringify(getLastStats.data, null, 2)}
          </pre>
            </Collapse.Panel>
          

          </Collapse>


        </LoadingOverlay>
      )}

    </div>
  );

  return (
    <>
      <Tooltip title={t('statistics.last_stats')}>
        <Button
          type='primary'
          onClick={onOpen}
          size="middle"
          icon={
            <img
              src={lastStatisticsIcon}
              alt="Last Statistics"
              style={{
                width: 16,
                height: 16,
                verticalAlign: 'middle',
                transform: 'translateY(-1px)'
              }}
            />
          }
        >
          {t('statistics.last_stats')}
        </Button>
      </Tooltip>

      <AmpConCustomModal
        title={t('statistics.last_stats')}
        childItems={modalContent}
        isModalOpen={isOpen}
        onCancel={onClose}
        modalClass="ampcon-max-modal"
        footer={null}
      />
    </>
  );
};

export default ViewLastStatsModal;
