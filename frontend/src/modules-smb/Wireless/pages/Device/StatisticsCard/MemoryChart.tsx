import * as React from 'react';
import { useColorMode } from '@chakra-ui/react';
import * as echarts from 'echarts';
import {
  formatDateTime,
  formatFullDateTime,
  getVisibleLabelIndices,
} from './chartUtils';

type Props = {
  data: {
    used: number[];
    buffered: number[];
    cached: number[];
    free: number[];
    total: number[];
    recorded: number[];
  };
};

const DeviceMemoryChart: React.FC<Props> = ({ data }) => {
  const { colorMode } = useColorMode();
  const isDarkMode = colorMode === 'dark';
  const chartRef = React.useRef<HTMLDivElement>(null);
  let chartInstance: echarts.ECharts | null = null;

  const FREE_COLOR = '#249EFF';
  const CACHE_COLOR = '#21CCFF';
  const BUFFERED_COLOR = '#756FF3';

  // 计算可见X轴标签索引
  const visibleIndices = getVisibleLabelIndices(data.recorded.length);

  // 处理数据系列（保持原有内存单位转换逻辑）
  const processDataSeries = () => {
    const freeAreaColor = new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      { offset: 0, color: 'rgba(36, 158, 255, 0.5)' }, 
      { offset: 1, color: 'rgba(36, 158, 255, 0)' }   
    ]);
    const cacheAreaColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: 'rgba(33, 204, 255, 0.5)' }, 
      { offset: 1, color: 'rgba(33, 204, 255, 0)' }   
    ]);
    const bufferedAreaColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: 'rgba(117, 111, 243, 0.5)' }, 
      { offset: 1, color: 'rgba(117, 111, 243, 0)' }    
    ]);

    // 转换数据为MB单位
    const processData = (dataArray: number[]) =>
      dataArray.map(value => Math.floor(value / 1024 / 1024));

    return [
      {
        name: 'Free',
        data: processData(data.free),
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: { color: FREE_COLOR },
        areaStyle: { color: freeAreaColor },
        itemStyle: { color: FREE_COLOR },
      },
      {
        name: 'Cached',
        data: processData(data.cached),
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: { color: CACHE_COLOR },
        areaStyle: { color: cacheAreaColor },
        itemStyle: { color: CACHE_COLOR },
      },
      {
        name: 'Buffered',
        data: processData(data.buffered),
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: { color: BUFFERED_COLOR },
        areaStyle: { color: bufferedAreaColor }, // 应用渐变
        itemStyle: { color: BUFFERED_COLOR },
      }
    ];
  };

  // 生成ECharts配置项
  const getOption = (): echarts.EChartsOption => {
    const textColor = isDarkMode ? 'white' : '#333';
    const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

    return {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: isDarkMode ? '#333' : '#fff',
            color: textColor
          }
        },
        formatter: (params: any[]) => {
          // 从原始数据获取时间戳，避免依赖X轴标签
          const timestamp = data.recorded[params[0].dataIndex];
          let result = formatFullDateTime(timestamp) + '<br/>';
          params.forEach(param => {
            result += `<span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${param.color};margin-right:5px;"></span>${param.seriesName}: ${param.value} MB<br/>`;
          });
          return result;
        }
      },
      legend: {
        bottom: 0,
        left: 'center',
        textStyle: { color: textColor },
        data: [
          { name: 'Free', itemStyle: { color: FREE_COLOR } },
          { name: 'Cached', itemStyle: { color: CACHE_COLOR } },
          { name: 'Buffered', itemStyle: { color: BUFFERED_COLOR } }
        ],
        icon: 'roundRect',
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 15
      },
      grid: {
        left: '1%',
        right: '2%',
        top: '32px',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        // 仅对可见索引的标签格式化，其他留空
        data: data.recorded.map((timestamp, index) =>
          visibleIndices.includes(index) ? formatDateTime(timestamp) : ''
        ),
        axisLine: { lineStyle: { color: gridColor } },
        axisTick: {
          show: true, // 显示刻度线
          alignWithLabel: true, // 刻度线与标签对齐
          interval: (index: number) => visibleIndices.includes(index) // 仅显示可见标签的刻度
        },
        axisLabel: {
          color: textColor,
          rotate: 0, // 水平显示（因标签数量少，无需旋转）
          padding: [0, 0, 0, 15],// 增加顶部内边距，避免与刻度重叠
          interval: (index: number) => visibleIndices.includes(index) // 仅显示可见标签
        },
        splitLine: { lineStyle: { color: gridColor } }
      },
      yAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: gridColor } },
        axisLabel: {
          color: textColor,
          margin: 32,
          formatter: (value: number) => `${value} MB`
        },
        splitLine: { lineStyle: { color: gridColor } }
      },
      series: processDataSeries()
    };
  };

  // 初始化图表
  React.useEffect(() => {
    if (chartRef.current) {
      if (chartInstance) {
        echarts.dispose(chartInstance);
      }
      chartInstance = echarts.init(chartRef.current);
      chartInstance.setOption(getOption());
    }

    // 响应窗口大小变化
    const handleResize = () => {
      chartInstance?.resize();
    };
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance) {
        echarts.dispose(chartInstance);
        chartInstance = null;
      }
    };
  }, [data, isDarkMode]);

  // 数据更新时刷新图表
  React.useEffect(() => {
    if (chartInstance) {
      chartInstance.setOption(getOption());
    }
  }, [data, isDarkMode]);

  return (
    <div
      ref={chartRef}
      style={{ width: '100%', height: '100%', minHeight: '300px' }}
    />
  );
};

export default React.memo(DeviceMemoryChart);