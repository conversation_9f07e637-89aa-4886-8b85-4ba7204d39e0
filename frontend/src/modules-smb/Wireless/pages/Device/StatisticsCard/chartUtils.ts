import type { Dayjs } from 'dayjs';

/**
 * 格式化时间戳为「月/日 时:分」（用于X轴标签）
 * @param timestamp - 秒级时间戳
 * @returns 格式化后的时间字符串
 */
export const formatDateTime = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${month}/${day} ${hours}:${minutes}`;
};

/**
 * 格式化时间戳为完整「月/日 时:分」（用于Tooltip）
 * @param timestamp - 秒级时间戳
 * @returns 格式化后的完整时间字符串
 */
export const formatFullDateTime = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${month}/${day} ${hours}:${minutes}`;
};

/**
 * 计算X轴可见标签索引（确保仅显示5-6个节点）
 * @param totalCount - 数据总长度
 * @returns 可见索引数组
 */
export const getVisibleLabelIndices = (totalCount: number): number[] => {
  if (totalCount <= 6) {
    return Array.from({ length: totalCount }, (_, i) => i);
  }
  const step = Math.ceil(totalCount / 5); // 按5个节点计算步长，最终显示5-6个
  const indices: number[] = [];
  for (let i = 0; i < totalCount; i += step) {
    indices.push(i);
  }
  // 确保最后一个节点始终显示
  if (!indices.includes(totalCount - 1)) {
    indices.push(totalCount - 1);
  }
  return indices;
};

/**
 * 计算字节单位转换因子（B/KB/MB/GB）
 * @param maxBytes - 最大字节数
 * @returns 转换因子和单位
 */
export const getDivisionFactor = (maxBytes: number): { factor: number; unit: string } => {
  if (maxBytes < 1024) {
    return { factor: 1, unit: 'B' };
  }
  if (maxBytes < 1024 * 1024) {
    return { factor: 1024, unit: 'KB' };
  }
  if (maxBytes < 1024 * 1024 * 1024) {
    return { factor: 1024 * 1024, unit: 'MB' };
  }
  return { factor: 1024 * 1024 * 1024, unit: 'GB' };
};

/**
 * 计算数据包单位转换因子（无单位/K/M/G）
 * @param maxPackets - 最大数据包数量
 * @returns 转换因子和单位
 */
export const getDivisionFactorPackets = (maxPackets: number): { factor: number; unit: string } => {
  if (maxPackets < 1000) {
    return { factor: 1, unit: '' };
  }
  if (maxPackets < 1000 * 1000) {
    return { factor: 1000, unit: 'K' };
  }
  if (maxPackets < 1000 * 1000 * 1000) {
    return { factor: 1000 * 1000, unit: 'M' };
  }
  return { factor: 1000 * 1000 * 1000, unit: 'G' };
};

