import React, { useMemo, useEffect, useState } from "react";
import { Card, Row, Col, Typography, Image, Tag, Alert, message } from "antd";
import { Cloud } from "@phosphor-icons/react";
import ReactCountryFlag from "react-country-flag";
import { useTranslation } from "react-i18next";
import { ZoomInOutlined } from "@ant-design/icons";
import type { AxiosError } from 'axios';
import FormattedDate from "@/modules-smb/components/InformationDisplays/FormattedDate";
import COUNTRY_LIST from "@/modules-smb/constants/countryList";
import { compactDate, compactSecondsToDetailed } from "@/modules-smb/helpers/dateFormatting";
import { bytesString, getRevision, uppercaseFirstLetter } from "@/modules-smb/helpers/stringHelper";
import { useGetDevice, useGetDeviceStatus } from "@/modules-smb/Wireless/hooks/Network/Devices";
import { useGetDeviceLastStats } from "@/modules-smb/hooks/Network/Statistics";
import { ResponsiveTag } from "@/modules-smb/Wireless/components/Containers/ResponsiveTag";

import InfoRow from "./InfoRow";
import "./Status.scss"

const { Title, Text } = Typography;

type Props = {
  serialNumber: string;
};

const ICON_STYLE: React.CSSProperties = { width: 24, height: 24, marginRight: 8 };

const DeviceSummary: React.FC<Props> = ({ serialNumber }) => {
  const { t } = useTranslation();
  const getDevice = useGetDevice({
    serialNumber,
    disableToast: true,
    onError: () => { }
  });
  const getStatus = useGetDeviceStatus({
    serialNumber,
    onError: () => { }
  });
  const getStats = useGetDeviceLastStats({
    serialNumber,
    onError: () => { }
  });
  const [iconPos, setIconPos] = useState({ x: 0, y: 0 });
  const [hovering, setHovering] = useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    setIconPos({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });
  };

  const handleMouseEnter = () => setHovering(true);
  const handleMouseLeave = () => setHovering(false);



  const getMemory = () => {
    if (getStats.data?.unit?.memory) {
      const usedMemory = getStats.data.unit.memory.total - getStats.data.unit.memory.free;
      const memoryUsedPct =
        getStats.data?.unit?.memory.total > 0 ? (usedMemory / getStats.data.unit.memory.total) * 100 : 0;
      const colorMap: Record<
        "green" | "yellow" | "red",
        { color: string; backgroundColor: string; border: string }
      > = {
        green: {
          color: "#2BC174",
          backgroundColor: "rgba(43, 193, 116, 0.1)",
          border: "1px solid #2BC174",
        },
        yellow: {
          color: "#FFB020",
          backgroundColor: "rgba(255, 176, 32, 0.1)",
          border: "1px solid #FFB020",
        },
        red: {
          color: "#F54E45",
          backgroundColor: "rgba(245, 78, 69, 0.1)",
          border: "1px solid #F54E45",
        },
      };
      let level: "green" | "yellow" | "red" = "red";
      if (memoryUsedPct < 60) level = "green";
      else if (memoryUsedPct < 85) level = "yellow";

      return (
        <>
          {bytesString(getStats.data.unit.memory.total)}
          <Tag style={{ marginLeft: 8, ...colorMap[level] }}>
            {Math.floor(memoryUsedPct * 100) / 100}% {t("controller.stats.used")}
          </Tag>
        </>
      );
    }
    return "-";
  };

  const getDeviceCompatible = () => {
    if (!getDevice.data?.compatible) return undefined;
    if (getDevice.data.compatible.includes(" ")) {
      return getDevice.data.compatible.replaceAll(" ", "_");
    }
    return getDevice.data?.compatible;
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: 10, marginTop: -24 }}>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: 100 }}>
        <div
          className="device-image-wrapper"
          onMouseMove={handleMouseMove}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <Image
            src={`/devices/${getDeviceCompatible()}.png`}
            alt={getDevice?.data?.compatible}
            width={140}
            height={140}
            style={{marginTop: 5}}
            fallback="devices/generic_ap.png"
            preview={{
              mask: (
                hovering && (
                  <div
                    className="device-preview-mask"
                    style={{
                      left: iconPos.x,
                      top: iconPos.y,
                    }}
                  >
                    <ZoomInOutlined />
                  </div>
                )
              ),
            }}
          />
        </div>
        <div style={{ display: 'flex', textAlign: 'center', alignItems: 'center', marginLeft:15, marginTop: 10 }}>
          {getDevice.data?.blackListed ? (
            <ResponsiveTag
              label={t("Denied")}
              tooltip={t("devices.blacklisted_description")}
              colorScheme="red"
              style={{
                background: "rgba(245,63,63,0.1)",
                border: "1px solid #F53F3F",
                color: "#F53F3F",
              }}
            />
          ) : getStatus?.data?.connected ? (
            <ResponsiveTag
              label={t("Connected")}
              colorScheme="green"
              style={{
                background: "rgba(43,193,116,0.1)",
                border: "1px solid #2BC174",
                color: "#2BC174",
              }}
              icon={null}
            />
          ) : (
            <ResponsiveTag
              label={t("Disconnected")}
              colorScheme="red"
              style={{
                background: '#F4F5F7',
                border: '1px solid #DADCE1',
                color: '#B3BBC8'
              }}
              icon={null}
            />
          )}


          {getDevice.data?.restrictedDevice && (
            <ResponsiveTag
              label={`${t("devices.restricted")} (Dev Mode)`}
              tooltip={t("devices.restricted_overriden")}
              colorScheme="green"
            />
          )}
        </div>
      </div>

      <div style={{ width: 1, height: 100, background: '#F2F2F2', marginLeft: 20, marginRight: 20 }} />

      <div style={{ flex: 1, display: 'flex', flexWrap: 'wrap', gap: 24 }}>
        <InfoRow label={t("certificates.model")} value={getDevice.data?.manufacturer} />

        <InfoRow
          label={t("commands.revision")}
          value={getRevision(getDevice.data?.firmware)}
        />

        <InfoRow
          label={t("system.uptime")}
          value={
            getStats.data?.unit?.uptime
              ? compactSecondsToDetailed(getStats.data?.unit.uptime, t)
              : undefined
          }
        />

        <InfoRow
          label={t("controller.stats.load")}
          value={
            getStats.data?.unit?.load
              ? getStats.data.unit.load.map((l: number) => l.toFixed(2)).join(" | ")
              : undefined
          }
        />

        <InfoRow
          label={t("controller.devices.localtime")}
          value={getStats.data?.unit?.localtime ? compactDate(getStats.data?.unit.localtime) : undefined}
        />

        <InfoRow
          label={t("analytics.last_contact")}
          value={
            getStatus?.data?.lastContact && getStatus?.data.lastContact !== 0 ? (
              <FormattedDate date={getStatus.data.lastContact} />
            ) : (
              <FormattedDate date={getDevice.data?.lastRecordedContact} />
            )
          }
        />

        <InfoRow label={t("analytics.memory")} value={getMemory()} />

        <InfoRow
          label="Connect Reason"
          value={
            getStatus.data?.connectReason && getStatus.data?.connectReason.length > 0
              ? uppercaseFirstLetter(getStatus.data?.connectReason)
              : undefined
          }
        />
      </div >
    </div >
  );
};

export default React.memo(DeviceSummary);
