import React, {  useRef,useState,useEffect } from "react";
import { ArrowLeftOutlined } from '@ant-design/icons';
import axios from "axios";
import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router-dom";
import WifiAnalysisCard from "@/modules-smb/Wireless/pages/Device/WifiAnalysis/index";
import DeviceActionDropdown from "@/modules-smb/Wireless/components/Button/DeviceActionDropdown";
import {Card} from "@/modules-smb/components/Containers/Card";
import {useScriptModal} from "@/modules-smb/components/Modals/ScriptModal/useScriptModal";
import {TraceModal} from "@/modules-smb/Wireless/pages/Devices/Action/TraceModal";
import FactoryResetModal from "@/modules-smb/Wireless/pages/Devices/Action/FactoryResetModal";
import { Box } from '@chakra-ui/react';
import {
    useDeleteDevice,
    useGetDevice,
    useGetDeviceHealthChecks,
    useGetDeviceStatus,
    useBlinkDevice, 
    useGetDeviceRtty
} from "@/modules-smb/Wireless/hooks/Network/Devices";
import SwitchPortExamination from "./SwitchPortExamination";
import SearchInput from "../Devices/SearchInput";
import { refreshSvg } from "@/utils/common/iconSvg";
import {
  App,
  Button,
  Collapse
} from "antd";
import Icon from "@ant-design/icons";
import deleteSvg from "@/modules-smb/Wireless/assets/Devices/delete.svg?react";
import Status from "./Status/Status.tsx";
import Details from "./Details/Details.tsx";
import Statistics from "@/modules-smb/Wireless/pages/Device/StatisticsCard";
import Notes from "./Notes/Notes.tsx";
import LogsCard from "./LogsCard";
import './collapse.css';
import '@/modules-smb/Wireless/assets/wireless.scss';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import { useControllerStore } from '@/modules-smb/contexts/ControllerSocketProvider/useStore';
import { useRebootDevice } from '@/modules-smb/hooks/Network/Devices';
import useMutationResult from '@/modules-smb/hooks/useMutationResult';
type Props = {
    serialNumber: string;
};

const DevicePageWrapper = ({serialNumber}: Props) => {
    const {t} = useTranslation();
    const { message: messageApi } = App.useApp();
    // const toast = useToast();
    // const breakpoint = useBreakpoint();
    // const cancelRef = React.useRef(null);
    const navigate = useNavigate();
    const {mutateAsync: deleteDevice, isLoading: isDeleting} = useDeleteDevice({
        serialNumber
    });
    const [isDeleteOpen, setIsDeleteOpen] = useState(false);
    const getDevice = useGetDevice({serialNumber});
    const getStatus = useGetDeviceStatus({serialNumber});
    const getHealth = useGetDeviceHealthChecks({serialNumber, limit: 1});
    const [isTraceModalOpen, setIsTraceModalOpen] = useState(false);
    const [isFactoryResetModalOpen, setIsFactoryResetModalOpen] = useState(false);
    const scanModalProps = useRef<any>({});
  const eventQueueProps = useRef<any>({});
  const configureModalProps = useRef<any>({});
  const upgradeModalProps = useRef<any>({});
  const telemetryModalProps = useRef<any>({});
  const scriptModal = useScriptModal();
  const [activeKeys, setActiveKeys] = useState<string[]>([]);

  //reboot
  const addEventListeners = useControllerStore((state) => state.addEventListeners);
  const { mutateAsync: reboot, isLoading: isRebooting } = useRebootDevice({ serialNumber });
  const { onSuccess: onRebootSuccess, onError: onRebootError } = useMutationResult({
    objName: t('devices.one'),
    operationType: 'reboot',
    refresh: () => {
      // 迁移原 RebootModal 中的状态监听逻辑（设备重启前后的提示）
      addEventListeners([
        {
          id: `device-connection-${serialNumber}`,
          type: 'DEVICE_CONNECTION',
          serialNumber,
          callback: () => {
            messageApi.success({
              content: t('controller.devices.finished_reboot', { serialNumber }),
              duration: 5,
              placement: 'topRight',
            });
          },
        },
        {
          id: `device-disconnected-${serialNumber}`,
          type: 'DEVICE_DISCONNECTION',
          serialNumber,
          callback: () => {
            messageApi.success({
              content: t('controller.devices.started_reboot', { serialNumber }),
              duration: 5,
              placement: 'topRight',
            });
          },
        },
      ]);
    },
  });
  const handleRebootConfirm = () => {
    confirmModalAction(
      // 确认框提示文案（可根据需求调整，与 Delete 保持风格一致）
      `Are you sure you want to reboot this ${serialNumber}?`,
      // 确认后的回调函数（执行 Reboot 逻辑）
      async () => {
        try {
          await reboot(undefined); // 调用 Reboot 接口
          onRebootSuccess(); // 成功提示（复用原有逻辑）
        } catch (e) {
          onRebootError(e as AxiosError); // 失败提示（复用原有逻辑）
        }
      },
    );
  };
  useEffect(() => {
  if (getDevice.data) {
    const allKeys = deviceItems.map(item => item.key);
    setActiveKeys(allKeys); // 数据回来后强制展开所有
  }
}, [getDevice.data]);
  const handleDeleteClick = () => {
    deleteDevice(serialNumber, {
      onError: e => {
        if (axios.isAxiosError(e)) {
          messageApi.error(e.response?.data?.ErrorDescription || t("common.error"));
        }
      }
    });
    messageApi.success(t("common.success"));
    navigate("/");
    setIsDeleteOpen(false);
  };
    const refresh = () => {
        getDevice.refetch();
        getStatus.refetch();
        getHealth.refetch();
    };
    const handleDelete = () => {
            confirmModalAction(
                `Are you sure you want to delete this ${serialNumber}?`,
                handleDeleteClick
            );
        };

    const deviceItems = [
        {
          key: '1',
          label: <span className="collapse-title">Status</span>,
          children: <Status serialNumber={serialNumber}/>,
        },
        {
          key: '2',
          label: <div>
            <span className="collapse-title">Details</span>
            </div>,
          children: <Details serialNumber={serialNumber}/>,
        },
        {
          key: '3',
          label: <div>
            <span className="collapse-title">Statistics</span>
            </div>,
          children: <Statistics serialNumber={serialNumber}/>,
        },
        // 条件性地添加 WiFi 分析项
        ...(getDevice.data?.deviceType === "ap" 
          ? [{
              key: '4',
              label: <div>
                <span className="collapse-title">WiFi Analysis</span>
              </div>,
              children: <WifiAnalysisCard serialNumber={serialNumber} />
            }]
          : []
        )
      ];
      const noteItems=[
        {
          key: '1',
          label: <span className="collapse-title">Notes</span>,
          children: <Notes serialNumber={serialNumber}/>,
        },
      ];
    const handleBack = () => navigate(-1);
    return (
        <>
            <div style={{ marginTop: 1, marginBottom: 16 }}>
              <Box
                as="button"
                onClick={handleBack}
                display="flex"
                fontFamily="Lato, sans-serif"
                fontWeight={500}
                fontSize="14px"
                color="#929A9E"
                lineHeight="16px"
                cursor="pointer"
                bg="transparent"
                border="none"
                p={0}
                _hover={{ opacity: 0.8 }}
                _active={{ opacity: 0.6 }}
                _focus={{ outline: 'none' }}
              >
                <ArrowLeftOutlined style={{ marginRight: "8px", fontSize: "14px" }} />
                <Box as="span">Back</Box>
              </Box>
            </div>
              <Card style={{ width: '100%', minHeight: 'auto',backgroundColor: '#ffffff', borderRadius: 8, boxShadow: 'none',padding:"32px 24px 24px 24px",marginBottom: 32 }}>
                <div style={{display: 'flex', gap: 10}}>
                    {getDevice.data &&(
                      <DeviceActionDropdown
                        device={getDevice?.data}
                        refresh={refresh}
                        onOpenScan={() => scanModalProps.current.onOpen?.()}
                        onOpenFactoryReset={() => setIsFactoryResetModalOpen(true)}
                        onOpenUpgradeModal={() => upgradeModalProps.current.onOpen?.()}
                        onOpenTrace={() => setIsTraceModalOpen(true)}
                        onOpenEventQueue={() => eventQueueProps.current.onOpen?.()}
                        onOpenConfigureModal={() => configureModalProps.current.onOpen?.()}
                        onOpenTelemetryModal={() => telemetryModalProps.current.onOpen?.()}
                        onOpenRebootModal={() => handleRebootConfirm()}
                        onOpenScriptModal={scriptModal.openModal}
                      />
                    )}
                    <Button icon={<Icon component={deleteSvg} />} onClick={handleDelete}>Delete</Button>
                    <Button icon={<Icon component={refreshSvg} />}
                      onClick={refresh}
                      loading={getDevice.isFetching || getHealth.isFetching || getStatus.isFetching}
                    >Refresh</Button>  
                    <div style={{ display: "flex", justifyContent: "flex-end", width: "100%" }}>
                        <SearchInput />
                    </div>  
                </div>
                <Collapse
                  size="large"
                  items={deviceItems}
                  activeKey={activeKeys}
                  onChange={(keys) => setActiveKeys(keys as string[])}
                  // defaultActiveKey={deviceItems.map(item => item.key)} // 全部展开
                  expandIconPosition="right"
                  className="no-collapse-border"
                  style={{ marginTop: 32, marginBottom: 0,paddingBottom:0, border: 'none' ,background: '#ffffff'}}
                />             
              </Card>
                <LogsCard serialNumber={serialNumber} />
              <Card style={{ width: '100%', minHeight: 'auto', borderRadius: 8, boxShadow: 'none',backgroundColor: '#ffffff',padding:"32px 24px 24px 24px",marginTop: 32 }}>
                <Collapse
                  size="large"
                  items={noteItems}
                  defaultActiveKey={noteItems.map(item => item.key)} // 全部展开
                  expandIconPosition="right"
                  className="no-collapse-border"
                  style={{ marginTop: 12, marginBottom: 0, border: 'none' ,background: '#ffffff'}}
                />
              </Card>
              <TraceModal 
                serialNumber={serialNumber} 
                modalProps={{
                    isOpen: isTraceModalOpen,
                    onClose: () => setIsTraceModalOpen(false)
                }} 
              />
              <FactoryResetModal 
                serialNumber={serialNumber} 
                modalProps={{
                    isOpen: isFactoryResetModalOpen,
                    onClose: () => setIsFactoryResetModalOpen(false)
                }} 
              />
            </>
    );
};

export default DevicePageWrapper;
