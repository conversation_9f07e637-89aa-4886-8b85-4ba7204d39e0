import React from 'react';
import { Card, Table, Button, Modal, Input, message, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { addSvg } from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import { useGetDevice, useUpdateDevice } from '@/modules-smb/Wireless/hooks/Network/Devices';
import { Note as TNote } from '@/modules-smb/models/Note';
import FormattedDate from '@/modules-smb/components/InformationDisplays/FormattedDate';

type Props = {
  serialNumber: string;
};

const DeviceNotes = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const getDevice = useGetDevice({
    serialNumber,
    disableToast: true,
    onError: () => { }
  });
  const [newNote, setNewNote] = React.useState('');
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const updateDevice = useUpdateDevice({ serialNumber });

  const onNoteChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNewNote(e.target.value);
  }, []);

  const onNoteSubmit = React.useCallback(() => {
    updateDevice.mutateAsync(
      {
        serialNumber,
        notes: [{ note: newNote, created: 0 }],
      },
      {
        onSuccess: () => {
          message.success(t('controller.devices.update_success'));
          setIsModalOpen(false);
          setNewNote('');
        },
      }
    );
  }, [newNote, serialNumber, t, updateDevice]);

  const notes = React.useMemo(
    () => getDevice.data?.notes?.sort(({ created: a }, { created: b }) => b - a) ?? [],
    [getDevice.data?.notes]
  );

  const columns = [
    {
      title: t('common.date'),
      dataIndex: 'created',
      key: 'created',
      width: '20%',
      sorter: (a, b) => new Date(a.created).getTime() - new Date(b.created).getTime(),
      render: (created: number) => <FormattedDate date={created} />,
    },
    {
      title: t('common.note'),
      dataIndex: 'note',
      key: 'note',
      width: '20%',
      sorter: (a, b) => a.note.localeCompare(b.note),
      render: (note: string) => <div style={{ whiteSpace: 'pre-wrap' }}>{note}</div>,
    },
    {
      title: t('common.by'),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: '25%',
    },
  ];

  return (
    <div>
      <div style={{
        textAlign: 'left'
      }}>
        <Button
          type="primary"
          onClick={() => setIsModalOpen(true)}
          style={{
            borderRadius: '2px',
            marginTop: 8,
            marginBottom: 22,
            marginLeft: -8
          }}
        >
          <Icon component={addSvg} />
          Create
        </Button>
      </div>
      <Table
        rowKey={(record: TNote) => String(record.created)}
        columns={columns}
        dataSource={notes}
        pagination={false}
        style={{
          width: '100%',
          textAlign: 'center',
          marginLeft: -8
        }}
        // scroll={{ y: 300 }}
        bordered
      />

      <Modal
        title={
          <div style={{
            fontSize: 20,
            fontWeight: 700,
            color: '#212519',
            lineHeight: '24px'
          }}>
            {t('common.create')}
          </div>
        }
        open={isModalOpen}
        onOk={onNoteSubmit}
        onCancel={() => {
          setIsModalOpen(false);
          setNewNote('');
        }}
        okText={t('Apply')}
        cancelText={t('common.cancel')}
        okButtonProps={{
          disabled: newNote.trim().length === 0,
          loading: updateDevice.isLoading,
          style: {
            backgroundColor: '#14C9BB',
            border: '#14C9BB',
            color: '#FFFFFF',
            fontWeight: 400,
            fontSize: 14,
            borderRadius: '2px',
          }
        }}
        cancelButtonProps={{
          style: {
            border: '1px solid #14C9BB',
            color: '#14C9BB',
            borderRadius: '2px',
            fontWeight: 400,
            fontSize: 14,
          }
        }}
        width={680}
        bodyStyle={{
          height: 340,        
        }}
        style={{
          height: 450,
          borderRadius: '8px'
        }}
        closable={true}
      >
        <Divider style={{ margin: '0px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
        <div style={{
          marginBottom: 24,
          display: 'flex',
          alignItems: 'flex-start'
        }}>
          <label style={{
            fontWeight: 400,
            fontSize: 14,
            color: '#212519',
            textAlign: 'left',
            marginRight: 18,
          }}>
            {t('common.note')}
          </label>
          <Input.TextArea
            value={newNote}
            onChange={onNoteChange}
            style={{
              width: 280,
              height: 56,
              borderRadius: '2px',
              border: '1px solid #B2B2B2'
            }}
            maxLength={500}
          />
        </div>
        <Divider style={{ margin: '260px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
      </Modal>
    </div>
  );
};

export default DeviceNotes;
