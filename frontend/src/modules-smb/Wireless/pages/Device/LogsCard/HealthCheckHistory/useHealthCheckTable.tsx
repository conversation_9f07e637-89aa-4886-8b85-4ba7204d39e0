import * as React from 'react';
import { useTranslation } from 'react-i18next';
import FormattedDate from '@/modules-smb/components/InformationDisplays/FormattedDate';
import { HealthCheck, useGetHealthChecks, useGetHealthChecksWithTimestamps } from '@/modules-smb/hooks/Network/HealthChecks';
import { Column } from '@/modules-smb/models/Table';
import {Tag,Tooltip} from 'antd';
type Props = {
  serialNumber: string;
  limit: number;
};

const useHealthCheckTable = ({ serialNumber, limit }: Props) => {
  const { t } = useTranslation();
  const getHealthChecks = useGetHealthChecks({ serialNumber, limit });
  const [time, setTime] = React.useState<{ start: Date; end: Date } | undefined>();
  const getCustomHealthChecks = useGetHealthChecksWithTimestamps({
    serialNumber,
    start: time ? Math.floor(time.start.getTime() / 1000) : undefined,
    end: time ? Math.floor(time.end.getTime() / 1000) : undefined,
  });

  const dateCell = React.useCallback(
    (v: number) => (
      <div>
        <FormattedDate date={v} />
      </div>
    ),
    [],
  );
  const hexToRgba = (hex: string, alpha: number) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  };
  const sanityCell = React.useCallback((v: number) => {
    let colorScheme = '#F53F3F';
    if (v === 100) colorScheme = '#2BC174';
    else if (v >= 80) colorScheme = '#FFBB00';

    return (
      <Tag style={{
        border: `1px solid ${colorScheme}`,
        color: colorScheme,
        background: hexToRgba(colorScheme, 0.1),
      }}
      >
        {v}
      </Tag>
    );
  }, []);
  const jsonCell = React.useCallback((v: Record<string, unknown>) => 
  <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word',width: "100%", maxWidth: "100%" }}>
    {JSON.stringify(v, null, 0)}
    </div>
    , []);


  const columns: Column<HealthCheck>[] = React.useMemo(
    (): Column<HealthCheck>[] => [
      {
        key: 'submitted',
        title: t('common.submitted'),
        dataIndex: 'submitted',
        render: (_,record) => dateCell(record.recorded),
        // customWidth: '35px',
        // disableSortBy: true,
        fixed: 'left',
        sorter: false,
      },
      {
        key: 'UUID',
        title: t('controller.devices.config_id'),
        dataIndex: 'UUID',
        // customWidth: '35px',
        // alwaysShow: true,
        // disableSortBy: true,
        sorter: false,
        columnsFix:true,
      },
      {
        key: 'sanity',
        title: t('devices.sanity'),
        dataIndex: 'sanity',
        render: (_,record) => sanityCell(record.sanity),
        sorter: false,
      },
      {
        key: 'values',
        title: t('common.details'),
        dataIndex: 'values',
        render: (_,record) => jsonCell(record.values),
        sorter: false,
        width: 500,
      },
    ],
    [t],
  );

  return {
    columns,
    getHealthChecks,
    getCustomHealthChecks,
    time,
    setTime,
  };
};

export default useHealthCheckTable;
