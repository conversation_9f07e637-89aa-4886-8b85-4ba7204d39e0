import * as React from 'react';
import { useTranslation } from 'react-i18next';
import DeleteButton from '@/modules-smb/Wireless/components/Button/DeleteButton';
import 'react-datepicker/dist/react-datepicker.css';
import { useDeleteHealthChecks } from '@/modules-smb/hooks/Network/HealthChecks';
import { AxiosError } from '@/modules-smb/models/Axios';
import { AmpConCustomModal } from "@/modules-ampcon/components/custom_table";
import {Alert,DatePicker,message } from 'antd';
import dayjs,{ Dayjs } from 'dayjs';
import "../LogsCard.css";

type Props = { serialNumber: string };
const DeleteHealthChecksModal = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const deleteHealthChecks = useDeleteHealthChecks();
  const [date, setDate] = React.useState<Dayjs>(dayjs());
  const [isOpen, setIsOpen] = React.useState(false);
  const onDeleteClick = () => {
    deleteHealthChecks.mutate(
      { endDate: date.unix(), serialNumber },
      {
        onSuccess: () => {
          setIsOpen(false);
          message.success(
            t('controller.crud.delete_success_obj', {
              obj: t('controller.devices.healthchecks'),
            })
          );
        },
        onError: (e) => {
          const error = e as AxiosError;
          message.error(error?.response?.data?.ErrorDescription || t('common.error'));
        },
      },
    );
  };
  const onChange = (newDate: Date) => {
    setDate(newDate);
  };
  // 限制不能选未来日期
  const disabledDate = (current: Dayjs) => {
    return current && current > dayjs().endOf('day');
  };
     const modalContent = (
      <div>
        <Alert
          type="warning"
          showIcon
          message={t('controller.devices.delete_health_explanation')}
          style={{ borderRadius: 8, marginBottom: 16 }}
        />
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center',gap: 10 }}>
            <DatePicker
              popupClassName="picker-split-footer"
              value={date}
              onChange={onChange}
              format="YYYY-MM-DD HH:mm:ss"
              disabledDate={disabledDate}
              showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
              disabled={deleteHealthChecks.isLoading}
            />
           <DeleteButton onClick={onDeleteClick} isLoading={deleteHealthChecks.isLoading} />
          </div>
        </div>
    );
  

  return (
    <>
      <DeleteButton onClick={() => setIsOpen(true)} isCompact />
      <AmpConCustomModal
        title={`${t('crud.delete')} ${t('controller.devices.logs')}`}
        childItems={modalContent}
        isModalOpen={isOpen}
        onCancel={() => setIsOpen(false)}
        footer={null}
        modalClass="ampcon-middle-modal"
      />
    </>
  );
};

export default DeleteHealthChecksModal;
