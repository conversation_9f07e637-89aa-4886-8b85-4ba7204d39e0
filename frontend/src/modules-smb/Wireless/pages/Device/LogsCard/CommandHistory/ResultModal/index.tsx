import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, Collapse, Typography, Spin } from 'antd';
import { <PERSON><PERSON>Viewer } from '@textea/json-viewer';
import DownloadScriptButton from './DownloadScriptButton';
import DownloadTraceButton from './DownloadTraceButton';
import DownloadWifiScanButton from './DownloadWifiScanButton';
import { AmpConCustomModal } from '@/modules-ampcon/components/custom_table';
import WifiScanResultDisplay from '@/modules-smb/components/Modals/WifiScanModal/ResultDisplay';
import { compactDate } from '@/modules-smb/helpers/dateFormatting';
import { uppercaseFirstLetter } from '@/modules-smb/helpers/stringHelper';
import { DeviceCommandHistory, useGetSingleCommandHistory } from '@/modules-smb/hooks/Network/Commands';
import { WifiScanResult } from '@/modules-smb/models/Device';
import "@/modules-smb/Wireless/assets/wireless.scss";
const { Panel } = Collapse;
const { Text, Paragraph } = Typography;

type Props = {
  modalProps: {
    isOpen: boolean;
    onClose: () => void;
  };
  command?: DeviceCommandHistory;
};

const CommandResultModal = ({ modalProps, command: initialCommandInfo }: Props) => {
  const { t } = useTranslation();

  const { data: command, isLoading } = useGetSingleCommandHistory({
    commandId: initialCommandInfo?.UUID ?? '',
    serialNumber: initialCommandInfo?.serialNumber ?? '',
  });

  if (isLoading) {
    return (
      <AmpConCustomModal
        title={t('common.loading')}
        isModalOpen={modalProps.isOpen}
        onCancel={modalProps.onClose}
        childItems={
          <div style={{ textAlign: 'center', padding: '100px 0' }}>
            <Spin size="large" />
          </div>
        }
        footer={null}
      />
    );
  }

  if (!command) return null;

  // 定义 Modal 内部内容
  const modalContent = (() => {
    if (command.status === 'failed') {
      return (
        <div style={{ margin: '40px 0', textAlign: 'center' }}>
          <Alert
            message={uppercaseFirstLetter(command.status)}
            description={command.errorText}
            type="error"
            showIcon
          />
        </div>
      );
    }

    if (command.command === 'wifiscan') {
      return <WifiScanResultDisplay results={command as unknown as WifiScanResult} setCsvData={() => {}} />;
    }

    if (
      command.command === 'script' &&
      (command.details?.uri === undefined || command.details?.uri === '') &&
      command.status === 'completed'
    ) {
      return (
        <Paragraph>
          <Text code>
            {command.results?.status?.result ?? JSON.stringify(command.results, null, 2)}
          </Text>
        </Paragraph>
      );
    }
    return (
      <div className="collapse-modal">
      <Collapse
        expandIconPosition="right"
        bordered={false}
        defaultActiveKey={'1'}
        style={{ background: '#FFFFFF',marginTop:"14px"}}
      >
        <Collapse.Panel
          key="1"
          header={<h3 style={{ fontSize: "16px", margin: 0, border: "none",fontWeight: 600 }}>  {t('common.preview')}</h3>}
        >
            <JsonViewer
              rootName={false}
              displayDataTypes={false}
              enableClipboard={false}
              theme="light"
              defaultInspectDepth={1}
              value={command.results?.status as object}
              style={{ background: 'unset', display: 'unset' }}
            />
        </Collapse.Panel>
      </Collapse>
      </div>
    );
  })();

  return (
    <AmpConCustomModal
      title={`${uppercaseFirstLetter(command.command)} - ${compactDate(command.submitted)} `}
      childItems={modalContent}
      isModalOpen={modalProps.isOpen}
      onCancel={modalProps.onClose}
      footer={
        <>
          <DownloadWifiScanButton command={command as unknown as WifiScanResult} />
          <DownloadTraceButton command={command} />
          <DownloadScriptButton command={command} />
        </>
      }
      modalClass="ampcon-middle-modal"
    />
  );
};

export default CommandResultModal;
