import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Button, 
  Space, 
  Typography, 
  Table, 
  Card, 
  Dropdown,
  Menu,
  Checkbox,
  Tooltip,
  DatePicker,
  ConfigProvider,
  Spin,
  Tag
} from 'antd';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import CommandResultModal from './ResultModal';
import useCommandHistoryTable from './useCommandHistoryTable';
import dayjs from 'dayjs';
import DateTimeRangePicker from "../DateTimeRangePicker";
import Icon from "@ant-design/icons";
import { refreshSvg } from "@/utils/common/iconSvg";


const { RangePicker } = DatePicker;
const { Title } = Typography;

const customFooterStyle = `
.ant-picker-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 12px; 
  width: 100%;
  height: auto; 
}
.ant-picker-footer-extra {
  border: none !important;
}
.ant-picker-now-btn {
  padding: 0;
  height: auto;
}
.ant-picker-time-panel-btn {
  background: transparent;
  border: none;
  box-shadow: none;
}
.ant-picker-time-panel-btn:hover {
  background: rgba(0, 0, 0, 0.04);
}
`;

type Props = {
  serialNumber: string;
};

const CommandHistory = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const [limit, setLimit] = React.useState(25);
  const [hiddenColumns, setHiddenColumns] = React.useState<string[]>([]);
  const { time, setTime, getCustomCommands, getCommands, columns, selectedCommand, detailsModalProps } =
    useCommandHistoryTable({ serialNumber, limit });
  // RangePicker相关状态
  const [pickerTime, setPickerTime] = React.useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [activeInputIndex, setActiveInputIndex] = React.useState<number | null>(null);
  const [isPanelOpen, setIsPanelOpen] = React.useState(false);
  const rangePickerRef = React.useRef<any>(null);

  const raiseLimit = () => {
    setLimit(limit + 25);
  };
  console.log('columns',columns);
  // 处理时间范围变化
  const handleRangeChange = (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    if (dates) {
      setPickerTime(dates);
    } else {
      // 清除时恢复默认时间
      setPickerTime(null);
      setTime(undefined);
    }
  };
  // 处理面板打开/关闭
  const handleOpenChange = (open: boolean) => {
    setIsPanelOpen(open);
    if (!open && pickerTime) {
      // 面板关闭时确认选择的时间
      setTime({
        start: pickerTime[0].toDate(),
        end: pickerTime[1].toDate()
      });
    }
  };

  // 处理输入框聚焦
  const handleRangeFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    const index = e.target.placeholder === t('common.start_time') ? 0 : 1;
    setActiveInputIndex(index);
  };
  // 处理"Now"按钮点击
  const handleNow = () => {
    if (activeInputIndex === null) return;

    const now = dayjs();
    const baseTime = pickerTime || [dayjs(), dayjs()];
    const newTime = [baseTime[0], baseTime[1]] as [dayjs.Dayjs, dayjs.Dayjs];
    newTime[activeInputIndex] = now;

    setPickerTime(newTime);
  };

  // 处理日历变化
  const handleCalendarChange = (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    if (dates) {
      setPickerTime(dates);
    }
  };
  const setNewTime = (start: Date, end: Date) => {
    setTime({ start, end });
  };
  
  const onClear = () => {
    setPickerTime(null);
    setTime(undefined);
  };

  const noMoreAvailable =
    getCustomCommands.data || (getCommands.data !== undefined && getCommands.data.commands.length < limit);

  const data = React.useMemo(() => {
    if (getCustomCommands.data) return getCustomCommands.data.commands.sort((a, b) => b.submitted - a.submitted);
    if (getCommands.data) return getCommands.data.commands;
    return [];
  }, [getCustomCommands.data, getCommands.data]);
  // 获取数据总数
  const getTotalCount = () => {
    if (getCustomCommands.data) return getCustomCommands.data.totalCount || getCustomCommands.data.commands.length;
    if (getCommands.data) return getCommands.data.totalCount || getCommands.data.commands.length;
    return 0;
  };

  // 获取表格数据
  const getTableData = () => {
    if (getCustomCommands.data) {
      const filteredCommands = getCustomCommands.data.commands.filter(command => command.command !== 'wifiscan');
      return filteredCommands;
    }
    if (getCommands.data) {
      const filteredCommands = getCommands.data.commands.filter(command => command.command !== 'wifiscan');
      return filteredCommands;
    }
    return [];
  };

  // 列选择器菜单
  const columnMenu = (
    <Menu>
      {columns.map((column) => (
        <Menu.Item key={column.id}>
          <Checkbox
            checked={!hiddenColumns.includes(column.id)}
            onChange={(e) => {
              if (e.target.checked) {
                setHiddenColumns(hiddenColumns.filter(col => col !== column.id));
              } else {
                setHiddenColumns([...hiddenColumns, column.id]);
              }
            }}
          >
            {column.Header}
          </Checkbox>
        </Menu.Item>
      ))}
    </Menu>
  );

    // 处理表格变化（分页、排序、筛选）
  const handleTableChange = (pagination, filters, sorter) => {
    // setPageInfo({
    //   index: pagination.current - 1,
    //   limit: pagination.pageSize,
    // });

    // if (sorter.field) {
    //   setSortInfo([{
    //     id: sorter.field,
    //     sort: sorter.order === 'ascend' ? 'asc' : 'desc'
    //   }]);
    // }
  };
  return (
    <>
        
          <Space>
             <DateTimeRangePicker
              value={pickerTime}
              onChange={handleRangeChange}
              placeholder={[t('common.start_time'), t('common.end_time')]}
              style={{ width: 380 }}
              onOpenChange={handleOpenChange}
              open={isPanelOpen}
              t={t}
            />
          </Space>
          <div style={{display: 'flex', gap: 10,marginTop: 32,marginBottom:4}}>
          <Button icon={<Icon component={refreshSvg} />}
            onClick={getCommands.refetch}
            loading={getCommands.isFetching}
          >{t('common.refresh')}</Button>
        </div>
        <WirelessCustomTable
          // columnsOrder={true}
          columns={columns}
          dataSource={getTableData()}
          loading={getCommands.isFetching || getCustomCommands.isFetching}
          onChange={handleTableChange}
          showColumnSelector='true'
          pagination={false}
          disableInternalRowSelection
          scroll={{x: 'max-content', y: getTableData().length > 6 ? 300 : undefined }}
          // pagination={{
          //   current: pageInfo.index + 1,
          //   pageSize: pageInfo.limit,
          //   total: getTotalCount(),
          //   showSizeChanger: true,
          //   showQuickJumper: true,
          //   showTotal: (total, range) => 
          //     `${range[0]}-${range[1]} of ${total} items`,
          //   pageSizeOptions: ['10', '20', '50', '100'],
          // }}
        />
        {data.length > 0 && (
          <div style={{ textAlign: 'center', padding: '16px' }}>
            {!noMoreAvailable || getCommands.isFetching ? (
              <Button 
                onClick={raiseLimit} 
                loading={getCommands.isFetching}
                type="primary"
              >
                {t('controller.devices.show_more')}
              </Button>
            ) : (
              <span></span>
            )}
          </div>
        )}
      <CommandResultModal command={selectedCommand} modalProps={detailsModalProps} />
    </>
  );
};

export default CommandHistory;