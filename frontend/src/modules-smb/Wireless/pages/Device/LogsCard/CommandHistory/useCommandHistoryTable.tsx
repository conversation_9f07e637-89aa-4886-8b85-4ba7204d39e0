import * as React from 'react';
import { Badge, Box, HStack, IconButton, Tooltip, useDisclosure, useToast } from '@chakra-ui/react';
import { MagnifyingGlass, Trash } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import FormattedDate from '@/modules-smb/components/InformationDisplays/FormattedDate';
import { uppercaseFirstLetter } from '@/modules-smb/helpers/stringHelper';
import {
  DeviceCommandHistory,
  useDeleteCommand,
  useGetCommandHistory,
  useGetCommandHistoryWithTimestamps,
} from '@/modules-smb/hooks/Network/Commands';
import { AxiosError } from '@/modules-smb/models/Axios';
import { Column } from '@/modules-smb/models/Table';
import {Tag,message} from 'antd';

type Props = {
  serialNumber: string;
  limit: number;
};

const useCommandHistoryTable = ({ serialNumber, limit }: Props) => {
  const { t } = useTranslation();
  const [time, setTime] = React.useState<{ start: Date; end: Date } | undefined>();
  const getCustomCommands = useGetCommandHistoryWithTimestamps({
    serialNumber,
    start: time ? Math.floor(time.start.getTime() / 1000) : undefined,
    end: time ? Math.floor(time.end.getTime() / 1000) : undefined,
  });
  const getCommands = useGetCommandHistory({ serialNumber, limit });
  const deleteCommand = useDeleteCommand();
  const [selectedCommand, setSelectedCommand] = React.useState<DeviceCommandHistory | undefined>();
  const detailsModalProps = useDisclosure();
  const [loadingDeleteSerial, setLoadingDeleteSerial] = React.useState<string | undefined>();
  const toast = useToast();

  const onOpenDetails = React.useCallback(
    (command: DeviceCommandHistory) => () => {
      setSelectedCommand(command);
      detailsModalProps.onOpen();
    },
    [],
  );

  const onDeleteClick = React.useCallback(
    (command: DeviceCommandHistory) => () => {
      setLoadingDeleteSerial(command.UUID);
      deleteCommand.mutate(command.UUID, {
        onSuccess: () => {
          setLoadingDeleteSerial(undefined);
          message.success(
            t('controller.crud.delete_success_obj', {
              obj: uppercaseFirstLetter(command.command),
            })
          );
        },
        onError: (e) => {
          const error = e as AxiosError;
          setLoadingDeleteSerial(undefined);
          message.error(error?.response?.data?.ErrorDescription || t('common.error'));
        },
      });
    },
    [],
  );

  const dateCell = React.useCallback(
    (v: number) => (
      <Box>
        <FormattedDate date={v} />
      </Box>
    ),
    [],
  );
  const hexToRgba = (hex: string, alpha: number) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  };
  const capitalizeFirstLetterCell = React.useCallback((v: string) => <>{uppercaseFirstLetter(v)}</>, []);
  const statusCell = React.useCallback((command: DeviceCommandHistory) => {
    let colorScheme = '#F53F3F';
    let status=command.status;
    if (status === 'completed'&&command.errorCode===0) colorScheme = '#2BC174';
    else if(status === 'completed'&&command.errorCode!==0){
      status='failed';
    }
    // 将v转换为首字母大写
    const capitalizedValue = status.charAt(0).toUpperCase() + status.slice(1);
    return (
      <Tag style={{
        border: `1px solid ${colorScheme}`,
        color: colorScheme,
        background: hexToRgba(colorScheme, 0.1),
        fontSize: '14px',
      }}
      >
        {capitalizedValue}
      </Tag>
    );
  }, []);
  const actionCell = React.useCallback(
    (command: DeviceCommandHistory) => (
       <div style={{ display: 'flex',gap: 24, alignItems: 'center'}}>
          <a type="link" 
      style={{ display: 'flex', alignItems: 'center', color: '#14C9BB'}} 
      onClick={onDeleteClick(command)} 
      onFocus={(e) => e.target.blur()}
      >Delete</a>
      <a type="link" 
      style={{ display: 'flex', alignItems: 'center', color: '#14C9BB'}} 
      onClick={onOpenDetails(command)} 
      onFocus={(e) => e.target.blur()}
      >View</a>
        </div>
    ),
    [loadingDeleteSerial],
  );

  const columns: Column<DeviceCommandHistory>[] = React.useMemo(
    (): Column<DeviceCommandHistory>[] => [
      {
        key: 'submitted',
        title: t('common.submitted'),
        // Footer: '',
        dataIndex: 'submitted',
        render: (_,record) => {
        if (!record) return null;
        return dateCell(record.submitted);
      },
        // customWidth: '35px',
        // disableSortBy: true,
        fixed: 'left',
        sorter: false,
        isMonospace: true,
      },
      {
        key: 'command',
        title: t('controller.devices.command_one'),
        Footer: '',
        dataIndex: 'command',
        render: (_,record) => {
          if (!record) return null;
          return capitalizeFirstLetterCell(record.command);
        },
        // customWidth: '35px',
        // alwaysShow: true,
        // disableSortBy: true,
        sorter: false,
        isMonospace: true,
        columnsFix:true,
      },
      {
        key: 'status',
        title: t('common.status'),
        dataIndex: 'status',
        render: (_,record) => {
           if (!record) return null;
          return statusCell(record);
        },
        // customWidth: '50px',
        // disableSortBy: true,
        sorter: false,
        isMonospace: true,
      },
      {
        key: 'executed',
        title: t('controller.devices.executed'),
        dataIndex: 'executed',
        render: (_,record) => {
          if (!record) return null;
          return dateCell(record.executed)},
        // customWidth: '35px',
        // disableSortBy: true,
        sorter: false,
        isMonospace: true,
      },
      {
        key: 'completed',
        title: t('common.completed'),
        dataIndex: 'completed',
        render: (_,record) => {
          if (!record) return null;
          return dateCell(record.completed);
        },
        // customWidth: '35px',
        // disableSortBy: true,
        sorter: false,
        isMonospace: true,
      },
      {
        key: 'errorCode',
        title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          {t('controller.devices.error_code')}
        </span>
        ),
        dataIndex: 'errorCode',
        // customWidth: '35px',
        // disableSortBy: true,
        sorter: false,
        width:100,
        isMonospace: true,
      },
      {
        key: 'actions',
        title: 'Operation',
        dataIndex: 'actions',
        render: (_,record) => {
          if (!record) return null;
          return actionCell(record)},
        // customWidth: '35px',
        // disableSortBy: true,
      },
    ],
    [t, actionCell],
  );

  return {
    columns,
    getCommands,
    getCustomCommands,
    selectedCommand,
    detailsModalProps,
    time,
    setTime,
  };
};

export default useCommandHistoryTable;
