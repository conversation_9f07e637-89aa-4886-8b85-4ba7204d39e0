.logs-tabs .ant-tabs-nav {
  margin: 0;
  border-bottom: 1px solid #d9d9d9;
}

.logs-tabs .ant-tabs-tab {
  font-weight: 600;
  border: none !important;
  margin: 0 16px 0 0 !important;
  padding: 8px 0 !important;
}

.logs-tabs .ant-tabs-tab-active {
  color: #14C9BB !important; 
  border-bottom: 2px solid #14C9BB !important;
}

.logs-tabs .ant-tabs-ink-bar {
  display: none !important; /* 隐藏默认下划线 */
}

.logs-tabs .ant-tabs-content-holder {
  margin-top: 16px;
}
.ant-tabs, .ant-tabs-content-holder, .ant-tabs-content, .ant-tabs-tabpane{
  border-radius: 8px !important;
}
.ant-tabs-tabpane.ant-tabs-tabpane-active {
  padding-bottom: 24px !important;
}
.picker-split-footer .ant-picker-footer .ant-picker-ranges {
  display: flex !important;
  width: 100% !important;
  justify-content: flex-start;      /* 左对齐，让第一个元素保持在左侧 */
}

.picker-split-footer .ant-picker-footer .ant-picker-ranges .ant-picker-ok {
  margin-left: auto;                /* 把 OK 顶到最右 */
}

.ant-picker-cell-today.ant-picker-cell-selected .ant-picker-cell-inner,
.ant-picker-cell-today.ant-picker-cell-selected .ant-picker-cell-inner::before {
  color: #ffffff !important;
}
