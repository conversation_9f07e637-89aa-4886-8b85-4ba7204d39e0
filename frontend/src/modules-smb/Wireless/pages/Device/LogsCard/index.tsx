import * as React from 'react';
import { Card, Tabs, Grid } from 'antd';
import { useTranslation } from 'react-i18next';
import CommandHistory from './CommandHistory';
import HealthCheckHistory from './HealthCheckHistory';
import LogHistory from './LogHistory';
import CrashLogs from './LogHistory/CrashLogs';
import RebootLogs from './LogHistory/RebootLogs';
import { Box, Button, Center, Flex, Heading, HStack } from '@chakra-ui/react';

import './LogsCard.css';
const { useBreakpoint } = Grid;

type Props = {
  serialNumber: string;
};

const { TabPane } = Tabs;

const DeviceLogsCard = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const screens = useBreakpoint();
  const [activeKey, setActiveKey] = React.useState('0');

  const handleTabsChange = React.useCallback((key: string) => {
    setActiveKey(key);
  }, []);

  // 判断是否为紧凑布局
  // Ant Design 的断点系统: xs: <576px, sm: ≥576px, md: ≥768px, lg: ≥992px, xl: ≥1200px, xxl: ≥1600px
  const isCompact = !screens.lg; // 当屏幕宽度小于992px时认为是紧凑布局

  return (
    <div>
      <Tabs
        activeKey={activeKey}
        onChange={handleTabsChange}
        className="custom-logs-tabs"
      >
        <TabPane
        style={{width: '100%'}}
          tab={<span>{t('controller.devices.commands')}</span>}
          key="0"
        >
          <CommandHistory serialNumber={serialNumber} />
        </TabPane>

        <TabPane
          style={{width: '100%'}}
          tab={<span >{t('controller.devices.healthchecks')}</span>}
          key="1"
        >
          <HealthCheckHistory serialNumber={serialNumber} />   
        </TabPane>

        <TabPane
          style={{width: '100%'}}
          tab={<span>{t('controller.devices.logs')}</span>}
          key="2"
        >
          <LogHistory serialNumber={serialNumber} />
        </TabPane>

        <TabPane
          style={{width: '100%'}}
          tab={<span>
            {isCompact ? 'Crashes' : t('devices.crash_logs')}
          </span>}
          key="3"
        >
          <CrashLogs serialNumber={serialNumber} />
        </TabPane>

        <TabPane 
          style={{width: '100%'}}
          tab={<span>
            {isCompact ? 'Reboots' : t('devices.reboot_logs')}
          </span>}
          key="4"
        >
          <RebootLogs serialNumber={serialNumber} />
        </TabPane>
      </Tabs>
   </div>
  );
};

export default DeviceLogsCard;