import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Button, 
  Space, 
  Typography, 
  Card, 
  Dropdown,
  Menu,
  Checkbox,
  Tooltip,
  DatePicker,
  ConfigProvider,
  Spin,
  Tag
} from 'antd';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import CommandResultModal from './ResultModal';
import useCommandHistoryTable from './useCommandHistoryTable';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Title } = Typography;
const DateTimeRangePicker = React.forwardRef(({
  value,
  onChange,
  onCalendarChange,
  placeholder,
  onFocus,
  onOpenChange,
  open,
  style,
  format = "YYYY-MM-DD HH:mm:ss",
  showTime = { format: 'HH:mm:ss' },
  t,
  ...rest
}, ref) => {
  const [internalValue, setInternalValue] = React.useState(value);
  const [activeInputIndex, setActiveInputIndex] = React.useState(null);
  
  // 同步外部value变化
  React.useEffect(() => {
    setInternalValue(value);
  }, [value]);

  const handleRangeChange = (dates) => {
    setInternalValue(dates);
    if (onChange) {
      onChange(dates);
    }
  };

  const handleRangeFocus = (e) => {
    const index = e.target.placeholder === t('common.start_time') ? 0 : 1;
    setActiveInputIndex(index);
    if (onFocus) {
      onFocus(e);
    }
  };

  const handleNow = () => {
    if (activeInputIndex === null) return;

    const now = dayjs();
    const baseTime = internalValue || [dayjs(), dayjs()];
    const newTime = [baseTime[0], baseTime[1]];
    newTime[activeInputIndex] = now;

    setInternalValue(newTime);
    if (onChange) {
      onChange(newTime);
    }
  };

  return (
    <div>
      <span style={{marginRight:32}}>{t('controller.crud.choose_time')}</span>
    <Tooltip>
      <RangePicker
        ref={ref}
        value={internalValue}
        onChange={handleRangeChange}
        onCalendarChange={onCalendarChange}
        showTime={showTime}
        format={format}
        placeholder={placeholder}
        style={style}
        onFocus={handleRangeFocus}
        onOpenChange={onOpenChange}
        open={open}
        renderExtraFooter={() => (
          <div className="ant-picker-footer">
            <Button
              type="text"
              className="ant-picker-now-btn"
              onClick={handleNow}
            >
              Now
            </Button>
          </div>
        )}
        {...rest}
      /> 
    </Tooltip>
    </div>
  );
});

export default DateTimeRangePicker;