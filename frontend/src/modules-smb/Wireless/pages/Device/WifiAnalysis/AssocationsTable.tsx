import React, { useMemo, useState, forwardRef } from 'react';
import { Typography, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import DataCell from '@/modules-smb/components/TableCells/DataCell';
import IpCell from './IpCell';

const { Title } = Typography;

export type ParsedAssociation = {
  radio?: { index?: number; band?: string; deductedBand?: string };
  ips: { ipv4: string[]; ipv6: string[] };
  station: string;
  ssid: string;
  rssi: number | string;
  mode: string;
  rxBytes: number;
  rxRate: number;
  rxMcs: number | string;
  rxNss: number | string;
  txBytes: number;
  txRate: number;
  txMcs: number | string;
  txNss: number | string;
  recorded: number;
  dynamicVlan?: number;
  fingerprint?: object;
};

type Props = {
  data?: ParsedAssociation[];
  ouis?: Record<string, string>;
  isSingle?: boolean;
};

const WifiAnalysisAssociationsTable = forwardRef(({ data = [], ouis = {}, isSingle }: Props, ref) => {
  const { t } = useTranslation();
  const [visibleColumns, setVisibleColumns] = useState<string[]>([]);

  const allColumns = useMemo(() => [
    {
      title: '',
      dataIndex: ['radio', 'index'],
      key: 'index',
      render: (_, record) => record.radio?.band ?? record.radio?.deductedBand ?? '',
      fixed: 'left',
      defaultSortOrder: 'descend',
      columnsFix: true
    },
    {
      title: t('controller.wifi.station'),
      dataIndex: 'station',
      key: 'station',
      sorter: (a, b) => a.station.localeCompare(b.station),
      columnsFix: true,
    },
    {
      title: 'SSID',
      dataIndex: 'ssid',
      key: 'ssid',
      sorter: (a, b) => a.ssid.localeCompare(b.ssid),
      columnsFix: true,
    },
    {
      title: 'IPs',
      key: 'ips',
      render: (_, record) => <IpCell ipv4={record.ips.ipv4} ipv6={record.ips.ipv6} />,
    },
    {
      title: 'Fingerprint',
      key: 'fingerprint',
      render: (_, record) => Object.values(record.fingerprint ?? {}).join(', '),
    },
    {
      title: t('controller.wifi.vendor'),
      key: 'vendor',
      render: (_, record) => ouis[record.station] ?? '',
    },
    {
      title: 'VLAN',
      key: 'dynamicVlan',
      dataIndex: 'dynamicVlan',
      render: (v) => (v !== undefined ? `${v}` : '-'),
      sorter: (a, b) => a.dynamicVlan - b.dynamicVlan,
    },
    {
      title: t('controller.wifi.mode'),
      dataIndex: 'mode',
      key: 'mode',
    },
    {
      title: 'RSSI',
      dataIndex: 'rssi',
      key: 'rssi',
      sorter: (a, b) => a.rssi - b.rssi,
    },
    {
      title: t('controller.wifi.rx_rate'),
      dataIndex: 'rxRate',
      key: 'rxRate',
      sorter: (a, b) => a.rxRate - b.rxRate,
    },
    {
      title: 'Rx',
      dataIndex: 'rxBytes',
      key: 'rxBytes',
      render: (v) => <DataCell bytes={v} />,
      sorter: (a, b) => a.rxBytes - b.rxBytes,
    },
    {
      title: 'Rx MCS',
      dataIndex: 'rxMcs',
      key: 'rxMcs',
      sorter: (a, b) => a.rxMcs - b.rxMcs,
    },
    {
      title: 'Rx NSS',
      dataIndex: 'rxNss',
      key: 'rxNss',
      sorter: (a, b) => a.rxNss - b.rxNss,
    },
    {
      title: t('controller.wifi.tx_rate'),
      dataIndex: 'txRate',
      key: 'txRate',
      sorter: (a, b) => a.txRate - b.txRate,
    },
    {
      title: 'Tx',
      dataIndex: 'txBytes',
      key: 'txBytes',
      render: (v) => <DataCell bytes={v} />,
      sorter: (a, b) => a.txBytes - b.txBytes,
    },
  ], [t, ouis]);

  const defaultVisible = allColumns.map((col) => col.key);

  return (
    <>
      <Space align="center" style={{ marginTop: 24, marginBottom: 4, width: '100%' }}>
        <Title level={5} style={{ margin: 0 }}>
          {isSingle ? 'Association' : `${t('devices.associations')} (${data.length})`}
        </Title>
      </Space>

      <WirelessCustomTable
        ref={ref}
        tableId='wifianalysis-associations-table'
        // columnsOrder={true}
        columns={allColumns}
        dataSource={data}
        showColumnSelector={true}
        visibleColumns={visibleColumns.length ? visibleColumns : defaultVisible}
        onVisibleColumnsChange={setVisibleColumns}
        pagination={false}
      />
    </>
  );
});

export default WifiAnalysisAssociationsTable;