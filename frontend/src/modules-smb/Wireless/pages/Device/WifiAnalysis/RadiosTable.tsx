import React, { useMemo, useState, forwardRef } from 'react';
import { Typography, Space } from 'antd';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import { useTranslation } from 'react-i18next';

const { Title } = Typography;

export type ParsedRadio = {
  recorded: number;
  band?: string;
  deductedBand: string;
  index: number;
  channel: number;
  channelWidth: string;
  noise: number | string;
  txPower: number | string;
  activeMs: string;
  busyMs: string;
  receiveMs: string;
  sendMs: string;
  phy: string;
  frequency: string;
  temperature: string;
};

type Props = {
  data?: ParsedRadio[];
  isSingle?: boolean;
};

const WifiAnalysisRadioTable = forwardRef(({ data = [], isSingle }: Props, ref) => {
  const { t } = useTranslation();


  const allColumns = useMemo(() => [
    {
      title: '',
      dataIndex: 'index',
      key: 'index',
      render: (_, record) => record.band ?? record.deductedBand,
      fixed: 'left',
      columnsFix: true
    },
    { title: 'Ch.', dataIndex: 'channel', key: 'channel' },
    { title: 'Ch. W', dataIndex: 'channelWidth', key: 'channelWidth' },
    { title: 'Tx Pow.', dataIndex: 'txPower', key: 'txPower' },
    { title: t('controller.wifi.noise'), dataIndex: 'noise', key: 'noise' },
    { title: 'Active (MS)', dataIndex: 'activeMs', key: 'activeMs' },
    { title: 'Busy (MS)', dataIndex: 'busyMs', key: 'busyMs' },
    { title: 'Receive (MS)', dataIndex: 'receiveMs', key: 'receiveMs' },
    { title: 'Send (MS)', dataIndex: 'sendMs', key: 'sendMs' },
    // { title: 'Temp.', dataIndex: 'temperature', key: 'temperature' },
    { title: 'Frequency', dataIndex: 'frequency', key: 'frequency' },
  ], [t]);

  const [visibleColumns, setVisibleColumns] = useState(allColumns.map(col => col.key));

  return (
    <>
      <Space align="center" style={{ marginTop: 24, marginBottom: 4, width: '100%' }}>
        <Title level={5} style={{ margin: 0 }}>
          {isSingle ? 'Radio' : `${t('configurations.radios')} (${data.length})`}
        </Title>
      </Space>

      <WirelessCustomTable
        ref={ref}
        tableId='wifianalysis-radio-table'
        // columnsOrder={true}
        columns={allColumns}
        dataSource={data}
        showColumnSelector={true}
        visibleColumns={visibleColumns}
        onVisibleColumnsChange={setVisibleColumns}
        pagination={false}
      />
    </>
  );
});

export default WifiAnalysisRadioTable;