import React, { useState, useEffect, useCallback } from 'react';
import { Table, Spin, Button, Form, Input, Space, Select, InputNumber, Row, Col } from 'antd';
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';
import { ACCEPTED_CONFIGURATION_OVERRIDES } from '@/modules-smb/Wireless/hooks/Network/ConfigurationOverride';
import { getOverrides } from '@/modules-smb/Wireless/hooks/Network/ConfigurationOverride';
import { addSvg } from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/modules-smb/contexts/AuthProvider';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";


interface ConfigOverride {
  parameterName: string;
  parameterValue: string | number;
  source: string;
  reason: string;
  modified?: number;
  parameterType?: string;
}

interface ConfigOverridesProps {
  serialNumber: string;
  overrides: ConfigOverride[];
  setOverrides: React.Dispatch<React.SetStateAction<ConfigOverride[]>>;
}

interface CreateFormData {
  startName: string;
  nameIndex: string;
  endName: string;
  value: string | number;
  reason: string;
}


const ConfigOverrides: React.FC<ConfigOverridesProps> = ({ serialNumber, overrides, setOverrides }) => {
  const { t } = useTranslation();
  const [data, setData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editRecord, setEditRecord] = useState<ConfigOverride | null>(null);
  const [editValue, setEditValue] = useState<string | number>('');
  const [editReason, setEditReason] = useState('');
  const [editError, setEditError] = useState<{ value: string; reason: string }>({ value: '', reason: '' });
  const { user } = useAuth();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const handleTableChange = useCallback((pagination: any) => {
    setCurrentPage(pagination.current);
    setPageSize(pagination.pageSize);
  }, []);

  const START_NAME_OPTIONS = [
    { label: 'radios', value: 'radios' },
  ];
  
  // const NAME_INDEX_OPTIONS = [
  //   { label: '0', value: '0' },
  //   { label: '1', value: '1' },
  //   { label: '2', value: '2' },
  //   { label: '3', value: '3' },
  //   { label: '4', value: '4' },
  // ];

  const NAME_INDEX_OPTIONS = [
    { label: '2G', value: '2G' },
    { label: '5G', value: '5G' },
    { label: '6G', value: '6G' },
  ];
  
  const END_NAME_OPTIONS = [
    { label: 'channel', value: 'channel' },
    { label: 'tx-power', value: 'tx-power' },
  ];

  const DEFAULT_FORM: CreateFormData = {
    startName: 'radios',
    nameIndex: '2G',
    endName: 'channel',
    value: ACCEPTED_CONFIGURATION_OVERRIDES.radios.channel.defaultValue,
    reason: '',
  };

  const [createForm, setCreateForm] = useState<CreateFormData>({ ...DEFAULT_FORM });
  const [form] = Form.useForm();

  const getParameterName = useCallback((form: CreateFormData) => 
    `${form.startName}.${form.nameIndex}.${form.endName}`, []);

  // 表单校验规则
  const getValidationRules = useCallback(() => {
    const alreadyCreatedNames = overrides.map((o: ConfigOverride) => o.parameterName);
    
    return {
      startName: [
        { required: true, message: t('form.required') },
        {
          validator: async () => {
            const paramName = getParameterName(createForm);
            if (alreadyCreatedNames.includes(paramName)) {
              throw new Error('');
            }
          }
        }
      ],
      nameIndex: [
        { required: true, message: t('form.required') },
        {
          validator: async () => {
            const paramName = getParameterName(createForm);
            if (alreadyCreatedNames.includes(paramName)) {
              throw new Error('');
            }
          }
        }
      ],
      endName: [
        { required: true, message: t('form.required') },
        {
          validator: async () => {
            const paramName = getParameterName(createForm);
            if (alreadyCreatedNames.includes(paramName)) {
              throw new Error(t('overrides.name_error'));
            }
          }
        }
      ],
      value: [
        { required: true, message: t('form.required') },
        {
          validator: async (_: any, value: any) => {
            if (createForm.endName && value !== undefined) {
              const test = ACCEPTED_CONFIGURATION_OVERRIDES.radios[createForm.endName as keyof typeof ACCEPTED_CONFIGURATION_OVERRIDES.radios]?.test;
              if (test) {
                const res = test(value as number);
                if (res) {
                  throw new Error(t(res) || res);
                }
              }
            }
          }
        }
      ],
      reason: [
        { max: 64, message: t('overrides.reason_error')}
      ]
    };
  }, [createForm, overrides, t, getParameterName]);

  const handleCreateChange = useCallback((v: any, name: string) => {
    setCreateForm(prev => {
      const next = { ...prev, [name]: v };
      // 当 endName 改变时，自动设置对应的默认值
      if (name === 'endName') {
        if (v === 'channel') {
          next.value = ACCEPTED_CONFIGURATION_OVERRIDES.radios.channel.defaultValue;
          form.setFieldValue('value', next.value);
        } else if (v === 'tx-power') {
          next.value = ACCEPTED_CONFIGURATION_OVERRIDES.radios['tx-power'].defaultValue;
          form.setFieldValue('value', next.value);
        }
      }
      return next;
    });
  }, [form]);

  // 同名校验
  React.useEffect(() => {
    form.validateFields(['startName', 'nameIndex', 'endName']);
  }, [createForm.startName, createForm.nameIndex, createForm.endName, form]);

  const handleAddOverride = useCallback(async (values: any) => {
    const paramName = `${values.startName}.${values.nameIndex}.${values.endName}`;
    const newOverride: ConfigOverride = {
      parameterName: paramName,
      parameterValue: values.value,
      source: user?.userRole,
      reason: values.reason,
      modified: Math.floor(new Date().getTime() / 1000),
      parameterType: values.endName === 'tx-power' ? 'integer' : 'string',
    };
    
    const newOverrides = [newOverride, ...overrides];
    setOverrides(newOverrides);
    setAddModalOpen(false);
    setCreateForm({ ...DEFAULT_FORM });
    form.resetFields();
  }, [overrides, user?.userRole, form, setOverrides]);

  const handleEditOverride = useCallback(async (values: any) => {
    if (!editRecord) return;

    const newOverrides = overrides.map((o: ConfigOverride) =>
      o.parameterName === editRecord.parameterName && o.source === editRecord.source
        ? { ...o, parameterValue: editValue, reason: editReason }
        : o
    );
    setOverrides(newOverrides);
    setEditModalOpen(false);
  }, [editRecord, editValue, editReason, overrides, setOverrides, t]);

  const handleDeleteOverride = useCallback((record: ConfigOverride) => {
    confirmModalAction(
      `Are you sure you want to delete this Configuration Override?`,
      () => {
        const filtered = overrides.filter((o: ConfigOverride) => 
          o.parameterName !== record.parameterName || o.source !== record.source
        );
        setOverrides(filtered);
      }
    );
    
  }, [overrides, setOverrides, t]);

  // 初始化赋值，强制请求最新数据
  useEffect(() => {
    let isMounted = true;
    if (serialNumber) {
      setIsLoading(true);
      getOverrides(serialNumber)
        .then(res => {
          if (isMounted) {
            setData(res);
            if (res?.overrides) {
              setOverrides(res.overrides);
            }
          }
        })
        .finally(() => {
          if (isMounted) setIsLoading(false);
        });
    }
    return () => { isMounted = false; };
  }, [serialNumber, setOverrides]);


  const columns = [
    {
      title: t('overrides.source'),
      dataIndex: 'source',
      key: 'source',
      sorter: (a: ConfigOverride, b: ConfigOverride) => (a.source || '').localeCompare(b.source || ''),
    },
    {
      title: t('common.name'),
      dataIndex: 'parameterName',
      key: 'parameterName',
      sorter: (a: ConfigOverride, b: ConfigOverride) => (a.parameterName || '').localeCompare(b.parameterName || ''),
    },
    {
      title: t('overrides.value'),
      dataIndex: 'parameterValue',
      key: 'parameterValue',
      sorter: (a: ConfigOverride, b: ConfigOverride) => (a.parameterValue || '').toString().localeCompare((b.parameterValue || '').toString()),
    },
    {
      title: t('overrides.reason'),
      dataIndex: 'reason',
      key: 'reason',
      sorter: (a: ConfigOverride, b: ConfigOverride) => (a.reason || '').localeCompare(b.reason || ''),
    },
    {
      title: 'Operation',
      key: 'action',
      render: (_: any, record: ConfigOverride) => (
        <Space size={24}>
          <Button 
            type="text" 
            style={{ padding: 0 }}
            onClick={() => {
              setEditRecord(record);
              setEditValue(record.parameterValue);
              setEditReason(record.reason);
              setEditError({ value: '', reason: '' });
              setEditModalOpen(true);
            }}
          >
            {t('crud.edit')}
          </Button>
          <Button 
            type="text" 
            style={{ padding: 0 }}
            onClick={() => handleDeleteOverride(record)}
          >
            {t('crud.delete')}
          </Button>
        </Space>
      ),
    },
  ];

  // 编辑判定输入组件项
  const renderValueInput = useCallback((value: string | number, onChange: (v: any) => void, endName: string) => {
    if (endName === 'channel') {
      return (
        <Select
          value={value}
          style={{ minWidth: 100 }}
          onChange={onChange}
        >
          {ACCEPTED_CONFIGURATION_OVERRIDES.radios.channel.options.map((opt: any) => (
            <Select.Option key={opt.value} value={opt.value}>{opt.label}</Select.Option>
          ))}
        </Select>
      );
    } else if (endName === 'tx-power') {
      return (
        <InputNumber
          value={value}
          min={ACCEPTED_CONFIGURATION_OVERRIDES.radios['tx-power'].min}
          max={ACCEPTED_CONFIGURATION_OVERRIDES.radios['tx-power'].max}
          style={{ minWidth: 100 }}
          onChange={onChange}
        />
      );
    } else {
      return (
        <Input 
          value={value} 
          style={{ minWidth: 100 }} 
          onChange={e => onChange(e.target.value)} 
        />
      );
    }
  }, []);

  return (
    <div>
      <Space style={{ marginBottom: 20 }}>
        <Button
          type="primary"
          icon={<Icon component={addSvg} />}
          onClick={() => setAddModalOpen(true)}
        >
          Create
        </Button>
      </Space>
      
      {isLoading ? (
        <Spin />
      ) : (
        <Table
          columns={columns}
          dataSource={overrides}
          rowKey={(r) => r.parameterName + r.source + (r.modified || '')}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            showTotal: (total) => `Total ${total} items`,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['5', '10', '20', '50'],
            onChange: handleTableChange,
          }}
          size="small"
          scroll={{ y: 300 }}
        />
      )}

      {/* 编辑弹窗 */}
      <FormModal
        open={editModalOpen}
        title="Edit"
        onCancel={() => setEditModalOpen(false)}
        onFinish={handleEditOverride}
        modalClass="ampcon-middle-modal configOverridesForm"
      >
        {editRecord && (
          <>
            <Row gutter={24}>
              <Col span={18}>
                <Form.Item label={t('overrides.parameter')}>
                  <Input value={editRecord.parameterName} type='hidden'/>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginBottom: 16, marginTop: -20 }}>
              <Col span={18}>
                {editRecord.parameterName}
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={18}>
                <Form.Item label={t('overrides.value')} required>
                  {renderValueInput(
                    editValue, 
                    setEditValue, 
                    editRecord.parameterName.split('.').pop() || ''
                  )}
                  {editError.value && <div style={{ color: 'red', fontSize: 12 }}>{editError.value}</div>}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={18}>
                <Form.Item label={t('overrides.reason')}>
                  <Input.TextArea
                    value={editReason}
                    rows={2}
                    onChange={e => setEditReason(e.target.value)}
                  />
                  {editError.reason && <div style={{ color: 'red', fontSize: 12 }}>{editError.reason}</div>}
                </Form.Item>
              </Col>
            </Row>
          </>
        )}
      </FormModal>

      {/* 新建弹窗 */}
      <FormModal
        open={addModalOpen}
        title="Create"
        onCancel={() => { 
          setAddModalOpen(false); 
          setCreateForm({ ...DEFAULT_FORM }); 
          form.resetFields();
        }}
        onFinish={handleAddOverride}
        initialValues={DEFAULT_FORM}
        form={form}
        modalClass="ampcon-middle-modal configOverridesForm"
      >
        <Row gutter={24}>
          <Col span={18}>
            <Form.Item label={t('overrides.parameter')} required>
              <Space.Compact block>
                <Form.Item noStyle name="startName" rules={getValidationRules().startName}>
                  <Select
                    options={START_NAME_OPTIONS}
                    style={{ width: 88, marginRight: 8 }}
                    onChange={v => handleCreateChange(v, 'startName')}
                  />
                </Form.Item>
                <Form.Item noStyle name="nameIndex" rules={getValidationRules().nameIndex}>
                  <Select
                    options={NAME_INDEX_OPTIONS}
                    style={{ width: 78, marginRight: 8 }}
                    onChange={v => handleCreateChange(v, 'nameIndex')}
                  />
                </Form.Item>
                <Form.Item noStyle name="endName" rules={getValidationRules().endName}>
                  <Select
                    options={END_NAME_OPTIONS}
                    style={{ width: 98 }}
                    onChange={v => handleCreateChange(v, 'endName')}
                  />
                </Form.Item>
              </Space.Compact>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={18}>
            <Form.Item label={t('overrides.value')} name="value" rules={getValidationRules().value} required>
              {createForm.endName === 'channel' ? (
                <Select
                  onChange={v => handleCreateChange(v, 'value')}
                >
                  {ACCEPTED_CONFIGURATION_OVERRIDES.radios.channel.options.map((opt: any) => (
                    <Select.Option key={opt.value} value={opt.value}>{opt.label}</Select.Option>
                  ))}
                </Select>
              ) : createForm.endName === 'tx-power' ? (
                <InputNumber
                  min={ACCEPTED_CONFIGURATION_OVERRIDES.radios['tx-power'].min}
                  max={ACCEPTED_CONFIGURATION_OVERRIDES.radios['tx-power'].max}
                  onChange={v => handleCreateChange(v, 'value')}
                />
              ) : null}
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={18}>
            <Form.Item label={t('overrides.reason')} name="reason" rules={getValidationRules().reason}>
              <Input.TextArea
                rows={2}
                onChange={e => handleCreateChange(e.target.value, 'reason')}
              />
            </Form.Item>
          </Col>
        </Row>  
      </FormModal>
    </div>
  );
};

export default ConfigOverrides;

