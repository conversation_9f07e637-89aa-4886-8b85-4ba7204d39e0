import React, { useState } from 'react';
import { <PERSON>, Button, message, Modal, Divider } from 'antd';
import Main from './Main';
import ComputedConfig from './ComputedConfig';
import ConfigOverrides from './ConfigOverrides';
import Notes from './Notes';
import { useMutation } from '@tanstack/react-query';
import { axiosProv } from '@/modules-smb/utils/axiosInstances';
import { useUpdateSourceOverrides } from '@/modules-smb/Wireless/hooks/Network/ConfigurationOverride';
import '@/modules-smb/Wireless/assets/form.scss';
import './inventoryEditFormTabs.scss';

interface EditFormsProps {
  tag: any;
  refresh: () => void;
  onClose: () => void;
  open?: boolean;
}

const EditForms: React.FC<EditFormsProps> = ({
  tag,
  refresh,
  onClose,
  open = true,
}) => {
  const [activeKey, setActiveKey] = useState('main');
  const [mainForm, setMainForm] = useState({ ...tag });
  const [notes, setNotes] = useState<any[]>([]);
  const [overrides, setOverrides] = useState<any[]>([]);

  React.useEffect(() => {
    setActiveKey('main');
    setMainForm({ ...tag });
    setNotes([]);
    setOverrides([]);
  }, [tag]);

  const inventoryMutation = useMutation((data: any) =>
    axiosProv.put(
      `inventory/${tag?.serialNumber}`,
      data
    )
  );

  const updateSourceOverridesMutation = useUpdateSourceOverrides();

  const handleApply = async () => {
    // 保存 inventory 数据
    await inventoryMutation.mutateAsync({ ...mainForm, devClass: 'any', notes });
    
    // 保存 ConfigurationOverride 数据
    if (overrides && overrides.length > 0) {
      await updateSourceOverridesMutation.mutateAsync({
        data: {
          serialNumber: tag?.serialNumber,
          overrides: overrides,
          managementPolicy: tag?.managementPolicy || '',
        },
        source: overrides[0]?.source || 'User'
      });
    }
    
    message.success('Update successfully');
    refresh();
    onClose();
  };

  return (
    <Modal
      open={open}
      onCancel={onClose}
      destroyOnClose
      title={<div>{`Edit ${tag?.serialNumber}`} <Divider style={{ marginTop: 8, marginBottom: 0 }} /></div>}
      footer={
        <>
          <Divider />
          <div className='foot-btns'>
            <Button onClick={onClose}>Cancel</Button>
            <Button type="primary" onClick={handleApply}>Apply</Button>
          </div>
        </>
      }
      className="ampcon-max-modal"
    >
      <Radio.Group
        value={activeKey}
        onChange={e => setActiveKey(e.target.value)}
        className='inventoryEditFormTabs'
        style={{ marginTop: 8 }}
      >
        <Radio.Button value="main">Main</Radio.Button>
        <Radio.Button value="computed">Computed Config</Radio.Button>
        <Radio.Button value="overrides">Config Overrides</Radio.Button>
        <Radio.Button value="notes">Notes</Radio.Button>
      </Radio.Group>

      <div style={{ marginTop: 24}}>
        <div style={{ display: activeKey === 'main' ? 'block' : 'none' }}>
          <Main key={tag?.serialNumber + '-main'} value={mainForm} onChange={setMainForm} />
        </div>
        <div style={{ display: activeKey === 'computed' ? 'block' : 'none' }}>
          <ComputedConfig key={tag?.serialNumber + '-computed'} serialNumber={tag?.serialNumber} />
        </div>
        <div style={{ display: activeKey === 'overrides' ? 'block' : 'none' }}>
          <ConfigOverrides
            key={tag?.serialNumber + '-overrides'}
            serialNumber={tag?.serialNumber}
            overrides={overrides}
            setOverrides={setOverrides}
          />
        </div>
        <div style={{ display: activeKey === 'notes' ? 'block' : 'none' }}>
          <Notes key={tag?.serialNumber + '-notes'} oldNotes={tag?.notes} setNotes={setNotes} />
        </div>
      </div>
    </Modal>
  );
};

export default EditForms;

