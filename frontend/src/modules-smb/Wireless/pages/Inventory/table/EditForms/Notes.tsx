import React, { useState } from 'react';
import { Table, Input, Button, Space, Form } from 'antd';
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';
import { useTranslation } from 'react-i18next';
import { addSvg } from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import { useAuth } from '@/modules-smb/contexts/AuthProvider';
import { v4 as uuid } from 'uuid';


interface NotesProps {
  oldNotes: any[];
  setNotes: (notes: any[]) => void;
}

const Notes: React.FC<NotesProps> = ({ oldNotes, setNotes }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [newNote, setNewNote] = useState('');
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [allNotes, setAllNotes] = useState<any[]>([...(oldNotes || [])]);
  const [newNotes, setNewNotes] = useState<any[]>([]);

  const addNoteToForm = () => {
    if (!newNote) return;
    const newNoteData = { note: newNote, isNew: true, createdBy: user.email, created: Math.floor(new Date().getTime() / 1000) };
    setNewNotes(prev => {
      const updated = [...prev, newNoteData];
      setNotes(updated);
      return updated;
    });
    setAllNotes(prev => [...prev, newNoteData]);
    setNewNote('');
    setAddModalOpen(false);
  };

  const columns = [
    {
      title: 'Date',
      dataIndex: 'created',
      key: 'created',
      render: (val: number) => val ? new Date(val * 1000).toLocaleString() : '',
      sorter: (a: any, b: any) => a.created - b.created,
    },
    {
      title: 'Note',
      dataIndex: 'note',
      key: 'note',
      sorter: (a: any, b: any) => (a.note || '').localeCompare(b.note || ''),
    },
    {
      title: 'By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      sorter: (a: any, b: any) => (a.createdBy || '').localeCompare(b.createdBy || ''),
    },
  ];

  // 分页
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
  const pagedNotes = allNotes
    .sort((a, b) => b.created - a.created)
    .slice((pagination.current - 1) * pagination.pageSize, pagination.current * pagination.pageSize);

  return (
    <div>
      <Space style={{ marginBottom: 20 }}>
        <Button
          type="primary"
          icon={<Icon component={addSvg} />}
          onClick={() => setAddModalOpen(true)}
        >
          Create Note
        </Button>
      </Space>
      <Table
        columns={columns}
        dataSource={pagedNotes}
        rowKey={() => uuid()}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: allNotes.length,
          onChange: (page, pageSize) => setPagination({ current: page, pageSize }),
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: [5, 10, 20, 50],
          showTotal: (total) => `Total ${total} items`,
        }}
        size="small"
        scroll={{ y: 200 }}
        className="NoteTable"

      />

      <FormModal
        open={addModalOpen}
        title="Create Note "
        onCancel={() => { setAddModalOpen(false); setNewNote(''); }}
        onFinish={addNoteToForm}
        modalClass="ampcon-middle-modal"
      >
        <Form.Item
          label="Note"
          name="note"
          rules={[{ required: true, message: t('form.required') }]}
        >
          <Input.TextArea
            value={newNote}
            onChange={e => setNewNote(e.target.value)}
            style={{ width: 220 }}
          />
        </Form.Item>
      </FormModal>
    </div>
  );
};

export default Notes;

