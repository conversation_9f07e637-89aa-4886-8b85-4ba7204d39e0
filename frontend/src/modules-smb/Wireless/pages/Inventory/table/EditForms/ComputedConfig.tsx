import React from 'react';
import { Button, Typography, Alert, Spin, Flex } from 'antd';
import { useTranslation } from 'react-i18next';
import { refreshSvg } from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import { useGetComputedConfiguration } from '@/modules-smb/hooks/Network/Inventory';

const { Title } = Typography;

interface ComputedConfigProps {
  serialNumber: string;
}

const ComputedConfig: React.FC<ComputedConfigProps> = ({ serialNumber }) => {
  const { t } = useTranslation();
  const {
    data: computedConfig,
    isFetching,
    refetch,
  } = useGetComputedConfiguration({ serialNumber, enabled: true });

  if (isFetching) return <Spin />;
  if (!computedConfig || computedConfig.config === 'none') {
    return <Alert message={t('inventory.no_computed')} type="info" showIcon style={{ color: "#1677ff", borderColor: "#1677ff" }} />;
  }

  return (
    <div>
      <Flex style={{ flexDirection: 'column', marginBottom: 8 }}>
        <Title level={5} style={{ margin: 0 }}>Configuration</Title>
        <Button
          icon={<Icon component={refreshSvg} />}
          size="small"
          loading={isFetching}
          onClick={() => refetch()}
          style={{ width: 100, height: 32, marginTop: 20, marginBottom: 10 }}
        >
          Refresh
        </Button>
      </Flex>
      <div style={{ border: '1px solid #eee', borderRadius: 16, height: '30vh', overflowY: 'auto' }}>
        <pre style={{ margin: 0, padding: 8 }}>{JSON.stringify(computedConfig.config, null, 2)}</pre>
      </div>
    </div>
  );
};

export default ComputedConfig;

