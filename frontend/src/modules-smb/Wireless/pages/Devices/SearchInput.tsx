import React, { useState, useEffect, useRef } from 'react';
import { Input, Dropdown, List, Spin,Tooltip } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import debounce from 'lodash.debounce';
import { useGetDevices } from '@/modules-smb/hooks/Network/Devices';
import '@/modules-smb/Wireless/assets/wireless.scss';

const SearchInput: React.FC = () => {
  const navigate = useNavigate();
  const [inputValue, setInputValue] = useState('');
  const [searching, setSearching] = useState(false);
  const [filteredResults, setFilteredResults] = useState<string[]>([]);
  const [openDropdown, setOpenDropdown] = useState(false);

  const { data } = useGetDevices({
    pageInfo: { index: 0, limit: 1000 }, // 或根据你实际情况调整
    enabled: true,
    platform: 'ALL',
  });

  const serialList = data?.devicesWithStatus?.map((d) => d.serialNumber) || [];

  const handleSearch = debounce((val: string) => {
    if (val.length < 2) {
      setFilteredResults([]);
      setOpenDropdown(false);
      return;
    }

    setSearching(true);

    const matched = serialList.filter((sn) =>
      sn.toLowerCase().includes(val.toLowerCase())
    );

    setFilteredResults(matched);
    setOpenDropdown(matched.length > 0);
    setSearching(false);
  }, 300);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    setInputValue(val);
    handleSearch(val);
  };

  const handleSelect = (sn: string) => {
    setInputValue('');
    setFilteredResults([]);
    setOpenDropdown(false);
    navigate(`/wireless/devices/${sn}`);
  };

  return (
    <Tooltip
      title="Search serial numbers and radius clients. For radius clients you can either use the client's username (rad:<EMAIL>)
       or use the client's station ID (rad:11:22:33:44:55:66)"
      placement="left"
    >
    <Dropdown
      open={openDropdown}
      dropdownRender={() =>
        searching ? (
          <div style={{ background: '#fff', padding: 10 }}>
            <Spin style={{ padding: 10 }} />
          </div>
        ) : (
          <div style={{ background: '#fff' }}> 
          <List
          className='input-search-hover'
            dataSource={filteredResults}
            renderItem={(item) => (
              <List.Item
                style={{ cursor: 'pointer', padding: '4px 12px' }}
                onClick={() => handleSelect(item)}
              >
                {item}
              </List.Item>
            )}
            style={{ maxHeight: 200, overflowY: 'auto', minWidth: 250 }}
          />
          </div>
        )
      }
      trigger={['click']}
    >
      <Input
      style={{justifyContent:'flex-end',width: '250px'}} 
        value={inputValue}
        onChange={handleInputChange}
        placeholder="Search Serial Numbers"
        prefix={<SearchOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
        allowClear
      />
    </Dropdown>
    </Tooltip>
  );
};

export default SearchInput;
