import React,{useState} from 'react';
// import {
//   Box,
//   Center,
//   HStack,
//   IconButton,
//   Link,
//   Popover,
//   PopoverArrow,
//   PopoverBody,
//   PopoverCloseButton,
//   PopoverContent,
//   PopoverFooter,
//   PopoverHeader,
//   PopoverTrigger,
//   Tooltip,
//   useDisclosure,
// } from '@chakra-ui/react';
import { MagnifyingGlass, Trash } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import { Dropdown, Menu, Button,Popover,Tooltip } from 'antd';
import {DownOutlined,DeleteOutlined}  from '@ant-design/icons';
import DeviceActionDropdown from '@/modules-smb/components/Buttons/DeviceActionDropdown';
import { DeviceWithStatus, useDeleteDevice } from '@/modules-smb/hooks/Network/Devices';
import { GatewayDevice } from '@/modules-smb/models/Device';
import { useBlinkDevice, useGetDeviceRtty } from '@/modules-smb/Wireless/hooks/Network/Devices';
import  iconNavLeftExpand from '@/assets/Devices/icon_nav_left_expand.png';
import type { MenuProps } from 'antd';
// import useMutationResult from '@/modules-smb/hooks/useMutationResult';
import useMutationResult from '@/modules-smb/Wireless/hooks/useMutationResult';
import axios from 'axios';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";

interface Props {
  device: DeviceWithStatus;
  refreshTable: () => void;
  onOpenScan: (serialNumber: string) => void;
  onOpenFactoryReset: (serialNumber: string) => void;
//   onOpenUpgradeModal: (serialNumber: string) => void;
  onOpenTrace: (serialNumber: string) => void;
  onOpenEventQueue: (serialNumber: string) => void;
  onOpenConfigureModal: (serialNumber: string) => void;
  onOpenTelemetryModal: (serialNumber: string) => void;
//   onOpenScriptModal: (device: GatewayDevice) => void;
  onOpenRebootModal: (serialNumber: string) => void;
}

const Actions: React.FC<Props> = ({
  device,
  refreshTable,
  onOpenScan,
  onOpenFactoryReset,
//   onOpenUpgradeModal,
  onOpenTrace,
  onOpenEventQueue,
  onOpenConfigureModal,
  onOpenTelemetryModal,
//   onOpenScriptModal,
  onOpenRebootModal,
}) => {
  const { t } = useTranslation();
  const [popoverVisible, setPopoverVisible] = useState(false);
//   const { isOpen, onOpen, onClose } = useDisclosure();
  const { mutateAsync: deleteDevice, isLoading: isDeleting } = useDeleteDevice({
    serialNumber: device.serialNumber,
  });
  const { mutateAsync: blink } = useBlinkDevice({ serialNumber: device.serialNumber });
  const handleDeleteClick = () =>
    deleteDevice(device.serialNumber, {
      onSuccess: () => {
        refreshTable();
        setPopoverVisible(false);
      },
    });

    const { onSuccess: onBlinkSuccess, onError: onBlinkError } = useMutationResult({
        objName: t('devices.one'),
        operationType: 'blink',
        refreshTable,
    });

//   return (
//     <HStack spacing={2}>
//       <Popover isOpen={isOpen} onOpen={onOpen} onClose={onClose}>
//         <Tooltip hasArrow label={t('crud.delete')} placement="top" isDisabled={isOpen}>
//           <Box>
//             <PopoverTrigger>
//               <IconButton aria-label="Open Device Delete" colorScheme="red" icon={<Trash size={20} />} size="sm" />
//             </PopoverTrigger>
//           </Box>
//         </Tooltip>
//         <PopoverContent>
//           <PopoverArrow />
//           <PopoverCloseButton />
//           <PopoverHeader>
//             {t('crud.delete')} {device.serialNumber}
//           </PopoverHeader>
//           <PopoverBody>{t('crud.delete_confirm', { obj: t('devices.one') })}</PopoverBody>
//           <PopoverFooter>
//             <Center>
//               <Button colorScheme="gray" mr="1" onClick={onClose}>
//                 {t('common.cancel')}
//               </Button>
//               <Button colorScheme="red" ml="1" onClick={handleDeleteClick} isLoading={isDeleting}>
//                 {t('common.yes')}
//               </Button>
//             </Center>
//           </PopoverFooter>
//         </PopoverContent>
//       </Popover>
//       <DeviceActionDropdown
//         // @ts-ignore
//         device={device}
//         refresh={refreshTable}
//         onOpenScan={onOpenScan}
//         onOpenFactoryReset={onOpenFactoryReset}
//         onOpenUpgradeModal={onOpenUpgradeModal}
//         onOpenTrace={onOpenTrace}
//         onOpenEventQueue={onOpenEventQueue}
//         onOpenConfigureModal={onOpenConfigureModal}
//         onOpenTelemetryModal={onOpenTelemetryModal}
//         onOpenScriptModal={onOpenScriptModal}
//         onOpenRebootModal={onOpenRebootModal}
//       />
//       <Tooltip hasArrow label={t('common.view_details')} placement="top">
//         <Link href={`/wireless/devices/${device.serialNumber}#/devices/${device.serialNumber}`}>
//           <IconButton
//             aria-label={t('common.view_details')}
//             colorScheme="blue"
//             icon={<MagnifyingGlass size={20} />}
//             size="sm"
//           />
//         </Link>
//       </Tooltip>
//     </HStack>
//   );

const { refetch: getRtty, isFetching: isRtty } = useGetDeviceRtty({
    serialNumber: device.serialNumber,
    extraId: 'inventory-modal',
  });
    const handleConnectClick = () => {
      getRtty();
    }

//   const items: MenuProps['items'] = [
//   {
//     label: (
//       <a href="https://www.antgroup.com" target="_blank" rel="noopener noreferrer">
//         1st menu item
//       </a>
//     ),
//     key: '0',
//   },
//   {
//     label: (
//       <a href="https://www.aliyun.com" target="_blank" rel="noopener noreferrer">
//         2nd menu item
//       </a>
//     ),
//     key: '1',
//   },
//   {
//     type: 'divider',
//   },
//   {
//     label: '3rd menu item',
//     key: '3',
//   },
// ];

  const handleMenuClick = ({ key }: { key: string }) => {

    switch (key) {
      case 'blink':
        // trigger blink logic
        break;
      case 'configuration':
        // trigger configuration logic
        break;
      case 'factoryReset':
        // trigger factory reset logic
        break;
      case 'rtty':
        // trigger rtty logic
        break;
      // ... add more cases as needed
    }
  };

  // const handleBlinkClick = () => {
  //     blink(undefined, {
  //       onError: (e) => {
  //         if (axios.isAxiosError(e)) onBlinkError(e);
  //       },
  //     });
  //     onBlinkSuccess();
  //   };
      const handleBlinkClick = () =>
    blink(undefined, {
      onSuccess: () => {
        onBlinkSuccess();
      },
      onError: (e) => {
        onBlinkError(e);
      },
    });
  const handleOpenScan = () => onOpenScan(device.serialNumber);
  const handleOpenConfigure = () => onOpenConfigureModal(device.serialNumber);
  const handleRebootClick = () => onOpenRebootModal(device.serialNumber);
  const handleOpenQueue = () => onOpenEventQueue(device.serialNumber);
  const handleOpenFactoryReset = () => onOpenFactoryReset(device.serialNumber);
  const handleOpenTelemetry = () => onOpenTelemetryModal(device.serialNumber);
  const handleOpenTrace = () => onOpenTrace(device.serialNumber);

  const menu = (
    <Menu onClick={handleMenuClick}>
      <Menu.Item key="blink" onClick={handleBlinkClick}>{t('commands.blink')}</Menu.Item>
      <Menu.Item key="configuration" onClick={handleOpenConfigure}>{t('controller.configure.title')}</Menu.Item>
      {/* <Menu.Item key="rtty" onClick={handleConnectClick}>{t('commands.rtty')}</Menu.Item> */}
      {/* <Menu.Item key="eventQueue" onClick={handleOpenQueue}>{t('controller.queue.title')}</Menu.Item> */}
      <Menu.Item key="factoryReset" onClick={handleOpenFactoryReset}>{t('commands.factory_reset')}</Menu.Item>
      {/* <Menu.Item key="reboot" onClick={handleRebootClick}>{t('commands.reboot')}</Menu.Item> */}
      {/* <Menu.Item key="telemetry" onClick={handleOpenTelemetry}>{t('controller.telemetry.title')}</Menu.Item> */}
      <Menu.Item key="trace" onClick={handleOpenTrace}>{t('controller.devices.trace')}</Menu.Item>
      {/* <Menu.Item key="wifiScan" onClick={handleOpenScan}>{t('commands.wifiscan')}</Menu.Item> */}
    </Menu>
  );

  const deleteContent = (
    <div style={{ maxWidth: 220 }}>
      <p style={{ marginBottom: 12 }}>
        {t('crud.delete')} <strong>{device.serialNumber}</strong>?
      </p>
      <p>{t('crud.delete_confirm', { obj: t('devices.one') })}</p>
      <div style={{ marginTop: 16, textAlign: 'right' }}>
        <Button size="small" onClick={() => setPopoverVisible(false)} style={{ marginRight: 8 }}>
          {t('common.cancel')}
        </Button>
        <Button
          size="small"
          danger
          loading={isDeleting}
          onClick={handleDeleteClick}
        >
          {t('common.yes')}
        </Button>
      </div>
    </div>
  );

  const handleDelete = (device: any) => {
          confirmModalAction(
              `Are you sure you want to delete this ${device.serialNumber}?`,
              handleDeleteClick
          );
      };

    return(
        <div style={{ display: 'flex',gap: 24, alignItems: 'center'}}>
          {/* <a style={{color:'#14C9BB '}} onClick={handleConnectClick}>Rtty</a>
          <a style={{color:'#14C9BB '}} onClick={handleOpenConfigure}>Configure</a>
          <a style={{color:'#14C9BB '}} onClick={handleRebootClick}>Reboot</a>
          <a style={{color:'#14C9BB '}} onClick={handleOpenScan}>WiFi Scan</a> */}
          <Dropdown overlay={menu} trigger={['click']}>
            <a type="link" style={{ color: '#14C9BB', paddingLeft: 0,display: 'flex', alignItems: 'center' }}>
              Actions <DownOutlined />
            </a>
          </Dropdown>
          {/* <a style={{color:'#14C9BB '}} >Delete</a> */}
          {/* <Popover
        title={t('crud.delete')}
        content={deleteContent}
        trigger="click"
        open={popoverVisible}
        onOpenChange={(visible) => setPopoverVisible(visible)}
      >
        <Tooltip title={t('crud.delete')}>
          <a style={{color:'#14C9BB '}} >Delete</a>
        </Tooltip>
      </Popover> */}
      <a type="link" 
      style={{ display: 'flex', alignItems: 'center', color: '#14C9BB'}} 
      onClick={() => handleDelete(device)} 
      onFocus={(e) => e.target.blur()}
      >Delete</a>
        </div>

    );
};

export default React.memo(Actions);
