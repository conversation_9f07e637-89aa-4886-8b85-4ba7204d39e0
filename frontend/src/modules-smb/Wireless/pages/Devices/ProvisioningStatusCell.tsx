import * as React from "react";
import { DeviceWithStatus } from "@/modules-smb/Wireless/hooks/Network/Devices";
import { useGetTag } from "@/modules-smb/Wireless/hooks/Network/Inventory";
import { useNavigate } from "react-router-dom";

type Props = {
  device: DeviceWithStatus;
};

const ProvisioningStatusCell = ({ device }: Props) => {
  const getTag = useGetTag({ serialNumber: device.serialNumber });
  const navigate = useNavigate();
  const handleClick = (path: string) => () => {
    navigate(`/wireless/manage/Monitor#${path}`);
  };
  // 处理错误状态
  if (getTag.isError) {
    return (<span>-</span>);
  }
  if (getTag.data?.extendedInfo?.venue?.name) {
    return (
      <a style={{ color: "#14C9BB ", textDecoration: "underline" }} onClick={handleClick(`${getTag.data?.venue}`)}>
        {getTag.data?.extendedInfo?.venue?.name}
      </a>
    );
  }
  return (<span>-</span>);
};

export default ProvisioningStatusCell;
