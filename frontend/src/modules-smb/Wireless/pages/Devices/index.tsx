import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import SiteSelect from "@/modules-smb/Wireless/components/SiteSelect";
import "../../assets/wireless.scss";
import DevicesList from "./DevicesList";
import { deleteWirelessDevice } from '@/modules-smb/Wireless/apis/wireless_device_api';
import { Card } from 'antd';
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";
import { useEntityFavorite } from "@/modules-smb/Wireless/hooks/useEntityFavorite";

const DevicesPage = () => {
  const {
    selectedSiteId,
    isAllSitesSelected,
    setSelectedSiteId,
    setAllSites,
    resetFromOtherPage
  } = useSiteStore();
  const location = useLocation();
  const navigate = useNavigate();
  const [siteId, setSiteId] = useState<string>();
  const displaySiteId = isAllSitesSelected ? null : selectedSiteId;
  const { getFirstVenueFavoriteId } = useEntityFavorite();

  useEffect(() => {
    // resetFromOtherPage();
    const hash = window.location.hash.substring(1);

    if (hash === "all") {
      setAllSites();
      return;
    }

    if (isAllSitesSelected) {
      navigate(`${location.pathname}#all`, { replace: true });
      return;
    }

    if (!hash) {
      const favId = getFirstVenueFavoriteId();
      if (favId) {
        setSelectedSiteId(favId);
        navigate(`${location.pathname}#${favId}`, { replace: true });
      }
    }

    if (/^\d+$/.test(hash)) {
      setSelectedSiteId(hash);
    }
  }, []);
  // useEffect(() => {
  //   const hash = location.hash.replace('#', '');
  //   setSiteId(hash || selectedSiteId);
  // }, [location.hash, selectedSiteId]);

  // useEffect(() => {
  //   const hash = location.hash.replace('#', '');
  //   if (siteId && hash !== siteId) {
  //     navigate(`${location.pathname}#${siteId}`, { replace: true });
  //   }
  //   console.log('siteId UseEffect======', hash);
  // }, [siteId]);

  const handleChange = (value: string | string[]) => {
    const id = Array.isArray(value) ? value[0] : value;
    if (id === "all") {
      setAllSites();
    } else {
      setSelectedSiteId(id);
    }
    navigate(`${location.pathname}#${id}`);
  };

  if (siteId === null) return null;
  // 在组件或函数中调用
  const handleDeleteDevices = async () => {
    try {
      const result = await deleteWirelessDevice({
        snList: ["d4babaa398a0", "d4babaa399b0"] // 替换为实际的序列号列表
      });
      console.log('删除结果:', result);
    } catch (error) {
      console.error('删除失败:', error);
    }
  };


  return (
    <>
      <Card
        style={{
          width: '100%',
          minHeight: '100%',
          borderRadius: '8px',
          boxShadow: 'none',
          padding: '20px 24px',
          overflowX: 'auto', // 核心：内容超出时显示横向滚动条
        }}
        bodyStyle={{ padding: 0 }}
      >
        <span className="text-title">Devices</span>
        <SiteSelect onChange={handleChange} value={displaySiteId === null ? "all" : displaySiteId} />
        <DevicesList siteId={displaySiteId} />
        {/* <Button onClick={handleDeleteDevices}>delete</Button> */}
      </Card>
    </>
  );
};

export default DevicesPage;
