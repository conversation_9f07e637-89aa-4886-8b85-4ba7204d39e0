.TraceModalCreate {
  .ant-form-item-label > label {
    width: 150px;
    text-align: left;
    word-wrap: break-word;
    overflow: hidden;
  }
  .ant-form-item-control {
    max-width: 280px;

    .ant-input-affix-wrapper,
    .ant-input,
    .ant-select,
    .ant-input-number,
    .ant-picker {
      width: 100%;
    }
  }
}
.custom-trace-alert {
  // 自定义背景色
  background-color: #F3F8FF !important;
  border: none !important;
  // 自定义文字样式
  .ant-alert-message {
    font-size: 12px !important;
    color: #367EFF !important;
  }
  
  // 隐藏默认图标
  .ant-alert-icon {
    visibility: hidden;
    position: relative;
    
    // 显示自定义图标
    &::after {
      content: '';
      visibility: visible;
      position: absolute;
      left: 0;
      top: 1;
      width: 16px; // 图标宽度
      height: 16px; // 图标高度
      background-image: url('@/modules-smb/Wireless/assets/Devices/Logo_Alert.svg');
      background-size: contain;
      background-repeat: no-repeat;
    }
  }
  
  // 自定义关闭按钮颜色
  .ant-alert-close-icon {
    color: #367EFF !important;
  }
}
    